package com.dataxai.web.controller.admincontroller;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.TableDataInfo;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.web.domain.TArticle;
import com.dataxai.web.domain.TArticleDto;
import com.dataxai.web.service.ITArticleService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 文章Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/system/article")
@Api(tags = "文章信息")
public class TArticleController extends BaseController
{
    @Autowired
    private ITArticleService tArticleService;

    /**
     * 查询文章列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TArticle tArticle)
    {
        startPage();
        List<TArticle> list = tArticleService.selectTArticleList(tArticle);
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setTotal(new PageInfo(list).getTotal());
        return dataTable;
    }

    /**
     * 用户端获取文章列表
     * @param
     * @return
     */
    @GetMapping("/front/list")
    //@RequestBody TArticle tArticle
    public R<HashMap<String,Object>> weblist()
    {
        TArticle tArticle = new TArticle();
        startPage();
        List<TArticleDto> list = tArticleService.selectWebTArticleList(tArticle);
        // 组装自定义分页结构
        HashMap<String, Object> pageData = new HashMap<>();
        pageData.put("total", new com.github.pagehelper.PageInfo<>(list).getTotal());
        pageData.put("data", list);
        return R.ok(pageData);
    }

    /**
     * 导出文章列表
     */
    @PreAuthorize("@ss.hasPermi('system:article:export')")
    @Log(title = "文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TArticle tArticle)
    {
        List<TArticle> list = tArticleService.selectTArticleList(tArticle);
        ExcelUtil<TArticle> util = new ExcelUtil<TArticle>(TArticle.class);
        util.exportExcel(response, list, "文章数据");
    }

    /**
     * 获取文章详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tArticleService.selectTArticleById(id));
    }

    /**
     * 用户端获取文章详细信息
     */
    @GetMapping(value = "/front/{id}")
    public AjaxResult getWebInfo(@PathVariable("id") Long id)
    {
        return success(tArticleService.selectWebTArticleById(id));
    }

    /**
     * 新增文章
     */
    @PreAuthorize("@ss.hasPermi('system:article:add')")
    //@Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增文章")
    public AjaxResult add(@RequestBody TArticle tArticle)
    {
        return toAjax(tArticleService.insertTArticle(tArticle));
    }

    /**
     * 修改文章
     */
    @PreAuthorize("@ss.hasPermi('system:article:edit')")
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改文章")
    public AjaxResult edit(@RequestBody TArticle tArticle)
    {
        return toAjax(tArticleService.updateTArticle(tArticle));
    }

    /**
     * 删除文章
     */
    @PreAuthorize("@ss.hasPermi('system:article:remove')")
    @Log(title = "文章", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tArticleService.deleteTArticleByIds(ids));
    }
}
