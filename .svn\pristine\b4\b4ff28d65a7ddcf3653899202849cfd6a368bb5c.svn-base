# 日志配置
logging:
  level:
    com.dataxai: info
#    org.springframework: warn
#    com.dataxai.mapper: debug
#    org.springframework.web: DEBUG
#    com.fasterxml.jackson: DEBUG

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    # 应用名称
    name: ai-photo
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: local
#   文件上传 10MB
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 256MB
      # 设置总上传的文件大小
      max-request-size: 256MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

#  jackson:
#    time-zone: GMT+8
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 2880

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.dataxai.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  # configuration:
  #   log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
#  # 请求前缀
#  pathMapping: /dev-api
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/article
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Socket.IO配置优化
socketio:
  host: 0.0.0.0
  port: 9999
  bossCount: 1
  workCount: 100
  allowCustomRequests: true
  upgradeTimeout: 10000
  pingTimeout: 60000
  pingInterval: 25000
  namespaces: /socket.io
  maxFramePayloadLength: 1048576
  maxHttpContentLength: 1048576

  # 连接管理配置
  connection:
    maxConnectionsPerUser: 5
    connectionTimeout: 30000
    heartbeatInterval: 25000
    heartbeatTimeout: 60000
    enableConnectionPool: true
    connectionPoolSize: 1000

  # 消息配置
  message:
    maxMessageSize: 1048576
    messageTimeout: 10000
    maxRetryAttempts: 3
    retryDelay: 100
    enableAsyncSending: true
    asyncThreadPoolSize: 10

  # 监控配置
  monitor:
    enableHealthCheck: true
    healthCheckInterval: 30000
    enableConnectionCleanup: true
    cleanupInterval: 60000
    enableMetrics: true
    metricsInterval: 60000

# 平台API配置
platform:
  api:
    forgediy-url: https://design.styleforgediy.com/blade-product/AIShop/setMaterial
    dingzhi-url: https://design.the2016.com/api/xiaoaishop/api/image_task/store
    key: PLATFORM_KEY_2025
