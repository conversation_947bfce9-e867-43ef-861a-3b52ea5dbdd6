package com.dataxai.web.controller.admincontroller;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.redis.RedisCache;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.web.domain.*;
import com.dataxai.web.service.*;
import com.dataxai.web.utils.MyDateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 后台数据概览Controller
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@RestController
@RequestMapping("/admin/statistics")
@Api(tags = "网站的数据统计")
public class AdminStatisticsController extends BaseController
{
    @Autowired
    private IAdminOrderService adminOrderService;

    @Autowired
    private IAdminUserService adminUserService;

    @Autowired
    private IAdminTaskOrdinalService adminTaskOrdinalService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询订单列表
     */
    @GetMapping("/newlyAccessList")
    @ApiOperation(value = "访问量")
    public R<AdminCommonStatisticsBean> newlyAccessList() {
        List<String> data = redisCache.getCacheObject("pv");
        logger.info("从redis中取出的pv   "+data);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -6);
        logger.info(MyDateUtils.parseDateToStr(beginCreateTime));
        HashMap<String,Integer> countMap = getMonthDayHashMap(beginCreateTime);
        if(null != data){
            data.forEach(item->{
                String[] split = item.split(",");
                String dateStr = split[0];
                String count = split[1];
                SimpleDateFormat inputFormat = new SimpleDateFormat("dd/MMM/yyyy", Locale.ENGLISH);
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = MyDateUtils.dateConversion(dateStr, inputFormat, outputFormat);
                String dayStr = MyDateUtils.getDayStr(date);
                System.out.println(dayStr+"       "+count);
                countMap.put(dayStr,countMap.get(dayStr)+Integer.parseInt(count));
            });
        }
        List<AdminCommonStatistics> collect = new ArrayList<>();
        Set<Map.Entry<String, Integer>> entrySet = countMap.entrySet();
        for (Map.Entry<String, Integer> stringIntegerEntry : entrySet) {
            String key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
            adminCommonStatistics.setXAxis(key);
            adminCommonStatistics.setYAxis(value);
            collect.add(adminCommonStatistics);
        }

        Integer todayAddCount = collect.get(6).getYAxis();

        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }

    /**
     * 查询订单列表
     */
    @GetMapping("/newlyOrderMoneyList")
    @ApiOperation(value = "订单金额统计")
    public R<AdminCommonMoneyStatisticsBean> newlyOrderMoneyList()
    {
        AdminOrder adminOrderParam = new AdminOrder();
        adminOrderParam.setDelFlag(0L);
        adminOrderParam.setStatus(1L);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -6);
        Date todayEndTime = MyDateUtils.getTodayEndTime();
        adminOrderParam.setBeginCreateTime(beginCreateTime);
        adminOrderParam.setEndCreateTime(todayEndTime);
        List<AdminOrder> adminOrderList = adminOrderService.selectAdminOrderList(adminOrderParam);
        List<AdminCommonMoneyStatistics> collect = new ArrayList<>();
        HashMap<String,Double> countMap = getMoneyMonthDayHashMap(beginCreateTime);
        if(CollectionUtil.isNotEmpty(adminOrderList)){
            for (AdminOrder item : adminOrderList) {
                Date createTime = item.getCreateTime();
                String dayStr = MyDateUtils.getDayStr(createTime);
                countMap.put(dayStr,countMap.get(dayStr)+item.getMoney().doubleValue());
            }
        }

        Set<Map.Entry<String, Double>> entrySet = countMap.entrySet();
        for (Map.Entry<String, Double> stringIntegerEntry : entrySet) {
            String key = stringIntegerEntry.getKey();
            Double value = stringIntegerEntry.getValue();
            AdminCommonMoneyStatistics adminCommonStatistics = new AdminCommonMoneyStatistics();
            adminCommonStatistics.setXAxis(key);

            double yAxis = value.doubleValue();
            BigDecimal yBigDecimal = new BigDecimal(yAxis);
            BigDecimal bigDecimal = yBigDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            adminCommonStatistics.setYAxis(bigDecimal.doubleValue());
            collect.add(adminCommonStatistics);
        }

        double todayAddCount = collect.get(6).getYAxis();

        AdminCommonMoneyStatisticsBean result = new AdminCommonMoneyStatisticsBean();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }

    @GetMapping("/newlyAddOrderList")
    @ApiOperation(value = "新增订单统计")
    public R<AdminCommonStatisticsBean> newlyAddOrderList()
    {
        AdminOrder adminOrderParam = new AdminOrder();
        adminOrderParam.setDelFlag(0L);
        adminOrderParam.setStatus(1L);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -6);
        Date todayEndTime = MyDateUtils.getTodayEndTime();
        adminOrderParam.setBeginCreateTime(beginCreateTime);
        adminOrderParam.setEndCreateTime(todayEndTime);
        List<AdminOrder> adminOrderList = adminOrderService.selectNewlyAddAdminUserList(adminOrderParam);
        List<AdminCommonStatistics> collect = new ArrayList<>();
        HashMap<String,Integer> countMap = getMonthDayHashMap(beginCreateTime);
        if(CollectionUtil.isNotEmpty(adminOrderList)){
            for (AdminOrder item : adminOrderList) {
                Date createTime = item.getCreateTime();
                String dayStr = MyDateUtils.getDayStr(createTime);
                countMap.put(dayStr,countMap.get(dayStr)+1);
            }
        }

        Set<Map.Entry<String, Integer>> entrySet = countMap.entrySet();
        for (Map.Entry<String, Integer> stringIntegerEntry : entrySet) {
            String key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
            adminCommonStatistics.setXAxis(key);
            adminCommonStatistics.setYAxis(value);
            collect.add(adminCommonStatistics);
        }

        int todayAddCount = collect.get(6).getYAxis();

        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }

    /**
     * 新增用户
     */
    @GetMapping("/newlyAddUserList")
    @ApiOperation(value = "新增用户统计")
    public R<AdminCommonStatisticsBean> newlyAddUserList()
    {
        AdminUser adminUserParam = new AdminUser();
        adminUserParam.setDelFlag(0L);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -14);
        Date todayEndTime = MyDateUtils.getTodayEndTime();
        adminUserParam.setBeginCreateTime(beginCreateTime);
        adminUserParam.setEndCreateTime(todayEndTime);
        List<AdminUser> adminUserList = adminUserService.selectNewlyAddAdminUserList(adminUserParam);
        List<AdminCommonStatistics> collect = new ArrayList<>();
        HashMap<String,Integer> countMap = getHashMap(beginCreateTime);
        if(CollectionUtil.isNotEmpty(adminUserList)){
            for (AdminUser adminUser : adminUserList) {
                Date createTime = adminUser.getCreateTime();
                String yearMonthDayStr = MyDateUtils.getYearMonthDayStr(createTime);
                countMap.put(yearMonthDayStr,countMap.get(yearMonthDayStr)+1);
            }
        }
        Set<Map.Entry<String, Integer>> entrySet = countMap.entrySet();
        for (Map.Entry<String, Integer> stringIntegerEntry : entrySet) {
            String key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
            adminCommonStatistics.setXAxis(key.split("-")[1]+"/"+key.split("-")[2]);
            adminCommonStatistics.setYAxis(value);
            collect.add(adminCommonStatistics);
        }
        int todayAddCount = collect.get(14).getYAxis();
        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }

    @GetMapping("/newlyTotalTaskList")
    @ApiOperation(value = "每天任务总数(每日新增任务总数)")
    public R<AdminCommonStatisticsBean> newlyTotalTaskList()
    {
        AdminTaskOrdinal taskOrdinalParam = new AdminTaskOrdinal();
        taskOrdinalParam.setDelFlag(0L);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -14);
        Date currentDayEndTime = MyDateUtils.getCurrentDayEndTime(beginCreateTime);
        taskOrdinalParam.setBeginCreateTime(beginCreateTime);
        taskOrdinalParam.setEndCreateTime(currentDayEndTime);
        List<AdminTaskOrdinal> adminTaskOrdinals = adminTaskOrdinalService.selectNewlyAddTaskList(taskOrdinalParam);
        List<AdminCommonStatistics> collect = new ArrayList<>();
        AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
        adminCommonStatistics.setXAxis(MyDateUtils.getDayStr(currentDayEndTime));
        adminCommonStatistics.setYAxis(0);
        if(CollectionUtil.isNotEmpty(adminTaskOrdinals)){
            adminCommonStatistics.setYAxis(adminTaskOrdinals.size());
        }
        collect.add(adminCommonStatistics);
        for (int i = 0; i < 14; i++) {
            beginCreateTime = DateUtils.addDays(beginCreateTime, 1);
            currentDayEndTime = MyDateUtils.getCurrentDayEndTime(beginCreateTime);
            taskOrdinalParam.setBeginCreateTime(beginCreateTime);
            taskOrdinalParam.setEndCreateTime(currentDayEndTime);
            adminTaskOrdinals = adminTaskOrdinalService.selectNewlyAddTaskList(taskOrdinalParam);
            AdminCommonStatistics temp = new AdminCommonStatistics();
            temp.setXAxis(MyDateUtils.getDayStr(currentDayEndTime));
            temp.setYAxis(0);
            if(CollectionUtil.isNotEmpty(adminTaskOrdinals)){
                temp.setYAxis(adminTaskOrdinals.size());
            }
            collect.add(temp);
        }

        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        int todayAddCount = collect.get(14).getYAxis();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }

    @GetMapping("/totalTaskList")
    @ApiOperation(value = "总任务数")
    public R<AdminCommonStatisticsBean> totalTaskList()
    {
        AdminTaskOrdinal taskOrdinalParam = new AdminTaskOrdinal();
        taskOrdinalParam.setDelFlag(0L);
        List<AdminTaskOrdinal> taskOrdinalList = adminTaskOrdinalService.selectTaskOrdinalListByType(taskOrdinalParam);
        List<AdminCommonStatistics> collect = new ArrayList<>();
        HashMap<Long,Integer> typeCountMap = getTypeHashMap();
        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        if(CollectionUtil.isNotEmpty(taskOrdinalList)){
            result.setTodayCount(taskOrdinalList.size());
            for (AdminTaskOrdinal item : taskOrdinalList) {
                Long type = item.getType();
                if(null != type){
                    Integer getType = typeCountMap.get(type) == null ? 0 : typeCountMap.get(type);
                    typeCountMap.put(item.getType(),getType++);
                }
            }
        }

        HashMap<Long,String> typeMap = new LinkedHashMap<>();
        typeMap.put(0L,"真人图");
        typeMap.put(1L,"人台图");
        typeMap.put(2L,"商品图");
        typeMap.put(3L,"假发图");
        Set<Map.Entry<Long, Integer>> entrySet = typeCountMap.entrySet();
        for (Map.Entry<Long, Integer> stringIntegerEntry : entrySet) {
            Long key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
            adminCommonStatistics.setXAxis(typeMap.get(key));
            adminCommonStatistics.setYAxis(value);
            collect.add(adminCommonStatistics);
        }
        result.setData(collect);
        return R.ok(result);
    }

    @GetMapping("/totalUserList")
    @ApiOperation(value = "平台总用户数")
    public R<AdminCommonStatisticsBean> totalUserList()
    {
        AdminUser adminUserParam = new AdminUser();
        adminUserParam.setDelFlag(0L);
        Date beginCreateTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -14);
        Date currentDayEndTime = MyDateUtils.getCurrentDayEndTime(beginCreateTime);
        adminUserParam.setEndCreateTime(currentDayEndTime);
        //查找从今天开始倒推14天，即半个月前的那天的23:59:59 这个时间节点之前的所有用户数
        int listBeforeEndDateCount = adminUserService.selectTotalUserListBeforeEndDate(adminUserParam);

        List<AdminCommonStatistics> collect = new ArrayList<>();
        AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
        adminCommonStatistics.setYAxis(listBeforeEndDateCount);
        adminCommonStatistics.setXAxis(MyDateUtils.getDayStr(currentDayEndTime));
        collect.add(adminCommonStatistics);
        for (int i = 0; i < 14; i++) {
            beginCreateTime = DateUtils.addDays(beginCreateTime,1);
            currentDayEndTime = MyDateUtils.getCurrentDayEndTime(beginCreateTime);
            adminUserParam.setEndCreateTime(currentDayEndTime);
            listBeforeEndDateCount = adminUserService.selectTotalUserListBeforeEndDate(adminUserParam);
            AdminCommonStatistics temp = new AdminCommonStatistics();
            temp.setYAxis(listBeforeEndDateCount);
            temp.setXAxis(MyDateUtils.getDayStr(currentDayEndTime));
            collect.add(temp);
        }
        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        result.setTodayCount(listBeforeEndDateCount);
        result.setData(collect);
        return R.ok(result);
    }

    @GetMapping("/totalTypeTaskList")
    @ApiOperation(value = "今日任务数")
    public R<List<HashMap<String,Object>>> totalTypeTaskList()
    {
        AdminTaskOrdinal taskOrdinalParam = new AdminTaskOrdinal();
        taskOrdinalParam.setDelFlag(0L);
        Date todayBeginTime = MyDateUtils.getTodayStartTime();
        Date todayEndTime = MyDateUtils.getTodayEndTime();
        taskOrdinalParam.setBeginCreateTime(todayBeginTime);
        taskOrdinalParam.setEndCreateTime(todayEndTime);
        List<AdminUserTypeStatistics> adminUserTypeStatistics = adminTaskOrdinalService.selectTypeTaskList(taskOrdinalParam);
        List<HashMap<String,Object>> collect = new ArrayList<>();
        HashMap<Long,Integer> countUserTypeMap = new LinkedHashMap<>();
        for (int i = 0; i < 4; i++) {
            collect.add(new LinkedHashMap<>());
            countUserTypeMap.put((long)i,0);
        }

        if(CollectionUtil.isNotEmpty(adminUserTypeStatistics)){
            for (AdminUserTypeStatistics item : adminUserTypeStatistics) {
                Long type = item.getType();
                countUserTypeMap.put(type, item.getCount());
            }
        }

        //任务类型(0-真人图，1-人台图，2-商品图，3-假发图)
        HashMap<Long,String> typeMap = new LinkedHashMap<>();
        typeMap.put(0L,"真人图");
        typeMap.put(1L,"人台图");
        typeMap.put(2L,"商品图");
        typeMap.put(3L,"假发图");
        Set<Map.Entry<Long, Integer>> entrySet = countUserTypeMap.entrySet();
        for (Map.Entry<Long, Integer> stringIntegerEntry : entrySet) {
            Long type = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            HashMap<String,Object> map = new LinkedHashMap<>();
            map.put("xaxis",value);
            String typeVal = typeMap.get(type);
            map.put("yaxis", typeVal);
            if("人台图".equals(typeVal)){
                collect.set(0,map);
            }else if("真人图".equals(typeVal)){
                collect.set(1,map);
            }else if("假发图".equals(typeVal)){
                collect.set(2,map);
            }else {
                collect.set(3,map);
            }
        }
        return R.ok(collect);
    }

    @GetMapping("/newlyAccessUserList")
    @ApiOperation(value = "今日访问用户数")
    public R<AdminCommonStatisticsBean> newlyAccessUserList()
    {
        AdminUser adminUserParam = new AdminUser();
        adminUserParam.setDelFlag(0L);
        Date beginLoginTime = DateUtils.addDays(MyDateUtils.getTodayStartTime(), -6);
        Date todayEndTime = MyDateUtils.getTodayEndTime();
        adminUserParam.setBeginCreateTime(beginLoginTime);
        adminUserParam.setEndCreateTime(todayEndTime);
        List<AdminUser> adminUserList = adminUserService.selectNewlyAccessUserList(adminUserParam);
        List<AdminCommonStatistics> collect = new ArrayList<>();
        HashMap<String,Integer> countMap = getMonthDayHashMap(beginLoginTime);
        if(CollectionUtil.isNotEmpty(adminUserList)){
            for (AdminUser item : adminUserList) {
                Date loginTime = item.getLoginTime();
                String dayStr = MyDateUtils.getDayStr(loginTime);
                countMap.put(dayStr,countMap.get(dayStr)+1);
            }
        }

        Set<Map.Entry<String, Integer>> entrySet = countMap.entrySet();
        for (Map.Entry<String, Integer> stringIntegerEntry : entrySet) {
            String key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            AdminCommonStatistics adminCommonStatistics = new AdminCommonStatistics();
            adminCommonStatistics.setXAxis(key);
            adminCommonStatistics.setYAxis(value);
            collect.add(adminCommonStatistics);
        }
        int todayAddCount = collect.get(6).getYAxis();
        AdminCommonStatisticsBean result = new AdminCommonStatisticsBean();
        result.setData(collect);
        result.setTodayCount(todayAddCount);
        return R.ok(result);
    }


    @GetMapping("/proudctDistributionList")
    @ApiOperation(value = "产品生产分布")
    public R<List<HashMap<String,Object>>> proudctDistributionList()
    {
        List<ProudctDistributionStatistics> list = adminTaskOrdinalService.proudctDistributionList();
        HashMap<String,String> typeHashMap = new HashMap<>();
        typeHashMap.put("0","快捷描述");
        typeHashMap.put("1","预设模块");
        typeHashMap.put("2","高级描述");
        List<HashMap<String,Object>> resultMap = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            for (ProudctDistributionStatistics item : list) {
                String desType = item.getDesType();
                List<HashMap<String,Object>> arrayList = new ArrayList<>();
                Integer name0 = item.getName0();
                HashMap<String,Object> map0 = new HashMap<>();
                map0.put("type","真人图");
                map0.put("count",name0);

                Integer name1 = item.getName1();
                HashMap<String,Object> map1 = new HashMap<>();
                map1.put("type","人台图");
                map1.put("count",name1);

                Integer name2 = item.getName2();
                HashMap<String,Object> map2 = new HashMap<>();
                map2.put("type","商品图");
                map2.put("count",name2);

                Integer name3 = item.getName3();
                HashMap<String,Object> map3 = new HashMap<>();
                map3.put("type","假发图");
                map3.put("count",name3);

                arrayList.add(map0);
                arrayList.add(map3);
                arrayList.add(map2);
                arrayList.add(map1);

                HashMap<String,Object> dataMap = new LinkedHashMap<>();

                dataMap.put("desType",typeHashMap.get(desType));
                dataMap.put("data",arrayList);

                resultMap.add(dataMap);
            }
        }
        return R.ok(resultMap);
    }

    private HashMap<Long, Integer> getTypeHashMap() {
        HashMap<Long,Integer> countTypeMap = new LinkedHashMap<>();
        countTypeMap.put(1L,0);
        countTypeMap.put(0L,0);
        countTypeMap.put(3L,0);
        countTypeMap.put(2L,0);
        return countTypeMap;
    }

    private HashMap<String, Double> getMoneyMonthDayHashMap(Date start) {
        HashMap<String,Double> countMap = new LinkedHashMap<>();
        String dayStr = MyDateUtils.getDayStr(start);
        countMap.put(dayStr,0.0);
        for (int i = 0; i < 6; i++) {
            start = DateUtils.addDays(start,1);
            dayStr = MyDateUtils.getDayStr(start);
            countMap.put(dayStr,0.0);
        }
        return countMap;
    }

    private HashMap<String, Integer> getMonthDayHashMap(Date start) {
        HashMap<String,Integer> countMap = new LinkedHashMap<>();
        String dayStr = MyDateUtils.getDayStr(start);
        countMap.put(dayStr,0);
        for (int i = 0; i < 6; i++) {
            start = DateUtils.addDays(start,1);
            dayStr = MyDateUtils.getDayStr(start);
            countMap.put(dayStr,0);
        }
        return countMap;
    }

    private HashMap<String, Integer> getHashMap(Date start) {
        HashMap<String,Integer> countMap = new LinkedHashMap<>();
        String yearMonthDayStr = MyDateUtils.getYearMonthDayStr(start);
        countMap.put(yearMonthDayStr,0);
        for (int i = 1; i < 15; i++) {
            start = DateUtils.addDays(start,1);
            yearMonthDayStr = MyDateUtils.getYearMonthDayStr(start);
            countMap.put(yearMonthDayStr,0);
        }
        return countMap;
    }


    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('adminorder:adminorder:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminOrder adminOrder)
    {
        List<AdminOrder> list = adminOrderService.selectAdminOrderList(adminOrder);
        ExcelUtil<AdminOrder> util = new ExcelUtil<AdminOrder>(AdminOrder.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('adminorder:adminorder:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") String orderId)
    {
        return success(adminOrderService.selectAdminOrderByOrderId(orderId));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('adminorder:adminorder:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdminOrder adminOrder)
    {
        return toAjax(adminOrderService.insertAdminOrder(adminOrder));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('adminorder:adminorder:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdminOrder adminOrder)
    {
        return toAjax(adminOrderService.updateAdminOrder(adminOrder));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('adminorder:adminorder:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable String[] orderIds)
    {
        return toAjax(adminOrderService.deleteAdminOrderByOrderIds(orderIds));
    }
}
