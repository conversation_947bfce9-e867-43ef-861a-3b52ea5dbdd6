---
description:
globs:
alwaysApply: false
---
### 后端结构与约定（Spring Boot + MyBatis）

- **配置文件**：
  - 主配置：[ruoyi-admin/src/main/resources/application.yml](mdc:ruoyi-admin/src/main/resources/application.yml)
  - 开发配置：[ruoyi-admin/src/main/resources/application-dev.yml](mdc:ruoyi-admin/src/main/resources/application-dev.yml)
  - 本地配置：[ruoyi-admin/src/main/resources/application-local.yml](mdc:ruoyi-admin/src/main/resources/application-local.yml)

- **MyBatis**：
  - 全局配置：[ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml](mdc:ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml)
  - XML Mapper 位置：`ruoyi-admin/src/main/resources/mapper/**`（各业务子目录）

- **代码层次（常见约定）**：
  - 控制层（Controller）：`ruoyi-admin/src/main/java/com/dataxai/**/controller/**`
  - 服务层（Service）：`ruoyi-admin/src/main/java/com/dataxai/**/service/**`
  - 数据层（Mapper/DAO）：`ruoyi-admin/src/main/java/com/dataxai/**/mapper/**` 对应 `resources/mapper/**.xml`
  - 领域对象/实体：`ruoyi-admin/src/main/java/com/dataxai/**/domain/**` 或 `**/entity/**`
  - 配置与拦截器：`ruoyi-*/src/main/java/**/config/**`、`**/interceptor/**`

- **静态与模板资源**：
  - 静态页：[ruoyi-admin/src/main/resources/static/socketio-test.html](mdc:ruoyi-admin/src/main/resources/static/socketio-test.html)
  - Excel 模板：`ruoyi-admin/src/main/resources/excel-template/`

- **Docker**：
  - [ruoyi-admin/src/main/docker/Dockerfile](mdc:ruoyi-admin/src/main/docker/Dockerfile)

- **密钥与外部配置模板**：
  - [ruoyi-admin/src/main/resources/config/apiclient_key.pem.template](mdc:ruoyi-admin/src/main/resources/config/apiclient_key.pem.template)

提示：若定位具体接口实现，先从 Controller 包入手，结合对应 Service 与 Mapper；XML 映射在 `resources/mapper` 同名或同包路径下。
