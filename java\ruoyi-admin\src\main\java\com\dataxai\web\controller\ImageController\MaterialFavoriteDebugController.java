package com.dataxai.web.controller.ImageController;

import com.dataxai.web.domain.MaterialStyleUser;
import com.dataxai.web.mapper.MaterialStyleUserMapper;
import com.dataxai.web.service.MaterialStyleService;
import com.dataxai.web.domain.MaterialStyle;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/debug/favorite")
@Api(tags = "收藏功能调试")
public class MaterialFavoriteDebugController {

    @Autowired
    private MaterialStyleUserMapper materialStyleUserMapper;
    
    @Autowired
    private MaterialStyleService materialStyleService;

    @GetMapping("/check-data/{userId}")
    @ApiOperation("检查用户收藏数据")
    public R<Object> checkUserFavoriteData(@PathVariable Integer userId,
                                          @RequestParam(required = false) Integer taskType) {
        try {
            // 1. 检查收藏记录数量
            int count = materialStyleUserMapper.countByUser(userId, taskType);
            System.out.println("用户 " + userId + " 的收藏数量: " + count);
            
            // 2. 获取收藏记录列表
            List<MaterialStyleUser> favoriteList = materialStyleUserMapper.selectByUser(userId, 0, 10, taskType);
            System.out.println("收藏记录列表大小: " + favoriteList.size());
            
            // 3. 检查每个收藏记录对应的素材
            for (MaterialStyleUser favorite : favoriteList) {
                System.out.println("收藏记录ID: " + favorite.getId() + 
                                 ", 素材ID: " + favorite.getMaterialStyleId() + 
                                 ", 用户ID: " + favorite.getUserId() +
                                 ", 任务类型: " + favorite.getTaskType());
                
                MaterialStyle materialStyle = materialStyleService.getById(favorite.getMaterialStyleId());
                if (materialStyle != null) {
                    System.out.println("  -> 找到素材: " + materialStyle.getName());
                } else {
                    System.out.println("  -> 素材不存在或已删除");
                }
            }
            
            return R.success("检查完成，请查看控制台输出");
            
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/raw-favorites/{userId}")
    @ApiOperation("获取原始收藏数据")
    public R<List<MaterialStyleUser>> getRawFavorites(@PathVariable Integer userId,
                                                     @RequestParam(required = false) Integer taskType) {
        try {
            List<MaterialStyleUser> favoriteList = materialStyleUserMapper.selectByUser(userId, 0, 10, taskType);
            return R.success(favoriteList);
        } catch (Exception e) {
            return R.fail("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/test-style-service/{styleId}")
    @ApiOperation("测试素材服务")
    public R<MaterialStyle> testStyleService(@PathVariable Integer styleId) {
        try {
            MaterialStyle materialStyle = materialStyleService.getById(styleId);
            return R.success(materialStyle);
        } catch (Exception e) {
            return R.fail("获取失败: " + e.getMessage());
        }
    }
}
