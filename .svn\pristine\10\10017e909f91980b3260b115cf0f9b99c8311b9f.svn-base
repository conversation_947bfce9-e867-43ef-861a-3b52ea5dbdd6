/**
 * @Author: wuy<PERSON>
 * @Date: 2023/12/20
 * @Description: ""
 */

import { IMaskList, IMatrixImage } from '@/component/layer-recognition/types'
import { DRAW_COLOR, DRAW_OPACITY } from '@/constant'
import { hexToRgb } from '@/utils/transformColor'

const [r, g, b, a] = hexToRgb(DRAW_COLOR)

export function transposeArray(img: number[], shape = [500, 500]) {
	const transposed: number[][] = new Array(shape[1])
		.fill(0)
		.map(() => new Array(shape[0]).fill(0))
	for (let i = 0; i < shape[0]; i++) {
		for (let j = 0; j < shape[1]; j++) {
			transposed[j][i] = img[i * shape[1] + j]
		}
	}
	return transposed
}

/**
 * rle格式图片信息转换为mask信息
 */
export function rle2mask(mask_rle: string, shape = [500, 500]) {
	const s = mask_rle.split(' ')
	let starts = s.filter((_, index) => index % 2 === 0).map(Number)
	const lengths = s.filter((_, index) => index % 2 !== 0).map(Number)
	starts = starts.map((start) => start - 1)
	const ends = starts.map((start, index) => start + lengths[index])
	const img = new Array(shape[0] * shape[1]).fill(0)

	for (let i = 0; i < starts.length; i++) {
		for (let j = starts[i]; j < ends[i]; j++) {
			img[j] = 1
		}
	}

	return img
}

/**
 * 转换mask图片信息，并设置mask的填充颜色
 */
export function transformMaskImage(
	item: IMaskList,
	_width: number,
	_height: number
): IMatrixImage {
	let canvas = document.createElement('canvas')
	let canvasContext = canvas.getContext('2d')
	canvas.width = _width
	canvas.height = _height
	let rgbaData = transposeArray(rle2mask(item.mask || '', [_width, _height]), [
		_width,
		_height
	])
	for (let y = 0; y < rgbaData.length; y++) {
		let row = rgbaData[y]
		for (let x = 0; x < row.length; x++) {
			let dot = rgbaData[y][x]
			if (1 === dot && canvasContext) {
				// 值为1的点填充颜色
				canvasContext.fillStyle = `rgba(${r}, ${g}, ${b},${DRAW_OPACITY})`
				canvasContext.fillRect(x, y, 1, 1)
			}
		}
	}
	// imageData：当前层的图片（base64格式）
	// matrix：上边生成的二维数组
	return { imageData: canvas.toDataURL('image/png') }
}

/**
 * 图片合成
 */
export function drawingLayerImages(
	images: string[],
	width: number,
	height: number
): Promise<string> {
	const { promise, resolve, reject } = Promise.withResolvers()

	const canvas = document.createElement('canvas')
	canvas.width = width // 设置canvas宽
	canvas.height = height // 设置canvas高
	images = images.filter((image) => !!image)
	const ctx = canvas.getContext('2d')!
	let loadedImages = 0
	if (!ctx) {
		return reject('合成失败')
	}
	if (images.length === 0) {
		resolve('')
	} else {
		images.forEach(function (src) {
			const img = new Image()
			img.src = src
			img.crossOrigin = 'Anonymous'
			img.onload = function () {
				loadedImages++

				// 绘制每张图片到 canvas 上
				ctx.drawImage(img, 0, 0)

				// 如果所有图片都加载完成，保存合并后的图片
				if (loadedImages === images.length) {
					// 获取图片的像素数据
					const imageData = ctx.getImageData(0, 0, img.width, img.height)
					// 此处暂时不需要将图片修改为黑白像素点
					// const data = imageData?.data
					// 转换为黑白效果
					// for (let i = 0; i < data.length; i += 4) {
					// 	// 将 R、G、B 设置为0
					// 	data[i] = r
					// 	data[i + 1] = g
					// 	data[i + 2] = b
					// }
					// 将修改后的数据放回 canvas
					ctx.putImageData(imageData, 0, 0)
					// 导出为 base64 图片
					const mergedImageBase64 = canvas.toDataURL('image/png')
					// 此处需要判断转换为 base 64 的图片大小，如果过大的化，要进行压缩
					// if (mergedImageBase64.length > UPLOAD_MAX_SIZE *)
					// 如果需要，你可以将mergedImageBase64图片用于其他操作，比如发送到服务器

					resolve(mergedImageBase64)
				}
			}
		})
	}

	return promise
}

export const inverseImageLayer = (
	images: string[],
	width: number,
	height: number
): Promise<string> => {
	const { promise, resolve, reject } = Promise.withResolvers()
	drawingLayerImages(images, width, height).then((res) => {
		if (res) {
			const base64Img = new Image()
			base64Img.src = res
			base64Img.onload = () => {
				const img = drawImageWithInversion(base64Img, width, height)
				resolve(img)
			}
		} else {
			resolve('')
		}
	})
	return promise
}

// 绘制图像，并根据isInverted标志位决定是否反选
function drawImageWithInversion(
	image: HTMLImageElement,
	width: number,
	height: number
): string {
	let canvas: HTMLCanvasElement | null = document.createElement('canvas')
	const ctx = canvas.getContext('2d')!
	canvas.width = width
	canvas.height = height
	ctx.clearRect(0, 0, width, height) // 清除画布
	ctx.drawImage(image, 0, 0)
	ctx.globalCompositeOperation = 'source-out'
	ctx.fillStyle = `rgb(${r}, ${g}, ${b})`
	ctx.fillRect(0, 0, width, height)
	const imageData = ctx.getImageData(0, 0, width, height)
	const data = imageData?.data

	for (let i = 0; i < data.length; i += 4) {
		if (data[i] !== r || data[i + 1] !== g || data[i + 2] !== b) {
			data[i + 3] = 0
		} else {
			data[i + 3] = 179
		}
	}

	ctx.putImageData(imageData, 0, 0)

	const imageBase64 = canvas!.toDataURL('image/png')
	return imageBase64
}
