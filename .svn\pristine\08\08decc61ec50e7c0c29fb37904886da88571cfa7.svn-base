
import { useMemo, useEffect, useState, useContext } from 'react';
import { Slider, Select, Modal, Button, Dropdown, Radio, Checkbox, message, Pagination, Tooltip, Switch, Spin, Input } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { useMemoizedFn } from 'ahooks'
import { CopyOutlined, FileTextOutlined, ScissorOutlined, BgColorsOutlined, ZoomInOutlined, DoubleRightOutlined, QuestionCircleOutlined, AppstoreOutlined, SafetyOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import dayjs from 'dayjs'
import { useLocation, useNavigate } from 'react-router-dom';
import { batchDownloadImages } from '@/utils/downFile'
import { getCommonStyleList, addBatch, uploadSynchronization, getInfoPage, createByImageUrls } from '@/api/task'
import { log } from 'console';
import { batchZipUrl } from '@/api/common'


export const SimilarDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()
    // 模拟图片数据
    const [imageList, setImageList] = useState<any>([
        {
            image: [
            ],
            orgImg: '',
            smallOrgImg: ''
        },
    ])

    const { batchId } = location.state || {};
    const [record, setRecord] = useState<any>();
    const [detailTotal, setDetailTotal] = useState(0);
    const [pageLoading, setPageLoading] = useState(false);
    const fetchDetail = (pageNumber: number, pageSize: number,) => {
        setPageLoading(true)
        getInfoPage({ batchId, pageNumber, pageSize }).then((res: any) => {
            setRecord(res)
            setDetailTotal(res.total)
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }
    useEffect(() => {
        fetchDetail(1, 12)
    }, []);

    let listData: any = []
    useEffect(() => {
        record?.data.map((item: any) => {
            listData.push({
                image: [
                    { small: item.taskOrdinalList[0].ordinalImgResultList[0].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[0].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[0].hasUploaded },
                    { small: item.taskOrdinalList[0].ordinalImgResultList[1].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[1].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[1].hasUploaded },
                    { small: item.taskOrdinalList[0].ordinalImgResultList[2].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[2].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[2].hasUploaded },
                    { small: item.taskOrdinalList[0].ordinalImgResultList[3].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[3].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[3].hasUploaded },
                ],
                orgImg: item.originalUrl,
                smallOrgImg: item.thumbnailUrl,
            })

        })
        setImageList(listData)
    }, [record]);
    const [selectedImages, setSelectedImages] = useState<string[]>([]);
    const handleSelect = (imageUrl: string) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(imageUrl)
                ? prev.filter(url => url !== imageUrl)
                : [...prev, imageUrl];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions);
        if (!showBatchActions === false) {
            setSelectedImages([]); // 清空已选图片
        }
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };
    //实时更新选择状态
    useEffect(() => {
        setShowBatchActions(selectedImages.length > 0);
    }, [selectedImages]);


    const items = [
        {
            key: '8',
            label: '文生图',
            icon: <FileTextOutlined />,
            path: 'textToImg'
        },
        {
            key: '9',
            label: '相似图裂变',
            icon: <CopyOutlined />,
            path: 'similar'
        },
        {
            key: '6',
            label: '平铺图生成',
            icon: <AppstoreOutlined />,
            path: 'continuous'
        },
        {
            key: '14',
            label: '图案裁剪',
            icon: <ScissorOutlined />,
            path: 'cut'
        },
        {
            key: '11',
            label: '图片去背景',
            icon: <BgColorsOutlined />,
            path: 'blockingOut'
        },
        {
            key: '12',
            label: '图片变清晰',
            icon: <ZoomInOutlined />,
            path: 'clear'
        },
        {
            key: '17',
            label: '侵权风险过滤',
            icon: <SafetyOutlined />,
            path: 'filter'
        },
    ];
    const [taskInfo, setTaskInfo] = useState({
        type: '',
        typeName: '',
        path: '',
        isModalOpen: false
    });

    const selectTaskType = (key: string) => {
        const action = items.find(item => item.key === key)?.label || '';
        setTaskInfo({
            type: key,
            typeName: action,
            path: items.find(item => item.key === key)?.path || '',
            isModalOpen: true
        });
        setRadioValue('1'); //初始化风格选项为不选风格并清空当前风格
        setIsMattingFree(false) // 免抠图生成（默认否）
        setImageScale(key === '9' ? '原图比例' : '1:1') // 图片生成比例（相似图裂变时默认原图比例, 否则1：1）
        setSimilarity(0.5) // 相似图相似度（默认0.5）
        setGenerateQuantity('1') // 放大倍数（默认1倍）

        setCurrentStyle({
            styleId: '',
            id: '',
            thumbnailImgUrl: '',
            originImgUrl: '',
            style: '',
            stylePrompt: ''
        })
        // 根据任务类型设置不同的type参数
        const styleType = key === '8' ? 2 : 1; // 文生图(type=8)用2，平铺图生成(type=6)用1
        fetchCommonStyleData(styleType, 1, 12);
    };

    const createTask = async () => {
        const handleSuccess = () => {
            messageApi.success(`创建${taskInfo.typeName}成功`);
            // window.location.href = `/workspace/batchTools/${taskInfo.path}/index`; // 跳转到对应的任务列表页
            setTaskInfo({
                type: '',
                typeName: '',
                path: '',
                isModalOpen: false
            });
            userinfoService.refresh(); // 刷新用户积分
        };
        try {
            let params = {};
            if (taskInfo.type == '6') {
                params = {
                    styleId: radioValue == '1' ? '' : currentStyle?.styleId,
                    style: radioValue == '1' ? '无风格' : currentStyle?.style,
                    stylePrompt: radioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                    imageScale,
                }
            } else if (taskInfo.type == '8') {
                params = {
                    styleId: radioValue == '1' ? '' : currentStyle?.styleId,
                    style: radioValue == '1' ? '无风格' : currentStyle?.style,
                    stylePrompt: radioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                    isMattingFree: isMattingFree ? 1 : 0,
                    imageScale,
                }
            } else if (taskInfo.type == '9') {
                params = {
                    similarity,
                    imageScale,
                }
            } else if (taskInfo.type == '12') {
                params = {
                    magnification: generateQuantity,
                }
            }
            if (taskInfo.type != '17') {
                const response = await addBatch({
                    type: taskInfo.type,
                    imgUrl: selectedImages.map(item => item).join(','),
                    taskParam: params ? JSON.stringify(params) : ''
                });
                if (response) handleSuccess();
            } else {
                if (!isChecked) {
                    messageApi.error('请先勾选确认侵权风险过滤功能使用须知')
                    return
                }
                const response = await createByImageUrls({ imageUrls: selectedImages.map(item => item) });
                if (response) handleSuccess();
            }

        } catch (err: any) {
            messageApi.error(`创建失败: ${err?.data?.msg || err?.msg}`)
        }
    };
    const generateQuantityOptions: CheckboxGroupProps<string>['options'] = [
        { label: '1X', value: '1' },
        { label: '2X', value: '2' },
        { label: '4X', value: '4' },
    ];

    // 放大倍数
    const [generateQuantity, setGenerateQuantity] = useState('1')
    const handleQuantityChange = (e: any) => {
        setGenerateQuantity(e.target.value)
    };
    //原图相似度
    const [similarity, setSimilarity] = useState(0.5);
    const onSimilarityChange = (value: number) => {
        setSimilarity(value)
    };
    //生成图片比例
    const [imageScale, setImageScale] = useState(taskInfo.type === '9' ? '原图比例' : '1:1')
    const handleChange = (value: string) => {
        console.log(`selected ${value}`);
        setImageScale(value)
    };
    // 是否选择风格
    const [radioValue, setRadioValue] = useState('1');

    const handleRadioChange = (e: any) => {
        setRadioValue(e.target.value)
        if (e.target.value == '1') {
            setCurrentStyle({
                styleId: '',
                id: '',
                thumbnailImgUrl: '',
                originImgUrl: '',
                style: '',
                stylePrompt: ''
            })
        } else {
            setCurrentStyle(commonStyleList[0])
        }

    }

    interface CustomStyle {
        styleId: string;
        id: string;
        originImgUrl: string;
        thumbnailImgUrl: string;
        stylePrompt: string;
        style: string;
        // 其他属性...
    }

    interface CustomStyleListResponse {
        data: CustomStyle[];
        total: number;
        default: any;
        // 其他属性...
    }

    // 参考风格列表
    const [commonStyleList, setCommonStyleList] = useState<CustomStyle[]>([]);
    // 无风格数据详情
    const [noStyleData, setNoStyleData] = useState<null | {
        stylePrompt: string
    }>(null)
    // 参考风格总页数
    const [commonStyleTotal, setCommonStyleTotal] = useState(0)
    // 参考风格当前页
    const [commonStylePage, setCommonStylePage] = useState(1)
    // 当前选中的风格
    const [currentStyle, setCurrentStyle] = useState<null | {
        styleId: string
        id: string
        originImgUrl: string
        thumbnailImgUrl: string
        style: string
        stylePrompt: string
    }>(null)


    //获取参考风格列表
    const fetchCommonStyleData = async (type: number, pagenum: number, pagesize: number) => {
        try {
            const response = await getCommonStyleList(type, pagenum, pagesize) as CustomStyleListResponse;
            setCommonStyleList(response.data);
            setNoStyleData(response.default || { stylePrompt: '' });

            if (response.data.length) {
                setCurrentStyle(response.data[0])
            } else {
                setCurrentStyle({
                    styleId: '',
                    id: '',
                    thumbnailImgUrl: '',
                    originImgUrl: '',
                    style: '',
                    stylePrompt: ''
                })
            }

            setCommonStyleTotal(Math.ceil(response.total / pagesize));// 总页数
            setCommonStylePage(pagenum);
        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };
    // 参考风格分页
    const commonStylePageChange = (pagenum: any) => {
        const styleType = taskInfo.type === '8' ? 2 : 1; // 文生图(type=8)用2，平铺图生成(type=6)用1
        fetchCommonStyleData(styleType, pagenum, 12)
    }
    useEffect(() => {
        // fetchCommonStyleData(2, 1, 12)
    }, []);
    // 参考风格选择弹窗显影
    const [styleModalOpen, setStyleModalOpen] = useState(false)
    // 点击参考风格反显
    const selectStyle = (item: any) => {

        setCurrentStyle(item)
        setStyleModalOpen(false)
    }

    let commonStyleListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
        return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
            <img onClick={() => selectStyle(item)}
                src={item.originImgUrl || item.originImgUrl || ''}
                className={`${currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px] `}
                style={{ objectFit: 'cover' }}
                alt="" />
            <p style={{ textAlign: 'center' }} >{item.style}</p>
        </div>
    }) : null

    // 原图图片列表渲染
    const getOriImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                >
                    <LikeImage
                        type={1}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={image.orgImg}
                        oriImgUrl={image.orgImg}
                        smallImgUrl={image.smallOrgImg || image.orgImg}
                        markImgUrl={image.orgImg}
                        queue={1}
                        progress={100}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    // 结果图图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (item: any, index: number) => {
            let previewImageList = item.image.map(
                (result: any) => result.taskImage

            )
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                >
                    <LikeImage
                        type={1}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={item.image[index].taskImage}
                        oriImgUrl={item.orgImg}
                        smallImgUrl={item.image[index].small}
                        markImgUrl={item.orgImg}
                        queue={1}
                        progress={100}
                        previewImages={previewImageList}
                        index={index}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={true}
                    />
                </div>
            )
        }
    )
    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        setDownloadLoading(true)
        batchZipUrl({ imageUrls: selectedImages, type: 9 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false)
        })
        // batchDownloadImages(selectedImages);
    };

    const synchronizeItems = [
        {
            key: 1,
            label: '上传设计器',
        },
        {
            key: 2,
            label: '上传设计平台',
        },
    ];

    const [synchronizeType, setSynchronizeType] = useState<number>(1);

    const [uploadLoading, setUploadLoading] = useState(false);
    const [tagModalVisible, setTagModalVisible] = useState(false);
    const [tagValue, setTagValue] = useState('');

    // 展示标签输入框
    const showTagModal = () => {
        setTagModalVisible(true);
        setTagValue('');
    };

    // 上传设计器
    const handleUploadDesigner = () => {
        setUploadLoading(true);
        uploadSynchronization({ imageUrls: selectedImages, tags: tagValue, platform: synchronizeType }).then((res: any) => {
            messageApi.success(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}成功`);
            fetchDetail(detailPage, 12)
        }).catch(err => {
            messageApi.error(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}失败: ${err?.data?.msg || err?.msg}, 请重试`);
        }).finally(() => {
            setUploadLoading(false);
            setTagModalVisible(false);
            setTagValue('');
        })
    };

    const [detailPage, setDetailPage] = useState(1);
    const handleDetailPage = (page: number, pageSize: number) => {
        setDetailPage(page);  // 更新页码状态
        setSelectedImages([]); // 清空已选图片
        fetchDetail(page, pageSize)
    }

    // 是否免抠图生成（默认否）
    const [isMattingFree, setIsMattingFree] = useState(false)
    const onSwitchChange = (checked: boolean) => {
        console.log(`switch to ${checked}`);
        setIsMattingFree(checked)
    };

    const [isGuideModalVisible, setIsGuideModalVisible] = useState(false)
    // 是否勾选
    const [isChecked, setIsChecked] = useState(false)
    // 同意侵权风险过滤功能使用须知
    const handleAgree = () => {
        setIsChecked(true)
        setIsGuideModalVisible(false)
    }

    return (
        <div className='h-full w-full p-[20px]'>
            {contextHolder}  {/* 这里确保 Modal 挂载 */}
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : (<>
                {/* 标签输入弹窗 start */}
                <Modal
                    title={synchronizeType === 1 ? '上传设计器标签' : '上传设计平台标签'}
                    open={tagModalVisible}
                    onOk={handleUploadDesigner}
                    onCancel={() => {
                        setTagModalVisible(false);
                        setTagValue('');
                    }}
                    okText="确认上传"
                    cancelText="取消"
                    confirmLoading={uploadLoading}
                    centered
                >
                    <Input
                        placeholder="请输入标签"
                        value={tagValue}
                        onChange={(e) => setTagValue(e.target.value)}
                    />
                </Modal>
                {/* 标签输入弹窗 end */}
                {/* 创建任务弹窗 start */}
                <Modal title={`新建${taskInfo.typeName}任务`} okText="创建" open={taskInfo.isModalOpen} centered onOk={createTask} onCancel={() => setTaskInfo(prev => ({ ...prev, isModalOpen: false }))}>
                    <div className=' min-h-[140px] flex  flex-col items-center justify-center'>
                        <p >将对选中的 <span style={{ color: '#32649f' }}>{selectedImages?.length}</span> 张图片执行{taskInfo.typeName}操作</p>
                        {(taskInfo.type === '8' || taskInfo.type === '6') &&
                            <div>
                                <div className='flex justify-between items-center mt-8'>风格选择
                                    <Radio.Group style={{ width: '260px' }} block options={[
                                        { label: '不选风格', value: '1' },
                                        { label: '风格选择', value: '2' },
                                    ]} value={radioValue} onChange={(e) => handleRadioChange(e)} />
                                </div>
                                {radioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setStyleModalOpen(true)}>
                                    <img className='w-[80px] h-[80px] mr-[10px]' style={{ objectFit: 'cover' }} src={currentStyle?.originImgUrl || currentStyle?.thumbnailImgUrl || ""} />
                                    <div className='w-[200px]'>
                                        <p className='text-[18px] mb-[12px]'>参考风格</p>
                                        <p>{currentStyle ? currentStyle.style : ''}</p>
                                    </div>
                                    <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                                </div>}
                                {taskInfo.type === '8' && <div className='flex  justify-between items-center  text-normal mt-[20px] mb-[10px]'>
                                    <p>免抠图生成<Tooltip title="利用AI自动从图片中精准分离出前景对象并去除背景,可以省去用户手动抠图的操作。"><QuestionCircleOutlined className=' ml-[6px]' /></Tooltip></p>
                                    <Switch checked={isMattingFree} onChange={onSwitchChange} />
                                </div>}
                                <div className='flex justify-between items-center  mt-4 mb-8'>
                                    <p className='w-[100px]'>生成图片比例</p>
                                    <Select
                                        defaultValue="1:1"
                                        style={{ width: 280 }}
                                        onChange={handleChange}
                                        value={imageScale}
                                        options={[
                                            { value: '1:1', label: '1 : 1' },
                                            { value: '2:3', label: '2 : 3' },
                                            { value: '3:2', label: '3 : 2' },
                                            { value: '3:4', label: '3 : 4' },
                                            { value: '4:3', label: '4 : 3' },
                                            { value: '9:16', label: '9 : 16' },
                                            { value: '16:9', label: '16 : 9' },
                                        ]}
                                    />
                                </div>

                                {/* 参考风格弹窗 start */}
                                <Modal width={800} title="" footer={null} open={styleModalOpen} onCancel={() => setStyleModalOpen(false)}>
                                    <div >
                                        <div className='flex flex-wrap mt-[20px] h-[340px]'>
                                            {commonStyleListEL}
                                        </div>
                                        <label>
                                            <ul className='flex justify-center  mt-[20px]'>
                                                {Array.from({ length: commonStyleTotal }, (_, index) => (
                                                    <li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
                                                        style={{ lineHeight: '18px' }} onClick={() => { commonStylePageChange(index + 1) }}>{index + 1}</li>
                                                ))}
                                                <li >{'共' + commonStyleTotal + '页'}</li>
                                            </ul>
                                        </label>
                                    </div>
                                </Modal>
                                {/* 参考风格弹窗 end */}
                            </div>
                        }
                        {taskInfo.type === '9' &&
                            <div>
                                <div className='flex justify-between items-center mt-4'>
                                    <p className='w-[110px]'>原图相似度</p>
                                    <span>低</span>
                                    <Slider
                                        styles={{
                                            rail: {
                                                background: '#ddd'
                                            },
                                            track: {
                                                background: 'var(--primary-color)'
                                            },
                                            handle: {
                                                background: 'var(--primary-color)'
                                            },
                                        }}
                                        className='w-[220px]'
                                        min={0.3}
                                        max={0.8}
                                        step={0.05}
                                        value={similarity}
                                        onChange={onSimilarityChange}
                                        tooltip={{ open: false }}
                                    />
                                    <span>高</span>
                                </div>
                                <div className='flex justify-between items-center  mt-4'>
                                    <p className='w-[110px]'>生成图片比例</p>
                                    <Select
                                        defaultValue="原图比例"
                                        style={{ width: 260 }}
                                        onChange={handleChange}
                                        value={imageScale}
                                        options={[
                                            { value: '原图比例', label: '原图比例' },
                                            { value: '1:1', label: '1 : 1' },
                                            { value: '2:3', label: '2 : 3' },
                                            { value: '3:2', label: '3 : 2' },
                                            { value: '3:4', label: '3 : 4' },
                                            { value: '4:3', label: '4 : 3' },
                                            { value: '9:16', label: '9 : 16' },
                                            { value: '16:9', label: '16 : 9' },
                                        ]}
                                    />
                                </div>

                            </div>
                        }
                        {taskInfo.type === '12' &&
                            <div>
                                <p className='w-[360px] flex items-center justify-between text-normal mt-[26px] '>放大倍数
                                    <Radio.Group
                                        size="large"
                                        block
                                        options={generateQuantityOptions}
                                        onChange={handleQuantityChange}
                                        defaultValue="1"
                                        value={generateQuantity}
                                        optionType="button"
                                        buttonStyle="solid"
                                        className='w-[280px]'
                                    />
                                </p>

                            </div>
                        }
                    </div>
                    {taskInfo.type === '17' &&
                        <div className="w-full flex items-center">
                            <Checkbox
                                checked={isChecked}
                                onChange={(e) => {
                                    if (isChecked) {
                                        setIsChecked(false);
                                    } else {
                                        setIsGuideModalVisible(true);
                                    }
                                }}
                            />
                            <span className='ml-[10px] cursor-pointer' onClick={() => {
                                if (isChecked) {
                                    setIsChecked(false);
                                } else {
                                    setIsGuideModalVisible(true);
                                }
                            }}>侵权风险过滤功能使用须知</span>
                        </div>
                    }
                </Modal>
                {/* 创建任务弹窗 end */}
                {/* 侵权风险过滤功能使用须知弹窗 start */}
                <Modal
                    title="侵权风险检测功能使用须知"
                    open={isGuideModalVisible}
                    onOk={handleAgree}
                    onCancel={() => setIsGuideModalVisible(false)}
                    width={660}
                    centered
                    okText="我已知晓，同意"
                >
                    <div className='border-[1px] border-normal p-[16px] border-[#999] bg-[#fafafa] mt-4 mb-4' style={{ borderRadius: '8px' }}>
                        <p className='mb-4'>在您使用本网站提供的侵权风险过滤服务前，请您仔细阅读本声明的所有条款，您一旦开始使用该服务，即表明您无条件地接受本免责声明，您应遵守本声明和相关法律的规定。</p>
                        <p className='mb-4'>1. 我们提供的侵权风险过滤服务（包括但不限于文字、图片等内容的侵权风险分析）均基于自动化算法及公开数据，结果仅供参考，不构成任何形式的法律意见或专业建议。</p>
                        <p className='mb-4'>2. 本服务无法保证结果的绝对准确性、完整性或时效性，可能存在漏判、误判或因法律法规变化导致的偏差。</p>
                        <p className='mb-4'>3. 您需自行判断过滤检测结果的适用性，并承担因依赖该结果而产生的全部风险。对于您依据本服务做出的任何行为（如内容发布、下架、商业决策等），我们不承担法律责任。</p>
                        <p className='mb-4'>4. 如您不同意本声明内容，应立即停止使用侵权风险过滤服务。继续使用视为接受全部条款。</p>
                        <p className='mb-4'>生成页面建议提示内容：检测结果仅供参考，不构成任何形式的法律意见或专业建议。您应做出独立的判断，本网站对您依据本服务做出的决策不承担责任。</p>
                    </div>
                </Modal>
                {/* 侵权风险过滤功能使用须知弹窗 end */}


                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}  >
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center justify-start h-[60px] border-b-[1px] border-normal'>
                    <p className='mr-[20px]'>批次: {record?.batch?.batchNumber}</p>
                    <p className='mr-[20px]'>创建时间：{dayjs(record?.batch?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    {record?.data[0] && <p className='mr-[20px]'>比例尺寸：{JSON.parse(record?.data[0].taskOrdinalList[0].taskParam).imageScale}</p>}
                    <p>总数：{record?.batch?.totalAmount}
                        <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.batch?.successAmount}</span>
                        {record?.batch?.failAmount > 0 && <span style={{ color: '#CF1322', marginLeft: '6px' }}>失败：{record?.batch?.failAmount}</span>}
                    </p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="flex flex-wrap pt-[10px]">
                        {
                            imageList.map((item: any, index: number) => {
                                return (
                                    <div className='aspect-square h-[300px] mb-[15px] w-[100%] pr-[12px] p-2 relative bg-[#d5d5d5] '>
                                        <Checkbox className="itemCheckbox-similar"
                                            checked={item.image.every((img: any) => selectedImages.includes(img.taskImage))}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    // 添加所有 taskImage 到 selectedImages
                                                    setSelectedImages(prev => {
                                                        let newArray = [...prev, ...item.image.map((img: any) => img.taskImage)]
                                                        return Array.from(new Set(newArray))
                                                    });
                                                } else {
                                                    // 移除所有 taskImage 从 selectedImages
                                                    setSelectedImages(prev => prev.filter(img => !item.image.map((img: any) => img.taskImage).includes(img)));
                                                }
                                            }}
                                        />
                                        <div className='left' style={{ width: '400px', display: 'inline-block' }}>
                                            <label htmlFor="" className='text-[#fff]' style={{ fontSize: '24px', position: 'absolute', top: '10px', left: '60px' }}>原图</label>
                                            <div className="w-full h-full flex min-h-[300px] h-[300px] pt-[20px] rounded-lg items-center justify-center  relative group overflow-hidden"
                                                style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                <div className='max-w-[240px] aspect-square pr-[12px] p-2 inline-block' >
                                                    {getOriImageComponent(item)}
                                                </div>
                                                <div className='rightIcon' style={{ fontSize: '30px', width: '100px', textAlign: 'center' }}>
                                                    <DoubleRightOutlined /><DoubleRightOutlined />
                                                </div>
                                            </div>
                                        </div>
                                        <div className='right' style={{ width: 'calc(100% - 400px)', display: 'inline-flex' }}>
                                            <label htmlFor="" className='text-[#fff]' style={{ fontSize: '24px', position: 'absolute', top: '10px', left: '460px' }}>生成图</label>
                                            {item.image.map((imageUrl: any, index: number) => (
                                                <div className='max-w-[240px]  aspect-square  w-[25%] pr-[12px] p-2 '>
                                                    <div key={index} className="w-full h-full flex min-h-[200px] rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden"
                                                        style={{ flexDirection: 'row' }}
                                                    >
                                                        <Checkbox
                                                            className="absolute top-4 left-4 z-10 "
                                                            style={{ transform: 'scale(1.25)' }}  // 放大1.5倍
                                                            checked={selectedImages.includes(imageUrl.taskImage)}
                                                            onChange={() => handleSelect(imageUrl.taskImage)}
                                                        />
                                                        {imageUrl.hasUploaded && <p className="absolute bottom-4  z-10"
                                                            style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                                        {getTaskImageComponent(item, index)}

                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )
                            })
                        }
                    </div>
                    <Pagination align="center" style={{ margin: '20px 0 120px' }} current={detailPage} pageSize={12} onChange={handleDetailPage} total={detailTotal} />
                    {showBatchActions && (
                        <div className="fixed w-[60%] bottom-16 left-[20%] right-0 bg-white border-t border-gray-200 p-4 shadow-md " style={{ zIndex: '100' }}>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <Checkbox
                                        checked={selectedImages.length == imageList.length * 4 || selectedImages.length == 48}/*imageList[0].image.length需要替换成图片总数  */
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                /* 小于48个图直接全选4*12=48  大于48个图只选前48个 */
                                                // if (imageList.length < 13) {
                                                //imageList.map((item,index)=>{})
                                                setSelectedImages(prevSelectedImages => {
                                                    let checkArray: any = [];
                                                    for (let i = 0; i < imageList.length; i++) {
                                                        imageList[i].image.map((item: any, index: number) => {
                                                            checkArray.push(item.taskImage);
                                                        })
                                                    }
                                                    return [...new Set([...prevSelectedImages, ...checkArray])]; // 去重后返回
                                                });
                                                // } else {
                                                //     setSelectedImages(prevSelectedImages => {
                                                //         let checkArray: string[] = [];
                                                //         for (let i = 0; i < imageList.length; i++) {
                                                //             imageList[i].image.map((item: any, index: number) => {
                                                //                 checkArray.push(item.taskImage);
                                                //             });
                                                //         }
                                                //         // 去重后返回
                                                //         let urlArray: any = [...new Set([...prevSelectedImages, ...checkArray])];
                                                //         // 只取前48个
                                                //         return [...urlArray.slice(0, 48)];
                                                //     });
                                                // }
                                            } else {
                                                setSelectedImages([]);
                                            }
                                        }}
                                    >
                                        全选
                                    </Checkbox>
                                    {/* {selectedImages.length > 0 && ( */}
                                    <span className="text-gray-600">已选择：{selectedImages.length}项</span>
                                    {/* )} */}
                                </div>
                                <div className="flex space-x-2">
                                    <Dropdown
                                        menu={{
                                            items: synchronizeItems,
                                            onClick: ({ key }) => {
                                                setSynchronizeType(Number(key));
                                                showTagModal();
                                            }
                                        }}
                                        placement="top"
                                    >
                                        <Button type="primary" disabled={selectedImages.length === 0}>
                                            同步图片
                                        </Button>
                                    </Dropdown>
                                    <Button type="primary"
                                        disabled={selectedImages.length === 0}
                                        onClick={handleDownloadImgages}
                                        loading={downloadLoading}
                                    >
                                        下载
                                    </Button>
                                    <Dropdown
                                        menu={{
                                            items,
                                            onClick: ({ key }) => selectTaskType(key)
                                            // {
                                            //     const action = items.find(item => item.key === key)?.label;
                                            //     Modal.info({
                                            //         title: `执行${action}`,
                                            //         content: `将对选中的${selectedImages.length}张图片执行${action}操作`,
                                            //     });
                                            // }
                                        }}
                                        placement="top"
                                    >
                                        <Button type="primary" disabled={selectedImages.length === 0}>
                                            图片操作
                                        </Button>
                                    </Dropdown>
                                    <Button type="primary"
                                        onClick={cancelSelection}
                                    >
                                        取消选择
                                    </Button>
                                </div>


                            </div>
                        </div>
                    )}
                </div>
            </>)}
        </div >
    );
};

export default SimilarDetail;





