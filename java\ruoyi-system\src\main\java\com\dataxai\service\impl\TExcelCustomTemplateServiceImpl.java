package com.dataxai.service.impl;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.*;
import com.dataxai.mapper.TExcelCustomValueMapper;
import com.dataxai.mapper.TExcelTemplateTitleMapsMapper;
import com.dataxai.mapper.TUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.AreaReference;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.mapper.TExcelCustomTemplateMapper;
import com.dataxai.service.ITExcelCustomTemplateService;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

/**
 * 半托数据商品模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
@Slf4j
public class TExcelCustomTemplateServiceImpl implements ITExcelCustomTemplateService {
    @Autowired
    private TExcelCustomTemplateMapper tExcelCustomTemplateMapper;
    @Autowired
    private TExcelTemplateTitleMapsMapper tExcelTemplateTitleMapsMapper;
    @Autowired
    private TExcelCustomValueMapper tExcelCustomValueMapper;
    @Autowired
    private TUserMapper tUserMapper;

    /**
     * 查询半托数据商品模板
     *
     * @param id 半托数据商品模板主键
     * @return 半托数据商品模板
     */
    @Override
    public TExcelCustomTemplate selectTExcelCustomTemplateById(Integer id) {
        return tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(id);
    }

    /**
     * 查询自定义模板详情，按 overall/base/spu/sku 四类组装字段定义与取值。
     *
     * @param id 模板主键ID
     * @return 详情Map：
     * - overallList/baseList/spuList/skuList: 字段定义列表
     * - baseValueList/spuValueList/skuValueList: 字段取值列表（sku 为二维，按 index 分组）
     * - id/templatename: 模板基本信息
     */
    public Map<String, Object> selectTExcelCustomTemplateDetails(Integer id) {
        long startMs = System.currentTimeMillis();
        log.info("开始查询自定义模板详情, id={}", id);
        TExcelCustomTemplate tExcelCustomTemplate = tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(id);

        String tExcelCustomTemplateId = tExcelCustomTemplate.getTExcelCustomTemplateId();
        List<TExcelCustomTemplateVO> tExcelCustomTemplateVOList = tExcelCustomValueMapper.selectTExcelCustomValueListSign(tExcelCustomTemplateId);
        Map<String, Object> result = new HashMap<>();
        if (tExcelCustomTemplate.getTemplateType() == 1) {
            try {
                // 定义各类字段定义集合
                List<TTemplateInfromationDataVO> tTemplateInfromationDataoverallList = new ArrayList<>();
                List<TTemplateInfromationDataVO> tTemplateInfromationDataoveBaseList = new ArrayList<>();
                List<TTemplateInfromationDataVO> tTemplateInfromationDataoveSPUList = new ArrayList<>();
                List<TTemplateInfromationDataVO> tTemplateInfromationDataSKUList = new ArrayList<>();
                List<TTemplateInfromationDataVO> tTemplateInfromationDataoveBaseValueList = new ArrayList<>();
                List<TTemplateInfromationDataVO> tTemplateInfromationDataoveSPUValueList = new ArrayList<>();
//        List<TTemplateInfromationDataVO> tTemplateInfromationDataoveSKUValueList = new ArrayList<>();
                List<List<TTemplateInfromationDataVO>> tTemplateInfromationDataoveSKUValueLists = new ArrayList<>();
                List<TExcelCustomTemplateVO> overallList = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "overall".equals(vo.getSign()))
                        .collect(Collectors.toList());
                for (TExcelCustomTemplateVO vo : overallList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setValue(vo.getColumnValue());
                    tTemplateInformationVO.setType(vo.getColumnType());
                    tTemplateInformationVO.setRemark(vo.getRemark());
                    String options = vo.getOptions();
                    if (options != null && !options.equals("")) {
                        if (options.startsWith("[") && options.endsWith("]")) {
                            options = options.substring(1, options.length() - 1);
                        }
                    }
                    sanitizeOptionsCSV(options);
                    List<String> optionList = options == null
                            ? Collections.emptyList()
                            : Arrays.stream(options.split(","))
                            .map(String::trim)
                            .filter(str -> !str.isEmpty())
                            .collect(Collectors.toList());
                    tTemplateInformationVO.setOptions(options);
                    tTemplateInformationVO.setRequired(Boolean.valueOf(vo.getRequired()));
                    tTemplateInfromationDataoverallList.add(tTemplateInformationVO);
                }

                // 处理 base 字段定义
                List<TExcelCustomTemplateVO> baseList = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "base".equals(vo.getSign()))
                        .collect(Collectors.toList());
                for (TExcelCustomTemplateVO vo : baseList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setHead(vo.getMainCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setType(vo.getColumnType());
                    tTemplateInformationVO.setRemark(vo.getRemark());
                    String options = vo.getOptions();
                    if (options != null && !options.equals("")) {
                        if (options.startsWith("[") && options.endsWith("]")) {
                            options = options.substring(1, options.length() - 1);
                        }
                    }
                    // 去除空格
                    String cleaneds = options.replaceAll("\\s+", "");
                    tTemplateInformationVO.setOptions(cleaneds);
                    tTemplateInformationVO.setRequired(Boolean.valueOf(vo.getRequired()));
                    tTemplateInfromationDataoveBaseList.add(tTemplateInformationVO);
                }

                // 处理 spu 字段定义
                List<TExcelCustomTemplateVO> spuList = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "spu".equals(vo.getSign()))
                        .collect(Collectors.toList());
                for (TExcelCustomTemplateVO vo : spuList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setHead(vo.getMainCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setType(vo.getColumnType());
                    tTemplateInformationVO.setRemark(vo.getRemark());
                    String options = vo.getOptions();
                    if (options != null && !options.equals("")) {
                        if (options.startsWith("[") && options.endsWith("]")) {
                            options = options.substring(1, options.length() - 1);
                        }
                    }
                    // 处理 options空格
                    String cleaneds = options.replaceAll("\\s+", "");
                    tTemplateInformationVO.setOptions(cleaneds);
                    tTemplateInformationVO.setRequired(Boolean.valueOf(vo.getRequired()));
                    tTemplateInfromationDataoveSPUList.add(tTemplateInformationVO);
                }

                // 处理 sku 字段定义
                List<TExcelCustomTemplateVO> tExcelCustomTemplateVOSKUList = tExcelCustomValueMapper.selectTExcelCustomValueListSignSKU(tExcelCustomTemplateId);
                for (TExcelCustomTemplateVO vo : tExcelCustomTemplateVOSKUList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setHead(vo.getMainCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setValue(vo.getColumnValue());
                    tTemplateInformationVO.setType(vo.getColumnType());
                    tTemplateInformationVO.setRemark(vo.getRemark());
                    String options = vo.getOptions();
                    String cleaneds = "";
                    if (options != null && !options.equals("")) {
                        if (options.startsWith("[") && options.endsWith("]")) {
                            options = options.substring(1, options.length() - 1);
                            cleaneds   = options.replaceAll("\\s+", "");
                        }
                    }

                    tTemplateInformationVO.setOptions(cleaneds);
                    tTemplateInformationVO.setRequired(Boolean.valueOf(vo.getRequired()));
                    tTemplateInfromationDataSKUList.add(tTemplateInformationVO);
                }

                // 组装 base 取值
                List<TExcelCustomTemplateVO> baseValueList = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "base".equals(vo.getSign()))
                        .collect(Collectors.toList());
                for (TExcelCustomTemplateVO vo : baseValueList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setId(vo.getId());
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setHead(vo.getMainCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setType(vo.getType());
                    tTemplateInformationVO.setValue(vo.getColumnValue());
                    tTemplateInformationVO.setOptions(vo.getOptions());
                    tTemplateInfromationDataoveBaseValueList.add(tTemplateInformationVO);
                }

                // 组装 spu 取值
                List<TExcelCustomTemplateVO> spuValueList = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "spu".equals(vo.getSign()))
                        .collect(Collectors.toList());
                for (TExcelCustomTemplateVO vo : spuValueList) {
                    TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                    tTemplateInformationVO.setId(vo.getId());
                    tTemplateInformationVO.setName(vo.getChildCategory());
                    tTemplateInformationVO.setHead(vo.getMainCategory());
                    tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                    tTemplateInformationVO.setType(vo.getType());
                    tTemplateInformationVO.setValue(vo.getColumnValue());
                    tTemplateInformationVO.setOptions(vo.getOptions());
                    tTemplateInfromationDataoveSPUValueList.add(tTemplateInformationVO);
                }
// 1. 定义转换方法（避免重复代码）
                Function<TExcelCustomTemplateVO, TTemplateInfromationDataVO> convertToDataVO = vo -> {
                    TTemplateInfromationDataVO dataVO = new TTemplateInfromationDataVO();
                    dataVO.setId(vo.getId());
                    dataVO.setName(vo.getChildCategory());
                    dataVO.setHead(vo.getMainCategory());
                    dataVO.setColumn(vo.getTTemplateTitleId());
                    dataVO.setType(vo.getType());
                    dataVO.setValue(vo.getColumnValue());
                    dataVO.setIndex(vo.getIndex());
                    dataVO.setOptions(vo.getOptions());
                    return dataVO;
                };

                // 1. 获取所有 index 并排序（确保顺序）
                List<Long> sortedIndices = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "sku".equals(vo.getSign()))
                        .map(TExcelCustomTemplateVO::getIndex)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                // 2. 按 index 分组 + 转换 + **内部排序**
                Map<Long, List<TTemplateInfromationDataVO>> groupedByIndex = tExcelCustomTemplateVOList.stream()
                        .filter(vo -> "sku".equals(vo.getSign()))
                        .collect(Collectors.groupingBy(
                                TExcelCustomTemplateVO::getIndex,
                                Collectors.collectingAndThen(
                                        Collectors.mapping(convertToDataVO, Collectors.toList()),
                                        list -> list.stream()
                                                .sorted(Comparator.comparing(TTemplateInfromationDataVO::getColumn))
                                                .collect(Collectors.toList())
                                )
                        ));
                // 3. 按 sortedIndices 的顺序提取数据
                List<List<TTemplateInfromationDataVO>> result1 = new ArrayList<>();
                for (Long index : sortedIndices) {
                    result1.add(groupedByIndex.getOrDefault(index, Collections.emptyList()));
                }
                tTemplateInfromationDataSKUList.sort(Comparator.comparing(TTemplateInfromationDataVO::getColumn));
                // 组装返回结果

                result.put("overallList", tTemplateInfromationDataoverallList);
                result.put("baseList", tTemplateInfromationDataoveBaseList);
                result.put("spuList", tTemplateInfromationDataoveSPUList);
                result.put("skuList", tTemplateInfromationDataSKUList);
                result.put("baseValueList", tTemplateInfromationDataoveBaseValueList);
                result.put("spuValueList", tTemplateInfromationDataoveSPUValueList);
                result.put("skuValueList", result1);
                if (log.isDebugEnabled()) {
                    log.debug("模板详情统计: overall={}, base={}, spu={}, sku={}, baseValue={}, spuValue={}, skuValue组数={}",
                            tTemplateInfromationDataoverallList.size(),
                            tTemplateInfromationDataoveBaseList.size(),
                            tTemplateInfromationDataoveSPUList.size(),
                            tTemplateInfromationDataSKUList.size(),
                            tTemplateInfromationDataoveBaseValueList.size(),
                            tTemplateInfromationDataoveSPUValueList.size(),
                            tTemplateInfromationDataoveSKUValueLists.size());
                }
                log.info("查询自定义模板详情完成, id={}, 模板名={}, 耗时={}ms", id, tExcelCustomTemplate.getTemplateName(), System.currentTimeMillis() - startMs);


            } catch (Exception e) {
                log.error("查询自定义模板详情失败, id={}, 错误={}", id, e.getMessage(), e);
                throw e;
            }
        } else {
            List<TTemplateInfromationDataVO> tTemplateInfromationDataSKUList = new ArrayList<>();
            List<TExcelCustomTemplateVO> tExcelCustomTemplateVOSKUList = tExcelCustomValueMapper.selectTExcelCustomValueListSignSKU(tExcelCustomTemplateId);
            for (TExcelCustomTemplateVO vo : tExcelCustomTemplateVOSKUList) {
                TTemplateInfromationDataVO tTemplateInformationVO = new TTemplateInfromationDataVO();
                tTemplateInformationVO.setName(vo.getChildCategory());
                tTemplateInformationVO.setHead(vo.getMainCategory());
                tTemplateInformationVO.setColumn(vo.getTTemplateTitleId());
                tTemplateInformationVO.setValue(vo.getColumnValue());
                tTemplateInformationVO.setType(vo.getType());
                tTemplateInformationVO.setRemark(vo.getRemark());
                String options = vo.getOptions();
                tTemplateInformationVO.setOptions(options);
                tTemplateInformationVO.setRequired(Boolean.valueOf(vo.getRequired()));
                tTemplateInfromationDataSKUList.add(tTemplateInformationVO);
            }
            // 1. 定义转换方法（避免重复代码）
            Function<TExcelCustomTemplateVO, TTemplateInfromationDataVO> convertToDataVO = vo -> {
                TTemplateInfromationDataVO dataVO = new TTemplateInfromationDataVO();
                dataVO.setId(vo.getId());
                dataVO.setName(vo.getChildCategory());
                dataVO.setHead(vo.getMainCategory());
                dataVO.setColumn(vo.getTTemplateTitleId());
                dataVO.setType(vo.getType());
                dataVO.setValue(vo.getColumnValue());
                dataVO.setIndex(vo.getIndex());
                return dataVO;
            };

            // 1. 获取所有 index 并排序（确保顺序）
            List<Long> sortedIndices = tExcelCustomTemplateVOList.stream()
                    .filter(vo -> "sku".equals(vo.getSign()))
                    .map(TExcelCustomTemplateVO::getIndex)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            // 2. 按 index 分组 + 转换 + **内部排序**
            Map<Long, List<TTemplateInfromationDataVO>> groupedByIndex = tExcelCustomTemplateVOList.stream()
                    .filter(vo -> "sku".equals(vo.getSign()))
                    .collect(Collectors.groupingBy(
                            TExcelCustomTemplateVO::getIndex,
                            Collectors.collectingAndThen(
                                    Collectors.mapping(convertToDataVO, Collectors.toList()),
                                    list -> list.stream()
                                            .sorted(Comparator.comparing(TTemplateInfromationDataVO::getColumn))
                                            .collect(Collectors.toList())
                            )
                    ));
            // 3. 按 sortedIndices 的顺序提取数据
            List<List<TTemplateInfromationDataVO>> result1 = new ArrayList<>();
            for (Long index : sortedIndices) {
                result1.add(groupedByIndex.getOrDefault(index, Collections.emptyList()));
            }
            tTemplateInfromationDataSKUList.sort(Comparator.comparing(TTemplateInfromationDataVO::getColumn));
            result.put("skuList", tTemplateInfromationDataSKUList);
            result.put("skuValueList", result1);
        }
        result.put("id", tExcelCustomTemplate.getId());
        result.put("templatename", tExcelCustomTemplate.getTemplateName());
        result.put("templateType", tExcelCustomTemplate.getTemplateType());
        return result;
    }

    /**
     * 查询半托数据商品模板列表
     *
     * @param tExcelCustomTemplate 半托数据商品模板
     * @return 半托数据商品模板
     */
    @Override
    public List<TExcelCustomTemplate> selectTExcelCustomTemplateList(TExcelCustomTemplate tExcelCustomTemplate) {
        return tExcelCustomTemplateMapper.selectTExcelCustomTemplateList(tExcelCustomTemplate);
    }

    /**
     * 新增半托数据商品模板
     *
     * @param tExcelCustomTemplate 半托数据商品模板
     * @return 结果
     */
    @Override
    public int insertTExcelCustomTemplate(TExcelCustomTemplate tExcelCustomTemplate) {
        tExcelCustomTemplate.setCreateTime(DateUtils.getNowDate());
        tExcelCustomTemplate.setUpdateTime(DateUtils.getNowDate());
        return tExcelCustomTemplateMapper.insertTExcelCustomTemplate(tExcelCustomTemplate);
    }

    /**
     * 修改半托数据商品模板
     *
     * @param tExcelCustomTemplate 半托数据商品模板
     * @return 结果
     */
    @Override
    public int updateTExcelCustomTemplate(TExcelCustomTemplate tExcelCustomTemplate) {
        Long currentUserId = SecurityUtils.getUserId();
        TUser user = tUserMapper.selectTUserById(currentUserId);
        tExcelCustomTemplate.setUserId(currentUserId);
        tExcelCustomTemplate.setTeamId(user.getTeamId());
        tExcelCustomTemplate.setUpdateTime(DateUtils.getNowDate());
        return tExcelCustomTemplateMapper.updateTExcelCustomTemplate(tExcelCustomTemplate);
    }

    /**
     * 批量删除半托数据商品模板
     *
     * @param ids 需要删除的半托数据商品模板主键
     * @return 结果
     */
    @Override
    public int deleteTExcelCustomTemplateByIds(Integer[] ids) {
        for (Integer id : ids) {

            TExcelCustomTemplate tExcelCustomTemplate = tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(id);
            String tExcelCustomTemplateId = tExcelCustomTemplate.getTExcelCustomTemplateId();
            TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
            tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
            List<TExcelCustomValue> tExcelCustomValues = tExcelCustomValueMapper.selectTExcelCustomValueList(tExcelCustomValue);
            if (tExcelCustomValues.size() > 0) {
                tExcelCustomValueMapper.deleteTExcelCustomValueTempatle(tExcelCustomTemplateId);
            }
        }
        return tExcelCustomTemplateMapper.deleteTExcelCustomTemplateByIds(ids);
    }

    /**
     * 删除半托数据商品模板信息
     *
     * @param id 半托数据商品模板主键
     * @return 结果
     */
    @Override
    public int deleteTExcelCustomTemplateById(Integer id) {
        TExcelCustomTemplate tExcelCustomTemplate = tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(id);
        String tExcelCustomTemplateId = tExcelCustomTemplate.getTExcelCustomTemplateId();
        TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
        tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
        List<TExcelCustomValue> tExcelCustomValues = tExcelCustomValueMapper.selectTExcelCustomValueList(tExcelCustomValue);
        if (tExcelCustomValues.size() > 0) {
            tExcelCustomValueMapper.deleteTExcelCustomValueTempatle(tExcelCustomTemplateId);
        }

        return tExcelCustomTemplateMapper.deleteTExcelCustomTemplateById(id);
    }

    /**
     * 解析上传的模板 Excel 并返回结构化字段定义
     * <p>
     * 采用策略模式（Strategy）：根据 templateType 选择不同的解析策略，避免大量 if/else 分支，便于扩展新模板类型。
     * - type=1：解析“模版”工作表，产出 overall/base/spu/sku 四类字段
     * - 其他：解析“Template”工作表，仅产出 sku 字段
     */
    @Override
    public Map<String, Object> selectTExcelCustomTemplateExcel(MultipartFile file, Long templateType) {
//        Long i = 1L;
//        templateType = i;
        TemplateExcelParser parser = resolveParser(templateType);
        return parser.parse(file, templateType);
    }

    /**
     * 策略选择器：根据模板类型返回对应解析策略
     */
    private TemplateExcelParser resolveParser(Long templateType) {
        if (templateType != null && templateType == 1L) {
            return this::parseType1Template;
        }
        return this::parseGenericTemplate;
    }

    /**
     * 策略接口：解析 Excel -> Map 结果
     */
    @FunctionalInterface
    private interface TemplateExcelParser {
        Map<String, Object> parse(MultipartFile file, Long templateType);
    }

    /**
     * 解析 type=1 的“半托/本地模板”，读取“模版”工作表，产出 overall/base/spu/sku 四类字段
     */
    private Map<String, Object> parseType1Template(MultipartFile file, Long templateType) {
        TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
        List<Map<String, Object>> overallList = new ArrayList<>();
        List<TTemplateInfromationDataVO> baseList = new ArrayList<>();
        List<TTemplateInfromationDataVO> spuList = new ArrayList<>();
        List<TTemplateInfromationDataVO> skuList = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream(); Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet templateSheet = workbook.getSheet("模版");
            Row headerRow = templateSheet.getRow(0);
            // 读取前两行数据（表头+第一行数据），构建 overallList
            for (int rowNum = 0; rowNum <= 1; rowNum++) {
                Row row = templateSheet.getRow(rowNum);
                if (row == null || rowNum == 0) continue; // 跳过表头
                int lastColumnIndex = row.getLastCellNum();
                for (int columnIndex = 0; columnIndex < lastColumnIndex; columnIndex++) {
                    if (templateSheet.isColumnHidden(columnIndex)) continue; // 跳过隐藏列
                    Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    if (cell != null && cell.getCellType() != CellType.BLANK) {
                        Map<String, Object> fieldMap = new HashMap<>();
                        String headerName = "";
                        if (headerRow != null) {
                            Cell headerCell = headerRow.getCell(cell.getColumnIndex(), Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                            if (headerCell != null && headerCell.getCellType() != CellType.BLANK) {
                                headerName = headerCell.getStringCellValue();
                            }
                        }
                        fieldMap.put("name", headerName);
                        fieldMap.put("column", cell.getColumnIndex());
                        fieldMap.put("type", "text");
                        fieldMap.put("value", cell.getStringCellValue());
                        fieldMap.put("required", false);
                        overallList.add(fieldMap);
                    }
                }
            }
            Row headRow = templateSheet.getRow(2); // 第三行作为head
            Row baseRow = templateSheet.getRow(3);  // 第四行作为name
            Row remarkRow = templateSheet.getRow(4); // 第五行作为remark
            Row sixthRow = templateSheet.getRow(5);  // 第六行用于select类型判断
            if (baseRow != null && headRow != null && remarkRow != null) {
                int lastColumnIndex = baseRow.getLastCellNum();
                for (int col = 0; col < lastColumnIndex; col++) {
                    // 第四行 name
                    Cell baseCell = baseRow.getCell(col, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String name = baseCell != null && baseCell.getCellType() != CellType.BLANK ? baseCell.getStringCellValue() : "";
                    // 第三行 head（示例代码实际仅取第0列，如需逐列请自行完善）
                    Cell headCell = headRow.getCell(0, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String head = headCell != null && headCell.getCellType() != CellType.BLANK ? headCell.getStringCellValue() : "";
                    // 第六行 判断是否为下拉
                    Cell sixthCell = sixthRow.getCell(col, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String options = null;
                    if (sixthCell != null) {
                        String dropdownOptions = getCellDropdownOptions(templateSheet, sixthCell);
                        if (dropdownOptions != null) options = dropdownOptions;
                    }
                    // base 区
                    tExcelTemplateTitleMaps.setChildCategory(name);
                    tExcelTemplateTitleMaps.setSign("base");
                    tExcelTemplateTitleMaps.setTemplateType(templateType);
                    List<TExcelTemplateTitleMaps> baseMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
                    for (TExcelTemplateTitleMaps m : baseMaps) {
                        TTemplateInfromationDataVO vo = new TTemplateInfromationDataVO();
                        vo.setName(m.getChildCategory());
                        vo.setHead(m.getMainCategory());
                        vo.setColumn(m.gettTemplateTitleId());
                        vo.setType(m.getColumnType());
                        vo.setRequired(Boolean.valueOf(m.getRequired()));
                        vo.setRemark(m.getRemark());
                        String opts = m.getOptions();
                        if (opts.startsWith("[") && opts.endsWith("]")) {
                            opts = opts.substring(1, opts.length() - 1);
                        }
                        vo.setOptions(opts.replaceAll("\\s+", ""));
                        baseList.add(vo);
                    }
                    // spu 区
                    tExcelTemplateTitleMaps.setSign("spu");
                    List<TExcelTemplateTitleMaps> spuMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
                    for (TExcelTemplateTitleMaps m : spuMaps) {
                        TTemplateInfromationDataVO vo = new TTemplateInfromationDataVO();
                        vo.setName(m.getChildCategory());
                        vo.setHead(m.getMainCategory());
                        vo.setColumn(m.gettTemplateTitleId());
                        String clean = options != null ? options.replaceAll("\\s+", "") : "";
                        vo.setOptions(clean);
                        vo.setRemark(m.getRemark());
                        vo.setRequired("true".equals(m.getRequired()));
                        vo.setType(m.getColumnType());
                        spuList.add(vo);
                    }
                    // sku 区
                    tExcelTemplateTitleMaps.setSign("sku");
                    List<TExcelTemplateTitleMaps> skuMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
                    for (TExcelTemplateTitleMaps m : skuMaps) {
                        TTemplateInfromationDataVO vo = new TTemplateInfromationDataVO();
                        vo.setName(m.getChildCategory());
                        vo.setHead(m.getMainCategory());
                        vo.setColumn(m.gettTemplateTitleId());
                        String clean;
                        if ("尺码类型".equals(m.getChildCategory())) {
                            clean = m.getOptions().replaceAll("\\s+", "");
                        } else {
                            clean = options != null ? options.replaceAll("\\s+", "") : "";
                        }
                        vo.setOptions(clean);
                        vo.setRemark(m.getRemark());
                        vo.setRequired("true".equals(m.getRequired()));
                        vo.setType(m.getColumnType());
                        skuList.add(vo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Excel文件验证异常：{}", e.getMessage(), e);
            return AjaxResult.error("文件格式验证失败：不是有效的Excel文件");
        }
        // spu/sku 以 name 去重，并按 column 排序
        Set<String> spuSeen = new HashSet<>();
        List<TTemplateInfromationDataVO> distinctSpu = spuList.stream().filter(v -> spuSeen.add(v.getName())).collect(Collectors.toList());
        distinctSpu.sort(Comparator.comparing(TTemplateInfromationDataVO::getColumn));
        Set<String> skuSeen = new HashSet<>();
        List<TTemplateInfromationDataVO> distinctSku = skuList.stream().filter(v -> skuSeen.add(v.getName())).collect(Collectors.toList());
        distinctSku.sort(Comparator.comparing(TTemplateInfromationDataVO::getColumn));
        Map<String, Object> result = new HashMap<>();
        result.put("overallList", overallList);
        result.put("baseList", baseList);
        result.put("spuList", distinctSpu);
        result.put("skuList", distinctSku);
        return result;
    }

    /**
     * 解析其他类型模板（如 shein 等），读取“Template”工作表，仅产出 sku 字段
     */
    private Map<String, Object> parseGenericTemplate(MultipartFile file, Long templateType) {
        List<TTemplateInfromationDataVO> skuList = new ArrayList<>();
        TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
        tExcelTemplateTitleMaps.setTemplateType(templateType);
        try (InputStream inputStream = file.getInputStream(); Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet templateSheet = workbook.getSheet("Template");
            Row headRow = templateSheet.getRow(2); // 第三行作为name
            Row remarkRow = templateSheet.getRow(4); // 第五行作为remark
            Row sixthRow = templateSheet.getRow(6);  // 第六行用于select类型判断
            if (headRow != null && remarkRow != null) {
                int lastColumnIndex = headRow.getLastCellNum();
                for (int col = 0; col < lastColumnIndex; col++) {
                    Cell baseCell = headRow.getCell(col, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String name = baseCell != null && baseCell.getCellType() != CellType.BLANK ? baseCell.getStringCellValue() : "";
                    Cell headCell = headRow.getCell(0, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String head = headCell != null && headCell.getCellType() != CellType.BLANK ? headCell.getStringCellValue() : "";
                    Cell sixthCell = sixthRow.getCell(col, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    String options = null;
                    if (sixthCell != null) {
                        String dropdownOptions = getCellDropdownOptions(templateSheet, sixthCell);
                        if (dropdownOptions != null) options = dropdownOptions;
                    }
                    tExcelTemplateTitleMaps.setChildCategory(name);
                    tExcelTemplateTitleMaps.setSign("sku");
                    tExcelTemplateTitleMaps.setTemplateType(templateType);
                    List<TExcelTemplateTitleMaps> skuMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
                    for (TExcelTemplateTitleMaps m : skuMaps) {
                        TTemplateInfromationDataVO vo = new TTemplateInfromationDataVO();
                        vo.setName(m.getChildCategory());
                        vo.setColumn(m.gettTemplateTitleId());
                        String clean = options != null ? options.replaceAll("\\s+", "") : "";
                        vo.setOptions(clean);
                        vo.setRemark(m.getRemark());
                        vo.setRequired("true".equals(m.getRequired()));
                        vo.setType(m.getColumnType());
                        skuList.add(vo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Excel文件验证异常：{}", e.getMessage(), e);
            return AjaxResult.error("文件格式验证失败：不是有效的Excel文件");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("skuList", skuList);
        return result;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public int updatatExcelCustomTemplate(TTemplateInformationVO tTemplateInformationVO) {
        Integer templateId = tTemplateInformationVO.getId();
        TExcelCustomTemplate slecttExcelCustomTemplate = tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(templateId);
        String tExcelCustomTemplateId = slecttExcelCustomTemplate.getTExcelCustomTemplateId();
        List<TExcelCustomTemplateVO> tExcelCustomTemplateVOS = tExcelCustomValueMapper.selectTExcelCustomValueListSign(tExcelCustomTemplateId);
        List<TExcelCustomTemplateVO> skuValueList = tExcelCustomTemplateVOS.stream()
                .filter(vo -> "sku".equals(vo.getSign()))
                .collect(Collectors.toList());
        if (tTemplateInformationVO.getTemplateType() == 1){
            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getBaseValues()) {
                TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                tExcelCustomValue.setId(tTemplateInfromationDataVO.getId());
                tExcelCustomValue.setTExcelTemplateTitleMapsId(tTemplateInfromationDataVO.getColumn());
                tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                tExcelCustomValueMapper.updateTExcelCustomValue(tExcelCustomValue);
            }
            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSpuValues()) {
                TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                tExcelCustomValue.setId(tTemplateInfromationDataVO.getId());
                tExcelCustomValue.setTExcelTemplateTitleMapsId(tTemplateInfromationDataVO.getColumn());
                tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                tExcelCustomValueMapper.updateTExcelCustomValue(tExcelCustomValue);
            }
            for (List<TTemplateInfromationDataVO> tTemplateInfromationDataVOs : tTemplateInformationVO.getSkuValues()) {
                for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInfromationDataVOs) {
                    TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                    if (tTemplateInfromationDataVO.getId() == null || tTemplateInfromationDataVO.getId().equals(0)) {
                        Long column = tTemplateInfromationDataVO.getColumn();
                        TExcelTemplateTitleMaps tExcelTemplateTitleMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsByTitleId(column);
                        tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(tExcelTemplateTitleMaps.getId()));
                        tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                        tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                        tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                        tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                        tExcelCustomValueMapper.insertTExcelCustomValue(tExcelCustomValue);
                    } else {
                        tExcelCustomValue.setId(tTemplateInfromationDataVO.getId());
//                    tExcelCustomValue.setTExcelTemplateTitleMapsId(tTemplateInfromationDataVO.getColumn());
//                    tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                        tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                        tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                        tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                        tExcelCustomValueMapper.updateTExcelCustomValue(tExcelCustomValue);
                    }
                }
            }
        }else {
            for (List<TTemplateInfromationDataVO> tTemplateInfromationDataVOs : tTemplateInformationVO.getSkuValues()) {
                for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInfromationDataVOs) {
                    TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                    if (tTemplateInfromationDataVO.getId() == null || tTemplateInfromationDataVO.getId().equals(0)) {
                        Long column = tTemplateInfromationDataVO.getColumn();
                        TExcelTemplateTitleMaps tExcelTemplateTitleMaps = tExcelTemplateTitleMapsMapper.selectTExcelTemplateTitleMapsByTitleId(column);
                        tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(tExcelTemplateTitleMaps.getId()));
                        tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                        tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                        tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                        tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                        tExcelCustomValueMapper.insertTExcelCustomValue(tExcelCustomValue);
                    } else {
                        tExcelCustomValue.setId(tTemplateInfromationDataVO.getId());
//                    tExcelCustomValue.setTExcelTemplateTitleMapsId(tTemplateInfromationDataVO.getColumn());
//                    tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplateId);
                        tExcelCustomValue.setColumnType(tTemplateInfromationDataVO.getType());
                        tExcelCustomValue.setColumnValue(tTemplateInfromationDataVO.getValue());
                        tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                        tExcelCustomValueMapper.updateTExcelCustomValue(tExcelCustomValue);
                    }
                }
            }
        }
        // 统一在所有 SKU 值处理完成后，删除前置数据中已不存在的记录，避免在循环中误删其它行
        Set<Long> incomingExistingIds = tTemplateInformationVO.getSkuValues().stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(TTemplateInfromationDataVO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Long[] idsToDelete = skuValueList.stream()
                .map(TExcelCustomTemplateVO::getId)
                .filter(Objects::nonNull)
                .filter(id -> !incomingExistingIds.contains(id))
                .toArray(Long[]::new);
        if (idsToDelete.length > 0) {
            tExcelCustomValueMapper.deleteTExcelCustomValueByIds(idsToDelete);
        }
        TExcelCustomTemplate tExcelCustomTemplate = new TExcelCustomTemplate();
        tExcelCustomTemplate.setId(tTemplateInformationVO.getId());
        tExcelCustomTemplate.setTemplateName(tTemplateInformationVO.getName());
        tExcelCustomTemplate.setTExcelCustomTemplateId(tTemplateInformationVO.getTExcelCustomTemplateId());
        tExcelCustomTemplate.setTemplateType(tTemplateInformationVO.getTemplateType());
        tExcelCustomTemplate.settExcelCustomGroupId(tTemplateInformationVO.getTExcelCustomGroupId());
        tExcelCustomTemplate.setExcelUrl(tTemplateInformationVO.getFile());
        Long currentUserId = SecurityUtils.getUserId();
        TUser user = tUserMapper.selectTUserById(currentUserId);
        tExcelCustomTemplate.setUserId(currentUserId);
        tExcelCustomTemplate.setTeamId(user.getTeamId());
        tExcelCustomTemplate.setUpdateTime(DateUtils.getNowDate());
        return tExcelCustomTemplateMapper.updateTExcelCustomTemplate(tExcelCustomTemplate);
    }

    @Override
    public AjaxResult exportexcel(TExcelCustomTemplate tExcelCustomTemplate) {
        return null;
    }

    @Override
    public int tExcelCustomTemplateCopeExcelCustomTemplate(Integer id) {

        Long currentUserId = SecurityUtils.getUserId();
        TUser user = tUserMapper.selectTUserById(currentUserId);
        TExcelCustomTemplate tExcelCustomTemplateCope = new TExcelCustomTemplate();
        TExcelCustomTemplate tExcelCustomTemplate = tExcelCustomTemplateMapper.selectTExcelCustomTemplateById(id);
        String templateName = tExcelCustomTemplate.getTemplateName();
        String name = templateName + "_cope";
        String templateId = "t_" + generate();
        tExcelCustomTemplateCope.setTemplateName(name);
        tExcelCustomTemplateCope.setTExcelCustomTemplateId(templateId);
        tExcelCustomTemplateCope.setTemplateType(tExcelCustomTemplate.getTemplateType());
        tExcelCustomTemplateCope.setCreateTime(DateUtils.getNowDate());
        tExcelCustomTemplateCope.setFile(tExcelCustomTemplate.getFile());
        tExcelCustomTemplateCope.setTeamId(user.getTeamId());
        tExcelCustomTemplateCope.setUserId(currentUserId);
        tExcelCustomTemplateCope.settExcelCustomGroupId(tExcelCustomTemplate.gettExcelCustomGroupId());
        TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
        tExcelCustomValue.setTExcelCustomTemplateId(tExcelCustomTemplate.getTExcelCustomTemplateId());
        List<TExcelCustomValue> tExcelCustomValuesList = tExcelCustomValueMapper.selectTExcelCustomValueList(tExcelCustomValue);
        for (TExcelCustomValue tExcelCustomValues : tExcelCustomValuesList) {
            TExcelCustomValue tExcelCustomValue1 = new TExcelCustomValue();
            tExcelCustomValue1.setTExcelCustomTemplateId(templateId);
            tExcelCustomValue1.setColumnType(tExcelCustomValues.getColumnType());
            tExcelCustomValue1.setTExcelTemplateTitleMapsId(tExcelCustomValues.getTExcelTemplateTitleMapsId());
            tExcelCustomValue1.setColumnValue(tExcelCustomValues.getColumnValue());
            tExcelCustomValue1.setNoSplit(tExcelCustomValues.getNoSplit());
            tExcelCustomValue1.setIndex(tExcelCustomValues.getIndex());
            tExcelCustomValue1.setOptions(tExcelCustomValues.getOptions());
            tExcelCustomValueMapper.insertTExcelCustomValue(tExcelCustomValue1);
        }
        return tExcelCustomTemplateMapper.insertTExcelCustomTemplate(tExcelCustomTemplateCope);
    }

    @Override
    public Map<String, Object> excelAnalysis(MultipartFile file) {
        List<Map<String, Object>> overallList = new ArrayList<>();
        List<Map<String, Object>> baseList = new ArrayList<>();
        List<Map<String, Object>> spuList = new ArrayList<>();
        List<Map<String, Object>> skuList = new ArrayList<>();
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !(originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx"))) {
            return AjaxResult.error("只能上传Excel文件（.xls或.xlsx格式）");
        }
        try (InputStream inputStream = file.getInputStream()) {
            File tempFile = File.createTempFile("excel-", ".tmp");
            try (Workbook workbook = WorkbookFactory.create(inputStream)) {
                // 验证是否为temu半托表格
                Sheet sheet = workbook.getSheetAt(1);
                Sheet templateSheet = workbook.getSheet("模版");
                if (templateSheet == null) {
                    throw new IllegalArgumentException("模版Sheet不存在");
                }
                Row headerRow = templateSheet.getRow(0);
                if (headerRow == null) {
                    return AjaxResult.error("表格标题行不存在");
                }
                // 验证关键列是否存在
                Cell cell0 = headerRow.getCell(1, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                Cell cell1 = headerRow.getCell(3, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                Cell cell2 = headerRow.getCell(4, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);

                if (cell0 == null || !"发货仓".equals(cell0.getStringCellValue()) ||
                        cell1 == null || !"运费模版".equals(cell1.getStringCellValue()) ||
                        cell2 == null || !"承诺发货时效".equals(cell2.getStringCellValue())) {
                    return AjaxResult.error("表格列格式不符合temu半托要求");
                }
                // 读取前两行数据（表头+第一行数据）
                for (int rowNum = 0; rowNum <= 1; rowNum++) {
                    Row row = templateSheet.getRow(rowNum);
                    if (row == null) continue;

                    // 跳过表头行的处理逻辑，只处理数据行
                    if (rowNum == 0) continue;

                    // 使用RETURN_NULL_AND_BLANK策略获取所有单元格
                    int lastColumnIndex = row.getLastCellNum();
                    for (int columnIndex = 0; columnIndex < lastColumnIndex; columnIndex++) {
                        // 跳过隐藏列
                        if (templateSheet.isColumnHidden(columnIndex)) {
                            continue;
                        }

                        Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        if (cell != null && cell.getCellType() != CellType.BLANK) {
                            Map<String, Object> fieldMap = new HashMap<>();

                            // 获取对应的表头名称
                            String headerName = "";
                            if (headerRow != null) {
                                Cell headerCell = headerRow.getCell(cell.getColumnIndex(), Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                                if (headerCell != null && headerCell.getCellType() != CellType.BLANK) {
                                    headerName = headerCell.getStringCellValue();
                                }
                            }

                            fieldMap.put("name", headerName);
                            fieldMap.put("column", cell.getColumnIndex());
                            fieldMap.put("type", "text");
                            fieldMap.put("value", cell.getStringCellValue());
                            fieldMap.put("required", false);

                            overallList.add(fieldMap);
                        }
                    }
                }

                // 读取第三到第五行数据（基础信息）
                Row headRow = templateSheet.getRow(2); // 第三行作为head
                Row baseRow = templateSheet.getRow(3);  // 第四行作为name
                Row remarkRow = templateSheet.getRow(4); // 第五行作为remark
                Row sixthRow = templateSheet.getRow(5);  // 第六行用于select类型判断
                if (baseRow != null && headRow != null && remarkRow != null) {
                    int lastColumnIndex = baseRow.getLastCellNum();
                    for (int columnIndex = 0; columnIndex < lastColumnIndex; columnIndex++) {
                        // 跳过隐藏列
                        if (templateSheet.isColumnHidden(columnIndex)) {
                            continue;
                        }

                        // 获取name（第四行）
                        Cell baseCell = baseRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String name = "";
                        if (baseCell != null && baseCell.getCellType() != CellType.BLANK) {
                            name = baseCell.getStringCellValue();
                        }

                        // 获取head（第三行）
                        Cell headCell = headRow.getCell(0, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String head = "";
                        if (headCell != null && headCell.getCellType() != CellType.BLANK) {
                            head = headCell.getStringCellValue();
                        }

                        // 获取remark（第五行）
                        Cell remarkCell = remarkRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String remark = "";
                        if (remarkCell != null && remarkCell.getCellType() != CellType.BLANK) {
                            remark = remarkCell.getStringCellValue();
                        }
                        // 检查第六行是否是下拉框（select类型）
                        Cell sixthCell = sixthRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String options = null; // 存储下拉框选项
//                        String[] options = null; // 存储下拉框选项
                        if (sixthCell != null) {
                            // 检查当前列是否有下拉框（数据验证）
                            String dropdownOptions = getCellDropdownOptions(templateSheet, sixthCell);
                            if (dropdownOptions != null) {
//                                options = dropdownOptions.split(","); // 拆分成数组
                                options = dropdownOptions;
                            }
                        }
                        // 设置type和required
                        String type;
                        boolean required;
                        if (columnIndex <= 1) {
                            type = "spu";
                            required = true;
                        } else if (columnIndex >= 2 && columnIndex <= 4) {
                            type = "text";
                            required = true;
                        } else if (columnIndex == 5) {
                            type = "text";
                            required = false;
                        } else {
                            continue; // 跳过其他列
                        }
                        Map<String, Object> fieldMap = new HashMap<>();
                        fieldMap.put("name", name);
                        fieldMap.put("head", head);
                        fieldMap.put("column", columnIndex);
                        fieldMap.put("type", type);
                        fieldMap.put("required", required);
                        fieldMap.put("remark", remark);
                        fieldMap.put("option", options);
                        baseList.add(fieldMap);
                    }
                }

                if (baseRow != null && headRow != null && remarkRow != null) {
                    for (int columnIndex = 6; columnIndex < 64; columnIndex++) {
                        // 跳过隐藏列
                        if (templateSheet.isColumnHidden(columnIndex)) {
                            continue;
                        }

                        // 获取name（第四行）
                        Cell baseCell = baseRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String name = "";
                        if (baseCell != null && baseCell.getCellType() != CellType.BLANK) {
                            name = baseCell.getStringCellValue();
                        }

                        // 获取head（第三行）
                        Cell headCell = headRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String head = "";
                        if (headCell != null && headCell.getCellType() != CellType.BLANK) {
                            head = headCell.getStringCellValue();
                        }
                        // 获取remark（第五行）
                        Cell remarkCell = remarkRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String remark = "";
                        if (remarkCell != null && remarkCell.getCellType() != CellType.BLANK) {
                            remark = remarkCell.getStringCellValue();
                        }

                        // 判断当前列在headRow是否属于合并单元格，并计算合并宽度
                        int mergeSpan = getMergedSpanWidth(templateSheet, headRow.getRowNum(), columnIndex);
                        boolean merged = mergeSpan > 1;

                        // 检查第六行是否是下拉框（select类型）
                        Cell sixthCell = sixthRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String[] options = null; // 存储下拉框选项
                        if (sixthCell != null) {
                            // 检查当前列是否有下拉框（数据验证）
                            String dropdownOptions = getCellDropdownOptions(templateSheet, sixthCell);
                            if (dropdownOptions != null) {
                                options = dropdownOptions.split(","); // 拆分成数组
                            }
                        }

                        Map<String, Object> fieldMap = new HashMap<>();
                        fieldMap.put("name", name);
                        fieldMap.put("head", head);
                        fieldMap.put("column", columnIndex);
                        fieldMap.put("type", "");
                        fieldMap.put("required", "");
                        fieldMap.put("remark", remark);
                        fieldMap.put("option", options);
                        fieldMap.put("merged", merged);
                        if (merged) {
                            fieldMap.put("mergeSpan", mergeSpan);
                        }

                        spuList.add(fieldMap);
                    }
                }
                if (baseRow != null && headRow != null && remarkRow != null) {
                    int lastColumnIndex = baseRow.getLastCellNum();
                    for (int columnIndex = 64; columnIndex < lastColumnIndex; columnIndex++) {
                        // 跳过隐藏列
                        if (templateSheet.isColumnHidden(columnIndex)) {
                            continue;
                        }

                        // 获取name（第四行）
                        Cell baseCell = baseRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String name = "";
                        if (baseCell != null && baseCell.getCellType() != CellType.BLANK) {
                            name = baseCell.getStringCellValue();
                        }

                        // 获取head（第三行）
                        String head = "";
                        Cell headCell = headRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        if (headCell != null && headCell.getCellType() != CellType.BLANK) {
                            head = headCell.getStringCellValue();
                        }
                        // 获取remark（第五行）
                        Cell remarkCell = remarkRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String remark = "";
                        if (remarkCell != null && remarkCell.getCellType() != CellType.BLANK) {
                            remark = remarkCell.getStringCellValue();
                        }
                        // 检查第六行是否是下拉框（select类型）
                        Cell sixthCell = sixthRow.getCell(columnIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                        String[] options = null; // 存储下拉框选项
                        if (sixthCell != null) {
                            // 检查当前列是否有下拉框（数据验证）
                            String dropdownOptions = getCellDropdownOptions(templateSheet, sixthCell);
                            if (dropdownOptions != null) {
                                options = dropdownOptions.split(","); // 拆分成数组
                            }
                        }

                        Map<String, Object> fieldMap = new HashMap<>();
                        fieldMap.put("name", name);
                        fieldMap.put("head", head);
                        fieldMap.put("column", columnIndex);
                        fieldMap.put("type", "type");
                        fieldMap.put("required", "required");
                        fieldMap.put("remark", remark);
                        fieldMap.put("option", options);

                        skuList.add(fieldMap);
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("flag", true);
                result.put("overall", overallList);
                result.put("base", baseList);
                result.put("spu", spuList);
                result.put("sku", skuList);

                //删除临时文件
                Files.deleteIfExists(tempFile.toPath());
                return result;
            }
        } catch (Exception e) {
            log.error("Excel文件验证异常：{}", e.getMessage(), e);
            return AjaxResult.error("文件格式验证失败：不是有效的Excel文件");
        }
    }

    // 安全提取函数调用的外层参数（处理嵌套括号）
    private static String extractOuterArg(String funcCall) {
        int start = funcCall.indexOf('(');
        int end = funcCall.lastIndexOf(')');
        if (start < 0 || end < 0 || end <= start) return null;
        int depth = 0;
        for (int i = start; i <= end; i++) {
            char ch = funcCall.charAt(i);
            if (ch == '(') depth++;
            else if (ch == ')') {
                depth--;
                if (depth == 0) {
                    return funcCall.substring(start + 1, i).trim();
                }
            }
        }
        return funcCall.substring(start + 1, end).trim();
    }

    private static String stripOuterQuotes(String s) {
        if (s == null) return null;
        String t = s.trim();
        if ((t.startsWith("\"") && t.endsWith("\"")) || (t.startsWith("'") && t.endsWith("'"))) {
            return t.substring(1, t.length() - 1);
        }
        return t;
    }

    private static String evaluateFormulaToString(Workbook workbook, String expr) {
        try {
            String sheetName = "__tmp_eval__";
            Sheet tmp = workbook.getSheet(sheetName);
            boolean created = false;
            if (tmp == null) {
                tmp = workbook.createSheet(sheetName);
                created = true;
            }
            Row r = tmp.getRow(0);
            if (r == null) r = tmp.createRow(0);
            Cell c = r.getCell(0);
            if (c == null) c = r.createCell(0);
            String formula = expr.startsWith("=") ? expr.substring(1) : expr;
            // 注意：setCellFormula 接受的字符串不应带 '=' 前缀
            c.setCellFormula(formula);
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            CellValue cv = evaluator.evaluate(c);
            String result = null;
            if (cv != null) {
                switch (cv.getCellType()) {
                    case STRING:
                        result = cv.getStringValue();
                        break;
                    case NUMERIC:
                        result = String.valueOf(cv.getNumberValue());
                        break;
                    case BOOLEAN:
                        result = String.valueOf(cv.getBooleanValue());
                        break;
                    default:
                        result = null;
                }
            }

            // 清理
            r.removeCell(c);
            if (created) {
                int idx = workbook.getSheetIndex(sheetName);
                if (idx >= 0) workbook.removeSheetAt(idx);
            }
            return result != null ? result.trim() : null;
        } catch (Exception e) {
            return null;
        }
    }

    // 使用给定 Sheet 作为公式的上下文来求值（让诸如 $E$6 的相对引用在该 Sheet 上解析）
    private static String evaluateFormulaToString(Sheet contextSheet, String expr) {
        try {
            if (contextSheet == null || expr == null || expr.trim().isEmpty()) return null;
            Workbook wb = contextSheet.getWorkbook();
            int tmpRowIndex = Math.max(contextSheet.getLastRowNum() + 1, 0);
            Row r = contextSheet.getRow(tmpRowIndex);
            if (r == null) r = contextSheet.createRow(tmpRowIndex);
            Cell c = r.createCell(0);
            String formula = expr.startsWith("=") ? expr.substring(1) : expr;
            c.setCellFormula(formula);
            String result = null;
//            FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
//            CellValue cv = evaluator.evaluate(c);
            try {
                c.setCellFormula(formula);
                FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
                CellValue cv = evaluator.evaluate(c);
                if (cv != null) {
                    switch (cv.getCellType()) {
                        case STRING:
                            result = cv.getStringValue();
                            break;
                        case NUMERIC:
                            result = String.valueOf(cv.getNumberValue());
                            break;
                        case BOOLEAN:
                            result = String.valueOf(cv.getBooleanValue());
                            break;
                        default:
                            result = null;
                    }
                }
                // 清理
                r.removeCell(c);
                if (r.getLastCellNum() <= 0) {
                    contextSheet.removeRow(r);
                }
                return result != null ? result.trim() : null;
                // ... 正常返回结果
            } catch (Exception evalEx) {
                log.debug("[FormulaEval] evaluate failed, expr={}", expr, evalEx);
                // 兜底：手动解析 VLOOKUP（若 expr 为 VLOOKUP(...) 或 INDIRECT(VLOOKUP(...))）
                String target = expr;
                if (expr.startsWith("INDIRECT(") && expr.contains("VLOOKUP(")) {
                    int start = expr.indexOf("VLOOKUP(");
                    target = expr.substring(start, expr.lastIndexOf(')') + 1);
                }
                if (target.startsWith("VLOOKUP(")) {
                    String manual = tryManualVlookup(contextSheet, target);
                    if (manual != null) return manual.trim();
                }
                // 清理
                r.removeCell(c);
                if (r.getLastCellNum() <= 0) {
                    contextSheet.removeRow(r);
                }
                return target != null ? target.trim() : null;
            }

        } catch (IllegalStateException e) {
            throw new RuntimeException(e);
        }
    }

    private static String evaluateFormulaToString1(Sheet contextSheet, String expr) {
        try {
            if (contextSheet == null || expr == null || expr.trim().isEmpty()) return null;
            Workbook wb = contextSheet.getWorkbook();
            int tmpRowIndex = Math.max(contextSheet.getLastRowNum() + 1, 0);
            Row r = contextSheet.getRow(tmpRowIndex);
            if (r == null) r = contextSheet.createRow(tmpRowIndex);
            Cell c = r.createCell(0);
            String formula = expr.startsWith("=") ? expr.substring(1) : expr;
            c.setCellFormula(formula);
            FormulaEvaluator evaluator = wb.getCreationHelper().createFormulaEvaluator();
            CellValue cv = evaluator.evaluate(c);
            String result = null;
            if (cv != null) {
                switch (cv.getCellType()) {
                    case STRING:
                        result = cv.getStringValue();
                        break;
                    case NUMERIC:
                        result = String.valueOf(cv.getNumberValue());
                        break;
                    case BOOLEAN:
                        result = String.valueOf(cv.getBooleanValue());
                        break;
                    default:
                        result = null;
                }
            }
            // 清理临时单元格
            r.removeCell(c);
            if (r.getLastCellNum() <= 0) {
                contextSheet.removeRow(r);
            }
            return result != null ? result.trim() : null;
        } catch (Exception e) {
            return null;
        }
    }

    private static String resolveNamedOrAreaToOptions(Workbook workbook, Sheet defaultSheet, String text) {
        if (text == null) return "";
        String t = stripOuterQuotes(text);
        // 尝试直接按范围解析（含跨 Sheet）
        try {
            return getOptionsFromRangeString(workbook, defaultSheet, t);
        } catch (Exception ignore) {
        }
        return "";
    }

    private static String getOptionsFromReference(Sheet sheet, String formula) {
        Workbook workbook = sheet.getWorkbook();
        try {
            String f = formula == null ? "" : formula.trim();
            // 处理 INDIRECT( ... )
            if (f.startsWith("INDIRECT(")) {
                String arg = extractOuterArg(f);
                if (arg == null) return "";
                // 1) 纯字符串 "Sheet1!A1:A10" / "NamedRange"
                if ((arg.startsWith("\"") && arg.endsWith("\"")) || (arg.startsWith("'") && arg.endsWith("'"))) {
                    String val = stripOuterQuotes(arg);
                    return resolveNamedOrAreaToOptions(workbook, sheet, val);
                }
                // 2) 形如 $BJ$6 / Sheet!$BJ$6 的单元格引用
                try {
                    CellReference ref = new CellReference(arg.replace("$", ""));
                    Sheet refSheet = ref.getSheetName() != null ? workbook.getSheet(ref.getSheetName()) : sheet;
                    if (refSheet != null) {
                        Row rr = refSheet.getRow(ref.getRow());
                        if (rr != null) {
                            Cell cc = rr.getCell(ref.getCol());
                            if (cc != null) {
                                if (cc.getCellType() == CellType.STRING) {
                                    return resolveNamedOrAreaToOptions(workbook, sheet, cc.getStringCellValue());
                                } else if (cc.getCellType() == CellType.FORMULA) {
                                    FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
                                    CellValue cv = evaluator.evaluate(cc);
                                    if (cv != null && cv.getCellType() == CellType.STRING) {
                                        return resolveNamedOrAreaToOptions(workbook, sheet, cv.getStringValue());
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception ignore) {
                    // 3) 复杂表达式（如 VLOOKUP/连接等），用求值器求值为字符串（使用当前 Sheet 作为上下文）
                    String evaluated = evaluateFormulaToString(sheet, arg.startsWith("=") ? arg.substring(1) : arg);
                    if (evaluated != null) {
                        return resolveNamedOrAreaToOptions(workbook, sheet, evaluated);
                    }
                }
                // 尝试最终一次通过求值（仍在当前 Sheet 上下文）
                String evaluated = evaluateFormulaToString(sheet, arg);
                if (evaluated != null) {
                    return resolveNamedOrAreaToOptions(workbook, sheet, evaluated);
                }
                // 手动 VLOOKUP 兜底（当 INDIRECT 参数是 VLOOKUP 表达式时）
                String vArg = arg.startsWith("=") ? arg.substring(1) : arg;
                if (vArg.startsWith("VLOOKUP(")) {
                    String manual = tryManualVlookup(sheet, vArg);
                    if (manual != null) {
                        return resolveNamedOrAreaToOptions(workbook, sheet, manual);
                    }
                }
            }
            // VLOOKUP(...) 直接求值，返回命名区域或范围字符串（使用当前 Sheet 作为上下文）
            else if (f.startsWith("VLOOKUP(")) {
                String evaluated = evaluateFormulaToString(sheet, f.startsWith("=") ? f.substring(1) : f);
                if (evaluated != null) {
                    return resolveNamedOrAreaToOptions(workbook, sheet, evaluated);
                }
            }
            // 直接范围/跨表
            else if (f.contains("!")) {
                return getOptionsFromRangeString(workbook, sheet, f);
            }
        } catch (Exception e) {
            System.err.println("解析引用公式失败: " + formula + " | 错误: " + e.getMessage());
        }
        return "";
    }

    /**
     * 从范围字符串（如 'Sheet2'!A1:A10 或 A1:A10）提取选项值
     */
    private static String getOptionsFromRangeString(Workbook workbook, Sheet defaultSheet, String rangeStr) {
        try {
            String rs = stripOuterQuotes(rangeStr);
            // 支持类似 Sheet!A:A 以及命名区域展开后的表达式
            AreaReference areaRef = new AreaReference(rs, workbook.getSpreadsheetVersion());
            String sheetName = areaRef.getFirstCell().getSheetName();
            Sheet refSheet = workbook.getSheet(sheetName);
            if (refSheet == null) {
                System.err.println("Sheet 不存在: " + sheetName);
                return "";
            }

            // 遍历范围，提取数据
            List<String> options = new ArrayList<>();
            for (CellReference cellRef : areaRef.getAllReferencedCells()) {
                Row row = refSheet.getRow(cellRef.getRow());
                if (row != null) {
                    Cell cell = row.getCell(cellRef.getCol());
                    if (cell != null && cell.getCellType() == CellType.STRING) {
                        String val = sanitizeOptionToken(cell.getStringCellValue());
                        if (val != null && !val.isEmpty()) options.add(val);
                    }
                }
            }
            return String.join(",", options);
        } catch (Exception e) {
            System.err.println("解析范围失败: " + rangeStr + " | 错误: " + e.getMessage());
            return "";
        }
    }

    /**
     * 从 AreaReference 提取选项值
     */
    private static String getOptionsFromCellRange(Sheet sheet, AreaReference areaRef) {
        List<String> options = new ArrayList<>();
        for (CellReference cellRef : areaRef.getAllReferencedCells()) {
            Row refRow = sheet.getRow(cellRef.getRow());
            if (refRow != null) {
                Cell refCell = refRow.getCell(cellRef.getCol());
                if (refCell != null && refCell.getCellType() == CellType.STRING) {
                    options.add(refCell.getStringCellValue());
                }
            }
        }
        return String.join(",", options);
    }

    /**
     * 检查单元格是否有下拉框，并返回选项值的逗号分隔字符串（若无则返回 null）
     */
    private static String getCellDropdownOptions(Sheet sheet, Cell cell) {
        if (!(sheet instanceof XSSFSheet)) {
            return null; // 仅支持 .xlsx 格式
        }
        XSSFSheet xssfSheet = (XSSFSheet) sheet;
        List<XSSFDataValidation> dataValidations = xssfSheet.getDataValidations();
        for (XSSFDataValidation validation : dataValidations) {
            CellRangeAddressList regions = validation.getRegions();
            for (CellRangeAddress address : regions.getCellRangeAddresses()) {
                if (isCellInRange(cell, address)) {
                    DataValidationConstraint constraint = validation.getValidationConstraint();
                    if (constraint.getValidationType() == DataValidationConstraint.ValidationType.LIST) {
                        // 处理直接定义的选项
                        String[] explicitValues = constraint.getExplicitListValues();
                        if (explicitValues != null) {
                            String csv = String.join(",", explicitValues);
                            return sanitizeOptionsCSV(csv);
                        }
                        // 处理引用其他单元格的选项
                        String formula = constraint.getFormula1();
                        if (formula != null) {
                            // 特殊规则：当解析 F6 时，强制将 E6 视为 “中国大陆” 进行解析
                            // 兼容两种取第六行的方式：getRow(5) 与 getRow(6)
                            if (cell.getColumnIndex() == 5 && (cell.getRowIndex() == 5 || cell.getRowIndex() == 6)) {
                                log.info("[Dropdown] Force E6 as 中国大陆 for cell={}{}", cell.getRowIndex() + 1, "," + (cell.getColumnIndex() + 1));
                                formula = adjustFormulaAssumeE6ChinaMainland(formula);
                            }
                            String resolved = getOptionsFromReference(sheet, formula);
                            return sanitizeOptionsCSV(resolved);
                        }
                    }
                }
            }
        }
        return null; // 不是下拉框
    }

    /**
     * 手动解析 VLOOKUP(lookup_value, table_array, col_index, range_lookup) ，仅支持精确匹配 (range_lookup=0/false)
     * 返回命名区域名或范围字符串（B 列值），否则返回 null
     */
    private static String tryManualVlookup(Sheet contextSheet, String vlookupFormula) {
        try {
            String expr = vlookupFormula.trim();
            if (expr.startsWith("VLOOKUP(")) {
                expr = expr.substring("VLOOKUP(".length(), expr.lastIndexOf(')'));
            }
            // 简单的顶层逗号切分（不破坏括号里的逗号）
            List<String> args = splitTopLevel(expr, ',');
            if (args.size() < 3) return null;
            String lookup = args.get(0).trim();
            String table = args.get(1).trim();
            int colIndex = Integer.parseInt(args.get(2).trim());
            boolean exact = true;
            if (args.size() >= 4) {
                String r = args.get(3).trim();
                exact = !("1".equals(r) || "TRUE".equalsIgnoreCase(r));
            }
            if (!exact) return null; // 只支持精确匹配

            // 计算 lookup 文本
            String lookupText;
            if ((lookup.startsWith("\"") && lookup.endsWith("\"")) || (lookup.startsWith("'") && lookup.endsWith("'"))) {
                lookupText = stripOuterQuotes(lookup);
            } else {
                String v = evaluateFormulaToString(contextSheet, lookup);
                if (v == null) return null;
                lookupText = v;
            }
            // 解析 table 范围
            AreaReference area = new AreaReference(stripOuterQuotes(table), contextSheet.getWorkbook().getSpreadsheetVersion());
            String sheetName = area.getFirstCell().getSheetName();
            Sheet refSheet = contextSheet.getWorkbook().getSheet(sheetName);
            if (refSheet == null) return null;

            // 遍历第一列匹配
            for (CellReference ref : area.getAllReferencedCells()) {
                if (ref.getCol() != area.getFirstCell().getCol()) continue; // 仅第一列
                Row row = refSheet.getRow(ref.getRow());
                if (row == null) continue;
                Cell keyCell = row.getCell(ref.getCol());
                if (keyCell != null && keyCell.getCellType() == CellType.STRING) {
                    String key = keyCell.getStringCellValue();
                    if (lookupText.equals(key)) {
                        int targetCol = ref.getCol() + (colIndex - 1);
                        Cell valCell = row.getCell(targetCol);
                        if (valCell != null && valCell.getCellType() == CellType.STRING) {
                            return valCell.getStringCellValue();
                        }
                        break;
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 按顶层括号平衡切分（不分割括号内部的逗号）
     */
    private static List<String> splitTopLevel(String s, char sep) {
        List<String> res = new ArrayList<>();
        int depth = 0;
        int last = 0;
        for (int i = 0; i < s.length(); i++) {
            char ch = s.charAt(i);
            if (ch == '(') depth++;
            else if (ch == ')') depth--;
            else if (ch == sep && depth == 0) {
                res.add(s.substring(last, i));
                last = i + 1;
            }
        }
        if (last <= s.length()) res.add(s.substring(last));
        return res;
    }


    /**
     * 将公式中对 E6 的间接引用替换为固定文本 "中国大陆"
     * 仅在特定单元格（如 F6）解析时调用，避免影响其他列/行
     */
    private static String adjustFormulaAssumeE6ChinaMainland(String formula) {
        if (formula == null) return null;
        String f = formula;
        // 常见写法替换
        f = f.replace("INDIRECT(\"$E$6\")", "\"中国大陆\"");
        f = f.replace("INDIRECT($E$6)", "\"中国大陆\"");
        f = f.replace("INDIRECT(\"E6\")", "\"中国大陆\"");
        f = f.replace("INDIRECT(E6)", "\"中国大陆\"");
        // 连接场景："_"&E6 或 "_"&$E$6
        f = f.replace("\"_\"&$E$6", "\"_\"&\"中国大陆\"");
        f = f.replace("\"_\"&E6", "\"_\"&\"中国大陆\"");
        return f;
    }


    // 单个 token 的强力清洗，移除所有可能的空白字符与不可见分隔符
    private static String sanitizeOptionToken(String s) {
        if (s == null) return null;
        return s
                .replace("\u00A0", " ")
                .replace("\u3000", " ")
                .replace("\u202F", " ")
                .replace("\u2009", " ")
                .replace("\u200A", " ")
                .replace("\u2002", " ")
                .replace("\u2003", " ")
                .replace("\u2005", " ")
                .replace("\u2006", " ")
                .replace("\u2007", " ")
                .replace("\u2008", " ")
                .replace("\uFEFF", " ")
                .replace("\u200B", " ")
                .replaceAll("[\\p{Z}\\s]+", "")
                .trim();
    }

    // 清理 options 的 CSV（中英文逗号分割后逐项清洗）
    private static String sanitizeOptionsCSV(String csv) {
        if (csv == null || csv.isEmpty()) return csv;
        String[] parts = csv.split("[,，]+");
        List<String> cleaned = new ArrayList<>();
        for (String p : parts) {
            String t = sanitizeOptionToken(p);
            if (t != null && !t.isEmpty()) cleaned.add(t);
        }
        return String.join(",", cleaned);
    }


    /**
     * 计算指定行上的某列在合并区域中的水平跨度（列数）。
     * 若该列不在任何合并区域内，返回 1。
     */
    private static int getMergedSpanWidth(Sheet sheet, int rowNum, int colNum) {
        if (sheet == null) return 1;
        int numMerged = sheet.getNumMergedRegions();
        for (int i = 0; i < numMerged; i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (region == null) continue;
            if (rowNum >= region.getFirstRow() && rowNum <= region.getLastRow()
                    && colNum >= region.getFirstColumn() && colNum <= region.getLastColumn()) {
                // 水平跨度：最后列 - 起始列 + 1
                return region.getLastColumn() - region.getFirstColumn() + 1;
            }
        }
        return 1;
    }

    /**
     * 判断单元格是否在指定范围内
     */
    private static boolean isCellInRange(Cell cell, CellRangeAddress range) {
        int rowNum = cell.getRowIndex();
        int colNum = cell.getColumnIndex();
        return rowNum >= range.getFirstRow() && rowNum <= range.getLastRow()
                && colNum >= range.getFirstColumn() && colNum <= range.getLastColumn();
    }

    /**
     * 使用 AtomicInteger 确保每次调用递增，避免高并发下出现数据重复
     *
     * @return
     */
    public static String generate() {
        // 时间部分：12位（年月日时分秒）
        DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(timeFormat);
        // 随机数部分：4位
        int randomNum = ThreadLocalRandom.current().nextInt(0, 10000);
        String randomPart = String.format("%04d", randomNum);
        // 计数器部分：4位（循环自增）
        int count = counter.getAndIncrement() % 10000;
        String countPart = String.format("%04d", count);
        return timestamp + countPart; // 组合为16位
    }

    private static final AtomicInteger counter = new AtomicInteger(0);
}
