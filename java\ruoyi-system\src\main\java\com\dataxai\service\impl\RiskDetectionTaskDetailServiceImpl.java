package com.dataxai.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.TaskLogUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.mapper.RiskDetectionTaskDetailMapper;
import com.dataxai.domain.RiskDetectionTaskDetail;
import com.dataxai.service.IRiskDetectionTaskDetailService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.dataxai.domain.dto.RiskDetectionTaskDetailDTO;

/**
 * 风险检测任务详情表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RiskDetectionTaskDetailServiceImpl implements IRiskDetectionTaskDetailService
{
    @Autowired
    private RiskDetectionTaskDetailMapper riskDetectionTaskDetailMapper;


    /**
     * 查询风险检测任务详情表
     *
     * @param id 风险检测任务详情表主键
     * @return 风险检测任务详情表
     */
    @Override
    public RiskDetectionTaskDetail selectRiskDetectionTaskDetailById(Long id)
    {
        return riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailById(id);
    }

    /**
     * 查询风险检测任务详情表列表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 风险检测任务详情表
     */
    @Override
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailList(RiskDetectionTaskDetail riskDetectionTaskDetail)
    {
        return riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailList(riskDetectionTaskDetail);
    }

    /**
     * 根据任务ID查询详情列表
     *
     * @param taskId 任务ID
     * @return 风险检测任务详情表集合
     */
    @Override
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailByTaskId(Long taskId)
    {
        return riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailByTaskId(taskId);
    }

    /**
     * 新增风险检测任务详情表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    @Override
    public int insertRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail)
    {
        riskDetectionTaskDetail.setCreateTime(DateUtils.getNowDate());
        return riskDetectionTaskDetailMapper.insertRiskDetectionTaskDetail(riskDetectionTaskDetail);
    }

    /**
     * 批量新增风险检测任务详情表
     *
     * @param riskDetectionTaskDetailList 风险检测任务详情表列表
     * @return 结果
     */
    @Override
    public int insertRiskDetectionTaskDetailBatch(List<RiskDetectionTaskDetail> riskDetectionTaskDetailList) throws IOException {
        int result = 0;
        for (RiskDetectionTaskDetail detail : riskDetectionTaskDetailList) {
            detail.setCreateTime(DateUtils.getNowDate());
            result += riskDetectionTaskDetailMapper.insertRiskDetectionTaskDetail(detail);
        }
        return result;
    }

    /**
     * 修改风险检测任务详情表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    @Override
    public int updateRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail)
    {
        riskDetectionTaskDetail.setUpdateTime(DateUtils.getNowDate());
        return riskDetectionTaskDetailMapper.updateRiskDetectionTaskDetail(riskDetectionTaskDetail);
    }

    /**
     * 删除风险检测任务详情表
     *
     * @param id 风险检测任务详情表主键
     * @return 结果
     */
    @Override
    public int deleteRiskDetectionTaskDetailById(Long id)
    {
        return riskDetectionTaskDetailMapper.deleteRiskDetectionTaskDetailById(id);
    }

    /**
     * 批量删除风险检测任务详情表
     *
     * @param ids 需要删除的风险检测任务详情表主键
     * @return 结果
     */
    @Override
    public int deleteRiskDetectionTaskDetailByIds(Long[] ids)
    {
        return riskDetectionTaskDetailMapper.deleteRiskDetectionTaskDetailByIds(ids);
    }

    /**
     * 根据任务ID删除详情
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteRiskDetectionTaskDetailByTaskId(Long taskId)
    {
        return riskDetectionTaskDetailMapper.deleteRiskDetectionTaskDetailByTaskId(taskId);
    }

    @Override
    public int updateRiskDetectionTaskDetailAbandoned(RiskDetectionTaskDetail riskDetectionTaskDetail) {

        return riskDetectionTaskDetailMapper.updateRiskDetectionTaskDetailAbandoned(riskDetectionTaskDetail);
    }

}