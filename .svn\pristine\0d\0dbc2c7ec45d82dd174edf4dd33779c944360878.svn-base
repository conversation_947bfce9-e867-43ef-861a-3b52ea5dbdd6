<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.WorkflowTemplateGroupMapper">
    
    <resultMap type="WorkflowTemplateGroup" id="WorkflowTemplateGroupResult">
        <result property="id"           column="id"           />
        <result property="groupName"    column="group_name"   />
        <result property="teamId"       column="team_id"      />
        <result property="userId"       column="user_id"      />
        <result property="createTime"   column="create_time"  />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <sql id="selectWorkflowTemplateGroupVo">
        select id, group_name, team_id, user_id, create_time, update_time
        from workflow_template_group
    </sql>

    <select id="selectWorkflowTemplateGroupList" parameterType="WorkflowTemplateGroup" resultMap="WorkflowTemplateGroupResult">
        <include refid="selectWorkflowTemplateGroupVo"/>
        <where>  
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWorkflowTemplateGroupById" parameterType="Long" resultMap="WorkflowTemplateGroupResult">
        <include refid="selectWorkflowTemplateGroupVo"/>
        where id = #{id}
    </select>

    <select id="selectByTeamIdAndGroupName" resultMap="WorkflowTemplateGroupResult">
        <include refid="selectWorkflowTemplateGroupVo"/>
        where team_id = #{teamId} and group_name = #{groupName}
    </select>

    <select id="countTemplatesByGroupId" parameterType="Long" resultType="int">
        select count(*) from workflow_template where group_id = #{groupId}
    </select>
        
    <insert id="insertWorkflowTemplateGroup" parameterType="WorkflowTemplateGroup" useGeneratedKeys="true" keyProperty="id">
        insert into workflow_template_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="teamId != null">team_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWorkflowTemplateGroup" parameterType="WorkflowTemplateGroup">
        update workflow_template_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkflowTemplateGroupById" parameterType="Long">
        delete from workflow_template_group where id = #{id}
    </delete>

    <delete id="deleteWorkflowTemplateGroupByIds" parameterType="String">
        delete from workflow_template_group where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 