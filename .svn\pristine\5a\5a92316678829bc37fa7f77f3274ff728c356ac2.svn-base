package com.dataxai.web.task.core.ordinal.material;

import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.TaskOrdinalDTO;
import com.dataxai.web.service.MaterialHistoryService;
import com.dataxai.web.service.MaterialResolverService;
import com.dataxai.web.task.core.ordinal.AbstractMaterialTaskOrdinalFactory;
import com.dataxai.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 文生图任务序号工厂实现类
 *
 * <p>处理文生图任务（任务类型：8）的参数校验和逻辑处理</p>
 *
 * <AUTHOR>
 * @date 2024-01-08
 * @version 1.0
 */
@Component
@Slf4j
public class TextToImageTaskOrdinalFactory extends AbstractMaterialTaskOrdinalFactory {

    @Autowired
    private MaterialResolverService materialResolverService;

    @Autowired
    private MaterialHistoryService materialHistoryService;

    @Override
    public void validateParameters(TaskOrdinalDTO taskOrdinalDTO) {
        if (taskOrdinalDTO == null) {
            throw new ServiceException("任务参数不能为空", 500);
        }

        // 校验任务类型
        Long taskType = taskOrdinalDTO.getType();
        if (taskType == null) {
            throw new ServiceException("任务类型不能为空", 500);
        }

        if (Constants.TASK_TYPE_TEXT_TO_IMAGE != taskType) {
            throw new ServiceException("不支持的任务类型：" + taskType + "，当前工厂仅支持文生图任务", 500);
        }

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "开始校验文生图任务参数，任务ID: {}", taskOrdinalDTO.getTaskId());

        // 文生图特定校验
        validateTextToImageSpecificParameters(taskOrdinalDTO);

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "文生图任务参数校验通过，任务ID: {}", taskOrdinalDTO.getTaskId());

    }

    @Override
    public void processLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) throws Exception {
        if (taskOrdinalDTO == null || taskOrdinal == null) {
            throw new ServiceException("任务参数或实体不能为空", 500);
        }

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "开始处理文生图任务逻辑，任务ID: {}", taskOrdinalDTO.getTaskId());

        // 设置任务基本信息
        taskOrdinal.setShortCutDesc(taskOrdinalDTO.getPrompt());

        // 处理文生图特定逻辑
        processTextToImageSpecificLogic(taskOrdinalDTO, taskOrdinal);

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "文生图任务逻辑处理完成，任务ID: {}", taskOrdinalDTO.getTaskId());
    }

    /**
     * 校验文生图特定参数
     */
    private void validateTextToImageSpecificParameters(TaskOrdinalDTO taskOrdinalDTO) {
        // 校验描述信息
        validateDescription(taskOrdinalDTO);
    }

    /**
     * 处理文生图特定逻辑
     */
    private void processTextToImageSpecificLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) {
        log.info("=== 开始处理文生图特定逻辑 ===");
        log.info("任务ID: {}", taskOrdinalDTO.getTaskId());
        log.info("任务类型: {}", taskOrdinalDTO.getType());

        // 检查MaterialResolverService是否注入成功
        if (materialResolverService == null) {
            log.error("MaterialResolverService注入失败！");
            return;
        }
        log.info("MaterialResolverService注入成功");

        // 处理素材信息，构建完整的任务参数（包含第三方接口需要的详细信息）
        String originalTaskParam = taskOrdinal.getTaskParam();
        log.info("调用buildFullTaskParam前，原始参数: {}", originalTaskParam);

        String fullTaskParam = null;
        try {
            fullTaskParam = materialResolverService.buildFullTaskParam(originalTaskParam);
            log.info("调用buildFullTaskParam后，完整参数: {}", fullTaskParam);
        } catch (Exception e) {
            log.error("调用buildFullTaskParam失败", e);
            return;
        }

        // 更新任务参数
        if (StringUtils.isNotEmpty(fullTaskParam)) {
            taskOrdinal.setTaskParam(fullTaskParam);
            log.info("文生图任务参数已更新，包含完整素材信息");
            log.info("更新后的taskParam: {}", taskOrdinal.getTaskParam());
        } else {
            log.warn("buildFullTaskParam返回空值，不更新taskParam");
        }

        // 处理素材信息，构建完整的提示词
        String originalPrompt = taskOrdinalDTO.getPrompt();
        String fullPrompt = materialResolverService.buildFullPrompt(originalPrompt, fullTaskParam);

        // 保持shortCutDesc为用户原始输入，不包含风格信息
        // fullPrompt将在执行阶段动态构建，不存储在taskParam中
        log.info("文生图任务提示词已构建: 原始='{}', 完整='{}'", originalPrompt, fullPrompt);

        // 注意：不将fullPrompt添加到taskParam中，避免OrdinalParamDTO反序列化问题
        // fullPrompt将在实际执行时通过MaterialResolverService.buildFullPrompt()动态生成

        // 记录素材使用历史
        try {
            // 使用taskOrdinalDTO中的taskId，直接使用String类型
            String taskId = taskOrdinalDTO.getTaskId();
            if (taskId != null && !taskId.trim().isEmpty()) {
                Long userIdLong = taskOrdinal.getUserId();
                Integer userId = userIdLong != null ? userIdLong.intValue() : null;

                // 使用更新后的完整任务参数记录历史（带任务类型）
                materialHistoryService.recordMaterialUsageFromTaskParam(
                    fullTaskParam,
                    userId,
                    taskId,
                    com.dataxai.web.Constants.Constants.TASK_TYPE_TEXT_TO_IMAGE
                );
                log.info("素材使用历史记录成功，任务ID: {}", taskId);
            } else {
                log.warn("任务ID为空，跳过素材使用历史记录");
            }
        } catch (Exception e) {
            log.error("记录素材使用历史失败，任务ID: {}", taskOrdinalDTO.getTaskId(), e);
            // 不影响主流程，继续执行
        }

        log.info("=== 文生图特定逻辑处理完成 ===");
    }

    @Override
    protected boolean isSupportedTaskType(Long taskType) {
        return Constants.TASK_TYPE_TEXT_TO_IMAGE == taskType;
    }

    @Override
    protected void validateSpecificParameters(TaskOrdinalDTO taskOrdinalDTO) {
        validateTextToImageSpecificParameters(taskOrdinalDTO);
    }

    @Override
    protected void processSpecificLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) throws Exception {
        processTextToImageSpecificLogic(taskOrdinalDTO, taskOrdinal);
    }

    @Override
    protected void processSpecificExecution(TaskOrdinal taskOrdinal) throws Exception {
        // 文生图特定的执行逻辑
    }

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{(long) Constants.TASK_TYPE_TEXT_TO_IMAGE};
    }

        /**
     * 判断是否需要AIOCR预处理
     * 文生图任务如果有原始图片且描述为空，则需要AIOCR预处理来识别图片内容
     */
    @Override
    protected boolean needsAiocrPreprocess(TaskOrdinal taskOrdinal) {
        // 检查是否有原始图片且描述为空
        boolean hasOriginImage = StringUtils.isNotEmpty(taskOrdinal.getOriginImgUrl());
        boolean hasEmptyDescription = StringUtils.isEmpty(taskOrdinal.getShortCutDesc());

        boolean needAiocr = hasOriginImage && hasEmptyDescription;

        if (needAiocr) {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "文生图任务需要AIOCR预处理 - TaskOrdinalId: {}, 有原始图片: {}, 描述为空: {}",
                     taskOrdinal.getTaskOrdinalId(), hasOriginImage, hasEmptyDescription);
        } else {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TEXT_TO_IMAGE, "文生图任务不需要AIOCR预处理 - TaskOrdinalId: {}, 有原始图片: {}, 描述为空: {}",
                     taskOrdinal.getTaskOrdinalId(), hasOriginImage, hasEmptyDescription);
        }

        return needAiocr;
    }

}