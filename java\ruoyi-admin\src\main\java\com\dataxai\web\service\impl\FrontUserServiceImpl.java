package com.dataxai.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.annotation.DataScope;
import com.dataxai.common.core.Dto.UpdatePasswordDto;
import com.dataxai.common.core.domain.entity.EquityDescriptionDTO;
import com.dataxai.common.core.domain.model.User;
import com.dataxai.common.core.domain.model.UserPackageAndBagVO;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.mapper.*;
import com.dataxai.web.service.IFrontUserService;
import com.dataxai.web.service.ITariffPackageService;
import com.dataxai.web.task.core.TaskScoreService;
import com.dataxai.web.utils.MyDateUtils;
import com.dataxai.web.utils.SnowFlakeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class FrontUserServiceImpl implements IFrontUserService
{
    private static final Logger log = LoggerFactory.getLogger(FrontUserServiceImpl.class);

    @Autowired
    private FrontUserMapper userMapper;

    @Autowired
    protected Validator validator;

    @Resource
    private TariffPackageMapper tariffPackageMapper;

    @Autowired
    private ITariffPackageService tariffPackageService;

    @Resource
    private TariffPackageSubMapper tariffPackageSubMapper;

    @Resource
    private RefuelingBagMapper refuelingBagMapper;

    @Autowired
    private UserPackageMapper userPackageMapper;

    @Autowired
    private UserRefueligBagMapper userRefueligBagMapper;

    @Resource
    private TaskScoreService taskScoreService;

    @Autowired
    private EquityDescriptionMapper equityDescriptionMapper;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<User> selectUserList(User user)
    {
        return userMapper.selectUserList(user);
    }

    @Override
    public User selectUserByPhone(String phone) {
        return userMapper.selectUserByPhone(phone);
    }


    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public User selectUserByNickName(String userName)
    {
        return userMapper.selectUserByNickName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public User selectUserById(Long userId)
    {
        //获取用户信息的时候，如果事付费套餐，则需要判断付费套餐是否过期，如果付费套餐过期，则将用户
        // 使用的套餐变更为默认套餐，设置用户表中的积分字段时，判断
        System.out.println("userId  "+userId);
        User user = userMapper.selectUserByUserId(userId);
        if(null != user){
            if(user.getEnable() == Constants.ENABLE_1){
                throw new ServiceException(Constants.ENABLE_1_Str);
            }
            if(StringUtils.isNotEmpty(user.getPhone())){
                user.setResidueScore(0L);

                //先查询用户套餐是否过期，如果过期，则设置默认套餐，在根据新设定的套餐来计算当前用户的总积分，以及
                //当前使用的套餐信息
                boolean currentUsePackageExpire = currentUsePackageExpire(user);
                if(currentUsePackageExpire){
                    //当前用户使用的套餐过期
                    //套餐过期，剩余积分就是0，需要给用户设定一个默认套餐，至于给设定默认套餐多少积分，
                    //需要看失效的套餐所在的自然月，是否已经赠送过默认套餐，如果已经赠送过默认
                    //套餐，则设定的默认套餐的积分就是0，如果未赠送过默认套餐，则设定默认套餐表中设定
                    // 默认套餐的积分
                    updateDefaultTariffPackageToUser(user);
                }
                long expireRefueligBagScore = getExpireRefueligBagScore(userId);
                if(expireRefueligBagScore>0){
                    //如果存在过期的积分，将过期的积分写入到历史积分记录表
                    taskScoreService.addScoreHistoryLog(userId, Constants.SCORE_TYPE_3, null, -expireRefueligBagScore);
                    //更新加油包中过期的积分为0
                    List<UserRefueligBag> userRefuelingBagList = getUserRefuelingBagList(userId);
                    if(StringUtils.isNotEmpty(userRefuelingBagList)){
                        for (UserRefueligBag item : userRefuelingBagList) {
                            Date refuelingBagEndTime = item.getRefuelingBagEndTime();
                            Date nowDate = DateUtils.getNowDate();
                            int result = nowDate.compareTo(refuelingBagEndTime);
                            if(result>=0){
                                UserRefueligBag userRefueligBagParam = new UserRefueligBag();
                                userRefueligBagParam.setUserRefuelingBagId(item.getUserRefuelingBagId());
                                userRefueligBagParam.setRefuelingBagScore(0L);
                                userRefueligBagMapper.updateUserRefueligBag(userRefueligBagParam);
                            }
                        }
                    }
                }
                //剩余积分
                Long total = 0L;
                List<UserPackage> userPackageList = getUserPackageList(user.getUserId());
                if(null != userPackageList){
                    if(userPackageList.size() == 1){
                        UserPackage userPackage = userPackageList.get(0);
                        // 设置当前套餐的过期时间
                        HashMap<String, Object> hashMap = getUnExpireTariffScore(userPackage);
                        Date endTime = (Date) hashMap.get(Constants.KEY_TARIFF_END_TIME);
                        long scoreLeft = (long) hashMap.get(Constants.KEY_TARIFF_UNEXPIRE_SCORE);
                        total += scoreLeft;
                        user.setTariffEndTime(endTime);
                        user.setPackageSubId(userPackage.getPackageSubId());
                    }else {
                        log.error("用户购买多个套餐");
                    }
                }else {
                    log.error("用户未购买套餐");
                }

                //查询套餐名称
                String packageId = user.getPackageId();
                if(StringUtils.isNotEmpty(packageId)){
                    TariffPackage tariffPackage = tariffPackageMapper.selectById(packageId);
                    if(null != tariffPackage){
                        user.setPackageName(tariffPackage.getName());
                        //当前套餐是否 是 默认套餐，前端显示钻石用
                        user.setIsDefaultPackage(tariffPackage.getIsDefault());
                        //当前套餐是否能够下载4k图
                        user.setFourKDown(tariffPackage.getFourKDown());
                    }else {
                        log.error("用户的套餐为空");
                    }
                }
                long unExpireRefuelingBagScore = getUnExpireRefueligBagScore(userId);
                user.setResidueScore(total+unExpireRefuelingBagScore);

                //设置过期积分和失效时间
                setExpireScoreAndTime(userPackageList, user);
            }
            if(StringUtils.isNotEmpty(user.getPhone())){
                String phone = user.getPhone();
                String prefix = phone.substring(0,3);
                String suffix = phone.substring(7);
                String replacePhone = prefix+"****"+suffix;
                user.setPhone(replacePhone);
            }
            //处理密码值
            if(StringUtils.isNotEmpty(user.getPassword())){
                user.setIsPassword(true);
                user.setPassword(null);
            }else{
                user.setIsPassword(false);
            }
            //查询用户使用的套餐的权益信息
            //根据套餐id查询权益描述
            List<EquityDescription> equityDescriptions = equityDescriptionMapper.selectListByTaruffId(user.getPackageId());
            List<EquityDescriptionDTO> collect = equityDescriptions.stream().map(item->{
                EquityDescriptionDTO equityDescriptionDTO = new EquityDescriptionDTO();
                BeanUtils.copyProperties(item,equityDescriptionDTO);
                return equityDescriptionDTO;
            }).collect(Collectors.toList());
            user.setEquityDescriptionList(collect);

            user = selectUserPackageAndBag(user,user.getUserId());


        }else {
            log.info("查询的用户为空，或者用户手机号为空");
        }
        return user;
    }

    //处理用户的加油包套餐信息
    private User selectUserPackageAndBag(User user,Long userId) {
        List<UserPackageAndBagVO> list = new ArrayList<>();
        UserPackage userPackage = new UserPackage();
        userPackage.setUserId(userId);
        userPackage.setDelFlag(0);
        //查询套餐信息
        List<UserPackage> userPackages = userPackageMapper.selectUserPackageList(userPackage);
        if(CollectionUtil.isNotEmpty(userPackages)){
            UserPackage userPackageRes = userPackages.get(0);
            Date endTime = getEndTime(userPackageRes.getPackageStartTime(), userPackageRes.getPackageCycle());
            Boolean flag = DateUtils.whetherExpired(endTime);
            if(! flag){
                UserPackageAndBagVO vo = new UserPackageAndBagVO();
                vo.setType(1);
                vo.setRefuelingBagScore(userPackageRes.getPackageScore());
                vo.setRefuelingBagEndTime(endTime);
                list.add(vo);
            }
        }
        //查询加油包信息
        UserRefueligBag userRefueligBag = new UserRefueligBag();
        userRefueligBag.setUserId(userId);
        userRefueligBag.setDelFlag(0);

        List<UserRefueligBag> userRefueligBags = userRefueligBagMapper.selectUserRefueligBagListByUserId(userRefueligBag);
        if(CollectionUtil.isNotEmpty(userRefueligBags)){
            // 过滤出购买和后台添加的
            List<UserRefueligBag> insertBag = userRefueligBags.stream().filter(item -> item.getPackageId() == null && item.getRefuelingBagId() == null).collect(Collectors.toList());
            UserPackageAndBagVO insertBagVO = disposeBagList(insertBag,3);
            if(insertBagVO != null ){
                list.add(insertBagVO);
            }
            List<UserRefueligBag> buyBags = userRefueligBags.stream().filter(item -> item.getRefuelingBagId() != null).collect(Collectors.toList());
            UserPackageAndBagVO buyBagVO = disposeBagList(buyBags, 2);
            if(buyBagVO != null ){
                list.add(buyBagVO);
            }

        }
        user.setUserPackageAndBagVOs(list);
        return user;
    }

    // 处理加油包信息
    private UserPackageAndBagVO disposeBagList(List<UserRefueligBag> insertBag, int type) {
        long insertTotal = 0l;
        if(CollectionUtil.isNotEmpty(insertBag)){
            List<Date> dateList = new ArrayList<>();
            for (UserRefueligBag refueligBag : insertBag) {
                Boolean aBoolean = DateUtils.whetherExpired(refueligBag.getRefuelingBagEndTime());
                if(! aBoolean){
                    insertTotal = insertTotal + refueligBag.getRefuelingBagScore();
                    dateList.add(refueligBag.getRefuelingBagEndTime());
                }
            }

            if(CollectionUtil.isNotEmpty(dateList)){
                UserPackageAndBagVO userPackageAndBagVO = new UserPackageAndBagVO();
                userPackageAndBagVO.setType(type);
                userPackageAndBagVO.setRefuelingBagScore(insertTotal);
                Date maxDate = DateUtils.findMaxDateWithCollections(dateList);
                userPackageAndBagVO.setRefuelingBagEndTime(maxDate);
                return userPackageAndBagVO;
            }
        }
       return null;
    }

    public long getUnExpireRefueligBagScore(Long userId) {
        List<UserRefueligBag> userRefueligBagList = getUserRefuelingBagList(userId);
        long total=0L;
        if(null != userRefueligBagList){
            total+=userRefueligBagList.stream().filter(item->{
                Date refuelingBagEndTime = item.getRefuelingBagEndTime();
                Date nowDate = DateUtils.getNowDate();
                int result = nowDate.compareTo(refuelingBagEndTime);
                return result<0;
            }).mapToLong(UserRefueligBag::getRefuelingBagScore).sum();
        }
        return total;
    }

    public long getExpireRefueligBagScore(Long userId) {
        List<UserRefueligBag> userRefueligBagList = getUserRefuelingBagList(userId);
        long total=0L;
        if(null != userRefueligBagList){
            total+=userRefueligBagList.stream().filter(item->{
                Date refuelingBagEndTime = item.getRefuelingBagEndTime();
                Date nowDate = DateUtils.getNowDate();
                int result = nowDate.compareTo(refuelingBagEndTime);
                return result>=0;
            }).mapToLong(UserRefueligBag::getRefuelingBagScore).sum();
        }
        return total;
    }

    private List<UserRefueligBag> getUserRefuelingBagList(Long userId) {
        UserRefueligBag userRefueligBag = new UserRefueligBag();
        userRefueligBag.setUserId(userId);
        userRefueligBag.setDelFlag(0);
        List<UserRefueligBag> userRefueligBagList = userRefueligBagMapper.selectUserRefueligBagList(userRefueligBag);
        return userRefueligBagList;
    }

    public HashMap<String,Object> getUnExpireTariffScore(UserPackage userPackage) {
        HashMap<String,Object> resultMap = new HashMap<>();
        Date packageStartTime = userPackage.getPackageStartTime();
        TariffPackage tariffPackage = tariffPackageMapper.selectTariffPackageByTariffPackageId(userPackage.getPackageId());
        Date endTime = getEndTime(packageStartTime, userPackage.getPackageCycle());
        if(null != tariffPackage){
            if(tariffPackage.getIsDefault() == 1){
                endTime = MyDateUtils.getLastDayOfMonth(packageStartTime);
            }
        }
        Date nowDate = DateUtils.getNowDate();
        int result = nowDate.compareTo(endTime);
        long scoreLeft = 0L;
        if(result<0){
            //套餐未过期
            scoreLeft = userPackage.getPackageScore();
        }
        resultMap.put(Constants.KEY_TARIFF_END_TIME,endTime);
        resultMap.put(Constants.KEY_TARIFF_UNEXPIRE_SCORE,scoreLeft);
        return resultMap;
    }

    private boolean currentUsePackageExpire(User frontUser){
        List<UserPackage> userPackageList = getUserPackageList(frontUser.getUserId());
        if(null != userPackageList){
            if(userPackageList.size() == 1){
                UserPackage userPackage = userPackageList.get(0);
                // 设置当前套餐的过期时间
                Date packageStartTime = userPackage.getPackageStartTime();
                Date endTime = getEndTime(packageStartTime, userPackage.getPackageCycle());
                //如果使用的套餐是默认套餐(免费套餐)
                TariffPackage tariffPackage = tariffPackageMapper.selectTariffPackageByTariffPackageId(userPackage.getPackageId());
                if(null != tariffPackage){
                    if(null != tariffPackage.getIsDefault() && tariffPackage.getIsDefault()==1){
                        //表示用使用的是默认套餐,默认套餐的过期时间是自然月的最后一天的23:59:59
                        endTime = MyDateUtils.getLastDayOfMonth(packageStartTime);
                    }
                    Date nowDate = DateUtils.getNowDate();
                    int result = nowDate.compareTo(endTime);
                    if(result>=0){
                        if(userPackage.getPackageScore()>0){
                            taskScoreService.addScoreHistoryLog(frontUser.getUserId(), Constants.SCORE_TYPE_3, null, -userPackage.getPackageScore());
                        }
                    }
                    return result>=0; // 过期
                }else {
                    log.info("套餐不存在");
                    throw new ServiceException("套餐不存在");
                }
            }else if(userPackageList.size() == 0){
                log.info("用户没有使用套餐");
                throw new ServiceException("用户没有使用套餐");
            }else {
                log.error("用户购买多个套餐");
                throw new ServiceException("用户使用多个套餐");
            }
        }else {
            log.error("用户没有使用套餐");
            throw new ServiceException("用户没有使用套餐");
        }
    }

    /**
     * 设置过期积分和失效时间
     * @param userPackageList
     * @param user
     */
    private void setExpireScoreAndTime(List<UserPackage> userPackageList, User user) {
        //过期积分和失效时间
        //加油包积分
        Date nowDate = DateUtils.getNowDate();
        UserRefueligBag userRefueligBag = new UserRefueligBag();
        userRefueligBag.setUserId(user.getUserId());
        userRefueligBag.setDelFlag(0);
        List<UserRefueligBag> userRefueligBags = userRefueligBagMapper.selectUserRefueligBagListByUserId(userRefueligBag);
        List<UserRefueligBag> unExpireUserRefuelingBags = userRefueligBags.stream()
                .filter(item -> {
                        Date refuelingBagEndTime = item.getRefuelingBagEndTime();
                        int result = nowDate.compareTo(refuelingBagEndTime);
                        return result<0 && item.getRefuelingBagScore()>0 ;
                    }
                )
                .collect(Collectors.toList());
        // 套餐和加油包都不为空
        if(CollectionUtil.isNotEmpty(userPackageList) && CollectionUtil.isNotEmpty(unExpireUserRefuelingBags)){
            for (int i = 0; i < unExpireUserRefuelingBags.size(); i++) {
                UserPackage userPackageVo = userPackageList.get(0);
                UserRefueligBag userRefueligBagVo = unExpireUserRefuelingBags.get(i);
                //套餐中积分过期时间
                Date packageStartTime = userPackageVo.getPackageStartTime();
                Date packageEndTime = getEndTime(packageStartTime, Integer.parseInt(userPackageVo.getPackageCycle().toString()));
                //如果套餐是默认套餐，则套餐的积分过期时间是当前自然月的最后一天
                String packageId = userPackageVo.getPackageId();
                //通过用户当前正在使用套餐id,查看套餐表，看用户使用的是否是默认套餐，如果是默认套餐，则默认套餐的过期时间是当前月的最后一天
                TariffPackage tariffPackage = tariffPackageService.selectTariffPackageByTariffPackageId(packageId);
                if(null != tariffPackage && null != tariffPackage.getIsDefault() && tariffPackage.getIsDefault() == 1){
                    packageEndTime = MyDateUtils.getLastDayOfMonth(packageStartTime);
                }
                //加油包中积分过期时间
                Date refuelingBagEndTime = userRefueligBagVo.getRefuelingBagEndTime();
                boolean packUnExpire = nowDate.compareTo(packageEndTime) < 0;
                boolean refuelingUnExpire = nowDate.compareTo(refuelingBagEndTime) < 0;
                Long packageScore = userPackageVo.getPackageScore();
                Long refuelingBagScore = userRefueligBagVo.getRefuelingBagScore();
                if(packUnExpire && refuelingUnExpire){
                    //套餐和加油包都未过期
                    //返回套餐积分
                    if(packageEndTime.compareTo(refuelingBagEndTime) <0 ){
                        //快要过期的积分
                        user.setResidualIntegral(packageScore);
                        user.setExpirationTime(packageScore>0?packageEndTime:null);
                        user.setPackageOrRefueligBag(1);
                    }
                    //返回加油包积分
                    else {
                        user.setResidualIntegral(refuelingBagScore);
                        user.setExpirationTime(refuelingBagScore>0?userRefueligBagVo.getRefuelingBagEndTime():null);
                        user.setPackageOrRefueligBag(2);
                    }
                }else if(packUnExpire && !refuelingUnExpire){
                    //套餐未过期，加油包已过期
                    //快要过期的积分
                    user.setResidualIntegral(packageScore);
                    user.setExpirationTime(packageScore>0?packageEndTime:null);
                    user.setPackageOrRefueligBag(1);
                }else if(!packUnExpire && refuelingUnExpire){
                    //套餐已过期，加油包未过期
                    user.setResidualIntegral(refuelingBagScore);
                    user.setExpirationTime(refuelingBagScore>0?userRefueligBagVo.getRefuelingBagEndTime():null);
                    user.setPackageOrRefueligBag(2);
                }
            }
        }
        //套餐有，加油包为空
        if(CollectionUtil.isNotEmpty(userPackageList) && CollectionUtil.isEmpty(unExpireUserRefuelingBags)){
            UserPackage userPackageVo = userPackageList.get(0);
            Date packageStartTime = userPackageVo.getPackageStartTime();
            Date packageEndTime = getEndTime(packageStartTime, Integer.parseInt(userPackageVo.getPackageCycle().toString()));
            //如果套餐是默认套餐，则套餐的积分过期时间是当前自然月的最后一天
            String packageId = userPackageVo.getPackageId();
            //通过用户当前正在使用套餐id,查看套餐表，看用户使用的是否是默认套餐，如果是默认套餐，则默认套餐的过期时间是当前月的最后一天
            TariffPackage tariffPackage = tariffPackageService.selectTariffPackageByTariffPackageId(packageId);
            if(null != tariffPackage && null != tariffPackage.getIsDefault() && tariffPackage.getIsDefault() == 1){
                packageEndTime = MyDateUtils.getLastDayOfMonth(packageStartTime);
            }
            if(nowDate.compareTo(packageEndTime)<0){
                //快要过期的积分
                Long packageScore = userPackageVo.getPackageScore();
                user.setResidualIntegral(packageScore);
                //失效时间
                user.setExpirationTime(packageScore>0?packageEndTime:null);
                user.setPackageOrRefueligBag(1);
            }
        }
        //套餐空，加油包不空
        if(CollectionUtil.isEmpty(userPackageList) && CollectionUtil.isNotEmpty(unExpireUserRefuelingBags)){
            for (int i = 0; i < unExpireUserRefuelingBags.size(); i++) {
                UserRefueligBag userRefueligBagVo = unExpireUserRefuelingBags.get(0);
                Date refuelingBagEndTime = userRefueligBagVo.getRefuelingBagEndTime();
                if(nowDate.compareTo(refuelingBagEndTime)<0){
                    //快要过期的积分
                    Long refuelingBagScore = userRefueligBagVo.getRefuelingBagScore();
                    user.setResidualIntegral(refuelingBagScore);
                    //失效时间
                    user.setExpirationTime(refuelingBagScore>0?refuelingBagEndTime:null);
                    user.setPackageOrRefueligBag(2);
                }
            }

        }
    }

    private static Date getEndTime(Date packageStartTime, int dayNums) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(packageStartTime);
        calendar.add(Calendar.DAY_OF_YEAR, dayNums);
        //套餐失效时间
        return calendar.getTime();
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(User user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && (user.getEnable()==1))
        {
            throw new ServiceException("不允许操作");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(User user)
    {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(User user)
    {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(User user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(User user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(User user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId)
    {
        return userMapper.deleteUserByUserId(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds)
    {
        return userMapper.deleteUserByUserIds(userIds);
    }

    public Boolean loginOrRegisterByPhone(String frontUserPhone){
        log.info("loginOrRegister  frontUserPhone :"+frontUserPhone);
        //根据手机号查询用户是否存在
        User frontUser = userMapper.selectUserByPhone(frontUserPhone);
        // 判断用户信息是否存在
        Boolean flag = false;
        if (null == frontUser){
            flag = true;
            // 如果不存在，插入用户信息
            User user = new User();
            user.setNickName("");
            user.setGender(0);
            user.setPhone(frontUserPhone);
            user.setEnable(0);
            user.setDelFlag(0);
            Date now = new Date();
            user.setCreateTime(now);

            int inserted = userMapper.insertUser(user);
            System.out.println("user信息"+user.toString());
            if(inserted>0){
                User userByPhone = userMapper.selectUserByPhone(frontUserPhone);
                //如果用户未被赠送过套餐，则给用户赠送默认的套餐
                if(StringUtils.isEmpty(userByPhone.getPackageId())){
                    updateTariffPackageToUser(userByPhone);
                    userByPhone.setFreeGiftTime(now);
                    userMapper.updateUser(userByPhone);
                }
            }
        }else {
            //如果用户未被赠送过套餐，则给用户赠送默认的套餐
            if(StringUtils.isEmpty(frontUser.getPackageId())){
                updateTariffPackageToUser(frontUser);
                frontUser.setFreeGiftTime(DateUtils.getNowDate());
                userMapper.updateUser(frontUser);
            }
        }
        return flag;
    }

    @Override
    @Transactional
    public void loginOrRegisterByOpenId(String openId,String frontUserPhone) {

    }

    private synchronized void updateTariffPackageToUser(User frontUser) {
        TariffPackage tariffPackageParam = new TariffPackage();
        tariffPackageParam.setIsDefault(1);
        tariffPackageParam.setIsRecommend(null);
        tariffPackageParam.setOnSale(1L);
        List<TariffPackage> tariffPackageList = tariffPackageMapper.selectTariffPackageList(tariffPackageParam);
        System.out.println("查询到的默认套餐列表数据    "+tariffPackageList);
        if(null != tariffPackageList){
            System.out.println("查询到的默认套餐列表数量    "+tariffPackageList.size());
            if(tariffPackageList.size() == 1){
                TariffPackage tariffPackage = tariffPackageList.get(0);
                String tariffPackageId = tariffPackage.getTariffPackageId();
                frontUser.setPackageId(tariffPackageId);

                UserPackage userPackage = new UserPackage();
                String nextId  = SnowFlakeUtils.nextIdStr();
                userPackage.setUserPackageId(nextId);
                log.info("       传入的userId = "+frontUser.getUserId());
                userPackage.setUserId(frontUser.getUserId());
                userPackage.setPackageId(tariffPackageId);
                userPackage.setDelFlag(0);
                List<TariffPackageSub> tariffPackageSubList = tariffPackage.getTariffPackageSubList();
                System.out.println("查询到的默认套餐子套餐列表    "+tariffPackageSubList);
                if(null != tariffPackageSubList){
                    System.out.println("查询到的默认套餐子套餐数量    "+tariffPackageSubList.size());
                    if(tariffPackageSubList.size() == 1){
                        TariffPackageSub tariffPackageSub = tariffPackageSubList.get(0);
                        userPackage.setPackageSubId(tariffPackageSub.getTariffPackageSubId());
                        userPackage.setPackageScore(tariffPackageSub.getScore());
                        YearMonth now = YearMonth.now();
                        userPackage.setPackageCycle(now.lengthOfMonth());
                        //userPackage.setPackageCycle(tariffPackageSub.getCycle());
                        Date nowDate = DateUtils.getNowDate();
                        userPackage.setPackageStartTime(nowDate);
                        userPackage.setCreateTime(nowDate);
                        userPackage.setUpdateTime(nowDate);
                    }
                }
                log.info("将默认套餐信息添加到用户的套餐加油包信息表中    ");
                userPackageMapper.insertUserPackage(userPackage);
            }else {
                log.error("存在多个默认套餐");
                for (TariffPackage aPackage : tariffPackageList) {
                    System.out.println(aPackage.getIsDefault() +"       "+aPackage.getTariffPackageId());
                }
                throw new ServiceException("存在多个默认套餐");
            }
        }else {
            log.error("默认套餐不存在");
            throw new ServiceException("默认套餐不存在");
        }
    }

    @Transactional
    public void updateDefaultTariffPackageToUser(User frontUser) {
        TariffPackage tariffPackageParam = new TariffPackage();
        tariffPackageParam.setIsDefault(1);
        tariffPackageParam.setIsRecommend(null);
        tariffPackageParam.setOnSale(1L);
        List<TariffPackage> tariffPackageList = tariffPackageMapper.selectTariffPackageList(tariffPackageParam);
        System.out.println("查询到的默认套餐列表数据    "+tariffPackageList);
        if(null != tariffPackageList){
            System.out.println("查询到的默认套餐列表数量    "+tariffPackageList.size());
            if(tariffPackageList.size() == 1){
                TariffPackage tariffPackage = tariffPackageList.get(0);
                String tariffPackageId = tariffPackage.getTariffPackageId();
                //重新设置新的套餐id
                frontUser.setPackageId(tariffPackageId);
                frontUser.setIsDefaultPackage(tariffPackage.getIsDefault());
                List<UserPackage> userPackageList = getUserPackageList(frontUser.getUserId());
                if(null != userPackageList){
                    if(userPackageList.size() == 1){
                        UserPackage userPackage = userPackageList.get(0);
                        userPackage.setPackageId(tariffPackageId);
                        List<TariffPackageSub> tariffPackageSubList = tariffPackage.getTariffPackageSubList();
                        System.out.println("通过openId登录时查询到的默认套餐子套餐列表    "+tariffPackageSubList);
                        if(null != tariffPackageSubList){
                            System.out.println("通过openId登录时查询到的默认套餐子套餐数量    "+tariffPackageSubList.size());
                            if(tariffPackageSubList.size() == 1){
                                TariffPackageSub tariffPackageSub = tariffPackageSubList.get(0);
                                userPackage.setPackageSubId(tariffPackageSub.getTariffPackageSubId());
                                //已经赠送过了，那么就不能在次赠送默认套餐的积分了，只能给0积分
                                Date nowDate = DateUtils.getNowDate();
                                Date freeGiftTime = frontUser.getFreeGiftTime();
                                boolean sameMonth = MyDateUtils.isSameMonth(nowDate, freeGiftTime);
                                Long packScore = sameMonth?0L:tariffPackageSub.getScore();
                                userPackage.setPackageScore(packScore);
                                //根据本月天数给周期值
                                YearMonth currentYearMonth = YearMonth.now();
                                userPackage.setPackageCycle(currentYearMonth.lengthOfMonth());
                                //第一次注册的时候，赠送的免费套餐的起始有效时间安装注册的时间算
                                //后面每个自然月赠送的免费套餐的积分，积分的有效开始时间按照每个自然月的
                                //第一天开始算
                                Date packageStartTime = MyDateUtils.firstDayOfMonthWithTimeToDate();

                                userPackage.setPackageStartTime(packageStartTime);
                                userPackage.setUpdateTime(nowDate);
                                userPackageMapper.updateUserPackage(userPackage);
                                //同时更新用户表中的套餐id
                                frontUser.setFreeGiftTime(nowDate);
                                userMapper.updateUser(frontUser);
                            }
                        }
                    }
                }
            }else {
                log.error("存在多个默认套餐");
                for (TariffPackage aPackage : tariffPackageList) {
                    System.out.println(aPackage.getIsDefault() +"       "+aPackage.getTariffPackageId());
                }
                throw new ServiceException("存在多个默认套餐");
            }
        }else {
            log.error("默认套餐不存在");
            throw new ServiceException("默认套餐不存在");
        }
    }

    public List<UserPackage> getUserPackageList(Long userId) {
        UserPackage userPackageParam = new UserPackage();
        userPackageParam.setUserId(userId);
        userPackageParam.setDelFlag(0);
        List<UserPackage> userPackageList = userPackageMapper.selectUserPackageList(userPackageParam);
        return userPackageList;
    }


    public User getUserByPhone(String frontUserPhone) {
        log.info("getUserByPhone frontUserPhone :  "+frontUserPhone);
        User frontUser = userMapper.selectUserByPhone(frontUserPhone);
        log.info("frontUser = "+frontUser);
        return frontUser;
    }

    @Override
    public int refreshLoginTime(Long userId) {
        User user = new User();
        user.setUserId(userId);
        user.setLoginTime(new Date());
        return userMapper.updateUser(user);
    }

    /**
     * 通过openId删除用户
     *
     * @param openId 用户ID
     * @return 结果
     */
    @Override
    public int deleteUserByOpenId(String openId)
    {
        return userMapper.deleteUserByOpenId(openId);
    }

    @Override
    public User selectUserByOpenId(String openId) {
        log.info("selectUserByOpenId openId :  "+openId);
        User frontUser = userMapper.selectUserByOpenId(openId);
        log.info("frontUser = "+frontUser);
        return frontUser;
    }

    @Override
    public int resetBindState(Long userId) {
        User userParam = new User();
        userParam.setUserId(userId);
        userParam.setBindFail(null);
        userParam.setUpdateTime(DateUtils.getNowDate());
        return userMapper.resetUserBindFailState(userParam);
    }

    @Override
    public User selectUserPhoneById(Long userId) {
        return userMapper.selectUserByUserId(userId);
    }

    /**
     * 用手机号查询用户
     * @param frontUserPhone
     * @param verificationCode
     * @return
     */
    @Override
    public User selectOneByPhonePassword(String frontUserPhone) throws NoSuchAlgorithmException {
        return  userMapper.selectOneByPhonePassword(frontUserPhone);
    }

    /**
     * 修改密码 需传原密码
     * @param dto
     * @return
     */
    @Override
    public Integer updatePassword(UpdatePasswordDto dto) throws NoSuchAlgorithmException {
        if(dto.getUserId() == null || StringUtils.isEmpty(dto.getNewPassword())){
            throw new ServiceException("新密码不能为空");
        }
        if(dto.getNewPassword().length()< 6 || dto.getNewPassword().length() > 16){
            throw new ServiceException("新密码需在 6-16位");
        }
        //校验旧密码
        //根据手机号查询用户
        User user = userMapper. selectUserByUserId(dto.getUserId());
        if(user != null && StringUtils.isNotEmpty(user.getPassword())) {
            //老用户 判断密码
            //密码哈希
            StringBuilder hexString = passwordHash(dto.getOldPassword());
            if (!user.getPassword().equals(hexString.toString())) {
                throw new ServiceException("用户密码错误");
            }
        }else if(user == null){
            throw new ServiceException("用户不存在");
        }
        //原密码验证成功 修改新密码
        StringBuilder hexString = passwordHash(dto.getNewPassword());
        user.setPassword(hexString.toString());
        user.setUpdateTime(new Date());
        Integer count = userMapper.updateUser(user);

        return count;
    }

    @Override
    public Integer updatePasswordByNew(UpdatePasswordDto dto) throws NoSuchAlgorithmException {
        if(StringUtils.isEmpty(dto.getNewPassword()) ){
            throw new ServiceException("新密码不能为空");
        }
        if(dto.getNewPassword().length()< 6 || dto.getNewPassword().length() > 16){
            throw new ServiceException("新密码需在 6-16位");
        }

        //根据手机号查询用户
        User user = userMapper. selectUserByUserId(dto.getUserId());
        if(user == null){
            throw new ServiceException("用户不存在");
        }
        //原密码验证成功 修改新密码
        StringBuilder hexString = passwordHash(dto.getNewPassword());
        user.setPassword(hexString.toString());
        user.setUpdateTime(new Date());
        Integer count = userMapper.updateUser(user);
        return count;

    }


    /**
     * 密码哈希
     * @param verificationCode
     * @return
     * @throws NoSuchAlgorithmException
     */
    private StringBuilder passwordHash(String verificationCode) throws NoSuchAlgorithmException {
        StringBuilder hexString = new StringBuilder();
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] encodedhash = digest.digest(verificationCode.getBytes());

        for (byte b : encodedhash) {
            String hex = Integer.toHexString(0xff & b);
            if(hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString;

    }

}
