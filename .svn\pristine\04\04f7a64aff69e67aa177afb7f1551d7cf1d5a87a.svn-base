package com.dataxai.web.controller.ImageController;

import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.web.domain.MaterialStyleUser;
import com.dataxai.web.service.MaterialStyleUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/material/style/favorite")
@Api(tags = "风格素材收藏管理")
public class MaterialStyleUserController {
    @Autowired
    private MaterialStyleUserService service;

    @PostMapping
    @ApiOperation("添加风格收藏")
    public R<Boolean> addFavorite(@RequestBody MaterialStyleUser record) {
        try {
            // 如果没有传userId，从登录信息获取
            if (record.getUserId() == null) {
                Long userIdLong = SecurityUtils.getUserId();
                Integer userId = userIdLong.intValue();
                record.setUserId(userId);
            }

            // 验证必要字段
            if (record.getMaterialStyleId() == null) {
                return R.fail("素材ID不能为空");
            }

            return R.success(service.addFavorite(record));
        } catch (Exception e) {
            return R.fail("添加收藏失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("移除风格收藏")
    public R<Boolean> removeFavorite(@PathVariable Integer id) {
        return R.success(service.removeFavorite(id));
    }

    @DeleteMapping("/by-style")
    @ApiOperation("根据用户和风格移除收藏")
    public R<Boolean> removeFavoriteByUserAndStyle(
            @RequestParam Integer userId,
            @RequestParam Integer styleId) {
        return R.success(service.removeFavoriteByUserAndStyle(userId, styleId));
    }

    @GetMapping("/user")
    @ApiOperation("获取用户收藏列表")
    public R<PageResult<MaterialStyleUser>> getUserFavorites(
            @RequestParam Integer userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return R.success(service.getUserFavorites(userId, pageNum, pageSize));
    }

    @GetMapping("/check")
    @ApiOperation("检查是否已收藏")
    public R<Boolean> checkFavorite(
            @RequestParam Integer userId,
            @RequestParam Integer styleId) {
        return R.success(service.isFavorited(userId, styleId));
    }

    @PostMapping("/simple")
    @ApiOperation("简单添加收藏（只需传素材ID）")
    public R<Boolean> addFavoriteSimple(@ApiParam("素材ID") @RequestParam Integer materialStyleId) {
        try {
            Long userIdLong = SecurityUtils.getUserId();
            Integer userId = userIdLong.intValue();

            MaterialStyleUser record = new MaterialStyleUser();
            record.setUserId(userId);
            record.setMaterialStyleId(materialStyleId);

            return R.success(service.addFavorite(record));
        } catch (Exception e) {
            return R.fail("添加收藏失败: " + e.getMessage());
        }
    }
}