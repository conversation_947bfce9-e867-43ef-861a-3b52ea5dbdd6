package com.dataxai.web.service;

import com.dataxai.web.controller.ImageController.PageResult;
import com.dataxai.web.dto.MaterialIpWithFavoriteDTO;
import com.dataxai.web.dto.MaterialStyleWithFavoriteDTO;

/**
 * 带收藏状态的素材服务接口
 */
public interface MaterialWithFavoriteService {
    
    /**
     * 分页查询IP素材（带收藏状态）
     */
    PageResult<MaterialIpWithFavoriteDTO> queryIpPageWithFavorite(
            Integer userId, Integer pageNum, Integer pageSize, 
            String name, Integer status, Integer categoryId);
    
    /**
     * 分页查询风格素材（带收藏状态）
     */
    PageResult<MaterialStyleWithFavoriteDTO> queryStylePageWithFavorite(
            Integer userId, Integer pageNum, Integer pageSize, 
            String name, Integer status, Integer categoryId, Integer taskType);
    
    /**
     * 获取用户收藏的IP素材列表
     */
    PageResult<MaterialIpWithFavoriteDTO> getUserFavoriteIps(
            Integer userId, Integer pageNum, Integer pageSize);
    
    /**
     * 获取用户收藏的风格素材列表
     */
    PageResult<MaterialStyleWithFavoriteDTO> getUserFavoriteStyles(
            Integer userId, Integer pageNum, Integer pageSize, Integer taskType);

    /**
     * 获取用户最近使用的IP素材列表（7天内）
     */
    PageResult<MaterialIpWithFavoriteDTO> getUserRecentIps(
            Integer userId, Integer pageNum, Integer pageSize);

    /**
     * 获取用户最近使用的风格素材列表（7天内）
     */
    PageResult<MaterialStyleWithFavoriteDTO> getUserRecentStyles(
            Integer userId, Integer pageNum, Integer pageSize, Integer taskType);
}
