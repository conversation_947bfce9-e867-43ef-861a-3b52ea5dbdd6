---
description: 
globs: 
alwaysApply: false
---
### 项目总览

- **后端模块**：`ruoyi-admin`、`ruoyi-common`、`ruoyi-framework`、`ruoyi-generator`、`ruoyi-quartz`、`ruoyi-system`
- **前端管理端**：`ruoyi-ui/admin`（Vue）与 `ruoyi-ui/client`
- **脚本**：根目录 `bin/` 下提供 `run.bat`、`package.bat`、`clean.bat`
- **配置**：Spring Boot 配置入口为 [ruoyi-admin/src/main/resources/application.yml](mdc:ruoyi-admin/src/main/resources/application.yml)，含 `dev/local` 等 profile；MyBatis 全局配置见 [ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml](mdc:ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml)
- **数据库脚本**：参见 [sql/](mdc:sql) 目录（开发库初始化与变更脚本）
- **日志**：按日期分目录存放于 [logs/](mdc:logs)
- **容器**：`ruoyi-admin` 镜像构建 Dockerfile 在 [ruoyi-admin/src/main/docker/Dockerfile](mdc:ruoyi-admin/src/main/docker/Dockerfile)

### 常用入口
- Spring Boot 应用配置：[ruoyi-admin/src/main/resources/application.yml](mdc:ruoyi-admin/src/main/resources/application.yml)
- MyBatis 全局配置：[ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml](mdc:ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml)
- 静态测试页（SocketIO）：[ruoyi-admin/src/main/resources/static/socketio-test.html](mdc:ruoyi-admin/src/main/resources/static/socketio-test.html)
- 微信/支付密钥模板：[ruoyi-admin/src/main/resources/config/apiclient_key.pem.template](mdc:ruoyi-admin/src/main/resources/config/apiclient_key.pem.template)

### 目录速览
- 后端主业务代码位于 `ruoyi-admin/src/main/java` 下的 `com/dataxai/...`
- 公共与框架层在 `ruoyi-common`、`ruoyi-framework`、`ruoyi-system` 等模块中
- 生成器与定时任务在 `ruoyi-generator`、`ruoyi-quartz`
- 前端管理端位于 `ruoyi-ui/admin`，支持一键脚本运行

