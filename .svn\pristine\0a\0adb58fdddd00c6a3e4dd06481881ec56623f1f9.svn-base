"use strict";(self.webpackChunkai_console=self.webpackChunkai_console||[]).push([[470],{35470:function(e,t,n){n.d(t,{A:function(){return pt}});var o=n(94423),r=n(27569),i=n(9939),a=n(76787),c=n.n(a),l=n(48524),u=n(45726),s=n(68887),d=n(48281),f=n(62656),p=n(8076),v=n(17909),m=n(81718),g=n(69923),h=n(92604),b=function(e){var t=e.className,n=e.customizeIcon,o=e.customizeIconProps,r=e.children,a=e.onMouseDown,l=e.onClick,u="function"===typeof n?n(o):n;return i.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null===a||void 0===a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:l,"aria-hidden":!0},void 0!==u?u:i.createElement("span",{className:c()(t.split(/\s+/).map((function(e){return"".concat(e,"-icon")})))},r))},A=i.createContext(null);function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,t=i.useRef(null),n=i.useRef(null);return i.useEffect((function(){return function(){window.clearTimeout(n.current)}}),[]),[function(){return t.current},function(o){(o||null===t.current)&&(t.current=o),window.clearTimeout(n.current),n.current=window.setTimeout((function(){t.current=null}),e)}]}var y=n(99371);var S=n(71306),C=n(23713),E=function(e,t){var n,o=e.prefixCls,r=e.id,a=e.inputElement,l=e.disabled,u=e.tabIndex,d=e.autoFocus,f=e.autoComplete,p=e.editable,m=e.activeDescendantId,g=e.value,b=e.maxLength,A=e.onKeyDown,w=e.onMouseDown,y=e.onChange,S=e.onPaste,C=e.onCompositionStart,E=e.onCompositionEnd,x=e.onBlur,I=e.open,M=e.attrs,O=a||i.createElement("input",null),R=O,H=R.ref,z=R.props,N=z.onKeyDown,D=z.onChange,P=z.onMouseDown,T=z.onCompositionStart,B=z.onCompositionEnd,k=z.onBlur,L=z.style;return(0,v.$e)(!("maxLength"in O.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),O=i.cloneElement(O,(0,s.A)((0,s.A)((0,s.A)({type:"search"},z),{},{id:r,ref:(0,h.K4)(t,H),disabled:l,tabIndex:u,autoComplete:f||"off",autoFocus:d,className:c()("".concat(o,"-selection-search-input"),null===(n=O)||void 0===n||null===(n=n.props)||void 0===n?void 0:n.className),role:"combobox","aria-expanded":I||!1,"aria-haspopup":"listbox","aria-owns":"".concat(r,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(r,"_list"),"aria-activedescendant":I?m:void 0},M),{},{value:p?g:"",maxLength:b,readOnly:!p,unselectable:p?null:"on",style:(0,s.A)((0,s.A)({},L),{},{opacity:p?null:0}),onKeyDown:function(e){A(e),N&&N(e)},onMouseDown:function(e){w(e),P&&P(e)},onChange:function(e){y(e),D&&D(e)},onCompositionStart:function(e){C(e),T&&T(e)},onCompositionEnd:function(e){E(e),B&&B(e)},onPaste:S,onBlur:function(e){x(e),k&&k(e)}}))};var x=i.forwardRef(E);function I(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var M="undefined"!==typeof window&&window.document&&window.document.documentElement;function O(e){return["string","number"].includes((0,f.A)(e))}function R(e){var t=void 0;return e&&(O(e.title)?t=e.title.toString():O(e.label)&&(t=e.label.toString())),t}function H(e){var t;return null!==(t=e.key)&&void 0!==t?t:e.value}var z=function(e){e.preventDefault(),e.stopPropagation()},N=function(e){var t,n,a=e.id,l=e.prefixCls,u=e.values,s=e.open,d=e.searchValue,f=e.autoClearSearchValue,p=e.inputRef,v=e.placeholder,m=e.disabled,g=e.mode,h=e.showSearch,A=e.autoFocus,w=e.autoComplete,y=e.activeDescendantId,E=e.tabIndex,I=e.removeIcon,O=e.maxTagCount,N=e.maxTagTextLength,D=e.maxTagPlaceholder,P=void 0===D?function(e){return"+ ".concat(e.length," ...")}:D,T=e.tagRender,B=e.onToggleOpen,k=e.onRemove,L=e.onInputChange,j=e.onInputPaste,F=e.onInputKeyDown,W=e.onInputMouseDown,_=e.onInputCompositionStart,V=e.onInputCompositionEnd,X=e.onInputBlur,K=i.useRef(null),Y=(0,i.useState)(0),G=(0,r.A)(Y,2),q=G[0],U=G[1],Q=(0,i.useState)(!1),$=(0,r.A)(Q,2),J=$[0],Z=$[1],ee="".concat(l,"-selection"),te=s||"multiple"===g&&!1===f||"tags"===g?d:"",ne="tags"===g||"multiple"===g&&!1===f||h&&(s||J);t=function(){U(K.current.scrollWidth)},n=[te],M?i.useLayoutEffect(t,n):i.useEffect(t,n);var oe=function(e,t,n,r,a){return i.createElement("span",{title:R(e),className:c()("".concat(ee,"-item"),(0,o.A)({},"".concat(ee,"-item-disabled"),n))},i.createElement("span",{className:"".concat(ee,"-item-content")},t),r&&i.createElement(b,{className:"".concat(ee,"-item-remove"),onMouseDown:z,onClick:a,customizeIcon:I},"\xd7"))},re=function(e,t,n,o,r,a){return i.createElement("span",{onMouseDown:function(e){z(e),B(!s)}},T({label:t,value:e,disabled:n,closable:o,onClose:r,isMaxTag:!!a}))},ie=i.createElement("div",{className:"".concat(ee,"-search"),style:{width:q},onFocus:function(){Z(!0)},onBlur:function(){Z(!1)}},i.createElement(x,{ref:p,open:s,prefixCls:l,id:a,inputElement:null,disabled:m,autoFocus:A,autoComplete:w,editable:ne,activeDescendantId:y,value:te,onKeyDown:F,onMouseDown:W,onChange:L,onPaste:j,onCompositionStart:_,onCompositionEnd:V,onBlur:X,tabIndex:E,attrs:(0,S.A)(e,!0)}),i.createElement("span",{ref:K,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},te,"\xa0")),ae=i.createElement(C.A,{prefixCls:"".concat(ee,"-overflow"),data:u,renderItem:function(e){var t=e.disabled,n=e.label,o=e.value,r=!m&&!t,i=n;if("number"===typeof N&&("string"===typeof n||"number"===typeof n)){var a=String(i);a.length>N&&(i="".concat(a.slice(0,N),"..."))}var c=function(t){t&&t.stopPropagation(),k(e)};return"function"===typeof T?re(o,i,t,r,c):oe(e,i,t,r,c)},renderRest:function(e){if(!u.length)return null;var t="function"===typeof P?P(e):P;return"function"===typeof T?re(void 0,t,!1,!1,void 0,!0):oe({title:t},t,!1)},suffix:ie,itemKey:H,maxCount:O});return i.createElement("span",{className:"".concat(ee,"-wrap")},ae,!u.length&&!te&&i.createElement("span",{className:"".concat(ee,"-placeholder")},v))},D=function(e){var t=e.inputElement,n=e.prefixCls,o=e.id,a=e.inputRef,c=e.disabled,l=e.autoFocus,u=e.autoComplete,s=e.activeDescendantId,d=e.mode,f=e.open,p=e.values,v=e.placeholder,m=e.tabIndex,g=e.showSearch,h=e.searchValue,b=e.activeValue,A=e.maxLength,w=e.onInputKeyDown,y=e.onInputMouseDown,C=e.onInputChange,E=e.onInputPaste,I=e.onInputCompositionStart,M=e.onInputCompositionEnd,O=e.onInputBlur,H=e.title,z=i.useState(!1),N=(0,r.A)(z,2),D=N[0],P=N[1],T="combobox"===d,B=T||g,k=p[0],L=h||"";T&&b&&!D&&(L=b),i.useEffect((function(){T&&P(!1)}),[T,b]);var j=!("combobox"!==d&&!f&&!g)&&!!L,F=void 0===H?R(k):H,W=i.useMemo((function(){return k?null:i.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:j?{visibility:"hidden"}:void 0},v)}),[k,j,v,n]);return i.createElement("span",{className:"".concat(n,"-selection-wrap")},i.createElement("span",{className:"".concat(n,"-selection-search")},i.createElement(x,{ref:a,prefixCls:n,id:o,open:f,inputElement:t,disabled:c,autoFocus:l,autoComplete:u,editable:B,activeDescendantId:s,value:L,onKeyDown:w,onMouseDown:y,onChange:function(e){P(!0),C(e)},onPaste:E,onCompositionStart:I,onCompositionEnd:M,onBlur:O,tabIndex:m,attrs:(0,S.A)(e,!0),maxLength:T?A:void 0})),!T&&k?i.createElement("span",{className:"".concat(n,"-selection-item"),title:F,style:j?{visibility:"hidden"}:void 0},k.label):null,W)},P=function(e,t){var n=(0,i.useRef)(null),o=(0,i.useRef)(!1),a=e.prefixCls,c=e.open,u=e.mode,s=e.showSearch,d=e.tokenWithEnter,f=e.disabled,p=e.prefix,v=e.autoClearSearchValue,m=e.onSearch,g=e.onSearchSubmit,h=e.onToggleOpen,b=e.onInputKeyDown,A=e.onInputBlur,S=e.domRef;i.useImperativeHandle(t,(function(){return{focus:function(e){n.current.focus(e)},blur:function(){n.current.blur()}}}));var C=w(0),E=(0,r.A)(C,2),x=E[0],I=E[1],M=(0,i.useRef)(null),O=function(e){!1!==m(e,!0,o.current)&&h(!0)},R={inputRef:n,onInputKeyDown:function(e){var t,r=e.which,i=n.current instanceof HTMLTextAreaElement;(i||!c||r!==y.A.UP&&r!==y.A.DOWN||e.preventDefault(),b&&b(e),r!==y.A.ENTER||"tags"!==u||o.current||c||null===g||void 0===g||g(e.target.value),i&&!c&&~[y.A.UP,y.A.DOWN,y.A.LEFT,y.A.RIGHT].indexOf(r))||(t=r)&&![y.A.ESC,y.A.SHIFT,y.A.BACKSPACE,y.A.TAB,y.A.WIN_KEY,y.A.ALT,y.A.META,y.A.WIN_KEY_RIGHT,y.A.CTRL,y.A.SEMICOLON,y.A.EQUALS,y.A.CAPS_LOCK,y.A.CONTEXT_MENU,y.A.F1,y.A.F2,y.A.F3,y.A.F4,y.A.F5,y.A.F6,y.A.F7,y.A.F8,y.A.F9,y.A.F10,y.A.F11,y.A.F12].includes(t)&&h(!0)},onInputMouseDown:function(){I(!0)},onInputChange:function(e){var t=e.target.value;if(d&&M.current&&/[\r\n]/.test(M.current)){var n=M.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");t=t.replace(n,M.current)}M.current=null,O(t)},onInputPaste:function(e){var t=e.clipboardData,n=null===t||void 0===t?void 0:t.getData("text");M.current=n||""},onInputCompositionStart:function(){o.current=!0},onInputCompositionEnd:function(e){o.current=!1,"combobox"!==u&&O(e.target.value)},onInputBlur:A},H="multiple"===u||"tags"===u?i.createElement(N,(0,l.A)({},e,R)):i.createElement(D,(0,l.A)({},e,R));return i.createElement("div",{ref:S,className:"".concat(a,"-selector"),onClick:function(e){e.target!==n.current&&(void 0!==document.body.style.msTouchAction?setTimeout((function(){n.current.focus()})):n.current.focus())},onMouseDown:function(e){var t=x();e.target===n.current||t||"combobox"===u&&f||e.preventDefault(),("combobox"===u||s&&t)&&c||(c&&!1!==v&&m("",!0,!1),h())}},p&&i.createElement("div",{className:"".concat(a,"-prefix")},p),H)};var T=i.forwardRef(P),B=n(13118),k=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],L=function(e,t){var n=e.prefixCls,r=(e.disabled,e.visible),a=e.children,u=e.popupElement,f=e.animation,p=e.transitionName,v=e.dropdownStyle,m=e.dropdownClassName,g=e.direction,h=void 0===g?"ltr":g,b=e.placement,A=e.builtinPlacements,w=e.dropdownMatchSelectWidth,y=e.dropdownRender,S=e.dropdownAlign,C=e.getPopupContainer,E=e.empty,x=e.getTriggerDOMNode,I=e.onPopupVisibleChange,M=e.onPopupMouseEnter,O=(0,d.A)(e,k),R="".concat(n,"-dropdown"),H=u;y&&(H=y(u));var z=i.useMemo((function(){return A||function(e){var t=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1},htmlRegion:"scroll"}}}(w)}),[A,w]),N=f?"".concat(R,"-").concat(f):p,D="number"===typeof w,P=i.useMemo((function(){return D?null:!1===w?"minWidth":"width"}),[w,D]),T=v;D&&(T=(0,s.A)((0,s.A)({},T),{},{width:w}));var L=i.useRef(null);return i.useImperativeHandle(t,(function(){return{getPopupElement:function(){var e;return null===(e=L.current)||void 0===e?void 0:e.popupElement}}})),i.createElement(B.A,(0,l.A)({},O,{showAction:I?["click"]:[],hideAction:I?["click"]:[],popupPlacement:b||("rtl"===h?"bottomRight":"bottomLeft"),builtinPlacements:z,prefixCls:R,popupTransitionName:N,popup:i.createElement("div",{onMouseEnter:M},H),ref:L,stretch:P,popupAlign:S,popupVisible:r,getPopupContainer:C,popupClassName:c()(m,(0,o.A)({},"".concat(R,"-empty"),E)),popupStyle:T,getTriggerDOMNode:x,onPopupVisibleChange:I}),a)};var j=i.forwardRef(L),F=n(33115);function W(e,t){var n,o=e.key;return"value"in e&&(n=e.value),null!==o&&void 0!==o?o:void 0!==n?n:"rc-index-key-".concat(t)}function _(e){return"undefined"!==typeof e&&!Number.isNaN(e)}function V(e,t){var n=e||{},o=n.label||(t?"children":"label");return{label:o,value:n.value||"value",options:n.options||"options",groupLabel:n.groupLabel||o}}function X(e){var t=(0,s.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,v.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var K=i.createContext(null);function Y(e){var t=e.visible,n=e.values;if(!t)return null;return i.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,50).map((function(e){var t=e.label,n=e.value;return["number","string"].includes((0,f.A)(t))?t:n})).join(", ")),n.length>50?", ...":null)}var G=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],q=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],U=function(e){return"tags"===e||"multiple"===e},Q=i.forwardRef((function(e,t){var n,a=e.id,v=e.prefixCls,y=e.className,S=e.showSearch,C=e.tagRender,E=e.direction,x=e.omitDomProps,I=e.displayValues,M=e.onDisplayValuesChange,O=e.emptyOptions,R=e.notFoundContent,H=void 0===R?"Not Found":R,z=e.onClear,N=e.mode,D=e.disabled,P=e.loading,B=e.getInputElement,k=e.getRawInputElement,L=e.open,W=e.defaultOpen,V=e.onDropdownVisibleChange,X=e.activeValue,Q=e.onActiveValueChange,$=e.activeDescendantId,J=e.searchValue,Z=e.autoClearSearchValue,ee=e.onSearch,te=e.onSearchSplit,ne=e.tokenSeparators,oe=e.allowClear,re=e.prefix,ie=e.suffixIcon,ae=e.clearIcon,ce=e.OptionList,le=e.animation,ue=e.transitionName,se=e.dropdownStyle,de=e.dropdownClassName,fe=e.dropdownMatchSelectWidth,pe=e.dropdownRender,ve=e.dropdownAlign,me=e.placement,ge=e.builtinPlacements,he=e.getPopupContainer,be=e.showAction,Ae=void 0===be?[]:be,we=e.onFocus,ye=e.onBlur,Se=e.onKeyUp,Ce=e.onKeyDown,Ee=e.onMouseDown,xe=(0,d.A)(e,G),Ie=U(N),Me=(void 0!==S?S:Ie)||"combobox"===N,Oe=(0,s.A)({},xe);q.forEach((function(e){delete Oe[e]})),null===x||void 0===x||x.forEach((function(e){delete Oe[e]}));var Re=i.useState(!1),He=(0,r.A)(Re,2),ze=He[0],Ne=He[1];i.useEffect((function(){Ne((0,g.A)())}),[]);var De=i.useRef(null),Pe=i.useRef(null),Te=i.useRef(null),Be=i.useRef(null),ke=i.useRef(null),Le=i.useRef(!1),je=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=i.useState(!1),n=(0,r.A)(t,2),o=n[0],a=n[1],c=i.useRef(null),l=function(){window.clearTimeout(c.current)};return i.useEffect((function(){return l}),[]),[o,function(t,n){l(),c.current=window.setTimeout((function(){a(t),n&&n()}),e)},l]}(),Fe=(0,r.A)(je,3),We=Fe[0],_e=Fe[1],Ve=Fe[2];i.useImperativeHandle(t,(function(){var e,t;return{focus:null===(e=Be.current)||void 0===e?void 0:e.focus,blur:null===(t=Be.current)||void 0===t?void 0:t.blur,scrollTo:function(e){var t;return null===(t=ke.current)||void 0===t?void 0:t.scrollTo(e)},nativeElement:De.current||Pe.current}}));var Xe=i.useMemo((function(){var e;if("combobox"!==N)return J;var t=null===(e=I[0])||void 0===e?void 0:e.value;return"string"===typeof t||"number"===typeof t?String(t):""}),[J,N,I]),Ke="combobox"===N&&"function"===typeof B&&B()||null,Ye="function"===typeof k&&k(),Ge=(0,h.xK)(Pe,null===Ye||void 0===Ye||null===(n=Ye.props)||void 0===n?void 0:n.ref),qe=i.useState(!1),Ue=(0,r.A)(qe,2),Qe=Ue[0],$e=Ue[1];(0,m.A)((function(){$e(!0)}),[]);var Je=(0,p.A)(!1,{defaultValue:W,value:L}),Ze=(0,r.A)(Je,2),et=Ze[0],tt=Ze[1],nt=!!Qe&&et,ot=!H&&O;(D||ot&&nt&&"combobox"===N)&&(nt=!1);var rt=!ot&&nt,it=i.useCallback((function(e){var t=void 0!==e?e:!nt;D||(tt(t),nt!==t&&(null===V||void 0===V||V(t)))}),[D,nt,tt,V]),at=i.useMemo((function(){return(ne||[]).some((function(e){return["\n","\r\n"].includes(e)}))}),[ne]),ct=i.useContext(K)||{},lt=ct.maxCount,ut=ct.rawValues,st=function(e,t,n){if(!(Ie&&_(lt)&&(null===ut||void 0===ut?void 0:ut.size)>=lt)){var o=!0,r=e;null===Q||void 0===Q||Q(null);var i=function(e,t,n){if(!t||!t.length)return null;var o=!1,r=function e(t,n){var r=(0,F.A)(n),i=r[0],a=r.slice(1);if(!i)return[t];var c=t.split(i);return o=o||c.length>1,c.reduce((function(t,n){return[].concat((0,u.A)(t),(0,u.A)(e(n,a)))}),[]).filter(Boolean)}(e,t);return o?"undefined"!==typeof n?r.slice(0,n):r:null}(e,ne,_(lt)?lt-ut.size:void 0),a=n?null:i;return"combobox"!==N&&a&&(r="",null===te||void 0===te||te(a),it(!1),o=!1),ee&&Xe!==r&&ee(r,{source:t?"typing":"effect"}),o}};i.useEffect((function(){nt||Ie||"combobox"===N||st("",!1,!1)}),[nt]),i.useEffect((function(){et&&D&&tt(!1),D&&!Le.current&&_e(!1)}),[D]);var dt=w(),ft=(0,r.A)(dt,2),pt=ft[0],vt=ft[1],mt=i.useRef(!1),gt=i.useRef(!1),ht=[];i.useEffect((function(){return function(){ht.forEach((function(e){return clearTimeout(e)})),ht.splice(0,ht.length)}}),[]);var bt,At=i.useState({}),wt=(0,r.A)(At,2)[1];Ye&&(bt=function(e){it(e)}),function(e,t,n,o){var r=i.useRef(null);r.current={open:t,triggerOpen:n,customizedTrigger:o},i.useEffect((function(){function t(t){var n;if(null===(n=r.current)||void 0===n||!n.customizedTrigger){var o=t.target;o.shadowRoot&&t.composed&&(o=t.composedPath()[0]||o),r.current.open&&e().filter((function(e){return e})).every((function(e){return!e.contains(o)&&e!==o}))&&r.current.triggerOpen(!1)}}return window.addEventListener("mousedown",t),function(){return window.removeEventListener("mousedown",t)}}),[])}((function(){var e;return[De.current,null===(e=Te.current)||void 0===e?void 0:e.getPopupElement()]}),rt,it,!!Ye);var yt,St=i.useMemo((function(){return(0,s.A)((0,s.A)({},e),{},{notFoundContent:H,open:nt,triggerOpen:rt,id:a,showSearch:Me,multiple:Ie,toggleOpen:it})}),[e,H,rt,nt,a,Me,Ie,it]),Ct=!!ie||P;Ct&&(yt=i.createElement(b,{className:c()("".concat(v,"-arrow"),(0,o.A)({},"".concat(v,"-arrow-loading"),P)),customizeIcon:ie,customizeIconProps:{loading:P,searchValue:Xe,open:nt,focused:We,showSearch:Me}}));var Et,xt=function(e,t,n,o,r){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],c=arguments.length>6?arguments[6]:void 0,l=arguments.length>7?arguments[7]:void 0,u=i.useMemo((function(){return"object"===(0,f.A)(o)?o.clearIcon:r||void 0}),[o,r]);return{allowClear:i.useMemo((function(){return!(a||!o||!n.length&&!c||"combobox"===l&&""===c)}),[o,a,n.length,c,l]),clearIcon:i.createElement(b,{className:"".concat(e,"-clear"),onMouseDown:t,customizeIcon:u},"\xd7")}}(v,(function(){var e;null===z||void 0===z||z(),null===(e=Be.current)||void 0===e||e.focus(),M([],{type:"clear",values:I}),st("",!1,!1)}),I,oe,ae,D,Xe,N),It=xt.allowClear,Mt=xt.clearIcon,Ot=i.createElement(ce,{ref:ke}),Rt=c()(v,y,(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(v,"-focused"),We),"".concat(v,"-multiple"),Ie),"".concat(v,"-single"),!Ie),"".concat(v,"-allow-clear"),oe),"".concat(v,"-show-arrow"),Ct),"".concat(v,"-disabled"),D),"".concat(v,"-loading"),P),"".concat(v,"-open"),nt),"".concat(v,"-customize-input"),Ke),"".concat(v,"-show-search"),Me)),Ht=i.createElement(j,{ref:Te,disabled:D,prefixCls:v,visible:rt,popupElement:Ot,animation:le,transitionName:ue,dropdownStyle:se,dropdownClassName:de,direction:E,dropdownMatchSelectWidth:fe,dropdownRender:pe,dropdownAlign:ve,placement:me,builtinPlacements:ge,getPopupContainer:he,empty:O,getTriggerDOMNode:function(e){return Pe.current||e},onPopupVisibleChange:bt,onPopupMouseEnter:function(){wt({})}},Ye?i.cloneElement(Ye,{ref:Ge}):i.createElement(T,(0,l.A)({},e,{domRef:Pe,prefixCls:v,inputElement:Ke,ref:Be,id:a,prefix:re,showSearch:Me,autoClearSearchValue:Z,mode:N,activeDescendantId:$,tagRender:C,values:I,open:nt,onToggleOpen:it,activeValue:X,searchValue:Xe,onSearch:st,onSearchSubmit:function(e){e&&e.trim()&&ee(e,{source:"submit"})},onRemove:function(e){var t=I.filter((function(t){return t!==e}));M(t,{type:"remove",values:[e]})},tokenWithEnter:at,onInputBlur:function(){mt.current=!1}})));return Et=Ye?Ht:i.createElement("div",(0,l.A)({className:Rt},Oe,{ref:De,onMouseDown:function(e){var t,n=e.target,o=null===(t=Te.current)||void 0===t?void 0:t.getPopupElement();if(o&&o.contains(n)){var r=setTimeout((function(){var e,t=ht.indexOf(r);-1!==t&&ht.splice(t,1),Ve(),ze||o.contains(document.activeElement)||null===(e=Be.current)||void 0===e||e.focus()}));ht.push(r)}for(var i=arguments.length,a=new Array(i>1?i-1:0),c=1;c<i;c++)a[c-1]=arguments[c];null===Ee||void 0===Ee||Ee.apply(void 0,[e].concat(a))},onKeyDown:function(e){var t,n=pt(),o=e.key,r="Enter"===o;if(r&&("combobox"!==N&&e.preventDefault(),nt||it(!0)),vt(!!Xe),"Backspace"===o&&!n&&Ie&&!Xe&&I.length){for(var i=(0,u.A)(I),a=null,c=i.length-1;c>=0;c-=1){var l=i[c];if(!l.disabled){i.splice(c,1),a=l;break}}a&&M(i,{type:"remove",values:[a]})}for(var s=arguments.length,d=new Array(s>1?s-1:0),f=1;f<s;f++)d[f-1]=arguments[f];!nt||r&&mt.current||(r&&(mt.current=!0),null===(t=ke.current)||void 0===t||t.onKeyDown.apply(t,[e].concat(d))),null===Ce||void 0===Ce||Ce.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];var r;nt&&(null===(r=ke.current)||void 0===r||r.onKeyUp.apply(r,[e].concat(n))),"Enter"===e.key&&(mt.current=!1),null===Se||void 0===Se||Se.apply(void 0,[e].concat(n))},onFocus:function(){_e(!0),D||(we&&!gt.current&&we.apply(void 0,arguments),Ae.includes("focus")&&it(!0)),gt.current=!0},onBlur:function(){Le.current=!0,_e(!1,(function(){gt.current=!1,Le.current=!1,it(!1)})),D||(Xe&&("tags"===N?ee(Xe,{source:"submit"}):"multiple"===N&&ee("",{source:"blur"})),ye&&ye.apply(void 0,arguments))}}),i.createElement(Y,{visible:We&&!nt,values:I}),Ht,yt,It&&Mt),i.createElement(A.Provider,{value:St},Et)}));var $=Q,J=function(){return null};J.isSelectOptGroup=!0;var Z=J,ee=function(){return null};ee.isSelectOption=!0;var te=ee,ne=n(45591),oe=n(42292),re=n(47238);var ie=["disabled","title","children","style","className"];function ae(e){return"string"===typeof e||"number"===typeof e}var ce=function(e,t){var n=i.useContext(A),a=n.prefixCls,s=n.id,f=n.open,p=n.multiple,v=n.mode,m=n.searchValue,g=n.toggleOpen,h=n.notFoundContent,w=n.onPopupScroll,C=i.useContext(K),E=C.maxCount,x=C.flattenOptions,I=C.onActiveValue,M=C.defaultActiveFirstOption,O=C.onSelect,R=C.menuItemSelectedIcon,H=C.rawValues,z=C.fieldNames,N=C.virtual,D=C.direction,P=C.listHeight,T=C.listItemHeight,B=C.optionRender,k="".concat(a,"-item"),L=(0,ne.A)((function(){return x}),[f,x],(function(e,t){return t[0]&&e[1]!==t[1]})),j=i.useRef(null),F=i.useMemo((function(){return p&&_(E)&&(null===H||void 0===H?void 0:H.size)>=E}),[p,E,null===H||void 0===H?void 0:H.size]),W=function(e){e.preventDefault()},V=function(e){var t;null===(t=j.current)||void 0===t||t.scrollTo("number"===typeof e?{index:e}:e)},X=i.useCallback((function(e){return"combobox"!==v&&H.has(e)}),[v,(0,u.A)(H).toString(),H.size]),Y=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=L.length,o=0;o<n;o+=1){var r=(e+o*t+n)%n,i=L[r]||{},a=i.group,c=i.data;if(!a&&(null===c||void 0===c||!c.disabled)&&(X(c.value)||!F))return r}return-1},G=i.useState((function(){return Y(0)})),q=(0,r.A)(G,2),U=q[0],Q=q[1],$=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Q(e);var n={source:t?"keyboard":"mouse"},o=L[e];o?I(o.value,e,n):I(null,-1,n)};(0,i.useEffect)((function(){$(!1!==M?Y(0):-1)}),[L.length,m]);var J=i.useCallback((function(e){return"combobox"===v?String(e).toLowerCase()===m.toLowerCase():H.has(e)}),[v,m,(0,u.A)(H).toString(),H.size]);(0,i.useEffect)((function(){var e,t=setTimeout((function(){if(!p&&f&&1===H.size){var e=Array.from(H)[0],t=L.findIndex((function(t){return t.data.value===e}));-1!==t&&($(t),V(t))}}));f&&(null===(e=j.current)||void 0===e||e.scrollTo(void 0));return function(){return clearTimeout(t)}}),[f,m]);var Z=function(e){void 0!==e&&O(e,{selected:!H.has(e)}),p||g(!1)};if(i.useImperativeHandle(t,(function(){return{onKeyDown:function(e){var t=e.which,n=e.ctrlKey;switch(t){case y.A.N:case y.A.P:case y.A.UP:case y.A.DOWN:var o=0;if(t===y.A.UP?o=-1:t===y.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&n&&(t===y.A.N?o=1:t===y.A.P&&(o=-1)),0!==o){var r=Y(U+o,o);V(r),$(r,!0)}break;case y.A.TAB:case y.A.ENTER:var i,a=L[U];!a||null!==a&&void 0!==a&&null!==(i=a.data)&&void 0!==i&&i.disabled||F?Z(void 0):Z(a.value),f&&e.preventDefault();break;case y.A.ESC:g(!1),f&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){V(e)}}})),0===L.length)return i.createElement("div",{role:"listbox",id:"".concat(s,"_list"),className:"".concat(k,"-empty"),onMouseDown:W},h);var ee=Object.keys(z).map((function(e){return z[e]})),te=function(e){return e.label};function ce(e,t){return{role:e.group?"presentation":"option",id:"".concat(s,"_list_").concat(t)}}var le=function(e){var t=L[e];if(!t)return null;var n=t.data||{},o=n.value,r=t.group,a=(0,S.A)(n,!0),c=te(t);return t?i.createElement("div",(0,l.A)({"aria-label":"string"!==typeof c||r?null:c},a,{key:e},ce(t,e),{"aria-selected":J(o)}),o):null},ue={role:"listbox",id:"".concat(s,"_list")};return i.createElement(i.Fragment,null,N&&i.createElement("div",(0,l.A)({},ue,{style:{height:0,width:0,overflow:"hidden"}}),le(U-1),le(U),le(U+1)),i.createElement(re.A,{itemKey:"key",ref:j,data:L,height:P,itemHeight:T,fullHeight:!1,onMouseDown:W,onScroll:w,virtual:N,direction:D,innerProps:N?null:ue},(function(e,t){var n=e.group,r=e.groupOption,a=e.data,u=e.label,s=e.value,f=a.key;if(n){var p,v=null!==(p=a.title)&&void 0!==p?p:ae(u)?u.toString():void 0;return i.createElement("div",{className:c()(k,"".concat(k,"-group"),a.className),title:v},void 0!==u?u:f)}var m=a.disabled,g=a.title,h=(a.children,a.style),A=a.className,w=(0,d.A)(a,ie),y=(0,oe.A)(w,ee),C=X(s),E=m||!C&&F,x="".concat(k,"-option"),I=c()(k,x,A,(0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(x,"-grouped"),r),"".concat(x,"-active"),U===t&&!E),"".concat(x,"-disabled"),E),"".concat(x,"-selected"),C)),M=te(e),O=!R||"function"===typeof R||C,H="number"===typeof M?M:M||s,z=ae(H)?H.toString():void 0;return void 0!==g&&(z=g),i.createElement("div",(0,l.A)({},(0,S.A)(y),N?{}:ce(e,t),{"aria-selected":J(s),className:I,title:z,onMouseMove:function(){U===t||E||$(t)},onClick:function(){E||Z(s)},style:h}),i.createElement("div",{className:"".concat(x,"-content")},"function"===typeof B?B(e,{index:t}):H),i.isValidElement(R)||C,O&&i.createElement(b,{className:"".concat(k,"-option-state"),customizeIcon:R,customizeIconProps:{value:s,disabled:E,isSelected:C}},C?"\u2713":null))})))};var le=i.forwardRef(ce);function ue(e,t){return I(e).join("").toUpperCase().includes(t)}var se=n(67337),de=0,fe=(0,se.A)();function pe(e){var t=i.useState(),n=(0,r.A)(t,2),o=n[0],a=n[1];return i.useEffect((function(){a("rc_select_".concat(function(){var e;return fe?(e=de,de+=1):e="TEST_OR_SSR",e}()))}),[]),e||o}var ve=n(36891),me=["children","value"],ge=["children"];function he(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,ve.A)(e).map((function(e,n){if(!i.isValidElement(e)||!e.type)return null;var o=e,r=o.type.isSelectOptGroup,a=o.key,c=o.props,l=c.children,u=(0,d.A)(c,ge);return t||!r?function(e){var t=e,n=t.key,o=t.props,r=o.children,i=o.value,a=(0,d.A)(o,me);return(0,s.A)({key:n,value:void 0!==i?i:n,children:r},a)}(e):(0,s.A)((0,s.A)({key:"__RC_SELECT_GRP__".concat(null===a?n:a,"__"),label:a},u),{},{options:he(l)})})).filter((function(e){return e}))}var be=function(e,t,n,o,r){return i.useMemo((function(){var i=e;!e&&(i=he(t));var a=new Map,c=new Map,l=function(e,t,n){n&&"string"===typeof n&&e.set(t[n],t)};return function e(t){for(var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],u=0;u<t.length;u+=1){var s=t[u];!s[n.options]||i?(a.set(s[n.value],s),l(c,s,n.label),l(c,s,o),l(c,s,r)):e(s[n.options],!0)}}(i),{options:i,valueOptions:a,labelOptions:c}}),[e,t,n,o,r])};function Ae(e){var t=i.useRef();t.current=e;var n=i.useCallback((function(){return t.current.apply(t,arguments)}),[]);return n}var we=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],ye=["inputValue"];var Se=i.forwardRef((function(e,t){var n=e.id,a=e.mode,c=e.prefixCls,v=void 0===c?"rc-select":c,m=e.backfill,g=e.fieldNames,h=e.inputValue,b=e.searchValue,A=e.onSearch,w=e.autoClearSearchValue,y=void 0===w||w,S=e.onSelect,C=e.onDeselect,E=e.dropdownMatchSelectWidth,x=void 0===E||E,M=e.filterOption,O=e.filterSort,R=e.optionFilterProp,H=e.optionLabelProp,z=e.options,N=e.optionRender,D=e.children,P=e.defaultActiveFirstOption,T=e.menuItemSelectedIcon,B=e.virtual,k=e.direction,L=e.listHeight,j=void 0===L?200:L,F=e.listItemHeight,_=void 0===F?20:F,Y=e.labelRender,G=e.value,q=e.defaultValue,Q=e.labelInValue,J=e.onChange,Z=e.maxCount,ee=(0,d.A)(e,we),te=pe(n),ne=U(a),oe=!(z||!D),re=i.useMemo((function(){return(void 0!==M||"combobox"!==a)&&M}),[M,a]),ie=i.useMemo((function(){return V(g,oe)}),[JSON.stringify(g),oe]),ae=(0,p.A)("",{value:void 0!==b?b:h,postState:function(e){return e||""}}),ce=(0,r.A)(ae,2),se=ce[0],de=ce[1],fe=be(z,D,ie,R,H),ve=fe.valueOptions,me=fe.labelOptions,ge=fe.options,he=i.useCallback((function(e){return I(e).map((function(e){var t,n,o,r,i,a;(function(e){return!e||"object"!==(0,f.A)(e)})(e)?t=e:(o=e.key,n=e.label,t=null!==(a=e.value)&&void 0!==a?a:o);var c,l=ve.get(t);l&&(void 0===n&&(n=null===l||void 0===l?void 0:l[H||ie.label]),void 0===o&&(o=null!==(c=null===l||void 0===l?void 0:l.key)&&void 0!==c?c:t),r=null===l||void 0===l?void 0:l.disabled,i=null===l||void 0===l?void 0:l.title);return{label:n,value:t,key:o,disabled:r,title:i}}))}),[ie,H,ve]),Se=(0,p.A)(q,{value:G}),Ce=(0,r.A)(Se,2),Ee=Ce[0],xe=Ce[1],Ie=i.useMemo((function(){var e,t=he(ne&&null===Ee?[]:Ee);return"combobox"===a&&function(e){return!e&&0!==e}(null===(e=t[0])||void 0===e?void 0:e.value)?[]:t}),[Ee,he,a,ne]),Me=function(e,t){var n=i.useRef({values:new Map,options:new Map});return[i.useMemo((function(){var o=n.current,r=o.values,i=o.options,a=e.map((function(e){var t;return void 0===e.label?(0,s.A)((0,s.A)({},e),{},{label:null===(t=r.get(e.value))||void 0===t?void 0:t.label}):e})),c=new Map,l=new Map;return a.forEach((function(e){c.set(e.value,e),l.set(e.value,t.get(e.value)||i.get(e.value))})),n.current.values=c,n.current.options=l,a}),[e,t]),i.useCallback((function(e){return t.get(e)||n.current.options.get(e)}),[t])]}(Ie,ve),Oe=(0,r.A)(Me,2),Re=Oe[0],He=Oe[1],ze=i.useMemo((function(){if(!a&&1===Re.length){var e=Re[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return Re.map((function(e){var t;return(0,s.A)((0,s.A)({},e),{},{label:null!==(t="function"===typeof Y?Y(e):e.label)&&void 0!==t?t:e.value})}))}),[a,Re,Y]),Ne=i.useMemo((function(){return new Set(Re.map((function(e){return e.value})))}),[Re]);i.useEffect((function(){if("combobox"===a){var e,t=null===(e=Re[0])||void 0===e?void 0:e.value;de(function(e){return void 0!==e&&null!==e}(t)?String(t):"")}}),[Re]);var De=Ae((function(e,t){var n=null!==t&&void 0!==t?t:e;return(0,o.A)((0,o.A)({},ie.value,e),ie.label,n)})),Pe=function(e,t,n,r,a){return i.useMemo((function(){if(!n||!1===r)return e;var i=t.options,c=t.label,l=t.value,u=[],d="function"===typeof r,f=n.toUpperCase(),p=d?r:function(e,t){return a?ue(t[a],f):t[i]?ue(t["children"!==c?c:"label"],f):ue(t[l],f)},v=d?function(e){return X(e)}:function(e){return e};return e.forEach((function(e){if(e[i])if(p(n,v(e)))u.push(e);else{var t=e[i].filter((function(e){return p(n,v(e))}));t.length&&u.push((0,s.A)((0,s.A)({},e),{},(0,o.A)({},i,t)))}else p(n,v(e))&&u.push(e)})),u}),[e,r,a,n,t])}(i.useMemo((function(){if("tags"!==a)return ge;var e=(0,u.A)(ge);return(0,u.A)(Re).sort((function(e,t){return e.value<t.value?-1:1})).forEach((function(t){var n=t.value;(function(e){return ve.has(e)})(n)||e.push(De(n,t.label))})),e}),[De,ge,ve,Re,a]),ie,se,re,R),Te=i.useMemo((function(){return"tags"!==a||!se||Pe.some((function(e){return e[R||"value"]===se}))||Pe.some((function(e){return e[ie.value]===se}))?Pe:[De(se)].concat((0,u.A)(Pe))}),[De,R,a,Pe,se,ie]),Be=function e(t){return(0,u.A)(t).sort((function(e,t){return O(e,t,{searchValue:se})})).map((function(t){return Array.isArray(t.options)?(0,s.A)((0,s.A)({},t),{},{options:t.options.length>0?e(t.options):t.options}):t}))},ke=i.useMemo((function(){return O?Be(Te):Te}),[Te,O,se]),Le=i.useMemo((function(){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.fieldNames,o=t.childrenAsData,r=[],i=V(n,!1),a=i.label,c=i.value,l=i.options,u=i.groupLabel;return function e(t,n){Array.isArray(t)&&t.forEach((function(t){if(n||!(l in t)){var i=t[c];r.push({key:W(t,r.length),groupOption:n,data:t,label:t[a],value:i})}else{var s=t[u];void 0===s&&o&&(s=t.label),r.push({key:W(t,r.length),group:!0,data:t,label:s}),e(t[l],!0)}}))}(e,!1),r}(ke,{fieldNames:ie,childrenAsData:oe})}),[ke,ie,oe]),je=function(e){var t=he(e);if(xe(t),J&&(t.length!==Re.length||t.some((function(e,t){var n;return(null===(n=Re[t])||void 0===n?void 0:n.value)!==(null===e||void 0===e?void 0:e.value)})))){var n=Q?t:t.map((function(e){return e.value})),o=t.map((function(e){return X(He(e.value))}));J(ne?n:n[0],ne?o:o[0])}},Fe=i.useState(null),We=(0,r.A)(Fe,2),_e=We[0],Ve=We[1],Xe=i.useState(0),Ke=(0,r.A)(Xe,2),Ye=Ke[0],Ge=Ke[1],qe=void 0!==P?P:"combobox"!==a,Ue=i.useCallback((function(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).source,o=void 0===n?"keyboard":n;Ge(t),m&&"combobox"===a&&null!==e&&"keyboard"===o&&Ve(String(e))}),[m,a]),Qe=function(e,t,n){var o=function(){var t,n=He(e);return[Q?{label:null===n||void 0===n?void 0:n[ie.label],value:e,key:null!==(t=null===n||void 0===n?void 0:n.key)&&void 0!==t?t:e}:e,X(n)]};if(t&&S){var i=o(),a=(0,r.A)(i,2),c=a[0],l=a[1];S(c,l)}else if(!t&&C&&"clear"!==n){var u=o(),s=(0,r.A)(u,2),d=s[0],f=s[1];C(d,f)}},$e=Ae((function(e,t){var n,o=!ne||t.selected;n=o?ne?[].concat((0,u.A)(Re),[e]):[e]:Re.filter((function(t){return t.value!==e})),je(n),Qe(e,o),"combobox"===a?Ve(""):U&&!y||(de(""),Ve(""))})),Je=i.useMemo((function(){var e=!1!==B&&!1!==x;return(0,s.A)((0,s.A)({},fe),{},{flattenOptions:Le,onActiveValue:Ue,defaultActiveFirstOption:qe,onSelect:$e,menuItemSelectedIcon:T,rawValues:Ne,fieldNames:ie,virtual:e,direction:k,listHeight:j,listItemHeight:_,childrenAsData:oe,maxCount:Z,optionRender:N})}),[Z,fe,Le,Ue,qe,$e,T,Ne,ie,B,x,k,j,_,oe,N]);return i.createElement(K.Provider,{value:Je},i.createElement($,(0,l.A)({},ee,{id:te,prefixCls:v,ref:t,omitDomProps:ye,mode:a,displayValues:ze,onDisplayValuesChange:function(e,t){je(e);var n=t.type,o=t.values;"remove"!==n&&"clear"!==n||o.forEach((function(e){Qe(e.value,!1,n)}))},direction:k,searchValue:se,onSearch:function(e,t){if(de(e),Ve(null),"submit"!==t.source)"blur"!==t.source&&("combobox"===a&&je(e),null===A||void 0===A||A(e));else{var n=(e||"").trim();if(n){var o=Array.from(new Set([].concat((0,u.A)(Ne),[n])));je(o),Qe(n,!0),de("")}}},autoClearSearchValue:y,onSearchSplit:function(e){var t=e;"tags"!==a&&(t=e.map((function(e){var t=me.get(e);return null===t||void 0===t?void 0:t.value})).filter((function(e){return void 0!==e})));var n=Array.from(new Set([].concat((0,u.A)(Ne),(0,u.A)(t))));je(n),n.forEach((function(e){Qe(e,!0)}))},dropdownMatchSelectWidth:x,OptionList:le,emptyOptions:!Le.length,activeValue:_e,activeDescendantId:"".concat(te,"_list_").concat(Ye)})))}));var Ce=Se;Ce.Option=te,Ce.OptGroup=Z;var Ee=Ce,xe=n(41539),Ie=n(55643),Me=n(8289),Oe=n(4310),Re=n(45847),He=n(98056),ze=n(6455),Ne=n(72790),De=n(18317),Pe=n(17105),Te=n(13084),Be=n(52407),ke=n(73109);var Le=function(e,t){return e||function(e){var t={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},t),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},t),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},t),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},t),{points:["br","tr"],offset:[0,-4]})}}(t)},je=n(3425),Fe=n(99830),We=n(80894),_e=n(47749),Ve=n(69897),Xe=n(58019),Ke=function(e){var t=e.optionHeight,n=e.optionFontSize,o=e.optionLineHeight;return{position:"relative",display:"block",minHeight:t,padding:e.optionPadding,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:o,boxSizing:"border-box"}},Ye=function(e){var t=e.antCls,n=e.componentCls,r="".concat(n,"-item"),i="&".concat(t,"-slide-up-enter").concat(t,"-slide-up-enter-active"),a="&".concat(t,"-slide-up-appear").concat(t,"-slide-up-appear-active"),c="&".concat(t,"-slide-up-leave").concat(t,"-slide-up-leave-active"),l="".concat(n,"-dropdown-placement-"),u="".concat(r,"-option-selected");return[(0,o.A)({},"".concat(n,"-dropdown"),Object.assign(Object.assign({},(0,je.dF)(e)),(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},"\n          ".concat(i).concat(l,"bottomLeft,\n          ").concat(a).concat(l,"bottomLeft\n        "),{animationName:Ve.ox}),"\n          ".concat(i).concat(l,"topLeft,\n          ").concat(a).concat(l,"topLeft,\n          ").concat(i).concat(l,"topRight,\n          ").concat(a).concat(l,"topRight\n        "),{animationName:Ve.nP}),"".concat(c).concat(l,"bottomLeft"),{animationName:Ve.vR}),"\n          ".concat(c).concat(l,"topLeft,\n          ").concat(c).concat(l,"topRight\n        "),{animationName:Ve.YU}),"&-hidden",{display:"none"}),r,Object.assign(Object.assign({},Ke(e)),{cursor:"pointer",transition:"background ".concat(e.motionDurationSlow," ease"),borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":(0,o.A)((0,o.A)((0,o.A)((0,o.A)({display:"flex","&-content":Object.assign({flex:"auto"},je.L9),"&-state":{flex:"none",display:"flex",alignItems:"center"}},"&-active:not(".concat(r,"-option-disabled)"),{backgroundColor:e.optionActiveBg}),"&-selected:not(".concat(r,"-option-disabled)"),(0,o.A)({color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg},"".concat(r,"-option-state"),{color:e.colorPrimary})),"&-disabled",(0,o.A)((0,o.A)((0,o.A)({},"&".concat(r,"-option-selected"),{backgroundColor:e.colorBgContainerDisabled}),"color",e.colorTextDisabled),"cursor","not-allowed")),"&-grouped",{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}),"&-empty":Object.assign(Object.assign({},Ke(e)),{color:e.colorTextDisabled})})),"".concat(u,":has(+ ").concat(u,")"),(0,o.A)({borderEndStartRadius:0,borderEndEndRadius:0},"& + ".concat(u),{borderStartStartRadius:0,borderStartEndRadius:0})),"&-rtl",{direction:"rtl"}))),(0,Ve._j)(e,"slide-up"),(0,Ve._j)(e,"slide-down"),(0,Xe.Mh)(e,"move-up"),(0,Xe.Mh)(e,"move-down")]},Ge=n(76832),qe=n(54942);function Ue(e,t){var n=e.componentCls,r=e.inputPaddingHorizontalBase,i=e.borderRadius,a=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),c=t?"".concat(n,"-").concat(t):"";return(0,o.A)({},"".concat(n,"-single").concat(c),(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({fontSize:e.fontSize,height:e.controlHeight},"".concat(n,"-selector"),Object.assign(Object.assign({},(0,je.dF)(e,!0)),(0,o.A)((0,o.A)((0,o.A)((0,o.A)({display:"flex",borderRadius:i,flex:"1 1 auto"},"".concat(n,"-selection-search"),{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}}),"\n          ".concat(n,"-selection-item,\n          ").concat(n,"-selection-placeholder\n        "),{display:"block",padding:0,lineHeight:(0,qe.zA)(a),transition:"all ".concat(e.motionDurationSlow,", visibility 0s"),alignSelf:"center"}),"".concat(n,"-selection-placeholder"),{transition:"none",pointerEvents:"none"}),["&:after","".concat(n,"-selection-item:empty:after"),"".concat(n,"-selection-placeholder:empty:after")].join(","),{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}))),"\n        &".concat(n,"-show-arrow ").concat(n,"-selection-item,\n        &").concat(n,"-show-arrow ").concat(n,"-selection-search,\n        &").concat(n,"-show-arrow ").concat(n,"-selection-placeholder\n      "),{paddingInlineEnd:e.showArrowPaddingInlineEnd}),"&".concat(n,"-open ").concat(n,"-selection-item"),{color:e.colorTextPlaceholder}),"&:not(".concat(n,"-customize-input)"),(0,o.A)({},"".concat(n,"-selector"),(0,o.A)((0,o.A)({width:"100%",height:"100%",alignItems:"center",padding:"0 ".concat((0,qe.zA)(r))},"".concat(n,"-selection-search-input"),{height:a}),"&:after",{lineHeight:(0,qe.zA)(a)}))),"&".concat(n,"-customize-input"),(0,o.A)({},"".concat(n,"-selector"),(0,o.A)((0,o.A)({"&:after":{display:"none"}},"".concat(n,"-selection-search"),{position:"static",width:"100%"}),"".concat(n,"-selection-placeholder"),{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:"0 ".concat((0,qe.zA)(r)),"&:after":{display:"none"}}))))}function Qe(e){var t=e.componentCls,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[Ue(e),Ue((0,_e.oX)(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),(0,o.A)({},"".concat(t,"-single").concat(t,"-sm"),(0,o.A)({},"&:not(".concat(t,"-customize-input)"),(0,o.A)((0,o.A)((0,o.A)({},"".concat(t,"-selector"),{padding:"0 ".concat((0,qe.zA)(n))}),"&".concat(t,"-show-arrow ").concat(t,"-selection-search"),{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()}),"\n            &".concat(t,"-show-arrow ").concat(t,"-selection-item,\n            &").concat(t,"-show-arrow ").concat(t,"-selection-placeholder\n          "),{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}))),Ue((0,_e.oX)(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}var $e=function(e,t){var n=e.componentCls,r=e.antCls,i=e.controlOutlineWidth;return(0,o.A)((0,o.A)({},"&:not(".concat(n,"-customize-input) ").concat(n,"-selector"),{border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(t.borderColor),background:e.selectorBg}),"&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(r,"-pagination-size-changer)"),(0,o.A)((0,o.A)((0,o.A)({},"&:hover ".concat(n,"-selector"),{borderColor:t.hoverBorderHover}),"".concat(n,"-focused& ").concat(n,"-selector"),{borderColor:t.activeBorderColor,boxShadow:"0 0 0 ".concat((0,qe.zA)(i)," ").concat(t.activeOutlineColor),outline:0}),"".concat(n,"-prefix"),{color:t.color}))},Je=function(e,t){return(0,o.A)({},"&".concat(e.componentCls,"-status-").concat(t.status),Object.assign({},$e(e,t)))},Ze=function(e,t){var n=e.componentCls,r=e.antCls;return(0,o.A)((0,o.A)({},"&:not(".concat(n,"-customize-input) ").concat(n,"-selector"),{background:t.bg,border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," transparent"),color:t.color}),"&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(r,"-pagination-size-changer)"),(0,o.A)((0,o.A)({},"&:hover ".concat(n,"-selector"),{background:t.hoverBg}),"".concat(n,"-focused& ").concat(n,"-selector"),{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}))},et=function(e,t){return(0,o.A)({},"&".concat(e.componentCls,"-status-").concat(t.status),Object.assign({},Ze(e,t)))},tt=function(e,t){var n=e.componentCls,r=e.antCls;return(0,o.A)((0,o.A)({},"&:not(".concat(n,"-customize-input) ").concat(n,"-selector"),{borderWidth:"0 0 ".concat((0,qe.zA)(e.lineWidth)," 0"),borderStyle:"none none ".concat(e.lineType," none"),borderColor:t.borderColor,background:e.selectorBg,borderRadius:0}),"&:not(".concat(n,"-disabled):not(").concat(n,"-customize-input):not(").concat(r,"-pagination-size-changer)"),(0,o.A)((0,o.A)((0,o.A)({},"&:hover ".concat(n,"-selector"),{borderColor:t.hoverBorderHover}),"".concat(n,"-focused& ").concat(n,"-selector"),{borderColor:t.activeBorderColor,outline:0}),"".concat(n,"-prefix"),{color:t.color}))},nt=function(e,t){return(0,o.A)({},"&".concat(e.componentCls,"-status-").concat(t.status),Object.assign({},tt(e,t)))},ot=function(e){return(0,o.A)({},e.componentCls,Object.assign(Object.assign(Object.assign(Object.assign({},function(e){return{"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},$e(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Je(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Je(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),(0,o.A)((0,o.A)({},"&".concat(e.componentCls,"-disabled"),(0,o.A)({},"&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector"),{background:e.colorBgContainerDisabled,color:e.colorTextDisabled})),"&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item"),{background:e.multipleItemBg,border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)}))}}(e)),function(e){return{"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Ze(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),et(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),et(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),(0,o.A)((0,o.A)({},"&".concat(e.componentCls,"-disabled"),(0,o.A)({},"&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector"),{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled})),"&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item"),{background:e.colorBgContainer,border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit)}))}}(e)),function(e){return{"&-borderless":(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(e.componentCls,"-selector"),{background:"transparent",border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," transparent")}),"&".concat(e.componentCls,"-disabled"),(0,o.A)({},"&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector"),{color:e.colorTextDisabled})),"&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item"),{background:e.multipleItemBg,border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)}),"&".concat(e.componentCls,"-status-error"),(0,o.A)({},"".concat(e.componentCls,"-prefix, ").concat(e.componentCls,"-selection-item"),{color:e.colorError})),"&".concat(e.componentCls,"-status-warning"),(0,o.A)({},"".concat(e.componentCls,"-prefix, ").concat(e.componentCls,"-selection-item"),{color:e.colorWarning}))}}(e)),function(e){return{"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},tt(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),nt(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),nt(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),(0,o.A)((0,o.A)({},"&".concat(e.componentCls,"-disabled"),(0,o.A)({},"&:not(".concat(e.componentCls,"-customize-input) ").concat(e.componentCls,"-selector"),{color:e.colorTextDisabled})),"&".concat(e.componentCls,"-multiple ").concat(e.componentCls,"-selection-item"),{background:e.multipleItemBg,border:"".concat((0,qe.zA)(e.lineWidth)," ").concat(e.lineType," ").concat(e.multipleItemBorderColor)}))}}(e)))},rt=function(e){var t=e.antCls,n=e.componentCls,r=e.inputPaddingHorizontalBase,i=e.iconCls;return(0,o.A)((0,o.A)({},n,Object.assign(Object.assign({},(0,je.dF)(e)),(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({position:"relative",display:"inline-flex",cursor:"pointer"},"&:not(".concat(n,"-customize-input) ").concat(n,"-selector"),Object.assign(Object.assign({},function(e){var t=e.componentCls;return(0,o.A)((0,o.A)({position:"relative",transition:"all ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut),input:{cursor:"pointer"}},"".concat(t,"-show-search&"),{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}}),"".concat(t,"-disabled&"),{cursor:"not-allowed",input:{cursor:"not-allowed"}})}(e)),function(e){var t=e.componentCls;return(0,o.A)({},"".concat(t,"-selection-search-input"),{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none","-webkit-appearance":"none"}})}(e))),"".concat(n,"-selection-item"),Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},je.L9),(0,o.A)({},"> ".concat(t,"-typography"),{display:"inline"}))),"".concat(n,"-selection-placeholder"),Object.assign(Object.assign({},je.L9),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"})),"".concat(n,"-arrow"),Object.assign(Object.assign({},(0,je.Nk)()),(0,o.A)((0,o.A)((0,o.A)({position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:r,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:"opacity ".concat(e.motionDurationSlow," ease")},i,(0,o.A)({verticalAlign:"top",transition:"transform ".concat(e.motionDurationSlow),"> svg":{verticalAlign:"top"}},"&:not(".concat(n,"-suffix)"),{pointerEvents:"auto"})),"".concat(n,"-disabled &"),{cursor:"not-allowed"}),"> *:not(:last-child)",{marginInlineEnd:8}))),"".concat(n,"-selection-wrap"),{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}}),"".concat(n,"-prefix"),{flex:"none",marginInlineEnd:e.selectAffixPadding}),"".concat(n,"-clear"),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:r,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:"color ".concat(e.motionDurationMid," ease, opacity ").concat(e.motionDurationSlow," ease"),textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}}),"&:hover ".concat(n,"-clear"),{opacity:1,background:e.colorBgBase,borderRadius:"50%"}))),"".concat(n,"-status"),{"&-error, &-warning, &-success, &-validating":(0,o.A)({},"&".concat(n,"-has-feedback"),(0,o.A)({},"".concat(n,"-clear"),{insetInlineEnd:e.calc(r).add(e.fontSize).add(e.paddingXS).equal()}))})},it=function(e){var t=e.componentCls;return[(0,o.A)({},t,(0,o.A)({},"&".concat(t,"-in-form-item"),{width:"100%"})),rt(e),Qe(e),(0,Ge.Ay)(e),Ye(e),(0,o.A)({},"".concat(t,"-rtl"),{direction:"rtl"}),(0,Fe.G)(e,{borderElCls:"".concat(t,"-selector"),focusElCls:"".concat(t,"-focused")})]},at=(0,We.OF)("Select",(function(e,t){var n=t.rootPrefixCls,o=(0,_e.oX)(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[it(o),ot(o)]}),(function(e){var t=e.fontSize,n=e.lineHeight,o=e.lineWidth,r=e.controlHeight,i=e.controlHeightSM,a=e.controlHeightLG,c=e.paddingXXS,l=e.controlPaddingHorizontal,u=e.zIndexPopupBase,s=e.colorText,d=e.fontWeightStrong,f=e.controlItemBgActive,p=e.controlItemBgHover,v=e.colorBgContainer,m=e.colorFillSecondary,g=e.colorBgContainerDisabled,h=e.colorTextDisabled,b=e.colorPrimaryHover,A=e.colorPrimary,w=e.controlOutline,y=2*c,S=2*o,C=Math.min(r-y,r-S),E=Math.min(i-y,i-S),x=Math.min(a-y,a-S);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(c/2),zIndexPopup:u+50,optionSelectedColor:s,optionSelectedFontWeight:d,optionSelectedBg:f,optionActiveBg:p,optionPadding:"".concat((r-t*n)/2,"px ").concat(l,"px"),optionFontSize:t,optionLineHeight:n,optionHeight:r,selectorBg:v,clearBg:v,singleItemHeightLG:a,multipleItemBg:m,multipleItemBorderColor:"transparent",multipleItemHeight:C,multipleItemHeightSM:E,multipleItemHeightLG:x,multipleSelectorBgDisabled:g,multipleItemColorDisabled:h,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:A,activeOutlineColor:w,selectAffixPadding:c}}),{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}}),ct=n(77233);var lt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n},ut="SECRET_COMBOBOX_MODE_DO_NOT_USE",st=function(e,t){var n,a,l=e.prefixCls,u=e.bordered,s=e.className,d=e.rootClassName,f=e.getPopupContainer,p=e.popupClassName,v=e.dropdownClassName,m=e.listHeight,g=void 0===m?256:m,h=e.placement,b=e.listItemHeight,A=e.size,w=e.disabled,y=e.notFoundContent,S=e.status,C=e.builtinPlacements,E=e.dropdownMatchSelectWidth,x=e.popupMatchSelectWidth,I=e.direction,M=e.style,O=e.allowClear,R=e.variant,H=e.dropdownStyle,z=e.transitionName,N=e.tagRender,D=e.maxCount,P=e.prefix,T=lt(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix"]),B=i.useContext(Re.QO),k=B.getPopupContainer,L=B.getPrefixCls,j=B.renderEmpty,F=B.direction,W=B.virtual,_=B.popupMatchSelectWidth,V=B.popupOverflow,X=(0,Re.TP)("select"),K=(0,ke.Ay)(),Y=(0,r.A)(K,2)[1],G=null!==b&&void 0!==b?b:null===Y||void 0===Y?void 0:Y.controlHeight,q=L("select",l),U=L(),Q=null!==I&&void 0!==I?I:F,$=(0,Be.RQ)(q,Q),J=$.compactSize,Z=$.compactItemClassnames,ee=(0,Te.A)("select",R,u),te=(0,r.A)(ee,2),ne=te[0],re=te[1],ie=(0,Ne.A)(q),ae=at(q,ie),ce=(0,r.A)(ae,3),le=ce[0],ue=ce[1],se=ce[2],de=i.useMemo((function(){var t=e.mode;if("combobox"!==t)return t===ut?"combobox":t}),[e.mode]),fe="multiple"===de||"tags"===de,pe=function(e,t){return void 0!==t?t:null!==e}(e.suffixIcon,e.showArrow),ve=null!==(n=null!==x&&void 0!==x?x:E)&&void 0!==n?n:_,me=i.useContext(Pe.$W),ge=me.status,he=me.hasFeedback,be=me.isFormItemInput,Ae=me.feedbackIcon,we=(0,Oe.v)(ge,S);a=void 0!==y?y:"combobox"===de?null:(null===j||void 0===j?void 0:j("Select"))||i.createElement(He.A,{componentName:"Select"});var ye=(0,ct.A)(Object.assign(Object.assign({},T),{multiple:fe,hasFeedback:he,feedbackIcon:Ae,showSuffixIcon:pe,prefixCls:q,componentName:"Select"})),Se=ye.suffixIcon,Ce=ye.itemIcon,Me=ye.removeIcon,je=ye.clearIcon,Fe=!0===O?{clearIcon:je}:O,We=(0,oe.A)(T,["suffixIcon","itemIcon"]),_e=c()(p||v,(0,o.A)({},"".concat(q,"-dropdown-").concat(Q),"rtl"===Q),d,se,ie,ue),Ve=(0,De.A)((function(e){var t;return null!==(t=null!==A&&void 0!==A?A:J)&&void 0!==t?t:e})),Xe=i.useContext(ze.A),Ke=null!==w&&void 0!==w?w:Xe,Ye=c()((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(q,"-lg"),"large"===Ve),"".concat(q,"-sm"),"small"===Ve),"".concat(q,"-rtl"),"rtl"===Q),"".concat(q,"-").concat(ne),re),"".concat(q,"-in-form-item"),be),(0,Oe.L)(q,we,he),Z,X.className,s,d,se,ie,ue),Ge=i.useMemo((function(){return void 0!==h?h:"rtl"===Q?"bottomRight":"bottomLeft"}),[h,Q]),qe=(0,xe.YK)("SelectLike",null===H||void 0===H?void 0:H.zIndex),Ue=(0,r.A)(qe,1)[0];return le(i.createElement(Ee,Object.assign({ref:t,virtual:W,showSearch:X.showSearch},We,{style:Object.assign(Object.assign({},X.style),M),dropdownMatchSelectWidth:ve,transitionName:(0,Ie.b)(U,"slide-up",z),builtinPlacements:Le(C,V),listHeight:g,listItemHeight:G,mode:de,prefixCls:q,placement:Ge,direction:Q,prefix:P,suffixIcon:Se,menuItemSelectedIcon:Ce,removeIcon:Me,allowClear:Fe,notFoundContent:a,className:Ye,getPopupContainer:f||k,dropdownClassName:_e,disabled:Ke,dropdownStyle:Object.assign(Object.assign({},H),{zIndex:Ue}),maxCount:fe?D:void 0,tagRender:fe?N:void 0})))};var dt=i.forwardRef(st),ft=(0,Me.A)(dt,"dropdownAlign");dt.SECRET_COMBOBOX_MODE_DO_NOT_USE=ut,dt.Option=te,dt.OptGroup=Z,dt._InternalPanelDoNotUseOrYouWillBeFired=ft;var pt=dt},47238:function(e,t,n){n.d(t,{A:function(){return j}});var o=n(48524),r=n(62656),i=n(68887),a=n(94423),c=n(27569),l=n(48281),u=n(76787),s=n.n(u),d=n(6632),f=n(68157),p=n(81718),v=n(9939),m=n(46916),g=v.forwardRef((function(e,t){var n=e.height,r=e.offsetY,c=e.offsetX,l=e.children,u=e.prefixCls,f=e.onInnerResize,p=e.innerProps,m=e.rtl,g=e.extra,h={},b={display:"flex",flexDirection:"column"};return void 0!==r&&(h={height:n,position:"relative",overflow:"hidden"},b=(0,i.A)((0,i.A)({},b),{},(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({transform:"translateY(".concat(r,"px)")},m?"marginRight":"marginLeft",-c),"position","absolute"),"left",0),"right",0),"top",0))),v.createElement("div",{style:h},v.createElement(d.A,{onResize:function(e){e.offsetHeight&&f&&f()}},v.createElement("div",(0,o.A)({style:b,className:s()((0,a.A)({},"".concat(u,"-holder-inner"),u)),ref:t},p),l,g)))}));g.displayName="Filler";var h=g;function b(e){var t=e.children,n=e.setRef,o=v.useCallback((function(e){n(e)}),[]);return v.cloneElement(t,{ref:o})}function A(e,t,n){var o=v.useState(e),r=(0,c.A)(o,2),i=r[0],a=r[1],l=v.useState(null),u=(0,c.A)(l,2),s=u[0],d=u[1];return v.useEffect((function(){var o=function(e,t,n){var o,r,i=e.length,a=t.length;if(0===i&&0===a)return null;i<a?(o=e,r=t):(o=t,r=e);var c={__EMPTY_ITEM__:!0};function l(e){return void 0!==e?n(e):c}for(var u=null,s=1!==Math.abs(i-a),d=0;d<r.length;d+=1){var f=l(o[d]);if(f!==l(r[d])){u=d,s=s||f!==l(r[d+1]);break}}return null===u?null:{index:u,multiple:s}}(i||[],e||[],t);void 0!==(null===o||void 0===o?void 0:o.index)&&(null===n||void 0===n||n(o.index),d(e[o.index])),a(e)}),[e]),[s]}var w=n(88632),y="object"===("undefined"===typeof navigator?"undefined":(0,r.A)(navigator))&&/Firefox/i.test(navigator.userAgent),S=function(e,t,n,o){var r=(0,v.useRef)(!1),i=(0,v.useRef)(null);var a=(0,v.useRef)({top:e,bottom:t,left:n,right:o});return a.current.top=e,a.current.bottom=t,a.current.left=n,a.current.right=o,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=e?t<0&&a.current.left||t>0&&a.current.right:t<0&&a.current.top||t>0&&a.current.bottom;return n&&o?(clearTimeout(i.current),r.current=!1):o&&!r.current||(clearTimeout(i.current),r.current=!0,i.current=setTimeout((function(){r.current=!1}),50)),!r.current&&o}};function C(e,t,n,o,r,i,a){var c=(0,v.useRef)(0),l=(0,v.useRef)(null),u=(0,v.useRef)(null),s=(0,v.useRef)(!1),d=S(t,n,o,r);var f=(0,v.useRef)(null),p=(0,v.useRef)(null);return[function(t){if(e){w.A.cancel(p.current),p.current=(0,w.A)((function(){f.current=null}),2);var n=t.deltaX,o=t.deltaY,r=t.shiftKey,v=n,m=o;("sx"===f.current||!f.current&&r&&o&&!n)&&(v=o,m=0,f.current="sx");var g=Math.abs(v),h=Math.abs(m);null===f.current&&(f.current=i&&g>h?"x":"y"),"y"===f.current?function(e,t){if(w.A.cancel(l.current),!d(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,c.current+=t,u.current=t,y||n.preventDefault(),l.current=(0,w.A)((function(){var e=s.current?10:1;a(c.current*e,!1),c.current=0})))}}(t,m):function(e,t){a(t,!0),y||e.preventDefault()}(t,v)}},function(t){e&&(s.current=t.detail===u.current)}]}var E=n(57827),x=n(91161),I=n(68305),M=function(){function e(){(0,x.A)(this,e),(0,a.A)(this,"maps",void 0),(0,a.A)(this,"id",0),(0,a.A)(this,"diffKeys",new Set),this.maps=Object.create(null)}return(0,I.A)(e,[{key:"set",value:function(e,t){this.maps[e]=t,this.id+=1,this.diffKeys.add(e)}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffKeys.clear()}},{key:"getRecord",value:function(){return this.diffKeys}}]),e}();function O(e){var t=parseFloat(e);return isNaN(t)?0:t}var R=14/15;function H(e){return Math.floor(Math.pow(e,.5))}function z(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var N=v.forwardRef((function(e,t){var n=e.prefixCls,o=e.rtl,r=e.scrollOffset,l=e.scrollRange,u=e.onStartMove,d=e.onStopMove,f=e.onScroll,p=e.horizontal,m=e.spinSize,g=e.containerSize,h=e.style,b=e.thumbStyle,A=e.showScrollBar,y=v.useState(!1),S=(0,c.A)(y,2),C=S[0],E=S[1],x=v.useState(null),I=(0,c.A)(x,2),M=I[0],O=I[1],R=v.useState(null),H=(0,c.A)(R,2),N=H[0],D=H[1],P=!o,T=v.useRef(),B=v.useRef(),k=v.useState(A),L=(0,c.A)(k,2),j=L[0],F=L[1],W=v.useRef(),_=function(){!0!==A&&!1!==A&&(clearTimeout(W.current),F(!0),W.current=setTimeout((function(){F(!1)}),3e3))},V=l-g||0,X=g-m||0,K=v.useMemo((function(){return 0===r||0===V?0:r/V*X}),[r,V,X]),Y=v.useRef({top:K,dragging:C,pageY:M,startTop:N});Y.current={top:K,dragging:C,pageY:M,startTop:N};var G=function(e){E(!0),O(z(e,p)),D(Y.current.top),u(),e.stopPropagation(),e.preventDefault()};v.useEffect((function(){var e=function(e){e.preventDefault()},t=T.current,n=B.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",G,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",G)}}),[]);var q=v.useRef();q.current=V;var U=v.useRef();U.current=X,v.useEffect((function(){if(C){var e,t=function(t){var n=Y.current,o=n.dragging,r=n.pageY,i=n.startTop;w.A.cancel(e);var a=T.current.getBoundingClientRect(),c=g/(p?a.width:a.height);if(o){var l=(z(t,p)-r)*c,u=i;!P&&p?u-=l:u+=l;var s=q.current,d=U.current,v=d?u/d:0,m=Math.ceil(v*s);m=Math.max(m,0),m=Math.min(m,s),e=(0,w.A)((function(){f(m,p)}))}},n=function(){E(!1),d()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),w.A.cancel(e)}}}),[C]),v.useEffect((function(){return _(),function(){clearTimeout(W.current)}}),[r]),v.useImperativeHandle(t,(function(){return{delayHidden:_}}));var Q="".concat(n,"-scrollbar"),$={position:"absolute",visibility:j?null:"hidden"},J={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return p?($.height=8,$.left=0,$.right=0,$.bottom=0,J.height="100%",J.width=m,P?J.left=K:J.right=K):($.width=8,$.top=0,$.bottom=0,P?$.right=0:$.left=0,J.width="100%",J.height=m,J.top=K),v.createElement("div",{ref:T,className:s()(Q,(0,a.A)((0,a.A)((0,a.A)({},"".concat(Q,"-horizontal"),p),"".concat(Q,"-vertical"),!p),"".concat(Q,"-visible"),j)),style:(0,i.A)((0,i.A)({},$),h),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:_},v.createElement("div",{ref:B,className:s()("".concat(Q,"-thumb"),(0,a.A)({},"".concat(Q,"-thumb-moving"),C)),style:(0,i.A)((0,i.A)({},J),b),onMouseDown:G}))}));function D(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var P=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],T=[],B={overflowY:"auto",overflowAnchor:"none"};function k(e,t){var n=e.prefixCls,u=void 0===n?"rc-virtual-list":n,g=e.className,y=e.height,x=e.itemHeight,I=e.fullHeight,k=void 0===I||I,L=e.style,j=e.data,F=e.children,W=e.itemKey,_=e.virtual,V=e.direction,X=e.scrollWidth,K=e.component,Y=void 0===K?"div":K,G=e.onScroll,q=e.onVirtualScroll,U=e.onVisibleChange,Q=e.innerProps,$=e.extraRender,J=e.styles,Z=e.showScrollBar,ee=void 0===Z?"optional":Z,te=(0,l.A)(e,P),ne=v.useCallback((function(e){return"function"===typeof W?W(e):null===e||void 0===e?void 0:e[W]}),[W]),oe=function(e,t,n){var o=v.useState(0),r=(0,c.A)(o,2),i=r[0],a=r[1],l=(0,v.useRef)(new Map),u=(0,v.useRef)(new M),s=(0,v.useRef)(0);function d(){s.current+=1}function f(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];d();var t=function(){var e=!1;l.current.forEach((function(t,n){if(t&&t.offsetParent){var o=(0,E.Ay)(t),r=o.offsetHeight,i=getComputedStyle(o),a=i.marginTop,c=i.marginBottom,l=r+O(a)+O(c);u.current.get(n)!==l&&(u.current.set(n,l),e=!0)}})),e&&a((function(e){return e+1}))};if(e)t();else{s.current+=1;var n=s.current;Promise.resolve().then((function(){n===s.current&&t()}))}}return(0,v.useEffect)((function(){return d}),[]),[function(o,r){var i=e(o),a=l.current.get(i);r?(l.current.set(i,r),f()):l.current.delete(i),!a!==!r&&(r?null===t||void 0===t||t(o):null===n||void 0===n||n(o))},f,u.current,i]}(ne,null,null),re=(0,c.A)(oe,4),ie=re[0],ae=re[1],ce=re[2],le=re[3],ue=!(!1===_||!y||!x),se=v.useMemo((function(){return Object.values(ce.maps).reduce((function(e,t){return e+t}),0)}),[ce.id,ce.maps]),de=ue&&j&&(Math.max(x*j.length,se)>y||!!X),fe="rtl"===V,pe=s()(u,(0,a.A)({},"".concat(u,"-rtl"),fe),g),ve=j||T,me=(0,v.useRef)(),ge=(0,v.useRef)(),he=(0,v.useRef)(),be=(0,v.useState)(0),Ae=(0,c.A)(be,2),we=Ae[0],ye=Ae[1],Se=(0,v.useState)(0),Ce=(0,c.A)(Se,2),Ee=Ce[0],xe=Ce[1],Ie=(0,v.useState)(!1),Me=(0,c.A)(Ie,2),Oe=Me[0],Re=Me[1],He=function(){Re(!0)},ze=function(){Re(!1)},Ne={getKey:ne};function De(e){ye((function(t){var n=function(e){var t=e;Number.isNaN(Je.current)||(t=Math.min(t,Je.current));return t=Math.max(t,0),t}("function"===typeof e?e(t):e);return me.current.scrollTop=n,n}))}var Pe=(0,v.useRef)({start:0,end:ve.length}),Te=(0,v.useRef)(),Be=A(ve,ne),ke=(0,c.A)(Be,1)[0];Te.current=ke;var Le=v.useMemo((function(){if(!ue)return{scrollHeight:void 0,start:0,end:ve.length-1,offset:void 0};var e;if(!de)return{scrollHeight:(null===(e=ge.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:ve.length-1,offset:void 0};for(var t,n,o,r=0,i=ve.length,a=0;a<i;a+=1){var c=ve[a],l=ne(c),u=ce.get(l),s=r+(void 0===u?x:u);s>=we&&void 0===t&&(t=a,n=r),s>we+y&&void 0===o&&(o=a),r=s}return void 0===t&&(t=0,n=0,o=Math.ceil(y/x)),void 0===o&&(o=ve.length-1),{scrollHeight:r,start:t,end:o=Math.min(o+1,ve.length-1),offset:n}}),[de,ue,we,ve,le,y]),je=Le.scrollHeight,Fe=Le.start,We=Le.end,_e=Le.offset;Pe.current.start=Fe,Pe.current.end=We,v.useLayoutEffect((function(){var e=ce.getRecord();if(1===e.size){var t=Array.from(e)[0],n=ve[Fe];if(n)if(ne(n)===t){var o=ce.get(t)-x;De((function(e){return e+o}))}}ce.resetRecord()}),[je]);var Ve=v.useState({width:0,height:y}),Xe=(0,c.A)(Ve,2),Ke=Xe[0],Ye=Xe[1],Ge=(0,v.useRef)(),qe=(0,v.useRef)(),Ue=v.useMemo((function(){return D(Ke.width,X)}),[Ke.width,X]),Qe=v.useMemo((function(){return D(Ke.height,je)}),[Ke.height,je]),$e=je-y,Je=(0,v.useRef)($e);Je.current=$e;var Ze=we<=0,et=we>=$e,tt=Ee<=0,nt=Ee>=X,ot=S(Ze,et,tt,nt),rt=function(){return{x:fe?-Ee:Ee,y:we}},it=(0,v.useRef)(rt()),at=(0,f._q)((function(e){if(q){var t=(0,i.A)((0,i.A)({},rt()),e);it.current.x===t.x&&it.current.y===t.y||(q(t),it.current=t)}}));function ct(e,t){var n=e;t?((0,m.flushSync)((function(){xe(n)})),at()):De(n)}var lt=function(e){var t=e,n=X?X-Ke.width:0;return t=Math.max(t,0),t=Math.min(t,n)},ut=(0,f._q)((function(e,t){t?((0,m.flushSync)((function(){xe((function(t){return lt(t+(fe?-e:e))}))})),at()):De((function(t){return t+e}))})),st=C(ue,Ze,et,tt,nt,!!X,ut),dt=(0,c.A)(st,2),ft=dt[0],pt=dt[1];!function(e,t,n){var o,r=(0,v.useRef)(!1),i=(0,v.useRef)(0),a=(0,v.useRef)(0),c=(0,v.useRef)(null),l=(0,v.useRef)(null),u=function(e){if(r.current){var t=Math.ceil(e.touches[0].pageX),o=Math.ceil(e.touches[0].pageY),c=i.current-t,u=a.current-o,s=Math.abs(c)>Math.abs(u);s?i.current=t:a.current=o;var d=n(s,s?c:u,!1,e);d&&e.preventDefault(),clearInterval(l.current),d&&(l.current=setInterval((function(){s?c*=R:u*=R;var e=Math.floor(s?c:u);(!n(s,e,!0)||Math.abs(e)<=.1)&&clearInterval(l.current)}),16))}},s=function(){r.current=!1,o()},d=function(e){o(),1!==e.touches.length||r.current||(r.current=!0,i.current=Math.ceil(e.touches[0].pageX),a.current=Math.ceil(e.touches[0].pageY),c.current=e.target,c.current.addEventListener("touchmove",u,{passive:!1}),c.current.addEventListener("touchend",s,{passive:!0}))};o=function(){c.current&&(c.current.removeEventListener("touchmove",u),c.current.removeEventListener("touchend",s))},(0,p.A)((function(){return e&&t.current.addEventListener("touchstart",d,{passive:!0}),function(){var e;null===(e=t.current)||void 0===e||e.removeEventListener("touchstart",d),o(),clearInterval(l.current)}}),[e])}(ue,me,(function(e,t,n,o){var r=o;return!ot(e,t,n)&&((!r||!r._virtualHandled)&&(r&&(r._virtualHandled=!0),ft({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0))})),function(e,t,n){v.useEffect((function(){var o=t.current;if(e&&o){var r,i,a=!1,c=function(){w.A.cancel(r)},l=function e(){c(),r=(0,w.A)((function(){n(i),e()}))},u=function(e){if(!e.target.draggable){var t=e;t._virtualHandled||(t._virtualHandled=!0,a=!0)}},s=function(){a=!1,c()},d=function(e){if(a){var t=z(e,!1),n=o.getBoundingClientRect(),r=n.top,u=n.bottom;t<=r?(i=-H(r-t),l()):t>=u?(i=H(t-u),l()):c()}};return o.addEventListener("mousedown",u),o.ownerDocument.addEventListener("mouseup",s),o.ownerDocument.addEventListener("mousemove",d),function(){o.removeEventListener("mousedown",u),o.ownerDocument.removeEventListener("mouseup",s),o.ownerDocument.removeEventListener("mousemove",d),c()}}}),[e])}(de,me,(function(e){De((function(t){return t+e}))})),(0,p.A)((function(){function e(e){var t=Ze&&e.detail<0,n=et&&e.detail>0;!ue||t||n||e.preventDefault()}var t=me.current;return t.addEventListener("wheel",ft,{passive:!1}),t.addEventListener("DOMMouseScroll",pt,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",ft),t.removeEventListener("DOMMouseScroll",pt),t.removeEventListener("MozMousePixelScroll",e)}}),[ue,Ze,et]),(0,p.A)((function(){if(X){var e=lt(Ee);xe(e),at({x:e})}}),[Ke.width,X]);var vt=function(){var e,t;null===(e=Ge.current)||void 0===e||e.delayHidden(),null===(t=qe.current)||void 0===t||t.delayHidden()},mt=function(e,t,n,o,a,l,u,s){var d=v.useRef(),f=v.useState(null),m=(0,c.A)(f,2),g=m[0],h=m[1];return(0,p.A)((function(){if(g&&g.times<10){if(!e.current)return void h((function(e){return(0,i.A)({},e)}));l();var r=g.targetAlign,c=g.originAlign,s=g.index,d=g.offset,f=e.current.clientHeight,p=!1,v=r,m=null;if(f){for(var b=r||c,A=0,w=0,y=0,S=Math.min(t.length-1,s),C=0;C<=S;C+=1){var E=a(t[C]);w=A;var x=n.get(E);A=y=w+(void 0===x?o:x)}for(var I="top"===b?d:f-d,M=S;M>=0;M-=1){var O=a(t[M]),R=n.get(O);if(void 0===R){p=!0;break}if((I-=R)<=0)break}switch(b){case"top":m=w-d;break;case"bottom":m=y-f+d;break;default:var H=e.current.scrollTop;w<H?v="top":y>H+f&&(v="bottom")}null!==m&&u(m),m!==g.lastTop&&(p=!0)}p&&h((0,i.A)((0,i.A)({},g),{},{times:g.times+1,targetAlign:v,lastTop:m}))}}),[g,e.current]),function(e){if(null!==e&&void 0!==e){if(w.A.cancel(d.current),"number"===typeof e)u(e);else if(e&&"object"===(0,r.A)(e)){var n,o=e.align;n="index"in e?e.index:t.findIndex((function(t){return a(t)===e.key}));var i=e.offset;h({times:0,index:n,offset:void 0===i?0:i,originAlign:o})}}else s()}}(me,ve,ce,x,ne,(function(){return ae(!0)}),De,vt);v.useImperativeHandle(t,(function(){return{nativeElement:he.current,getScrollInfo:rt,scrollTo:function(e){var t;(t=e)&&"object"===(0,r.A)(t)&&("left"in t||"top"in t)?(void 0!==e.left&&xe(lt(e.left)),mt(e.top)):mt(e)}}})),(0,p.A)((function(){if(U){var e=ve.slice(Fe,We+1);U(e,ve)}}),[Fe,We,ve]);var gt=function(e,t,n,o){var r=v.useMemo((function(){return[new Map,[]]}),[e,n.id,o]),i=(0,c.A)(r,2),a=i[0],l=i[1];return function(r){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r,c=a.get(r),u=a.get(i);if(void 0===c||void 0===u)for(var s=e.length,d=l.length;d<s;d+=1){var f,p=e[d],v=t(p);a.set(v,d);var m=null!==(f=n.get(v))&&void 0!==f?f:o;if(l[d]=(l[d-1]||0)+m,v===r&&(c=d),v===i&&(u=d),void 0!==c&&void 0!==u)break}return{top:l[c-1]||0,bottom:l[u]}}}(ve,ne,ce,x),ht=null===$||void 0===$?void 0:$({start:Fe,end:We,virtual:de,offsetX:Ee,offsetY:_e,rtl:fe,getSize:gt}),bt=function(e,t,n,o,r,i,a,c){var l=c.getKey;return e.slice(t,n+1).map((function(e,n){var c=a(e,t+n,{style:{width:o},offsetX:r}),u=l(e);return v.createElement(b,{key:u,setRef:function(t){return i(e,t)}},c)}))}(ve,Fe,We,X,Ee,ie,F,Ne),At=null;y&&(At=(0,i.A)((0,a.A)({},k?"height":"maxHeight",y),B),ue&&(At.overflowY="hidden",X&&(At.overflowX="hidden"),Oe&&(At.pointerEvents="none")));var wt={};return fe&&(wt.dir="rtl"),v.createElement("div",(0,o.A)({ref:he,style:(0,i.A)((0,i.A)({},L),{},{position:"relative"}),className:pe},wt,te),v.createElement(d.A,{onResize:function(e){Ye({width:e.offsetWidth,height:e.offsetHeight})}},v.createElement(Y,{className:"".concat(u,"-holder"),style:At,ref:me,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==we&&De(t),null===G||void 0===G||G(e),at()},onMouseEnter:vt},v.createElement(h,{prefixCls:u,height:je,offsetX:Ee,offsetY:_e,scrollWidth:X,onInnerResize:ae,ref:ge,innerProps:Q,rtl:fe,extra:ht},bt))),de&&je>y&&v.createElement(N,{ref:Ge,prefixCls:u,scrollOffset:we,scrollRange:je,rtl:fe,onScroll:ct,onStartMove:He,onStopMove:ze,spinSize:Qe,containerSize:Ke.height,style:null===J||void 0===J?void 0:J.verticalScrollBar,thumbStyle:null===J||void 0===J?void 0:J.verticalScrollBarThumb,showScrollBar:ee}),de&&X>Ke.width&&v.createElement(N,{ref:qe,prefixCls:u,scrollOffset:Ee,scrollRange:X,rtl:fe,onScroll:ct,onStartMove:He,onStopMove:ze,spinSize:Ue,containerSize:Ke.width,horizontal:!0,style:null===J||void 0===J?void 0:J.horizontalScrollBar,thumbStyle:null===J||void 0===J?void 0:J.horizontalScrollBarThumb,showScrollBar:ee}))}var L=v.forwardRef(k);L.displayName="List";var j=L},76832:function(e,t,n){n.d(t,{Q3:function(){return l},_8:function(){return c}});var o=n(94423),r=n(54942),i=n(3425),a=n(47749),c=function(e){var t=e.multipleSelectItemHeight,n=e.paddingXXS,o=e.lineWidth,i=e.INTERNAL_FIXED_ITEM_MARGIN,a=e.max(e.calc(n).sub(o).equal(),0);return{basePadding:a,containerPadding:e.max(e.calc(a).sub(i).equal(),0),itemHeight:(0,r.zA)(t),itemLineHeight:(0,r.zA)(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},l=function(e){var t=e.componentCls,n=e.iconCls,r=e.borderRadiusSM,a=e.motionDurationSlow,c=e.paddingXS,l=e.multipleItemColorDisabled,u=e.multipleItemBorderColorDisabled,s=e.colorIcon,d=e.colorIconHover,f=e.INTERNAL_FIXED_ITEM_MARGIN,p="".concat(t,"-selection-overflow");return(0,o.A)({},p,(0,o.A)({position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"}},"".concat(t,"-selection-item"),(0,o.A)((0,o.A)((0,o.A)({display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:f,borderRadius:r,cursor:"default",transition:"font-size ".concat(a,", line-height ").concat(a,", height ").concat(a),marginInlineEnd:e.calc(f).mul(2).equal(),paddingInlineStart:c,paddingInlineEnd:e.calc(c).div(2).equal()},"".concat(t,"-disabled&"),{color:l,borderColor:u,cursor:"not-allowed"}),"&-content",{display:"inline-block",marginInlineEnd:e.calc(c).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"}),"&-remove",Object.assign(Object.assign({},(0,i.Nk)()),(0,o.A)((0,o.A)({display:"inline-flex",alignItems:"center",color:s,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer"},"> ".concat(n),{verticalAlign:"-0.2em"}),"&:hover",{color:d})))))},u=function(e,t){var n=e.componentCls,i=e.INTERNAL_FIXED_ITEM_MARGIN,a="".concat(n,"-selection-overflow"),u=e.multipleSelectItemHeight,s=function(e){var t=e.multipleSelectItemHeight,n=e.selectHeight,o=e.lineWidth;return e.calc(n).sub(t).div(2).sub(o).equal()}(e),d=t?"".concat(n,"-").concat(t):"",f=c(e);return(0,o.A)({},"".concat(n,"-multiple").concat(d),Object.assign(Object.assign({},l(e)),(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({},"".concat(n,"-selector"),(0,o.A)((0,o.A)({display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:f.basePadding,paddingBlock:f.containerPadding,borderRadius:e.borderRadius},"".concat(n,"-disabled&"),{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"}),"&:after",{display:"inline-block",width:0,margin:"".concat((0,r.zA)(i)," 0"),lineHeight:(0,r.zA)(u),visibility:"hidden",content:'"\\a0"'})),"".concat(n,"-selection-item"),{height:f.itemHeight,lineHeight:(0,r.zA)(f.itemLineHeight)}),"".concat(n,"-selection-wrap"),{alignSelf:"flex-start","&:after":{lineHeight:(0,r.zA)(u),marginBlock:i}}),"".concat(n,"-prefix"),{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal()}),"".concat(a,"-item + ").concat(a,"-item,\n        ").concat(n,"-prefix + ").concat(n,"-selection-wrap\n      "),(0,o.A)((0,o.A)({},"".concat(n,"-selection-search"),{marginInlineStart:0}),"".concat(n,"-selection-placeholder"),{insetInlineStart:0})),"".concat(a,"-item-suffix"),{minHeight:f.itemHeight,marginBlock:i}),"".concat(n,"-selection-search"),(0,o.A)((0,o.A)((0,o.A)({display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(s).equal()},"\n          &-input,\n          &-mirror\n        ",{height:u,fontFamily:e.fontFamily,lineHeight:(0,r.zA)(u),transition:"all ".concat(e.motionDurationSlow)}),"&-input",{width:"100%",minWidth:4.1}),"&-mirror",{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"})),"".concat(n,"-selection-placeholder"),{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(f.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:"all ".concat(e.motionDurationSlow)})))};function s(e,t){var n=e.componentCls,r=t?"".concat(n,"-").concat(t):"",i=(0,o.A)({},"".concat(n,"-multiple").concat(r),(0,o.A)((0,o.A)({fontSize:e.fontSize},"".concat(n,"-selector"),(0,o.A)({},"".concat(n,"-show-search&"),{cursor:"text"})),"\n        &".concat(n,"-show-arrow ").concat(n,"-selector,\n        &").concat(n,"-allow-clear ").concat(n,"-selector\n      "),{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}));return[u(e,t),i]}t.Ay=function(e){var t=e.componentCls,n=(0,a.oX)(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),r=(0,a.oX)(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[s(e),s(n,"sm"),(0,o.A)({},"".concat(t,"-multiple").concat(t,"-sm"),(0,o.A)((0,o.A)({},"".concat(t,"-selection-placeholder"),{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()}),"".concat(t,"-selection-search"),{marginInlineStart:2})),s(r,"lg")]}},77233:function(e,t,n){n.d(t,{A:function(){return s}});var o=n(9939),r=n(39462),i=n(58761),a=n(51551),c=n(89153),l=n(62951),u=n(23519);function s(e){var t=e.suffixIcon,n=e.clearIcon,s=e.menuItemSelectedIcon,d=e.removeIcon,f=e.loading,p=e.multiple,v=e.hasFeedback,m=e.prefixCls,g=e.showSuffixIcon,h=e.feedbackIcon,b=e.showArrow,A=(e.componentName,null!==n&&void 0!==n?n:o.createElement(i.A,null)),w=function(e){return null!==t||v||b?o.createElement(o.Fragment,null,!1!==g&&e,v&&h):null},y=null;if(void 0!==t)y=w(t);else if(f)y=w(o.createElement(l.A,{spin:!0}));else{var S="".concat(m,"-suffix");y=function(e){var t=e.open,n=e.showSearch;return w(t&&n?o.createElement(u.A,{className:S}):o.createElement(c.A,{className:S}))}}return{clearIcon:A,suffixIcon:y,itemIcon:void 0!==s?s:p?o.createElement(r.A,null):null,removeIcon:void 0!==d?d:o.createElement(a.A,null)}}},89350:function(e,t,n){n.d(t,{A:function(){return S}});var o=n(94423),r=n(27569),i=n(9939),a=n(76787),c=n.n(a),l=n(48739),u=n(37471),s=n(73109);var d=function(){var e=(0,s.Ay)(),t=(0,r.A)(e,2)[1],n=(0,l.A)("Empty"),o=(0,r.A)(n,1)[0],a=new u.Y(t.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return i.createElement("svg",{style:a,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,(null===o||void 0===o?void 0:o.description)||"Empty"),i.createElement("g",{fill:"none",fillRule:"evenodd"},i.createElement("g",{transform:"translate(24 31.67)"},i.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),i.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),i.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),i.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),i.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),i.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),i.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},i.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),i.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))};var f=function(){var e=(0,s.Ay)(),t=(0,r.A)(e,2)[1],n=(0,l.A)("Empty"),o=(0,r.A)(n,1)[0],a=t.colorFill,c=t.colorFillTertiary,d=t.colorFillQuaternary,f=t.colorBgContainer,p=(0,i.useMemo)((function(){return{borderColor:new u.Y(a).onBackground(f).toHexString(),shadowColor:new u.Y(c).onBackground(f).toHexString(),contentColor:new u.Y(d).onBackground(f).toHexString()}}),[a,c,d,f]),v=p.borderColor,m=p.shadowColor,g=p.contentColor;return i.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},i.createElement("title",null,(null===o||void 0===o?void 0:o.description)||"Empty"),i.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},i.createElement("ellipse",{fill:m,cx:"32",cy:"33",rx:"32",ry:"7"}),i.createElement("g",{fillRule:"nonzero",stroke:v},i.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),i.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:g}))))},p=n(80894),v=n(47749),m=function(e){var t=e.componentCls,n=e.margin,r=e.marginXS,i=e.marginXL,a=e.fontSize,c=e.lineHeight;return(0,o.A)({},t,(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({marginInline:r,fontSize:a,lineHeight:c,textAlign:"center"},"".concat(t,"-image"),{height:e.emptyImgHeight,marginBottom:r,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}}),"".concat(t,"-description"),{color:e.colorTextDescription}),"".concat(t,"-footer"),{marginTop:n}),"&-normal",(0,o.A)((0,o.A)({marginBlock:i,color:e.colorTextDescription},"".concat(t,"-description"),{color:e.colorTextDescription}),"".concat(t,"-image"),{height:e.emptyImgHeightMD})),"&-small",(0,o.A)({marginBlock:r,color:e.colorTextDescription},"".concat(t,"-image"),{height:e.emptyImgHeightSM})))},g=(0,p.OF)("Empty",(function(e){var t=e.componentCls,n=e.controlHeightLG,o=e.calc,r=(0,v.oX)(e,{emptyImgCls:"".concat(t,"-img"),emptyImgHeight:o(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:o(n).mul(.875).equal()});return[m(r)]})),h=n(45847),b=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n},A=i.createElement(d,null),w=i.createElement(f,null),y=function(e){var t=e.className,n=e.rootClassName,a=e.prefixCls,u=e.image,s=void 0===u?A:u,d=e.description,f=e.children,p=e.imageStyle,v=e.style,m=e.classNames,y=e.styles,S=b(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),C=(0,h.TP)("empty"),E=C.getPrefixCls,x=C.direction,I=C.className,M=C.style,O=C.classNames,R=C.styles,H=E("empty",a),z=g(H),N=(0,r.A)(z,3),D=N[0],P=N[1],T=N[2],B=(0,l.A)("Empty"),k=(0,r.A)(B,1)[0],L="undefined"!==typeof d?d:null===k||void 0===k?void 0:k.description,j="string"===typeof L?L:"empty",F=null;return F="string"===typeof s?i.createElement("img",{alt:j,src:s}):s,D(i.createElement("div",Object.assign({className:c()(P,T,H,I,(0,o.A)((0,o.A)({},"".concat(H,"-normal"),s===w),"".concat(H,"-rtl"),"rtl"===x),t,n,O.root,null===m||void 0===m?void 0:m.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},R.root),M),null===y||void 0===y?void 0:y.root),v)},S),i.createElement("div",{className:c()("".concat(H,"-image"),O.image,null===m||void 0===m?void 0:m.image),style:Object.assign(Object.assign(Object.assign({},p),R.image),null===y||void 0===y?void 0:y.image)},F),L&&i.createElement("div",{className:c()("".concat(H,"-description"),O.description,null===m||void 0===m?void 0:m.description),style:Object.assign(Object.assign({},R.description),null===y||void 0===y?void 0:y.description)},L),f&&i.createElement("div",{className:c()("".concat(H,"-footer"),O.footer,null===m||void 0===m?void 0:m.footer),style:Object.assign(Object.assign({},R.footer),null===y||void 0===y?void 0:y.footer)},f)))};y.PRESENTED_IMAGE_DEFAULT=A,y.PRESENTED_IMAGE_SIMPLE=w;var S=y},98056:function(e,t,n){var o=n(9939),r=n(45847),i=n(89350);t.A=function(e){var t=e.componentName,n=(0,(0,o.useContext)(r.QO).getPrefixCls)("empty");switch(t){case"Table":case"List":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return o.createElement(i.A,{image:i.A.PRESENTED_IMAGE_SIMPLE,className:"".concat(n,"-small")});case"Table.filter":return null;default:return o.createElement(i.A,null)}}}}]);
//# sourceMappingURL=470.27ac4efb.chunk.js.map