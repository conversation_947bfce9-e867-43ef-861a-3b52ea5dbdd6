<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.MaterialStyleMapper">

    <resultMap id="BaseResultMap" type="com.dataxai.web.domain.MaterialStyle">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="style_url" property="styleUrl" jdbcType="VARCHAR" />
        <result column="thumbnail_img_url" property="thumbnailImgUrl" jdbcType="VARCHAR" />
        <result column="material_style_category_id" property="materialStyleCategoryId" jdbcType="INTEGER" />
        <result column="task_type" property="taskType" jdbcType="INTEGER" />
        <result column="style_prompt" property="stylePrompt" jdbcType="VARCHAR" />
        <result column="style" property="style" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT s.*, c.name AS category_name
        FROM t_material_style s
        LEFT JOIN t_material_style_category c ON s.material_style_category_id = c.id
        <where>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="categoryId != null">
                AND s.material_style_category_id = #{categoryId}
            </if>
            <if test="taskType != null">
                AND s.task_type = #{taskType}
            </if>
        </where>
        ORDER BY s.sort_order ASC, s.id DESC
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM t_material_style WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_material_style (
            name, style_url, thumbnail_img_url,
            material_style_category_id, task_type, style_prompt, style,
            status, sort_order, create_time, update_time
        ) VALUES (
                     #{name}, #{styleUrl}, #{thumbnailImgUrl},
                     #{materialStyleCategoryId}, #{taskType}, #{stylePrompt}, #{style},
                     #{status}, #{sortOrder}, #{createTime}, #{updateTime}
                 )
    </insert>

    <update id="update">
        UPDATE t_material_style
        SET name = #{name},
            style_url = #{styleUrl},
            thumbnail_img_url = #{thumbnailImgUrl},
            material_style_category_id = #{materialStyleCategoryId},
            task_type = #{taskType},
            style_prompt = #{stylePrompt},
            style = #{style},
            status = #{status},
            sort_order = #{sortOrder},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM t_material_style WHERE id = #{id}
    </delete>

    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM t_material_style
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="categoryId != null">
                AND material_style_category_id = #{categoryId}
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
        </where>
    </select>

    <select id="selectPageByCondition" resultMap="BaseResultMap">
        SELECT s.*, c.name AS category_name
        FROM t_material_style s
        LEFT JOIN t_material_style_category c ON s.material_style_category_id = c.id
        <where>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="categoryId != null">
                AND s.material_style_category_id = #{categoryId}
            </if>
            <if test="taskType != null">
                AND s.task_type = #{taskType}
            </if>
        </where>
        ORDER BY s.sort_order ASC, s.id DESC
        LIMIT #{offset}, #{pageSize}
    </select>
</mapper>