package com.dataxai.web.gtask.scheduler;

import cn.hutool.json.JSONArray;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.domain.RiskDetectionTask;
import com.dataxai.domain.RiskDetectionTaskDetail;
import com.dataxai.service.IRiskDetectionTaskDetailService;
import com.dataxai.service.IRiskDetectionTaskService;
import com.dataxai.web.gtask.config.GTaskConfig;
import com.dataxai.web.gtask.config.GTaskTokenPoolsProps;
import com.dataxai.web.gtask.infra.RiskDetectionHttpClient;
import com.dataxai.web.gtask.infra.RiskDetectionPayloads;
import com.dataxai.web.gtask.infra.TokenPoolDispatcher;
import com.dataxai.web.gtask.processor.GTaskProcessor;
import com.dataxai.web.gtask.processor.RiskDetectionGTaskProcessor;
import com.dataxai.web.gtask.utils.ImageProcessUtils;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.utils.JpgUrlToWebpConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import cn.hutool.json.JSONObject;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 风险检测（按令牌分池）GTask 调度器
 * 与 RiskDetectionGTaskScheduler 风格一致：
 * - 继承 AbstractGTaskScheduler
 * - @PostConstruct 初始化，@Scheduled 定时触发 executeSchedule()
 * - 不修改原有 Processor/Scheduler，仅新增该类
 */
@Slf4j
@Component
public class RiskDetectionTokenPoolGTaskScheduler extends AbstractGTaskScheduler<RiskDetectionTaskDetail> {

    @Autowired private RiskDetectionGTaskProcessor riskDetectionProcessor;
    @Autowired private TokenPoolDispatcher dispatcher;
    @Autowired private GTaskTokenPoolsProps tokenPools;
    @Autowired private RiskDetectionHttpClient httpClient;
    @Autowired private ImageProcessUtils imageProcessUtils;
    @Autowired private AliYunFileService aliYunFileService;
    @Autowired private IRiskDetectionTaskDetailService taskDetailService;

    @Autowired private IRiskDetectionTaskService taskService;
    @Autowired private GTaskConfig gTaskConfig;

    @PostConstruct
    public void init() {
        initScheduler();
        TaskLogUtils.RiskFilter.info("风险检测(令牌并发)GTask调度器初始化完成");
    }

    @Override
    protected GTaskProcessor<RiskDetectionTaskDetail> createTaskProcessor() {
        return riskDetectionProcessor;
    }

    /**
     * 与 RiskDetectionGTaskScheduler 风格一致的调度入口（30s一次，可通过配置策略调整）
     */
    @Scheduled(fixedDelayString = "${gtask.intervals.risk_detection:30000}")
    public void scheduleRiskDetectionTasks() {
        if (!gTaskConfig.isEnabled()) {
            return;
        }
        long startTime = System.currentTimeMillis();
        try {
            TaskLogUtils.RiskFilter.info("开始执行风险检测任务调度");
            executeSchedule(); // 模板方法：内部会调用我们覆写的 processConcurrently()
            long duration = System.currentTimeMillis() - startTime;
            TaskLogUtils.RiskFilter.info("风险检测(令牌并发)任务调度执行完成，耗时: {}ms", duration);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TaskLogUtils.RiskFilter.error("风险检测(令牌并发)任务调度执行失败，耗时: {}ms", duration, e);
        }
    }

    /**
     * 覆写并发处理逻辑：改为“两个令牌并发 + 每令牌并发限制”的执行方式。
     */
    @Override
    @SuppressWarnings("unchecked")
    protected void processConcurrently(List<RiskDetectionTaskDetail> tasks) {
        final String taskType = taskProcessor.getTaskType(); // "risk_detection"
        GTaskTokenPoolsProps.TokenPoolConfig cfg = tokenPools.get(taskType);
        if (cfg == null) {
            TaskLogUtils.RiskFilter.warn("[risk-token-scheduler] token-pools 未配置，回退默认并发处理");
            super.processConcurrently((List) tasks);
            return;
        }
        List<GTaskTokenPoolsProps.UrlTokenGroup> groups = cfg.getUrlTokenGroups();
        boolean noGroups = (groups == null || groups.isEmpty());
        boolean noTokens = (cfg.getTokens() == null || cfg.getTokens().isEmpty());
        if (noGroups && noTokens) {
            TaskLogUtils.RiskFilter.warn("[risk-token-scheduler] token-pools 未配置（无 groups、无 tokens），回退默认并发处理");
            super.processConcurrently((List) tasks);
            return;
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        if (groups != null && !groups.isEmpty()) {
            // 基于“每令牌并发(concurrency)”进行加权分配；可叠加组权重(weight)
            List<String[]> pairs = new ArrayList<>(); // (url, token)
            for (GTaskTokenPoolsProps.UrlTokenGroup g : groups) {
                if (g == null || g.getUrl() == null) continue;
                List<String> toks = GTaskTokenPoolsProps.resolveGroupTokens(g);
                if (toks == null || toks.isEmpty()) continue;
                int gw = (g.getWeight()==null || g.getWeight()<=0) ? 1 : g.getWeight();
                for (String tk : toks) {
                    int tc = tokenPools.getConcurrency(taskType, tk);
                    int rep = Math.max(1, tc) * gw;
                    for (int r=0; r<rep; r++) pairs.add(new String[]{g.getUrl(), tk});
                }
            }
            if (pairs.isEmpty()) {
                TaskLogUtils.RiskFilter.warn("[risk-token-scheduler] urlTokenGroups configured but empty tokens");
            } else {
                AtomicInteger idx = new AtomicInteger(0);
                for (RiskDetectionTaskDetail detail : tasks) {
                    String[] p = pairs.get(Math.abs(idx.getAndIncrement()) % pairs.size());
                    String url = p[0]; String token = p[1];
                    String tail = token == null ? "null" : token.substring(Math.max(0, token.length()-4));
                    TaskLogUtils.RiskFilter.info("[risk-token-scheduler] submit detailId={} -> url={}, token=***{}", detail.getId(), url, tail);
                    futures.add(dispatcher.submit(taskType, token, () -> { handleSingleWithUrl(detail, token, url); return null; }));
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                return;
            }
        }

        // 回退：使用 cfg.tokens 与默认客户端 URL
        List<String> tokens = new ArrayList<>(cfg.getTokens());
        int i = 0;
        for (RiskDetectionTaskDetail detail : tasks) {
            String token = tokens.get(i++ % tokens.size());
            futures.add(dispatcher.submit(taskType, token, () -> { handleSingle(detail, token); return null; }));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 单任务完整处理流程（与现有 Processor 逻辑一致）：
     * 下载/压缩 -> 上传阿里云 -> 调用风险API -> 解析 -> 更新详情与主任务统计。
     */
    private void handleSingle(RiskDetectionTaskDetail t, String token) {
        Long detailId = t.getId(); Long mainTaskId = t.getTaskId();
        try {
            ensureMainTaskExecuting(mainTaskId);
            // 1) 下载并压缩为 webp
            byte[] webpBytes = JpgUrlToWebpConverter.urlToWebpBytes(t.getImageUrl(), "risk_detection");
            if (webpBytes == null) { markDetailFail(detailId, mainTaskId, null); return; }
            // 2) 上传阿里云
            ByteArrayResource res = imageProcessUtils.createImageResource(webpBytes);
            String processedUrl = aliYunFileService.uploadImageWebp(res);
            if (processedUrl == null || processedUrl.isEmpty()) { markDetailFail(detailId, mainTaskId, null); return; }
            // 3) 调风险 API（令牌化）

            JSONObject body = RiskDetectionPayloads.forImage(processedUrl);
            long st = System.currentTimeMillis();
            JSONObject api = httpClient.call(token, body);
            long cost = System.currentTimeMillis() - st;
            // 4) 解析
            String riskLevel = extract(api, "risk_level");
            String elements = extractArray(api, "elements");
            String suggestion = extract(api, "suggestion");
            // 5) 更新详情成功
            RiskDetectionTaskDetail upd = new RiskDetectionTaskDetail();
            upd.setId(detailId); upd.setProcessStatus(2);
            upd.setRiskLevel(riskLevel); upd.setElements(elements); upd.setSuggestion(suggestion);
            upd.setScaleImageUrl(processedUrl);
            TaskLogUtils.RiskFilter.info("令牌并发-任务成功 url={}, batchId={}, detailId={}, costMs={}", "(default)", mainTaskId, detailId, cost);
            taskDetailService.updateRiskDetectionTaskDetail(upd);
            incrementSuccessAndMaybeFinish(mainTaskId);
        } catch (Exception e) {
            TaskLogUtils.RiskFilter.error("令牌并发-任务失败 url={}, batchId={}, detailId={}", "(default)", mainTaskId, detailId, e);
            markDetailFail(detailId, mainTaskId, null);
        }
    }


    private void handleSingleWithUrl(RiskDetectionTaskDetail t, String token, String url) {
        Long detailId = t.getId(); Long mainTaskId = t.getTaskId();
        try {
            ensureMainTaskExecuting(mainTaskId);
            byte[] webpBytes = JpgUrlToWebpConverter.urlToWebpBytes(t.getImageUrl(), "risk_detection");
            if (webpBytes == null) { markDetailFail(detailId, mainTaskId, null); return; }
            ByteArrayResource res = imageProcessUtils.createImageResource(webpBytes);
            String processedUrl = aliYunFileService.uploadImageWebp(res);
            if (processedUrl == null || processedUrl.isEmpty()) { markDetailFail(detailId, mainTaskId, null); return; }
            JSONObject body = RiskDetectionPayloads.forImage(processedUrl);
            long st = System.currentTimeMillis();
            JSONObject api = httpClient.callWithUrl(url, token, body, detailId);
            long cost = System.currentTimeMillis() - st;
            String tail = token == null ? "null" : token.substring(Math.max(0, token.length()-4));
            TaskLogUtils.RiskFilter.info("[risk-token-scheduler] done detailId={}, url={}, token=***{}, costMs={}", detailId, url, tail, cost);
            String riskLevel = extract(api, "risk_level");
            String elements = extractArray(api, "elements");
            String suggestion = extract(api, "suggestion");
            RiskDetectionTaskDetail upd = new RiskDetectionTaskDetail();
            upd.setId(detailId); upd.setProcessStatus(2);
            upd.setRiskLevel(riskLevel); upd.setElements(elements); upd.setSuggestion(suggestion);
            upd.setScaleImageUrl(processedUrl);
            taskDetailService.updateRiskDetectionTaskDetail(upd);
            incrementSuccessAndMaybeFinish(mainTaskId);
        } catch (Exception e) {
            TaskLogUtils.RiskFilter.warn("[risk-token-scheduler] 单任务失败 id="+detailId+", url="+url, e);
            markDetailFail(detailId, mainTaskId, null);
        }
    }

    private void ensureMainTaskExecuting(Long taskId){
        try {
            RiskDetectionTask task = taskService.selectRiskDetectionTaskById(taskId);
            if (task != null && Objects.equals(task.getStatus(), 1)) {
                RiskDetectionTask upd = new RiskDetectionTask(); upd.setId(taskId); upd.setStatus(2);
                taskService.updateRiskDetectionTask(upd);
            }
        } catch (Exception ignore) {}
    }

    private void markDetailFail(Long detailId, Long mainTaskId, String processedUrl){
        try { RiskDetectionTaskDetail upd = new RiskDetectionTaskDetail();
            upd.setId(detailId); upd.setProcessStatus(3); upd.setScaleImageUrl(processedUrl);
            taskDetailService.updateRiskDetectionTaskDetail(upd);
        } catch (Exception ignore) {}
        try { taskService.incrementFailAmount(mainTaskId); maybeFinish(mainTaskId); } catch (Exception ignore) {}
    }

    private void incrementSuccessAndMaybeFinish(Long mainTaskId){
        try { taskService.incrementSuccessAmount(mainTaskId); } catch (Exception ignore) {}
        maybeFinish(mainTaskId);
    }

    private void maybeFinish(Long mainTaskId){
        try {
            RiskDetectionTask t = taskService.selectRiskDetectionTaskById(mainTaskId);
            if (t == null) return;
            int total = t.getTotalAmount() == null ? 0 : t.getTotalAmount();
            int done = (t.getSuccessAmount()==null?0:t.getSuccessAmount()) + (t.getFailAmount()==null?0:t.getFailAmount());
            if (done >= total && !Objects.equals(t.getStatus(), 3)) {
                RiskDetectionTask upd = new RiskDetectionTask(); upd.setId(mainTaskId); upd.setStatus(3);
                taskService.updateRiskDetectionTask(upd);
            }
        } catch (Exception ignore) {}
    }



    private String extract(JSONObject result, String key){
        try { JSONObject ans = result.getJSONObject("answer"); return ans!=null? ans.getStr(key): null; } catch(Exception e){ return null; }
    }
    private String extractArray(JSONObject result, String key){
        try { JSONObject ans = result.getJSONObject("answer"); if(ans==null) return null; JSONArray arr = ans.getJSONArray(key); return arr!=null? arr.toString(): null; } catch(Exception e){ return null; }
    }

    @PreDestroy
    public void destroy() {
        TaskLogUtils.RiskFilter.info("风险检测(令牌并发)GTask调度器正在关闭...");
        shutdown();
        TaskLogUtils.RiskFilter.info("风险检测(令牌并发)GTask调度器已关闭");
    }
}

