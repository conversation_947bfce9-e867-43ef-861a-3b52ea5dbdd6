# FilterBar 组件使用说明

## 概述
FilterBar 是一个通用的筛选条组件，支持多种筛选字段，包括批次号、备注、日期范围、图片颜色和电话等。

## 支持的字段类型
- `batchNumber`: 批次号
- `remark`: 备注
- `dateRange`: 日期范围
- `imageColor`: 图片颜色（新增）
- `phone`: 电话（新增）

## 基本用法

### 1. 基础筛选（批次号 + 日期范围）
```tsx
import FilterBar from '@/component/filter';

<FilterBar
  fields={['batchNumber', 'dateRange']}
  storageKey="my-filter"
  onSearch={(params) => {
    console.log('查询参数:', params);
    // params 包含: batchNumber, startTime, endTime, stringDateRange
  }}
/>
```

### 2. 包含备注的筛选
```tsx
<FilterBar
  fields={['batchNumber', 'remark', 'dateRange']}
  storageKey="my-filter"
  onSearch={(params) => {
    console.log('查询参数:', params);
    // params 包含: batchNumber, remark, startTime, endTime, stringDateRange
  }}
/>
```

### 3. 包含图片颜色和电话的筛选
```tsx
<FilterBar
  fields={['batchNumber', 'remark', 'imageColor', 'phone', 'dateRange']}
  storageKey="my-filter"
  onSearch={(params) => {
    console.log('查询参数:', params);
    // params 包含: batchNumber, remark, imageColor, phone, startTime, endTime, stringDateRange
  }}
/>
```

### 4. 带时分秒的日期范围
```tsx
<FilterBar
  fields={['batchNumber', 'dateRange']}
  storageKey="my-filter"
  withTime={true}
  onSearch={(params) => {
    console.log('查询参数:', params);
    // startTime 和 endTime 包含时分秒信息
  }}
/>
```

### 5. 设置默认值
```tsx
<FilterBar
  fields={['batchNumber', 'imageColor', 'phone']}
  storageKey="my-filter"
  defaults={{
    batchNumber: '默认批次号',
    imageColor: '红色',
    phone: '13800138000'
  }}
  onSearch={(params) => {
    console.log('查询参数:', params);
  }}
/>
```

## 参数说明

### FilterBarProps
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| fields | FilterField[] | ['batchNumber', 'dateRange'] | 需要展示的字段 |
| storageKey | string | - | sessionStorage key，用于记住查询条件 |
| withTime | boolean | false | 日期范围是否带时分秒 |
| defaults | Partial<FilterParams> | - | 初始默认值 |
| onSearch | (params: FilterParams) => void | - | 点击查询时回调 |
| onReset | (params: FilterParams) => void | - | 点击重置时回调（可选） |
| className | string | - | 外层样式 |

### FilterParams
| 字段 | 类型 | 说明 |
|------|------|------|
| batchNumber | string | 批次号 |
| remark | string | 备注 |
| imageColor | string | 图片颜色 |
| phone | string | 电话 |
| stringDateRange | string[] | 原始字符串日期数组 |
| startTime | string | 处理后的开始时间 |
| endTime | string | 处理后的结束时间 |
| type | any | 类型筛选（可选） |

## 特性

1. **状态持久化**: 通过 `storageKey` 将筛选条件保存到 sessionStorage
2. **智能时间处理**: 自动处理日期范围，生成标准化的 startTime/endTime
3. **灵活字段配置**: 可以自由组合需要的筛选字段
4. **响应式设计**: 支持自定义样式类名
5. **类型安全**: 完整的 TypeScript 类型定义

## 注意事项

1. 当 `withTime=true` 时，日期选择器会显示时分秒选择器
2. 日期范围会自动处理边界时间（00:00:00 和 23:59:59）
3. 所有字段都是可选的，只有在 `fields` 数组中指定的字段才会显示
4. 重置操作会清空所有字段并触发查询
