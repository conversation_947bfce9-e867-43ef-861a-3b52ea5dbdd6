package com.dataxai.web.controller.task;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.constant.HttpStatus;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.domain.model.User;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.core.domain.model.LoginUser;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.domain.TeamUser;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.BatchPageDTO;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.dto.ZIPDTO;
import com.dataxai.web.mapper.OrdinalImgResultMapper;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.service.BatchService;
import com.dataxai.web.service.MaterialResolverService;
import com.dataxai.web.service.platform.IPlatformApiService;
import com.dataxai.web.task.core.common.TaskTypeUtils;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.utils.CustomMultipartFile;
import com.dataxai.web.utils.FileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.dataxai.web.mapper.BatchMapper;
import com.dataxai.domain.RiskDetectionTask;
import com.dataxai.domain.TitleExtractionTask;
import com.dataxai.domain.ProductInfo;
import com.dataxai.service.IRiskDetectionTaskService;
import com.dataxai.service.ITitleExtractionTaskService;
import com.dataxai.service.IProductInfoService;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/batch")
@Api(tags = {"任务管理接口"})
public class BatchController extends BaseController {

    @Autowired
    private BatchService batchService;

    @Autowired
    private AliYunFileService aliYunFileService;

    @Autowired
    private IPlatformApiService platformApiService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private OrdinalImgResultMapper ordinalImgResultMapper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private com.dataxai.web.batch.BatchTaskFactoryManager batchTaskFactoryManager;

    @Autowired
    private com.dataxai.common.service.UserTeamInfoService userTeamInfoService;

    @Autowired
    private com.dataxai.service.ITeamUserService iTeamUserService;

    @Autowired
    private MaterialResolverService materialResolverService;
    @Autowired
    private TUserMapper tUserMapper;

    @Autowired
    private IRiskDetectionTaskService riskDetectionTaskService;
    @Autowired
    private ITitleExtractionTaskService titleExtractionTaskService;
    @Autowired
    private IProductInfoService productInfoService;

    // 常量定义
    private static final String VALIDATION_FLAG = "flag";
    private static final String FILES_PARAM = "files[]";
    private static final String ERROR_NO_IMAGE_DATA = "图片信息为空：未提供imgUrl参数且未上传文件";
    private static final String ERROR_TABLE_VALIDATION = "Excel表格验证失败: ";
    private static final String ERROR_FILE_PROCESS = "文件处理失败: ";
    private static final String ERROR_BATCH_CREATE = "批次创建失败";

    /**
     * 新增批次信息
     * <p>
     * 该方法支持多种类型的批次创建：
     * 1. 带Excel表格的批次（表格验证 + 文件上传）
     * 2. 批量上传类型批次（type=15，直接文件上传）
     * 3. 其他类型批次（通过imgUrls或files参数上传图片）
     *
     * @param table     需要上传的Excel表格文件（可选）
     * @param type      批次类型（必传）
     * @param imgUrls   图片URL数组（可选）
     * @param remark    批次备注信息（可选）
     * @param taskParam 任务参数（可选）
     * @param files     需要上传的文件集合（可选）
     * @return 创建的批次信息
     */
    @Log(title = "任务batch-add", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Transactional
    @ApiOperation("新增批次")
    public R<Object> add(
            @ApiParam("需要上传的表格") @RequestPart(value = "table", required = false) MultipartFile table,
            @ApiParam("批次类型") @RequestParam Long type,
            @ApiParam("图片URL数组") @RequestParam(value = "imageUrls[]", required = false) List<String> imgUrls,
            @ApiParam("图片URL数组(无中括号)") @RequestParam(value = "imageUrls", required = false) List<String> imageUrlsNoBracket,
            @ApiParam("单个图片URL") @RequestParam(value = "imageUrl", required = false) String imageUrl,
            @ApiParam("备注信息") @RequestParam(required = false) String remark,
            @ApiParam("任务参数") @RequestParam(required = false) String taskParam,
            @ApiParam("风格素材ID") @RequestParam(required = false) Integer materialStyleId,
            @ApiParam("IP素材ID") @RequestParam(required = false) Integer materialIpId,
            @ApiParam("印花图提取品类ID") @RequestParam(required = false) Integer cropCategoryId,
            @ApiParam("是否仅裂变图案") @RequestParam(required = false) Integer onlyFissionPattern,
            @ApiParam("图片生成数量") @RequestParam(required = false) Integer imageNumber,
            @ApiParam(value = "需要上传的文件集合（非必传）", required = false) @RequestParam(value = FILES_PARAM, required = false) List<MultipartFile> files
    ) {

        try {
            // 首先处理基础参数（如 onlyFissionPattern）
            String enhancedTaskParam = addBasicParamsToTaskParam(taskParam, onlyFissionPattern);

            // 兼容多种前端字段：imageUrls[] / imageUrls / imageUrl
            List<String> effectiveImgUrls = imgUrls;
            if (CollectionUtil.isEmpty(effectiveImgUrls) && CollectionUtil.isNotEmpty(imageUrlsNoBracket)) {
                effectiveImgUrls = imageUrlsNoBracket;
            }
            if (CollectionUtil.isEmpty(effectiveImgUrls) && StringUtils.isNotEmpty(imageUrl)) {
                effectiveImgUrls = java.util.Collections.singletonList(imageUrl);
            }

            // 然后处理素材参数 - 如果是文生图类型且有素材ID，则增强taskParam
            if (isTextToImageType(type.intValue()) && (materialStyleId != null || materialIpId != null)) {
                enhancedTaskParam = buildTaskParamWithMaterials(enhancedTaskParam, materialStyleId, materialIpId, cropCategoryId);
                log.info("批量任务素材参数已增强: type={}, materialStyleId={}, materialIpId={}, cropCategoryId={}",
                        type, materialStyleId, materialIpId, cropCategoryId);
            }
            // 如果是印花图提取类型且有品类ID，则增强taskParam
            else if (type.intValue() == Constants.TASK_TYPE_CROP_EXTRACT && cropCategoryId != null) {
                enhancedTaskParam = buildTaskParamWithMaterials(enhancedTaskParam, null, null, cropCategoryId);
                log.info("批量任务印花图提取品类参数已增强: type={}, cropCategoryId={}", type, cropCategoryId);
            }

            // 调用服务层完整批次创建流程（使用合并后的图片URL列表）
            BatchService.BatchCreationResult result = batchService.createBatchWithFullProcesscope(
                    table,
                    type,
                    effectiveImgUrls,
                    remark,
                    enhancedTaskParam,
                    files,
                    SecurityUtils.getUserId()
            );
            System.out.println("异步处理开始");
            System.out.println(result);
            System.out.println(imageNumber);
            System.out.println("---------------------------------------------------------------------");

            // 异步处理后续任务（统一走服务层公共方法）
            batchService.processAsyncTasks(result, SecurityUtils.getUserId());

            return R.ok(result.getBatch());

        } catch (ServiceException e) {
            // 业务异常直接抛出，让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("批次创建失败 - 类型: {}, 错误: {}", type, e.getMessage(), e);
            throw new ServiceException("批次创建失败: " + e.getMessage());
        }
    }

    /**
     * 转换文件为字节数组
     */
    private byte[] convertFileToBytes(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            log.error("文件读取失败 - 文件名: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件读取失败: " + file.getOriginalFilename(), e);
        }
    }

    @GetMapping("/list")
    @ApiOperation(value = "查询批次列表")
    public R<Object> list(BatchPageDTO dto) {

        // 根据用户模式设置数据过滤条件
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            Long userId = SecurityUtils.getUserId();
            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
            if (teamUser.getIsAdmin() == true) {
                Long selectUserId = dto.getUserId();
                if (selectUserId != null) {
                    dto.setTeamId(userTeamInfo.getTeamId());
                    dto.setUserId(selectUserId);
                } else {
                    dto.setTeamId(userTeamInfo.getTeamId());
                    dto.setUserId(null); // 清空userId，避免冲突
                }
            } else {
                dto.setUserId(userId);
            }

        } else {
            // 个人模式：通过user_id过滤，且team_id为0
            dto.setUserId(SecurityUtils.getUserId());
            dto.setTeamId(null); // 确保不设置teamId
        }

        startPage();
        List<Batch> batchList = Collections.emptyList();
        if (null != dto.getType()) {
            batchList = batchMapper.getInfo(dto);
        } else if (CollectionUtil.isNotEmpty(dto.getTypes())) {
            batchList = batchMapper.getInfo(dto);
        }

        if (null != dto.getType() && dto.getType() == 15) {
            for (Batch batch : batchList) {
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                List<Task> list = taskMapper.selectTaskListAddUrl(task);
                batch.setTasks(list);
            }
        } else {
            for (Batch batch : batchList) {
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                List<OrdinalImgResult> list = ordinalImgResultMapper.selectImageResult(batch.getBatchId());
                batch.setWaitTime(calculateWaitTime(batch, null, 1));
                batch.setImgResults(list);
            }
        }

        // 3. 封装分页信息
        PageInfo<Batch> pageInfo = new PageInfo<>(batchList);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", pageInfo.getTotal());
        dataMap.put("data", pageInfo.getList());
        return R.ok(dataMap);
    }

    @GetMapping(value = "/getOne/{batchId}")
    @ApiOperation(value = "查询批次详情-不分页")
    public R<Batch> getOne(@PathVariable("batchId") String batchId) {
        Batch batch = batchService.getOne(batchId);
        return R.ok(batch);
    }

    @GetMapping(value = "/getOnePage/list")
    @ApiOperation(value = "查询批次详情-分页")
//    public R<HashMap<String, Object>> getOnePage(@PathVariable("batchId") String batchId) {
    public R<HashMap<String, Object>> getOnePage(Batch  batch) {
        String batchId = batch.getBatchId();
        Batch batchInfo = batchMapper.selectById(batchId);
        if (batchInfo == null) {
            return null;
        }
        Task task = new Task();
        task.setBatchId(batchId);
        task.setDelFlag(0);
        task.setStatus(Constants.TASK_STATUS_SUCCESS);
        List<Task> list = taskMapper.selectTaskList(task);
        startPage();
        List<Task> processedList = batchService.getTaskInfo(list);
        // 组装自定义分页结构
        HashMap<String, Object> pageData = new HashMap<>();
//        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("total", new PageInfo<>(processedList).getTotal());
        pageData.put("data", processedList);
        pageData.put("batch", batchInfo);
        return R.ok(pageData);
    }

    /**
     * 删除批次信息
     */
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{batchId}")
    @ApiOperation("删除批次信息")
    @ApiImplicitParam(name = "batchId", required = true, dataType = "long", paramType = "path", dataTypeClass = String.class)
    public R<Integer> remove(@PathVariable String batchId) {
        Long userId = SecurityUtils.getUserId();
        return R.ok(batchService.logicDeleteTaskByTaskIds(userId, batchId));
    }

    /**
     * 修复批次统计数据
     */
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PostMapping("/fixStatistics/{batchId}")
    @ApiOperation("修复批次统计数据")
    @ApiImplicitParam(name = "batchId", required = true, dataType = "long", paramType = "path", dataTypeClass = String.class)
    public R<Map<String, Object>> fixStatistics(@PathVariable String batchId) {
        Map<String, Object> result = batchService.fixBatchStatistics(batchId);
        return R.ok(result);
    }

    /**
     * 终止批次
     */
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @PostMapping("/stop/{batchId}")
    @ApiOperation("终止批次")
    @ApiImplicitParam(name = "batchId", required = true, dataType = "long", paramType = "path", dataTypeClass = String.class)
    public R<Integer> stop(@PathVariable String batchId) {
        Long userId = SecurityUtils.getUserId();
        return R.ok(batchService.stop(userId, batchId));
    }

    /**
     * 修改裁剪后的图片
     */
    @Log(title = "每次任务执行后生成的图片", businessType = BusinessType.UPDATE)
    @PostMapping("/updateImage")
    @ApiOperation("修改自动裁剪任务执行后生成的图片")
    public R<Integer> updateImage(@ApiParam("需要上传的文件") @RequestPart("file") MultipartFile file, String imageId) throws IOException {
        return R.ok(batchService.updateImage(file, imageId));
    }
    

    @ApiOperation(value = "将图片同步到设计器")
    @ApiResponses(value = {@ApiResponse(code = HttpStatus.SUCCESS, message = "成功"), @ApiResponse(code = HttpStatus.ERROR, message = "失败"),
    })
    @PostMapping("/synchronization")
    public R<Object> synchronization(@RequestBody ZIPDTO dto) throws Exception {
        if (dto.getImageUrls() == null || dto.getImageUrls().isEmpty()) {
            throw new ServiceException("图片地址为空");
        }
        if (dto.getPlatform() == null) {
            throw new ServiceException("平台类型不能为空");
        }

        // 根据平台类型调用对应的推送接口
        Long userId = SecurityUtils.getUserId();
        String platformCode = getPlatformCode(dto.getPlatform());

        // 获取phone和name参数
        String phone = getCurrentUserPhone();
        String name = getTypeNameFromImages(dto.getImageUrls(), dto.getType());

        // 调用平台API服务推送图片（带参数）
        Map<String, Object> result = platformApiService.pushImagesWithParams(
                platformCode,
                userId,
                dto.getImageUrls(),
                phone,
                name,
                dto.getTags(),
                dto.getImageUrls().size()
        );

        // 检查平台调用结果
        Boolean success = (Boolean) result.get("success");
        if (success == null || !success) {
            // 平台调用失败，返回错误状态
            String message = (String) result.get("message");
            if (message == null) {
                message = "平台调用失败";
            }
            return R.fail(HttpStatus.ERROR, message);
        }
        return R.ok(result);
    }

    /**
     * 获取当前登录用户的手机号
     */
    private String getCurrentUserPhone() {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getAppUser() != null) {
                String phone = loginUser.getAppUser().getPhone();
                log.info("获取到当前用户手机号: {}", phone);
                return phone;
            } else {
                log.warn("无法获取当前用户信息，使用默认手机号");
                return "default_phone";
            }
        } catch (Exception e) {
            log.error("获取用户手机号失败", e);
            return "default_phone";
        }
    }

    /**
     * 根据图片URL等信息获取类型名称
     */
    private String getTypeNameFromImages(List<String> images, String type) {
        // 如果传入了type，直接使用
        if (StringUtils.isNotEmpty(type)) {
            return TaskTypeUtils.getTaskTypeName(type);
        }

        // 如果没有图片，返回默认名称
        if (images == null || images.isEmpty()) {
            return "默认分类";
        }

        try {
            // 取第一张图片的URL进行查询
            String firstImageUrl = images.get(0);
            String subUrl = CommonUtils.subCosPrefix(firstImageUrl);

            // 先查询结果表
            OrdinalImgResult imgResult = ordinalImgResultMapper.selectTypeByUrl(subUrl);
            if (imgResult != null) {
                String typeName = TaskTypeUtils.getTaskTypeName(imgResult.getType());
                log.info("从结果表获取到类型名称: {}", typeName);
                return typeName;
            }

            // 如果结果表没有，查询任务表
            Task task = taskMapper.selectTypeByUrl(subUrl);
            if (task != null) {
                String typeName = TaskTypeUtils.getTaskTypeName(task.getType());
                log.info("从任务表获取到类型名称: {}", typeName);
                return typeName;
            }

            // 如果都查询不到，返回默认名称
            log.warn("无法从数据库获取类型信息，使用默认名称");
            return "未知类型";

        } catch (Exception e) {
            log.error("获取类型名称失败", e);
            return "未知类型";
        }
    }

    /**
     * 根据平台类型获取平台代码
     */
    private String getPlatformCode(Integer platform) {
        switch (platform) {
            case 1:
                return "dingzhi";
            case 2:
                return "forgediy";
            default:
                throw new ServiceException("不支持的平台类型: " + platform);
        }
    }

    //计算等待时间 - 优化版本：根据任务在队列中的位置计算递增等待时间
    private int calculateWaitTime(Batch batch, String taskId, int isBatch) {
        String type = "";
        String createTime = "";
        if (isBatch == 1) {
            if (batch == null || !"4".equals(String.valueOf(batch.getStatus()))) {
                return 0;
            }
            type = batch.getType().toString();
            createTime = dateFormat(batch.getCreateTime());

        } else {
            Task task = taskMapper.selectTaskByTaskId(taskId);
            if (task == null || !"4".equals(String.valueOf(task.getStatus()))) {
                return 0;
            }
            type = task.getType().toString();
            createTime = dateFormat(task.getCreateTime());
        }

        //获取这个任务的GPU数量
        Integer gpuCountObj = taskMapper.getGpuCount(isBatch, type);
        int gpuCount = (gpuCountObj == null || gpuCountObj <= 0) ? 1 : gpuCountObj.intValue();
        if (gpuCount <= 0) {
            log.warn("未找到任务类型 {} 的GPU配置", type);
            return 0;
        }

        //获取任务在队列中的位置
        Integer queuePosition = taskMapper.getTaskQueuePosition(isBatch, createTime, type);
        if (queuePosition == null || queuePosition <= 0) {
            return 0;
        }

        //获取之前等待中的数据和时间
        List<Map<String, String>> waitList = taskMapper.getWaitList(isBatch, createTime, type);
        if (waitList.isEmpty()) {
            return 0;
        }

        //计算平均任务处理时间
        List<Integer> timeList = new ArrayList<>();
        for (Map<String, String> waitMap : waitList) {
            timeList.add((int) Math.ceil(Double.parseDouble(String.valueOf(waitMap.get("totalTime")))));
        }

        //计算平均处理时间
        double avgProcessTime = timeList.stream()
                .mapToInt(Integer::intValue)
                .average()
                .orElse(0.0);

        if (avgProcessTime <= 0) {
            return 0;
        }

        //根据队列位置计算等待时间
        // 公式：等待时间 = (队列位置 - 1) * 平均处理时间 / GPU数量
        int waitTimeSeconds = (int) Math.ceil((queuePosition - 1) * avgProcessTime / gpuCount);

        //转换为分钟
        int waitTimeMinutes = (int) Math.ceil((double) waitTimeSeconds / 60);

        log.info("任务队列位置: {}, 平均处理时间: {}秒, GPU数量: {}, 计算等待时间: {}分钟",
                queuePosition, avgProcessTime, gpuCount, waitTimeMinutes);

        return waitTimeMinutes;
    }

    private String dateFormat(Date date) {
        if (date == null) {
            return null;
        }
        // 将 Date 转换为 Instant，再结合系统默认时区转换为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return df.format(localDateTime);
    }

    /**
     * 添加基础参数到任务参数中
     */
    private String addBasicParamsToTaskParam(String originalTaskParam, Integer onlyFissionPattern) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode paramNode;

            // 解析原始taskParam
            if (StringUtils.isNotEmpty(originalTaskParam)) {
                paramNode = (ObjectNode) objectMapper.readTree(originalTaskParam);
            } else {
                paramNode = objectMapper.createObjectNode();
            }

            // 添加仅裂变图案参数
            if (onlyFissionPattern != null) {
                paramNode.put("onlyFissionPattern", onlyFissionPattern);
                log.info("批量任务添加仅裂变图案参数: {}", onlyFissionPattern);
            }

            return objectMapper.writeValueAsString(paramNode);

        } catch (Exception e) {
            log.error("批量任务添加基础参数失败: {}", e.getMessage(), e);
            // 如果失败，返回原始参数
            return originalTaskParam;
        }
    }

    /**
     * 判断是否为文生图类型任务
     */
    private boolean isTextToImageType(int taskType) {
        return taskType == Constants.TASK_TYPE_TEXT_TO_IMAGE ||
                taskType == Constants.TASK_TYPE_FOUR_TEXT ||
                taskType == Constants.TASK_TYPE_FOUR_IMAGE;
    }

    /**
     * 构建包含素材信息的任务参数
     */
    private String buildTaskParamWithMaterials(String originalTaskParam, Integer materialStyleId, Integer materialIpId, Integer cropCategoryId) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode paramNode;

            // 解析原始taskParam
            if (StringUtils.isNotEmpty(originalTaskParam)) {
                paramNode = (ObjectNode) objectMapper.readTree(originalTaskParam);
            } else {
                paramNode = objectMapper.createObjectNode();
            }

            // 添加素材ID到参数中
            if (materialStyleId != null) {
                paramNode.put("materialStyleId", materialStyleId);
                log.info("批量任务添加风格素材ID: {}", materialStyleId);
            }

            if (materialIpId != null) {
                paramNode.put("materialIpId", materialIpId);
                log.info("批量任务添加IP素材ID: {}", materialIpId);
            }

            // 添加印花图提取品类ID到参数中
            if (cropCategoryId != null) {
                paramNode.put("cropCategoryId", cropCategoryId);
                log.info("批量任务添加印花图提取品类ID: {}", cropCategoryId);
            }

            // 转换为JSON字符串
            String baseTaskParam = objectMapper.writeValueAsString(paramNode);
            log.info("批量任务包含素材ID的基础参数: {}", baseTaskParam);

            // 检查MaterialResolverService是否可用
            if (materialResolverService == null) {
                log.warn("MaterialResolverService未注入，返回基础参数");
                return baseTaskParam;
            }

            // 使用MaterialResolverService构建完整的素材信息
            String fullTaskParam = materialResolverService.buildFullTaskParam(baseTaskParam);
            log.info("批量任务构建完成的完整参数: {}", fullTaskParam);
            return StringUtils.isNotEmpty(fullTaskParam) ? fullTaskParam : baseTaskParam;

        } catch (Exception e) {
            log.error("批量任务构建包含素材信息的任务参数失败: {}", e.getMessage(), e);
            // 如果失败，返回原始参数
            return originalTaskParam;
        }
    }

    /**
     * 更新任务备注（通用接口）
     */
        @Log(title = "任务备注更新", businessType = BusinessType.UPDATE)
    @PutMapping("/task/remark")
    @ApiOperation(value = "更新任务备注")
    public R<Object> updateTaskRemark(@RequestBody Map<String, Object> params) {
        String taskType = (String) params.get("taskType"); // "risk", "title", "product", "batch"
        Object id = params.get("id");
        String remark = (String) params.get("remark");

        if (StringUtils.isEmpty(taskType) || id == null) {
            throw new ServiceException("任务类型和记录ID不能为空");
        }

        try {
            switch (taskType) {
                case "risk":
                    return updateRiskDetectionTaskRemark(Long.valueOf(String.valueOf(id)), remark);
                case "title":
                    return updateTitleExtractionTaskRemark(Long.valueOf(String.valueOf(id)), remark);
                case "product":
                    return updateProductExportRemark(Long.valueOf(String.valueOf(id)), remark);
                case "batch":
                    return updateBatchRemark(String.valueOf(id), remark);
                default:
                    throw new ServiceException("不支持的任务类型: " + taskType);
            }
        } catch (Exception e) {
            log.error("更新任务备注失败，taskType: {}, id: {}", taskType, id, e);
            throw new ServiceException("更新任务备注失败: " + e.getMessage());
        }
    }
    
    private R<Object> updateRiskDetectionTaskRemark(Long id, String remark) {
        RiskDetectionTask task = new RiskDetectionTask();
        task.setId(id);
        task.setRemark(remark);
        task.setUpdateTime(new Date());
        
        Integer updated = riskDetectionTaskService.updateRiskDetectionTask(task);
        return updated != null && updated > 0 ? R.ok() : R.fail("更新失败");
    }
    
    private R<Object> updateTitleExtractionTaskRemark(Long id, String remark) {
        TitleExtractionTask task = new TitleExtractionTask();
        task.setId(id);
        task.setRemark(remark);
        task.setUpdateTime(new Date());
        
        Integer updated = titleExtractionTaskService.updateTitleExtractionTask(task);
        return updated != null && updated > 0 ? R.ok() : R.fail("更新失败");
    }
    
    private R<Object> updateProductExportRemark(Long id, String remark) {
        ProductInfo productInfo = new ProductInfo();
        productInfo.setId(id);
        productInfo.setRemark(remark);
        productInfo.setUpdateTime(new Date());
        
        Integer updated = productInfoService.updateProductInfo(productInfo);
        return updated != null && updated > 0 ? R.ok() : R.fail("更新失败");
    }
    
    private R<Object> updateBatchRemark(String batchId, String remark) {
        Batch batch = new Batch();
        batch.setBatchId(batchId);
        batch.setRemark(remark);
        batch.setUpdateTime(new Date());
        
        Integer updated = batchService.updateBat(batch);
        return updated != null && updated > 0 ? R.ok() : R.fail("更新失败");
    }
}
