import { getCharacterList, delCharacterByHistoryId } from '@/api/task'
import { useContext, useEffect, useState } from 'react'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { Modal } from 'antd'
import { Context } from './historyProvider'
import { addCharacter } from '@/api/task'
import { useTaskService } from '@/common/services/task/taskContext'
import './TaskFaceFeatureCustom.scss'


interface HistoryComponentProps {
    onCharacterChange?: (newCharacter?: {
        characterId: string,
        imgUrl: any
    }) => void;
}

const HistoryComponent: React.FC<HistoryComponentProps> = ({ onCharacterChange }) => {
    // 需要点击返显的图片地址
    const taskService = useTaskService();

    const [history, setHistory] = useState([])
    // 分页总页数
    const [total, setTotal] = useState(0)
    // 当前页
    const [page, setPage] = useState(1)
    // content
    const content: any = useContext(Context)
    // 反显状态

    const userId: any = userinfoService.userInfo.get()?.userId;

    useEffect(() => {
        // 缓存进本地
        // localStorage.setItem('history',JSON.stringify(value));
        // 有时候拿不到userId，会导致调用接口报错，进而页面报错
        if (userId) {
            getCharacterList(userId, 1).then((value: any) => {
                setHistory(value);
                setTotal(Math.ceil(value.length / 10));// 总页数
                setPage(value.length / 10);
            }).catch((error) => {
                console.error('Error fetching data: ', error);
            })// eslint-disable-next-line
        } else {
            console.warn('UserId is not avaliable. Please check your userId.')
            console.log('userinfoService.userInfo', userinfoService.userInfo);
        }
    }, [])  // eslint-disable-next-line
    useEffect(() => {
    }, [history])

    const delHisory = (historyId: any) => {
        if (!historyId) {
            console.error('historyId is not avaliable. Please check your historyId.')
            return
        }
        // 先删除， 再调用查询接口
        delCharacterByHistoryId(historyId)
        // 调用查询接口需要延时，因为前端速度更快，会在数据更新前调用接口查询，查询到的数据为旧数据
        setTimeout(() => {
            getCharacterList(userId, 1).then((value: any) => {
                setHistory(value);
            })
        }, 300);
    }

    const pageChange = (pagenum: any) => {
        getCharacterList(userId, pagenum).then((value: any) => {
            setHistory(value);
        })
    }
    const [isModalOpen, setIsModalOpen] = useState(false)

    const handleOk = () => {
        setIsModalOpen(false)
    }

    const handleCancel = () => {
        setIsModalOpen(false)
    }

    // 点击历史记录反显
    const hisimg = (id: any, path: any) => {
        addCharacter({ imgUrl: path, taskId: taskService.taskId, source: 1 }).then((res) => {
            // 拿到新的imgUrl，修改父组件的 character.imgUrl 触发组件更新
            const newCharacter = {
                characterId: res.characterId,
                imgUrl: path || undefined
            }
            //
            onCharacterChange && onCharacterChange(newCharacter)
        }).catch((error) => {
            console.log('error in adding custom face character', error);
        })
        // if (content?.deltype === true) {
        //     addCharacter({ imgUrl: path, taskId: taskService.taskId })
        //     content.setDeltype(false)
        //     console.log('不可以再传');
        // } else {
        //     console.log('一个任务仅能自定义一张，请删除后再试');
        // }
        // content.setHisUrl(path)
        setIsModalOpen(false)
    }
    /* ()=>{content.setHisUrl("https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com" + item.imgUrl)} */
    let historyEL = history?.length > 0 ? history.map((item: any) => {
        return <div className="w-[74px] h-[104px] mr-[14px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
            <img onClick={() => hisimg(1, "http://image-task.xiaoaishop.com/" + item.imgUrl)}
                src={"http://image-task.xiaoaishop.com/" + item.imgUrl}
                className="w-full h-[74px] rounded-lg  hover:border-primary border-[2px] "
                alt="" />
            <span className='del' onClick={() => delHisory(item.historyId)} >删除</span>
        </div>
    }) : null

    // 分页
    const listItems = Array.from({ length: total }, (_, index) => (
        <li onClick={() => { pageChange(index + 1) }}>{index + 1}</li>
    ));
    return (
        <>
            <label className='his-label'
                onClick={() => setIsModalOpen(true)}>历史上传记录</label>
            <Modal width={500} title="自定义脸部特征历史" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
                <div className='uphistory'>
                    <label>历史上传记录</label>
                    {historyEL}
                    <label>
                        <ul>
                            {listItems}
                            <li className='total-title'>{'共' + total + '页'}</li>
                        </ul>
                    </label>
                </div>
            </Modal>
        </>
    )
}
export default HistoryComponent