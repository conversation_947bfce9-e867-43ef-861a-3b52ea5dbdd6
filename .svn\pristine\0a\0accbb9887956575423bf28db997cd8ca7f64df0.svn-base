/**
 * @Author: wuyang
 * @Date: 2024/1/8
 * @Description: ""
 */

import cl from 'classnames'
import { useEffect, useState } from 'react'
import { NumberLogin } from '@/business-component/login/number/NumberLogin'
import { PasswordLogin } from '@/business-component/login/password/PasswordLogin'
import { Radio, RadioChangeEvent } from 'antd'
import styles from './login.module.scss'
import { userinfoService } from '@/common/services/userinfo/userinfoService'

const Login = () => {
	const options = [
		{ label: '手机验证码登录', value: 'phone' },
		{ label: '账号密码登录', value: 'password' }
	]
	const [loginType, setLoginType] = useState('phone')
	useEffect(() => {
		userinfoService.logoutByOverTime()
	}, [])

	const onChange4 = ({ target: { value } }: RadioChangeEvent) => {
		setLoginType(value)
	}

	return (
		<div className="flex w-full h-screen overflow-hidden pt-0 relative">
			{/* header */}
			<div className="absolute z-10">
				<div className={cl('mt-[20px]', 'mb-[20px]', 'flex', 'items-center')}>
					<div className={cl('pl-10')}>
						<img
							src={require('@/asset/image/logo.png')}
							alt={''}
							className={cl('h-6')}
						/>
					</div>
				</div>
			</div>
			<div className="flex-1 flex items-center justify-center flex-col relative">
				{/* banner */}
				<div className="max-w-[760px]">
					<img
						src={require('@/asset/image/banner.png')}
						alt={''}
						className="w-full h-full object-cover"
					/>
				</div>
				<div className={styles['css-5vapv']}></div>
			</div>
			<div className="w-[480px] flex items-center justify-center">
				{/* form 表单 */}
				<div className="w-[352px]">
					<div className={cl('relative')}>
						<div className="text-center">
							<Radio.Group
								options={ options }
								onChange={onChange4}
								value={loginType}
								optionType="button"
								buttonStyle="solid"
							/>
						</div>
						<div className="pt-[20px]">
							{loginType === 'phone' ? (
								<NumberLogin isContinue={false} />
							) : (
								<PasswordLogin isContinue={false}/>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
export default Login
