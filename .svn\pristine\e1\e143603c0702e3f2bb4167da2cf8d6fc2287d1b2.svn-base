/**
 * @Author: xujing
 * @Date: 2025/7/23
 * @Description: ""
 */

import { useEffect, useState } from 'react';
import { Slider, Select, Modal, Button, Dropdown, Radio, Checkbox, message, Tooltip, Switch, Spin, Input, DatePicker } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { useMemoizedFn } from 'ahooks'
import { CopyOutlined, FileTextOutlined, ScissorOutlined, BgColorsOutlined, ZoomInOutlined, QuestionCircleOutlined, AppstoreOutlined, SafetyOutlined, FontSizeOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getCommonStyleList, addBatch, getBatchDetail, uploadSynchronization, createByImageUrls, createTitleExtractionTaskByImageUrls } from '@/api/task'
import { useLocation, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import { batchZipUrl } from '@/api/common'


export const MyMaterial = () => {
    const navigate = useNavigate();
    const location = useLocation();

    return (
        <div className='h-full w-full p-[20px]'>
            我的素材
        </div >
    );
};


export default MyMaterial;




