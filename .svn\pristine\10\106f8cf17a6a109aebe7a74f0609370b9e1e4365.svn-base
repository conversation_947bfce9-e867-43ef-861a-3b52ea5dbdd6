/**
 * @Author: wuy<PERSON>
 * @Date: 2024/1/11
 * @Description: "任务列表项"
 */
import { Button, Tag } from 'antd'
import {
	ClockCircleOutlined,
	EditOutlined,
	ExclamationCircleOutlined,
	SyncOutlined
} from '@ant-design/icons'
import { ETaskStatus, ITaskListItem } from '@/types/task'
import dayjs from 'dayjs'
import {
	memo,
	MutableRefObject,
	useCallback,
	useEffect,
	useMemo,
	useState
} from 'react'
import { useMatches, useNavigate, useParams } from 'react-router-dom'
import classNames from 'classnames'
import { canDelTask } from '@/helper/utils/rights/rights'
import { ReactComponent as LoadingSvg } from '@/asset/svg/loading.svg'

interface ITaskItemProps extends ITaskListItem {
	isBatch: boolean
	batchSelectItem: MutableRefObject<string[]>
}
export const TaskItem = memo((props: ITaskItemProps) => {
	const { isBatch, batchSelectItem } = props

	const taskId = `${props.taskId}`

	const navigate = useNavigate()
	const matches = useMatches()
	const params = useParams()

	const { status, taskName, updateTime, originalUrl } = props
	// 是否批量选中状态
	const [isSelectItem, setSelectItem] = useState(false)
	useEffect(() => {
		if (!isBatch) {
			setSelectItem(false)
		}
	}, [isBatch])

	// activeItem 表示是否是我的任务选中状态
	const activeItem = useMemo(() => {
		return taskId === params.taskId
	}, [params.taskId, taskId])

	const parentPath = useMemo(() => {
		return matches?.[2]?.pathname
	}, [matches])
	const handleSelectItem = useCallback(() => {
		const isSelect = !isSelectItem
		setSelectItem(isSelect)
		if (isSelect) {
			batchSelectItem.current.push(taskId)
		} else {
			batchSelectItem.current = batchSelectItem.current.filter(
				(item) => item !== taskId
			)
		}
	}, [batchSelectItem, isSelectItem, taskId])
	//通过任务状态显示不同的状态标签
	const getTag = useMemo(() => {
		switch (status) {
			case ETaskStatus.ai:
				return (
					<Tag
						color="processing"
						icon={
							<LoadingSvg className="w-[14px] h-[14px] mr-[2px] svg-primary loading-rotate" />
						}
						className="flex items-center"
						style={{
							color: '#32649f',
							background: '#eef2ff',
							borderColor: '#32649f'
						}}
					>
						AI 准备中
					</Tag>
				)
			case ETaskStatus.execute:
				return (
					<Tag
						color="processing"
						icon={
							<LoadingSvg className="w-[14px] h-[14px] mr-[2px] svg-primary loading-rotate" />
						}
						className="flex items-center"
						style={{
							color: '#32649f',
							background: '#eef2ff',
							borderColor: '#32649f'
						}}
					>
						执行中
					</Tag>
				)
			case ETaskStatus.fail:
				return <Tag color="error">已失败</Tag>
			case ETaskStatus.queue:
				return (
					<Tag
						color="warning"
						icon={<ClockCircleOutlined />}
						style={{
							color: '#e2c0a2',
							background: '#fff7f0',
							borderColor: '#f5e6d8'
						}}
					>
						排队中
					</Tag>
				)
			case ETaskStatus.success:
				return (
					<Tag
						color="success"
						style={{
							color: '#acacac',
							background: '#ffffff',
							borderColor: '#acacac'
						}}
					>
						已完成
					</Tag>
				)
			default:
				return (
					<Tag
						color="default"
						icon={<EditOutlined />}
						style={{
							color: '#32649f',
							background: '#ffffff',
							borderColor: '#32649f'
						}}
					>
						编辑中
					</Tag>
				)
		}
	}, [status])
	//点击任务列表跳转子页面
	const handleClickItem = useCallback(() => {
		navigate(`${parentPath}/${taskId}/task`)
		//console.log('taskId', taskId);
		window.localStorage.setItem('Now_taskId', taskId);//暂存任内id以便取用，后去迁移至jotai？状态管理

	}, [navigate, parentPath, taskId])

	return (
		<div className={'mt-[10px] rounded-lg relative overflow-hidden'}>
			<div
				className={classNames(
					'border border-normal rounded-lg p-[10px] flex cursor-pointer items-center hover:border-primary',
					activeItem ? 'border-primary bg-[#EEF2FF]' : ''
				)}
				onClick={handleClickItem}
			>
				<div
					className={
						'w-[45px] h-[45px] rounded overflow-hidden object-fill flex justify-center items-center bg-normal'
					}
				>
					{originalUrl ? (
						<img src={originalUrl} alt="" className={'w-full'} />
					) : (
						<div></div>
					)}
				</div>
				<div className={'flex-1 ml-[10px] overflow-hidden'}>
					<div className={'text-[14px] text-normal truncate w-full'}>
						{taskName}
					</div>
					<div className={'flex justify-between mt-[4px] items-center'}>
						<div className={'text-[12px] text-subtext'}>
							{dayjs(updateTime).format('YYYY-MM-DD')}
						</div>
						<div>{getTag}</div>
					</div>
				</div>
			</div>
			{isBatch ? (
				<div
					className={
						'absolute top-0 left-0 w-full h-full z-10 bg-black/[.6] flex justify-center items-center'
					}
				>
					{canDelTask(status) ? (
						<Button
							type={isSelectItem ? 'primary' : undefined}
							className={'w-[100px]'}
							onClick={handleSelectItem}
						>
							{isSelectItem ? '取消' : '选择'}
						</Button>
					) : (
						<div></div>
					)}
				</div>
			) : null}
		</div>
	)
})
