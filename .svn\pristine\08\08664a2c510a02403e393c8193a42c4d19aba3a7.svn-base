/**
 * @Author: wuyang
 * @Date: 2023/12/21
 * @Description: ""
 */
import { atom } from 'jotai'
import {
	ILayerDrawingContext,
	TDrawBarAction
} from '@/component/layer-drawing/types'
import { createContext, useContextSelector } from 'use-context-selector'

export const LayerDrawingContext = createContext<ILayerDrawingContext>({})
export const useDrawingManager = () =>
	useContextSelector(LayerDrawingContext, (state) => state.dm)

export const drawBarActionType = atom<TDrawBarAction>('gripper')
// 缩放比例 默认是 1
export const scaleBearingSize = atom(1)
export const bearingLeft = atom<string | number>('50%')
export const bearingTop = atom<string | number>('50%')

export const setScaleBearingSize = atom(null, (get, set, number: number) => {
	set(scaleBearingSize, number)
})

export const renderScaleBearingSize = atom(
	(get) => Math.round(get(scaleBearingSize) * 100) + '%'
)
export const zoomInScaleBearingSize = atom(null, (get, set, args) => {
	const current = get(scaleBearingSize) - 0.1
	if (current > 0) {
		set(scaleBearingSize, current)
	}
})
export const zoomOutScaleBearingSize = atom(null, (get, set, args) => {
	const current = get(scaleBearingSize) + 0.1
	if (current < 3) {
		set(scaleBearingSize, current)
	}
})
