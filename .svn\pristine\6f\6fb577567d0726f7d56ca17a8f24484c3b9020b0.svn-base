
import { useEffect, useState } from 'react';
import { Button, Checkbox, message, Pagination, Spin } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { DoubleRightOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import dayjs from 'dayjs'
import { useLocation } from 'react-router-dom';
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import {
    getOnePage
} from '@/api/task'
import { batchZipUrl } from '@/api/common'

import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const ExtractDetail = () => {
    const location = useLocation();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [messageApi, messageContextHolder] = message.useMessage()
    // 模拟图片数据
    const [imageList, setImageList] = useState<any>([
        {
            image: [
            ],
            orgImg: '',
            smallOrgImg: ''
        },
    ])

    const { batchId } = location.state || {};
    const [record, setRecord] = useState<any>();
    const [detailTotal, setDetailTotal] = useState(0);
    const [pageLoading, setPageLoading] = useState(false);
    const fetchDetail = (pageNum: number, pageSize: number,) => {
        setPageLoading(true)
        getOnePage({ batchId, pageNum, pageSize }).then((res: any) => {
            setRecord(res)
            setDetailTotal(res.total)
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }
    useEffect(() => {
        fetchDetail(1, 50)
    }, []);

    useEffect(() => {
        if (record?.data) {
            const listData = record.data.map((item: any) => {
                const results = item?.taskOrdinalList?.[0]?.ordinalImgResultList || [];
                const image = (results || []).slice(0, 4).map((res: any) => ({
                    small: res?.resSmallImgUrl,
                    taskImage: res?.resImgUrl,
                    hasUploaded: res?.hasUploaded,
                })).filter((it: any) => it?.taskImage);
                return {
                    image,
                    orgImg: item.originalUrl,
                    smallOrgImg: item.thumbnailUrl,
                }
            })
            setImageList(listData || [])
        }
    }, [record]);

    const [selectedImages, setSelectedImages] = useState<string[]>([]);
    const handleSelect = (imageUrl: string) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(imageUrl)
                ? prev.filter(url => url !== imageUrl)
                : [...prev, imageUrl];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions);
        if (!showBatchActions === false) {
            setSelectedImages([]); // 清空已选图片
        }
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };
    //实时更新选择状态
    useEffect(() => {
        setShowBatchActions(selectedImages.length > 0);
    }, [selectedImages]);


    // 相关功能已移至 BottomActionBar 组件

    // 原图图片列表渲染
    const getOriImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                >
                    <LikeImage
                        type={1}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={image.orgImg}
                        oriImgUrl={image.orgImg}
                        smallImgUrl={image.smallOrgImg || image.orgImg}
                        markImgUrl={image.orgImg}
                        progress={100}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    // 结果图图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (item: any, index: number) => {
            let previewImageList = item.image.map(
                (result: any) => result.taskImage

            )
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                >
                    <LikeImage
                        type={1}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={item.image[index].taskImage}
                        oriImgUrl={item.orgImg}
                        smallImgUrl={item.image[index].small}
                        markImgUrl={item.orgImg}
                        progress={100}
                        previewImages={previewImageList}
                        index={index}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={true}
                    />
                </div>
            )
        }
    )
    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        setDownloadLoading(true)
        batchZipUrl({ imageUrls: selectedImages, type: 52 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false)
        })
        // batchDownloadImages(selectedImages);
    };

    const [detailPage, setDetailPage] = useState(1);
    const handleDetailPage = (page: number, pageSize: number) => {
        setDetailPage(page);  // 更新页码状态
        fetchDetail(page, pageSize)
    }


    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : (<>



                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}  >
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center justify-start h-[60px] border-b-[1px] border-normal'>
                    <p className='mr-[20px]'>批次: {record?.batch?.batchNumber}</p>
                    <p className='mr-[20px]'>创建时间：{dayjs(record?.batch?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    {record?.data[0] && <p className='mr-[20px]'>比例尺寸：{JSON.parse(record?.data[0].taskOrdinalList[0].taskParam).imageScale}</p>}
                    <p>总数：{record?.batch?.totalAmount}
                        <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.batch?.successAmount}</span>
                        {record?.batch?.failAmount > 0 && <span style={{ color: '#CF1322', marginLeft: '6px' }}>失败：{record?.batch?.failAmount}</span>}
                    </p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className={`flex flex-wrap pt-[10px] ${JSON.parse(record?.data[0]?.taskOrdinalList?.[0]?.taskParam || '{}')?.imageNumber > 2 ? 'w-[100%]' : 'grid grid-cols-2 gap-3'}`}>
                        {
                            imageList.map((item: any, index: number) => {
                                return (
                                    <div className='aspect-square h-[300px] mb-[15px] w-[100%] pr-[12px] p-2 relative bg-[#d5d5d5] '>
                                        {(() => {
                                            const enabledImages = item.image.filter((img: any) => !checkImageDisabled(img, 'extract'))
                                            const enabledTaskImages = enabledImages.map((img: any) => img.taskImage)
                                            const allEnabledChecked = enabledImages.length > 0 && enabledImages.every((img: any) => selectedImages.includes(img.taskImage))
                                            return (
                                                <Checkbox
                                                    className="itemCheckbox-similar"
                                                    checked={allEnabledChecked}
                                                    disabled={enabledImages.length === 0}
                                                    onChange={(e) => {
                                                        if (e.target.checked) {
                                                            setSelectedImages(prev => {
                                                                const newArray = [...prev, ...enabledTaskImages]
                                                                return Array.from(new Set(newArray))
                                                            })
                                                        } else {
                                                            setSelectedImages(prev => prev.filter(img => !enabledTaskImages.includes(img)))
                                                        }
                                                    }}
                                                />
                                            )
                                        })()}
                                        <div className='left' style={{ width: '300px', display: 'inline-block' }}>
                                            <label htmlFor="" className='text-[#fff]' style={{ fontSize: '24px', position: 'absolute', top: '10px', left: '60px' }}>原图</label>
                                            <div className="w-full h-full flex min-h-[300px] h-[300px] pt-[20px] rounded-lg items-center justify-center  relative group overflow-hidden"
                                                style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                <div className='max-w-[240px] aspect-square pr-[12px]  p-2 inline-block' >
                                                    <div className='w-full h-full flex min-h-[200px] rounded-lg items-center justify-center  bg-[#eef2ff] overflow-hidden'>
                                                        {getOriImageComponent(item)}
                                                    </div>
                                                </div>
                                                <div className='rightIcon' style={{ fontSize: '30px', width: '40px', textAlign: 'center' }}>
                                                    <DoubleRightOutlined />
                                                </div>
                                            </div>
                                        </div>
                                        <div className='right' style={{ width: 'calc(100% - 300px)', display: 'inline-flex' }}>
                                            <label htmlFor="" className='text-[#fff]' style={{ fontSize: '24px', position: 'absolute', top: '10px', left: '340px' }}>生成图</label>
                                            {item.image.map((imageUrl: any, index: number) => (
                                                <div className={'max-w-[240px]  aspect-square  pr-[12px] p-2 ' + ((item?.image?.length || 0) === 1 ? 'w-[100%]' : (item?.image?.length || 0) === 2 ? 'w-[50%]' : (item?.image?.length || 0) === 4 ? 'w-[25%]' : 'w-[25%]')}>
                                                    <div key={index} className="w-full h-full flex min-h-[200px] rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden"
                                                        style={{ flexDirection: 'row' }}
                                                    >
                                                        <Checkbox
                                                            className="absolute top-4 left-4 z-10 "
                                                            style={{ transform: 'scale(1.25)' }}  // 放大1.5倍
                                                            checked={selectedImages.includes(imageUrl.taskImage)}
                                                            onChange={() => handleSelect(imageUrl.taskImage)}
                                                            disabled={checkImageDisabled(imageUrl, 'extract')}
                                                        />
                                                        {imageUrl.hasUploaded && <p className="absolute bottom-4  z-10"
                                                            style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                                        {getTaskImageComponent(item, index)}

                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )
                            })
                        }
                    </div>
                    {/* <Pagination align="center" style={{ margin: '20px 0 120px' }} current={detailPage} pageSize={50} onChange={handleDetailPage} total={detailTotal} showSizeChanger={false} /> */}

                    {/* 底部操作栏 */}
                    <BottomActionBar
                        visible={selectedImages.length > 0 || showBatchActions}
                        selectedCount={selectedImages.length}
                        isAllSelected={imageList.length > 0 && selectedImages.length === imageList.reduce((total: number, item: any) => total + item.image.filter((img: any) => !checkImageDisabled(img, 'extract')).length, 0)}
                        onToggleSelectAll={() => {
                            const enabledImagesCount = imageList.reduce((total: number, item: any) => total + item.image.filter((img: any) => !checkImageDisabled(img, 'extract')).length, 0);
                            if (imageList.length > 0 && selectedImages.length === enabledImagesCount) {
                                setSelectedImages([])
                            } else {
                                const allEnabledImages = imageList.reduce((acc: string[], item: any) => {
                                    return [...acc, ...item.image.filter((img: any) => !checkImageDisabled(img, 'extract')).map((img: any) => img.taskImage)]
                                }, [])
                                setSelectedImages([...new Set(allEnabledImages)] as string[])
                            }
                        }}
                        onCancelSelection={cancelSelection}
                        syncEnabled
                        selectedItems={selectedImages}
                        extractImageUrl={(imageUrl) => imageUrl}
                        syncExtraParams={{}}
                        onDownload={handleDownloadImgages}
                        downloadLoading={downloadLoading}
                        downloadDisabled={selectedImages.length === 0}
                        enableWorkflow={userInfo?.currentMode == 2}
                        onActionFinished={() => {
                            // 刷新积分或数据
                            userinfoService.refresh();
                            fetchDetail(detailPage, 50);
                            setSelectedImages([])
                        }}
                        actionDisabled={selectedImages.length === 0}
                    />
                </div>
            </>)}
        </div >
    );
};

export default ExtractDetail;





