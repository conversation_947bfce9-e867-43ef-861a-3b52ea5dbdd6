/**
 * @Author: xujing
 * @Date: 2025/3/3
 * @Description: ""
 */
import { useMemo, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { DataXHeader } from '@/business-component/data-x-header/DataXHeader';
import { UploadImage } from '@/pages/batchTools/uploadImage/index'
import { UploadImageDetail } from '@/pages/batchTools/uploadImage/detail'
import { BatchToolsContinuous } from '@/pages/batchTools/continuous/index'
import { ContinuousDetail } from '@/pages/batchTools/continuous/detail'
import { BatchToolsTextToImg } from '@/pages/batchTools/textToImg/index'
import { TextToImgDetail } from '@/pages/batchTools/textToImg/detail'
import { BatchToolsSimilar } from '@/pages/batchTools/similar/index'
import { SimilarDetail } from '@/pages/batchTools/similar/detail'
import { BatchToolsCut } from '@/pages/batchTools/cut/index'
import { CutDetail } from '@/pages/batchTools/cut/detail'
import { BatchToolsBlockingOut } from '@/pages/batchTools/blockingOut/index'
import { BlockingOutDetail } from '@/pages/batchTools/blockingOut/detail'
import { BatchToolsClear } from '@/pages/batchTools/clear/index'
import { ClearDetail } from '@/pages/batchTools/clear/detail'
import { BatchToolsGather } from '@/pages/batchTools/gather/index'
import { BatchToolsFilter } from '@/pages/batchTools/filter/index'
import { FilterDetail } from '@/pages/batchTools/filter/detail'
import { BatchToolsTitleExtraction } from '@/pages/batchTools/titleExtraction/index'
import { TitleExtractionDetail } from '@/pages/batchTools/titleExtraction/detail'
import { BatchToolsExtract } from '@/pages/batchTools/extract/index'
import { ExtractDetail } from '@/pages/batchTools/extract/detail'


export const BatchTools = () => {
    const navigate = useNavigate();

    useEffect(() => {
        // You can handle side effects here if needed.
    }, []);

    const navs = useMemo(() => {
        return [
            {
                title: '图片上传',
                id: 1,
                path: 'uploadImage'
            },
            {
                title: '文生图',
                id: 2,
                path: 'textToImg'
            },
            {
                title: '相似图裂变',
                id: 3,
                path: 'similar'
            },
            {
                title: '平铺图生成',
                id: 7,
                path: 'continuous'
            },
            {
                title: '图案裁剪',
                id: 4,
                path: 'cut'
            },
            {
                title: '图片去背景',
                id: 5,
                path: 'blockingOut'
            },
            {
                title: '图片变清晰',
                id: 6,
                path: 'clear'
            },
            {
                title: '印花图提取',
                id: 11,
                path: 'extract'
            },
            {
                title: '数据采集',
                id: 8,
                path: 'gather'
            },
            {
                title: '侵权风险过滤',
                id: 9,
                path: 'filter'
            },
            {
                title: '标题提取',
                id: 10,
                path: 'titleExtraction'
            },
        ];
    }, []);


    const location = useLocation()
    const currentPath = location.pathname;
    // 初始化 activeNav 状态
    const [activeNav, setActiveNav] = useState(() => {

        if (
            currentPath === '/workspace/batchTools' ||
            currentPath === '/workspace/batchTools/uploadImage'
        ) {
            return navs.find(nav => nav.path === 'uploadImage');
        }
        return navs.find(nav => currentPath.includes(nav.path));
    });

    const handleNavClick = (nav: any) => {
        setActiveNav(nav); // Set active navigation
        navigate(`/workspace/batchTools/${nav.path}/index`); // Update the route
    };

    return (
        <div className="bg-normal flex flex-col min-h-[100vh] h-screen overflow-hidden">
            <DataXHeader />
            <div className={'p-[14px] h-full box-border'}>
                <div className={'bg-white rounded-xl flex overflow-hidden h-full'}>
                    <div className={'w-[86px] border-r-[1px]  h-[calc(100vh-102px)] overflow-y-scroll scrollbar-container scrollbar-hide border-normal min-w-[86px]'}>
                        <div className={'text-center'}>
                            {navs.map((nav) => (
                                <div
                                    className={'mt-[14px] flex flex-col items-center cursor-pointer'}
                                    key={nav.id}
                                    onClick={() => handleNavClick(nav)} // Handle click event
                                >
                                    <img
                                        className={'w-[48px] h-[48px] rounded-full overflow-hidden'}
                                        src={
                                            activeNav?.id === nav.id
                                                ? require(`@/asset/icon/${nav.path}-active.webp`)
                                                : require(`@/asset/icon/${nav.path}.webp`)
                                        }
                                        alt={nav.title}
                                    />
                                    <div className={'text-normal mt-[6px] text-[12px]'}>
                                        {nav.title}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="flex-1">
                        {/* The content corresponding to the active navigation */}

                        {activeNav?.id === 1 && currentPath.includes('index') && <UploadImage />}
                        {activeNav?.id === 1 && currentPath.includes('detail') && <UploadImageDetail />}
                        {activeNav?.id === 2 && currentPath.includes('index') && <BatchToolsTextToImg />}
                        {activeNav?.id === 2 && currentPath.includes('detail') && <TextToImgDetail />}
                        {activeNav?.id === 3 && currentPath.includes('index') && <BatchToolsSimilar />}
                        {activeNav?.id === 3 && currentPath.includes('detail') && <SimilarDetail />}
                        {activeNav?.id === 4 && currentPath.includes('index') && <BatchToolsCut />}
                        {activeNav?.id === 4 && currentPath.includes('detail') && <CutDetail />}
                        {activeNav?.id === 5 && currentPath.includes('index') && <BatchToolsBlockingOut />}
                        {activeNav?.id === 5 && currentPath.includes('detail') && <BlockingOutDetail />}
                        {activeNav?.id === 6 && currentPath.includes('index') && <BatchToolsClear />}
                        {activeNav?.id === 6 && currentPath.includes('detail') && <ClearDetail />}
                        {activeNav?.id === 7 && currentPath.includes('index') && <BatchToolsContinuous />}
                        {activeNav?.id === 7 && currentPath.includes('detail') && <ContinuousDetail />}
                        {activeNav?.id === 8 && currentPath.includes('index') && <BatchToolsGather />}
                        {activeNav?.id === 9 && currentPath.includes('index') && <BatchToolsFilter />}
                        {activeNav?.id === 9 && currentPath.includes('detail') && <FilterDetail />}
                        {activeNav?.id === 10 && currentPath.includes('index') && <BatchToolsTitleExtraction />}
                        {activeNav?.id === 10 && currentPath.includes('detail') && <TitleExtractionDetail />}
                        {activeNav?.id === 11 && currentPath.includes('index') && <BatchToolsExtract />}
                        {activeNav?.id === 11 && currentPath.includes('detail') && <ExtractDetail />}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BatchTools;




