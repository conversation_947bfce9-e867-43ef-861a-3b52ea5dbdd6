<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.RiskDetectionTaskMapper">

    <resultMap type="com.dataxai.domain.RiskDetectionTask" id="RiskDetectionTaskResult">
        <result property="id"    column="id"    />
        <result property="taskBatch"    column="task_batch"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="successAmount"    column="success_amount"    />
        <result property="failAmount"    column="fail_amount"    />
        <result property="status"    column="status"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="workflowNodeExecutionId"    column="workflow_node_execution_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRiskDetectionTaskVo">
        select id, task_batch, total_amount, success_amount, fail_amount, status, owner_id, team_id, workflow_node_execution_id, create_time, update_time, remark from risk_detection_task
    </sql>

    <select id="selectRiskDetectionTaskList" parameterType="com.dataxai.domain.RiskDetectionTask" resultMap="RiskDetectionTaskResult">
        <include refid="selectRiskDetectionTaskVo"/>
        <where>
            <if test="startTime != null and startTime != ''">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and create_time &lt;= #{endTime}
            </if>
            <if test="status != null "> and status = #{status}</if>
            <if test="teamId != null and  ownerId ==null" >
                <!-- 团队模式：检查 is_admin 权限 -->
                and team_id = #{teamId}  <!-- 直接属于该团队的任务 -->
<!--                OR(team_id IS NULL  &lt;!&ndash; 不属于任何团队的任务 &ndash;&gt;-->
<!--                AND(-->
<!--                &lt;!&ndash; 情况1：当前用户是管理员，显示所有数据 &ndash;&gt;-->
<!--                EXISTS (SELECT 1 FROM t_team_user WHERE team_id = #{teamId} AND user_id = #{ownerId}  &lt;!&ndash; 当前用户 &ndash;&gt;-->
<!--                AND is_admin = 1)OR-->
<!--                &lt;!&ndash; 情况2：当前用户不是管理员，只显示自己的数据 &ndash;&gt;-->
<!--                (EXISTS (SELECT 1 FROM t_team_user WHERE team_id = #{teamId} AND user_id = #{ownerId} AND is_admin = 0)AND owner_id = #{ownerId}  &lt;!&ndash; 仅自己的数据 &ndash;&gt;-->
<!--                ))))-->
            </if>
            <if test="teamId !=null and  ownerId != null">
                and owner_id = #{ownerId} and team_id = #{teamId}
            </if>
<!--            <if test="teamId != null and teamId != 0 " >-->
<!--                &lt;!&ndash; 团队模式：直接通过team_id过滤 &ndash;&gt;-->
<!--                and (team_id = #{teamId} OR (team_id IS NULL AND-->
<!--&#45;&#45;                 owner_id IN (-->
<!--&#45;&#45;                     SELECT user_id FROM t_user WHERE team_id = #{teamId} AND del_flag = 0-->
<!--                )))-->
<!--            </if>-->

            <if test="ownerId != null and (teamId == null or teamId==0)">
                <!-- 个人模式：通过owner_id过滤 -->
                and owner_id = #{ownerId}
            </if>
            <if test="taskBatch != null and taskBatch != ''"> and task_batch like concat('%', #{taskBatch}, '%')</if>
            <if test="remark != null and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectRiskDetectionTaskById" parameterType="Long" resultMap="RiskDetectionTaskResult">
        <include refid="selectRiskDetectionTaskVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRiskDetectionTask" parameterType="RiskDetectionTask" useGeneratedKeys="true" keyProperty="id">
        insert into risk_detection_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskBatch != null and taskBatch != ''">task_batch,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="successAmount != null">success_amount,</if>
            <if test="failAmount != null">fail_amount,</if>
            <if test="status != null">status,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="workflowNodeExecutionId != null">workflow_node_execution_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskBatch != null and taskBatch != ''">#{taskBatch},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="successAmount != null">#{successAmount},</if>
            <if test="failAmount != null">#{failAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="workflowNodeExecutionId != null">#{workflowNodeExecutionId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRiskDetectionTask" parameterType="RiskDetectionTask">
        update risk_detection_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskBatch != null and taskBatch != ''">task_batch = #{taskBatch},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="successAmount != null">success_amount = #{successAmount},</if>
            <if test="failAmount != null">fail_amount = #{failAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="workflowNodeExecutionId != null">workflow_node_execution_id = #{workflowNodeExecutionId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRiskDetectionTaskById" parameterType="Long">
        delete from risk_detection_task where id = #{id}
    </delete>

    <delete id="deleteRiskDetectionTaskByIds" parameterType="String">
        delete from risk_detection_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="incrementSuccessAmount" parameterType="Long">
        update risk_detection_task
        set success_amount = IFNULL(success_amount, 0) + 1,
            update_time = now()
        where id = #{id}
    </update>

    <update id="incrementFailAmount" parameterType="Long">
        update risk_detection_task
        set fail_amount = IFNULL(fail_amount, 0) + 1,
            update_time = now()
        where id = #{id}
    </update>

    <!-- 根据工作流节点执行记录ID查询风险检测任务列表 -->
    <select id="selectByWorkflowNodeExecutionId" parameterType="Long" resultMap="RiskDetectionTaskResult">
        <include refid="selectRiskDetectionTaskVo"/>
        where workflow_node_execution_id = #{workflowNodeExecutionId}
        order by create_time desc
    </select>
</mapper>