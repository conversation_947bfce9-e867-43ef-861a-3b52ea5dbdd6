<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.MaterialStyleHistoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.dataxai.web.domain.MaterialStyleHistory">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="material_style_id" property="materialStyleId" jdbcType="INTEGER"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, material_style_id, task_type, user_id, task_id, create_time
    </sql>

    <!-- 插入使用历史记录 -->
    <insert id="insert" parameterType="com.dataxai.web.domain.MaterialStyleHistory">
        INSERT INTO t_material_style_history (material_style_id, task_type, user_id, task_id, create_time)
        VALUES (#{materialStyleId}, #{taskType}, #{userId}, #{taskId}, #{createTime})
    </insert>

    <!-- 根据用户ID获取最近7天使用的风格素材（分页，去重） -->
    <select id="selectRecentByUser" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_material_style_history h1
        WHERE h1.user_id = #{userId}
        <if test="taskType != null">
            AND h1.task_type = #{taskType}
        </if>
        AND h1.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND h1.create_time = (
            SELECT MAX(h2.create_time)
            FROM t_material_style_history h2
            WHERE h2.user_id = h1.user_id
            AND h2.material_style_id = h1.material_style_id
            <if test="taskType != null">
                AND h2.task_type = #{taskType}
            </if>
            AND h2.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        )
        ORDER BY h1.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计用户最近7天使用的风格素材数量 -->
    <select id="countRecentByUser" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT material_style_id) 
        FROM t_material_style_history 
        WHERE user_id = #{userId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    </select>

    <!-- 获取用户最近使用的风格映射 -->
    <select id="selectUserRecentMap" resultType="java.util.Map">
        SELECT 
            material_style_id as styleId,
            MAX(id) as historyId,
            MAX(create_time) as lastUsedTime
        FROM t_material_style_history
        WHERE user_id = #{userId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        <if test="styleIds != null and styleIds.size() > 0">
            AND material_style_id IN
            <foreach collection="styleIds" item="styleId" open="(" separator="," close=")">
                #{styleId}
            </foreach>
        </if>
        GROUP BY material_style_id
    </select>

    <!-- 检查用户是否在最近7天使用过某个风格素材 -->
    <select id="checkRecentUsed" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_material_style_history 
        WHERE user_id = #{userId} 
        AND material_style_id = #{styleId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    </select>

    <!-- 删除7天前的历史记录（定时清理） -->
    <delete id="deleteOldRecords">
        DELETE FROM t_material_style_history 
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL 7 DAY)
    </delete>

    <!-- 新增：根据风格ID删除所有历史记录（用于风格被删除时清理最近使用） -->
    <delete id="deleteByStyleId">
        DELETE FROM t_material_style_history WHERE material_style_id = #{styleId}
    </delete>

</mapper>
