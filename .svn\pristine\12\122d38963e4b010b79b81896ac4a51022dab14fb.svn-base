---
description:
globs:
alwaysApply: false
---
### 内部文档与指南索引

- **API 说明**：`apiExp/`
  - 风控：[apiExp/risk.md](mdc:apiExp/risk.md)
  - 标题规范：[apiExp/title.md](mdc:apiExp/title.md)

- **工作文档**：`work-doc/`
  - 队列处理与任务流程：
    - [work-doc/任务/MaterialQueueProcessor队列处理器详细说明.md](mdc:work-doc/任务/MaterialQueueProcessor队列处理器详细说明.md)
    - [work-doc/任务/任务执行流程详细说明.md](mdc:work-doc/任务/任务执行流程详细说明.md)
  - 工作流：
    - [work-doc/工作流/工作流控制器接口文档.md](mdc:work-doc/工作流/工作流控制器接口文档.md)
    - [work-doc/工作流/工作流模块开发总结.md](mdc:work-doc/工作流/工作流模块开发总结.md)
    - [work-doc/工作流/工作流流程修改.md](mdc:work-doc/工作流/工作流流程修改.md)
  - 团队功能：
    - [work-doc/团队/团队功能接口使用文档.md](mdc:work-doc/团队/团队功能接口使用文档.md)
    - [work-doc/团队/新增团队逻辑.md](mdc:work-doc/团队/新增团队逻辑.md)
    - [work-doc/团队/需求文档实现总结.md](mdc:work-doc/团队/需求文档实现总结.md)
  - 日志分类：
    - [work-doc/日志/任务类型日志分类使用说明.md](mdc:work-doc/日志/任务类型日志分类使用说明.md)
    - [work-doc/日志/任务类型日志分类实施总结.md](mdc:work-doc/日志/任务类型日志分类实施总结.md)
    - 验证脚本（PowerShell）：[work-doc/日志/验证日志分类脚本.ps1](mdc:work-doc/日志/验证日志分类脚本.ps1)
  - 微服务迁徙：
    - [work-doc/微服务迁徙/ruoyi-微服务改造执行步骤.md](mdc:work-doc/微服务迁徙/ruoyi-微服务改造执行步骤.md)
    - [work-doc/微服务迁徙/微服务迁徙流程.md](mdc:work-doc/微服务迁徙/微服务迁徙流程.md)
  - 积分：
    - [work-doc/积分/积分接口使用文档.md](mdc:work-doc/积分/积分接口使用文档.md)
    - [work-doc/积分/积分管理优化方案.md](mdc:work-doc/积分/积分管理优化方案.md)
    - [work-doc/积分/积分逻辑验证报告.md](mdc:work-doc/积分/积分逻辑验证报告.md)

- **SQL 参考**：
  - 数据库初始化与变更脚本：[sql/](mdc:sql)

提示：需要实现某一需求时，先查阅对应的工作文档与接口说明，再定位到后端 `Controller/Service/Mapper` 与前端 `api/**` 使用处。
