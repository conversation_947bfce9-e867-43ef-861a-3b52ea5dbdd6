<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TaskOrdinalMapper">

    <resultMap type="TaskOrdinal" id="TaskOrdinalResult">
        <result property="taskOrdinalId" column="task_ordinal_id"/>
        <result property="name" column="name"/>
        <result property="taskId" column="task_id"/>
        <result property="type" column="type"/>
        <result property="currentQueue" column="current_queue"/>
        <result property="nextQueue" column="next_queue"/>
        <result property="userId" column="user_id"/>
        <result property="referedTaskId" column="refered_task_id"/>
        <result property="referedTaskOrdinalId" column="refered_task_ordinal_id"/>
        <result property="ordinal" column="ordinal"/>
        <result property="status" column="status"/>
        <result property="originImgUrl" column="origin_img_url"/>
        <result property="markImgUrl" column="mark_img_url"/>
        <result property="desType" column="des_type"/>
        <result property="shortCutDesc" column="short_cut_desc"/>
        <result property="modelPrompt" column="model_prompt"/>
        <result property="modelCharacterId" column="model_character_id"/>
        <result property="modelSex" column="model_sex"/>
        <result property="modelAge" column="model_age"/>
        <result property="modelSkin" column="model_skin"/>
        <result property="modelExpression" column="model_expression"/>
        <result property="modelTemperament" column="model_temperament"/>
        <result property="modelImgId" column="model_img_id"/>
        <result property="modelUploadUrl" column="model_upload_url"/>
        <result property="sceneType" column="scene_type"/>
        <result property="sceneTypeStr" column="scene_type_str"/>
        <result property="sceneCategoryLarge" column="scene_category_large"/>
        <result property="sceneCategorySmall" column="scene_category_small"/>
        <result property="sceneImgId" column="scene_img_id"/>
        <result property="sceneId" column="scene_id"/>
        <result property="goodsSceneCategory" column="goods_scene_category"/>
        <result property="goodsSceneCategorySub" column="goods_scene_category_sub"/>
        <result property="weight" column="weight"/>
        <result property="scenePrompt" column="scene_prompt"/>
        <result property="forward" column="forward"/>
        <result property="reverse" column="reverse"/>
        <result property="executeTime" column="execute_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="resultImgUrls" column="result_img_urls"/>
        <result property="delFlag" column="del_flag"/>
        <result property="seed" column="seed"/>
        <result property="realPerson" column="real_person"/>
        <result property="repair"    column="repair"    />
        <result property="optionalCharacter" column="optional_character"/>
        <result property="optionalCharacterIds" column="optional_character_ids"/>
        <result property="taskParam" column="task_param"/>
    </resultMap>
    <resultMap type="OrdinalImageVO" id="historyImageResult">
        <result property="taskOrdinalId" column="task_ordinal_id"/>
        <result property="taskId" column="task_id"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="originImgUrl" column="origin_img_url"/>
    </resultMap>

    <resultMap id="TaskOrdinalOrdinalImgResultResult" type="TaskOrdinal" extends="TaskOrdinalResult">
        <collection property="ordinalImgResultList" notNullColumn="sub_image_id" javaType="java.util.List"
                    resultMap="OrdinalImgResultResult"/>
    </resultMap>

    <resultMap type="OrdinalImgResult" id="OrdinalImgResultResult">
        <result property="imageId" column="sub_image_id"/>
        <result property="resImgUrl" column="sub_res_img_url"/>
        <result property="resSmallImgUrl" column="sub_res_small_img_url"/>
        <result property="follow" column="sub_follow"/>
        <result property="zan" column="sub_zan"/>
        <result property="progress" column="sub_progress"/>
        <result property="queue" column="sub_queue"/>
        <result property="taskOrdinalId" column="sub_task_ordinal_id"/>
        <result property="taskId" column="sub_task_id"/>
        <result property="userId" column="sub_user_id"/>
        <result property="delFlag" column="sub_del_flag"/>
        <result property="createTime" column="sub_create_time"/>
        <result property="updateTime" column="sub_update_time"/>
        <result property="createBy" column="sub_create_by"/>
        <result property="updateBy" column="sub_update_by"/>
        <result property="originalImgUrl" column="sub_original_img_url"/>
        <result property="seed" column="sub_seed"/>
    </resultMap>

    <sql id="selectTaskOrdinalVo">
        select task_ordinal_id,
               name,
               task_id,
               type,
               current_queue,
               next_queue,
               user_id,
               refered_task_id,
               refered_task_ordinal_id,
               ordinal,
               status,
               origin_img_url,
               mark_img_url,
               des_type,
               short_cut_desc,
               model_prompt,
               model_character_id,
               model_sex,
               model_age,
               model_skin,
               model_expression,
               model_temperament,
               model_img_id,
               model_upload_url,
               scene_type,
               scene_type_str,
               scene_category_large,
               scene_category_small,
               scene_img_id,
               scene_id,
               goods_scene_category,
               goods_scene_category_sub,
               weight,
               scene_prompt,
               forward,
               reverse,
               execute_time,
               create_time,
               update_time,
               result_img_urls,
               del_flag,
               seed,
               real_person,
               repair,
               optional_character,
               optional_character_ids,
               task_param
        from t_task_ordinal
    </sql>

    <select id="selectTaskOrdinalList" parameterType="TaskOrdinal" resultMap="TaskOrdinalResult">
        <include refid="selectTaskOrdinalVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="referedTaskId != null ">and refered_task_id = #{referedTaskId}</if>
            <if test="referedTaskOrdinalId != null ">and refered_task_ordinal_id = #{referedTaskOrdinalId}</if>
            <if test="ordinal != null ">and ordinal = #{ordinal}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="originImgUrl != null  and originImgUrl != ''">and origin_img_url = #{originImgUrl}</if>
            <if test="markImgUrl != null  and markImgUrl != ''">and mark_img_url = #{markImgUrl}</if>
            <if test="desType != null  and desType != ''">and des_type = #{desType}</if>
            <if test="shortCutDesc != null  and shortCutDesc != ''">and short_cut_desc = #{shortCutDesc}</if>
            <if test="modelPrompt != null  and modelPrompt != ''">and model_prompt = #{modelPrompt}</if>
            <if test="modelCharacterId != null ">and model_character_id = #{modelCharacterId}</if>
            <if test="modelSex != null  and modelSex != ''">and model_sex = #{modelSex}</if>
            <if test="modelAge != null  and modelAge != ''">and model_age = #{modelAge}</if>
            <if test="modelSkin != null  and modelSkin != ''">and model_skin = #{modelSkin}</if>
            <if test="modelExpression != null  and modelExpression != ''">and model_expression = #{modelExpression}</if>
            <if test="modelTemperament != null  and modelTemperament != ''">and model_temperament =
                #{modelTemperament}
            </if>
            <if test="modelImgId != null  and modelImgId != ''">and model_img_id = #{modelImgId}</if>
            <if test="modelUploadUrl != null  and modelUploadUrl != ''">and model_upload_url = #{modelUploadUrl}</if>
            <if test="sceneType != null ">and scene_type = #{sceneType}</if>
            <if test="sceneTypeStr != null  and sceneTypeStr != ''">and scene_type_str = #{sceneTypeStr}</if>
            <if test="sceneCategoryLarge != null  and sceneCategoryLarge != ''">and scene_category_large =
                #{sceneCategoryLarge}
            </if>
            <if test="sceneCategorySmall != null  and sceneCategorySmall != ''">and scene_category_small =
                #{sceneCategorySmall}
            </if>
            <if test="sceneImgId != null  and sceneImgId != ''">and scene_img_id = #{sceneImgId}</if>
            <if test="sceneId != null ">and scene_id = #{sceneId}</if>
            <if test="goodsSceneCategory != null  and goodsSceneCategory != ''">and goods_scene_category =
                #{goodsSceneCategory}
            </if>
            <if test="goodsSceneCategorySub != null  and goodsSceneCategorySub != ''">and goods_scene_category_sub =
                #{goodsSceneCategorySub}
            </if>
            <if test="weight != null ">and weight = #{weight}</if>
            <if test="scenePrompt != null  and scenePrompt != ''">and scene_prompt = #{scenePrompt}</if>
            <if test="forward != null  and forward != ''">and forward = #{forward}</if>
            <if test="reverse != null  and reverse != ''">and reverse = #{reverse}</if>
            <if test="executeTime != null ">and execute_time = #{executeTime}</if>
            <if test="resultImgUrls != null  and resultImgUrls != ''">and result_img_urls = #{resultImgUrls}</if>
            <if test="delFlag != null ">and del_flag = #{delFlag}</if>
            <if test="seed != null ">and seed = #{seed}</if>
            <if test="realPerson != null ">and real_person = #{realPerson}</if>
            <if test="repair != null ">and repair = #{repair}</if>
            <if test="optionalCharacter != null  and optionalCharacter != ''">and optional_character =
                #{optionalCharacter}
            </if>
            <if test="optionalCharacterIds != null  and optionalCharacterIds != ''">and optional_character_ids =
                #{optionalCharacterIds}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="selectTaskOrdinalByTaskOrdinalId" parameterType="String" resultMap="TaskOrdinalOrdinalImgResultResult">
        select a.task_ordinal_id,
               a.name,
               a.task_id,
               a.type,
               a.user_id,
               a.refered_task_id,
               a.refered_task_ordinal_id,
               a.ordinal,
               a.status,
               a.origin_img_url,
               a.mark_img_url,
               a.des_type,
               a.short_cut_desc,
               a.model_prompt,
               a.model_character_id,
               a.model_sex,
               a.model_age,
               a.model_skin,
               a.model_expression,
               a.model_temperament,
               a.model_img_id,
               a.model_upload_url,
               a.scene_type,
               a.scene_type_str,
               a.scene_category_large,
               a.scene_category_small,
               a.scene_img_id,
               a.scene_id,
               a.goods_scene_category,
               a.goods_scene_category_sub,
               a.weight,
               a.scene_prompt,
               a.forward,
               a.reverse,
               a.execute_time,
               a.create_time,
               a.update_time,
               a.result_img_urls,
               a.del_flag,
               a.seed,
               a.real_person,
               a.repair,
               a.optional_character,
               a.optional_character_ids,
               a.task_param,
               b.image_id          as sub_image_id,
               b.res_img_url       as sub_res_img_url,
               b.res_small_img_url as sub_res_small_img_url,
               b.follow            as sub_follow,
               b.zan               as sub_zan,
               b.progress          as sub_progress,
               b.queue             as sub_queue,
               b.task_ordinal_id   as sub_task_ordinal_id,
               b.task_id           as sub_task_id,
               b.user_id           as sub_user_id,
               b.del_flag          as sub_del_flag,
               b.create_time       as sub_create_time,
               b.update_time       as sub_update_time,
               b.create_by         as sub_create_by,
               b.update_by         as sub_update_by,
               b.original_img_url  as sub_original_img_url,
               b.seed              as sub_seed
        from t_task_ordinal a
                 left join t_ordinal_img_result b on b.task_ordinal_id = a.task_ordinal_id
        where a.task_ordinal_id = #{taskOrdinalId}
    </select>

    <select id="selectTaskOrdinalsWithResultsByTaskIds" resultMap="TaskOrdinalOrdinalImgResultResult">
        select
            a.task_ordinal_id, a.name, a.task_id, a.type, a.user_id, a.refered_task_id, a.refered_task_ordinal_id,
            a.ordinal, a.status, a.origin_img_url, a.mark_img_url, a.des_type, a.short_cut_desc, a.model_prompt,
            a.model_character_id, a.model_sex, a.model_age, a.model_skin, a.model_expression, a.model_temperament,
            a.model_img_id, a.model_upload_url, a.scene_type, a.scene_type_str, a.scene_category_large, a.scene_category_small,
            a.scene_img_id, a.scene_id, a.goods_scene_category, a.goods_scene_category_sub, a.weight, a.scene_prompt,
            a.forward, a.reverse, a.execute_time, a.create_time, a.update_time, a.result_img_urls, a.del_flag,
            a.seed, a.real_person, a.repair, a.optional_character, a.optional_character_ids, a.task_param,
            b.image_id          as sub_image_id,
            b.res_img_url       as sub_res_img_url,
            b.res_small_img_url as sub_res_small_img_url,
            b.follow            as sub_follow,
            b.zan               as sub_zan,
            b.progress          as sub_progress,
            b.queue             as sub_queue,
            b.task_ordinal_id   as sub_task_ordinal_id,
            b.task_id           as sub_task_id,
            b.user_id           as sub_user_id,
            b.del_flag          as sub_del_flag,
            b.create_time       as sub_create_time,
            b.update_time       as sub_update_time,
            b.create_by         as sub_create_by,
            b.update_by         as sub_update_by,
            b.original_img_url  as sub_original_img_url,
            b.seed              as sub_seed,
            b.res_white_img_url as res_white_img_url
        from t_task_ordinal a
        left join t_ordinal_img_result b on b.task_ordinal_id = a.task_ordinal_id and b.del_flag = 0
        where a.task_id in
        <foreach item="taskId" collection="list" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and a.del_flag = 0
        order by a.create_time asc
    </select>

    <select id="getHistoryImage" resultMap="historyImageResult">
        select
        task_id,
        original_url as origin_img_url,
        user_id,
        type
        from
        t_task
        where
            user_id = #{userId} and  original_url is not null and batch_id = 0
        and type in
        <foreach item="type" collection="types" open="(" separator="," close=")">
            #{type}
        </foreach>
        and del_flag=0
        order by create_time desc

    </select>
    <select id="selectByTaskIds" resultMap="TaskOrdinalResult">
        select
            tto.task_id ,
            tto.type,
            tto.origin_img_url ,
            tto.mark_img_url
        from
            t_task_ordinal tto
        <where>
            tto.del_flag=0
            and tto.task_id in
            <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        and tto.user_id=#{userId}
        </where>
    </select>

    <insert id="insertTaskOrdinal" parameterType="TaskOrdinal" keyProperty="taskOrdinalId">
        insert into t_task_ordinal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskOrdinalId != null">task_ordinal_id,</if>
            <if test="name != null">name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="type != null">type,</if>
            <if test="currentQueue != null">current_queue,</if>
            <if test="nextQueue != null">next_queue,</if>
            <if test="userId != null">user_id,</if>
            <if test="referedTaskId != null">refered_task_id,</if>
            <if test="referedTaskOrdinalId != null">refered_task_ordinal_id,</if>
            <if test="ordinal != null">ordinal,</if>
            <if test="status != null">status,</if>
            <if test="originImgUrl != null">origin_img_url,</if>
            <if test="markImgUrl != null">mark_img_url,</if>
            <if test="desType != null">des_type,</if>
            <if test="shortCutDesc != null">short_cut_desc,</if>
            <if test="modelPrompt != null">model_prompt,</if>
            <if test="modelCharacterId != null">model_character_id,</if>
            <if test="modelSex != null">model_sex,</if>
            <if test="modelAge != null">model_age,</if>
            <if test="modelSkin != null">model_skin,</if>
            <if test="modelExpression != null">model_expression,</if>
            <if test="modelTemperament != null">model_temperament,</if>
            <if test="modelImgId != null">model_img_id,</if>
            <if test="modelUploadUrl != null">model_upload_url,</if>
            <if test="sceneType != null">scene_type,</if>
            <if test="sceneTypeStr != null">scene_type_str,</if>
            <if test="sceneCategoryLarge != null">scene_category_large,</if>
            <if test="sceneCategorySmall != null">scene_category_small,</if>
            <if test="sceneImgId != null">scene_img_id,</if>
            <if test="sceneId != null">scene_id,</if>
            <if test="goodsSceneCategory != null">goods_scene_category,</if>
            <if test="goodsSceneCategorySub != null">goods_scene_category_sub,</if>
            <if test="weight != null">weight,</if>
            <if test="scenePrompt != null">scene_prompt,</if>
            <if test="forward != null">forward,</if>
            <if test="reverse != null">reverse,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="resultImgUrls != null">result_img_urls,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="seed != null">seed,</if>
            <if test="realPerson != null">real_person,</if>
            <if test="repair != null">repair,</if>
            <if test="optionalCharacter != null">optional_character,</if>
            <if test="optionalCharacterIds != null">optional_character_ids,</if>
            <if test="taskParam != null">task_param,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskOrdinalId != null">#{taskOrdinalId},</if>
            <if test="name != null">#{name},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="type != null">#{type},</if>
            <if test="currentQueue != null">#{currentQueue},</if>
            <if test="nextQueue != null">#{nextQueue},</if>
            <if test="userId != null">#{userId},</if>
            <if test="referedTaskId != null">#{referedTaskId},</if>
            <if test="referedTaskOrdinalId != null">#{referedTaskOrdinalId},</if>
            <if test="ordinal != null">#{ordinal},</if>
            <if test="status != null">#{status},</if>
            <if test="originImgUrl != null">#{originImgUrl},</if>
            <if test="markImgUrl != null">#{markImgUrl},</if>
            <if test="desType != null">#{desType},</if>
            <if test="shortCutDesc != null">#{shortCutDesc},</if>
            <if test="modelPrompt != null">#{modelPrompt},</if>
            <if test="modelCharacterId != null">#{modelCharacterId},</if>
            <if test="modelSex != null">#{modelSex},</if>
            <if test="modelAge != null">#{modelAge},</if>
            <if test="modelSkin != null">#{modelSkin},</if>
            <if test="modelExpression != null">#{modelExpression},</if>
            <if test="modelTemperament != null">#{modelTemperament},</if>
            <if test="modelImgId != null">#{modelImgId},</if>
            <if test="modelUploadUrl != null">#{modelUploadUrl},</if>
            <if test="sceneType != null">#{sceneType},</if>
            <if test="sceneTypeStr != null">#{sceneTypeStr},</if>
            <if test="sceneCategoryLarge != null">#{sceneCategoryLarge},</if>
            <if test="sceneCategorySmall != null">#{sceneCategorySmall},</if>
            <if test="sceneImgId != null">#{sceneImgId},</if>
            <if test="sceneId != null">#{sceneId},</if>
            <if test="goodsSceneCategory != null">#{goodsSceneCategory},</if>
            <if test="goodsSceneCategorySub != null">#{goodsSceneCategorySub},</if>
            <if test="weight != null">#{weight},</if>
            <if test="scenePrompt != null">#{scenePrompt},</if>
            <if test="forward != null">#{forward},</if>
            <if test="reverse != null">#{reverse},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="resultImgUrls != null">#{resultImgUrls},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="seed != null">#{seed},</if>
            <if test="realPerson != null">#{realPerson},</if>
            <if test="repair != null">#{repair},</if>
            <if test="optionalCharacter != null">#{optionalCharacter},</if>
            <if test="optionalCharacterIds != null">#{optionalCharacterIds},</if>
            <if test="taskParam != null">#{taskParam},</if>
        </trim>
    </insert>

    <update id="updateTaskOrdinal" parameterType="TaskOrdinal">
        update t_task_ordinal
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="currentQueue != null">current_queue = #{currentQueue},</if>
            <if test="nextQueue != null">next_queue = #{nextQueue},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="referedTaskId != null">refered_task_id = #{referedTaskId},</if>
            <if test="referedTaskOrdinalId != null">refered_task_ordinal_id = #{referedTaskOrdinalId},</if>
            <if test="ordinal != null">ordinal = #{ordinal},</if>
            <if test="status != null">status = #{status},</if>
            <if test="originImgUrl != null">origin_img_url = #{originImgUrl},</if>
            <if test="markImgUrl != null">mark_img_url = #{markImgUrl},</if>
            <if test="desType != null">des_type = #{desType},</if>
            <if test="shortCutDesc != null">short_cut_desc = #{shortCutDesc},</if>
            <if test="modelPrompt != null">model_prompt = #{modelPrompt},</if>
            <if test="modelCharacterId != null">model_character_id = #{modelCharacterId},</if>
            <if test="modelSex != null">model_sex = #{modelSex},</if>
            <if test="modelAge != null">model_age = #{modelAge},</if>
            <if test="modelSkin != null">model_skin = #{modelSkin},</if>
            <if test="modelExpression != null">model_expression = #{modelExpression},</if>
            <if test="modelTemperament != null">model_temperament = #{modelTemperament},</if>
            <if test="modelImgId != null">model_img_id = #{modelImgId},</if>
            <if test="modelUploadUrl != null">model_upload_url = #{modelUploadUrl},</if>
            <if test="sceneType != null">scene_type = #{sceneType},</if>
            <if test="sceneTypeStr != null">scene_type_str = #{sceneTypeStr},</if>
            <if test="sceneCategoryLarge != null">scene_category_large = #{sceneCategoryLarge},</if>
            <if test="sceneCategorySmall != null">scene_category_small = #{sceneCategorySmall},</if>
            <if test="sceneImgId != null">scene_img_id = #{sceneImgId},</if>
            <if test="sceneId != null">scene_id = #{sceneId},</if>
            <if test="goodsSceneCategory != null">goods_scene_category = #{goodsSceneCategory},</if>
            <if test="goodsSceneCategorySub != null">goods_scene_category_sub = #{goodsSceneCategorySub},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="scenePrompt != null">scene_prompt = #{scenePrompt},</if>
            <if test="forward != null">forward = #{forward},</if>
            <if test="reverse != null">reverse = #{reverse},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="resultImgUrls != null">result_img_urls = #{resultImgUrls},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="seed != null">seed = #{seed},</if>
            <if test="realPerson != null">real_person = #{realPerson},</if>
            <if test="repair != null">repair= #{repair},</if>
            <if test="optionalCharacter != null">optional_character = #{optionalCharacter},</if>
            <if test="optionalCharacterIds != null">optional_character_ids = #{optionalCharacterIds},</if>
            <if test="taskParam != null">task_param = #{taskParam},</if>
        </trim>
        where task_ordinal_id = #{taskOrdinalId}
    </update>
    <update id="updateOrdinalUrlSub">
        UPDATE t_task_ordinal
        SET origin_img_url =
                CASE
                    WHEN origin_img_url LIKE CONCAT(#{originImgUrl}, ',%') THEN SUBSTRING(origin_img_url, LENGTH(#{originImgUrl}) + 2)
                    WHEN origin_img_url = #{originImgUrl} THEN ''
                    WHEN origin_img_url LIKE CONCAT('%', #{originImgUrl}, ',%') THEN REPLACE(origin_img_url, CONCAT(#{originImgUrl}, ','), '')
                    WHEN origin_img_url LIKE CONCAT('%', ',', #{originImgUrl}) THEN LEFT(origin_img_url, LENGTH(origin_img_url) - LENGTH(#{originImgUrl}) - 1)
            WHEN RIGHT(origin_img_url, LENGTH(#{originImgUrl})) = #{originImgUrl} THEN LEFT(origin_img_url, LENGTH(origin_img_url) - LENGTH(#{originImgUrl}))
            ELSE origin_img_url
        END
        WHERE task_ordinal_id = #{taskOrdinalId};
    </update>
    <update id="updateTaskOrdinalStatus" parameterType="TaskOrdinal">
        update t_task_ordinal SET status=#{status} where task_id =#{taskId}
    </update>

    <delete id="deleteTaskOrdinalByTaskOrdinalId" parameterType="String">
        delete
        from t_task_ordinal
        where task_ordinal_id = #{taskOrdinalId}
    </delete>

    <delete id="deleteTaskOrdinalByTaskOrdinalIds" parameterType="String">
        delete from t_task_ordinal where task_ordinal_id in
        <foreach item="taskOrdinalId" collection="array" open="(" separator="," close=")">
            #{taskOrdinalId}
        </foreach>
    </delete>

    <delete id="deleteOrdinalImgResultByTaskOrdinalIds" parameterType="String">
        delete from t_ordinal_img_result where task_ordinal_id in
        <foreach item="taskOrdinalId" collection="array" open="(" separator="," close=")">
            #{taskOrdinalId}
        </foreach>
    </delete>

    <delete id="deleteOrdinalImgResultByTaskOrdinalId" parameterType="String">
        delete
        from t_ordinal_img_result
        where task_ordinal_id = #{taskOrdinalId}
    </delete>

    <insert id="batchOrdinalImgResult">
        insert into t_ordinal_img_result( image_id, res_img_url, res_small_img_url, follow, zan, progress, queue,
        task_ordinal_id, task_id, user_id, del_flag, create_time, update_time, create_by, update_by, original_img_url,
        seed) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.imageId}, #{item.resImgUrl}, #{item.resSmallImgUrl}, #{item.follow}, #{item.zan}, #{item.progress},
            #{item.queue}, #{item.taskOrdinalId}, #{item.taskId}, #{item.userId}, #{item.delFlag}, #{item.createTime},
            #{item.updateTime}, #{item.createBy}, #{item.updateBy}, #{item.originalImgUrl}, #{item.seed})
        </foreach>
    </insert>
    <select id="getTaskOrdinalByPush" resultMap="TaskOrdinalResult">
        select a.task_ordinal_id,
               a.name,
               a.task_id,
               a.type,
               a.user_id,
               a.refered_task_id,
               a.refered_task_ordinal_id,
               a.ordinal,
               a.status,
               a.origin_img_url,
               a.mark_img_url,
               a.des_type,
               a.short_cut_desc,
               a.model_prompt,
               a.model_character_id,
               a.model_sex,
               a.model_age,
               a.model_skin,
               a.model_expression,
               a.model_temperament,
               a.model_img_id,
               a.model_upload_url,
               a.scene_type,
               a.scene_type_str,
               a.scene_category_large,
               a.scene_category_small,
               a.scene_img_id,
               a.scene_id,
               a.goods_scene_category,
               a.goods_scene_category_sub,
               a.weight,
               a.scene_prompt,
               a.forward,
               a.reverse,
               a.execute_time,
               a.create_time,
               a.update_time,
               a.result_img_urls,
               a.del_flag,
               a.seed,
               a.real_person,
               a.repair,
               a.optional_character,
               a.optional_character_ids,
               a.task_param,
               tp.id as pushId
        from t_task_ordinal a
        join t_task_push tp on tp.task_id = a.task_id
        where tp.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="getTaskOrdinalByTask" resultMap="TaskOrdinalResult">
        select a.task_ordinal_id,
        a.name,
        a.task_id,
        a.type,
        a.user_id,
        a.refered_task_id,
        a.refered_task_ordinal_id,
        a.ordinal,
        a.status,
        a.origin_img_url,
        a.mark_img_url,
        a.des_type,
        a.short_cut_desc,
        a.model_prompt,
        a.model_character_id,
        a.model_sex,
        a.model_age,
        a.model_skin,
        a.model_expression,
        a.model_temperament,
        a.model_img_id,
        a.model_upload_url,
        a.scene_type,
        a.scene_type_str,
        a.scene_category_large,
        a.scene_category_small,
        a.scene_img_id,
        a.scene_id,
        a.goods_scene_category,
        a.goods_scene_category_sub,
        a.weight,
        a.scene_prompt,
        a.forward,
        a.reverse,
        a.execute_time,
        a.create_time,
        a.update_time,
        a.result_img_urls,
        a.del_flag,
        a.seed,
        a.real_person,
        a.repair,
        a.optional_character,
        a.optional_character_ids,
        a.task_param
        from t_task_ordinal a
        where a.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="selectTaskOrdinalListInfo" parameterType="TaskOrdinal" resultMap="TaskOrdinalResult">
        select * from t_task_ordinal   a
        left join t_ordinal_img_result b


    </select>

    <delete id="delTaskPush" parameterType="String">
        delete from t_task_push where id in
        <foreach item="pushId" collection="array" open="(" separator="," close=")">
            #{pushId}
        </foreach>
    </delete>
</mapper>
