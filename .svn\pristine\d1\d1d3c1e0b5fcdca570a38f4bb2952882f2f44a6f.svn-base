---
description:
globs:
alwaysApply: false
---
### 前端管理端（ruoyi-ui/admin）

- **入口与构建**：
  - 入口 HTML：[ruoyi-ui/admin/public/index.html](mdc:ruoyi-ui/admin/public/index.html)
  - 主入口：[ruoyi-ui/admin/src/main.js](mdc:ruoyi-ui/admin/src/main.js)
  - 路由：[ruoyi-ui/admin/src/router/index.js](mdc:ruoyi-ui/admin/src/router/index.js)
  - 运行脚本：[ruoyi-ui/admin/bin/run-web.bat](mdc:ruoyi-ui/admin/bin/run-web.bat)

- **API 调用**：
  - 统一在 `ruoyi-ui/admin/src/api/**` 下按业务分类
  - 登录与权限：[ruoyi-ui/admin/src/api/login.js](mdc:ruoyi-ui/admin/src/api/login.js)、[ruoyi-ui/admin/src/permission.js](mdc:ruoyi-ui/admin/src/permission.js)
  - 工具与下载：[ruoyi-ui/admin/src/plugins/download.js](mdc:ruoyi-ui/admin/src/plugins/download.js)

- **全局状态与权限**：
  - Vuex：[ruoyi-ui/admin/src/store/index.js](mdc:ruoyi-ui/admin/src/store/index.js)
  - 鉴权插件：[ruoyi-ui/admin/src/plugins/auth.js](mdc:ruoyi-ui/admin/src/plugins/auth.js)

- **常用组件与样式**：
  - 组件：`ruoyi-ui/admin/src/components/**`
  - 全局样式：`ruoyi-ui/admin/src/assets/styles/**`

- **联调提示**：
  - 若出现跨域或 Token 失效问题，检查后端 `CORS` 配置与登录接口，确认请求基地址与代理设置是否正确
