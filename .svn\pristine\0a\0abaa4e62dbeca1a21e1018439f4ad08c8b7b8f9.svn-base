package com.dataxai.web.task.core;

import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.mapper.*;
import com.dataxai.web.service.impl.FrontUserServiceImpl;
import com.dataxai.common.core.domain.model.User;
import com.dataxai.domain.TUser;
import com.dataxai.service.ITeamService;
import com.dataxai.mapper.TUserMapper;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskScoreService {
    @Autowired
    private AdminScoreHistoryMapper adminScoreHistoryMapper;
    @Autowired
    private UserPackageMapper userPackageMapper;
    @Autowired
    private UserRefueligBagMapper userRefueligBagMapper;
    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private FrontUserServiceImpl frontUserService;

    @Autowired
    private ITeamService teamService;

    @Autowired
    private TUserMapper tUserMapper;

    /**
     * 积分变更记录（公共方法，用于特殊场景如积分过期等）
     * @param userId 用户ID
     * @param scoreType 积分类型
     * @param taskType 任务类型
     * @param score 积分数量
     */
    public void addScoreHistoryLog(Long userId, Long scoreType, Integer taskType, Long score) {
        addScoreHistoryLogPrivate(userId, scoreType, taskType, score);
    }

    /**
     * 积分变更记录（内部方法）
     * @param userId 用户ID
     * @param scoreType 积分类型
     * @param taskType 任务类型
     * @param score 积分数量
     */
    private void addScoreHistoryLogPrivate(Long userId, Long scoreType, Integer taskType, Long score) {
        AdminScoreHistory adminScoreHistory = new AdminScoreHistory();
        adminScoreHistory.setUserId(userId);
        User user = frontUserMapper.selectUserByUserId(userId);
        if (null != user) {
            adminScoreHistory.setUserNickName(user.getNickName());
            adminScoreHistory.setUserPhone(user.getPhone());
        }
        List<UserPackage> userPackageList = frontUserService.getUserPackageList(userId);
        if (null != userPackageList && userPackageList.size() == 1) {
            UserPackage userPackage = userPackageList.get(0);
            HashMap<String, Object> hashMap = frontUserService.getUnExpireTariffScore(userPackage);
            long tariff_unexpire_score = (long) hashMap.get(Constants.KEY_TARIFF_UNEXPIRE_SCORE);
            long unExpireRefueligBagScore = frontUserService.getUnExpireRefueligBagScore(userId);
            adminScoreHistory.setRemainScore(tariff_unexpire_score + unExpireRefueligBagScore);
        }
        adminScoreHistory.setScore(score);
        adminScoreHistory.setType(scoreType);
        adminScoreHistory.setTaskType(taskType);
        adminScoreHistory.setUpdateTime(DateUtils.getNowDate());
        adminScoreHistoryMapper.insertAdminScoreHistory(adminScoreHistory);
    }

    /**
     * 扣减积分（支持个人和团队模式）- 向后兼容方法
     * @param userId 用户ID
     * @param integral 扣减的积分数量
     * @return 扣减是否成功
     */
    public boolean deductionPoints(Long userId, Long integral) {
        // 默认使用任务消耗类型和未知任务类型
        return deductionPoints(userId, integral, Constants.SCORE_TYPE_4, null);
    }

    /**
     * 扣减积分（支持个人和团队模式）
     * @param userId 用户ID
     * @param integral 扣减的积分数量
     * @param scoreType 积分类型（对应Constants中的积分类型常量）
     * @param taskType 任务类型（对应Constants中的任务类型常量）
     * @return 扣减是否成功
     */
    public boolean deductionPoints(Long userId, Long integral, Long scoreType, Integer taskType) {
        // 检查用户是否处于团队模式
        TUser user = tUserMapper.selectTUserById(userId);
        if (user != null && user.getCurrentMode() != null && user.getCurrentMode() == 2 && user.getTeamId() != null && user.getTeamId() > 0) {
            // 团队模式：从团队积分扣除，不记录个人积分历史
            return deductTeamPoints(user.getTeamId(), userId, integral, scoreType.intValue(), taskType);
        } else {
            // 个人模式：从个人积分扣除
            boolean success = deductPersonalPoints(userId, integral);
            if (success) {
                // 添加积分历史记录
                addScoreHistoryLogPrivate(userId, scoreType, taskType, -integral);
            }
            return success;
        }
    }

    /**
     * 扣减团队积分
     * @param teamId 团队ID
     * @param userId 使用人ID
     * @param integral 扣减的积分数量
     * @param scoreType 积分类型
     * @param taskType 任务类型
     * @return 扣减是否成功
     */
    private boolean deductTeamPoints(Long teamId, Long userId, Long integral, Integer scoreType, Integer taskType) {
        // 使用团队服务扣减积分
        return teamService.deductTeamScore(teamId, userId, integral, scoreType, taskType, "任务扣分", "执行任务扣减团队积分");
    }

    /**
     * 扣减个人积分（原有逻辑）
     * @param userId 用户ID
     * @param integral 扣减的积分数量
     * @return 扣减是否成功
     */
    private boolean deductPersonalPoints(Long userId, Long integral) {
        List<UserPackage> userPackages = getUserPackages(userId);
        Date packageEndTime = null;
        Date refueligBagEndTime = null;
        String userPackageId = "";
        String userRefueligBagId = "";
        Long usePackageUnexpiredScore = 0L;
        Long refuelingBagUnexpiredScore = 0L;
        Long totalUnexpiredScore = 0L;
        Date nowDate = DateUtils.getNowDate();
        if (CollectionUtil.isNotEmpty(userPackages)) {
            UserPackage userPackageVo = userPackages.get(0);
            packageEndTime = getEndTime(userPackageVo.getPackageStartTime(), Integer.parseInt(userPackageVo.getPackageCycle().toString()));
            userPackageId = userPackageVo.getUserPackageId();
            if (nowDate.compareTo(packageEndTime) < 0) {
                usePackageUnexpiredScore = userPackageVo.getPackageScore();
                totalUnexpiredScore += usePackageUnexpiredScore;
            }
        }
        if (null == packageEndTime) {
            log.error("用户id为    " + userId + "  未购买套餐,或者购买的套餐的过期日期计算出错");
            return false;
        }
        UserRefueligBag userRefueligBag = new UserRefueligBag();
        userRefueligBag.setUserId(userId);
        userRefueligBag.setDelFlag(0);
        List<UserRefueligBag> userRefueligBags = userRefueligBagMapper.selectUserRefueligBagListByUserId(userRefueligBag);
        if (CollectionUtil.isNotEmpty(userRefueligBags)) {
            List<UserRefueligBag> bagCollect = userRefueligBags.stream()
                    .filter(obj -> nowDate.compareTo(obj.getRefuelingBagEndTime()) < 0 && obj.getRefuelingBagScore() > 0)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(bagCollect)) {
                totalUnexpiredScore += bagCollect.stream().mapToLong(UserRefueligBag::getRefuelingBagScore).sum();
                if (totalUnexpiredScore < integral) {
                    return false;
                }
                for (UserRefueligBag refueligBag : bagCollect) {
                    refueligBagEndTime = refueligBag.getRefuelingBagEndTime();
                    userRefueligBagId = refueligBag.getUserRefuelingBagId();
                    refuelingBagUnexpiredScore = refueligBag.getRefuelingBagScore();
                    if (null == refueligBagEndTime) {
                        log.error("用户id为    " + userId + "  购买加油包没有过期时间");
                        return false;
                    }
                    if (packageEndTime.compareTo(refueligBagEndTime) < 0) {
                        if (usePackageUnexpiredScore >= integral) {
                            userPackageMapper.updatePackageScore(integral, userPackageId);
                            return true;
                        } else {
                            userPackageMapper.updatePackageScore(usePackageUnexpiredScore, userPackageId);
                            integral -= usePackageUnexpiredScore;
                            if (refuelingBagUnexpiredScore >= integral) {
                                userRefueligBagMapper.updateUserRefueligBagScore(integral, userRefueligBagId);
                                return true;
                            } else {
                                userRefueligBagMapper.updateUserRefueligBagScore(refuelingBagUnexpiredScore, userRefueligBagId);
                                integral -= refuelingBagUnexpiredScore;
                            }
                        }
                    } else {
                        if (refuelingBagUnexpiredScore >= integral) {
                            userRefueligBagMapper.updateUserRefueligBagScore(integral, userRefueligBagId);
                            return true;
                        } else {
                            userRefueligBagMapper.updateUserRefueligBagScore(refuelingBagUnexpiredScore, userRefueligBagId);
                            integral -= refuelingBagUnexpiredScore;
                        }
                    }
                }
                if (integral > 0) {
                    // 继续扣减剩余积分，但不再记录历史（避免重复记录）
                    return deductPersonalPoints(userId, integral);
                }
            } else {
                if (totalUnexpiredScore < integral) {
                    return false;
                }
                userPackageMapper.updatePackageScore(integral, userPackageId);
                return true;
            }
        } else {
            if (totalUnexpiredScore < integral) {
                return false;
            }
            userPackageMapper.updatePackageScore(integral, userPackageId);
            return true;
        }
        return false;
    }

    /**
     * 获取用户套餐列表
     * @param userId 用户ID
     * @return 用户套餐列表
     */
    private List<UserPackage> getUserPackages(Long userId) {
        UserPackage userPackage = new UserPackage();
        userPackage.setUserId(userId);
        userPackage.setDelFlag(0);
        List<UserPackage> userPackages = userPackageMapper.selectUserPackageList(userPackage);
        return userPackages;
    }

    /**
     * 计算套餐结束时间
     * @param packageStartTime 套餐开始时间
     * @param dayNums 天数
     * @return 套餐结束时间
     */
    private static Date getEndTime(Date packageStartTime, int dayNums) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(packageStartTime);
        calendar.add(Calendar.DAY_OF_YEAR, dayNums);
        return calendar.getTime();
    }
}