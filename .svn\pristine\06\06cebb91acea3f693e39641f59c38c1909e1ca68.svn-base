/**
 * @Author: wuyang
 * @Date: 2024/1/17 场景
 * @Description: ""
 */
import { ReloadButton } from '@/component/reload-button/ReloadButton'
import { getPresetSceneCategory, getPresetSceneList } from '@/api/task'
import { useEffect, useState } from 'react'
import classNames from 'classnames'
import { useRefreshData } from '@/common/hooks/useRefreshData'
import { Radio } from 'antd'
import { CheckCircleFilled } from '@ant-design/icons'
import { useTaskService } from '@/common/services/task/taskContext'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'

export const TaskMannequinSceneTab = () => {
	const taskService = useTaskService()
	const [presetScene, setPresetScene] = useAtomMethod(taskService.presetScene)
	// 场景选项卡数据信息
	const [sceneCategory, setSceneCategory] = useState<Array<string>>([])

	useEffect(() => {
		const { fire, cancel } = getPresetSceneCategory()

		fire()
			.then((res) => {
				setSceneCategory(res)
			})
			.catch((err) => {
				console.log('www--------->err', err)
			})
		return () => {
			cancel()
		}
	}, [])

	const { dataList, getList, resetPageGetList, resetPage } = useRefreshData<{
		prompt: string
		id: number
		categorySmall: string
		image: string
	}>({
		initFetch: false,
		getDataParams: { largeCategory: presetScene.scene },
		pageSize: presetScene.page.pageSize,
		defaultPageNumber: presetScene.page.pageNum,
		getData: getPresetSceneList
	})

	useEffect(() => {
		if (sceneCategory.length > 0) {
			if (presetScene.scene === '') {
				// 默认没有值，那么取第一个值
				setPresetScene({
					...presetScene,
					scene: sceneCategory[0]
				})
			} else {
				if (presetScene.scene) {
					// 切换场景时，需要调用复原方法
					getList()
				}
			}
		}
		// eslint-disable-next-line
	}, [presetScene.scene, sceneCategory])

	return (
		<div className={'mt-[40px]'}>
			<div className={'text-normal'}>场景</div>
			<div className={'flex justify-between'}>
				<div className={'flex-1 flex mt-[8px]'}>
					<Radio.Group
						value={presetScene.scene}
						onChange={(event) => {
							setPresetScene({
								...presetScene,
								scene: event.target.value
							})
							resetPage()
						}}
					>
						{sceneCategory.map((item) => {
							return (
								<Radio.Button key={item} value={item}>
									{item}
								</Radio.Button>
							)
						})}
					</Radio.Group>
				</div>
				<div className={'w-[80px] mt-[12px]'}>
					<ReloadButton
						onClick={() => {
							setPresetScene({
								...presetScene,
								activePrompt: { id: '', prompt: '', value: '' }
							})
							getList()
						}}
					/>
				</div>
			</div>
			<div className={'flex flex-wrap justify-start mt-[4px]'}>
				{dataList.map((item) => {
					return (
						<div
							key={item.id}
							className={'text-center'}
							onClick={() => {
								//点击将其设置为activePrompt 选过的点击即取消
								presetScene.activePrompt.id === item.id + '' ?
									setPresetScene({
										...presetScene,
										activePrompt: {
											id: '',
											prompt: '',
											value: ''
										}
									}) :
									setPresetScene({
										...presetScene,
										activePrompt: {
											id: item.id + '',
											prompt: item.prompt,
											value: item.categorySmall
										}
									})
							}}
						>
							<div
								className={classNames(
									'w-[74px] h-[74px] mr-[4px] mt-[4px] rounded-lg overflow-hidden hover:border-primary border-[2px] p-[2px] cursor-pointer relative',
									presetScene.activePrompt.id === item.id + ''
										? 'border-primary'
										: 'border-white'
								)}
							>
								<img
									src={item.image}
									alt=""
									className={'w-full h-full rounded-lg'}
								/>
								{presetScene.activePrompt.id === item.id + '' ? (
									<div className={'absolute top-[2px] right-[6px]'}>
										<CheckCircleFilled
											className={'text-primary bg-white rounded-full'}
										/>
									</div>
								) : null}
							</div>
							<span
								className={'text-subDescText text-[12px] text-center w-full'}
							>
								{item.categorySmall}
							</span>
						</div>
					)
				})}
			</div>
		</div>
	)
}
