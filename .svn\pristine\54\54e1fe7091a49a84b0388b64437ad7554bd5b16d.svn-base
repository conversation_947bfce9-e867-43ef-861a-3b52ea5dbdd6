package com.dataxai.web.gtask.processor;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dataxai.domain.TitleExtractionTask;
import com.dataxai.domain.TitleExtractionTaskDetail;
import com.dataxai.service.ITitleExtractionTaskDetailService;
import com.dataxai.service.ITitleExtractionTaskService;
import com.dataxai.service.ITitleExtractionConfigService;
import com.dataxai.service.IProductInfoService;
import com.dataxai.domain.TitleExtractionConfig;
import com.dataxai.domain.ProductInfo;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.gtask.utils.ImageProcessUtils;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.dataxai.domain.WorkflowNodeExecution;
import com.dataxai.service.IWorkflowStatusService;
import com.dataxai.service.IWorkflowBatchService;
import com.dataxai.mapper.TitleExtractionTaskMapper;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.utils.JpgUrlToWebpConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 标题提取GTask处理器
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Slf4j
@Component
public class TitleExtractionGTaskProcessor implements GTaskProcessor<TitleExtractionTaskDetail> {

    @Autowired
    private ITitleExtractionTaskDetailService titleExtractionTaskDetailService;

    @Autowired
    private ITitleExtractionTaskService titleExtractionTaskService;

    @Autowired
    private ITitleExtractionConfigService titleExtractionConfigService;

    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private ImageProcessUtils imageProcessUtils;

    @Autowired
    private AliYunFileService aliYunFileService;

    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;

    @Autowired
    private IWorkflowStatusService workflowStatusService;

    @Autowired
    private IWorkflowBatchService workflowBatchService;

    @Autowired
    private TitleExtractionTaskMapper titleExtractionTaskMapper;

    // 标题提取API配置
//    private static final String TITLE_EXTRACTION_API_URL = "https://aichat.the2016.com/v1/chat-messages";
    private static final String TITLE_EXTRACTION_API_URL = "http://**************:8601/v1/chat-messages";
    //138  token
     private static final String TITLE_EXTRACTION_API_TOKEN = "app-MJYtkVhVEw4DDZexWqJsfUEu";
//     private static final String TITLE_EXTRACTION_API_TOKEN = "app-ckGeiloqrkO4wydh7y6ppGBq";
    //160
//    private static final String TITLE_EXTRACTION_API_TOKEN = "app-lO3ikYbWb7BSyJbYhG11UXuL"; //批量

    @Override
    public String getTaskType() {
        return "title_extraction";
    }

    @Override
    public String getTaskDescription() {
        return "标题提取任务";
    }

    @Override
    public List<TitleExtractionTaskDetail> queryPendingTasks(int batchSize) {
        try {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "查询待处理的标题提取任务，批处理大小: {}", batchSize);

            // 查询process_status=1（待处理）的任务详情
            TitleExtractionTaskDetail query = new TitleExtractionTaskDetail();
            query.setProcessStatus(1); // 待处理

            List<TitleExtractionTaskDetail> pendingTasks = titleExtractionTaskDetailService
                .selectTitleExtractionTaskDetailList(query);

            // 限制批处理大小
            if (pendingTasks != null && pendingTasks.size() > batchSize) {
                pendingTasks = pendingTasks.subList(0, batchSize);
            }

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "查询到{}条待处理的标题提取任务",
                pendingTasks != null ? pendingTasks.size() : 0);

            return pendingTasks;
        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "查询待处理标题提取任务失败", e);
            return null;
        }
    }

    @Override
    public TaskProcessResult processSingleTask(TitleExtractionTaskDetail taskDetail) {
        Long taskDetailId = taskDetail.getId();
        String imageUrl = taskDetail.getImageUrl();

        try {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "开始处理标题提取任务，任务详情ID: {}, 图片URL: {}", taskDetailId, imageUrl);

            // 0. 确保主任务状态为执行中
            ensureTaskStatusIsExecuting(taskDetail.getTaskId());

            // 1. 下载并处理图片（转换为小图）
            byte[] processedImageData = JpgUrlToWebpConverter.urlToWebpBytes(imageUrl, "title_extraction");
            if (processedImageData == null) {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "图片处理失败，任务详情ID: {}", taskDetailId);
                updateTaskDetailStatus(taskDetailId, 3, "图片处理失败", (TitleExtractionResult) null,null);
                updateTaskStatisticsForFailure(taskDetail.getTaskId());
                return TaskProcessResult.failure("图片处理失败");
            }

            // 2. 上传处理后的图片到阿里云
            String processedImageUrl = uploadProcessedImage(processedImageData, taskDetailId);
            if (processedImageUrl == null) {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "上传处理后图片失败，任务详情ID: {}", taskDetailId);
                updateTaskDetailStatus(taskDetailId, 3, "上传处理后图片失败", (TitleExtractionResult) null,null);
                updateTaskStatisticsForFailure(taskDetail.getTaskId());
                return TaskProcessResult.failure("上传处理后图片失败");
            }

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "图片处理完成，使用处理后图片URL: {}", processedImageUrl);

            // 3. 调用标题提取API（使用处理后的图片）
            JSONObject apiResult = callTitleExtractionAPI(processedImageUrl, taskDetail.getTaskId(), taskDetail);
            if (apiResult == null) {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "调用标题提取API失败，任务详情ID: {}", taskDetailId);
                updateTaskDetailStatus(taskDetailId, 3, "API调用失败", (TitleExtractionResult) null,processedImageUrl);
                updateTaskStatisticsForFailure(taskDetail.getTaskId());
                return TaskProcessResult.failure("API调用失败");
            }

            // 记录API返回结果到日志
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "标题提取API调用成功，任务详情ID: {}, API返回结果: {}", taskDetailId, apiResult.toString());

            // 解析API返回结果（一次性解析所有字段）
            TitleExtractionResult extractionResult = parseAllTitleFields(apiResult);

            // 记录解析后的结果
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "标题提取结果解析完成，任务详情ID: {}, 商品标题: {}, TEMU标题: {}, 商品描述长度: {}, Meta描述长度: {}",
                taskDetailId, extractionResult.getProductTitle(), extractionResult.getTemuProductTitle(),
                extractionResult.getProductDescription() != null ? extractionResult.getProductDescription().length() : 0,
                extractionResult.getMetaDescription() != null ? extractionResult.getMetaDescription().length() : 0);

            // 直接使用解析后的结果更新任务详情状态
            boolean updateSuccess = updateTaskDetailStatus(taskDetailId, 2, "处理成功", extractionResult,processedImageUrl);

            if (updateSuccess) {
                // 更新主任务统计
                updateTaskStatistics(taskDetail.getTaskId(), true);
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取任务处理成功，任务详情ID: {}, 提取标题: {}", taskDetailId, extractionResult.getPrimaryTitle());
                return TaskProcessResult.success("处理成功", extractionResult.getPrimaryTitle());
            } else {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "更新标题提取任务详情失败，任务详情ID: {}", taskDetailId);
                updateTaskStatisticsForFailure(taskDetail.getTaskId());
                return TaskProcessResult.failure("更新任务详情失败");
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "处理标题提取任务异常，任务详情ID: {}", taskDetailId, e);
            updateTaskDetailStatus(taskDetailId, 3, "处理异常: " + e.getMessage(), (TitleExtractionResult) null,null);
            updateTaskStatisticsForFailure(taskDetail.getTaskId());
            return TaskProcessResult.failure("处理异常: " + e.getMessage());
        }
    }

    /**
     * 上传处理后的图片到阿里云
     */
    private String uploadProcessedImage(byte[] imageData, Long taskDetailId) {
        try {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "开始上传处理后的图片，任务详情ID: {}", taskDetailId);

            // 使用ByteArrayResource包装图片数据
            org.springframework.core.io.ByteArrayResource resource =
                imageProcessUtils.createImageResource(imageData);

            String uploadedUrl = aliYunFileService.uploadImageWebp(resource);
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "处理后图片上传成功，任务详情ID: {}, URL: {}", taskDetailId, uploadedUrl);
            return uploadedUrl;
        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "上传处理后图片异常，任务详情ID: {}", taskDetailId, e);
            return null;
        }
    }

    /**
     * 调用标题提取API
     */
    private JSONObject callTitleExtractionAPI(String imageUrl, Long taskId, TitleExtractionTaskDetail taskDetail) {
        try {
            // 获取任务信息
            TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(taskId);
            Integer taskType = task != null && task.getType() != null ? task.getType() : 1;

            // 构建inputs参数
            JSONObject inputs = new JSONObject();
            String titleTemplate = "";

            if (taskType == 1) {
                // 合肥配置
                titleTemplate = "*使用这个确切的结构：**风格+颜色+打印/图形+使用场景**\n" +
                              "*最多250个字符\n" +
                              "*遵循TEMU命名约定和SEO最佳实践\n" +
                              "*必须包括颜色。\n" +
                              "*不包括特殊符号，如加号（+）、星号（*）等。\n" +
                              "示例：\"Linen overalls with side slit pockets, detailed office wear\"";
            } else if (taskType == 2) {
                // 贵阳配置
                titleTemplate = "*使用这个确切的结构：**元素名称+产品关键词+热搜词**\n" +
                              "*100-256个字符之间\n" +
                              "*遵循TEMU命名约定和SEO最佳实践\n" +
                              "*借鉴示例标题进行微改\n" +
                              "*遵循自己产品的基本信息\n" +
                              "*不包括特殊符号，如加号（+）、星号（*）等。\n" +
                              "示例：【\"" + getExampleTitle(taskDetail) + "\"】";

            }

            inputs.set("title_template", titleTemplate);

            // 构建请求参数
            JSONObject requestData = new JSONObject();
            requestData.set("query", "请提取这张图片中的标题文字内容");
            requestData.set("inputs", inputs);
            requestData.set("response_mode", "blocking");
            requestData.set("user", "system");

            // 添加图片文件
            JSONArray files = new JSONArray();
            JSONObject fileInfo = new JSONObject();
            fileInfo.set("type", "image");
            fileInfo.set("transfer_method", "remote_url");
            fileInfo.set("url", imageUrl);
            files.add(fileInfo);
            requestData.set("files", files);

            // 记录API返回结果到日志
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
            "标题提取API调用，任务详情ID: {}, 请求参数: {}", taskId, requestData.toString());

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(TITLE_EXTRACTION_API_URL)
                    .header("Authorization", "Bearer " + TITLE_EXTRACTION_API_TOKEN)
                    .header("Content-Type", "application/json")
                    .body(requestData.toString())
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "标题提取API响应成功");
                return JSONUtil.parseObj(responseBody);
            } else {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取API调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return null;
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "调用标题提取API异常", e);
            return null;
        }
    }

    /**
     * 解析所有标题字段（一次性解析）
     */
    private TitleExtractionResult parseAllTitleFields(JSONObject result) {
        try {
            // API返回格式: {"answer": "{\"Product Title\": \"...\", ...}", "metadata": {...}}
            String answer = result.getStr("answer", "");
            if (answer.isEmpty()) {
                return new TitleExtractionResult();
            }

            // 尝试解析answer中的JSON内容
            try {
                JSONObject answerJson = new JSONObject(answer);

                // 提取各个字段
                String productTitle = answerJson.getStr("Product Title", "");
                String temuProductTitle = answerJson.getStr("TEMU Product Title", "");
                String productDescription = answerJson.getStr("Product Description", "");
                String metaDescription = answerJson.getStr("Meta Description", "");

                // 字段长度限制
                if (productTitle.length() > 200) productTitle = productTitle.substring(0, 200);
                if (temuProductTitle.length() > 200) temuProductTitle = temuProductTitle.substring(0, 200);
                if (productDescription.length() > 1000) productDescription = productDescription.substring(0, 1000);
                if (metaDescription.length() > 500) metaDescription = metaDescription.substring(0, 500);

                return new TitleExtractionResult(productTitle, temuProductTitle, productDescription, metaDescription);

            } catch (Exception jsonException) {
                // 如果answer不是JSON格式，只设置productTitle
                log.warn("answer字段不是有效的JSON格式，仅设置productTitle: {}", jsonException.getMessage());
                String title = answer.length() > 200 ? answer.substring(0, 200) : answer;
                return new TitleExtractionResult(title, "", "", "");
            }

        } catch (Exception e) {
            log.warn("解析标题字段失败", e);
            return new TitleExtractionResult();
        }
    }

    /**
     * 提取标题（保持向后兼容）
     */
    private String extractTitle(JSONObject result) {
        try {
            // API返回格式: {"answer": "{\"Product Title\": \"...\", ...}", "metadata": {...}}
            String answer = result.getStr("answer", "");
            if (answer.isEmpty()) {
                return "";
            }

            // 尝试解析answer中的JSON内容
            try {
                JSONObject answerJson = new JSONObject(answer);

                // 优先提取Product Title
                String productTitle = answerJson.getStr("Product Title", "");
                if (!productTitle.isEmpty()) {
                    return productTitle;
                }

                // 如果没有Product Title，尝试其他字段
                String temuTitle = answerJson.getStr("TEMU Product Title", "");
                if (!temuTitle.isEmpty()) {
                    return temuTitle;
                }

                // 如果都没有，返回第一个非空的值
                for (String key : answerJson.keySet()) {
                    String value = answerJson.getStr(key, "");
                    if (!value.isEmpty() && value.length() <= 200) { // 限制长度避免数据库字段超限
                        return value;
                    }
                }

            } catch (Exception jsonException) {
                // 如果answer不是JSON格式，直接返回answer内容（截取前200字符）
                log.warn("answer字段不是有效的JSON格式，直接返回内容: {}", jsonException.getMessage());
                return answer.length() > 200 ? answer.substring(0, 200) : answer;
            }

            return "";
        } catch (Exception e) {
            log.warn("提取标题失败", e);
            return "";
        }
    }

    /**
     * 更新任务详情状态（直接使用解析后的结果）
     */
    private boolean updateTaskDetailStatus(Long taskDetailId, Integer processStatus,
                                         String message, TitleExtractionResult extractionResult,String imageUrl) {
        try {
            TitleExtractionTaskDetail updateDetail = new TitleExtractionTaskDetail();
            updateDetail.setId(taskDetailId);
            updateDetail.setProcessStatus(processStatus);

            // 如果处理成功，直接使用传入的解析结果
            if (processStatus == 2 && extractionResult != null) {
                setTitleFieldsFromResult(updateDetail, extractionResult);
            }
            updateDetail.setScaleImageUrl(imageUrl);
            int result = titleExtractionTaskDetailService.updateTitleExtractionTaskDetail(updateDetail);
            return result > 0;
        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "更新任务详情状态失败，任务详情ID: {}", taskDetailId, e);
            return false;
        }
    }

    /**
     * 从解析结果设置到实体对象（直接使用解析后的结果，不再重新解析）
     */
    private void setTitleFieldsFromResult(TitleExtractionTaskDetail updateDetail, TitleExtractionResult extractionResult) {
        try {
            // 直接使用传入的解析结果，不再重新解析
            if (extractionResult.getProductTitle() != null && !extractionResult.getProductTitle().isEmpty()) {
                updateDetail.setProductTitle(extractionResult.getProductTitle());
            }

            if (extractionResult.getTemuProductTitle() != null && !extractionResult.getTemuProductTitle().isEmpty()) {
                updateDetail.setTemuProductTitle(extractionResult.getTemuProductTitle());
            }

            if (extractionResult.getProductDescription() != null && !extractionResult.getProductDescription().isEmpty()) {
                updateDetail.setProductDescription(extractionResult.getProductDescription());
            }

            if (extractionResult.getMetaDescription() != null && !extractionResult.getMetaDescription().isEmpty()) {
                updateDetail.setMetaDescription(extractionResult.getMetaDescription());
            }

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "直接保存解析结果到数据库 - 商品标题: {}, TEMU标题: {}, 商品描述长度: {}, Meta描述长度: {}",
                extractionResult.getProductTitle(), extractionResult.getTemuProductTitle(),
                extractionResult.getProductDescription() != null ? extractionResult.getProductDescription().length() : 0,
                extractionResult.getMetaDescription() != null ? extractionResult.getMetaDescription().length() : 0);

        } catch (Exception e) {
            log.warn("设置标题字段失败", e);
        }
    }



    /**
     * 更新任务统计（成功）
     * 使用数据库原子操作避免并发问题
     */
    private synchronized void updateTaskStatistics(Long taskId, boolean success) {
        try {
            // 使用原子操作更新统计数据
            boolean updateResult = updateTaskStatisticsAtomic(taskId, success);
            if (!updateResult) {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "原子更新任务统计失败，任务ID: {}", taskId);
                return;
            }

            // 检查是否所有任务都已完成
            TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(taskId);
            if (task == null) {
                return;
            }

            int totalCount = task.getTotalAmount();
            int successCount = task.getSuccessAmount();
            int failCount = task.getFailAmount();

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "任务统计更新，任务ID: {}, 总数: {}, 成功: {}, 失败: {}",
                taskId, totalCount, successCount, failCount);

            // 检查是否所有任务都已完成
            if (successCount + failCount >= totalCount) {
                TitleExtractionTask updateTask = new TitleExtractionTask();
                updateTask.setId(taskId);
                updateTask.setStatus(3); // 执行完成
                titleExtractionTaskService.updateTitleExtractionTask(updateTask);

                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取任务批次完成，任务ID: {}, 成功: {}, 失败: {}",
                    taskId, successCount, failCount);

                // 检查是否关联了工作流节点执行记录，如果关联了需要更新状态为2（已完成）
                updateWorkflowNodeExecutionStatus(task);
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "更新任务统计异常，任务ID: {}", taskId, e);
        }
    }

    /**
     * 更新任务统计（失败）
     */
    private void updateTaskStatisticsForFailure(Long taskId) {
        updateTaskStatistics(taskId, false);
    }

    /**
     * 原子更新任务统计
     *
     * @param taskId 任务ID
     * @param success 是否成功
     * @return 更新结果
     */
    private boolean updateTaskStatisticsAtomic(Long taskId, boolean success) {
        try {
            int updateCount;
            if (success) {
                updateCount = titleExtractionTaskService.incrementSuccessAmount(taskId);
            } else {
                updateCount = titleExtractionTaskService.incrementFailAmount(taskId);
            }

            return updateCount > 0;
        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "原子更新任务统计异常，任务ID: {}, 成功: {}", taskId, success, e);
            return false;
        }
    }

    /**
     * 确保主任务状态为执行中
     * 使用synchronized防止并发问题
     */
    private synchronized void ensureTaskStatusIsExecuting(Long taskId) {
        try {
            TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(taskId);
            if (task != null && task.getStatus() == 1) { // 待执行状态
                TitleExtractionTask updateTask = new TitleExtractionTask();
                updateTask.setId(taskId);
                updateTask.setStatus(2); // 执行中
                titleExtractionTaskService.updateTitleExtractionTask(updateTask);
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取任务状态更新为执行中，任务ID: {}", taskId);
            }
        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "更新任务状态为执行中失败，任务ID: {}", taskId, e);
        }
    }

    /**
     * 更新工作流节点执行状态
     * 如果任务关联了工作流节点执行记录，将状态更新为2（已完成）
     *
     * @param task 标题提取任务
     */
    private void updateWorkflowNodeExecutionStatus(TitleExtractionTask task) {
        try {
            if (task == null) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "任务对象为空，跳过工作流节点状态更新");
                return;
            }

            if (task.getWorkflowNodeExecutionId() == null || task.getWorkflowNodeExecutionId() <= 0) {
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "任务未关联工作流节点执行记录，跳过状态更新，任务ID: {}", task.getId());
                return;
            }

            Long workflowNodeExecutionId = task.getWorkflowNodeExecutionId();

            // 更新工作流节点执行状态为2（已完成）
            WorkflowNodeExecution updateNodeExecution = new WorkflowNodeExecution();
            updateNodeExecution.setId(workflowNodeExecutionId);
            updateNodeExecution.setStatus(2); // 已完成
            updateNodeExecution.setUpdateTime(new java.util.Date());

            int updateResult = workflowNodeExecutionMapper.updateWorkflowNodeExecution(updateNodeExecution);

            if (updateResult > 0) {
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "工作流节点执行状态更新成功，节点执行ID: {}, 任务ID: {}",
                    workflowNodeExecutionId, task.getId());

                // 判断当前节点是否是最后一个节点，并更新工作流状态
                updateWorkflowStatusBasedOnNode(workflowNodeExecutionId);

            } else {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "工作流节点执行状态更新失败，节点执行ID: {}, 任务ID: {}",
                    workflowNodeExecutionId, task.getId());
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "更新工作流节点执行状态异常，任务ID: {}", task != null ? task.getId() : "unknown", e);
        }
    }

    /**
     * 根据节点状态更新工作流状态
     * 判断当前节点是否是最后一个节点，如果是则将工作流状态改为2（已完成），否则改为1（执行中）
     *
     * @param workflowNodeExecutionId 工作流节点执行记录ID
     */
    private void updateWorkflowStatusBasedOnNode(Long workflowNodeExecutionId) {
        try {
            // 获取工作流节点信息
            WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(workflowNodeExecutionId);
            if (nodeExecution == null) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "工作流节点执行记录不存在，节点ID: {}", workflowNodeExecutionId);
                return;
            }

            Long workflowId = nodeExecution.getWorkflowId();

            // 判断是否是最后一个节点
            boolean isLastNode = workflowStatusService.isLastNode(workflowNodeExecutionId);

            if (isLastNode) {
                // 是最后一个节点，工作流状态改为2（已完成）
                workflowStatusService.updateWorkflowStatus(workflowId, 2);
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "工作流执行完成，工作流ID: {}, 节点执行ID: {}", workflowId, workflowNodeExecutionId);
            } else {
                // 不是最后一个节点，工作流状态改为1（执行中）
                workflowStatusService.updateWorkflowStatus(workflowId, 1);
                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "工作流继续执行中，工作流ID: {}, 节点执行ID: {}", workflowId, workflowNodeExecutionId);

                // 为下一个节点创建批次任务
                createNextNodeBatchWithTitleExtractionResults(nodeExecution);
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "更新工作流状态异常，节点执行ID: {}", workflowNodeExecutionId, e);
        }
    }

    /**
     * 为下一个节点创建批次任务，使用标题提取结果中的图片URL作为素材
     *
     * @param currentNodeExecution 当前工作流节点执行记录
     */
    private void createNextNodeBatchWithTitleExtractionResults(WorkflowNodeExecution currentNodeExecution) {
        try {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "开始为下一个节点创建批次，当前节点ID: {}", currentNodeExecution.getId());

            // 查找下一个节点
            WorkflowNodeExecution nextNode = findNextNode(currentNodeExecution);
            if (nextNode == null) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "未找到下一个节点，当前节点ID: {}", currentNodeExecution.getId());
                return;
            }

            // 获取当前节点关联的标题提取任务
            List<TitleExtractionTask> titleTasks = titleExtractionTaskMapper.selectByWorkflowNodeExecutionId(currentNodeExecution.getId());
            if (titleTasks == null || titleTasks.isEmpty()) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "未找到当前节点关联的标题提取任务，节点ID: {}", currentNodeExecution.getId());
                return;
            }

            // 取第一个任务（通常一个节点只有一个任务）
            TitleExtractionTask titleTask = titleTasks.get(0);

            // 获取标题提取任务的所有详情记录中的图片URL
            List<TitleExtractionTaskDetail> taskDetails = titleExtractionTaskDetailService.selectTitleExtractionTaskDetailByTaskId(titleTask.getId());
            if (taskDetails == null || taskDetails.isEmpty()) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取任务没有详情记录，任务ID: {}", titleTask.getId());
                return;
            }

            // 提取所有的图片URL作为下一个节点的素材，并去掉域名前缀避免重复添加
            List<String> materialUrls = taskDetails.stream()
                    .map(TitleExtractionTaskDetail::getImageUrl)
                    .filter(url -> url != null && !url.trim().isEmpty())
                    .map(url -> CommonUtils.subCosPrefix(url)) // 去掉域名前缀
                    .collect(Collectors.toList());

            if (materialUrls.isEmpty()) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "标题提取任务详情中没有有效的图片URL，任务ID: {}", titleTask.getId());
                return;
            }

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "获取到 {} 个图片URL作为下一个节点的素材，下一个节点ID: {}", materialUrls.size(), nextNode.getId());

            // 为下一个节点创建批次
            String batchId = workflowBatchService.createBatchForWorkflowNode(nextNode, materialUrls);

            if (batchId != null) {
                // 更新下一个节点的批次ID
                nextNode.setBatchId(Long.valueOf(batchId));
                workflowNodeExecutionMapper.updateWorkflowNodeExecution(nextNode);

                TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "为下一个节点创建批次成功，节点ID: {}, 批次ID: {}, 素材数量: {}",
                    nextNode.getId(), batchId, materialUrls.size());
            } else {
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                    "为下一个节点创建批次失败，节点ID: {}", nextNode.getId());
            }

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "为下一个节点创建批次异常，当前节点ID: {}", currentNodeExecution.getId(), e);
        }
    }

    /**
     * 查找下一个节点
     *
     * @param currentNode 当前节点
     * @return 下一个节点，如果没有则返回null
     */
    private WorkflowNodeExecution findNextNode(WorkflowNodeExecution currentNode) {
        try {
            List<WorkflowNodeExecution> allNodes = workflowNodeExecutionMapper.selectByWorkflowId(currentNode.getWorkflowId());

            return allNodes.stream()
                    .filter(node -> node.getNodeOrder() == currentNode.getNodeOrder() + 1)
                    .findFirst()
                    .orElse(null);

        } catch (Exception e) {
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "查找下一个节点异常，当前节点ID: {}", currentNode.getId(), e);
            return null;
        }
    }

    /**
     * 获取示例标题
     * 如果title_extraction_task.type=2并且title_extraction_task_detail.type=2和title_extraction_task_detail.type_id>0，
     * 用type_id关联product_info.id,获取product_info.product_title来替换示例字符串，
     * 如果没有找到对应的product_title,直接用""来替换
     */
    private String getExampleTitle(TitleExtractionTaskDetail taskDetail) {
        try {
            if (taskDetail != null && taskDetail.getType() != null && taskDetail.getType() == 2
                && taskDetail.getTypeId() != null && taskDetail.getTypeId() > 0) {

                ProductInfo productInfo = productInfoService.selectProductInfoById(taskDetail.getTypeId());
                if (productInfo != null && productInfo.getProductTitle() != null) {
                    return productInfo.getProductTitle();
                }
            }
            return "";
        } catch (Exception e) {
            TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT,
                "获取示例标题失败，任务详情ID: {}, 错误: {}", taskDetail != null ? taskDetail.getId() : "unknown", e.getMessage());
            return "";
        }
    }
}
