package com.dataxai.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.exception.ServiceException;
// import com.dataxai.web.Constants.Constants; // Constants不在system模块中，需要直接使用常量值
import com.dataxai.domain.TUser;
import com.dataxai.common.core.domain.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.dataxai.mapper.TeamMapper;
import com.dataxai.mapper.TeamUserMapper;
import com.dataxai.mapper.TeamScoreHistoryMapper;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.domain.Team;
import com.dataxai.domain.TeamUser;
import com.dataxai.domain.TeamScoreHistory;
import com.dataxai.domain.dto.CreateTeamDTO;
import com.dataxai.domain.dto.SwitchTeamDTO;
import com.dataxai.domain.dto.BatchAddTeamMemberDTO;
import com.dataxai.domain.dto.UpdateTeamDTO;
import com.dataxai.domain.dto.AddTeamScoreDTO;
import com.dataxai.service.ITeamService;

/**
 * 团队Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class TeamServiceImpl implements ITeamService
{
    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private TeamUserMapper teamUserMapper;

    @Autowired
    private TeamScoreHistoryMapper teamScoreHistoryMapper;

    @Autowired
    private TUserMapper tUserMapper;

    /**
     * 查询团队
     *
     * @param id 团队主键
     * @return 团队
     */
    @Override
    public Team selectTeamById(Long id)
    {
        return teamMapper.selectTeamById(id);
    }

    /**
     * 查询团队列表
     *
     * @param team 团队
     * @return 团队
     */
    @Override
    public List<Team> selectTeamList(Team team)
    {
        return teamMapper.selectTeamList(team);
    }

    /**
     * 新增团队
     *
     * @param team 团队
     * @return 结果
     */
    @Override
    public int insertTeam(Team team)
    {
        team.setCreateTime(DateUtils.getNowDate());
        return teamMapper.insertTeam(team);
    }

    /**
     * 修改团队
     *
     * @param team 团队
     * @return 结果
     */
    @Override
    public int updateTeam(Team team)
    {
        team.setUpdateTime(DateUtils.getNowDate());
        return teamMapper.updateTeam(team);
    }

    /**
     * 批量删除团队
     *
     * @param ids 需要删除的团队主键
     * @return 结果
     */
    @Override
    public int deleteTeamByIds(Long[] ids)
    {
        return teamMapper.deleteTeamByIds(ids);
    }

    /**
     * 删除团队信息
     *
     * @param id 团队主键
     * @return 结果
     */
    @Override
    public int deleteTeamById(Long id)
    {
        return teamMapper.deleteTeamById(id);
    }

    /**
     * 创建团队
     */
    @Override
    @Transactional
    public Team createTeam(CreateTeamDTO createTeamDTO, Long currentUserId)
    {
        // 检查团队名称是否重复
        Team existingTeam = teamMapper.selectTeamByName(createTeamDTO.getTeamName());
        if (existingTeam != null) {
            throw new ServiceException("团队名称已存在");
        }

        // 检查用户是否已经在其他团队中
        TeamUser existingTeamUser = teamUserMapper.selectTeamUserByUserId(currentUserId);
        if (existingTeamUser != null) {
            throw new ServiceException("您已经在其他团队中，一个用户只能加入一个团队");
        }

        // 创建团队
        Team team = new Team();
        team.setTeamName(createTeamDTO.getTeamName());
        team.setIndustry(createTeamDTO.getIndustry());
        team.setCreatorId(currentUserId);
        team.setCurrentScore(0L);
        team.setCreateTime(DateUtils.getNowDate());

        int result = teamMapper.insertTeam(team);
        if (result <= 0) {
            throw new ServiceException("创建团队失败");
        }

        // 将创建者加入团队，并设置为管理员
        TeamUser teamUser = new TeamUser();
        teamUser.setTeamId(team.getId());
        teamUser.setUserId(currentUserId);
        teamUser.setNickname(createTeamDTO.getNickname());
        teamUser.setProfession(createTeamDTO.getProfession());
        teamUser.setIsAdmin(true);
        teamUser.setJoinTime(DateUtils.getNowDate());
        teamUser.setCreateTime(DateUtils.getNowDate());

        int teamUserResult = teamUserMapper.insertTeamUser(teamUser);
        if (teamUserResult <= 0) {
            throw new ServiceException("添加团队成员失败");
        }

        // 更新用户表的团队ID和当前模式
        TUser tUser = new TUser();
        tUser.setUserId(currentUserId);
        tUser.setTeamId(team.getId());
        tUser.setCurrentMode(2); // 2-团队模式
        tUser.setUpdateTime(DateUtils.getNowDate());
        int updateResult = tUserMapper.updateTUser(tUser);
        if (updateResult <= 0) {
            throw new ServiceException("更新用户团队信息失败");
        }

        return team;
    }

    /**
     * 切换团队
     */
    @Override
    @Transactional
    public boolean switchTeam(SwitchTeamDTO switchTeamDTO, Long currentUserId)
    {
        TUser tUser = new TUser();
        tUser.setUserId(currentUserId);

        if (switchTeamDTO.getTeamId() == null || switchTeamDTO.getTeamId() == 0) {
            // 切换到个人模式
            tUser.setCurrentMode(1); // 1-个人模式
        } else {
            // 切换到团队模式
            // 检查用户是否在该团队中
            int count = teamUserMapper.checkUserInTeam(switchTeamDTO.getTeamId(), currentUserId);
            if (count == 0) {
                throw new ServiceException("您不在该团队中");
            }
            tUser.setCurrentMode(2); // 2-团队模式
        }

        return tUserMapper.updateTUser(tUser) > 0;
    }

    /**
     * 批量添加团队成员
     */
    @Override
    @Transactional
    public List<String> batchAddTeamMembers(BatchAddTeamMemberDTO batchAddDTO, Long currentUserId)
    {
        // 获取当前用户的团队信息
        TeamUser currentTeamUser = teamUserMapper.selectTeamUserByUserId(currentUserId);
        if (currentTeamUser == null) {
            throw new ServiceException("您不在任何团队中");
        }

        // 检查当前用户是否为团队管理员
        if (!currentTeamUser.getIsAdmin()) {
            throw new ServiceException("只有团队管理员才能添加成员");
        }

        List<String> results = new ArrayList<>();

        for (String phoneNumber : batchAddDTO.getPhoneNumbers()) {
            try {
                // 查找用户
                User user = tUserMapper.selectUserByPhone(phoneNumber);
                if (user == null) {
                    // 自动创建用户
                    TUser newUser = new TUser();
                    newUser.setNickName(phoneNumber);
                    newUser.setPhone(phoneNumber);
                    newUser.setDelFlag(0); // 正常状态
                    newUser.setTeamId(currentTeamUser.getTeamId());
                    newUser.setCurrentMode(2); // 团队模式
                    newUser.setCreateTime(DateUtils.getNowDate());

                    int insertResult = tUserMapper.insertTUser(newUser);
                    if (insertResult <= 0) {
                        results.add(phoneNumber + ": 创建用户失败");
                        continue;
                    }
                    user = newUser; // 将新用户赋值给user变量
                } else {
                    // 检查用户是否已经在其他团队中
                    TeamUser existingTeamUser = teamUserMapper.selectTeamUserByUserId(user.getUserId());
                    if (existingTeamUser != null) {
                        results.add(phoneNumber + ": 该用户已在其他团队中");
                        continue;
                    }

                    // 更新用户的团队信息
                    TUser updateUser = new TUser();
                    updateUser.setUserId(user.getUserId());
                    updateUser.setTeamId(currentTeamUser.getTeamId());
                    updateUser.setCurrentMode(2); // 团队模式
                    tUserMapper.updateTUser(updateUser);
                }

                // 添加到团队关系表
                TeamUser teamUser = new TeamUser();
                teamUser.setTeamId(currentTeamUser.getTeamId());
                teamUser.setUserId(user.getUserId());
                teamUser.setNickname(user.getNickName());
                teamUser.setIsAdmin(false);
                teamUser.setJoinTime(DateUtils.getNowDate());
                teamUser.setCreateTime(DateUtils.getNowDate());

                int teamUserResult = teamUserMapper.insertTeamUser(teamUser);
                if (teamUserResult > 0) {
                    results.add(phoneNumber + ": 添加成功");
                } else {
                    results.add(phoneNumber + ": 添加到团队失败");
                }
            } catch (Exception e) {
                results.add(phoneNumber + ": " + e.getMessage());
            }
        }

        return results;
    }

    @Override
    @Transactional
    public Map<String, Object> batchAddTeamMembersNew(BatchAddTeamMemberDTO batchAddDTO, Long currentUserId)
    {
        // 获取当前用户的团队信息
        TeamUser currentTeamUser = teamUserMapper.selectTeamUserByUserId(currentUserId);
        if (currentTeamUser == null) {
            throw new ServiceException("您不在任何团队中");
        }

        // 检查当前用户是否为团队管理员
        if (!currentTeamUser.getIsAdmin()) {
            throw new ServiceException("只有团队管理员才能添加成员");
        }

        int successCount = 0;
        int errorCount = 0;
        List<Map<String, String>> errorList = new ArrayList<>();

        for (String phoneNumber : batchAddDTO.getPhoneNumbers()) {
            try {
                // 查找用户
                User user = tUserMapper.selectUserByPhone(phoneNumber);
                if (user == null) {
                    // 用户不存在，创建新用户（和原有逻辑一致）
                    TUser newUser = new TUser();
                    newUser.setPhone(phoneNumber);
                    newUser.setNickName("");
                    newUser.setGender(0);
                    newUser.setEnable(0);
                    newUser.setDelFlag(0);
                    newUser.setCreateTime(DateUtils.getNowDate());

                    int inserted = tUserMapper.insertTUser(newUser);
                    if (inserted > 0) {
                        user = tUserMapper.selectUserByPhone(phoneNumber);
                    }
                }

                if (user != null) {
                    // 检查用户是否已在其他团队中
                    TeamUser existingTeamUser = teamUserMapper.selectTeamUserByUserId(user.getUserId());
                    if (existingTeamUser != null) {
                        errorCount++;
                        Map<String, String> error = new HashMap<>();
                        error.put("mobile", phoneNumber);
                        errorList.add(error);
                        continue;
                    }

                    // 添加到团队
                    TeamUser teamUser = new TeamUser();
                    teamUser.setTeamId(currentTeamUser.getTeamId());
                    teamUser.setUserId(user.getUserId());
                    teamUser.setNickname(user.getNickName());
                    teamUser.setProfession("");
                    teamUser.setIsAdmin(false);
                    teamUser.setJoinTime(DateUtils.getNowDate());
                    teamUser.setCreateTime(DateUtils.getNowDate());

                    int result = teamUserMapper.insertTeamUser(teamUser);
                    if (result > 0) {
                        // 更新用户的团队信息
                        TUser updateUser = new TUser();
                        updateUser.setUserId(user.getUserId());
                        updateUser.setTeamId(currentTeamUser.getTeamId());
                        updateUser.setCurrentMode(2); // 团队模式
                        updateUser.setUpdateTime(DateUtils.getNowDate());
                        tUserMapper.updateTUser(updateUser);

                        successCount++;
                    } else {
                        errorCount++;
                        Map<String, String> error = new HashMap<>();
                        error.put("mobile", phoneNumber);
                        errorList.add(error);
                    }
                }
            } catch (Exception e) {
                errorCount++;
                Map<String, String> error = new HashMap<>();
                error.put("mobile", phoneNumber);
                errorList.add(error);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("errorCount", errorCount);
        result.put("errorList", errorList);

        return result;
    }

    /**
     * 移除团队成员
     */
    @Override
    @Transactional
    public boolean removeTeamMember(Long teamId, Long userId, Long currentUserId)
    {
        // 检查当前用户是否为团队管理员
        if (!isTeamAdmin(teamId, currentUserId)) {
            throw new ServiceException("只有团队管理员才能移除成员");
        }

        // 不能移除自己
        if (userId.equals(currentUserId)) {
            throw new ServiceException("不能移除自己");
        }

        // 移除团队关系
        int result = teamUserMapper.deleteTeamUserByTeamIdAndUserId(teamId, userId);
        if (result > 0) {
            // 更新用户表
            TUser tUser = new TUser();
            tUser.setUserId(userId);
            tUser.setTeamId(0L);
            tUser.setCurrentMode(1); // 个人模式
            tUserMapper.updateTUser(tUser);
        }

        return result > 0;
    }

    /**
     * 转让管理员
     */
    @Override
    @Transactional
    public boolean transferAdmin(Long teamId, Long userId, Long currentUserId)
    {
        // 检查当前用户是否为团队管理员
        if (!isTeamAdmin(teamId, currentUserId)) {
            throw new ServiceException("只有团队管理员才能转让管理员权限");
        }

        // 检查目标用户是否在团队中
        int count = teamUserMapper.checkUserInTeam(teamId, userId);
        if (count == 0) {
            throw new ServiceException("目标用户不在团队中");
        }

        // 转让管理员权限
        return teamUserMapper.transferAdmin(teamId, currentUserId, userId) > 0;
    }

    /**
     * 扣减团队积分
     */
    @Override
    @Transactional
    public boolean deductTeamScore(Long teamId, Long userId, Long score, Integer scoreType, Integer taskType, String changeType, String remark)
    {
        // 检查团队积分是否足够
        Team team = teamMapper.selectTeamById(teamId);
        if (team == null) {
            throw new ServiceException("团队不存在");
        }

        if (team.getCurrentScore() < score) {
            return false; // 积分不足
        }

        // 扣减积分
        int result = teamMapper.deductTeamScore(teamId, score);
        if (result > 0) {
            // 记录积分变更历史
            TeamScoreHistory history = new TeamScoreHistory();
            history.setTeamId(teamId);
            history.setUserId(userId);
            history.setScoreChange(-score);
            history.setType(scoreType);
            history.setTaskType(taskType);
            history.setChangeType(changeType);
            history.setRemark(remark);
            history.setCreateTime(DateUtils.getNowDate());

            teamScoreHistoryMapper.insertTeamScoreHistory(history);
        }

        return result > 0;
    }

    /**
     * 检查用户是否为团队管理员
     */
    @Override
    public boolean isTeamAdmin(Long teamId, Long userId)
    {
        return teamUserMapper.checkUserIsTeamAdmin(teamId, userId) > 0;
    }

    /**
     * 修改团队信息
     *
     * @param teamId 团队ID
     * @param updateTeamDTO 修改团队信息参数
     * @param currentUserId 当前用户ID
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public boolean updateTeam(Long teamId, UpdateTeamDTO updateTeamDTO, Long currentUserId)
    {
        // 检查用户是否为团队管理员
        if (!isTeamAdmin(teamId, currentUserId)) {
            throw new ServiceException("只有团队管理员才能修改团队信息");
        }

        // 检查团队名称是否已存在（排除当前团队）
        Team existingTeam = teamMapper.selectTeamByName(updateTeamDTO.getTeamName());
        if (existingTeam != null && !existingTeam.getId().equals(teamId)) {
            throw new ServiceException("团队名称已存在");
        }

        // 更新团队信息
        Team team = new Team();
        team.setId(teamId);
        team.setTeamName(updateTeamDTO.getTeamName());
        team.setIndustry(updateTeamDTO.getIndustry());
        team.setUpdateTime(DateUtils.getNowDate());

        return teamMapper.updateTeam(team) > 0;
    }

    /**
     * 增加团队积分
     *
     * @param addTeamScoreDTO 增加团队积分参数
     * @param currentUserId 当前用户ID（操作人）
     * @return 是否添加成功
     */
    @Override
    @Transactional
    public boolean addTeamScore(AddTeamScoreDTO addTeamScoreDTO, Long currentUserId)
    {
        // 检查团队是否存在
        Team team = teamMapper.selectTeamById(addTeamScoreDTO.getTeamId());
        if (team == null) {
            throw new ServiceException("团队不存在");
        }

        // 增加团队积分
        int result = teamMapper.addTeamScore(addTeamScoreDTO.getTeamId(), addTeamScoreDTO.getScore());
        if (result <= 0) {
            throw new ServiceException("增加团队积分失败");
        }

        // 添加积分记录
        TeamScoreHistory history = new TeamScoreHistory();
        history.setTeamId(addTeamScoreDTO.getTeamId());
        history.setUserId(currentUserId);
        history.setScoreChange(addTeamScoreDTO.getScore());
        history.setType(7); // 后台增加（对应Constants.SCORE_TYPE_7）
        history.setTaskType(null); // 后台操作没有关联任务类型
        history.setChangeType("后台添加");
        history.setRemark(StringUtils.isNotEmpty(addTeamScoreDTO.getRemark()) ?
            addTeamScoreDTO.getRemark() : "管理员添加团队积分");
        history.setCreateTime(DateUtils.getNowDate());

        teamScoreHistoryMapper.insertTeamScoreHistory(history);

        return true;
    }

    @Override
    public boolean checkPhoneInTeam(String phone)
    {
        // 通过手机号查询用户
        User user = tUserMapper.selectUserByPhone(phone);
        if (user == null) {
            return false;
        }

        // 查询用户是否在团队中
        TeamUser teamUser = teamUserMapper.selectTeamUserByUserId(user.getUserId());
        return teamUser != null;
    }

    @Override
    @Transactional
    public boolean updateMemberInfo(Long userId, String nickname, String profession)
    {
        // 查询用户的团队信息
        TeamUser teamUser = teamUserMapper.selectTeamUserByUserId(userId);
        if (teamUser == null) {
            throw new ServiceException("您不在任何团队中");
        }

        // 更新团队成员信息
        TeamUser updateTeamUser = new TeamUser();
        updateTeamUser.setId(teamUser.getId());
        updateTeamUser.setNickname(nickname);
        updateTeamUser.setProfession(profession);

        return teamUserMapper.updateTeamUser(updateTeamUser) > 0;
    }

    @Override
    @Transactional
    public boolean updateMemberNickname(Long teamId, Long targetUserId, String nickname, Long currentUserId)
    {
        // 检查当前用户是否为团队管理员
        if (!isTeamAdmin(teamId, currentUserId)) {
            throw new ServiceException("只有团队管理员才能修改成员昵称");
        }

        // 检查目标用户是否在该团队中
        TeamUser teamUser = teamUserMapper.selectTeamUserByTeamIdAndUserId(teamId, targetUserId);
        if (teamUser == null) {
            throw new ServiceException("该成员不在团队中");
        }

        // 更新团队成员昵称
        TeamUser updateTeamUser = new TeamUser();
        updateTeamUser.setId(teamUser.getId());
        updateTeamUser.setNickname(nickname);

        return teamUserMapper.updateTeamUser(updateTeamUser) > 0;
    }

    @Override
    public Team selectCreate(Long currentUserId) {
        return teamMapper.selectCreate(currentUserId);
    }
}