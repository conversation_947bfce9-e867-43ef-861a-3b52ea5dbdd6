<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.MaterialIpUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.dataxai.web.domain.MaterialIpUser">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="material_ip_id" property="materialIpId" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, material_ip_id, user_id
    </sql>

    <!-- 插入收藏记录 -->
    <insert id="insert" parameterType="com.dataxai.web.domain.MaterialIpUser">
        INSERT INTO t_material_ip_user (material_ip_id, user_id)
        VALUES (#{materialIpId}, #{userId})
    </insert>

    <!-- 根据ID删除收藏记录 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM t_material_ip_user WHERE id = #{id}
    </delete>

    <!-- 根据用户ID和IP ID删除收藏记录 -->
    <delete id="deleteByUserAndIp">
        DELETE FROM t_material_ip_user 
        WHERE user_id = #{userId} AND material_ip_id = #{ipId}
    </delete>

    <!-- 新增：根据IP ID删除所有收藏记录 -->
    <delete id="deleteByIpId">
        DELETE FROM t_material_ip_user WHERE material_ip_id = #{ipId}
    </delete>

    <!-- 根据用户ID查询收藏列表（分页） -->
    <select id="selectByUser" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_material_ip_user
        WHERE user_id = #{userId}
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据用户ID统计收藏数量 -->
    <select id="countByUser" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_material_ip_user WHERE user_id = #{userId}
    </select>

    <!-- 检查收藏是否存在 -->
    <select id="checkFavoriteExists" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_material_ip_user
        WHERE user_id = #{userId} AND material_ip_id = #{ipId}
    </select>

    <!-- 获取用户收藏的IP映射 -->
    <select id="selectUserFavoriteMap" resultType="java.util.Map">
        SELECT
            material_ip_id as ipId,
            id as favoriteId
        FROM t_material_ip_user
        WHERE user_id = #{userId}
        <if test="ipIds != null and ipIds.size() > 0">
            AND material_ip_id IN
            <foreach collection="ipIds" item="ipId" open="(" separator="," close=")">
                #{ipId}
            </foreach>
        </if>
    </select>

    <!-- 根据用户ID和IP ID查询收藏记录 -->
    <select id="selectByUserAndIp" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_material_ip_user
        WHERE user_id = #{userId} AND material_ip_id = #{ipId}
        LIMIT 1
    </select>

</mapper>
