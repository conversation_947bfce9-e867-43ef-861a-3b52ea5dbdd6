package com.dataxai.web.service.export.strategy;
/**
 * 针对中文模板（templateType = 1）的具体导出策略实现。
 *
 * @see BaseExportStrategy
 */
import com.dataxai.domain.TExcelCustomTemplateDTO;
import com.dataxai.web.service.export.support.ExcelFillService;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TemplateModeStrategy extends BaseExportStrategy {

    /**
     * 通过构造函数注入依赖。
     * @param excelFillService 提供填充逻辑的服务类。
     */
    @Autowired
    public TemplateModeStrategy(ExcelFillService excelFillService) {
        super(excelFillService);
    }

    /**
     * {@inheritDoc}
     * <p>
     * 此策略仅支持 {@code templateType} 为 1 的情况。
     */
    @Override
    public boolean supports(Long templateType) {
        return Long.valueOf(1L).equals(templateType);
    }

    /**
     * {@inheritDoc}
     * <p>
     * 将具体的填充逻辑委托给 {@link ExcelFillService#fillForTemplateMode}。
     */
    @Override
    protected void doFill(TExcelCustomTemplateDTO dto, Workbook workbook, Sheet sheet) throws Exception {
        excelFillService.fillForTemplateMode(dto, workbook, sheet);
    }
}
