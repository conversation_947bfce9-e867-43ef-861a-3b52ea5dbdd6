package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialStyleCategory;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface MaterialStyleCategoryMapper {
    List<MaterialStyleCategory> selectByCondition(@Param("name") String name, @Param("taskType") Integer taskType);

    MaterialStyleCategory selectById(@Param("id") Integer id);

    int insert(MaterialStyleCategory category);

    int update(MaterialStyleCategory category);

    int deleteById(@Param("id") Integer id);

    // 用于分页查询的总数
    int countByCondition(@Param("name") String name, @Param("taskType") Integer taskType);

    // 分页查询
    List<MaterialStyleCategory> selectPageByCondition(@Param("offset") int offset,
                                                      @Param("pageSize") int pageSize,
                                                      @Param("name") String name,
                                                      @Param("taskType") Integer taskType);
}