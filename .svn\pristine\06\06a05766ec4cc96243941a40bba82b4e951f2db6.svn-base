package com.dataxai.web.controller.platform;

import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.uuid.UUID;
import com.dataxai.domain.TUser;
import com.dataxai.mapper.TUserMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.env.Environment;
import com.dataxai.web.service.IFrontUserService;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;

/**
 * 第三方平台授权控制器
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Api(tags = "第三方平台授权接口")
@RestController
@RequestMapping("/platform")
public class ThirdPartyAuthController extends BaseController {

    @Autowired
    private TUserMapper tUserMapper;

    @Autowired
    private IFrontUserService frontUserService;

    @Autowired
    private Environment environment;

    // 平台密钥，从配置文件读取，如果没有配置则使用默认值
    @Value("${platform.api.key:PLATFORM_KEY_2025}")
    private String platformKey;

    /**
     * 申请授权接口
     * @param request HTTP请求对象
     * @param requestBody 请求体，包含phoneNumber
     * @return 授权结果，包含client_code和client_secret
     */
    @ApiOperation("申请授权")
    @PostMapping("/applyToken")
    @Transactional
    public R<Map<String, Object>> applyToken(
            HttpServletRequest request,
            @ApiParam("申请授权参数") @RequestBody Map<String, String> requestBody) {

        try {
            // 1. 验证平台token
            String authorization = request.getHeader("Authorization");
            String time = request.getHeader("Time");

            if (StringUtils.isEmpty(authorization) || StringUtils.isEmpty(time)) {
                return R.fail("缺少必要的鉴权参数");
            }

            // 提取Bearer Token
            String token = null;
            if (authorization.startsWith("Bearer ")) {
                token = authorization.substring(7);
            } else {
                return R.fail("无效的Authorization格式");
            }

            // 验证时间戳（防止重放攻击，环境为local时1小时，其余为5分钟）
            try {
                long requestTime = Long.parseLong(time);
                long currentTime = System.currentTimeMillis() / 1000; // 秒级
                boolean isLocal = Arrays.asList(environment.getActiveProfiles()).contains("local");
                long allowedSkewSeconds = isLocal ? 60L * 60L : 5L * 60L; // local: 1小时，其它: 5分钟
                if (Math.abs(currentTime - requestTime) > allowedSkewSeconds) {
                    return R.fail("请求已过期");
                }
            } catch (NumberFormatException e) {
                return R.fail("无效的时间戳");
            }

            // 验证平台token
            String expectedToken = generateToken(platformKey, time);
            if (!token.equals(expectedToken)) {
                return R.fail("无效的平台访问令牌"+time+','+expectedToken);
            }

            // 2. 获取手机号
            String phoneNumber = requestBody.get("phoneNumber");
            if (StringUtils.isEmpty(phoneNumber)) {
                return R.fail("手机号不能为空");
            }

            // 3. 查找或创建用户
            com.dataxai.common.core.domain.model.User existingUser = tUserMapper.selectUserByPhone(phoneNumber);
            TUser user = null;

            if (existingUser == null) {
                // 创建新用户
                user = new TUser();
                user.setPhone(phoneNumber);
                user.setNickName("");
                user.setGender(0);
                user.setEnable(0);
                user.setDelFlag(0);
                user.setCreateTime(DateUtils.getNowDate());

                int result = tUserMapper.insertTUser(user);
                if (result <= 0) {
                    return R.fail("创建用户失败");
                }

                // 重新查询获取完整的TUser对象
                existingUser = tUserMapper.selectUserByPhone(phoneNumber);
                if (existingUser != null) {
                    user = tUserMapper.selectTUserById(existingUser.getUserId());
                }

                // 赠送默认套餐（如未赠送）
                frontUserService.loginOrRegisterByPhone(phoneNumber);
            } else {
                // 根据userId查询TUser类型的完整对象
                user = tUserMapper.selectTUserById(existingUser.getUserId());
                // 赠送默认套餐（如未赠送）
                frontUserService.loginOrRegisterByPhone(phoneNumber);
            }

            if (user == null) {
                return R.fail("用户查询失败");
            }

            // 4. 生成或更新client_code和client_secret
            if (StringUtils.isEmpty(user.getClientCode()) || StringUtils.isEmpty(user.getClientSecret())) {
                String clientCode = generateClientCode();
                String clientSecret = generateClientSecret();

                user.setClientCode(clientCode);
                user.setClientSecret(clientSecret);
                user.setUpdateTime(DateUtils.getNowDate());

                int updateResult = tUserMapper.updateTUser(user);
                if (updateResult <= 0) {
                    return R.fail("更新用户授权信息失败");
                }
            }

            // 5. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("phoneNumber", phoneNumber);
            result.put("client_code", user.getClientCode());
            result.put("client_secret", user.getClientSecret());

            return R.ok(result, "授权成功");

        } catch (Exception e) {
            logger.error("申请授权失败", e);
            return R.fail("申请授权失败: " + e.getMessage());
        }
    }

    /**
     * 生成客户端编码
     */
    private String generateClientCode() {
        // 生成8位大写字母和数字的组合
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
    }

    /**
     * 生成客户端密钥
     */
    private String generateClientSecret() {
        // 生成32位的密钥
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成Token
     */
    private String generateToken(String secret, String time) {
        String source = secret + time;
        return md5(source).toLowerCase();
    }

    /**
     * MD5加密
     */
    private String md5(String source) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(source.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                String hex = Integer.toHexString(b & 0xff);
                if (hex.length() == 1) {
                    sb.append("0");
                }
                sb.append(hex);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}