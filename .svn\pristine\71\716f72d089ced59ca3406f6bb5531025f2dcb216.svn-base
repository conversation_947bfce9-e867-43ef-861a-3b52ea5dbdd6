package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialStyle;
import com.dataxai.web.mapper.MaterialStyleMapper;
import com.dataxai.web.service.MaterialStyleService;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.utils.ThumbnailUtils;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.Constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MaterialStyleServiceImpl implements MaterialStyleService {

    @Autowired
    private MaterialStyleMapper materialStyleMapper;
    
    @Autowired
    private AliYunFileService aliYunFileService;

    @Override
    public List<MaterialStyle> queryAll(String name, Integer status, Integer categoryId, Integer taskType) {
        return materialStyleMapper.selectByCondition(name, status, categoryId, taskType);
    }

    @Override
    public MaterialStyle getById(Integer id) {
        return materialStyleMapper.selectById(id);
    }

    @Override
    public boolean addMaterialStyle(MaterialStyle materialStyle) {
        materialStyle.setCreateTime(new Date());
        materialStyle.setUpdateTime(new Date());
        
        // 生成缩略图
        generateAndSaveThumbnail(materialStyle);
        
        return materialStyleMapper.insert(materialStyle) > 0;
    }

    @Override
    public boolean updateMaterialStyle(MaterialStyle materialStyle) {
        materialStyle.setUpdateTime(new Date());
        
        // 生成缩略图
        generateAndSaveThumbnail(materialStyle);
        
        return materialStyleMapper.update(materialStyle) > 0;
    }

    @Override
    public boolean deleteMaterialStyle(Integer id) {
        return materialStyleMapper.deleteById(id) > 0;
    }

    @Override
    public List<MaterialStyle> queryPage(Integer pageNum, Integer pageSize,
                                         String name, Integer status, Integer categoryId, Integer taskType) {
        int offset = (pageNum - 1) * pageSize;
        return materialStyleMapper.selectPageByCondition(offset, pageSize, name, status, categoryId, taskType);
    }

    @Override
    public int countByCondition(String name, Integer status, Integer categoryId, Integer taskType) {
        return materialStyleMapper.countByCondition(name, status, categoryId, taskType);
    }
    
    /**
     * 生成并保存缩略图
     * @param materialStyle 素材风格对象
     */
    private void generateAndSaveThumbnail(MaterialStyle materialStyle) {
        try {
            // 检查是否有风格图片URL
            if (StringUtils.hasText(materialStyle.getStyleUrl())) {
                log.info("开始为风格素材生成缩略图: styleUrl={}", materialStyle.getStyleUrl());
                
                // 从URL下载图片并生成缩略图
                String thumbnailUrl = generateThumbnailFromUrl(materialStyle.getStyleUrl());
                if (StringUtils.hasText(thumbnailUrl)) {
                    // 直接存储完整的缩略图URL，不需要去掉前缀
                    materialStyle.setThumbnailImgUrl(thumbnailUrl);
                    log.info("缩略图生成成功: thumbnailUrl={}", thumbnailUrl);
                } else {
                    log.warn("缩略图生成失败，使用原图URL");
                    materialStyle.setThumbnailImgUrl(materialStyle.getStyleUrl());
                }
            } else {
                log.info("风格素材没有图片URL，跳过缩略图生成");
            }
        } catch (Exception e) {
            log.error("生成缩略图时发生异常: {}", e.getMessage(), e);
            // 缩略图生成失败时，使用原图URL
            materialStyle.setThumbnailImgUrl(materialStyle.getStyleUrl());
        }
    }
    
    /**
     * 从URL生成缩略图
     * @param imageUrl 原图URL
     * @return 缩略图URL
     */
    private String generateThumbnailFromUrl(String imageUrl) {
        try {
            // 检查图片URL是否为空
            if (!StringUtils.hasText(imageUrl)) {
                return null;
            }
            
            // 使用项目中已有的阿里云图片处理服务
            // 直接返回带缩略图处理参数的URL
            String thumbnailUrl = imageUrl + Constants.OOS_URL_THUMBNAIL;
            log.info("生成缩略图URL: 原图={}, 缩略图={}", imageUrl, thumbnailUrl);
            return thumbnailUrl;
            
        } catch (Exception e) {
            log.error("从URL生成缩略图失败: {}", e.getMessage(), e);
            return null;
        }
    }
}