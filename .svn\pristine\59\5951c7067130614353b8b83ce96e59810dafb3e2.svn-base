// 获取图片宽度和高度
export function getImageSize(
	url: string
): Promise<{ width: number; height: number }> {
	return new Promise((resolve, reject) => {
		const img = new Image()
		img.onload = function () {
			resolve({ width: img.width, height: img.height })
		}
		img.src = url
	})
}

// Ai生成违规图片特殊标记
export const AI_VIOLATION_FLAG = 'Violating_regulations'

// 检查图片是否应该被禁用勾选
export function isImageDisabled(imageUrl: string | null | undefined): boolean {
	// 检查图片地址是否为空、null或包含AI_VIOLATION_FLAG
	return !imageUrl || imageUrl === null || imageUrl.includes(AI_VIOLATION_FLAG);
}

// 通用的图片禁用检查函数，支持不同的数据结构
export function checkImageDisabled(item: any, type: 'blockingOut' | 'similar' | 'textToImg' | 'continuous' | 'extract' | 'filter' | 'titleExtraction' | 'cut' | 'clear' | 'uploadImage' | 'gather'): boolean {
	switch (type) {
		case 'blockingOut':
		case 'cut':
		case 'clear':
			const imgUrl = item?.taskOrdinalList?.[0]?.ordinalImgResultList?.[0]?.resImgUrl;
			return isImageDisabled(imgUrl);

		case 'similar':
		case 'textToImg':
		case 'continuous':
		case 'extract':
			// 这些页面使用处理过的 imageUrl.taskImage
			return isImageDisabled(item?.taskImage);

		case 'uploadImage':
			return isImageDisabled(item?.originalUrl);

		case 'filter':
		case 'titleExtraction':
		case 'gather':
			// 这些页面可能使用不同的结构，暂时使用通用检查
			return isImageDisabled(item?.imageUrl || item?.resImgUrl || item?.taskImage || item?.productImageUrl);

		default:
			return isImageDisabled(item?.imageUrl || item?.resImgUrl || item?.taskImage || item?.productImageUrl);
	}
}
