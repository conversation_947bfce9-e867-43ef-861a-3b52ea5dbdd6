/**
 * @Author: wuy<PERSON>
 * @Date: 2024/1/23
 * @Description: ""
 */
import { IOpenPayModalProps } from '@/business-component/pay-modal/PayModal'
import { CloseCircleFilled, WechatOutlined } from '@ant-design/icons'
import { App, QRCode } from 'antd'
import classNames from 'classnames'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { useEffect, useState } from 'react'
import {
	canUpdatePackage,
	getPayListByRefueling,
	getPayListByVipPackage
} from '@/api/vip'
import { useMemoizedFn } from 'ahooks'
import { ISocketMessagePayStatus } from '@/types/socket'

export const PayModalComponent = (
	props: IOpenPayModalProps & { closeModal: () => void }
) => {
	const { closeModal, payType, packageType, refuelingBagId } = props
	// const [messageApi, contextPlaceHolder] = message.useMessage()
	const { message } = App.useApp()

	const [userInfo] = useAtomMethod(userinfoService.userInfo)

	// 当前选中付费列表的详细信息
	const [activePackage, setActivePackage] = useState<null | {
		packageId: string
		money: number
		qrUrl: string
	}>(null)

	// 付费列表展示
	const [payList, setPayList] = useState<
		Array<{
			id: string
			name: string
			cycle: number
			score: number
			money: number
		}>
	>([])

	// 获取当前套餐详情
	const handleActivePackage = useMemoizedFn((activeId: string) => {
		setActivePackage({
			packageId: activeId,
			money: -1,
			qrUrl: ''
		})
		canUpdatePackage({
			subId: activeId,
			packageId:
				payType === 1 ? (packageType ?? '') : (userInfo?.packageId ?? ''),
			type: payType
		})
			.then(({ money, update, codeUrl }) => {
				// 根据 update 来判断能否升级，后期增加 qrUrl 字符串
				if (update || payType === 0) {
					setActivePackage({
						packageId: activeId,
						money: money,
						qrUrl: codeUrl ?? '-'
					})
				} else {
					message.warning('当前套餐不支持付费购买，请选择其他套餐进行购买')
				}
			})
			.catch((err) => {
				if (err?.data?.code === 401) {
					closeModal()
				}
				message.warning(err?.data?.msg)
			})
	})

	// 默认选中第一项
	useEffect(() => {
		if (refuelingBagId) {
			handleActivePackage(refuelingBagId)
		} else if (payList[0]?.id) {
			handleActivePackage(payList[0].id)
		}
	}, [refuelingBagId, handleActivePackage, payList])
	// 动态获取套餐付费列表
	useEffect(() => {
		if (payType) {
			getPayListByVipPackage(`${packageType}`)
				.then((res) => {
					setPayList(
						res?.data?.map((item) => {
							const {
								tariffPackageSubId,
								tariffPackageSubName,
								subMoney,
								cycle,
								score
							} = item
							return {
								id: tariffPackageSubId,
								name: tariffPackageSubName,
								money: subMoney,
								cycle,
								score
							}
						})
					)
				})
				.catch((err) => {
					if (err?.data?.code === 401) {
						closeModal()
					}
					message.warning(err?.data?.msg)
				})
		} else {
			getPayListByRefueling()
				.then((res) => {
					setPayList(
						res.data?.map((item) => {
							const { refuelingBagId, refuelingBagName, money, cycle, score } =
								item
							return {
								id: refuelingBagId,
								name: refuelingBagName,
								money,
								cycle,
								score
							}
						})
					)
				})
				.catch((err) => {
					if (err?.data?.code === 401) {
						closeModal()
					}
					message.warning(err?.data?.msg)
				})
		}
		// eslint-disable-next-line
	}, [payType, packageType])

	const handlePaySocket = useMemoizedFn((data: ISocketMessagePayStatus) => {
		console.log('www--------->支付完成', data)
		if (data.status) {
			message.success(`扫码支付成功`)
			closeModal()
		} else {
			message.warning('扫码支付失败，请重新扫码')
			if (activePackage?.packageId) {
				handleActivePackage(activePackage.packageId)
			}
		}
	})

	useEffect(() => {
		userinfoService.mitt.on('onPayStatus', handlePaySocket)
		return () => {
			userinfoService.mitt.off('onPayStatus', handlePaySocket)
		}
		// eslint-disable-next-line
	}, [])

	return (
		<div
			className={
				'relative w-[630px] pb-[20px]  rounded-lg border border-normal overflow-hidden bg-gradient-to-b from-[#E3F0FF] from-10% to-white'
			}
		>
			<div
				className={'absolute top-[10px] right-[10px] cursor-pointer'}
				onClick={closeModal}
			>
				<CloseCircleFilled
					className={'text-[22px] text-subDescText hover:text-primary'}
				/>
			</div>
			<div className={'p-[20px]'}>
				<div className={'text-center text-normal text-[24px] font-bold'}>
					{payType === 0 ? '购买加油包' : '升级基础套餐'}
				</div>
				{/* <div className={'flex items-center mt-[10px] text-normal flex-col'}>
					{payType === 0 ? (
						''
					) : (
						<div className={'text-center'}>
							<div>
								您的当前套餐为
								<span className={'ml-[8px] text-primary font-bold'}>
									{userInfo?.packageName}
								</span>
							</div>
							<div>
								剩余积分价值
								<span className={'ml-[8px] text-[#F42929] font-bold'}>
									¥{userInfo?.residueScoreValue}
								</span>
							</div>
						</div>
					)}
				</div> */}
				<div className={'flex justify-between mt-[20px]'}>
					<div
						className={
							'w-[230px] border border-normal rounded-lg bg-white flex items-center flex-col pb-[30px]'
						}
					>
						<div className={'mt-[20px] text-normal'}>购买所需费用</div>
						<div className={'text-[#F42929] text-[30px] font-bold'}>
							¥
							{activePackage && activePackage.money >= 0
								? activePackage.money
								: '--'}
						</div>
						<div className={'mt-[2px]'}>
							<QRCode
								size={160}
								errorLevel="H"
								value={activePackage?.qrUrl || '-'}
								onRefresh={() => {
									if (activePackage?.packageId) {
										handleActivePackage(activePackage.packageId)
									}
								}}
								status={activePackage?.qrUrl ? 'active' : 'loading'}
							/>
						</div>
						<div className={'mt-[10px] text-normal flex'}>
							<div
								className={
									'w-[22px] h-[22px] rounded-[50%] bg-[#50b674] flex justify-center items-center'
								}
							>
								<WechatOutlined className={'text-white'} />
							</div>
							<div className={'ml-[10px]'}>微信扫码支付</div>
						</div>
						<div className={'mt-[10px] text-subtext text-[14px]'}>
							虚拟商品，不支持退款
						</div>
					</div>
					<div className={'flex-1 ml-[10px]'}>
						{payList.map((pay, index) => {
							return (
								<div
									key={pay.id}
									className={classNames(
										'flex border border-normal rounded-lg  justify-between p-[20px] hover:border-primary cursor-pointer mt-[10px]',
										pay.id === activePackage?.packageId
											? 'border-primary bg-[#32649F]/[0.2]'
											: 'bg-white',
										index === 0 && 'mt-0'
									)}
									onClick={() => {
										handleActivePackage(pay.id)
									}}
								>
									<div className={'flex-1'}>
										<div className={'text-normal'}>{pay.name}</div>
										<div
											className={'text-subtext mt-[5px]'}
										>{`权益包含${pay.score}积分`}</div>
										<div
											className={'text-subtext mt-[5px]'}
										>{`有效期${pay.cycle}天`}</div>
									</div>
									<div
										className={
											'text-[#F42929] text-[30px] font-bold w-[100px] text-right'
										}
									>
										¥{pay.money}
									</div>
								</div>
							)
						})}
					</div>
				</div>
			</div>
		</div>
	)
}
