/**
 * @Author: xujing 
 * @Date: 2025/8/6
 * @Description: ""
 * @edit： 22 xhl
 */
import { useEffect, useMemo, useRef, memo, useState, } from 'react';
import { Table, Modal, Button, Input, message, Checkbox, Select, Empty, Tabs, Upload, Tag, Dropdown, Tooltip, } from 'antd';
import { FolderOpenOutlined, PlusOutlined, UploadOutlined, EditOutlined, QuestionCircleOutlined, MinusOutlined, MoreOutlined } from '@ant-design/icons';
import {
    getImportTemplateList, getGoodsTemplateGroupList, addGoodsTemplateGroup, editGoodsTemplateGroup, delGoodsTemplateGroup, parseExcelFile,
    addGoodsTemplate, delGoodsTemplate, copyGoodsTemplate, moveGoodsTemplate, getGoodsTemplateDetail, editGoodsTemplate
} from '@/api/task'
import { uploadFileOssApi } from '@/api/common'
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import './index.css'
import { getTeamMembers } from '@/api/task'
import { getUserInfo } from '@/api/common'
const { Option } = Select;
    
export const ImportTemplate = () => {
    const navigate = useNavigate()

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()

    const [formTemplateName, setFormTemplateName] = useState(''); // 模板名称
    const [groups, setGroups] = useState<any[]>([]);
    const [templateType, setTemplateType] = useState([{templateName:'Temu半托',id:1},{templateName:'TikTok',id:4}]); // 添加模板类型
    const [activeTab, setActiveTab] = useState({ id: 0, groupName: "全部" });
    const [modalVisible, setModalVisible] = useState(false);
    const [groupName, setGroupName] = useState('');
    const [editingGroupId, setEditingGroupId] = useState('');
	const [userId, setUserId] = useState<string>('');
	const [selectedTeam, setSelectedTeam] = useState<Array <any> | null>([]);
    // 表格数据
    const [dataSource, setDataSource] = useState([])
    // 表格loading
    const [tableLoading, setTableLoading] = useState(false)
    // 分页配置
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    })
    
	//获取团队id
	useEffect(() => { 
		getUserInfo().then(res => { 

			getTeamMembers({ teamId: res.teamId }).then((res:any) => { 
				
				setSelectedTeam(res.data)
			});
		});
	}, []);
    //获取导入商品模版列表
    const fetchImportTemplateList = async (
        pageNum: number,
        pageSize: number,
        groupId: any,
        userId: string,
    ) => {
        setDataSource([])
        setTableLoading(true)
        try {
            const response: any = await getImportTemplateList({
                pageNum,
                pageSize,
                tExcelCustomGroupId: groupId == '0' ? '' : groupId,
                templateName: formTemplateName || '',
                userId
            })
            if (response.data) {
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
            }
        } catch (error) {
            console.error('获取数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }
    //获取分组列表
    const fetchGroupListData = (groupName: string) => {
        getGoodsTemplateGroupList({ pageNum: 1, pageSize: 99, groupName }).then((res: any) => {
            setGroups([{ id: 0, groupName: "全部" }, ...res.data]);
        }).catch(err => {
            messageApi.error(`获取分组列表失败:${err?.msg || err?.data?.msg || ''}`)
        })
    }

    useEffect(() => {
        fetchImportTemplateList(1, 10, activeTab?.id,userId)
        fetchGroupListData('')
    }, []);


    const [fileListCsv, setFileListCsv] = useState<File | null>(null);// 上传的csv文件列表
    const [nextLoading, setNextLoading] = useState(false);


    const [overallData, setOverallData] = useState<any>([])
    const [baseData, setBaseData] = useState([])
    const [spuData, setSpuData] = useState([])
    const [skuData, setSkuData] = useState([])
    // 基础信息行数据结构（对应基础信息表格每行）
    interface BaseRow {
        column: string;
        name: string;
        type: string;
        value: string;
    }
    // SPU/ SKU 信息行数据结构（和基础信息类似，根据实际字段微调）
    interface SpuSkuRow {
        column: string;
        name: string;
        type: string;
        value: string;
        options: string;
    }

    // 在组件内初始化适配的数据结构
    const [baseValues, setBaseValues] = useState<BaseRow[]>([]);
    const [spuValues, setSpuValues] = useState<SpuSkuRow[]>([]);
    const [skuValues, setSkuValues] = useState<SpuSkuRow[][]>([]);
    const [initSkuValues, setInitSkuValues] = useState<SpuSkuRow[]>([]);

    // 生成唯一行ID（用于 Table 的稳定 rowKey）
    const genRowId = () => (typeof window !== 'undefined' && (window as any).crypto?.randomUUID ? (window as any).crypto.randomUUID() : `${Date.now()}-${Math.random().toString(36).slice(2)}`);
    const [skuRowIds, setSkuRowIds] = useState<string[]>([]);

    const [fileUrl, setFileUrl] = useState('')

    const [currentType, setCurrentType] = useState('add')

    const resetData = () => {
        setOverallData([]);
        setBaseData([]);
        setBaseValues([]);
        setSpuData([]);
        setSpuValues([]);
        setSkuData([]);
        setSkuValues([[]]);
        setFileUrl('');
        setInitSkuValues([]);
        setSkuRowIds([]);
    }

    const nextStep = () => {
        if (!templateName.trim()) {
            messageApi.warning('请填写模板名称');
            return;
        }
        if (!fileListCsv?.name) {
            messageApi.warning('请上传模板文件');
            return;
        }
        resetData()
        setNextLoading(true);
        parseExcelFile({ file: fileListCsv ,templateType:selectedModel}).then((res: any) => {
            setOverallData(res?.overallList || []);
            setBaseData(res?.baseList || [])
            setSpuData(res?.spuList || [])
            setSkuData(res?.skuList || [])
            setFileUrl(res?.file || '')

            // 处理基础信息
            const initialBaseValues: BaseRow[] = res?.baseList?.map((item: any, index: number) => ({
                column: item.column,
                name: item.name,
                type: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : 'fixed',
                value: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : '',
                options:item.options || ''
            }));
            setBaseValues(initialBaseValues);
            // 处理 SPU 信息（同理，按实际字段映射）
            const initialSpuValues: SpuSkuRow[] = res?.spuList?.map((item: any, index: number) => ({
                column: item.column,
                name: item.name,
                type: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : 'fixed',
                value: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : '',
                options:item.options || ''
            }));
            setSpuValues(initialSpuValues);
            const createSkuRow = (item: any): SpuSkuRow => ({
                column: item.column,
                name: item.name,
                type: item.type === 'spu' ? 'spu' : item.type === 'sku' ? 'sku' : 'fixed',
                value: item.type === 'spu' ? 'spu' : item.type === 'sku' ? 'sku' : '',
                options:item.options || ''
            });

            const list = res?.skuList || [];
            const initialSkuValues: SpuSkuRow[][] = [list.map(createSkuRow)];
            const initialSku: SpuSkuRow[] = list.map(createSkuRow);
            setSkuValues(initialSkuValues);
            setInitSkuValues(initialSku);
            setSkuRowIds([genRowId()])
            setAddProductTemplate(false)
            setIsModalVisible(true);
            setCurrentType('add');
            setTimeout(()=>{
                console.log(baseValues,spuValues,skuValues);
                
            },340)
        }).catch(err => {
            messageApi.error(`解析 excel 文件失败:${err?.msg || err?.data?.msg || ''}`);
        }).finally(() => {
            setNextLoading(false);
        });
    };


    // 表格列配置
    const columns = [
        {
            title: '模板名称',
            dataIndex: 'templateName',
            key: 'templateName',
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            render: (updateTime: string) => (
                <p>{dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            )
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            render: (createTime: string) => (
                <p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            )
        },
        {
            title: '操作',
            key: 'action',
            render: (_: any, record: any) => (
                <div className='flex items-center'>
                    <Button
                        type="link"
                        style={{ color: '#32649f', paddingLeft: 0 }}
                        onClick={() => handleEdit(record)}
                    >
                        编辑
                    </Button>
                    <Button
                        type="link"
                        style={{ color: '#32649f' }}
                        onClick={() => handleCopy(record)}
                    >
                        复制
                    </Button>
                    <Button
                        type="link"
                        style={{ color: '#32649f' }}
                        onClick={() => handleDelete(record)}
                    >
                        删除
                    </Button>
                    <Button
                        type="link"
                        style={{ color: '#32649f' }}
                        onClick={() => moveTemplate(record)}
                    >
                        移动到
                    </Button>

                </div>
            )
        }
    ]
    const [currentId, setCurrentId] = useState(null)
    // 编辑
    const handleEdit = (record: any) => {
        resetData()
        getGoodsTemplateDetail({ id: record?.id }).then((res: any) => {

            setTemplateName(res?.templatename || '')
            setCurrentId(res?.id || null)
            setOverallData(res?.overallList || []);
            setBaseData(res?.baseList || [])
            setSpuData(res?.spuList || [])
            setSkuData(res?.skuList || [])
            setFileUrl(res?.file || '')
            setBaseValues(res?.baseValueList || []);
            setSpuValues(res?.spuValueList || []);
            setSkuValues(res?.skuValueList || [[]]);
            setSkuRowIds(((res?.skuValueList || [[]]) as any[]).map(() => genRowId()))
            setSelectedModel(res?.templateType || null);
            const initialSkuValues: SpuSkuRow[] = res?.skuList?.map((item: any, index: number) => ({
                column: item.column,
                name: item.name,
                type: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : 'fixed',
                value: item.type == 'spu' ? 'spu' : item.type == 'sku' ? 'sku' : '',
            }));
            setInitSkuValues(initialSkuValues);
            setAddProductTemplate(false)
            setIsModalVisible(true);
            setCurrentType('edit');
        })
    }
    // 删除导入商品模板
    const handleDelete = (item: any) => {
        modal.confirm({
            title: '提示',
            content: `确定要删除该导入商品模板 "${item?.templateName}" 吗？`,
            centered: true,
            onOk: () => {
                delGoodsTemplate({ id: item?.id }).then(res => {
                    messageApi.success('删除成功')
                    fetchImportTemplateList(1, 10, activeTab?.id,userId)
                }).catch(err => {
                    messageApi.error(`删除失败:${err?.msg || err?.data?.msg || ''}`)
                })
            }
        });
    };
    // 复制导入商品模板
    const handleCopy = (item: any) => {
        copyGoodsTemplate({
            id: item?.id,
        }).then(res => {
            messageApi.success('模板复制成功')
            fetchImportTemplateList(1, 10, activeTab?.id,userId)
        }).catch(err => {
            messageApi.error(`模板复制失败:${err?.msg || err?.data?.msg || ''}`)
        })

    };

    // 在组件中添加分页变化处理函数
    const handleTableChange = (pagination: any) => {
        setPagination({
            ...pagination,
            current: pagination.current,
            pageSize: pagination.pageSize
        })
        fetchImportTemplateList(pagination.current, pagination.pageSize, activeTab?.id,userId)
    }

    const [isModalVisible, setIsModalVisible] = useState(false)

    const handleCancel = () => {
        modal.confirm({
            title: '提示',
            content: '关闭后不会保存，是否直接关闭?',
            centered: true,
            onOk: () => {
                setIsModalVisible(false)
            }
        });
    }

    // 添加状态管理选择的值
    const [selectedValues, setSelectedValues] = useState<Record<string, string>>({});

    // 稳定的表格组件配置，避免每次渲染都创建新函数导致闪烁
    const stableTableComponents = useMemo(() => ({
        body: {
            cell: (props: any) => (
                <td {...props} className={`${props.className} ant-table-cell-row-hover`} style={{ padding: 0 }} />
            )
        }
    }), []);

    // 公共配置
    const COMMON_SELECT_OPTIONS = [
        { value: 'fixed', label: '固定值' },
        { value: 'dynamics', label: '动态值' },
        { value: 'multiple', label: '多个值' },
        { value: 'spu', label: 'SPU货号' },
        { value: 'sku', label: 'SKU货号' },
    ];

    const [addValueModal, setAddValueModal] = useState(false)
    // 当前正在编辑"多个值"的单元格上下文
    const [currentMultipleContext, setCurrentMultipleContext] = useState<{
        listType: string;
        index: number;
        colIndex: number;
    } | null>(null)

    const openAddMultiple = (listType: string, colIndex: number, value: string, rowIndex?: number) => {
        setCurrentMultipleContext({ listType, index: rowIndex || 0, colIndex });
        try {
            const parsed = value ? JSON.parse(value) : [];
            setMultipleValueList(Array.isArray(parsed) ? parsed : []);
        } catch {
            setMultipleValueList([]);
        }
        setAddValueModal(true)
    }



    // 校验错误状态（按列索引）
    const [baseErrors, setBaseErrors] = useState<boolean[][]>([]);
    const [spuErrors, setSpuErrors] = useState<boolean[][]>([]);
    const [skuErrors, setSkuErrors] = useState<boolean[][]>([]);

    // 低卡顿文本域：本地缓冲，失焦提交
    const CellTextArea = memo(({ value, onCommit, id }: { value: string; onCommit: (v: string) => void; id?: string }) => {
        const [local, setLocal] = useState<string>(value || '')
        const [isFocused, setIsFocused] = useState(false)

        // 只有在非聚焦状态下才同步外部值，避免用户输入时被覆盖导致闪烁
        useEffect(() => {
            if (!isFocused) {
                setLocal(value || '')
            }
        }, [value, isFocused])

        return (
            <div style={{ width: '100%' }} id={id}>
                <Input.TextArea
                    variant="borderless"
                    value={local}
                    autoSize={{ minRows: 2, maxRows: 2 }}
                    onChange={(e) => setLocal(e.target.value)}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => {
                        setIsFocused(false)
                        onCommit(local)
                    }}
                />
            </div>
        )
    }, (prevProps, nextProps) => {
        // 精确的比较函数，避免不必要的重渲染
        return prevProps.value === nextProps.value &&
            prevProps.id === nextProps.id;
    })

    // 低卡顿单行输入：本地缓冲，失焦提交
    const LowLagInput = memo(({ value, onCommit, maxLength = 30 }: { value: string; onCommit: (v: string) => void; maxLength?: number }) => {
        const [local, setLocal] = useState<string>(value || '')
        const [isFocused, setIsFocused] = useState(false)

        // 只有在非聚焦状态下才同步外部值，避免用户输入时被覆盖导致闪烁
        useEffect(() => {
            if (!isFocused) {
                setLocal(value || '')
            }
        }, [value, isFocused])

        return (
            <Input
                value={local}
                onChange={(e) => setLocal(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => {
                    setIsFocused(false)
                    onCommit(local)
                }}
                maxLength={maxLength}
                showCount
                autoFocus
            />
        )
    }, (prevProps, nextProps) => {
        // 精确的比较函数，避免不必要的重渲染
        return prevProps.value === nextProps.value &&
            prevProps.maxLength === nextProps.maxLength;
    })

    // 公共渲染逻辑
    const RenderCell = memo(({ item, selectedValue, value, setValueList, index, onOpenMultiple, listType }: { item: any, selectedValue: string, value: string, setValueList: any, index: number, onOpenMultiple?: () => void, listType: 'base' | 'spu' | 'sku' }) => {
        // 定义类型标签映射
        const typeLabels: Record<any, string> = {
            /* images: '结果图', */
            original: '原图',
            /* printing: '印花图', */
            title: '标题',
            /* cnTitle: '中文标题',
            enTitle: '英文标题',
            name: '名称', */
            upload: '上传图片',
        };
        // 定义显示文本生成函数
        const getDisplayText = (item: any) => {
            if (item.type === 'fixed') {
                return item.value; // 固定值直接显示内容
            }
            // 如果值是图片URL，显示"上传图片"
            if (item.value && item.value.startsWith('https://image')) {
                console.log(item.value);
                return '上传图片';
            }
            const [type, num] = item.value.split('-'); // 拆分动态值类型和序号（如"images-1"拆分为["images", "1"]）
            const label = typeLabels[type] || type; // 获取类型对应的显示标签
            if (type === 'upload' || type === 'dynamics') {
                return '上传图片'; // 上传图片类型显示固定文本
            }
            return type === 'images' && num ? `${label}第${num}张` : label; // 图片类型特殊处理序号
        };
        switch (selectedValue) {
            case 'text':
                return (
                    <CellTextArea
                        value={value}
                        id={`cell-${listType}-${index}`}
                        onCommit={(val) => {
                            setValueList((prev: any[]) => {
                                const next = [...prev]
                                if (next[index]) next[index] = { ...next[index], value: val }
                                return next
                            })
                        }}
                    />
                );
            case 'fixed':
                // 特殊处理产地省份字段：根据商品产地决定是否显示下拉选项
                if (item.name === '产地省份') {
                    // 获取商品产地值
                    const getProductOrigin = () => {
                        const rowValues = listType === 'base' ? (baseValues || []) : (listType === 'spu' ? (spuValues || []) : []);
                        const originFieldIndex = baseData?.findIndex((field: any) => field.name === '商品产地');
                        if (originFieldIndex !== -1 && rowValues[originFieldIndex]) {
                            return rowValues[originFieldIndex].value;
                        }
                        return '';
                    };
                    
                    const productOrigin = getProductOrigin();
                    const isMainlandChina = productOrigin === '中国大陆';
                    
                    // 如果是中国大陆，显示下拉选项
                    if (isMainlandChina && item.type === 'select' && item.options && item.options.length > 0) {
                        return (
                            <div style={{ width: '100%' }} id={`cell-${listType}-${index}`}>
                                <Select
                                    variant="borderless"
                                    style={{ width: '100%', textAlign: 'left' }}
                                    value={value}
                                    showSearch
                                    allowClear
                                    onChange={(val) => {
                                        setValueList((prev: any[]) => {
                                            const next = [...prev]
                                            if (next[index]) next[index] = { ...next[index], value: val }
                                            return next
                                        })
                                    }}
                                    options={item.options.split(",").map((opt: any) => ({ value: opt, label: opt }))}
                                />
                            </div>
                        );
                    } else {
                        // 如果不是中国大陆，显示文本输入框
                        return (
                            <CellTextArea
                                value={value}
                                id={`cell-${listType}-${index}`}
                                onCommit={(val) => {
                                    setValueList((prev: any[]) => {
                                        const next = [...prev]
                                        if (next[index]) next[index] = { ...next[index], value: val }
                                        return next
                                    })
                                }}
                            />
                        );
                    }
                }
                
                // 其他字段的正常处理逻辑
                return item.type === 'select' && item.options && item.options.length > 0 ? (
                    <div style={{ width: '100%' }} id={`cell-${listType}-${index}`}>
                        <Select
                            variant="borderless"
                            style={{ width: '100%', textAlign: 'left' }}
                            value={value}
                            showSearch
                            allowClear
                            onChange={(val) => {
                                setValueList((prev: any[]) => {
                                    const next = [...prev]
                                    if (next[index]) next[index] = { ...next[index], value: val }
                                    return next
                                })
                            }}
                            options={item.options.split(",").map((opt: any) => ({ value: opt, label: opt }))}
                        />
                    </div>
                ) : (
                    <CellTextArea
                        value={value}
                        id={`cell-${listType}-${index}`}
                        onCommit={(val) => {
                            setValueList((prev: any[]) => {
                                const next = [...prev]
                                if (next[index]) next[index] = { ...next[index], value: val }
                                return next
                            })
                        }}
                    />
                );
            case 'dynamics':
                return (
                    <div className='flex items-center w-full' style={{ width: '100%' }} id={`cell-${listType}-${index}`}>
                        <Select
                            variant="borderless"
                            style={{ flex: '1', marginLeft: '3%', textAlign: 'left' }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onChange={(val) => {
                                setValueList((prev: any[]) => {
                                    const next = [...prev]
                                    let newVal = val
                                    if (val === 'images') {
                                        const currentNum = value?.includes('images-') ? value.split('-')[1] || '1' : '1'
                                        newVal = `images-${currentNum}`
                                    }
                                    if (next[index]) next[index] = { ...next[index], value: newVal }
                                    return next
                                })
                            }}
                            value={value && value.startsWith('http') ? 'upload' : value?.split('-')[0]}
                            options={[
                                /* { value: 'images', label: '结果图' }, */
                                { value: 'original', label: '原图' },
                                /* { value: 'printing', label: '印花图' }, */
                                { value: 'title', label: '标题' },
                                /* { value: 'cnTitle', label: '中文标题' },
                                { value: 'enTitle', label: '英文标题' },
                                { value: 'name', label: '名称' }, */
                                { value: 'upload', label: '上传图片' },
                            ]}
                        />
                        {value?.split('-')[0] === 'images' && (
                                <Input
                                    style={{ width: '40px', textAlign: 'center' }}
                                    variant="borderless"
                                    value={value?.split('-')[1]}
                                    onChange={(e) => {
                                        // 允许用户正常输入，实时更新状态
                                        const inputValue = e.target.value;
                                        setValueList((prev: any[]) => {
                                            const next = [...prev];
                                            if (next[index]) next[index] = { ...next[index], value: `images-${inputValue}` };
                                            return next;
                                        });
                                    }}
                                    onBlur={(e) => {
                                        let num = Number(e.target.value);
                                        // 失焦时验证：非数字、<=0 时默认 1
                                        if (isNaN(num) || num <= 0) {
                                            num = 1;
                                        }
                                        num = Math.floor(num); // 确保是整数
                                        setValueList((prev: any[]) => {
                                            const next = [...prev];
                                            if (next[index]) next[index] = { ...next[index], value: `images-${num}` };
                                            return next;
                                        });
                                    }}
                                />
                        )}
                        {(value?.split('-')[0] === 'upload' || (value && value.startsWith('http'))) && (
                            <div className='w-[40%] flex items-center pr-4'>
                                {value && value.startsWith('http') ? (
                                    <div className='flex items-center gap-2'>
                                        <img 
                                            src={value} 
                                            alt="上传的图片" 
                                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                        />
                                        <Button
                                            size="small"
                                            danger
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setValueList((prev: any[]) => {
                                                    const next = [...prev];
                                                    if (next[index]) next[index] = { ...next[index], value: 'upload', type: 'dynamics' };
                                                    return next;
                                                })
                                            }}
                                        >删除</Button>
                                    </div>
                                ) : (
                                    <Upload
                                        accept="image/*"
                                        showUploadList={false}
                                        beforeUpload={(file) => {
                                            handleImageUpload(file, 'base', index);
                                            return false;
                                        }}
                                    >
                                        <Button 
                                            size="small" 
                                            loading={uploadingImages[`base-${index}`]}
                                            disabled={uploadingImages[`base-${index}`]}
                                        >
                                            {uploadingImages[`base-${index}`] ? '上传中' : '上传图片'}
                                        </Button>
                                    </Upload>
                                )}
                            </div>
                        )}
                    </div>
                );
            case 'multiple': {
                let items: any[] = []
                try {
                    items = value ? JSON.parse(value) : []
                    if (!Array.isArray(items)) items = []
                } catch {
                    items = []
                }
                return (
                    <div style={{ width: '100%' }} id={`cell-${listType}-${index}`}>
                        <p
                            className='text-[#999] cursor-pointer pl-3'
                            onClick={(e) => { e.stopPropagation(); onOpenMultiple && onOpenMultiple(); }}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            {items.length > 0 ? (
                                items.map((it, idx) => (
                                    <Tag key={idx}>{getDisplayText(it)}</Tag>
                                ))
                            ) : (
                                <span>点击添加值</span>
                            )}
                        </p>
                    </div>
                );
            }
            case 'spu':
                return <p className='text-[#999] cursor-default pl-3'>批次号 + 序号</p>;
            case 'sku':
                return <p className='text-[#999] cursor-default pl-3'>SKU + 序号</p>;
            default:
                return <Input.TextArea variant="borderless" autoSize={{ minRows: 2, maxRows: 2 }} />;
        }
    });
    // 公共渲染逻辑
    const RenderSkuCell = memo(({
        item,
        selectedValue,
        value,
        setValueList,
        index,
        rowIndex,
        onOpenMultiple,
        listType
    }: {
        item: any,
        selectedValue: string,
        value: string,
        setValueList: any,
        index: number,
        rowIndex: number,
        onOpenMultiple?: () => void,
        listType: 'base' | 'spu' | 'sku'
    }) => {
        // 定义类型标签映射
        const typeLabels: Record<any, string> = {
            /* images: '结果图', */
            original: '原图',
            /* printing: '印花图', */
            title: '原标题',
            /* cnTitle: '中文标题',
            enTitle: '英文标题',
            name: '名称', */
            upload: '上传图片',
        };
        // 定义显示文本生成函数
        const getDisplayText = (item: any) => {
            if (item.type === 'fixed') {
                return item.value; // 固定值直接显示内容
            }
            // 如果值是图片URL，显示"上传图片"
            if (item.value && item.value.startsWith('http')) {
                return '上传图片';
            }
            const [type, num] = item.value.split('-'); // 拆分动态值类型和序号（如"images-1"拆分为["images", "1"]）
            const label = typeLabels[type] || type; // 获取类型对应的显示标签
            if (type === 'upload' || type === 'dynamics') {
                return '上传图片'; // 上传图片类型显示固定文本
            }
            return type === 'images' && num ? `${label}第${num}张` : label; // 图片类型特殊处理序号
        };
        switch (selectedValue) {
            case 'text':
                return (
                    <CellTextArea
                        value={value}
                        id={`cell-${listType}-${rowIndex}-${index}`}
                        onCommit={(val) => {
                            setValueList((prev: any) =>
                                prev.map((row: any, rIdx: number) =>
                                    rIdx === rowIndex
                                        ? row.map((cell: any, cIdx: number) =>
                                            cIdx === index ? { ...cell, value: val } : cell
                                        )
                                        : row
                                )
                            );
                        }}
                    />
                );
            case 'fixed':
                return item.type === 'select' && item.options ? (
                    <div style={{ width: '100%' }} id={`cell-${listType}-${rowIndex}-${index}`}>
                        <Select
                            variant="borderless"
                            style={{ width: '100%', textAlign: 'left' }}
                            value={value}
                            showSearch
                            allowClear
                            onChange={(val) => {
                                setValueList((prev: any[]) => {
                                    const next = [...prev];
                                    if (!next[rowIndex]) next[rowIndex] = [];
                                    next[rowIndex][index] = { ...next[rowIndex][index], value: val };
                                    return next;
                                })
                            }}
                            options={item.options.split(",").map((opt: any) => ({ value: opt, label: opt }))}
                        />
                    </div>
                ) : (
                    <CellTextArea
                        value={value}
                        id={`cell-${listType}-${rowIndex}-${index}`}
                        onCommit={(val) => {
                            setValueList((prev: any) =>
                                prev.map((row: any, rIdx: number) =>
                                    rIdx === rowIndex
                                        ? row.map((cell: any, cIdx: number) =>
                                            cIdx === index ? { ...cell, value: val } : cell
                                        )
                                        : row
                                )
                            );
                        }}
                    />
                );
            case 'dynamics':
                return (
                    <div className='flex items-center w-full' style={{ width: '100%' }} id={`cell-${listType}-${rowIndex}-${index}`}>
                        <Select
                            variant="borderless"
                            style={{ flex: '1', marginLeft: '3%', textAlign: 'left' }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onChange={(val) => {
                                const currentNum = value?.includes('images-') ? value.split('-')[1] || '1' : '1';
                                setValueList((prev: any[]) => {
                                    const next = [...prev];
                                    if (!next[rowIndex]) next[rowIndex] = [];
                                    // 保留原有属性，只更新 value
                                    next[rowIndex][index] = {
                                        ...next[rowIndex][index],
                                        value: val === 'images' ? `images-${currentNum}` : val
                                    };
                                    return next;
                                })
                            }}
                            value={value && value.startsWith('http') ? 'upload' : value?.split('-')[0]}
                            options={[
                                /* { value: 'images', label: '结果图' }, */
                                { value: 'original', label: '原图' },
                                /* { value: 'printing', label: '印花图' }, */
                                { value: 'title', label: '标题' },
                                /* { value: 'cnTitle', label: '中文标题' },
                                { value: 'enTitle', label: '英文标题' },
                                { value: 'name', label: '名称' }, */
                                { value: 'upload', label: '上传图片' },
                            ]}
                        />
                        {value?.split('-')[0] === 'images' && (
                                <Input
                                    style={{ width: '40px', textAlign: 'center' }}
                                    variant="borderless"
                                    value={value?.split('-')[1]}
                                    onChange={(e) => {
                                        // 允许用户正常输入，实时更新状态
                                        const inputValue = e.target.value;
                                        setValueList((prev: any[]) => {
                                            const next = [...prev];
                                            if (!next[rowIndex]) next[rowIndex] = [];
                                            // 保留原有属性，只更新 value
                                            next[rowIndex][index] = {
                                                ...next[rowIndex][index],
                                                value: `images-${inputValue}`
                                            };
                                            return next;
                                        });
                                    }}
                                    onBlur={(e) => {
                                        let num = Number(e.target.value);
                                        // 失焦时验证：非数字、<=0 时默认 1
                                        if (isNaN(num) || num <= 0) {
                                            num = 1;
                                        }
                                        num = Math.floor(num); // 确保是整数
                                        setValueList((prev: any[]) => {
                                            const next = [...prev];
                                            if (!next[rowIndex]) next[rowIndex] = [];
                                            // 保留原有属性，只更新 value
                                            next[rowIndex][index] = {
                                                ...next[rowIndex][index],
                                                value: `images-${num}`
                                            };
                                            return next;
                                        });
                                    }}
                                />
                        )}
                        {(value?.split('-')[0] === 'upload' || (value && value.startsWith('http'))) && (
                            <div className='w-[40%] flex items-center pr-4'>
                                {value && value.startsWith('http') ? (
                                    <div className='flex items-center gap-2'>
                                        <img 
                                            src={value} 
                                            alt="上传的图片" 
                                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                        />
                                        <Button
                                            size="small"
                                            danger
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setValueList((prev: any[]) => {
                                                    const next = [...prev];
                                                    if (!next[rowIndex]) next[rowIndex] = [];
                                                    next[rowIndex][index] = { ...next[rowIndex][index], value: 'upload', type: 'dynamics' };
                                                    return next;
                                                })
                                            }}
                                        >删除</Button>
                                    </div>
                                ) : (
                                    <Upload
                                        accept="image/*"
                                        showUploadList={false}
                                        beforeUpload={(file) => {
                                            handleImageUpload(file, listType, index, rowIndex);
                                            return false;
                                        }}
                                    >
                                        <Button 
                                            size="small" 
                                            loading={uploadingImages[`${listType}-${rowIndex}-${index}`]}
                                            disabled={uploadingImages[`${listType}-${rowIndex}-${index}`]}
                                        >
                                            {uploadingImages[`${listType}-${rowIndex}-${index}`] ? '上传中' : '上传图片'}
                                        </Button>
                                    </Upload>
                                )}
                            </div>
                        )}
                    </div>
                );
            case 'multiple': {
                let items: any[] = []
                try {
                    items = value ? JSON.parse(value) : []
                    if (!Array.isArray(items)) items = []
                } catch {
                    items = []
                }
                return (
                    <div style={{ width: '100%' }} id={`cell-${listType}-${rowIndex}-${index}`}>
                        <p
                            className='text-[#999] cursor-pointer pl-3'
                            onClick={(e) => { e.stopPropagation(); onOpenMultiple && onOpenMultiple(); }}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            {items.length > 0 ? items.map((it, idx) => (
                                <Tag key={idx}>{getDisplayText(it)}</Tag>
                            )) : <span>点击添加值</span>}
                        </p>
                    </div>
                );
            }
            case 'spu':
                return <p className='text-[#999] cursor-default pl-3'>批次号 + 序号</p>;
            case 'sku':
                return <p className='text-[#999] cursor-default pl-3'>SKU + 序号</p>;
            default:
                return <Input.TextArea variant="borderless" autoSize={{ minRows: 2, maxRows: 2 }} />;
        }
    })

    // 添加处理函数
    const handleCopyRow = (rowIndex: number) => {

        setSkuValues((prev) => {
            const clonedRow = (prev[rowIndex] || []).map((cell: any) => {
                const cloned = { ...cell } as any;
                if ('id' in cloned) cloned.id = "";
                return cloned;
            });
            const next = [...prev];
            next.splice(rowIndex + 1, 0, clonedRow);
            return next;
        });
        setSkuRowIds((prev) => {
            const ids = [...prev];
            ids.splice(rowIndex + 1, 0, genRowId());
            return ids;
        });
    };

    const handleDeleteRow = (rowIndex: number) => {
        setSkuValues((prev) => {
            const next = prev.filter((_, index) => index !== rowIndex);
            return next;
        });
        setSkuRowIds((prev) => prev.filter((_, index) => index !== rowIndex));
    };


    // 稳定列生成器：列的 render 从 record 读取值，避免因 value 变化重建列
    const generateColumnsStable = (
        data: any[],
        setValueList: any,
        listType: 'base' | 'spu' | 'sku'
    ) => {
        // 根据 listType 获取对应的 valueList
        const getCurrentValueList = () => {
            switch (listType) {
                case 'base': return baseValues;
                case 'spu': return spuValues;
                case 'sku': return skuValues;
                default: return [];
            }
        };
        const currentValueList = getCurrentValueList();
        // 关键修复：分组时保留原始列下标 originalIndex，避免组内 index 重置导致跨组共享同一 valueList 索引
        const groupMap = data?.reduce((acc, cur, index) => {
            acc[cur.head] = acc[cur.head] || [];
            acc[cur.head].push({ ...cur, originalIndex: index });
            return acc;
        }, {} as Record<string, any[]>);

        const isNoGroupHeader = (h: any) => {
            const s = String(h ?? '').trim().toLowerCase();
            return s === '' || s === 'null' || s === 'undefined';
        };

        const columns: any[] = [];

        Object.entries(groupMap || {}).forEach(([head, groupData]) => {
            const childCols = (groupData as any[]).map((item) => {
                const originalColIndex = (item as any).originalIndex;
                return ({
                    title: (
                        <div className='flex items-center justify-between'>
                            <p>
                                {item.required == true && <span style={{ color: 'red' }}>*</span>}
                                {item.name}
                            </p>
                            <p>
                                <Tooltip title={item.remark || ''}>
                                    <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                                </Tooltip>
                                <Select
                                    variant="borderless"
                                    value={((currentValueList as any)?.[originalColIndex]?.type || selectedValues[item.name] || 'fixed')}
                                    onChange={(value: string) => {
                                        setSelectedValues(prev => ({ ...prev, [item.name]: value }));
                                        setValueList((prev: any[]) => {
                                            const next = [...prev];
                                            if (!next[originalColIndex]) next[originalColIndex] = {};
                                            next[originalColIndex].type = value;
                                            if (value === 'dynamics') next[originalColIndex].value = 'images-1';
                                            else if (value === 'multiple') next[originalColIndex].value = '[]';
                                            else if (value === 'spu') next[originalColIndex].value = 'spu';
                                            else if (value === 'sku') next[originalColIndex].value = 'sku';
                                            else if (value === 'upload') next[originalColIndex].value = 'upload';
                                            else next[originalColIndex].value = '';
                                            return next;
                                        });
                                    }}
                                    style={{ width: 100 }}
                                    options={COMMON_SELECT_OPTIONS}
                                />
                            </p>
                        </div>
                    ),
                    dataIndex: `column-${originalColIndex}`,
                    key: `column-${originalColIndex}`,
                    width: 306,
                    render: (_: any, record: any) => {
                        const cell = record?.[`column-${originalColIndex}`] || {};
                        const selectedValue = cell.type || selectedValues[item.name] || 'fixed';
                        const value = cell.value || '';
                        return (
                            <RenderCell
                                item={item}
                                selectedValue={selectedValue}
                                value={value}
                                setValueList={setValueList}
                                index={originalColIndex}
                                onOpenMultiple={() => openAddMultiple(listType, originalColIndex, value)}
                                listType={listType}
                            />
                        );
                    }
                });
            });

            if (isNoGroupHeader(head)) {
                columns.push(...childCols);
            } else {
                columns.push({
                    title: head,
                    align: 'center' as const,
                    children: childCols
                });
            }
        });

        return columns;
    };

    const generateColumns2Stable = (
        data: any[],
        setValueList: any,
        listType: 'base' | 'spu' | 'sku'
    ) => {
        // 获取当前的 valueList 来反映实际状态
        const getCurrentValueList = () => {
            switch (listType) {
                case 'sku': return skuValues;
                default: return [];
            }
        };
        const currentValueList = getCurrentValueList();

        const groupMap = data?.reduce((acc, cur, index) => {
            acc[cur.head] = acc[cur.head] || [];
            acc[cur.head].push({ ...cur, originalIndex: index });
            return acc;
        }, {} as Record<string, any[]>);

        const isNoGroupHeader = (h: any) => {
            const s = String(h ?? '').trim().toLowerCase();
            return s === '' || s === 'null' || s === 'undefined';
        };

        const columns: any[] = [];

        Object.entries(groupMap || {}).forEach(([head, groupData]) => {
            const childCols = (groupData as any[]).map((item) => {
                const originalColIndex = (item as any).originalIndex;
                return {
                    title: (
                        <div className='flex items-center justify-between'>
                            <p>
                                {item.required == true && <span style={{ color: 'red' }}>*</span>}
                                {item.name}
                            </p>
                            <p>
                                <Tooltip title={item.remark || ''}>
                                    <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                                </Tooltip>
                                <Select
                                    variant="borderless"
                                    value={((currentValueList as any)?.[0]?.[originalColIndex]?.type || selectedValues[item.name] || 'fixed')}
                                    onChange={(value: string) => {
                                        setSelectedValues(prev => ({ ...prev, [item.name]: value }));
                                        setValueList((prev: any[][]) => {
                                            const next = Array.isArray(prev) ? prev.map(row => Array.isArray(row) ? [...row] : []) : [];
                                            next.forEach(row => {
                                                if (!row[originalColIndex]) row[originalColIndex] = {} as any;
                                                row[originalColIndex].type = value;
                                                if (value === 'dynamics') row[originalColIndex].value = 'images-1';
                                                else if (value === 'multiple') row[originalColIndex].value = '[]';
                                                else if (value === 'spu') row[originalColIndex].value = 'spu';
                                                else if (value === 'sku') row[originalColIndex].value = 'sku';
                                                else if (value === 'upload') row[originalColIndex].value = 'upload';
                                                else row[originalColIndex].value = '';
                                            });
                                            return next;
                                        });
                                    }}
                                    style={{ width: 100 }}
                                    options={COMMON_SELECT_OPTIONS}
                                />
                            </p>
                        </div>
                    ),
                    dataIndex: `column-${originalColIndex}`,
                    key: `column-${originalColIndex}`,
                    width: 306,
                    render: (_: any, record: any, rowIndex: number) => {
                        const cell = record?.[`column-${originalColIndex}`] || {};
                        const selectedValue = cell.type || selectedValues[item.name] || 'fixed';
                        const value = cell.value || '';
                        return (
                            <RenderSkuCell
                                item={item}
                                selectedValue={selectedValue}
                                value={value}
                                setValueList={setValueList}
                                index={originalColIndex}
                                rowIndex={rowIndex}
                                onOpenMultiple={() => openAddMultiple(listType, originalColIndex, value, rowIndex)}
                                listType={listType}
                            />
                        );
                    }
                };
            });

            if (isNoGroupHeader(head)) {
                columns.push(...childCols);
            } else {
                columns.push({
                    title: head,
                    align: 'center' as const,
                    children: childCols
                });
            }
        });

        const operationColumn = {
            title: '操作',
            key: 'action',
            className: 'th-position',
            width: 140,
            render: (text: string, record: any, rowIndex: number) => (
                <div className='flex  items-center'>
                    <Button
                        type="link"
                        style={{ color: '#32649f' }}
                        onClick={() => handleCopyRow(rowIndex)}
                    >
                        复制
                    </Button>
                    <Button
                        type="link"
                        style={{ color: '#32649f' }}
                        onClick={() => handleDeleteRow(rowIndex)}
                    >
                        删除
                    </Button>
                </div>
            )
        };

        return [...columns, operationColumn];
    };

    // 从 values 派生表格 dataSource（行内记录包含各列的 {type,value}）
    const baseTableDataSource = useMemo(() => {
        const row: any = {};
        (baseValues || []).forEach((v: any, i: number) => {
            row[`column-${i}`] = { type: v?.type, value: v?.value };
        })
        return [row];
    }, [baseValues]);

    const spuTableDataSource = useMemo(() => {
        const row: any = {};
        (spuValues || []).forEach((v: any, i: number) => {
            row[`column-${i}`] = { type: v?.type, value: v?.value };
        })
        return [row];
    }, [spuValues]);

    const skuTableDataSource = useMemo(() => {
        const ds = (skuValues || []).map((rowArr: any[], rIdx: number) => {
            const row: any = { key: skuRowIds[rIdx] || String(rIdx) };
            (rowArr || []).forEach((cell: any, cIdx: number) => {
                row[`column-${cIdx}`] = { type: cell?.type, value: cell?.value };
            });
            return row;
        });
        return ds;
    }, [skuValues, skuRowIds]);

    // 基础/SPU/SKU 列：使用稳定版生成器并 memo 化

    const baseColumns = useMemo(() => generateColumnsStable(baseData, setBaseValues, 'base'), [baseData, selectedValues, baseValues]);
    const spuColumns = useMemo(() => generateColumnsStable(spuData, setSpuValues, 'spu'), [spuData, selectedValues]);
    const skuColumns = useMemo(() => generateColumns2Stable(skuData, setSkuValues, 'sku'), [skuData, selectedValues]);

    // 添加 SKU 行
    const handleAddSkuRow = () => {
        setSkuValues((prev: any) => {
            const cloned = (initSkuValues || []).map((cell: any) => {
                const c = { ...cell } as any;
                if ('id' in c) c.id = null;
                return c;
            });
            return [...prev, cloned];
        });

        setSkuRowIds(prev => [...prev, genRowId()])
    };

    const [multipleValueList, setMultipleValueList] = useState<any[]>([])
    
    // 图片上传相关状态
    const [uploadingImages, setUploadingImages] = useState<Record<string, boolean>>({})
    const [uploadedImageUrls, setUploadedImageUrls] = useState<Record<string, string>>({})

    const addItem = (type: string) => {
        setMultipleValueList(prev => [
            ...prev,
            {
                type,
                // 根据类型设置默认值
                value: type === 'fixed' ? '' : type === 'upload' ? 'upload' : 'images-1',
                noSplit: false,
            }
        ]);
    }

    // 删除项目的函数实现
    const delItem = (index: number) => {
        setMultipleValueList(prev => {
            const newList = [...prev];
            newList.splice(index, 1);
            return newList;
        });
    };

    // 处理图片上传
    const handleImageUpload = async (file: File, listType: string, index: number, rowIndex?: number) => {
        const key = `${listType}-${rowIndex !== undefined ? rowIndex : 0}-${index}`;
        
        setUploadingImages(prev => ({ ...prev, [key]: true }));
        
        try {
            const res = await uploadFileOssApi(file);
            if (res.url) {
                setUploadedImageUrls(prev => ({ ...prev, [key]: res.url }));
                
                // 更新对应的值
                if (listType === 'base') {
                    setBaseValues(prev => {
                        const next = [...prev];
                        if (next[index]) next[index] = { 
                            ...next[index], 
                            value: res.url,
                            type: 'dynamics' // 提交时类型为动态值
                        };
                        return next;
                    });
                } else if (listType === 'spu') {
                    setSpuValues(prev => {
                        const next = [...prev];
                        if (next[index]) next[index] = { 
                            ...next[index], 
                            value: res.url,
                            type: 'dynamics' // 提交时类型为动态值
                        };
                        return next;
                    });
                } else if (listType === 'sku') {
                    setSkuValues(prev => {
                        const next = [...prev];
                        if (!next[rowIndex!]) next[rowIndex!] = [];
                        if (next[rowIndex!][index]) {
                            next[rowIndex!][index] = { 
                                ...next[rowIndex!][index], 
                                value: res.url,
                                type: 'dynamics' // 提交时类型为动态值
                            };
                        }
                        return next;
                    });
                } else if (listType === 'multiple') {
                    setMultipleValueList(prev => {
                        const next = [...prev];
                        if (next[index]) next[index] = { 
                            ...next[index], 
                            value: res.url,
                            type: 'dynamics' // 提交时类型为动态值
                        };
                        return next;
                    });
                }
                
                messageApi.success('图片上传成功');
            }
        } catch (error) {
            messageApi.error('图片上传失败,违规图片');
        } finally {
            setUploadingImages(prev => ({ ...prev, [key]: false }));
        }
    };

    const [addProductTemplate, setAddProductTemplate] = useState(false)


    // 新建/编辑分组
    const handleGroupAction = () => {
        const name = groupName.trim();
        if (!name) return messageApi.warning('请输入分组名称');

        if (editingGroupId) {
            editGoodsTemplateGroup({ id: editingGroupId, groupName: name }).then(res => {
                messageApi.success('分组重命名成功')
                fetchGroupListData('') // 添加成功后刷新分组列表
            }).catch(err => {
                messageApi.error(`分组重命名失败:${err?.msg || err?.data?.msg || ''}`)
            })
        } else {
            addGoodsTemplateGroup({ groupName: name }).then(res => {
                messageApi.success('分组创建成功')
                fetchGroupListData('') // 添加成功后刷新分组列表
            }).catch(err => {
                messageApi.error(`分组创建失败:${err?.msg || err?.data?.msg || ''}`)
            })
        }

        setModalVisible(false);
        setGroupName('');
        setEditingGroupId('');
    };

    const handleDeleteGroup = (group: any) => {
        modal.confirm({
            title: '确认删除分组',
            content: `确定要删除分组 "${group?.groupName}" 吗？`,
            centered: true,
            onOk: () => {
                delGoodsTemplateGroup({ id: group?.id }).then(res => {
                    messageApi.success('分组删除成功')
                    fetchGroupListData('') // 添加成功后刷新分组列表
                    if (group?.id == activeTab?.id) {
                        setActiveTab(groups[0])
                    }
                }).catch(err => {
                    messageApi.error(`分组删除失败:${err?.msg || err?.data?.msg || ''}`)
                })
            }
        });
    };

    const tabItems = groups.map((group, index) => ({
        label: (
            <div className='flex items-center group' >
                {group?.groupName}
                <div onClick={(e) => e.stopPropagation()}>
                    <Dropdown
                        menu={{
                            items: [
                                {
                                    key: 'rename', label: '重命名', onClick: () => {
                                        setGroupName(group?.groupName);
                                        setEditingGroupId(group?.id);
                                        setModalVisible(true);
                                    }
                                },
                                { key: 'delete', label: '删除', onClick: () => handleDeleteGroup(group) }
                            ]
                        }}
                        placement="bottomLeft"
                    >
                        {group?.id != 0 && (
                            <div className="relative ml-[2px] h-[20px] w-[10px] " >
                                <MoreOutlined className='absolute top-[3px] left-0 opacity-0 group-hover:opacity-100 transition-opacity' />
                            </div>
                        )}
                    </Dropdown>
                </div>
            </div>
        ),
        key: group?.id?.toString()
    }));

    const [templateName, setTemplateName] = useState('')
    const [selectedGroup, setSelectedGroup] = useState<any>(null); // 选择的分组
    const [selectedModel, setSelectedModel] = useState<any>(null); // 选择的模板

    const [currentItem, setCurrentItem] = useState<any>({})


    // 移动导入商品模板
    const moveTemplate = (item: any) => {
        if (groups.length <= 1) return messageApi.warning('请先创建分组')
        setMoveTemplateModal(true);
        setSelectedGroup({})
        setCurrentItem(item)
    };

    const [moveTemplateModal, setMoveTemplateModal] = useState(false); // 移动到分组弹窗
    //修改商品模板名称
    const [editingTemplateName, setEditingTemplateName] = useState(false)

    const handleTemplateName = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.slice(0, 30); // 限制30字符
        setTemplateName(value);
    };

    const handleNameBlur = () => {
        setEditingTemplateName(false);
    };

    const [submitLoading, setSubmitLoading] = useState(false)

    //修改商品模板名称   end
    return (
        <div className="h-full w-full p-[20px]">
            {contextHolder} {/* 这里确保 Modal 挂载 */}
            {messageContextHolder} {/* 这里确保 Message 挂载 */}
            <div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
                <Button type="primary" onClick={() => {
                    setTemplateName('')
                    setSelectedGroup(null)
                    setFileListCsv(null)
                    setAddProductTemplate(true);
                }}>
                    新增商品模板
                </Button>
                <div className="flex items-center gap-2 ]">
                    <Input
                        style={{ width: '200px' }}
                        value={formTemplateName}
                        onChange={(e) => setFormTemplateName(e.target.value)}
                        placeholder="输入模板名称"
                    />
                    <Select
                        placeholder="请选择要查看的账号"
                        style={{ width: 200 }}
                        value={userId || undefined}
                        onChange={(value) => setUserId(value)}
                        allowClear
                        showSearch
                    >
                        {selectedTeam && selectedTeam.map((item: any) => (
                            <Option key={item.phonenumber} value={item.userId}>
                                {item.nickname===""?item.phonenumber:item.nickname}
                            </Option>
                        ))}
                    </Select>
                    <Button
                        type="primary"
                        onClick={() => {
                            setPagination(prev => ({ ...prev, current: 1 }));
                            fetchImportTemplateList(1, 10, activeTab?.id,userId)
                        }}
                        loading={false}
                    >
                        查询
                    </Button>
                </div>
            </div >
            {/* 新建分组/重命名分组弹窗  start */}
            <Modal
                title={editingGroupId ? "重命名分组" : "新建分组"}
                open={modalVisible}
                onOk={handleGroupAction}
                onCancel={() => setModalVisible(false)}
                centered
            >
                <Input
                    placeholder="请输入分组名称"
                    value={groupName}
                    onChange={e => setGroupName(e.target.value)}
                />
            </Modal>
            {/* 新建分组/重命名分组弹窗  end */}
            {/* 移动到分组弹窗  start */}
            <Modal
                title='移动到分组'
                open={moveTemplateModal}
                onOk={() => { }}
                onCancel={() => {
                    setMoveTemplateModal(false);
                }}
                centered
                footer={
                    <div className='flex justify-between items-center'>
                        <Button
                            type="link"
                            onClick={() => {
                                setModalVisible(true)
                                setGroupName('')
                                setEditingGroupId('')
                            }}
                            style={{ color: '#F06A34', height: '32px', padding: '0px', gap: '2px' }}
                        >
                            <span className='text-[22px] mb-[5px]'>+</span>创建分组
                        </Button>
                        <div>
                            <Button style={{ marginRight: 8 }}
                                onClick={() => { setMoveTemplateModal(false) }}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (currentItem?.groupId && selectedGroup?.id == currentItem?.groupId) return messageApi.warning('所选项目已在此分组里')
                                    if (!selectedGroup?.id) return messageApi.warning('请选择分组')
                                    moveGoodsTemplate({ id: currentItem?.id, tExcelCustomGroupId: selectedGroup?.id }).then(res => {
                                        setMoveTemplateModal(false)
                                        messageApi.success('移动分组成功')
                                    }).catch(err => {
                                        messageApi.error(`移动分组失败:${err?.msg || err?.data?.msg || ''}`)
                                    })
                                }}
                            >
                                确定
                            </Button>
                        </div>
                    </div>
                }
            >
                <div className='border border-[#D8D8D8] rounded-lg mt-4 p-[2px] h-[220px] relative overflow-hidden'>
                    <div className={`h-[220px]  overflow-y-scroll scrollbar-container scrollbar-hide ${currentItem?.groupId && selectedGroup?.id == currentItem?.groupId ? 'pb-10' : 'pb-2'}`}>
                        <p className='font-bold ml-4 mt-3'>全部</p>
                        {groups.filter(group => group.id != 0).map((item: any, index) => (
                            <div key={index}
                                className={`pl-10 h-[30px] rounded-md flex items-center cursor-pointer ${selectedGroup?.id === item?.id ? 'bg-gray-100 text-[#F06A34]' : 'hover:text-[#F06A34]'}`}
                                onClick={() => setSelectedGroup(item)}
                            >
                                <FolderOpenOutlined style={{ marginRight: 6 }} />{item?.groupName}
                            </div>
                        ))}
                    </div>
                    {currentItem?.groupId && selectedGroup?.id == currentItem?.groupId && <div className='text-center text-[12px] absolute h-[30px] pt-1 rounded-md w-[456px] bg-[#F06A34] bottom-1 text-[#fff]'>所选项目已在此分组里</div>}

                </div>
            </Modal>
            {/* 移动到分组弹窗  end */}
            {/* 新增商品模板弹窗 start */}
            <Modal
                title="新建商品模板"
                open={addProductTemplate}
                onOk={nextStep}
                onCancel={() => setAddProductTemplate(false)}
                width={540}
                okText="下一步"
                centered
                confirmLoading={nextLoading}
            >
                <div className='flex items-center mb-3'>模板名称  <Input style={{ flex: '1', marginLeft: '16px' }} value={templateName} onChange={(e) => setTemplateName(e.target.value)} placeholder="请输入模板名称" /></div>
                <div className='flex items-center mb-3'>模板分组
                    <Select
                        options={groups.filter(group => group.id != 0).map(group => ({
                            label: group?.groupName,
                            value: group?.id
                        }))}
                        placeholder="请选择分组"
                        allowClear
                        value={selectedGroup}
                        onChange={(value) => {
                            setSelectedGroup(value)
                        }}
                        style={{ flex: '1', marginLeft: '16px' }}
                    />
                </div>
                <div className='flex items-center mb-3'>模板类型
                    <Select
                        options={templateType.map(template => ({
                            label: template?.templateName,
                            value: template?.id
                        }))}
                        placeholder="请选择模板类型"
                        allowClear
                        value={selectedModel}
                        onChange={(value) => {
                            setSelectedModel(value)
                        }}
                        style={{ flex: '1', marginLeft: '16px' }}
                    />
                </div>
                <p className='mb-2'>模板文件</p>
                <Upload.Dragger
                    accept=".csv,.xls,.xlsx"
                    beforeUpload={(file) => {
                        setFileListCsv(file)
                        return false
                    }} // 阻止自动上传
                    customRequest={() => { }}
                    showUploadList={false}
                    maxCount={1}
                >
                    <div className='pt-2 pb-2'>
                        <Button icon={<UploadOutlined />} style={{ padding: '10px' }} >
                            <p style={{ maxWidth: '400px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                上传execl (CSV/XLS/XLSX)
                                {"   "}{fileListCsv?.name}
                            </p>
                        </Button>
                    </div>
                </Upload.Dragger>
            </Modal>
            {/* 新增商品模板弹窗 end */}
            {/* 添加值弹窗 start */}
            <Modal
                open={addValueModal}
                zIndex={2200}
                onOk={() => {
                    const str = JSON.stringify(multipleValueList.filter(item => item.value || item.value.trim()))
                    if (currentMultipleContext) {
                        const { listType, index, colIndex } = currentMultipleContext
                        if (listType === 'base') {
                            setBaseValues(prev => {
                                const next = [...prev]
                                if (next[colIndex]) next[colIndex] = { ...next[colIndex], value: str }
                                return next
                            })
                        } else if (listType === 'spu') {
                            setSpuValues(prev => {
                                const next = [...prev]
                                if (next[colIndex]) next[colIndex] = { ...next[colIndex], value: str }
                                return next
                            })
                        } else if (listType === 'sku') {
                            setSkuValues(prev => {
                                const next = [...prev]
                                if (next[index]) {
                                    next[index] = [...next[index]];
                                    if (next[index][colIndex]) {
                                        next[index][colIndex] = {
                                            ...next[index][colIndex],
                                            value: str
                                        };
                                    }
                                }
                                return next
                            })
                        }
                    }
                    setAddValueModal(false)
                    setCurrentMultipleContext(null)
                    setMultipleValueList([])
                }}

                onCancel={() => setAddValueModal(false)}
                width={560}
                centered
            >
                <div className='flex flex-col pt-[50px] relative' id='addItem-modal'>
                    {multipleValueList.length > 0 ?
                        (
                            multipleValueList.map((item: any, index: number) => <div className='flex items-center gap-3 mb-2'>
                                <Select
                                    style={{ width: '84px', height: '46px' }}
                                    onMouseDown={(e) => e.stopPropagation()}
                                    onChange={(value) => {
                                        setMultipleValueList(prevList =>
                                            prevList.map((listItem, idx) =>
                                                idx === index
                                                    ? { ...listItem, type: value, value: value === 'fixed' ? '' : 'images-1' }
                                                    : listItem
                                            )
                                        );
                                    }}
                                    value={item?.type}
                                    options={[
                                        { value: 'fixed', label: '固定值' },
                                        { value: 'dynamics', label: '动态值' }, // 修正标签为动态值
                                    ]}
                                />
                                {item?.type == 'fixed' ?
                                    <div className='border-[2px] border-normal hover:border-[#f06A34] rounded-lg flex-1 h-[46px]'>
                                        <Input.TextArea variant="borderless" value={item.value} autoSize={{ minRows: 2, maxRows: 2 }}
                                            onChange={(e) => {
                                                setMultipleValueList(prevList =>
                                                    prevList.map(listItem =>
                                                        listItem === item ? { ...listItem, value: e.target.value } : listItem
                                                    )
                                                );
                                            }}
                                        />
                                    </div> :
                                    <div className='flex items-center multiple_modal border-[2px] border-normal hover:border-[#f06A34] rounded-lg flex-1 h-[46px]'>
                                        <Select
                                            variant="borderless"
                                            style={{ flex: '1', marginLeft: '3%', textAlign: 'left' }}
                                            // getPopupContainer={(trigger) => (trigger?.parentElement as HTMLElement) || document.body}
                                            onMouseDown={(e) => e.stopPropagation()}
                                            onChange={(value) => {
                                                setMultipleValueList(prevList =>
                                                    prevList.map(listItem =>
                                                        listItem === item ? {
                                                            ...listItem,
                                                            value: value === 'images' ? `${value}-${listItem.value ? listItem.value.split('-')[1] || '1' : '1'}` : value
                                                        } : listItem
                                                    )
                                                );
                                            }}
                                            value={item.value && item.value.startsWith('http') ? 'upload' : item?.value.split('-')[0]}
                                            options={[
                                                /* { value: 'images', label: '结果图' }, */
                                                { value: 'original', label: '原图' },
                                                /* { value: 'printing', label: '印花图' }, */
                                                { value: 'title', label: '标题' },
                                                /* { value: 'cnTitle', label: '中文标题' },
                                                { value: 'enTitle', label: '英文标题' },
                                                { value: 'name', label: '名称' }, */
                                                { value: 'upload', label: '上传图片' },
                                            ]}
                                        />
                                        {item?.value.split('-')[0] == 'images' && 
                                            <Input
                                                style={{ width: '40px', textAlign: 'center' }}
                                                variant="borderless"
                                                value={item?.value.split('-')[1]}
                                                onChange={(e) => {
                                                    if (item && typeof item.value === 'string') {
                                                        const [prefix] = item.value.split('-');
                                                        const newValue = e.target.value; // 允许正常输入
                                                        setMultipleValueList((prevList) =>
                                                            prevList.map((listItem) =>
                                                                listItem === item ? { ...listItem, value: `${prefix}-${newValue}` } : listItem
                                                            )
                                                        );
                                                    }
                                                }}
                                                onBlur={(e) => {
                                                    if (item && typeof item.value === 'string') {
                                                        const [prefix] = item.value.split('-');
                                                        let num = Number(e.target.value);
                                                        // 失焦时验证：非数字、<=0 时默认 1
                                                        if (isNaN(num) || num <= 0) num = 1;
                                                        num = Math.floor(num); // 确保是整数
                                                        const newValue = `${prefix}-${num}`;
                                                        setMultipleValueList((prevList) =>
                                                            prevList.map((listItem) =>
                                                                listItem === item ? { ...listItem, value: newValue } : listItem
                                                            )
                                                        );
                                                    }
                                                }}
                                            />}
                                        {(item?.value.split('-')[0] == 'upload' || (item.value && item.value.startsWith('http'))) && (
                                            <div className='w-[40%] flex items-center pr-4'>
                                                {item.value && item.value.startsWith('http') ? (
                                                    <div className='flex items-center gap-2'>
                                                        <img 
                                                            src={item.value} 
                                                            alt="上传的图片" 
                                                            style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                                        />
                                                        <Button
                                                            size="small"
                                                            danger
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                setMultipleValueList(prev => {
                                                                    const next = [...prev];
                                                                    if (next[index]) next[index] = { ...next[index], value: 'upload', type: 'dynamics' };
                                                                    return next;
                                                                });
                                                            }}
                                                        >删除</Button>
                                                    </div>
                                                ) : (
                                                    <Upload
                                                        accept="image/*"
                                                        showUploadList={false}
                                                        beforeUpload={(file) => {
                                                            handleImageUpload(file, 'multiple', index);
                                                            return false;
                                                        }}
                                                    >
                                                        <Button 
                                                            size="small" 
                                                            loading={uploadingImages[`multiple-${index}`]}
                                                            disabled={uploadingImages[`multiple-${index}`]}
                                                        >
                                                            {uploadingImages[`multiple-${index}`] ? '上传中' : '上传图片'}
                                                        </Button>
                                                    </Upload>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                }
                                <Checkbox checked={!item?.noSplit} onChange={(e) => {
                                    setMultipleValueList(prevList =>
                                        prevList.map(listItem =>
                                            listItem === item ? { ...listItem, noSplit: !e.target.checked } : listItem
                                        )
                                    );
                                }}>分隔符</Checkbox>
                                <Button onClick={() => delItem(index)} style={{ padding: 0, width: '32px' }}><MinusOutlined /></Button>
                            </div>)
                        ) : <Empty description="暂无内容，请添加" />
                    }
                    <Dropdown
                        menu={{
                            items: [
                                { key: 'fixed', label: '固定值', onClick: () => addItem('fixed') },
                                { key: 'dynamics', label: '动态值', onClick: () => addItem('dynamics') },
                                { key: 'upload', label: '上传图片', onClick: () => addItem('upload') },
                            ]
                        }}
                        placement="bottomLeft"
                        trigger={['hover']}
                    >
                        <Button type="primary"
                            onClick={(e) => e.stopPropagation()}
                            style={{ margin: '16px auto', ...(multipleValueList.length > 0 ? { position: 'absolute', top: '-18px' } : {}) }}
                        >添加内容</Button>
                    </Dropdown>

                </div>
            </Modal>
            {/* 添加值弹窗 end */}
            {/* 新增/编辑模板弹窗 start */}
            <Modal
                closeIcon={null}
                open={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                width={'calc(100vw - 40px)'}
                height={'calc(100vh - 80px)'}
                centered
                footer={null}
            >
                <div id='import-template-modal'>
                    <div className='flex items-center justify-between border-b-[1px] border-normal pb-3'>
                        <div className="flex items-center">
                            {editingTemplateName ? (
                                <LowLagInput
                                    value={templateName}
                                    onCommit={(val) => { setTemplateName(val); handleNameBlur(); }}
                                    maxLength={30}
                                />
                            ) : (
                                <>
                                    <span className='text-ellipsis overflow-hidden'>{templateName}</span>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() => setEditingTemplateName(true)}
                                        className="ml-1 mr-1" />
                                </>


                            )}
                        </div>
                        <div className='flex items-center gap-3'>
                            <Button onClick={handleCancel}>
                                取消
                            </Button>
                            <Button type="primary" loading={submitLoading} onClick={() => {
                                
                                // 生成提交所需的结构（携带规范化 value）- 支持表格结构
                                const buildPayload = (data: any[], values: any, listType: 'base' | 'spu' | 'sku') => {
                                    // 如果是表格结构（二维数组）
                                    if(values==undefined) return []
                                    if (Array.isArray(values[0])) {
                                        return values.map((row: any, rowIndex: number) =>
                                            row.map((itemVal: any, colIndex: number) => {
                                                const type = itemVal?.type || 'fixed'
                                                let value = itemVal?.value ?? ''
                                                // 规范化：SPU/SKU 强制 value 为 'spu'/'sku'
                                                if (type === 'spu') value = 'spu'
                                                if (type === 'sku') value = 'sku'
                                                // 多个值：若为数组，转成字符串
                                                if (type === 'multiple' && Array.isArray(value)) {
                                                    value = JSON.stringify(value)
                                                }
                                                return {
                                                    row: rowIndex + 1,
                                                    column: colIndex + 1,
                                                    type,
                                                    value,
                                                    name: data[colIndex]?.name,
                                                    noSplit: itemVal?.noSplit ?? false,
                                                    required: data[colIndex]?.required == true
                                                }
                                            })
                                        ).flat() // 扁平化为一维数组便于统一处理
                                    } else {
                                        // 处理一维数组的情况（向后兼容）
                                        return data.map((col, idx) => {
                                            const itemVal = values[idx] || {}
                                            const type = itemVal.type || 'fixed'
                                            let value = itemVal.value ?? ''
                                            if (type === 'spu') value = 'spu'
                                            if (type === 'sku') value = 'sku'
                                            if (type === 'multiple' && Array.isArray(value)) {
                                                value = JSON.stringify(value)
                                            }
                                            return {
                                                row: 1,
                                                column: idx + 1,
                                                type,
                                                value,
                                                name: col.name,
                                                noSplit: itemVal.noSplit ?? false,
                                                required: col.required == true
                                            }
                                        })
                                    }
                                }

                                // 修改校验函数支持表格结构
                                const checkRequired = (payload: any[], setErr: (arr: boolean[][]) => void, tableId: string, isTable: boolean = false) => {
                                    if (!isTable) {
                                        // 处理一维数组
                                        const errs = payload.map(p => (p.required && !p.value))
                                        const firstIdx = errs.findIndex(Boolean)
                                        const newErrs = payload.map(() => false)
                                        if (firstIdx > -1) newErrs[firstIdx] = true
                                        setErr([newErrs])

                                        if (firstIdx > -1) {
                                            const name = payload[firstIdx].name
                                            messageApi.destroy()
                                            messageApi.error(`请填写${tableId == 'base-table' ? '基本' : tableId == 'spu-table' ? 'SPU' : 'SKU'}信息-${name}`)
                                            const cell = document.querySelector(`#cell-${tableId.replace('-table', '')}-${firstIdx}`) as HTMLElement | null
                                            if (cell) {
                                                cell.scrollIntoView({ behavior: 'smooth', block: 'center' })
                                                cell.focus()
                                            }
                                            return false
                                        }
                                        return true
                                    }

                                    // 处理表格结构（二维数组）
                                    const rows = [...new Set(payload.map(item => item.row))]
                                    const errors: boolean[][] = []
                                    let firstError: any = null

                                    rows.forEach(rowIndex => {
                                        const rowItems = payload.filter(item => item.row === rowIndex)
                                        const rowErrors = rowItems.map(item => (item.required && !item.value))
                                        errors.push(rowErrors)

                                        if (!firstError) {
                                            const errorIndex = rowErrors.findIndex(Boolean)
                                            if (errorIndex > -1) {
                                                firstError = {
                                                    row: rowIndex,
                                                    col: errorIndex,
                                                    name: rowItems[errorIndex].name
                                                }
                                            }
                                        }
                                    })

                                    setErr(errors)

                                    if (firstError) {
                                        messageApi.destroy()
                                        messageApi.error(`请填写${tableId == 'base-table' ? '基本' : tableId == 'spu-table' ? 'SPU' : 'SKU'}信息 - 第${firstError.row}行 ${firstError.name}`)

                                        // 滚动到具体单元格
                                        const cell = document.querySelector(`#cell-${tableId.replace('-table', '')}-${firstError.row - 1}-${firstError.col}`) as HTMLElement | null
                                        if (cell) {
                                            cell.scrollIntoView({ behavior: 'smooth', block: 'center' })
                                            cell.focus()
                                        } else {
                                            const table = document.querySelector(`#${tableId}`)
                                            table?.scrollIntoView({ behavior: 'smooth', block: 'center' })
                                        }
                                        return false
                                    }

                                    return true
                                }

                                // 使用方式
                                const basePayload = buildPayload(baseData, baseValues, 'base')
                                const spuPayload = buildPayload(spuData, spuValues, 'spu')
                                const skuPayload = buildPayload(skuData, skuValues, 'sku')
                                

                                // 判断是否为表格结构(二维)
                                const isBaseTable = baseValues==undefined?false:Array.isArray(baseValues[0])
                                const isSpuTable = spuValues==undefined?false:Array.isArray(spuValues[0])
                                const isSkuTable = skuValues==undefined?false:Array.isArray(skuValues[0])
                                

                                const baseOk = checkRequired(basePayload, setBaseErrors, 'base-table', isBaseTable)
                                if (!baseOk) return
                                const spuOk = checkRequired(spuPayload, setSpuErrors, 'spu-table', isSpuTable)
                                if (!spuOk) return
                                const skuOk = checkRequired(skuPayload, setSkuErrors, 'sku-table', isSkuTable)
                                if (!skuOk) return
                                console.log(baseData, "baseData")
                                console.log(baseValues, "baseValues")
                                console.log(spuData, "spuData")
                                console.log(spuValues, "spuValues")
                                console.log(skuData, "skuData")
                                console.log(skuValues, "skuValues")
                                console.log(overallData, "overallData")
                                console.log(selectedGroup, "selectedGroup")
                                console.log(templateName, "templateName")
                                const sku = skuValues
                                const result = sku.map((item, index) => {
                                    return item.map((item2, index2) => {
                                        return {
                                            ...item2,
                                            index: index + 1,
                                        };
                                    });
                                });
                                setSubmitLoading(true)
                                // 构建提交数据对象，只包含非空的数据
                                const buildSubmitData = () => {
                                    // 将列表中的列 type 替换为用户当前选择（固定值/动态值/多个值/SPU/SKU）
                                    const withSelectedType = (
                                        cols: any[],
                                        vals: any,
                                        listType: 'base' | 'spu' | 'sku'
                                    ) => {
                                        if (!Array.isArray(cols) || cols.length === 0) return cols;
                                        const pickType = (idx: number, name: string) => {
                                            // 二维（如 sku）使用第一行的类型；一维直接取对应索引
                                            if (Array.isArray(vals?.[0])) {
                                                return vals?.[0]?.[idx]?.type
                                                    || selectedValues[name]
                                                    || 'fixed';
                                            }
                                            return vals?.[idx]?.type
                                                || selectedValues[name]
                                                || 'fixed';
                                        };
                                        return cols.map((col, idx) => ({
                                            ...col,
                                            type: pickType(idx, col?.name)
                                        }));
                                    };

                                    const submitData: any = {
                                        name: templateName,
                                    };

                                    // overall
                                    if (overallData && overallData.length > 0) {
                                        submitData.overall = overallData;
                                    }

                                    // base：同时携带列的最终 type 以及值列表
                                    if (baseData && baseData.length > 0) {
                                        submitData.base = withSelectedType(baseData, baseValues, 'base');
                                        submitData.baseValues = baseValues;
                                    }

                                    // spu：同时携带列的最终 type 以及值列表
                                    if (spuData && spuData.length > 0) {
                                        submitData.spu = withSelectedType(spuData, spuValues, 'spu');
                                        submitData.spuValues = spuValues;
                                    }

                                    // sku：同时携带列的最终 type（以第一行的类型为准）以及表格值
                                    if (skuData && skuData.length > 0) {
                                        submitData.sku = withSelectedType(skuData, skuValues, 'sku');
                                        submitData.skuValues = result;
                                    }

                                    // 其他字段
                                    if (currentType === 'add') {
                                        submitData.file = fileUrl;
                                        submitData.groupId = typeof selectedGroup === 'number' ? selectedGroup : '';
                                        submitData.templateType = selectedModel;
                                    } else {
                                        submitData.id = currentId;
                                        submitData.templateType = selectedModel;
                                    }

                                    return submitData;
                                };
                                const submitData = buildSubmitData();
                                console.log(submitData,templateName);
                                

                                if (currentType == 'add') {
                                    addGoodsTemplate(submitData).then(res => {
                                        messageApi.success('导入商品模板新增成功')
                                        setIsModalVisible(false)
                                        fetchImportTemplateList(1, 10, activeTab?.id,userId)
                                        setSelectedModel(null);//重置模板类型
                                    }).catch((err) => {
                                        messageApi.error(`商品模板新增失败:${err?.msg || err?.data?.msg || ''}`)
                                    }).finally(() => {
                                        setSubmitLoading(false)
                                    })
                                } else {
                                    
                                    editGoodsTemplate(submitData).then(res => {
                                        messageApi.success('导入商品模板编辑成功')
                                        setIsModalVisible(false)
                                        fetchImportTemplateList(1, 10, activeTab?.id,userId)
                                        setSelectedModel(null);//重置模板类型
                                    }).catch((err) => {
                                        messageApi.error(`商品模板编辑失败:${err?.msg || err?.data?.msg || ''}`)
                                    }).finally(() => {
                                        setSubmitLoading(false)
                                    })
                                }

                            }}>
                                提交
                            </Button>
                        </div>
                    </div>
                    <div className=' h-[calc(100vh-182px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                        <p className='font-bold text-[16px] mt-2 mb-1'>全局信息</p>
                        <div className='flex flex-wrap gap-y-1 gap-x-8  text-[12px] '>
                            {overallData?.map((item: any) => (
                                <p key={item?.column}><span className='text-[#555]'>{item?.name}:</span>{item?.value}</p>
                            ))}
                        </div>
                        {baseData && baseData.length > 0 && (
                            <>
                                <p className='font-bold text-[16px] mt-2 mb-1'>基础信息</p>
                                <Table
                                    id='base-table'
                                    columns={baseColumns}
                                    dataSource={baseTableDataSource}
                                    pagination={false}
                                    bordered
                                    scroll={{ x: 'max-content' }}
                                    components={stableTableComponents}
                                />
                            </>)}
                        {spuData && spuData.length > 0 && (
                            <>
                                <p className='font-bold text-[16px] mt-2 mb-1'>SPU信息</p>
                                <Table
                                    id='spu-table'
                                    columns={spuColumns}
                                    dataSource={spuTableDataSource}
                                    pagination={false}
                                    bordered
                                    scroll={{ x: 'max-content' }}
                                    components={stableTableComponents}
                                />
                            </>)}
                        {skuData && skuData.length > 0 && (
                            <>
                                <p className='font-bold text-[16px] mt-2 mb-1'>SKU信息</p>
                                <Table
                                    id='sku-table'
                                    rowKey='key'
                                    columns={skuColumns}
                                    dataSource={skuTableDataSource}
                                    pagination={false}
                                    bordered
                                    scroll={{ x: 'max-content' }}
                                    components={stableTableComponents}
                                />
                                <Button
                                    type="dashed"
                                    icon={<PlusOutlined />}
                                    onClick={handleAddSkuRow}
                                    style={{ marginTop: 16 }}
                                >
                                    添加 SKU 行
                                </Button>
                            </>)}
                    </div>
                </div>
            </Modal>
            {/* 新增/编辑模板弹窗 end */}

            <div className="w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide">
                <div className='flex'>
                    <Tabs
                        activeKey={activeTab?.id?.toString()}
                        onChange={(key: any) => {
                            const selectedTab = groups.find(g => g.id.toString() === key);
                            setActiveTab(selectedTab);
                            // 根据选中的分组ID查询数据
                            fetchImportTemplateList(1, 10, key,userId);
                        }}
                        items={tabItems}
                        style={{ maxWidth: 'calc(100vw - 258px)' }}
                    />
                    <Button
                        type="link"
                        onClick={() => {
                            setModalVisible(true)
                            setGroupName('')
                            setEditingGroupId('')
                        }}
                        style={{ color: '#F06A34', height: '32px', padding: '0px', gap: '2px', margin: '7px 0 0 10px' }}
                    >
                        <span className='text-[22px] mb-[5px]'>+</span>创建分组
                    </Button>
                </div>
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    className="mt-4"
                    scroll={{ x: 966 }}
                    loading={tableLoading}
                    pagination={{
                        ...pagination,
                        showTotal: (total: number) => `共 ${total} 条`,
                        showSizeChanger: false
                    }}
                    onChange={handleTableChange}
                />
            </div>
        </div >
    )
};

export default ImportTemplate;