/**
 * @Author: w<PERSON><PERSON>
 * @Date: 2023/12/28
 * @Description: ""
 */
import {
	BaseAbstractClass,
	IBaseAbstractClassProps
} from '@/helper/base/BaseAbstractClass'
import { DRAW_COLOR } from '@/constant'
import { atom } from 'jotai'
import { isPromise } from '@/utils/isPromise'

interface IDrawingManagerProps extends IBaseAbstractClassProps {
	el?: HTMLCanvasElement | Promise<HTMLCanvasElement>
}

export class DrawingManager extends BaseAbstractClass<IDrawingManagerProps> {
	/* canvas 元素 */
	private el?: HTMLCanvasElement
	/* 画笔颜色 */
	private brushColor: string = DRAW_COLOR
	/* 画笔大小 */
	private brushSizeAtom = atom(20)
	/* 橡皮大小 */
	private eraserSizeAtom = atom(
		(get) => get(this.brushSizeAtom),
		(get, set, args: number) => set(this.brushSizeAtom, args)
	)
	private undoDrawHistoryAtom = atom<string[]>([])
	/* 撤销画布历史记录 */
	private redoDrawHistoryAtom = atom<string[]>([])
	/* 画布的宽 */
	private canvasWidthAtom = atom(0)
	/* 画布的高 */
	private canvasHeightAtom = atom(0)

	// eslint-disable-next-line @typescript-eslint/no-useless-constructor
	constructor(props: IDrawingManagerProps) {
		super(props)
		this.init(props)
	}

	async asyncInit(props: IDrawingManagerProps): Promise<void> {
		const { el } = props
		const { promise, resolve, reject } = this.initTask

		if (isPromise(el)) {
			el.then((r) => {
				this.el = r
				resolve()
			}).catch(reject)
		} else {
			this.el = el
			resolve()
		}

		return promise
	}

	setCanvasEle(el: HTMLCanvasElement) {
		this.el = el
		this.initTask.resolve()
	}
	getCanvasEle() {
		return this.el
	}

	get brushSize() {
		return this.genAtomMethod(this.brushSizeAtom)
	}

	get eraserSize() {
		return this.genAtomMethod(this.eraserSizeAtom)
	}

	get undoDrawHistory() {
		return this.genAtomMethod(this.undoDrawHistoryAtom)
	}

	get redoDrawHistory() {
		return this.genAtomMethod(this.redoDrawHistoryAtom)
	}

	get canvasWidth() {
		return this.genAtomMethod(this.canvasWidthAtom)
	}

	get canvasHeight() {
		return this.genAtomMethod(this.canvasHeightAtom)
	}

	async destory(): Promise<void> {
		await super.destory()
		this.el = undefined
	}

	// 在 canvas 上绘制 图片
	async drawImage(
		url: string,
		options: {
			history: boolean // 是否要记入历史
		}
	) {
		const { promise, resolve, reject } = Promise.withResolvers()
		await this.initTask.promise
		if (!this.el) {
			return Promise.reject('el 为空')
		}
		const ctx = this.el.getContext('2d')!
		const image = document.createElement('img')
		const width = this.canvasWidth.get()
		const height = this.canvasHeight.get()
		image.addEventListener('load', () => {
			ctx.clearRect(0, 0, width, height)
			ctx.drawImage(image, 0, 0)
			// 进来第一步，要先将当前背景图片存到历史里面，并且要永远存在
			if (options.history) {
				const his = this.undoDrawHistory.get()
				this.undoDrawHistory.set([...his, this.el?.toDataURL()!], {
					silent: true
				})
			}
			resolve()
		})
		image.onerror = (error) => {
			// 进来第一步，要先将当前背景图片存到历史里面，并且要永远存在
			if (options.history) {
				const his = this.undoDrawHistory.get()
				this.undoDrawHistory.set([...his, this.el?.toDataURL()!], {
					silent: true
				})
			}
			console.log('www--------->图片加载失败，', url, error, this)
			reject(error)
		}
		image.src = url
		return promise
	}

	// 绘图
	drawing(
		oldX: number,
		oldY: number,
		newX: number,
		newY: number,
		options: {
			isEraser: boolean // 是否为橡皮擦
		}
	) {
		if (!this.el) {
			return 'el 为空'
		}
		const ctx = this.el.getContext('2d')!
		const width = this.canvasWidth.get()
		const height = this.canvasHeight.get()
		const eraserSize = this.eraserSize.get()
		const brushSize = this.brushSize.get()
		ctx.beginPath()
		ctx.save()
		if (options.isEraser) {
			ctx.globalCompositeOperation = 'destination-out'
		} else {
			ctx.globalCompositeOperation = 'source-over'
		}
		ctx.strokeStyle = this.brushColor
		ctx.lineJoin = 'round'
		ctx.lineWidth = brushSize
		ctx.moveTo(oldX, oldY)
		ctx.lineTo(newX, newY)
		ctx.closePath()
		ctx.stroke()
		ctx.restore()
	}

	// 增加历史记录
	setUndoDrawHistory() {
		const his = this.undoDrawHistory.get()
		this.undoDrawHistory.set([...his, this.el?.toDataURL()!], {
			silent: true
		})
		this.redoDrawHistory.set([], {
			silent: true
		})
	}

	// 上一步
	undoDraw() {
		const { get: getUndoDrawHistory, set: setUndoDrawHistory } =
			this.undoDrawHistory
		const { get: getRedoDrawHistory, set: setRedoDrawHistory } =
			this.redoDrawHistory
		const undoDrawHistory = getUndoDrawHistory()
		const redoDrawHistory = getRedoDrawHistory()
		// console.log('www------上一步--->', undoDrawHistory, redoDrawHistory)
		if (undoDrawHistory.length > 0) {
			const lastImageData = undoDrawHistory.at(-1)
			if (lastImageData) {
				setUndoDrawHistory(undoDrawHistory.slice(0, -1))
				setRedoDrawHistory([...redoDrawHistory, lastImageData])
			}
		}
	}

	// 下一步
	redoDraw() {
		const { get: getUndoDrawHistory, set: setUndoDrawHistory } =
			this.undoDrawHistory
		const { get: getRedoDrawHistory, set: setRedoDrawHistory } =
			this.redoDrawHistory
		if (getRedoDrawHistory().length > 0) {
			const lastImageData = getRedoDrawHistory().at(-1)
			if (lastImageData) {
				setUndoDrawHistory([...getUndoDrawHistory(), lastImageData])
				setRedoDrawHistory(getRedoDrawHistory().slice(0, -1))
			}
		}
	}

	// 清除
	clearDraw() {
		const { get: getUndoDrawHistory, set: setUndoDrawHistory } =
			this.undoDrawHistory
		const { get: getRedoDrawHistory, set: setRedoDrawHistory } =
			this.redoDrawHistory
		setUndoDrawHistory([...getUndoDrawHistory(), getUndoDrawHistory()[0]])
	}

	// 获取当前图片
	getCurrentImage() {
		return this.el?.toDataURL() ?? ''
	}
}
export const createDrawingManager = (props: IDrawingManagerProps) => {
	return new DrawingManager(props)
}
