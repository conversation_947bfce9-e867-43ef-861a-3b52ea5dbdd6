package com.dataxai.service.impl;

import java.util.List;
import java.util.ArrayList;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.mapper.TitleExtractionTaskDetailMapper;
import com.dataxai.domain.TitleExtractionTaskDetail;
import com.dataxai.service.ITitleExtractionTaskDetailService;
import com.github.pagehelper.PageHelper;

/**
 * 标题提取任务详情表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class TitleExtractionTaskDetailServiceImpl implements ITitleExtractionTaskDetailService
{
    @Autowired
    private TitleExtractionTaskDetailMapper titleExtractionTaskDetailMapper;

    /**
     * 查询标题提取任务详情表
     *
     * @param id 标题提取任务详情表主键
     * @return 标题提取任务详情表
     */
    @Override
    public TitleExtractionTaskDetail selectTitleExtractionTaskDetailById(Long id)
    {
        return titleExtractionTaskDetailMapper.selectTitleExtractionTaskDetailById(id);
    }

    /**
     * 查询标题提取任务详情表列表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 标题提取任务详情表
     */
    @Override
    public List<TitleExtractionTaskDetail> selectTitleExtractionTaskDetailList(TitleExtractionTaskDetail titleExtractionTaskDetail)
    {
        return titleExtractionTaskDetailMapper.selectTitleExtractionTaskDetailList(titleExtractionTaskDetail);
    }

    /**
     * 根据任务ID查询详情列表
     *
     * @param taskId 任务ID
     * @return 标题提取任务详情表集合
     */
    @Override
    public List<TitleExtractionTaskDetail> selectTitleExtractionTaskDetailByTaskId(Long taskId)
    {
        return titleExtractionTaskDetailMapper.selectTitleExtractionTaskDetailByTaskId(taskId);
    }

    /**
     * 新增标题提取任务详情表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 结果
     */
    @Override
    public int insertTitleExtractionTaskDetail(TitleExtractionTaskDetail titleExtractionTaskDetail)
    {
        titleExtractionTaskDetail.setCreateTime(DateUtils.getNowDate());
        return titleExtractionTaskDetailMapper.insertTitleExtractionTaskDetail(titleExtractionTaskDetail);
    }

    /**
     * 批量新增标题提取任务详情表
     *
     * @param titleExtractionTaskDetailList 标题提取任务详情表列表
     * @return 结果
     */
    @Override
    public int insertTitleExtractionTaskDetailBatch(List<TitleExtractionTaskDetail> titleExtractionTaskDetailList)
    {
        if (titleExtractionTaskDetailList == null || titleExtractionTaskDetailList.isEmpty()) {
            return 0;
        }

        int result = 0;
        for (TitleExtractionTaskDetail detail : titleExtractionTaskDetailList) {
            detail.setCreateTime(DateUtils.getNowDate());
            result += titleExtractionTaskDetailMapper.insertTitleExtractionTaskDetail(detail);
        }
        return result;
    }

    /**
     * 修改标题提取任务详情表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 结果
     */
    @Override
    public int updateTitleExtractionTaskDetail(TitleExtractionTaskDetail titleExtractionTaskDetail)
    {
        titleExtractionTaskDetail.setUpdateTime(DateUtils.getNowDate());
        return titleExtractionTaskDetailMapper.updateTitleExtractionTaskDetail(titleExtractionTaskDetail);
    }

    /**
     * 批量删除标题提取任务详情表
     *
     * @param ids 需要删除的标题提取任务详情表主键
     * @return 结果
     */
    @Override
    public int deleteTitleExtractionTaskDetailByIds(Long[] ids)
    {
        return titleExtractionTaskDetailMapper.deleteTitleExtractionTaskDetailByIds(ids);
    }

    /**
     * 删除标题提取任务详情表信息
     *
     * @param id 标题提取任务详情表主键
     * @return 结果
     */
    @Override
    public int deleteTitleExtractionTaskDetailById(Long id)
    {
        return titleExtractionTaskDetailMapper.deleteTitleExtractionTaskDetailById(id);
    }
}