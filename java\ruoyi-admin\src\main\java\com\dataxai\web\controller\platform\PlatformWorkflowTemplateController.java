package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.domain.TUser;
import com.dataxai.domain.TeamUser;
import com.dataxai.domain.WorkflowTemplate;
import com.dataxai.domain.WorkflowTemplateGroup;
import com.dataxai.domain.WorkflowTemplateNode;
import com.dataxai.domain.dto.CopyTemplateRequest;
import com.dataxai.domain.dto.MoveTemplateRequest;
import com.dataxai.domain.dto.UpdateNodesRequest;
import com.dataxai.domain.dto.WorkflowTemplateDetailDTO;
import com.dataxai.mapper.WorkflowTemplateNodeMapper;
import com.dataxai.service.ITeamUserService;
import com.dataxai.service.IWorkflowTemplateGroupService;
import com.dataxai.service.IWorkflowTemplateService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 平台API-工作流模板接口
 */
@Api(tags = "平台API工作流模板接口")
@RestController
@RequestMapping("/platform/workflow/template")
public class PlatformWorkflowTemplateController extends BaseController {

    @Autowired
    private IWorkflowTemplateService workflowTemplateService;
    @Autowired
    private IWorkflowTemplateGroupService workflowTemplateGroupService;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;
    @Autowired
    private WorkflowTemplateNodeMapper workflowTemplateNodeMapper;

    private boolean isTeamAdmin(Long userId) {
        TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
        return teamUser != null && Boolean.TRUE.equals(teamUser.getIsAdmin());
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId(TUser currentUser) {
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());

        Long teamId = currentUser.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 查询工作流模板列表（分页）
     */
    @ApiOperation("查询工作流模板列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(
            HttpServletRequest request,
            WorkflowTemplate workflowTemplate
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            // 验证团队模式并使用当前登录用户所属的团队ID
            Long currentUserTeamId = validateAndGetTeamId(user);
            workflowTemplate.setTeamId(currentUserTeamId);
            Long userId = user.getUserId();
            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
            if (teamUser.getIsAdmin() != true){
                workflowTemplate.setUserId(userId);
            }
            startPage();
            List<WorkflowTemplate> list = workflowTemplateService.selectWorkflowTemplateList(workflowTemplate);

            // 为每个工作流模板查询节点信息
            List<WorkflowTemplateDetailDTO> resultList = new ArrayList<>();
            for (WorkflowTemplate template : list) {
                WorkflowTemplateDetailDTO detailDTO = new WorkflowTemplateDetailDTO();
                // 复制模板基本信息
                detailDTO.setId(template.getId());
                detailDTO.setTemplateName(template.getTemplateName());
                detailDTO.setGroupId(template.getGroupId());
                detailDTO.setTeamId(template.getTeamId());
                detailDTO.setUserId(template.getUserId());
                detailDTO.setCreateTime(template.getCreateTime());
                detailDTO.setUpdateTime(template.getUpdateTime());

                // 查询关联的分组信息
                if (template.getGroupId() != null) {
                    WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(template.getGroupId());
                    if (group != null) {
                        detailDTO.setGroupName(group.getGroupName());
                    }
                }

                // 查询模板节点列表
                List<WorkflowTemplateNode> nodeList = workflowTemplateNodeMapper.selectByTemplateId(template.getId());

                detailDTO.setNodeList(nodeList);

                resultList.add(detailDTO);
            }

            // 组装自定义分页结构
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", new PageInfo<>(list).getTotal());
            pageData.put("data", resultList);
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流模板详细信息
     */
    @ApiOperation("获取工作流模板详细信息")
    @GetMapping("/detail/{id}")
    public R<WorkflowTemplate> getInfo(HttpServletRequest request, @ApiParam("模板ID") @PathVariable("id") Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
            if (template == null) {
                return R.fail("工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(template.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限访问此模板");
                }
                if (!admin && !Objects.equals(template.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此模板");
                }
            } else {
                if (!Objects.equals(template.getTeamId(), 0L) || !Objects.equals(template.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此模板");
                }
            }

            return R.ok(template);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流模板详细信息（包含节点）
     */
    @ApiOperation("获取工作流模板详细信息（包含节点）")
    @GetMapping("/details/{id}")
    public R<WorkflowTemplate> getDetails(HttpServletRequest request, @ApiParam("模板ID") @PathVariable("id") Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
            if (template == null) {
                return R.fail("工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(template.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限访问此模板");
                }
                if (!admin && !Objects.equals(template.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此模板");
                }
            } else {
                if (!Objects.equals(template.getTeamId(), 0L) || !Objects.equals(template.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此模板");
                }
            }

            return R.ok(workflowTemplateService.selectWorkflowTemplateWithNodes(id));
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增工作流模板
     */
    @ApiOperation("新增工作流模板")
    @Log(title = "平台API-新增工作流模板", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(HttpServletRequest request, @RequestBody WorkflowTemplate workflowTemplate) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplate == null || workflowTemplate.getTemplateName() == null || workflowTemplate.getTemplateName().trim().isEmpty()) {
                return R.fail("模板名称不能为空");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;

            workflowTemplate.setUserId(user.getUserId());
            workflowTemplate.setTeamId(resolvedTeamId);

            // 校验分组归属
            if (workflowTemplate.getGroupId() != null) {
                WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
                if (group == null || !Objects.equals(group.getTeamId(), resolvedTeamId)) {
                    return R.fail("指定的工作流模板分组不存在或不属于当前团队");
                }
            }

            // 名称唯一性
            if (!workflowTemplateService.checkTemplateNameUnique(resolvedTeamId, workflowTemplate.getTemplateName(), null)) {
                return R.fail("模板名称在当前团队中已存在");
            }

            int rows = workflowTemplateService.insertWorkflowTemplate(workflowTemplate);
            return rows > 0 ? R.ok("新增成功") : R.fail("新增失败");
        } catch (Exception e) {
            return R.fail("新增失败: " + e.getMessage());
        }
    }

    /**
     * 新增工作流模板（包含节点）
     */
    @ApiOperation("新增工作流模板（包含节点）")
    @Log(title = "平台API-新增工作流模板（包含节点）", businessType = BusinessType.INSERT)
    @PostMapping("/withNodes")
    public R<String> addWithNodes(HttpServletRequest request, @RequestBody WorkflowTemplate workflowTemplate) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplate == null || workflowTemplate.getTemplateName() == null || workflowTemplate.getTemplateName().trim().isEmpty()) {
                return R.fail("模板名称不能为空");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;

            workflowTemplate.setUserId(user.getUserId());
            workflowTemplate.setTeamId(resolvedTeamId);

            // 校验分组归属
            if (workflowTemplate.getGroupId() != null) {
                WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
                if (group == null || !Objects.equals(group.getTeamId(), resolvedTeamId)) {
                    return R.fail("指定的工作流模板分组不存在或不属于当前团队");
                }
            }

            // 名称唯一性
            if (!workflowTemplateService.checkTemplateNameUnique(resolvedTeamId, workflowTemplate.getTemplateName(), null)) {
                return R.fail("模板名称在当前团队中已存在");
            }

            // 节点校验
            if (workflowTemplate.getNodeList() != null && !workflowTemplate.getNodeList().isEmpty()) {
                String validation = workflowTemplateService.validateNodeConfiguration(workflowTemplate.getNodeList());
                if (validation != null && !validation.isEmpty()) {
                    return R.fail(validation);
                }
            }

            int rows = workflowTemplateService.insertWorkflowTemplateWithNodes(workflowTemplate, workflowTemplate.getNodeList());
            return rows > 0 ? R.ok("新增成功") : R.fail("新增失败");
        } catch (Exception e) {
            return R.fail("新增失败: " + e.getMessage());
        }
    }

    /**
     * 修改工作流模板
     */
    @ApiOperation("修改工作流模板")
    @Log(title = "平台API-修改工作流模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<String> edit(HttpServletRequest request, @RequestBody WorkflowTemplate workflowTemplate) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplate == null || workflowTemplate.getId() == null) {
                return R.fail("参数不完整");
            }
            WorkflowTemplate existing = workflowTemplateService.selectWorkflowTemplateById(workflowTemplate.getId());
            if (existing == null) {
                return R.fail("工作流模板不存在");
            }
            // 权限：团队管理员或创建者；个人模式仅创建者
            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(existing.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限修改此模板");
                }
                if (!admin && !Objects.equals(existing.getUserId(), user.getUserId())) {
                    return R.fail("无权限修改此模板");
                }
            } else {
                if (!Objects.equals(existing.getUserId(), user.getUserId()) || !Objects.equals(existing.getTeamId(), 0L)) {
                    return R.fail("无权限修改此模板");
                }
            }

            Long teamId = existing.getTeamId() == null ? 0L : existing.getTeamId();

            // 校验分组归属
            if (workflowTemplate.getGroupId() != null) {
                WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
                if (group == null || !Objects.equals(group.getTeamId(), teamId)) {
                    return R.fail("指定的工作流模板分组不存在或不属于当前团队");
                }
            }

            // 名称唯一性（排除自己）
            if (!workflowTemplateService.checkTemplateNameUnique(teamId, workflowTemplate.getTemplateName(), workflowTemplate.getId())) {
                return R.fail("模板名称在当前团队中已存在");
            }

            // 固定归属
            workflowTemplate.setTeamId(teamId);
            workflowTemplate.setUserId(existing.getUserId());

            int rows = workflowTemplateService.updateWorkflowTemplate(workflowTemplate);
            return rows > 0 ? R.ok("修改成功") : R.fail("修改失败");
        } catch (Exception e) {
            return R.fail("修改失败: " + e.getMessage());
        }
    }

    /**
     * 更新模板节点
     */
    @ApiOperation("更新模板节点")
    @Log(title = "平台API-更新模板节点", businessType = BusinessType.UPDATE)
    @PutMapping("/nodes/{templateId}")
    public R<String> updateNodes(HttpServletRequest request,
                                 @ApiParam("模板ID") @PathVariable("templateId") Long templateId,
                                 @RequestBody UpdateNodesRequest body) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplate existing = workflowTemplateService.selectWorkflowTemplateById(templateId);
            if (existing == null) {
                return R.fail("指定的工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(existing.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限更新此模板");
                }
                if (!admin && !Objects.equals(existing.getUserId(), user.getUserId())) {
                    return R.fail("无权限更新此模板");
                }
            } else {
                if (!Objects.equals(existing.getUserId(), user.getUserId()) || !Objects.equals(existing.getTeamId(), 0L)) {
                    return R.fail("无权限更新此模板");
                }
            }

            List<WorkflowTemplateNode> nodeList = body.getNodes();
            if (nodeList != null && !nodeList.isEmpty()) {
                String validation = workflowTemplateService.validateNodeConfiguration(nodeList);
                if (validation != null && !validation.isEmpty()) {
                    return R.fail(validation);
                }
            }

            int rows = workflowTemplateService.updateWorkflowTemplateNodes(templateId, nodeList);
            return rows > 0 ? R.ok("更新成功") : R.fail("更新失败");
        } catch (Exception e) {
            return R.fail("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除工作流模板（批量）
     */
    @ApiOperation("删除工作流模板")
    @Log(title = "平台API-删除工作流模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public R<String> remove(HttpServletRequest request, @ApiParam("模板ID数组") @PathVariable Long[] ids) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (ids == null || ids.length == 0) {
                return R.fail("参数不能为空");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            for (Long id : ids) {
                WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
                if (template == null) {
                    return R.fail("模板不存在，ID: " + id);
                }
                if (info.isTeamMode() && info.getTeamId() != null) {
                    if (!Objects.equals(template.getTeamId(), info.getTeamId())) {
                        return R.fail("无权限删除模板，ID: " + id);
                    }
                    if (!admin && !Objects.equals(template.getUserId(), user.getUserId())) {
                        return R.fail("无权限删除模板，ID: " + id);
                    }
                } else {
                    if (!Objects.equals(template.getTeamId(), 0L) || !Objects.equals(template.getUserId(), user.getUserId())) {
                        return R.fail("无权限删除模板，ID: " + id);
                    }
                }
            }

            int rows = workflowTemplateService.deleteWorkflowTemplateByIds(ids);
            return rows > 0 ? R.ok("删除成功") : R.fail("删除失败");
        } catch (Exception e) {
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    /**
     * 复制工作流模板
     */
    @ApiOperation("复制工作流模板")
    @Log(title = "平台API-复制工作流模板", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{templateId}")
    public R<String> copy(HttpServletRequest request,
                          @ApiParam("源模板ID") @PathVariable("templateId") Long templateId,
                          @RequestBody CopyTemplateRequest body) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplate source = workflowTemplateService.selectWorkflowTemplateById(templateId);
            if (source == null) {
                return R.fail("指定的源工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;

            // 权限：团队管理员或创建者；个人模式仅创建者
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(source.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限复制此模板");
                }
                if (!admin && !Objects.equals(source.getUserId(), user.getUserId())) {
                    return R.fail("无权限复制此模板");
                }
            } else {
                if (!Objects.equals(source.getUserId(), user.getUserId()) || !Objects.equals(source.getTeamId(), 0L)) {
                    return R.fail("无权限复制此模板");
                }
            }

            // 校验目标分组归属
            if (body.getNewGroupId() != null) {
                WorkflowTemplateGroup targetGroup = workflowTemplateGroupService.selectWorkflowTemplateGroupById(body.getNewGroupId());
                if (targetGroup == null || !Objects.equals(targetGroup.getTeamId(), resolvedTeamId)) {
                    return R.fail("指定的目标分组不存在或不属于当前团队");
                }
            }

            String newTemplateName = source.getTemplateName() + "_copy";
            int rows = workflowTemplateService.copyWorkflowTemplate(templateId, newTemplateName, body.getNewGroupId());
            return rows > 0 ? R.ok("复制成功") : R.fail("复制失败");
        } catch (Exception e) {
            return R.fail("复制失败: " + e.getMessage());
        }
    }

    /**
     * 移动模板到指定分组
     */
    @ApiOperation("移动模板到指定分组")
    @Log(title = "平台API-移动模板到指定分组", businessType = BusinessType.UPDATE)
    @PutMapping("/move/{templateId}")
    public R<String> moveToGroup(HttpServletRequest request,
                                 @ApiParam("模板ID") @PathVariable("templateId") Long templateId,
                                 @RequestBody MoveTemplateRequest body) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(templateId);
            if (template == null) {
                return R.fail("指定的工作流模板不存在");
            }
            if (body.getNewGroupId() == null) {
                return R.fail("新分组ID不能为空");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;

            // 权限：团队管理员或创建者；个人模式仅创建者
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(template.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限移动此模板");
                }
                if (!admin && !Objects.equals(template.getUserId(), user.getUserId())) {
                    return R.fail("无权限移动此模板");
                }
            } else {
                if (!Objects.equals(template.getUserId(), user.getUserId()) || !Objects.equals(template.getTeamId(), 0L)) {
                    return R.fail("无权限移动此模板");
                }
            }

            // 校验目标分组归属
            WorkflowTemplateGroup targetGroup = workflowTemplateGroupService.selectWorkflowTemplateGroupById(body.getNewGroupId());
            if (targetGroup == null || !Objects.equals(targetGroup.getTeamId(), resolvedTeamId)) {
                return R.fail("指定的目标分组不存在或不属于当前团队");
            }

            int rows = workflowTemplateService.moveTemplateToGroup(templateId, body.getNewGroupId());
            return rows > 0 ? R.ok("移动成功") : R.fail("移动失败");
        } catch (Exception e) {
            return R.fail("移动失败: " + e.getMessage());
        }
    }

    /**
     * 检查模板名称唯一性
     */
    @ApiOperation("检查模板名称唯一性")
    @PostMapping("/checkTemplateNameUnique")
    public R<Boolean> checkTemplateNameUnique(HttpServletRequest request, @RequestBody WorkflowTemplate workflowTemplate) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplate == null || workflowTemplate.getTemplateName() == null || workflowTemplate.getTemplateName().trim().isEmpty()) {
                return R.fail("模板名称不能为空");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;

            boolean unique = workflowTemplateService.checkTemplateNameUnique(
                    resolvedTeamId,
                    workflowTemplate.getTemplateName(),
                    workflowTemplate.getId());
            return R.ok(unique);
        } catch (Exception e) {
            return R.fail("校验失败: " + e.getMessage());
        }
    }
}