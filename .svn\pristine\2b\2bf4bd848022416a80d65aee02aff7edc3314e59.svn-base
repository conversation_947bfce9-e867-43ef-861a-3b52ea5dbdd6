<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="风格名称"
				prop="name"
			>
				<el-input
					v-model="queryParams.name"
					placeholder="请输入风格名称"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['system:tag:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
					>新增</el-button
				>
			</el-col>
			<right-toolbar
				:show-search.sync="showSearch"
				@queryTable="getList"
			></right-toolbar>
		</el-row>

		<el-table
			v-loading="loading"
			:data="styleList"
		>
			<!-- @selection-change="handleSelectionChange"
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/> -->
			<el-table-column
				label="id"
				align="center"
				prop="id"
			/>
			<el-table-column
				label="风格名称"
				align="center"
				prop="name"
			/>
			<el-table-column
				label="风格描述"
				align="center"
				prop="stylePrompt"
			/>
			<el-table-column
				label="风格分类"
				align="center"
				prop="categoryName"
			/>
			<el-table-column
				label="风格图片"
				align="center"
				prop="styleUrl"
				width="100"
			>
				<template slot-scope="scope">
					<image-preview
						:src="scope.row.styleUrl"
						:width="50"
						:height="50"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="排序"
				align="center"
				prop="sortOrder"
			/>
			<el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['system:tag:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<el-button
						v-hasPermi="['system:tag:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改风格对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="500px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
				@submit.native.prevent
			>
				<el-form-item
					label="风格名称"
					prop="name"
				>
					<el-input
						v-model="form.name"
						placeholder="请输入风格名称"
					/>
				</el-form-item>
			</el-form>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
				@submit.native.prevent
			>
				<el-form-item
					label="风格描述"
					prop="stylePrompt"
				>
					<el-input
						type="textarea"
						v-model="form.stylePrompt"
						placeholder="请输入风格描述"
					/>
				</el-form-item>
				<el-form-item
					label="风格分类"
					prop="materialStyleCategoryId"
				>
					<el-select
						v-model="form.materialStyleCategoryId"
						placeholder="请选择风格分类"
						style="width: 100%"
					>
						<el-option
							v-for="item in styleCategoryList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="排序"
					prop="sortOrder"
				>
					<el-input-number
						v-model="form.sortOrder"
						controls-position="right"
						:min="0"
					/>
				</el-form-item>
				<el-form-item
					label="风格图片"
					prop="styleUrl"
				>
					<image-upload
						v-model="form.styleUrl"
						:limit="1"
						:file-type="['png', 'jpg', 'jpeg', 'webp']"
					/>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	getStyleList,
	getStyle,
	delStyle,
	addStyle,
	updateStyle,
	getStyleCategoryList
} from '@/api/system/materialConfig'

export default {
	name: 'Tag',
	data() {
		return {
			// 类型
			taskType: 8,
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 风格表格数据
			styleList: [],
			// 风格分类列表
			styleCategoryList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				name: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {
				name: [
					{ required: true, message: '风格名称不能为空', trigger: 'blur' }
				],
				stylePrompt: [
					{ required: true, message: '风格描述不能为空', trigger: 'blur' }
				],
				materialStyleCategoryId: [
					{ required: true, message: '风格分类不能为空', trigger: 'blur' }
				],
				sortOrder: [
					{ required: true, message: '排序不能为空', trigger: 'blur' }
				],
				styleUrl: [
					{ required: true, message: '风格图片不能为空', trigger: 'blur' }
				]
			}
		}
	},
	created() {
		this.getList()
		this.getSelectlist()
	},
	methods: {
		/** 获取下拉 */
		getSelectlist() {
			getStyleCategoryList({ taskType: this.taskType }).then((res) => {
				this.styleCategoryList = res?.data?.list.map((v) => {
					return {
						label: v.name,
						value: v.id
					}
				})
			})
		},
		/** 查询风格列表 */
		getList() {
			this.loading = true
			getStyleList({ ...this.queryParams, taskType: this.taskType }).then(
				(response) => {
					this.styleList = response?.data?.list
					this.total = response?.data?.total
					this.loading = false
				}
			)
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				name: null,
				stylePrompt: null,
				materialStyleCategoryId: null,
				sortOrder: null,
				styleUrl: null
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		// handleSelectionChange(selection) {
		// 	this.ids = selection.map((item) => item.id)
		// 	this.single = selection.length !== 1
		// 	this.multiple = !selection.length
		// },
		/** 新增按钮操作 */
		handleAdd() {
			if (this.styleCategoryList == 0) {
				this.$modal.msgError('暂无风格分类，请先添加风格分类')
				return
			}
			this.reset()
			this.open = true
			this.title = '添加风格'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getStyle(id).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改风格'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.id != null) {
						updateStyle(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addStyle({ ...this.form, taskType: this.taskType }).then(
							(response) => {
								this.$modal.msgSuccess('新增成功')
								this.open = false
								this.getList()
							}
						)
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids
			this.$modal
				.confirm('是否确认删除风格编号为"' + ids + '"的数据项？')
				.then(function () {
					return delStyle(ids)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		}
	}
}
</script>
