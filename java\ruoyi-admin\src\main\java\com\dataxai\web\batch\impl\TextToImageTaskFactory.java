package com.dataxai.web.batch.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.AbstractBatchTaskFactory;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.utils.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 文生图任务工厂实现
 *
 * <p>处理文生图任务（type=8）</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class TextToImageTaskFactory extends AbstractBatchTaskFactory {

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{(long) Constants.TASK_TYPE_TEXT_TO_IMAGE};
    }

    @Override
    public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

        // 处理路径列表转换为图片URL
        if (!CollectionUtil.isEmpty(pathList)) {
            processPathList(pathList, dto);
        }
        System.out.println("任务开始");
        System.out.println(dto.getImageNumber());
        System.out.println("----------------------------------------------");
        String taskParam1 = dto.getTaskParam();
        JSONObject jsonObj = new JSONObject(taskParam1);
        int imageNumber = jsonObj.getInt("imageNumber");
        System.out.println(imageNumber);
        dto.setImageNumber((long) imageNumber);
//        dto.setImageNumber(imageNumber);
        // 构建任务参数
        String taskParam = buildTaskParam(dto, dto.getType());

        List<Task> taskList = new ArrayList<>();
        List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getTaskParam())) {
                param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
            }

            // 如果有表格数据
            if (tableBytes != null) {
                List<PromptDTO> promptDTOS = PromptDTO.parseTableBytes(tableBytes);
                for (PromptDTO promptDTO : promptDTOS) {
                    Task task = createBaseTask(dto, userId, param, batch);
                    taskList.add(task);

                    TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
                                                                  task.getTaskId(), param.getOriginImgUrl(), batch);
                    taskOrdinal.setShortCutDesc(promptDTO.getPrompt());
                    taskOrdinalList.add(taskOrdinal);
                }
            }
            // 如果有图片URL
            else if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getImgUrl())) {
                List<String> imgUrlList = parseImgUrlList(dto.getImgUrl());
                for (String url : imgUrlList) {
                    Task task = createBaseTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
                    taskList.add(task);

                    TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
                                                                  task.getTaskId(), url, batch);
                    taskOrdinal.setIsAiOcr("1");
                    taskOrdinalList.add(taskOrdinal);
                }
            }

        } catch (Exception e) {
            log.error("创建文生图任务失败", e);
            throw new RuntimeException("创建文生图任务失败", e);
        }

        // 批量插入任务
        insertTasks(taskList);

        // 处理子任务执行
        processTaskOrdinals(taskOrdinalList);

    }

    @Override
    public List<Task> createTasks(BatchDTO dto, Batch batch, Long userId, String taskParam) {
        // 文生图任务在 processBatchTask 中直接处理，不需要单独的创建逻辑
        return new ArrayList<>();
    }

    @Override
    public List<TaskOrdinal> createTaskOrdinals(BatchDTO dto, Batch batch, List<Task> taskList,
                                               Long userId, String taskParam) {
        // 文生图任务在 processBatchTask 中直接处理，不需要单独的创建逻辑
        return new ArrayList<>();
    }
}