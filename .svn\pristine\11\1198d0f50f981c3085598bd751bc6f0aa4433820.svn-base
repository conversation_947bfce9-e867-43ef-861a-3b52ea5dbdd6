"use strict";(self.webpackChunkai_console=self.webpackChunkai_console||[]).push([[776],{34497:function(n,e,a){a.d(e,{A:function(){return H}});var c=a(94423),t=a(27569),o=a(9939),i=a(62951),r=a(76787),l=a.n(r),d=a(48524),s=a(48281),u=a(8076),p=a(99371),h=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],g=o.forwardRef((function(n,e){var a,i=n.prefixCls,r=void 0===i?"rc-switch":i,g=n.className,m=n.checked,A=n.defaultChecked,v=n.disabled,f=n.loadingIcon,b=n.checkedChildren,k=n.unCheckedChildren,I=n.onClick,y=n.onChange,C=n.onKeyDown,x=(0,s.A)(n,h),S=(0,u.A)(!1,{value:m,defaultValue:A}),w=(0,t.A)(S,2),E=w[0],M=w[1];function N(n,e){var a=E;return v||(M(a=n),null===y||void 0===y||y(a,e)),a}var O=l()(r,g,(a={},(0,c.A)(a,"".concat(r,"-checked"),E),(0,c.A)(a,"".concat(r,"-disabled"),v),a));return o.createElement("button",(0,d.A)({},x,{type:"button",role:"switch","aria-checked":E,disabled:v,className:O,ref:e,onKeyDown:function(n){n.which===p.A.LEFT?N(!1,n):n.which===p.A.RIGHT&&N(!0,n),null===C||void 0===C||C(n)},onClick:function(n){var e=N(!E,n);null===I||void 0===I||I(e,n)}}),f,o.createElement("span",{className:"".concat(r,"-inner")},o.createElement("span",{className:"".concat(r,"-inner-checked")},b),o.createElement("span",{className:"".concat(r,"-inner-unchecked")},k)))}));g.displayName="Switch";var m=g,A=a(45475),v=a(45847),f=a(6455),b=a(18317),k=a(54942),I=a(37471),y=a(3425),C=a(80894),x=a(47749),S=function(n){var e=n.componentCls,a=n.trackHeightSM,t=n.trackPadding,o=n.trackMinWidthSM,i=n.innerMinMarginSM,r=n.innerMaxMarginSM,l=n.handleSizeSM,d=n.calc,s="".concat(e,"-inner"),u=(0,k.zA)(d(l).add(d(t).mul(2)).equal()),p=(0,k.zA)(d(r).mul(2).equal());return(0,c.A)({},e,(0,c.A)({},"&".concat(e,"-small"),(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)({minWidth:o,height:a,lineHeight:(0,k.zA)(a)},"".concat(e,"-inner"),(0,c.A)((0,c.A)((0,c.A)({paddingInlineStart:r,paddingInlineEnd:i},"".concat(s,"-checked, ").concat(s,"-unchecked"),{minHeight:a}),"".concat(s,"-checked"),{marginInlineStart:"calc(-100% + ".concat(u," - ").concat(p,")"),marginInlineEnd:"calc(100% - ".concat(u," + ").concat(p,")")}),"".concat(s,"-unchecked"),{marginTop:d(a).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0})),"".concat(e,"-handle"),{width:l,height:l}),"".concat(e,"-loading-icon"),{top:d(d(l).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize}),"&".concat(e,"-checked"),(0,c.A)((0,c.A)({},"".concat(e,"-inner"),(0,c.A)((0,c.A)({paddingInlineStart:i,paddingInlineEnd:r},"".concat(s,"-checked"),{marginInlineStart:0,marginInlineEnd:0}),"".concat(s,"-unchecked"),{marginInlineStart:"calc(100% - ".concat(u," + ").concat(p,")"),marginInlineEnd:"calc(-100% + ".concat(u," - ").concat(p,")")})),"".concat(e,"-handle"),{insetInlineStart:"calc(100% - ".concat((0,k.zA)(d(l).add(t).equal()),")")})),"&:not(".concat(e,"-disabled):active"),(0,c.A)((0,c.A)({},"&:not(".concat(e,"-checked) ").concat(s),(0,c.A)({},"".concat(s,"-unchecked"),{marginInlineStart:d(n.marginXXS).div(2).equal(),marginInlineEnd:d(n.marginXXS).mul(-1).div(2).equal()})),"&".concat(e,"-checked ").concat(s),(0,c.A)({},"".concat(s,"-checked"),{marginInlineStart:d(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:d(n.marginXXS).div(2).equal()})))))},w=function(n){var e=n.componentCls,a=n.handleSize,t=n.calc;return(0,c.A)({},e,(0,c.A)((0,c.A)({},"".concat(e,"-loading-icon").concat(n.iconCls),{position:"relative",top:t(t(a).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"}),"&".concat(e,"-checked ").concat(e,"-loading-icon"),{color:n.switchColor}))},E=function(n){var e=n.componentCls,a=n.trackPadding,t=n.handleBg,o=n.handleShadow,i=n.handleSize,r=n.calc,l="".concat(e,"-handle");return(0,c.A)({},e,(0,c.A)((0,c.A)((0,c.A)({},l,{position:"absolute",top:a,insetInlineStart:a,width:i,height:i,transition:"all ".concat(n.switchDuration," ease-in-out"),"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:t,borderRadius:r(i).div(2).equal(),boxShadow:o,transition:"all ".concat(n.switchDuration," ease-in-out"),content:'""'}}),"&".concat(e,"-checked ").concat(l),{insetInlineStart:"calc(100% - ".concat((0,k.zA)(r(i).add(a).equal()),")")}),"&:not(".concat(e,"-disabled):active"),(0,c.A)((0,c.A)({},"".concat(l,"::before"),{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0}),"&".concat(e,"-checked ").concat(l,"::before"),{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset})))},M=function(n){var e=n.componentCls,a=n.trackHeight,t=n.trackPadding,o=n.innerMinMargin,i=n.innerMaxMargin,r=n.handleSize,l=n.calc,d="".concat(e,"-inner"),s=(0,k.zA)(l(r).add(l(t).mul(2)).equal()),u=(0,k.zA)(l(i).mul(2).equal());return(0,c.A)({},e,(0,c.A)((0,c.A)((0,c.A)({},d,(0,c.A)((0,c.A)((0,c.A)({display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:i,paddingInlineEnd:o,transition:"padding-inline-start ".concat(n.switchDuration," ease-in-out, padding-inline-end ").concat(n.switchDuration," ease-in-out")},"".concat(d,"-checked, ").concat(d,"-unchecked"),{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:"margin-inline-start ".concat(n.switchDuration," ease-in-out, margin-inline-end ").concat(n.switchDuration," ease-in-out"),pointerEvents:"none",minHeight:a}),"".concat(d,"-checked"),{marginInlineStart:"calc(-100% + ".concat(s," - ").concat(u,")"),marginInlineEnd:"calc(100% - ".concat(s," + ").concat(u,")")}),"".concat(d,"-unchecked"),{marginTop:l(a).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0})),"&".concat(e,"-checked ").concat(d),(0,c.A)((0,c.A)({paddingInlineStart:o,paddingInlineEnd:i},"".concat(d,"-checked"),{marginInlineStart:0,marginInlineEnd:0}),"".concat(d,"-unchecked"),{marginInlineStart:"calc(100% - ".concat(s," + ").concat(u,")"),marginInlineEnd:"calc(-100% + ".concat(s," - ").concat(u,")")})),"&:not(".concat(e,"-disabled):active"),(0,c.A)((0,c.A)({},"&:not(".concat(e,"-checked) ").concat(d),(0,c.A)({},"".concat(d,"-unchecked"),{marginInlineStart:l(t).mul(2).equal(),marginInlineEnd:l(t).mul(-1).mul(2).equal()})),"&".concat(e,"-checked ").concat(d),(0,c.A)({},"".concat(d,"-checked"),{marginInlineStart:l(t).mul(-1).mul(2).equal(),marginInlineEnd:l(t).mul(2).equal()}))))},N=function(n){var e=n.componentCls,a=n.trackHeight,t=n.trackMinWidth;return(0,c.A)({},e,Object.assign(Object.assign(Object.assign(Object.assign({},(0,y.dF)(n)),(0,c.A)({position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:t,height:a,lineHeight:(0,k.zA)(a),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:"all ".concat(n.motionDurationMid),userSelect:"none"},"&:hover:not(".concat(e,"-disabled)"),{background:n.colorTextTertiary})),(0,y.K8)(n)),(0,c.A)((0,c.A)((0,c.A)({},"&".concat(e,"-checked"),(0,c.A)({background:n.switchColor},"&:hover:not(".concat(e,"-disabled)"),{background:n.colorPrimaryHover})),"&".concat(e,"-loading, &").concat(e,"-disabled"),{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}}),"&".concat(e,"-rtl"),{direction:"rtl"})))},O=(0,C.OF)("Switch",(function(n){var e=(0,x.oX)(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:"rgba(0, 0, 0, ".concat(n.opacityLoading,")"),switchHandleActiveInset:"-30%"});return[N(e),M(e),E(e),w(e),S(e)]}),(function(n){var e=n.fontSize*n.lineHeight,a=n.controlHeight/2,c=e-4,t=a-4;return{trackHeight:e,trackHeightSM:a,trackMinWidth:2*c+8,trackMinWidthSM:2*t+4,trackPadding:2,handleBg:n.colorWhite,handleSize:c,handleSizeSM:t,handleShadow:"0 2px 4px 0 ".concat(new I.Y("#00230b").setA(.2).toRgbString()),innerMinMargin:c/2,innerMaxMargin:c+2+4,innerMinMarginSM:t/2,innerMaxMarginSM:t+2+4}})),z=function(n,e){var a={};for(var c in n)Object.prototype.hasOwnProperty.call(n,c)&&e.indexOf(c)<0&&(a[c]=n[c]);if(null!=n&&"function"===typeof Object.getOwnPropertySymbols){var t=0;for(c=Object.getOwnPropertySymbols(n);t<c.length;t++)e.indexOf(c[t])<0&&Object.prototype.propertyIsEnumerable.call(n,c[t])&&(a[c[t]]=n[c[t]])}return a},P=o.forwardRef((function(n,e){var a=n.prefixCls,r=n.size,d=n.disabled,s=n.loading,p=n.className,h=n.rootClassName,g=n.style,k=n.checked,I=n.value,y=n.defaultChecked,C=n.defaultValue,x=n.onChange,S=z(n,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),w=(0,u.A)(!1,{value:null!==k&&void 0!==k?k:I,defaultValue:null!==y&&void 0!==y?y:C}),E=(0,t.A)(w,2),M=E[0],N=E[1],P=o.useContext(v.QO),j=P.getPrefixCls,H=P.direction,R=P.switch,q=o.useContext(f.A),T=(null!==d&&void 0!==d?d:q)||s,B=j("switch",a),L=o.createElement("div",{className:"".concat(B,"-handle")},s&&o.createElement(i.A,{className:"".concat(B,"-loading-icon")})),D=O(B),K=(0,t.A)(D,3),X=K[0],W=K[1],G=K[2],F=(0,b.A)(r),V=l()(null===R||void 0===R?void 0:R.className,(0,c.A)((0,c.A)((0,c.A)({},"".concat(B,"-small"),"small"===F),"".concat(B,"-loading"),s),"".concat(B,"-rtl"),"rtl"===H),p,h,W,G),_=Object.assign(Object.assign({},null===R||void 0===R?void 0:R.style),g);return X(o.createElement(A.A,{component:"Switch"},o.createElement(m,Object.assign({},S,{checked:M,onChange:function(){N(arguments.length<=0?void 0:arguments[0]),null===x||void 0===x||x.apply(void 0,arguments)},prefixCls:B,className:V,style:_,disabled:T,ref:e,loadingIcon:L}))))})),j=P;j.__ANT_SWITCH=!0;var H=j},50258:function(n,e,a){a.d(e,{A:function(){return _}});var c=a(94423),t=a(27569),o=a(9939),i=a(41610),r=a(76787),l=a.n(r),d=a(48524),s=a(45726),u=a(62656),p=a(8076),h=a(17909),g=a(48281),m=a(36891),A=a(68887),v=a(84546),f=a(99371),b=o.forwardRef((function(n,e){var a=n.prefixCls,i=n.forceRender,r=n.className,d=n.style,s=n.children,u=n.isActive,p=n.role,h=n.classNames,g=n.styles,m=o.useState(u||i),A=(0,t.A)(m,2),v=A[0],f=A[1];return o.useEffect((function(){(i||u)&&f(!0)}),[i,u]),v?o.createElement("div",{ref:e,className:l()("".concat(a,"-content"),(0,c.A)((0,c.A)({},"".concat(a,"-content-active"),u),"".concat(a,"-content-inactive"),!u),r),style:d,role:p},o.createElement("div",{className:l()("".concat(a,"-content-box"),null===h||void 0===h?void 0:h.body),style:null===g||void 0===g?void 0:g.body},s)):null}));b.displayName="PanelContent";var k=b,I=["showArrow","headerClass","isActive","onItemClick","forceRender","className","classNames","styles","prefixCls","collapsible","accordion","panelKey","extra","header","expandIcon","openMotion","destroyInactivePanel","children"],y=o.forwardRef((function(n,e){var a=n.showArrow,t=void 0===a||a,i=n.headerClass,r=n.isActive,s=n.onItemClick,u=n.forceRender,p=n.className,h=n.classNames,m=void 0===h?{}:h,b=n.styles,y=void 0===b?{}:b,C=n.prefixCls,x=n.collapsible,S=n.accordion,w=n.panelKey,E=n.extra,M=n.header,N=n.expandIcon,O=n.openMotion,z=n.destroyInactivePanel,P=n.children,j=(0,g.A)(n,I),H="disabled"===x,R=null!==E&&void 0!==E&&"boolean"!==typeof E,q=(0,c.A)((0,c.A)((0,c.A)({onClick:function(){null===s||void 0===s||s(w)},onKeyDown:function(n){"Enter"!==n.key&&n.keyCode!==f.A.ENTER&&n.which!==f.A.ENTER||null===s||void 0===s||s(w)},role:S?"tab":"button"},"aria-expanded",r),"aria-disabled",H),"tabIndex",H?-1:0),T="function"===typeof N?N(n):o.createElement("i",{className:"arrow"}),B=T&&o.createElement("div",(0,d.A)({className:"".concat(C,"-expand-icon")},["header","icon"].includes(x)?q:{}),T),L=l()("".concat(C,"-item"),(0,c.A)((0,c.A)({},"".concat(C,"-item-active"),r),"".concat(C,"-item-disabled"),H),p),D=l()(i,"".concat(C,"-header"),(0,c.A)({},"".concat(C,"-collapsible-").concat(x),!!x),m.header),K=(0,A.A)({className:D,style:y.header},["header","icon"].includes(x)?{}:q);return o.createElement("div",(0,d.A)({},j,{ref:e,className:L}),o.createElement("div",K,t&&B,o.createElement("span",(0,d.A)({className:"".concat(C,"-header-text")},"header"===x?q:{}),M),R&&o.createElement("div",{className:"".concat(C,"-extra")},E)),o.createElement(v.Ay,(0,d.A)({visible:r,leavedClassName:"".concat(C,"-content-hidden")},O,{forceRender:u,removeOnLeave:z}),(function(n,e){var a=n.className,c=n.style;return o.createElement(k,{ref:e,prefixCls:C,className:a,classNames:m,style:c,styles:y,isActive:r,forceRender:u,role:S?"tabpanel":void 0},P)})))})),C=["children","label","key","collapsible","onItemClick","destroyInactivePanel"];var x=function(n,e,a){return Array.isArray(n)?function(n,e){var a=e.prefixCls,c=e.accordion,t=e.collapsible,i=e.destroyInactivePanel,r=e.onItemClick,l=e.activeKey,s=e.openMotion,u=e.expandIcon;return n.map((function(n,e){var p=n.children,h=n.label,m=n.key,A=n.collapsible,v=n.onItemClick,f=n.destroyInactivePanel,b=(0,g.A)(n,C),k=String(null!==m&&void 0!==m?m:e),I=null!==A&&void 0!==A?A:t,x=null!==f&&void 0!==f?f:i,S=!1;return S=c?l[0]===k:l.indexOf(k)>-1,o.createElement(y,(0,d.A)({},b,{prefixCls:a,key:k,panelKey:k,isActive:S,accordion:c,openMotion:s,expandIcon:u,header:h,collapsible:I,onItemClick:function(n){"disabled"!==I&&(r(n),null===v||void 0===v||v(n))},destroyInactivePanel:x}),p)}))}(n,a):(0,m.A)(e).map((function(n,e){return function(n,e,a){if(!n)return null;var c=a.prefixCls,t=a.accordion,i=a.collapsible,r=a.destroyInactivePanel,l=a.onItemClick,d=a.activeKey,s=a.openMotion,u=a.expandIcon,p=n.key||String(e),h=n.props,g=h.header,m=h.headerClass,A=h.destroyInactivePanel,v=h.collapsible,f=h.onItemClick,b=!1;b=t?d[0]===p:d.indexOf(p)>-1;var k=null!==v&&void 0!==v?v:i,I={key:p,panelKey:p,header:g,headerClass:m,isActive:b,prefixCls:c,destroyInactivePanel:null!==A&&void 0!==A?A:r,openMotion:s,accordion:t,children:n.props.children,onItemClick:function(n){"disabled"!==k&&(l(n),null===f||void 0===f||f(n))},expandIcon:u,collapsible:k};return"string"===typeof n.type?n:(Object.keys(I).forEach((function(n){"undefined"===typeof I[n]&&delete I[n]})),o.cloneElement(n,I))}(n,e,a)}))},S=a(71306);function w(n){var e=n;if(!Array.isArray(e)){var a=(0,u.A)(e);e="number"===a||"string"===a?[e]:[]}return e.map((function(n){return String(n)}))}var E=o.forwardRef((function(n,e){var a=n.prefixCls,c=void 0===a?"rc-collapse":a,i=n.destroyInactivePanel,r=void 0!==i&&i,u=n.style,g=n.accordion,m=n.className,A=n.children,v=n.collapsible,f=n.openMotion,b=n.expandIcon,k=n.activeKey,I=n.defaultActiveKey,y=n.onChange,C=n.items,E=l()(c,m),M=(0,p.A)([],{value:k,onChange:function(n){return null===y||void 0===y?void 0:y(n)},defaultValue:I,postState:w}),N=(0,t.A)(M,2),O=N[0],z=N[1];(0,h.Ay)(!A,"[rc-collapse] `children` will be removed in next major version. Please use `items` instead.");var P=x(C,A,{prefixCls:c,accordion:g,openMotion:f,expandIcon:b,collapsible:v,destroyInactivePanel:r,onItemClick:function(n){return z((function(){return g?O[0]===n?[]:[n]:O.indexOf(n)>-1?O.filter((function(e){return e!==n})):[].concat((0,s.A)(O),[n])}))},activeKey:O});return o.createElement("div",(0,d.A)({ref:e,className:E,style:u,role:g?"tablist":void 0},(0,S.A)(n,{aria:!0,data:!0})),P)})),M=Object.assign(E,{Panel:y}),N=M,O=(M.Panel,a(42292)),z=a(55643),P=a(82682),j=a(45847),H=a(18317),R=o.forwardRef((function(n,e){var a=o.useContext(j.QO).getPrefixCls,t=n.prefixCls,i=n.className,r=n.showArrow,d=void 0===r||r,s=a("collapse",t),u=l()((0,c.A)({},"".concat(s,"-no-arrow"),!d),i);return o.createElement(N.Panel,Object.assign({ref:e},n,{prefixCls:s,className:u}))})),q=a(54942),T=a(3425),B=a(26241),L=a(80894),D=a(47749),K=function(n){var e=n.componentCls,a=n.contentBg,t=n.padding,o=n.headerBg,i=n.headerPadding,r=n.collapseHeaderPaddingSM,l=n.collapseHeaderPaddingLG,d=n.collapsePanelBorderRadius,s=n.lineWidth,u=n.lineType,p=n.colorBorder,h=n.colorText,g=n.colorTextHeading,m=n.colorTextDisabled,A=n.fontSizeLG,v=n.lineHeight,f=n.lineHeightLG,b=n.marginSM,k=n.paddingSM,I=n.paddingLG,y=n.paddingXS,C=n.motionDurationSlow,x=n.fontSizeIcon,S=n.contentPadding,w=n.fontHeight,E=n.fontHeightLG,M="".concat((0,q.zA)(s)," ").concat(u," ").concat(p);return(0,c.A)({},e,Object.assign(Object.assign({},(0,T.dF)(n)),(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)({backgroundColor:o,border:M,borderRadius:d,"&-rtl":{direction:"rtl"}},"& > ".concat(e,"-item"),(0,c.A)((0,c.A)((0,c.A)({borderBottom:M,"&:first-child":(0,c.A)({},"\n            &,\n            & > ".concat(e,"-header"),{borderRadius:"".concat((0,q.zA)(d)," ").concat((0,q.zA)(d)," 0 0")}),"&:last-child":(0,c.A)({},"\n            &,\n            & > ".concat(e,"-header"),{borderRadius:"0 0 ".concat((0,q.zA)(d)," ").concat((0,q.zA)(d))})},"> ".concat(e,"-header"),Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:i,color:g,lineHeight:v,cursor:"pointer",transition:"all ".concat(C,", visibility 0s")},(0,T.K8)(n)),(0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"> ".concat(e,"-header-text"),{flex:"auto"}),"".concat(e,"-expand-icon"),{height:w,display:"flex",alignItems:"center",paddingInlineEnd:b}),"".concat(e,"-arrow"),Object.assign(Object.assign({},(0,T.Nk)()),{fontSize:x,transition:"transform ".concat(C),svg:{transition:"transform ".concat(C)}})),"".concat(e,"-header-text"),{marginInlineEnd:"auto"}))),"".concat(e,"-collapsible-header"),(0,c.A)({cursor:"default"},"".concat(e,"-header-text"),{flex:"none",cursor:"pointer"})),"".concat(e,"-collapsible-icon"),(0,c.A)({cursor:"unset"},"".concat(e,"-expand-icon"),{cursor:"pointer"}))),"".concat(e,"-content"),(0,c.A)((0,c.A)({color:h,backgroundColor:a,borderTop:M},"& > ".concat(e,"-content-box"),{padding:S}),"&-hidden",{display:"none"})),"&-small",(0,c.A)({},"> ".concat(e,"-item"),(0,c.A)((0,c.A)({},"> ".concat(e,"-header"),(0,c.A)({padding:r,paddingInlineStart:y},"> ".concat(e,"-expand-icon"),{marginInlineStart:n.calc(k).sub(y).equal()})),"> ".concat(e,"-content > ").concat(e,"-content-box"),{padding:k}))),"&-large",(0,c.A)({},"> ".concat(e,"-item"),(0,c.A)((0,c.A)({fontSize:A,lineHeight:f},"> ".concat(e,"-header"),(0,c.A)({padding:l,paddingInlineStart:t},"> ".concat(e,"-expand-icon"),{height:E,marginInlineStart:n.calc(I).sub(t).equal()})),"> ".concat(e,"-content > ").concat(e,"-content-box"),{padding:I}))),"".concat(e,"-item:last-child"),(0,c.A)({borderBottom:0},"> ".concat(e,"-content"),{borderRadius:"0 0 ".concat((0,q.zA)(d)," ").concat((0,q.zA)(d))})),"& ".concat(e,"-item-disabled > ").concat(e,"-header"),(0,c.A)({},"\n          &,\n          & > .arrow\n        ",{color:m,cursor:"not-allowed"})),"&".concat(e,"-icon-position-end"),(0,c.A)({},"& > ".concat(e,"-item"),(0,c.A)({},"> ".concat(e,"-header"),(0,c.A)({},"".concat(e,"-expand-icon"),{order:1,paddingInlineEnd:0,paddingInlineStart:b}))))))},X=function(n){var e=n.componentCls,a="> ".concat(e,"-item > ").concat(e,"-header ").concat(e,"-arrow");return(0,c.A)({},"".concat(e,"-rtl"),(0,c.A)({},a,{transform:"rotate(180deg)"}))},W=function(n){var e=n.componentCls,a=n.headerBg,t=n.paddingXXS,o=n.colorBorder;return(0,c.A)({},"".concat(e,"-borderless"),(0,c.A)((0,c.A)((0,c.A)((0,c.A)((0,c.A)({backgroundColor:a,border:0},"> ".concat(e,"-item"),{borderBottom:"1px solid ".concat(o)}),"\n        > ".concat(e,"-item:last-child,\n        > ").concat(e,"-item:last-child ").concat(e,"-header\n      "),{borderRadius:0}),"> ".concat(e,"-item:last-child"),{borderBottom:0}),"> ".concat(e,"-item > ").concat(e,"-content"),{backgroundColor:"transparent",borderTop:0}),"> ".concat(e,"-item > ").concat(e,"-content > ").concat(e,"-content-box"),{paddingTop:t}))},G=function(n){var e=n.componentCls,a=n.paddingSM;return(0,c.A)({},"".concat(e,"-ghost"),(0,c.A)({backgroundColor:"transparent",border:0},"> ".concat(e,"-item"),(0,c.A)({borderBottom:0},"> ".concat(e,"-content"),(0,c.A)({backgroundColor:"transparent",border:0},"> ".concat(e,"-content-box"),{paddingBlock:a}))))},F=(0,L.OF)("Collapse",(function(n){var e=(0,D.oX)(n,{collapseHeaderPaddingSM:"".concat((0,q.zA)(n.paddingXS)," ").concat((0,q.zA)(n.paddingSM)),collapseHeaderPaddingLG:"".concat((0,q.zA)(n.padding)," ").concat((0,q.zA)(n.paddingLG)),collapsePanelBorderRadius:n.borderRadiusLG});return[K(e),W(e),G(e),X(e),(0,B.A)(e)]}),(function(n){return{headerPadding:"".concat(n.paddingSM,"px ").concat(n.padding,"px"),headerBg:n.colorFillAlter,contentPadding:"".concat(n.padding,"px 16px"),contentBg:n.colorBgContainer}})),V=o.forwardRef((function(n,e){var a=(0,j.TP)("collapse"),r=a.getPrefixCls,d=a.direction,s=a.expandIcon,u=a.className,p=a.style,h=n.prefixCls,g=n.className,A=n.rootClassName,v=n.style,f=n.bordered,b=void 0===f||f,k=n.ghost,I=n.size,y=n.expandIconPosition,C=void 0===y?"start":y,x=n.children,S=n.expandIcon,w=(0,H.A)((function(n){var e;return null!==(e=null!==I&&void 0!==I?I:n)&&void 0!==e?e:"middle"})),E=r("collapse",h),M=r(),R=F(E),q=(0,t.A)(R,3),T=q[0],B=q[1],L=q[2],D=o.useMemo((function(){return"left"===C?"start":"right"===C?"end":C}),[C]),K=null!==S&&void 0!==S?S:s,X=o.useCallback((function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e="function"===typeof K?K(n):o.createElement(i.A,{rotate:n.isActive?"rtl"===d?-90:90:void 0,"aria-label":n.isActive?"expanded":"collapsed"});return(0,P.Ob)(e,(function(){var n;return{className:l()(null===(n=null===e||void 0===e?void 0:e.props)||void 0===n?void 0:n.className,"".concat(E,"-arrow"))}}))}),[K,E]),W=l()("".concat(E,"-icon-position-").concat(D),(0,c.A)((0,c.A)((0,c.A)((0,c.A)({},"".concat(E,"-borderless"),!b),"".concat(E,"-rtl"),"rtl"===d),"".concat(E,"-ghost"),!!k),"".concat(E,"-").concat(w),"middle"!==w),u,g,A,B,L),G=Object.assign(Object.assign({},(0,z.A)(M)),{motionAppear:!1,leavedClassName:"".concat(E,"-content-hidden")}),V=o.useMemo((function(){return x?(0,m.A)(x).map((function(n,e){var a,c,t=n.props;if(null===t||void 0===t?void 0:t.disabled){var o=null!==(a=n.key)&&void 0!==a?a:String(e),i=Object.assign(Object.assign({},(0,O.A)(n.props,["disabled"])),{key:o,collapsible:null!==(c=t.collapsible)&&void 0!==c?c:"disabled"});return(0,P.Ob)(n,i)}return n})):null}),[x]);return T(o.createElement(N,Object.assign({ref:e,openMotion:G},(0,O.A)(n,["rootClassName"]),{expandIcon:X,prefixCls:E,className:W,style:Object.assign(Object.assign({},p),v)}),V))}));var _=Object.assign(V,{Panel:R})}}]);
//# sourceMappingURL=776.c0d70651.chunk.js.map