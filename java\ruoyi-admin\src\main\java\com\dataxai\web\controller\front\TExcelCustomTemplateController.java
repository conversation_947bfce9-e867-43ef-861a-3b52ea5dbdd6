package com.dataxai.web.controller.front;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.domain.*;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.service.*;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.service.export.TExcelExportService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 半托数据商品模板Controller
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/system/template")
public class TExcelCustomTemplateController extends BaseController {
    @Autowired
    private ITExcelCustomTemplateService tExcelCustomTemplateService;
    @Autowired
    private ITExcelTemplateTitleMapsService itExcelTemplateTitleMapsService;
    @Autowired
    private ITExcelTemplateTitleService itExcelTemplateTitleService;
    @Autowired
    private ITExcelCustomValueService itExcelCustomValueService;
    @Autowired
    private AliYunFileService aliYunFileService;
    @Autowired
    private TUserMapper tUserMapper;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITExcelCustomValueService iTExcelCustomValueService;
    @Autowired
    private IProductInfoService iProductInfoService;
    @Autowired
    private IRiskDetectionTaskDetailService iRiskDetectionTaskDetailService;
    @Autowired
    private ITitleExtractionTaskDetailService iTitleExtractionTaskDetailService;

    @Autowired
    private TExcelExportService tExcelExportService;
    @Autowired
    private ITeamUserService iTeamUserService;

    /**
     * 使用策略模式导出 Excel 文件（灰度接口）。
     *
     * 它将导出逻辑委托给 {@link com.dataxai.web.service.export.TExcelExportService}，
     * 由后者根据请求体中的 {@code templateType} 动态选择合适的填充策略。
     *
     * @param dto 客户端传来的请求体，包含所有导出操作所需的参数。
     * @param response HTTP 响应对象，用于将生成的 Excel 文件作为附件下载。
     * @see com.dataxai.web.service.export.TExcelExportService#export(TExcelCustomTemplateDTO, HttpServletResponse)
     */
    @PostMapping("/exportexcel")
    public void exportexcel2(@RequestBody com.dataxai.domain.TExcelCustomTemplateDTO dto, HttpServletResponse response) throws Exception {
        tExcelExportService.export(dto, response);
    }

    /**
     * 查询半托数据商品模板列表
     */
//    @PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public AjaxResult list(TExcelCustomTemplate tExcelCustomTemplate) {
        Long currentUserTeamId = validateAndGetTeamId();
        tExcelCustomTemplate.setTeamId(currentUserTeamId);
        Long userId = SecurityUtils.getUserId();
        TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
//        if(teamUser.getIsAdmin() == true){
            Long selectUserId = tExcelCustomTemplate.getUserId();
            if (selectUserId != null){
                tExcelCustomTemplate.setUserId(selectUserId);
            }
//        }else{
//            tExcelCustomTemplate.setUserId(userId);
//        }
        startPage();
        List<TExcelCustomTemplate> list = tExcelCustomTemplateService.selectTExcelCustomTemplateList(tExcelCustomTemplate);
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("data", list);
        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 导出半托数据商品模板列表
     */
//    @PreAuthorize("@ss.hasPermi('system:template:export')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TExcelCustomTemplate tExcelCustomTemplate) {
        List<TExcelCustomTemplate> list = tExcelCustomTemplateService.selectTExcelCustomTemplateList(tExcelCustomTemplate);
        ExcelUtil<TExcelCustomTemplate> util = new ExcelUtil<TExcelCustomTemplate>(TExcelCustomTemplate.class);
        util.exportExcel(response, list, "半托数据商品模板数据");
    }

    /**
     * 获取半托数据商品模板详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(tExcelCustomTemplateService.selectTExcelCustomTemplateById(id));
    }

    /**
     * 对自定义模板进行详情查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/selectdetails/{id}")
    public AjaxResult selectTExcelCustomTemplateDetails(@PathVariable("id") Integer id) {
        Map<String, Object> stringObjectMap = tExcelCustomTemplateService.selectTExcelCustomTemplateDetails(id);
        return success(stringObjectMap);
    }

    /**
     * 新增半托数据商品模板
     */
//    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TExcelCustomTemplate tExcelCustomTemplate) {

        return toAjax(tExcelCustomTemplateService.insertTExcelCustomTemplate(tExcelCustomTemplate));
    }

    /**
     * 自定义模板新增
     *
     * @param tTemplateInformationVO
     * @return
     */
    @PostMapping("/added")
    public AjaxResult added(@RequestBody TTemplateInformationVO tTemplateInformationVO) {
        String id = generate();
        String templateId = "t_" + id;
        if (tTemplateInformationVO.getTemplateType() == 1) {
            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getOverall()) {
                TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
                tExcelTemplateTitleMaps.setSign("overall");
                String head = tTemplateInfromationDataVO.getHead();
                String name = tTemplateInfromationDataVO.getName();

                String remark = tTemplateInfromationDataVO.getRemark();
                String value = tTemplateInfromationDataVO.getValue();
                String type = tTemplateInfromationDataVO.getType();
//            tExcelTemplateTitleMaps.setMainCategory(head);
                tExcelTemplateTitleMaps.setChildCategory(name);
//            tExcelTemplateTitleMaps.setRemark(remark);
                List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
                if (options.size() > 0) {
                    String string = options.toString();
                    if (string.startsWith("[") && string.endsWith("]")) {
                        string = string.substring(1, string.length() - 1);
                    }
                    tExcelTemplateTitleMaps.setOptions(string);
                } else {
                    tExcelTemplateTitleMaps.setOptions("");
                }
                TExcelTemplateTitleMaps tExcelTemplateTitleMaps1 = new TExcelTemplateTitleMaps();
                tExcelTemplateTitleMaps1.setSign("overall");
                tExcelTemplateTitleMaps1.setChildCategory(name);
                TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                List<TExcelTemplateTitleMaps> tExcelTemplateTitleMapsList = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps1);
                for (TExcelTemplateTitleMaps excelTemplateTitleMaps : tExcelTemplateTitleMapsList) {
                    tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(excelTemplateTitleMaps.getId()));
                }
                tExcelCustomValue.setColumnType(type);
                tExcelCustomValue.setColumnValue(value);
                tExcelCustomValue.setTExcelCustomTemplateId(templateId);
                itExcelCustomValueService.insertTExcelCustomValue(tExcelCustomValue);
            }

            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getBaseValues()) {
                String value = tTemplateInfromationDataVO.getValue();
                String type = tTemplateInfromationDataVO.getType();
                String name = tTemplateInfromationDataVO.getName();
                Long column = tTemplateInfromationDataVO.getColumn();
                TExcelTemplateTitle tExcelTemplateTitle = new TExcelTemplateTitle();
                TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                tExcelTemplateTitle.setName(name);
                TExcelTemplateTitleMaps tExcelTemplateTitleMaps1 = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsByTitleId(column);
                Integer TitleMapsId = tExcelTemplateTitleMaps1.getId();
                tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(TitleMapsId));
                tExcelCustomValue.setColumnType(type);
                tExcelCustomValue.setColumnValue(value);
                tExcelCustomValue.setTExcelCustomTemplateId(templateId);
                List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
                if (options.size() > 0) {
                    String string = options.toString();
                    if (string.startsWith("[") && string.endsWith("]")) {
                        string = string.substring(1, string.length() - 1);
                    }
                    tExcelCustomValue.setOptions(string);
                } else {
                    tExcelCustomValue.setOptions("");
                }
                itExcelCustomValueService.insertTExcelCustomValue(tExcelCustomValue);
            }
            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSpuValues()) {
                String value = tTemplateInfromationDataVO.getValue();
                String type = tTemplateInfromationDataVO.getType();
                String name = tTemplateInfromationDataVO.getName();
                Long column = tTemplateInfromationDataVO.getColumn();
                TExcelTemplateTitle tExcelTemplateTitle = new TExcelTemplateTitle();
                TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                tExcelTemplateTitle.setName(name);
                TExcelTemplateTitleMaps tExcelTemplateTitleMaps1 = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsByTitleId(column);
                Integer TitleMapsId = tExcelTemplateTitleMaps1.getId();
                tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(TitleMapsId));
                tExcelCustomValue.setColumnType(type);
                tExcelCustomValue.setColumnValue(value);
                tExcelCustomValue.setTExcelCustomTemplateId(templateId);
                List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
                if (options.size() > 0) {
                    String string = options.toString();
                    if (string.startsWith("[") && string.endsWith("]")) {
                        string = string.substring(1, string.length() - 1);
                    }
                    tExcelCustomValue.setOptions(string);
                } else {
                    tExcelCustomValue.setOptions("");
                }
                itExcelCustomValueService.insertTExcelCustomValue(tExcelCustomValue);
            }
            for (List<TTemplateInfromationDataVO> tTemplateInfromationDataVOs : tTemplateInformationVO.getSkuValues()) {
                for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInfromationDataVOs) {
                    String value = tTemplateInfromationDataVO.getValue();
                    String type = tTemplateInfromationDataVO.getType();
//                String name = tTemplateInfromationDataVO.getName();
                    Long column = tTemplateInfromationDataVO.getColumn();
                    TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                    TExcelTemplateTitleMaps tExcelTemplateTitleMaps1 = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsByTitleId(column);
                    Integer TitleMapsId = tExcelTemplateTitleMaps1.getId();
                    tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(TitleMapsId));
                    tExcelCustomValue.setColumnType(type);
                    tExcelCustomValue.setColumnValue(value);
                    tExcelCustomValue.setTExcelCustomTemplateId(templateId);
                    tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                    List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
                    if (options.size() > 0) {
                        String string = options.toString();
                        if (string.startsWith("[") && string.endsWith("]")) {
                            string = string.substring(1, string.length() - 1);
                        }
                        tExcelCustomValue.setOptions(string);
                    } else {
                        tExcelCustomValue.setOptions("");
                    }
                    itExcelCustomValueService.insertTExcelCustomValue(tExcelCustomValue);
                }
            }
        } else {
            for (List<TTemplateInfromationDataVO> tTemplateInfromationDataVOs : tTemplateInformationVO.getSkuValues()) {
                for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInfromationDataVOs) {
                    String value = tTemplateInfromationDataVO.getValue();
                    String type = tTemplateInfromationDataVO.getType();
//                String name = tTemplateInfromationDataVO.getName();
                    Long column = tTemplateInfromationDataVO.getColumn();
                    TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
                    TExcelTemplateTitleMaps tExcelTemplateTitleMaps1 = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsByTitleId(column);
                    Integer TitleMapsId = tExcelTemplateTitleMaps1.getId();
                    tExcelCustomValue.setTExcelTemplateTitleMapsId(Long.valueOf(TitleMapsId));
                    tExcelCustomValue.setColumnType(type);
                    tExcelCustomValue.setColumnValue(value);
                    tExcelCustomValue.setTExcelCustomTemplateId(templateId);
                    tExcelCustomValue.setIndex(tTemplateInfromationDataVO.getIndex());
                    List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
                    if (options.size() > 0) {
                        String string = options.toString();
                        if (string.startsWith("[") && string.endsWith("]")) {
                            string = string.substring(1, string.length() - 1);
                        }
                        tExcelCustomValue.setOptions(string);
                    } else {
                        tExcelCustomValue.setOptions("");
                    }
                    itExcelCustomValueService.insertTExcelCustomValue(tExcelCustomValue);
                }
            }
        }

        TExcelCustomTemplate tExcelCustomTemplate = new TExcelCustomTemplate();
        Long currentUserId = SecurityUtils.getUserId();
        TUser user = tUserMapper.selectTUserById(currentUserId);
        tExcelCustomTemplate.setTeamId(user.getTeamId());
        tExcelCustomTemplate.setUserId(currentUserId);
        tExcelCustomTemplate.setTemplateName(tTemplateInformationVO.getName());
        tExcelCustomTemplate.settExcelCustomGroupId(tTemplateInformationVO.getGroupId());
        tExcelCustomTemplate.setTemplateType(tTemplateInformationVO.getTemplateType());
        tExcelCustomTemplate.setExcelUrl(tTemplateInformationVO.getFile());
        tExcelCustomTemplate.setTExcelCustomTemplateId(templateId);
//        tExcelCustomTemplateService.insertTExcelCustomTemplate(tExcelCustomTemplate);
        return AjaxResult.success(tExcelCustomTemplateService.insertTExcelCustomTemplate(tExcelCustomTemplate));
    }

    /**
     * 通过id对自定义模板详细信息进行查询
     *
     * @param id
     * @return
     */
    @PostMapping("/copeexcelcustomtemplate/{id}")
    public AjaxResult copeexcelcustomtemplate(@PathVariable("id") Integer id) {
        return AjaxResult.success(tExcelCustomTemplateService.tExcelCustomTemplateCopeExcelCustomTemplate(id));

    }

    /**
     * 修改自定义模板关联数据信息
     *
     * @param tTemplateInformationVO
     * @return
     */
    @PutMapping("/updatatExceltemplate")
    public AjaxResult updatatExcelCustomTemplate(@RequestBody TTemplateInformationVO tTemplateInformationVO) {
        return success(tExcelCustomTemplateService.updatatExcelCustomTemplate(tTemplateInformationVO));
    }

    /**
     * 修改半托数据商品模板
     */
//    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TExcelCustomTemplate tExcelCustomTemplate) {
        return toAjax(tExcelCustomTemplateService.updateTExcelCustomTemplate(tExcelCustomTemplate));
    }


    /**
     * 删除半托数据商品模板
     */
//    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(tExcelCustomTemplateService.deleteTExcelCustomTemplateByIds(ids));
    }

    /**
     * 上传并解析excel文件，校验excel文件格式是否符合要求
     *
     * @param file
     * @return
     */

    @PostMapping("/uploadexcel")
    public AjaxResult uploadexcel(@RequestParam("file") MultipartFile file, Long templateType) {
        Map<String, Object> maps = tExcelCustomTemplateService.selectTExcelCustomTemplateExcel(file, templateType);
        Map<String, Object> map = aliYunFileService.excelUpload(file);
        Object url = map.get("url");
        maps.put("file", url);
        maps.put("name", file.getOriginalFilename());
        return success(maps);
    }

    @PostMapping("/uploadexcels")
    public AjaxResult uploadexcels(@RequestParam("file") MultipartFile file) {
        Map<String, Object> maps = tExcelCustomTemplateService.excelAnalysis(file);
//        Map<String, Object> map = aliYunFileService.excelUpload(file);
//        Object url = map.get("url");
//        maps.put("file", url);
//        maps.put("name", file.getOriginalFilename());
        return success(maps);
    }

    public static String generate() {
        // 时间部分：12位（年月日时分秒）
        DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(timeFormat);
        // 随机数部分：4位
        int randomNum = ThreadLocalRandom.current().nextInt(0, 10000);
        String randomPart = String.format("%04d", randomNum);

        return timestamp + randomPart; // 组合为16位
    }

    /**
     * 下载文件
     */
    private File downloadFileFromUrl(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        File tempFile = File.createTempFile("excel-", ".tmp");
        try (InputStream is = url.openStream();
             FileOutputStream fos = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
        return tempFile;
    }

    /**
     * 创建工作簿实例
     */
    private Workbook createWorkbook(File file) throws IOException {
        try (InputStream inputStream = new FileInputStream(file)) {
            return WorkbookFactory.create(inputStream);
        }
    }

    /**
     * 删除临时文件
     */
    private void deleteTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                Files.delete(file.toPath());
            } catch (IOException e) {
                // 忽略删除失败的情况
                file.delete();
            }
        }
    }

    /**
     * 将File转换为MultipartFile
     */
    private MultipartFile convertToFile(File file) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return file.getName();
            }

            @Override
            public String getOriginalFilename() {
                return file.getName();
            }

            @Override
            public String getContentType() {
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }

            @Override
            public boolean isEmpty() {
                return file.length() == 0;
            }

            @Override
            public long getSize() {
                return file.length();
            }

            @Override
            public byte[] getBytes() throws IOException {
                try (FileInputStream fis = new FileInputStream(file)) {
                    ByteArrayOutputStream output = new ByteArrayOutputStream();
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        output.write(buffer, 0, bytesRead);
                    }
                    return output.toByteArray();
                }
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(file);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                if (!file.renameTo(dest)) {
                    throw new IOException("无法移动文件到目标位置: " + dest.getAbsolutePath());
                }
            }
        };
    }

    @PostMapping("/addColumn")
    public AjaxResult addHead(@RequestBody TTemplateInformationVO tTemplateInformationVO) {
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getBase()) {
            TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
            tExcelTemplateTitleMaps.setSign("base");
            tExcelTemplateTitleMaps.setTemplateType(Long.valueOf(1));//默认半托
            Long columnId = tTemplateInfromationDataVO.getColumn();
            String head = tTemplateInfromationDataVO.getHead();
            String name = tTemplateInfromationDataVO.getName();
            List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
            String remark = tTemplateInfromationDataVO.getRemark();
            String value = tTemplateInfromationDataVO.getValue();
            String required = String.valueOf(tTemplateInfromationDataVO.getRequired());
            String type = tTemplateInfromationDataVO.getType();
            tExcelTemplateTitleMaps.setColumnType(type);
            tExcelTemplateTitleMaps.setRequired(required);
            tExcelTemplateTitleMaps.setMainCategory(head);
            tExcelTemplateTitleMaps.setChildCategory(name);
            tExcelTemplateTitleMaps.setRemark(remark);
            if (options.size() > 0) {
                tExcelTemplateTitleMaps.setOptions(options.toString());
            } else {
                tExcelTemplateTitleMaps.setOptions("");
            }
            TExcelTemplateTitle tExcelTemplateTitle = new TExcelTemplateTitle();
            tExcelTemplateTitle.setName(name);
            List<TExcelTemplateTitle> tExcelTemplateTitleList = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleList.size() <= 0) {
                itExcelTemplateTitleService.insertTExcelTemplateTitle(tExcelTemplateTitle);
            }
            List<TExcelTemplateTitle> tExcelTemplateTitleLists = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleLists.size() > 0) {
                for (TExcelTemplateTitle tExcelTemplateTitles : tExcelTemplateTitleLists) {
                    tExcelTemplateTitleMaps.settTemplateTitleId(tExcelTemplateTitles.getId());
                }
            }
            List<TExcelTemplateTitleMaps> tExcelTemplateTitleMapsLists = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
            if (tExcelTemplateTitleMapsLists.size() <= 0) {
                itExcelTemplateTitleMapsService.insertTExcelTemplateTitleMaps(tExcelTemplateTitleMaps);
            }
            ;
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSpu()) {
            TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
            tExcelTemplateTitleMaps.setSign("spu");
            tExcelTemplateTitleMaps.setTemplateType(Long.valueOf(1));//默认半托
            String head = tTemplateInfromationDataVO.getHead();
            String name = tTemplateInfromationDataVO.getName();
            List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
            String remark = tTemplateInfromationDataVO.getRemark();
            String required = String.valueOf(tTemplateInfromationDataVO.getRequired());
            String type = tTemplateInfromationDataVO.getType();
            tExcelTemplateTitleMaps.setMainCategory(head);
            tExcelTemplateTitleMaps.setChildCategory(name);
            tExcelTemplateTitleMaps.setRemark(remark);
            tExcelTemplateTitleMaps.setColumnType(type);
            tExcelTemplateTitleMaps.setRequired(required);
            if (options.size() > 0) {
                tExcelTemplateTitleMaps.setOptions(options.toString());
            } else {
                tExcelTemplateTitleMaps.setOptions("");
            }
            TExcelTemplateTitle tExcelTemplateTitle = new TExcelTemplateTitle();
            tExcelTemplateTitle.setName(name);
            List<TExcelTemplateTitle> tExcelTemplateTitleList = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleList.size() <= 0) {
                itExcelTemplateTitleService.insertTExcelTemplateTitle(tExcelTemplateTitle);
            }
            List<TExcelTemplateTitle> tExcelTemplateTitleLists = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleLists.size() > 0) {
                for (TExcelTemplateTitle tExcelTemplateTitles : tExcelTemplateTitleLists) {
                    tExcelTemplateTitleMaps.settTemplateTitleId(tExcelTemplateTitles.getId());
                }
            }
            List<TExcelTemplateTitleMaps> tExcelTemplateTitleMapsLists = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
            if (tExcelTemplateTitleMapsLists.size() <= 0) {
                itExcelTemplateTitleMapsService.insertTExcelTemplateTitleMaps(tExcelTemplateTitleMaps);
            }
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSku()) {
            TExcelTemplateTitleMaps tExcelTemplateTitleMaps = new TExcelTemplateTitleMaps();
            tExcelTemplateTitleMaps.setSign("sku");
            tExcelTemplateTitleMaps.setTemplateType(Long.valueOf(1));//默认半托
            Long columnId = tTemplateInfromationDataVO.getColumn();
            String head = tTemplateInfromationDataVO.getHead();
            String name = tTemplateInfromationDataVO.getName();
            List<String> options = Collections.singletonList(tTemplateInfromationDataVO.getOptions());
            String remark = tTemplateInfromationDataVO.getRemark();
            String value = tTemplateInfromationDataVO.getValue();
            String required = String.valueOf(tTemplateInfromationDataVO.getRequired());
            String type = tTemplateInfromationDataVO.getType();
            tExcelTemplateTitleMaps.setColumnType(type);
            tExcelTemplateTitleMaps.setRequired(required);
            tExcelTemplateTitleMaps.setMainCategory(head);
            tExcelTemplateTitleMaps.setChildCategory(name);
            tExcelTemplateTitleMaps.setRemark(remark);
            if (options.size() > 0) {
                tExcelTemplateTitleMaps.setOptions(options.toString());
            } else {
                tExcelTemplateTitleMaps.setOptions("");
            }
            TExcelTemplateTitle tExcelTemplateTitle = new TExcelTemplateTitle();
            TExcelCustomValue tExcelCustomValue = new TExcelCustomValue();
            tExcelTemplateTitle.setName(name);
            List<TExcelTemplateTitle> tExcelTemplateTitleList = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleList.size() <= 0) {
                itExcelTemplateTitleService.insertTExcelTemplateTitle(tExcelTemplateTitle);
            }
            List<TExcelTemplateTitle> tExcelTemplateTitleLists = itExcelTemplateTitleService.selectTExcelTemplateTitleList(tExcelTemplateTitle);
            if (tExcelTemplateTitleLists.size() > 0) {
                for (TExcelTemplateTitle tExcelTemplateTitles : tExcelTemplateTitleLists) {
                    tExcelTemplateTitleMaps.settTemplateTitleId(tExcelTemplateTitles.getId());
                }
            }
            List<TExcelTemplateTitleMaps> tExcelTemplateTitleMapsLists = itExcelTemplateTitleMapsService.selectTExcelTemplateTitleMapsList(tExcelTemplateTitleMaps);
            if (tExcelTemplateTitleMapsLists.size() <= 0) {
                itExcelTemplateTitleMapsService.insertTExcelTemplateTitleMaps(tExcelTemplateTitleMaps);
            }

        }
        return success();
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId() {
        UserTeamInfoDTO currentUserTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (currentUserTeamInfo == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUserTeamInfo.getCurrentMode());

        Long teamId = currentUserTeamInfo.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 通过模版导出excel
     *
     * @param tExcelCustomTemplateDTO
     * @param response
     */
    @PostMapping("/exportexcel1")
    public void exportexcel(@RequestBody TExcelCustomTemplateDTO tExcelCustomTemplateDTO, HttpServletResponse response) {
        int ids = 0;
        for (TExcelCustomTemplateDTO dto : tExcelCustomTemplateDTO.getTExcelCustomTemplateList()) {
            ids = dto.getId();
        }
        int total = 0;
        TExcelCustomTemplate tExcelCustomTemplate1 = tExcelCustomTemplateService.selectTExcelCustomTemplateById(ids);
        if (tExcelCustomTemplateDTO.getTemplateType() == 1) {
            // 初始化行号（从第6行开始写入数据）
            int currentRow = 5; // Excel 行号从0开始，第6行索引为5
            int startWriteRow = currentRow;
            int lastInsertedRow = startWriteRow - 1;
            int rowsAdded = 0;
            int totalRows = startWriteRow + rowsAdded;

            try {
                // 1. 下载并读取Excel文件
                File tempFile = downloadFileFromUrl(tExcelCustomTemplate1.getExcelUrl());
                Workbook workbook = createWorkbook(tempFile);
                Sheet sheet = workbook.getSheet("模版");
                if (sheet == null) {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "未找到名为'模版'的工作表");
                    return;
                }
                // 2. 清空模板数据（从第6行开始）
                clearSheetData(sheet, 5);
                // 3. 获取模板的固定行（主分类行和子分类行）
                Row mainCategoryRow = sheet.getRow(2); // 第3行是主分类
                Row childCategoryRow = sheet.getRow(3); // 第4行是子分类
                List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

                // 4. 遍历每个模板数据
                for (TExcelCustomTemplateDTO tExcelCustomTemplate : tExcelCustomTemplateDTO.getTExcelCustomTemplateList()) {
                    tExcelCustomTemplate.setExportType(tExcelCustomTemplateDTO.getExportType());
                    tExcelCustomTemplate.setDoesmatch(tExcelCustomTemplateDTO.getDoesmatch());
                    String generate = generate();
                    Integer id = tExcelCustomTemplate.getId();
                    TExcelCustomTemplate tExcelCustomTemplates = tExcelCustomTemplateService.selectTExcelCustomTemplateById(id);
                    String tExcelCustomTemplateId = tExcelCustomTemplates.getTExcelCustomTemplateId();

                    // 获取当前模板的所有数据
                    List<TExcelCustomTemplateVO> dataList = iTExcelCustomValueService.selectTExcelCustomValueListSign(tExcelCustomTemplateId);
                    List<TExcelCustomTemplateVO> sizeList = dataList.stream()
                            .filter(vo -> "尺码".equals(vo.getChildCategory()))
                            .collect(Collectors.toList());
                    // 1. 处理没有index的数据（这些数据会写入固定行）
                    processNonIndexedData(sheet, tExcelCustomTemplate, dataList, currentRow);
                    // 2. 处理有index的数据（这些数据会追加写入）
                    rowsAdded = processIndexedData(sheet, dataList, currentRow + 1, generate, sizeList, tExcelCustomTemplate); // +1 跳过固定行
                    // 确定需要填充的行范围：从startWriteRow到lastInsertedRow
                    // 定义需要特殊处理的列名
                    Set<String> specialColumns = new HashSet<>(Arrays.asList(
                            "商品层级", "SPU货号", "商品名称", "英文名称", "商品产地", "产地省份"
                    ));
                    // 获取所有需要填充的特殊列数据
                    List<TExcelCustomTemplateVO> specialData = dataList.stream()
                            .filter(item -> item.getIndex() == null && specialColumns.contains(item.getChildCategory()))
                            .collect(Collectors.toList());

                    if (specialData.isEmpty()) {
                        System.out.println("没有找到需要特殊处理的列数据");
                        return;
                    }
                    if (total != 0) {
                        totalRows = totalRows + rowsAdded + 1;
                    }
                    // 遍历每一行
                    for (int rowNum1 = totalRows; rowNum1 <= currentRow + rowsAdded; rowNum1++) {
                        Row targetRow = sheet.getRow(rowNum1);
                        if (targetRow == null) {
                            targetRow = sheet.createRow(rowNum1);
                        }
                        // 遍历每个特殊列数据
                        for (TExcelCustomTemplateVO data : specialData) {
                            String childCategory = data.getChildCategory();  // 直接使用子分类名称
                            String columnValue = data.getColumnValue();      // 要写入的值
                            if (childCategory.equals("商品名称")) {
                                columnValue = tExcelCustomTemplate.getEnname();
                            } else if (childCategory.equals("英文名称")) {
                                columnValue = tExcelCustomTemplate.getEnname();
                            } else if (childCategory.equals("商品层级")) {
                                if (startWriteRow == rowNum1 || rowNum1 == totalRows) {
                                    columnValue = "spu";
                                } else {
                                    columnValue = "sku";
                                }
                            } else if (childCategory.equals("SPU货号")) {
                                columnValue = generate;
                            } else if (childCategory.equals("产地省份")) {
                                columnValue = data.getColumnValue();
                            }
                            // 遍历子分类行，查找匹配的列
                            for (int col = 0; col < childCategoryRow.getLastCellNum(); col++) {
                                Cell childCell = childCategoryRow.getCell(col);
                                if (childCell == null) {
                                    continue;  // 跳过空单元格
                                }
                                String cellValue = getCellValueAsString(childCell).trim();  // 获取单元格值并去除空格
                                if (childCategory.equals(cellValue)) {  // 直接匹配子分类名称
                                    // 找到目标单元格并设置值
                                    Cell targetCell = targetRow.getCell(col);
                                    if (targetCell == null) {
                                        targetCell = targetRow.createCell(col);
                                    }
                                    targetCell.setCellValue(columnValue);
                                    System.out.printf("在行 %d 列 %d 设置了值: %s%n", currentRow
                                            , col, columnValue);
                                    break;  // 找到匹配后跳出循环
                                }
                            }
                        }
                    }
                    // 更新当前行号（固定行占1行，其他数据可能占多行）
                    currentRow += (1 + rowsAdded);
                    total++;
                }
                // 6. 清理多余空白行
                clearExtraEmptyRows(sheet, currentRow);

                // 7. 保存并输出Excel
                String newFileName = "updated_template_" + System.currentTimeMillis() + ".xlsx";
                File newFile = new File(tempFile.getParent(), newFileName);
                try (OutputStream fos = new FileOutputStream(newFile)) {
                    workbook.write(fos);
                }

                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode(newFileName, "UTF-8"));

                try (InputStream is = new FileInputStream(newFile);
                     OutputStream os = response.getOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = is.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                }
                String path = newFile.getPath();
                // 8. 清理临时文件
                deleteTempFile(tempFile);
                deleteTempFile(newFile);

            } catch (Exception e) {
                log.error("Excel文件导出失败：{}", e.getMessage(), e);
                try {
                    response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Excel文件导出失败: " + e.getMessage());
                } catch (IOException ex) {
                    log.error("发送错误信息失败：{}", ex.getMessage(), ex);
                }
            }

        } else {
            // 初始化行号（从第6行开始写入数据）
            int currentRow = 6; // Excel 行号从0开始，第6行索引为5
            int startWriteRow = currentRow;
            int lastInsertedRow = startWriteRow - 1;
            int rowsAdded = 0;
            int totalRows = startWriteRow + rowsAdded;
            try {
                // 1. 下载并读取Excel文件
                File tempFile = downloadFileFromUrl(tExcelCustomTemplate1.getExcelUrl());
                Workbook workbook = createWorkbook(tempFile);
                Sheet sheet = workbook.getSheet("Template");
                if (sheet == null) {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "未找到名为'模版'的工作表");
                    return;
                }
                // 2. 清空模板数据（从第6行开始）
                clearSheetData(sheet, 6);
                Row mainCategoryRow = sheet.getRow(2); // 第3行是列名

                // 4. 遍历每个模板数据
                for (TExcelCustomTemplateDTO tExcelCustomTemplate : tExcelCustomTemplateDTO.getTExcelCustomTemplateList()) {
                    tExcelCustomTemplate.setExportType(tExcelCustomTemplateDTO.getExportType());
                    tExcelCustomTemplate.setDoesmatch(tExcelCustomTemplateDTO.getDoesmatch());
                    String generate = generate();
                    Integer id = tExcelCustomTemplate.getId();
                    TExcelCustomTemplate tExcelCustomTemplates = tExcelCustomTemplateService.selectTExcelCustomTemplateById(id);
                    String tExcelCustomTemplateId = tExcelCustomTemplates.getTExcelCustomTemplateId();

                    // 获取当前模板的所有数据
                    List<TExcelCustomTemplateVO> dataList = iTExcelCustomValueService.selectTExcelCustomValueListSign(tExcelCustomTemplateId);
                    rowsAdded = processIndexedDataTk(sheet, dataList, currentRow, generate, tExcelCustomTemplate); // +1 跳过固定行
                    if (total != 0) {
                        totalRows = totalRows + rowsAdded ;
                    }
                    // 更新当前行号（固定行占1行，其他数据可能占多行）
                    currentRow += ( rowsAdded);
                    total++;
                }
                    // 6. 清理多余空白行
                    clearExtraEmptyRows(sheet, currentRow);

                    // 7. 保存并输出Excel
                    String newFileName = "updated_template_" + System.currentTimeMillis() + ".xlsx";
                    File newFile = new File(tempFile.getParent(), newFileName);
                    try (OutputStream fos = new FileOutputStream(newFile)) {
                        workbook.write(fos);
                    }

                    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode(newFileName, "UTF-8"));

                    try (InputStream is = new FileInputStream(newFile);
                         OutputStream os = response.getOutputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = is.read(buffer)) != -1) {
                            os.write(buffer, 0, bytesRead);
                        }
                    }
                    String path = newFile.getPath();
                    // 8. 清理临时文件
                    deleteTempFile(tempFile);
                    deleteTempFile(newFile);

            } catch (Exception e) {
                log.error("Excel文件下载或读取失败：{}", e.getMessage(), e);
                try {
                    response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Excel文件下载或读取失败: " + e.getMessage());
                } catch (IOException ex) {
                    log.error("发送错误信息失败：{}", ex.getMessage(), ex);
                }
            }
        }

    }

    /**
     * 处理没有index的数据（写入固定行）
     * 规则：必须同时匹配到第三行(mainCategory)与第四行(childCategory)才写入；
     * 第三行为合并单元格时，需在其合并列范围内查找第四行的子分类列。
     */
    private void processNonIndexedData(Sheet sheet, TExcelCustomTemplateDTO template,
                                       List<TExcelCustomTemplateVO> dataList, int rowNum) {
        if (sheet == null || dataList == null || dataList.isEmpty()) return;
        Row mainCategoryRow = sheet.getRow(2); // 第3行
        Row childCategoryRow = sheet.getRow(3); // 第4行
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        for (TExcelCustomTemplateVO excelCustomValue : dataList) {
            String mainCategory = excelCustomValue.getMainCategory();
            String childCategory = excelCustomValue.getChildCategory();
            String columnValue = excelCustomValue.getColumnValue();
            Long index = excelCustomValue.getIndex();
            if (index != null) continue; // 仅处理无 index 的数据
            if (mainCategoryRow == null || childCategoryRow == null) continue;

            // 1) 在第三行定位 mainCategory 所在列区间（处理合并单元格）
            int matchedFirstCol = -1, matchedLastCol = -1;
            // 先从合并区域里找
            for (CellRangeAddress region : mergedRegions) {
                if (region.containsRow(2)) { // 第3行索引为2
                    int firstCol = region.getFirstColumn();
                    Row topRow = sheet.getRow(region.getFirstRow());
                    Cell topCell = (topRow != null ? topRow.getCell(firstCol) : null);
                    String val = getCellValueAsString(topCell);
                    if (mainCategory != null && mainCategory.equals(val)) {
                        matchedFirstCol = region.getFirstColumn();
                        matchedLastCol = region.getLastColumn();
                        break;
                    }
                }
            }
            // 若未命中合并区域，则退化为按单列匹配
            if (matchedFirstCol == -1 && mainCategoryRow != null) {
                for (int col = 0; col < mainCategoryRow.getLastCellNum(); col++) {
                    Cell cell = mainCategoryRow.getCell(col);
                    String v = getCellValueAsString(cell);
                    if (mainCategory != null && mainCategory.equals(v)) {
                        matchedFirstCol = col;
                        matchedLastCol = col;
                        break;
                    }
                }
            }
            if (matchedFirstCol == -1) continue; // 未找到主分类列范围

            // 2) 在第四行于上述列范围内查找 childCategory
            for (int col = matchedFirstCol; col <= matchedLastCol; col++) {
                Cell childCell = childCategoryRow.getCell(col);
                String childVal = getCellValueAsString(childCell);
                if (headerMatches(childCategory, childVal)) {
                    Row targetRow = sheet.getRow(rowNum);
                    if (targetRow == null) targetRow = sheet.createRow(rowNum);
                    Cell targetCell = targetRow.getCell(col);
                    if (targetCell == null) targetCell = targetRow.createCell(col);
                    targetCell.setCellValue(columnValue);
                    break; // 本条数据已写入，继续处理下一条


                }
            }
        }
    }

    // 子分类匹配规则：
    // - 完全相等
    // - 当传入 childCategory 为 "面料" 时，允许匹配 "面料" 与 "面料弹性"
    private boolean headerMatches(String wanted, String header) {
        if (wanted == null || header == null) return false;
        if (wanted.equals(header)) return true;
        if ("面料".equals(wanted)) {
            return "面料".equals(header) || "面料弹性".equals(header);
        }
        return false;
    }

    /**
     * 处理有index的数据（追加写入）
     * 返回添加的行数
     */
    private int processIndexedData(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int startRowNum, String generate, List<TExcelCustomTemplateVO> sizeList, TExcelCustomTemplateDTO tExcelCustomTemplateDTO) {
        // 按index分组
        int startRowNums = startRowNum - 1;

        String[] whiteUrlList = {"https://a1.x914.com/xjmhjh/i/2025/06/23/beimianbai.jpg", "https://a1.x914.com/xjmhjh/i/2025/06/23/caizhibai.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/chefengbai.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/xiukoubai.png"};
        String[] blackUrlList = {"https://a1.x914.com/xjmhjh/i/2025/06/23/Beimian-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/chefeng-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/mianliao-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/xiukou-1.png"};
        List<TExcelCustomTemplateVO> colorList = dataList.stream()
                .filter(vo -> "色值(主规格)".equals(vo.getChildCategory()))
                .collect(Collectors.toList());
        String colorValue = "";
        if (tExcelCustomTemplateDTO.getDoesmatch() == 1) {
            //通过类型判断是那个任务1、数据采集2、侵权检测3、标题提取
            if (tExcelCustomTemplateDTO.getExportType() ==1) {
                ProductInfo productInfo = iProductInfoService.selectProductInfoById(tExcelCustomTemplateDTO.getDataId());
                String imageColor = productInfo.getImageColor();
                if (imageColor != null && !imageColor.isEmpty()) {
                    colorValue = imageColor;
                }
//                else{
//                    colorValue = !colorList.isEmpty() ? colorList.get(0).getColumnValue() : "";
//                }
            } else if (tExcelCustomTemplateDTO.getExportType() ==2) {
                RiskDetectionTaskDetail riskDetectionTaskDetail = iRiskDetectionTaskDetailService.selectRiskDetectionTaskDetailById(tExcelCustomTemplateDTO.getDataId());
                if (riskDetectionTaskDetail.getType()==2) {
                    ProductInfo productInfo = iProductInfoService.selectProductInfoById(riskDetectionTaskDetail.getTypeId());
                    String imageColor = productInfo.getImageColor();
                    if (imageColor != null && !imageColor.isEmpty()) {
                        colorValue = imageColor;
                    }
                }
            } else if (tExcelCustomTemplateDTO.getExportType() ==3) {
                TitleExtractionTaskDetail titleExtractionTaskDetail = iTitleExtractionTaskDetailService.selectTitleExtractionTaskDetailById(tExcelCustomTemplateDTO.getDataId());
                if (titleExtractionTaskDetail.getType() ==2) {
                    ProductInfo productInfo = iProductInfoService.selectProductInfoById(titleExtractionTaskDetail.getTypeId());
                    String imageColor = productInfo.getImageColor();
                    if (imageColor != null && !imageColor.isEmpty()) {
                        colorValue = imageColor;
                    }
                }
            }
        } else {
            colorValue = !colorList.isEmpty() ? colorList.get(0).getColumnValue() : "";
        }
        sizeList.sort(Comparator.comparing(TExcelCustomTemplateVO::getIndexs));
        Map<Long, List<TExcelCustomTemplateVO>> groupedData = dataList.stream()
                .filter(item -> item.getIndex() != null)
                .collect(Collectors.groupingBy(TExcelCustomTemplateVO::getIndex));

        if (groupedData.isEmpty()) {
            return 0;
        }

        Row mainCategoryRow = sheet.getRow(2);
        Row childCategoryRow = sheet.getRow(3);
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        int maxRowAdded = 0;
        int size = 0;
        for (Map.Entry<Long, List<TExcelCustomTemplateVO>> entry : groupedData.entrySet()) {
            Long index = entry.getKey();
            List<TExcelCustomTemplateVO> groupData = entry.getValue();

            // 计算目标行号（基于startRowNum + index）
            int targetRowNum = startRowNums + index.intValue();

            // 确保目标行存在
            Row targetRow = sheet.getRow(targetRowNum);
            if (targetRow == null) {
                targetRow = sheet.createRow(targetRowNum);
            }

            // 记录最大添加的行号
            if (targetRowNum > (startRowNums + maxRowAdded)) {
                maxRowAdded = targetRowNum - startRowNums;
            }

            // 处理该组所有数据
            for (TExcelCustomTemplateVO data : groupData) {
                int urlList = 0;
                String mainCategory = data.getMainCategory();
                String childCategory = data.getChildCategory();
                String columnValue = data.getColumnValue();

                // 遍历主类别行寻找匹配
                for (int i = 0; i < mainCategoryRow.getLastCellNum(); i++) {
                    Cell mainCell = mainCategoryRow.getCell(i);
                    if (mainCell == null) continue;

                    String cellMainCategory = getCellValueAsString(mainCell);
                    if (!Objects.equals(mainCategory, cellMainCategory)) continue;

                    // 检查合并单元格
                    boolean isMergedCell = false;
                    int firstCol = i, lastCol = i;
                    for (CellRangeAddress region : mergedRegions) {
                        if (region.containsRow(mainCell.getRowIndex()) && region.containsColumn(i)) {
                            isMergedCell = true;
                            firstCol = region.getFirstColumn();
                            lastCol = region.getLastColumn();
                            break;
                        }
                    }

                    // 检查子类别
                    for (int colIndex = firstCol; colIndex <= lastCol; colIndex++) {
                        Cell childCell = childCategoryRow.getCell(colIndex);
                        if (childCell == null) continue;

                        String cellChildCategory = getCellValueAsString(childCell);
                        if (!childCategory.equals(cellChildCategory)) continue;

                        // 写入值
                        Cell targetCell = targetRow.getCell(colIndex);
                        if (targetCell == null) {
                            targetCell = targetRow.createCell(colIndex);
                        }
                        if (childCategory.equals("SKU货号")) {
                            String sizeValue = sizeList.get(size).getColumnValue();
                            targetCell.setCellValue(generate + "-" + sizeValue);
                            size++;
                        } else {
                            targetCell.setCellValue(columnValue);
                        }
                        if (childCategory.equals("色值(主规格)")) {
                            targetCell.setCellValue(colorValue);
                            System.out.println("-------------------------------------------------");
                            System.out.println("-------------------------------------------------");
                            System.out.println("-------------------------------------------------");
                            System.out.println(colorValue);
                        }
                        if (childCategory.equals("商品轮播图1")) {
                            targetCell.setCellValue(tExcelCustomTemplateDTO.getImageUrl());
                        } else if (childCategory.equals("商品轮播图2") || childCategory.equals("商品轮播图3") || childCategory.equals("商品轮播图4") || childCategory.equals("商品轮播图5")) {
                            if (tExcelCustomTemplateDTO.getDoesmatch() == 1) {
                                if (colorValue.equals("白色")) {
                                    targetCell.setCellValue(whiteUrlList[urlList]);
                                    if (urlList < 3) {
                                        urlList++;
                                    }
                                } else {
                                    targetCell.setCellValue(blackUrlList[urlList]);
                                    if (urlList < 3) {
                                        urlList++;
                                    }
                                }
                            } else {
                                targetCell.setCellValue(columnValue);
                            }
                        }
                    }
                }
            }
        }
        return maxRowAdded;
    }

    /**
     * 在目标行中查找与模板行匹配的列，并写入值
     */
    private void writeValueToMatchingCell(Workbook workbook, Row targetRow, Row templateRow,
                                          String templateCellValue, String valueToWrite) {
        for (Cell templateCell : templateRow) {
            if (templateCellValue.equals(templateCell.getStringCellValue())) {
                int columnIndex = templateCell.getColumnIndex();
                Cell targetCell = targetRow.getCell(columnIndex);
                if (targetCell == null) {
                    targetCell = targetRow.createCell(columnIndex);
                }
                // 根据原始单元格类型设置值（避免格式丢失）
                switch (templateCell.getCellType()) {
                    case STRING:
                        targetCell.setCellValue(valueToWrite);
                        break;
                    case NUMERIC:
                        targetCell.setCellValue(Double.parseDouble(valueToWrite));
                        break;
                    case BOOLEAN:
                        targetCell.setCellValue(Boolean.parseBoolean(valueToWrite));
                        break;
                    default:
                        targetCell.setCellValue(valueToWrite);
                }
                // 复制样式（可选）
                CellStyle newStyle = workbook.createCellStyle();
                newStyle.cloneStyleFrom(templateCell.getCellStyle());
                targetCell.setCellStyle(newStyle);
            }
        }
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                // 处理数字类型，根据需要格式化
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                // 如果是公式，获取计算后的值
                switch (cell.getCachedFormulaResultType()) {
                    case STRING:
                        return cell.getStringCellValue().trim();
                    case NUMERIC:
                        return String.valueOf((long) cell.getNumericCellValue());
                    default:
                        return null;
                }
            case BLANK:
            default:
                return null;
        }
    }

    // 添加清空sheet数据的方法
    private void clearSheetData(Sheet sheet, int startRow) {
        if (sheet == null) {
            return;
        }
        int lastRowNum = sheet.getLastRowNum();
        if (startRow > lastRowNum) {
            return;
        }

        for (int i = startRow; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (Cell cell : row) {
                    if (cell != null) {
                        // 根据单元格类型进行安全清空
                        switch (cell.getCellType()) {
                            case STRING:
                                cell.setCellValue("");
                                break;
                            case NUMERIC:
                                cell.setCellValue(0);
                                break;
                            case BOOLEAN:
                                cell.setCellValue(false);
                                break;
                            case FORMULA:
                                // 公式单元格处理
                                cell.setCellValue("");
                                break;
                            case BLANK:
                            case ERROR:
                            case _NONE:
                            default:
                                cell.setCellType(CellType.BLANK);
                                break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 清理指定行之后的空白行
     *
     * @param sheet    工作表
     * @param startRow 保留数据的最后行号
     */
    private static void clearExtraEmptyRows(Sheet sheet, int startRow) {
        int maxRows = sheet.getWorkbook() instanceof XSSFWorkbook ? 1048576 : 65536;
        int lastRowNum = sheet.getLastRowNum();

        // 逆序删除空白行（从后往前删除更安全）
        for (int i = lastRowNum; i > startRow; i--) {
            Row row = sheet.getRow(i);
            if (isRowEmptyExceptHiddenColumns(sheet, row)) {
                sheet.removeRow(row);
            }
        }
    }

    /**
     * 检测行是否为空行（排除隐藏列）
     *
     * @param sheet 工作表
     * @param row   需要检测的行
     * @return 是否为空行
     */
    private static boolean isRowEmptyExceptHiddenColumns(Sheet sheet, Row row) {
        if (row == null) return true;
        for (int i = 0; i < row.getLastCellNum(); i++) {
            // 只检查非隐藏列
            if (!sheet.isColumnHidden(i)) {
                Cell cell = row.getCell(i, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                if (cell != null) {
                    switch (cell.getCellType()) {
                        case BLANK:
                        case ERROR:
                        case _NONE:
                            continue;
                        case STRING:
                            if (cell.getStringCellValue().trim().isEmpty()) {
                                continue;
                            }
                            return false;
                        default:
                            return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 处理有index的数据（追加写入）
     * 返回添加的行数
     */
    private int processIndexedDataTk(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int startRowNum, String generate, TExcelCustomTemplateDTO tExcelCustomTemplateDTO) {
        // 按index分组
        int startRowNums = startRowNum -1;
        Map<Long, List<TExcelCustomTemplateVO>> groupedData = dataList.stream()
                .filter(item -> item.getIndex() != null)
                .collect(Collectors.groupingBy(TExcelCustomTemplateVO::getIndex));
        if (groupedData.isEmpty()) {
            return 0;
        }
        Row childCategoryRow = sheet.getRow(2);
        int maxRowAdded = 0;
        for (Map.Entry<Long, List<TExcelCustomTemplateVO>> entry : groupedData.entrySet()) {
            Long index = entry.getKey();
            List<TExcelCustomTemplateVO> groupData = entry.getValue();
            // 计算目标行号（基于startRowNum + index）
            int targetRowNum = startRowNums + index.intValue();
            // 确保目标行存在
            Row targetRow = sheet.getRow(targetRowNum);
            if (targetRow == null) {
                targetRow = sheet.createRow(targetRowNum);
            }
            // 记录最大添加的行号
            if (targetRowNum > (startRowNums + maxRowAdded)) {
                maxRowAdded = targetRowNum - startRowNums;
            }
            // 处理该组所有数据
            for (TExcelCustomTemplateVO data : groupData) {
                String childCategory = data.getChildCategory();
                String columnValue = data.getColumnValue();
                // 遍历主类别行寻找匹配
                for (int i = 0; i < childCategoryRow.getLastCellNum(); i++) {
                    Cell mainCell = childCategoryRow.getCell(i);
                    if (mainCell == null) continue;
                    String cellMainCategory = getCellValueAsString(mainCell);
                    if (!Objects.equals(childCategory, cellMainCategory)) continue;
                    Cell targetCell = targetRow.getCell(i);
                    if (targetCell == null) {
                        targetCell = targetRow.createCell(i);
                    }
                    if (childCategory.equals("Seller SKU")) {
                        targetCell.setCellValue(generate);
                    }else if (childCategory.equals("商品主图")) {
                        targetCell.setCellValue(tExcelCustomTemplateDTO.getImageUrl());
                    }else if (childCategory.equals("商品名称")){
                        targetCell.setCellValue(tExcelCustomTemplateDTO.getEnname());
                    }else if (childCategory.equals("主要销售变体图片 1")){
                        targetCell.setCellValue(tExcelCustomTemplateDTO.getImageUrl());
                    }else{
                        targetCell.setCellValue(columnValue);
                    }
                }
            }
        }

        return maxRowAdded;
    }
}
