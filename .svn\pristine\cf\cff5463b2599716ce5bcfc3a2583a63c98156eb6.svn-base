package com.dataxai.web.service.impl;

import com.dataxai.web.controller.ImageController.PageResult;
import com.dataxai.web.domain.MaterialStyleUser;
import com.dataxai.web.mapper.MaterialStyleUserMapper;
import com.dataxai.web.service.MaterialStyleUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MaterialStyleUserServiceImpl implements MaterialStyleUserService {
    @Autowired
    private MaterialStyleUserMapper mapper;

    @Override
    public boolean addFavorite(MaterialStyleUser record) {
        // 检查是否已经收藏过
        if (mapper.checkFavoriteExists(record.getUserId(), record.getMaterialStyleId(), record.getTaskType()) > 0) {
            return false; // 已经收藏过，不重复添加
        }
        return mapper.insert(record) > 0;
    }

    @Override
    public boolean removeFavorite(Integer id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean removeFavoriteByUserAndStyle(Integer userId, Integer styleId, Integer taskType) {
        return mapper.deleteByUserAndStyle(userId, styleId, taskType) > 0;
    }

    @Override
    public PageResult<MaterialStyleUser> getUserFavorites(Integer userId, Integer pageNum, Integer pageSize, Integer taskType) {
        int offset = (pageNum - 1) * pageSize;
        List<MaterialStyleUser> list = mapper.selectByUser(userId, offset, pageSize, taskType);
        int total = mapper.countByUser(userId, taskType);

        PageResult<MaterialStyleUser> result = new PageResult<>();
        result.setList(list);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);

        return result;
    }

    @Override
    public boolean isFavorited(Integer userId, Integer styleId, Integer taskType) {
        return mapper.checkFavoriteExists(userId, styleId, taskType) > 0;
    }
}
