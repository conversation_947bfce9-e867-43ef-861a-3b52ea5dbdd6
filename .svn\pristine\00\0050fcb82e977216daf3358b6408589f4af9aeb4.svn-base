package com.dataxai.web.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebCharacterModelMapper;
import com.dataxai.web.service.IWebCharacterModelService;

/**
 * 预设模板-模特信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class WebCharacterModelServiceImpl implements IWebCharacterModelService
{
    @Autowired
    private WebCharacterModelMapper webCharacterModelMapper;

    /**
     * 查询预设模板-模特信息
     *
     * @param id 预设模板-模特信息主键
     * @return 预设模板-模特信息
     */
    @Override
    public WebCharacterModel selectWebCharacterModelById(Long id)
    {
        return webCharacterModelMapper.selectWebCharacterModelById(id);
    }

    /**
     * 查询预设模板-模特信息列表
     *
     * @param webCharacterModel 预设模板-模特信息
     * @return 预设模板-模特信息
     */
    @Override
    public List<WebCharacterModel> selectWebCharacterModelList(WebCharacterModel webCharacterModel)
    {
        List<CharacterModel> params = webCharacterModel.getParams();
        if (params != null && !params.isEmpty()) {
            List<CharacterModel> characterModelList = params.stream().peek(item -> {
                String key = item.getKey();
                String value = item.getValue();
                if(Constants.GENDER_STR_ZH.equals(key)){
                    webCharacterModel.setGender(value);
                }else if(Constants.AGE_STR_ZH.equals(key)){
                    webCharacterModel.setAge(value);
                }else {
                    webCharacterModel.setSkin(value);
                }
            }).collect(Collectors.toList());
            webCharacterModel.setParams(characterModelList);
        }
        List<WebCharacterModel> webCharacterModelList = webCharacterModelMapper.selectWebCharacterModelList(webCharacterModel);
        webCharacterModelList.stream().peek(item->{

            ArrayList<CharacterModel> list = new ArrayList<>();
            CharacterModel gender = new CharacterModel();
            gender.setKey(Constants.GENDER_STR_ZH);
            gender.setValue(item.getGender());
            list.add(gender);
            CharacterModel age = new CharacterModel();
            age.setKey(Constants.AGE_STR_ZH);
            age.setValue(item.getAge());
            list.add(age);

            CharacterModel skin = new CharacterModel();
            skin.setKey(Constants.SKIN_STR_ZH);
            skin.setValue(item.getSkin());
            list.add(skin);
            item.setParams(list);

        }).collect(Collectors.toList());
        return webCharacterModelList;
    }

    /**
     * 新增预设模板-模特信息
     *
     * @param webCharacterModel 预设模板-模特信息
     * @return 结果
     */
    @Override
    public int insertWebCharacterModel(WebCharacterModel webCharacterModel)
    {
        return webCharacterModelMapper.insertWebCharacterModel(webCharacterModel);
    }

    /**
     * 修改预设模板-模特信息
     *
     * @param webCharacterModel 预设模板-模特信息
     * @return 结果
     */
    @Override
    public int updateWebCharacterModel(WebCharacterModel webCharacterModel)
    {
        return webCharacterModelMapper.updateWebCharacterModel(webCharacterModel);
    }

    /**
     * 批量删除预设模板-模特信息
     *
     * @param ids 需要删除的预设模板-模特信息主键
     * @return 结果
     */
    @Override
    public int deleteWebCharacterModelByIds(Long[] ids)
    {
        return webCharacterModelMapper.deleteWebCharacterModelByIds(ids);
    }

    /**
     * 删除预设模板-模特信息信息
     *
     * @param id 预设模板-模特信息主键
     * @return 结果
     */
    @Override
    public int deleteWebCharacterModelById(Long id)
    {
        return webCharacterModelMapper.deleteWebCharacterModelById(id);
    }

    @Override
    public List<CharacterModelCategory> selectWebCharacterModelCategoryList() {
        List<String> genderList = webCharacterModelMapper.selectGenderList();
        Set<String> set = new HashSet<>();
        for (String s : genderList) {
            set.add(s);
        }
        genderList = new ArrayList<>();
        for (String s : set) {
            genderList.add(s);
        }
        Collections.reverse(genderList);
        List<String> ageList = webCharacterModelMapper.selectAgeList();
        List<String> skinList = webCharacterModelMapper.selectSkinList();
        List<CharacterModelCategory> characterModelCategoryList = new ArrayList<>();
        CharacterModelCategory gender = new CharacterModelCategory();
        gender.setValue(genderList);
        gender.setKey(Constants.GENDER_STR_ZH);
        characterModelCategoryList.add(gender);
        CharacterModelCategory age = new CharacterModelCategory();
        age.setKey(Constants.AGE_STR_ZH);
        age.setValue(ageList);
        characterModelCategoryList.add(age);
        CharacterModelCategory skin = new CharacterModelCategory();
        skin.setKey(Constants.SKIN_STR_ZH);
        skin.setValue(skinList);
        characterModelCategoryList.add(skin);
        return characterModelCategoryList;
    }

    @Override
    public int selectPageNumByCharacterId(WebCharacterModelDTO webCharacterModelDTO) {
        WebCharacterModel webCharacterModel = new WebCharacterModel();
        BeanUtils.copyProperties(webCharacterModelDTO,webCharacterModel);
        webCharacterModel.setPrompt(null);
        webCharacterModel.setIsVaild(1L);
        Long id = webCharacterModel.getId();
        WebCharacterModel webCharacterModelDB = webCharacterModelMapper.selectWebCharacterModelById(id);
        Integer sort = webCharacterModelDB.getSort();
        String gender = webCharacterModel.getGender();
        String age = webCharacterModel.getAge();
        String skin = webCharacterModel.getSkin();
        List<CharacterModel> params = webCharacterModelDTO.getParams();
        if(null != params){
            for (CharacterModel param : params) {
                String key = param.getKey();
                String value = param.getValue();
                if(Constants.AGE_STR_ZH.equals(key)){
                    age = value;
                }else if(Constants.GENDER_STR_ZH.equals(key)){
                    gender = value;
                }else if(Constants.SKIN_STR_ZH.equals(key)){
                    skin = value;
                }
            }
        }

        int totalCountBelow = webCharacterModelMapper.selectTotalCountBelow(sort, gender, age, skin);
        int div = totalCountBelow / webCharacterModelDTO.getPageSize();
        int mode = totalCountBelow % webCharacterModelDTO.getPageSize();
        return div+(mode!=0?1:0);
    }
}
