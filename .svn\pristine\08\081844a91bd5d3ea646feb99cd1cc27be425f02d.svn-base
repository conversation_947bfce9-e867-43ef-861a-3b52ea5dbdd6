package com.dataxai.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class ProductExportVO {
    @ExcelProperty("经营站点")
    private String operatingWebsite;
    @ExcelProperty("发货仓")
    private String warehouseStock;
    @ExcelProperty("类目")
    private String category;
    @ExcelProperty("运费模版")
    private String freightTemplate;
    @ExcelProperty("承诺发货时效")
    private String deliveryTime;
    @ExcelProperty("素材语言")
    private String materialLanguage;
    // 动态数据字段（与Excel第四行表头严格对应）
    @ExcelProperty("商品层级")
    @TableField(exist = false)
    private String productLevel;

    @ExcelProperty("SPU货号")
    @TableField(exist = false)
    private String spuCode;

    @ExcelProperty("商品名称")
    private String productTitle;

    @ExcelProperty("英文名称")
    private String productEnTitle;

    @ExcelProperty("商品产地")
    @TableField(exist = false)
    private String productOrigin;

    @ExcelProperty("产地省份")
    private String productionPlaceProvince;

    @ExcelProperty("材质")
    private String material;

    @ExcelProperty("成分1")
    private String ingredient1;

    @ExcelProperty("成分1成分比例")
    private String ingredient1Ratio;

    @ExcelProperty("成分2")
    private String ingredient2;

    @ExcelProperty("成分2成分比例")
    private String ingredient2Ratio;

    @ExcelProperty("成分3")
    private String ingredient3;

    @ExcelProperty("成分3成分比例")
    private String ingredient3Ratio;

    @ExcelProperty("图案")
    private String pattern;

    @ExcelProperty("细节")
    private String details;

    @ExcelProperty("领型")
    private String collarType;

    @ExcelProperty("风格")
    private String style;

    @ExcelProperty("护理说明")
    private String careInstructions;

    @ExcelProperty("面料")
    private String fabric;

    @ExcelProperty("面料弹性")
    private String fabricElasticity;

    @ExcelProperty("适用人群")
    private String targetAudience;

    @ExcelProperty("季节")
    private String season;

    @ExcelProperty("是否透明")
    private String isTransparent;

    @ExcelProperty("版型")
    private String silhouette;

    @ExcelProperty("织造方式")
    private String weavingMethod;

    @ExcelProperty("印花类型")
    private String printingType;

    @ExcelProperty("面料纹理1")
    private String fabricTexture1;

    @ExcelProperty("面料克重1（g/m²)")
    private String fabricWeight1;

    @ExcelProperty("面料克重1（g/m²)单位")
    private String fabricWeight1Unit;

    @ExcelProperty("里料纹理")
    private String liningTexture;

    @ExcelProperty("里料克重（g/m²)")
    private String liningWeight;

    @ExcelProperty("里料克重（g/m²)单位")
    private String liningWeightUnit;

    @ExcelProperty("里衬成分1")
    private String liningIngredient1;

    @ExcelProperty("里衬成分1成分比例")
    private String liningIngredient1Ratio;

    @ExcelProperty("里衬成分2")
    private String liningIngredient2;

    @ExcelProperty("里衬成分2成分比例")
    private String liningIngredient2Ratio;

    @ExcelProperty("里衬成分3")
    private String liningIngredient3;

    @ExcelProperty("里衬成分3成分比例")
    private String liningIngredient3Ratio;

    @ExcelProperty("里衬成分4")
    private String liningIngredient4;

    @ExcelProperty("里衬成分4成分比例")
    private String liningIngredient4Ratio;

    @ExcelProperty("里衬成分5")
    private String liningIngredient5;

    @ExcelProperty("里衬成分5成分比例")
    private String liningIngredient5Ratio;

    @ExcelProperty("次要材质")
    private String secondaryMaterial;

    @ExcelProperty("袖型")
    private String sleeveType;

    @ExcelProperty("袖长")
    private String sleeveLength;

    @ExcelProperty("长度")
    private String length;

    @ExcelProperty("场合")
    private String occasion;

    @ExcelProperty("场景")
    private String scene;

    @ExcelProperty("廓形")
    private String outline;

    @ExcelProperty("门襟类型")
    private String placketType;

    @ExcelProperty("下摆形状")
    private String hemShape;

    @ExcelProperty("腰带")
    private String waistband;

    @ExcelProperty("功能类型")
    private String functionType;

    @ExcelProperty("胸垫")
    private String chestPad;

    @ExcelProperty("品牌名")
    private String brandName;

    @ExcelProperty("二次工艺")
    private String secondaryProcess;

    @ExcelProperty("面料克重")
    private String fabricWeight;

    @ExcelProperty("面料纹理2")
    private String fabricTexture2;

    @ExcelProperty("面料克重2（g/m²)")
    private String fabricWeight2;

    @ExcelProperty("面料克重2（g/m²)单位")
    private String fabricWeight2Unit;

    @ExcelProperty("款式来源")
    private String designSource;

    @ExcelProperty("SKC货号")
    private String skcNumber;

    @ExcelProperty("SKU货号")
    private String skuNumber;

    @ExcelProperty("色值(主规格)")
    private String colorValue;

    @ExcelProperty("规格类型2")
    private String specType2;

    @ExcelProperty("尺码组别")
    private String sizeGroup;

    @ExcelProperty("尺码类型")
    private String sizeType;

    @ExcelProperty("尺码")
    private String size;

    @ExcelProperty("肩宽")
    private String shoulderWidth;

    @ExcelProperty("胸围全围")
    private String bustCircumference;

    @ExcelProperty("衣长")
    private String garmentLength;

    @ExcelProperty("袖长")
    private String sleeveLengthCm;

    @ExcelProperty("试穿模特")
    private String fittingModel;

    @ExcelProperty("试穿尺码")
    private String fittingSize;

    @ExcelProperty("试穿感受")
    private String fittingExperience;

    @ExcelProperty("参考链接")
    private String referenceLink;

    @ExcelProperty("申报价格-美国站")
    private String declaredPriceUs;

    @ExcelProperty("币种")
    private String currency;

    @ExcelProperty("发货仓1")
    private String warehouse1;

    @ExcelProperty("发货仓1库存")
    private String warehouse1Stock;

    @ExcelProperty("SKU分类")
    private String skuCategory;

    @ExcelProperty("SKU数量")
    private String skuQuantity;

    @ExcelProperty("SKU数量单位")
    private String skuQuantityUnit;

    @ExcelProperty("是否独立包装")
    private String isIndividuallyPackaged;

    @ExcelProperty("制造商建议零售价(USD)")
    private String msrpUsd;

    @ExcelProperty("敏感词属性1")
    private String sensitiveAttribute1;

    @ExcelProperty("敏感词属性2")
    private String sensitiveAttribute2;

    @ExcelProperty("敏感词属性3")
    private String sensitiveAttribute3;

    @ExcelProperty("敏感词属性4")
    private String sensitiveAttribute4;

    @ExcelProperty("敏感词属性5")
    private String sensitiveAttribute5;

    @ExcelProperty("敏感词属性6")
    private String sensitiveAttribute6;

    @ExcelProperty("敏感词属性7")
    private String sensitiveAttribute7;

    @ExcelProperty("液体容量（ml）")
    private String liquidVolumeMl;

    @ExcelProperty("刀具长度(cm)")
    private String bladeLengthCm;

    @ExcelProperty("刀尖角度(度)")
    private String bladeAngleDegrees;

    @ExcelProperty("储电容量（wh）")
    private String batteryCapacityWh;

    @ExcelProperty("最长边（cm）")
    private String longestSideCm;

    @ExcelProperty("次长边（cm）")
    private String middleSideCm;

    @ExcelProperty("最短边（cm）")
    private String shortestSideCm;

    @ExcelProperty("重量（g）")
    private String weightG;

    @ExcelProperty("商品轮播图1")
    private String carouselImage1;

    @ExcelProperty("商品轮播图2")
    private String carouselImage2;

    @ExcelProperty("商品轮播图3")
    private String carouselImage3;

    @ExcelProperty("商品轮播图4")
    private String carouselImage4;

    @ExcelProperty("商品轮播图5")
    private String carouselImage5;

    @ExcelProperty("商品轮播图6")
    private String carouselImage6;

    @ExcelProperty("商品轮播图7")
    private String carouselImage7;

    @ExcelProperty("商品轮播图8")
    private String carouselImage8;

    @ExcelProperty("商品轮播图9")
    private String carouselImage9;

    @ExcelProperty("商品轮播图10")
    private String carouselImage10;

    @ExcelProperty("详情图文-英语")
    private String detailDescriptionEn;

    @ExcelProperty("主图视频")
    private String mainVideo;

    @ExcelProperty("详情视频")
    private String detailVideo;

    private String productImageUrl;

    private String productPrice;


    @ExcelProperty("成分4")
    private String ingredient4;

    @ExcelProperty("成分4成分比例")
    private String ingredient4Ratio;

    @ExcelProperty("成分5")
    private String ingredient5;

    @ExcelProperty("成分5成分比例")
    private String ingredient5Ratio;

}
