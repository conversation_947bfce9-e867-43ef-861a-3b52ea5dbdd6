/**
 * @Author: wuyang
 * @Date: 2024/1/25
 * @Description: ""
 */

// 用户信息
export interface IUserInfo {
	avatar: string
	nickName: string
	phone: string
	userId: string
	residueScore: number
	packageName: string
	password: string
	isPassword?: boolean
	userPackageAndBagVOs?: Array<{
		refuelingBagEndTime: string
		refuelingBagScore: number
		type: number
	}>
	packageId: string
	isDefaultPackage: number
	wxOpenId: string
	bindFail?: number // 为空时表示正常，为 0-手机号已存在，1-绑定失败，微信已绑定其他用户！
	residualIntegral?: number // 过期积分
	expirationTime?: string // 积分过期时间
	tariffEndTime?: string // 当前套餐结束时间
	residueScoreValue?: number // 剩余积分价值
	packageOrRefueligBag?: number // 过期的积分是 套餐还是加油包；1:套餐 2:加油包
	fourKDown?: number // 套餐是否包含了 4k 下载 0不包含 1包含
	equityDescriptionList?: Array<{ content: string; contains: number }>
}

export interface IUploadGuide {
	name: string
	content: string
	id: number
}

export interface IUseGuide {
	useGuideContent: string
	useGuideName: string
	id: number
}
// 企业信息
export interface IBusinessInfo {
	name: string // 公司名称
	recordNumber: string // 备案号
	customVipDesc: string // 定制业务内容
	customVipTitle: string // 定制业务名称
	uploadGuide: IUploadGuide[] // 上传指南
	usedGuide: IUseGuide[] // 使用指南
}
