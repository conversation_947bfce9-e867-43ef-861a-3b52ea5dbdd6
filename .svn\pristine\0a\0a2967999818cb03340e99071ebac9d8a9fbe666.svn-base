
import { useEffect, useState, useMemo } from 'react';
import { <PERSON>lider, Select, Modal, Button, Dropdown, Radio, Checkbox, message, Pagination, Tooltip, Switch, UploadFile, Spin, Input, Tabs, Empty } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { useMemoizedFn } from 'ahooks'
import { CopyOutlined, FileTextOutlined, ScissorOutlined, BgColorsOutlined, ZoomInOutlined, QuestionCircleOutlined, AppstoreOutlined, SafetyOutlined, FontSizeOutlined, RightOutlined, NodeIndexOutlined, ReconciliationOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import {
	getCommonStyleList, addBatch, uploadSynchronization, getOnePage, createByImageUrls, createTitleExtractionTaskByImageUrls, getWorkflowTemplateList, getTemplateGroupList, createWorkflowByUrls,
	getTextToImgStyleList, getTextToImgStyleCategoryList, getFavoriteStyleList, getRecentStyleList, addStyleFavorite, delStyleFavorite,
	getFashIpList, getFashIpCategoryList, getFavoriteIpList, getRecentIpList, addIpFavorite, delIpFavorite
} from '@/api/task'
import { handleCopyText } from '@/utils/copyText'
import { useLocation, useNavigate } from 'react-router-dom';
import './index.css';
import dayjs from 'dayjs';
import { batchZipUrl } from '@/api/common'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { ReactComponent as FollowSvg } from '@/asset/svg/follow.svg'
import { ReactComponent as FollowActiveSvg } from '@/asset/svg/follow-active.svg'

export const TextToImgDetail = () => {
	const navigate = useNavigate();
	const location = useLocation();

	const allNodeList = [
		{
			taskType: 8,
			label: '文生图'
		},
		{
			taskType: 9,
			label: '相似图裂变'
		},
		{
			taskType: 6,
			label: '平铺图-文生图'
		},
		{
			taskType: 7,
			label: '平铺图-图生图'
		},
		{
			taskType: 14,
			label: '图案裁剪'
		},
		{
			taskType: 11,
			label: '图片去背景'
		},
		{
			taskType: 12,
			label: '图片变清晰'
		},
		{
			taskType: 52,
			label: '印花图提取'
		},
		{
			taskType: 17,
			label: '侵权风险过滤'
		},
		{
			taskType: 18,
			label: '标题提取'
		},
	]
	const [userInfo] = useAtomMethod(userinfoService.userInfo)


	const [styleOrIp, setStyleOrIp] = useState('style');

	const { batchId } = location.state || {};

	const [record, setRecord] = useState<any>();
	const [detailTotal, setDetailTotal] = useState(0);
	const [pageLoading, setPageLoading] = useState(false);
	const fetchDetail = (pageNum: number, pageSize: number,) => {
		setPageLoading(true)
		getOnePage(batchId).then((res: any) => {
			setRecord(res)
			setDetailTotal(res.total)
		}).catch(err => {
			message.error(`请求批次详情失败：${err?.data?.msg}`)
		}).finally(() => {
			setPageLoading(false)
		})
	}
	useEffect(() => {
		fetchDetail(1, 50)
	}, []);

	useEffect(() => {
		if (userInfo?.currentMode == 2) {
			fetchGroupListData('')
		}
	}, [userInfo?.currentMode]);

	const [style, setStyle] = useState('');

	const [modal, contextHolder] = Modal.useModal()
	const [messageApi, messageContextHolder] = message.useMessage()
	const [fileList, setFileList] = useState<UploadFile[]>([]) // 上传的图片列表
	const [fileListCsv, setFileListCsv] = useState<File | null>(null);// 上传的csv文件列表
	const [caozuo, setCaozuo] = useState(1) //1-导入表格 2-上传图片
	const [loading, setLoading] = useState(false);

	let listData: any = []
	useEffect(() => {
		record?.data.map((item: any) => {
			listData.push({
				image: [
					{ small: item.taskOrdinalList[0].ordinalImgResultList[0].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[0].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[0].hasUploaded },
					{ small: item.taskOrdinalList[0].ordinalImgResultList[1].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[1].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[1].hasUploaded },
					{ small: item.taskOrdinalList[0].ordinalImgResultList[2].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[2].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[2].hasUploaded },
					{ small: item.taskOrdinalList[0].ordinalImgResultList[3].resSmallImgUrl, taskImage: item.taskOrdinalList[0].ordinalImgResultList[3].resImgUrl, hasUploaded: item.taskOrdinalList[0].ordinalImgResultList[3].hasUploaded },
				],
				keyword: item.taskOrdinalList[0].shortCutDesc
			})
		})
		setImageList(listData)
		if (record?.data[0]) {
			setStyle(JSON.parse(record?.data[0].taskOrdinalList[0].taskParam).style)
		}
	}, [record]);
	const [selectedImages, setSelectedImages] = useState<string[]>([]);
	const [selectedItems, setSelectedItems] = useState<number[]>([]);
	const [previewImage, setPreviewImage] = useState<string>('');
	/* useEffect(() => {
		console.log('selectedImages 更新了:', selectedImages);
	}, [selectedImages]); */
	// 模拟图片数据
	const [imageList, setImageList] = useState([{ image: [], keyword: '', }]);

	const handleSelect = (imageUrl: string) => {
		setSelectedImages(prev => {
			const newSelected = prev.includes(imageUrl)
				? prev.filter(url => url !== imageUrl)
				: [...prev, imageUrl];
			// 根据选择数量自动显示/隐藏操作栏
			setShowBatchActions(newSelected.length > 0);
			return newSelected;
		});
		updateSelectedItemStatus();
	};

	const [showBatchActions, setShowBatchActions] = useState(false);

	const toggleBatchActions = () => {
		setShowBatchActions(!showBatchActions);
		if (!showBatchActions === false) {
			setSelectedImages([]); // 清空已选图片
		}
	};
	//实时更新选择状态
	useEffect(() => {
		setShowBatchActions(selectedImages.length > 0);
	}, [selectedImages]);

	const cancelSelection = () => {
		setSelectedImages([]);
		setShowBatchActions(false); // 取消选择时隐藏操作栏
	};
	const updateSelectedItemStatus = () => {
		imageList.forEach((item, index) => {
			const allImagesSelected = item.image.every(img => selectedImages.includes(img));
			if (allImagesSelected && !selectedItems.includes(index)) {
				setSelectedItems([...selectedItems, index]);
			} else if (!allImagesSelected && selectedItems.includes(index)) {
				setSelectedItems(selectedItems.filter(i => i !== index));
			}
		});
		console.log(selectedItems);

	};



	const items = useMemo(() => {
		const baseItems = [
			{
				key: '8',
				label: '文生图',
				icon: <FileTextOutlined />,
				path: 'textToImg'
			},
			{
				key: '9',
				label: '相似图裂变',
				icon: <CopyOutlined />,
				path: 'similar'
			},
			{
				key: '6',
				label: '平铺图生成',
				icon: <AppstoreOutlined />,
				path: 'continuous'
			},
			{
				key: '14',
				label: '图案裁剪',
				icon: <ScissorOutlined />,
				path: 'cut'
			},
			{
				key: '11',
				label: '图片去背景',
				icon: <BgColorsOutlined />,
				path: 'blockingOut'
			},
			{
				key: '12',
				label: '图片变清晰',
				icon: <ZoomInOutlined />,
				path: 'clear'
			},
			{
				key: '52',
				label: '印花图提取',
				icon: <ReconciliationOutlined />,
				path: 'extract'
			},
			{
				key: '17',
				label: '侵权风险过滤',
				icon: <SafetyOutlined />,
				path: 'filter'
			},
			{
				key: '18',
				label: '标题提取',
				icon: <FontSizeOutlined />,
				path: 'titleExtraction'
			},
		];

		if (userInfo?.currentMode == 2) {
			baseItems.push({
				key: '99',
				label: '工作流',
				icon: <NodeIndexOutlined />,
				path: ''
			});
		}
		return baseItems;
	}, [userInfo?.currentMode]);

	const [taskInfo, setTaskInfo] = useState({
		type: '',
		typeName: '',
		isModalOpen: false,
		path: '',
	});

	const selectTaskType = (key: string) => {
		if (key == '99') {
			showCreateWorkflow()
		} else {
			const action = items.find(item => item.key === key)?.label || '';
			setTaskInfo({
				type: key,
				typeName: action,
				isModalOpen: true,
				path: items.find(item => item.key === key)?.path || '',
			});
			setStyleRadioValue('1'); //初始化风格选项为不选风格并清空当前风格
			setFashIpRadioValue('1'); //初始化动漫Ip选项为不选IP并清空当前IP
			setIsMattingFree(false) // 免抠图生成（默认否）
			setImageScale(key === '9' ? '原图比例' : '1:1') // 图片生成比例（相似图裂变时默认原图比例, 否则1：1）
			setSimilarity(0.5) // 相似图相似度（默认0.5）
			setGenerateQuantity('1') // 放大倍数（默认1倍）
			setOnlyFissionPattern(false) // 是否只生成裂变图（默认否）

			setCurrentStyle({})
			setCurrentFashIP({})
			if (key === '6') {
				fetchCommonStyleData(1, 1, 12);
			}
			// const styleType = key === '8' ? 2 : 1; // 文生图(type=8)用2，平铺图生成(type=6)用1
			// fetchCommonStyleData(styleType, 1, 12);
		}

	};

	// 创建任务按钮loading
	const [creatLoading, setCreatLoading] = useState(false)

	// 标题提取任务配置类型
	const [taskType, setTaskType] = useState<number>(1);

	// 创建任务
	const createTask = async () => {
		const handleSuccess = () => {
			messageApi.success(`创建${taskInfo.typeName}成功`);
			// window.location.href = `/workspace/batchTools/${taskInfo.path}/index`; // 跳转到对应的任务列表页
			setTaskInfo({
				type: '',
				typeName: '',
				path: '',
				isModalOpen: false
			});
			userinfoService.refresh(); // 刷新用户积分
		};
		try {
			let params = {};
			if (taskInfo.type == '6') {
				params = {
					styleId: styleRadioValue == '1' ? '' : currentStyle?.styleId,
					style: styleRadioValue == '1' ? '无风格' : currentStyle?.style,
					stylePrompt: styleRadioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
					imageScale,
				}
			} else if (taskInfo.type == '8') {
				params = {
					isMattingFree: isMattingFree ? 1 : 0,
					imageScale,
				}
			} else if (taskInfo.type == '9') {
				params = {
					similarity,
					imageScale,
					onlyFissionPattern: onlyFissionPattern ? 1 : 0 // 根据开关状态设置
				}
			} else if (taskInfo.type == '12') {
				params = {
					magnification: generateQuantity,
				}
			}
			if (taskInfo.type == '17') {
				if (!isChecked) {
					messageApi.error('请先勾选确认侵权风险过滤功能使用须知')
					return
				}
				setCreatLoading(true)
				const response = await createByImageUrls({ imageUrls: selectedImages.map(item => item) });
				if (response) handleSuccess();
			} else if (taskInfo.type == '18') {
				setCreatLoading(true)
				const response = await createTitleExtractionTaskByImageUrls({
					imageUrls: selectedImages.map(item => item),
					type: taskType
				});
				if (response) handleSuccess();
			} else if (taskInfo.type == '8') {
				setCreatLoading(true)

				const response = await addBatch({
					type: taskInfo.type,
					imageUrls: selectedImages.map(item => item),
					materialStyleId: styleRadioValue == '1' ? '' : currentStyle?.id,
					materialIpId: fashIpRadioValue == '1' ? '' : currentFashIP?.id,
					taskParam: params ? JSON.stringify(params) : ''
				});
				if (response) handleSuccess();

			} else {

				setCreatLoading(true)
				const response = await addBatch({
					type: taskInfo.type,
					imageUrls: selectedImages.map(item => item),
					taskParam: params ? JSON.stringify(params) : ''
				});
				if (response) handleSuccess();
			}

		} catch (err: any) {
			messageApi.error(`创建失败: ${err?.data?.msg || err?.msg}`)
		} finally {
			setCreatLoading(false)
		}
	};
	const generateQuantityOptions: CheckboxGroupProps<string>['options'] = [
		{ label: '1X', value: '1' },
		{ label: '2X', value: '2' },
		{ label: '4X', value: '4' },
	];

	// 放大倍数
	const [generateQuantity, setGenerateQuantity] = useState('1')
	const handleQuantityChange = (e: any) => {
		setGenerateQuantity(e.target.value)
	};
	//是否仅裂变图案
	const [onlyFissionPattern, setOnlyFissionPattern] = useState(false);
	//原图相似度
	const [similarity, setSimilarity] = useState(0.5);
	const onSimilarityChange = (value: number) => {
		setSimilarity(value)
	};
	//生成图片比例
	const [imageScale, setImageScale] = useState(taskInfo.type === '9' ? '原图比例' : '1:1')
	const handleChange = (value: string) => {
		console.log(`selected ${value}`);
		setImageScale(value)
	};
	// 是否选择风格
	const [styleRadioValue, setStyleRadioValue] = useState('1');
	// 是否选择动漫IP
	const [fashIpRadioValue, setFashIpRadioValue] = useState('1');

	const handleStyleRadioChange = (e: any) => {
		setStyleRadioValue(e.target.value)
		if (e.target.value == '1') {
			setCurrentStyle({})
		} else {
			setCurrentStyle(commonStyleList[0])
		}
	}



	// 参考风格列表
	const [commonStyleList, setCommonStyleList] = useState<any[]>([]);
	// 无风格数据详情
	const [noStyleData, setNoStyleData] = useState<null | {
		stylePrompt: string
	}>(null)
	// 参考风格总页数
	const [commonStyleTotal, setCommonStyleTotal] = useState(0)
	// 参考风格当前页
	const [commonStylePage, setCommonStylePage] = useState(1)

	// 当前选中的风格
	const [currentStyle, setCurrentStyle] = useState<any>(null)
	// 当前选中的动漫IP
	const [currentFashIP, setCurrentFashIP] = useState<any>(null)


	//获取参考风格列表
	const fetchCommonStyleData = async (type: number, pagenum: number, pageSize: number) => {
		try {
			const response: any = await getCommonStyleList(type, pagenum, pageSize)
			setCommonStyleList(response.data);
			setNoStyleData(response.default || { stylePrompt: '' });

			if (response.data.length) {
				setCurrentStyle(response.data[0])
			} else {
				setCurrentStyle({
					styleId: '',
					id: '',
					thumbnailImgUrl: '',
					originImgUrl: '',
					style: '',
					stylePrompt: ''
				})
			}

			setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
			setCommonStylePage(pagenum);
		} catch (error) {
			console.error('获取数据时出错：', error);
		}
	};
	// 平铺图参考风格分页
	const commonStylePageChange = (pagenum: any) => {
		const styleType = taskInfo.type === '8' ? 2 : 1; // 文生图(type=8)用2，平铺图生成(type=6)用1
		fetchCommonStyleData(styleType, pagenum, 12)
	}
	// 文生图风格/动漫IP配置切换分页
	const StyleIpPageChange = (pagenum: any) => {
		if (styleOrIp === 'style') {
			if (filterTab == '2') {
				fetchFavoriteStyleData(pagenum, 12)
			} else if (filterTab == '3') {
				fetchRecentStyleData(pagenum, 12)
			} else {
				fetchStyleData(pagenum, 12, activeStyleTab)
			}
		} else {
			if (filterTab == '2') {
				fetchFavoriteIpData(pagenum, 12)
			} else if (filterTab == '3') {
				fetchRecentIpData(pagenum, 12)
			} else {
				fetchFashIpData(pagenum, 12, activeStyleTab)
			}
		}
	}

	//获取风格配置列表
	const fetchStyleData = async (pageNum: number, pageSize: number, categoryId: any) => {
		try {
			const response: any = await getTextToImgStyleList({ pageNum, pageSize, categoryId });
			setCommonStyleList(response.list);

			setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
			if (!currentStyle?.id && response.list?.length > 0) {
				setCurrentStyle(response.list[0]);
			}
		} catch (err: any) {
			messageApi.error(`获取风格配置列表失败: ${err?.data?.msg || err?.msg}`);
		}
	};
	//获取收藏风格列表
	const fetchFavoriteStyleData = (pageNum: number, pageSize: number) => {
		getFavoriteStyleList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);

		})

	};
	//获取用户最近7天使用风格列表
	const fetchRecentStyleData = (pageNum: number, pageSize: number) => {

		getRecentStyleList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);

		})
	};


	const [styleTabs, setStyleTabs] = useState<any[]>([]);
	const [activeStyleTab, setActiveStyleTab] = useState('');

	// 获取风格分类列表
	const fetchStyleCateData = () => {
		getTextToImgStyleCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
			const tabs = res?.list?.map((item: any) => ({
				key: item.id,
				label: item.name,
			})) || [];
			setStyleTabs(tabs);
			setActiveStyleTab(tabs[0]?.key)
			// 请求风格配置列表
			fetchStyleData(1, 12, tabs[0]?.key);
		});
	}

	const [filterTab, setFilterTab] = useState('1');
	const filterTabs = [
		{
			key: '1',
			label: '所有',
		},
		{
			key: '2',
			label: '收藏',
		},
		{
			key: '3',
			label: '最近',
		},
	];

	//获取动漫IP列表
	const fetchFashIpData = async (pageNum: number, pageSize: number, categoryId: any) => {
		try {
			const response: any = await getFashIpList({ pageNum, pageSize, categoryId });
			setCommonStyleList(response.list);
			setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
			if (!currentFashIP?.id && response.list?.length > 0) {
				setCurrentFashIP(response.list[0]);
			}
		} catch (err: any) {
			messageApi.error(`获取动漫IP列表失败: ${err?.data?.msg || err?.msg}`);
		}
	};
	//获取收藏动漫IP列表
	const fetchFavoriteIpData = (pageNum: number, pageSize: number) => {
		getFavoriteIpList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
		})

	};
	//获取用户最近7天使用动漫IP列表
	const fetchRecentIpData = (pageNum: number, pageSize: number) => {
		getRecentIpList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
		})
	};

	// 获取动漫IP分类列表
	const fetchIpCateData = () => {
		getFashIpCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
			const tabs = res?.list?.map((item: any) => ({
				key: item.id,
				label: item.name,
			})) || [];
			setStyleTabs(tabs);
			setActiveStyleTab(tabs[0]?.key)
			// 请求动漫IP配置列表
			fetchFashIpData(1, 12, tabs[0]?.key);
		});
	}

	// 收藏/取消收藏风格或IP
	const handleFollow = (item: any) => {
		const isFavorited = item.isFavorited;
		const isStyle = styleOrIp === 'style';

		// 根据类型和收藏状态确定要调用的API函数和提示信息
		const apiCall = isStyle
			? (isFavorited ? () => delStyleFavorite(item.favoriteId) : () => addStyleFavorite({ materialStyleId: item.id }))
			: (isFavorited ? () => delIpFavorite(item.favoriteId) : () => addIpFavorite({ materialIpId: item.id }));

		const successMessage = isStyle
			? (isFavorited ? '取消收藏风格成功' : '风格收藏成功')
			: (isFavorited ? '取消收藏IP成功' : 'IP收藏成功');

		const errorMessage = isStyle
			? (isFavorited ? '取消收藏风格失败' : '风格收藏失败')
			: (isFavorited ? '取消收藏IP失败' : 'IP收藏失败');

		apiCall()
			.then(() => {
				if (styleOrIp === 'style') {
					if (filterTab == '2') {
						fetchFavoriteStyleData(1, 12)
					} else if (filterTab == '3') {
						fetchRecentStyleData(1, 12)
					} else {
						fetchStyleData(1, 12, activeStyleTab)
					}
				} else {
					if (filterTab == '2') {
						fetchFavoriteIpData(1, 12)
					} else if (filterTab == '3') {
						fetchRecentIpData(1, 12)
					} else {
						fetchFashIpData(1, 12, activeStyleTab)
					}
				}
				messageApi.success(successMessage);
			})
			.catch((err) => {
				messageApi.error(`${errorMessage}: ${err?.data?.msg || err?.msg}`);
			});
	};

	//copy复制提示词
	const handleCopy = (keyword: string) => {
		handleCopyText(keyword, '提示词')
	};
	// 参考风格选择弹窗显影
	const [styleModalOpen, setStyleModalOpen] = useState(false)
	// 点击参考风格反显
	const selectStyle = (item: any) => {

		setCurrentStyle(item)
		setStyleModalOpen(false)
	}

	let commonStyleListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
		return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
			<img onClick={() => selectStyle(item)}
				src={item.originImgUrl || item.originImgUrl || ''}
				className={`${currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px] `}
				style={{ objectFit: 'cover' }}
				alt="" />
			<p style={{ textAlign: 'center' }} >{item.style}</p>
		</div>
	}) : null

	// 风格/动漫IP选择弹窗显影
	const [isModalOpen, setIsModalOpen] = useState(false)

	let commonStyleIPListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
		return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white"
			onClick={() => {
				if (styleOrIp == 'style') {
					setCurrentStyle(item)
				} else {
					setCurrentFashIP(item)
				}
				setIsModalOpen(false)
			}}>
			<div className="relative w-full h-[110px] rounded-lg overflow-hidden group">
				<img
					src={styleOrIp === 'style' ? `${item?.thumbnailImgUrl || item?.styleUrl || ''}` : `${item?.thumbnailImgUrl || item?.ipUrl || ''}`}
					className={`${(styleOrIp === 'style' ? currentStyle?.id : currentFashIP?.id) == item.id ? '!border-primary' : ''} w-full h-full rounded-lg hover:border-primary border-[2px]`}
					style={{ objectFit: 'cover' }}
					alt=""
				/>
				{/* 悬浮遮罩层 */}
				<div className="absolute inset-0 bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
					{/* 收藏 */}
					<div className='cursor-pointer absolute w-[20px] h-[20px] top-2  right-2'
						onClick={(e) => {
							e.stopPropagation(); // 阻止事件冒泡
							handleFollow(item);   // 传递item参数
						}}>
						{item.isFavorited ? <FollowActiveSvg className={'w-[16px]'} fill={'var(--primary-color)'} /> : <FollowSvg className={'w-[16px] svg-hover-white'} />}
					</div>
				</div>
			</div>
			<p style={{ textAlign: 'center' }} >{item.name}</p>
		</div>
	}) : <div className='w-full flex justify-center items-center pt-[100px]'><Empty /></div>


	const changeFilterTab = (key: string) => {
		setFilterTab(key)
		if (styleOrIp === 'style') {
			if (key == '2') {
				fetchFavoriteStyleData(1, 12)
			} else if (key == '3') {
				fetchRecentStyleData(1, 12)
			} else {
				fetchStyleData(1, 12, activeStyleTab)
			}
		} else {
			if (key == '2') {
				fetchFavoriteIpData(1, 12)
			} else if (key == '3') {
				fetchRecentIpData(1, 12)
			} else {
				fetchFashIpData(1, 12, activeStyleTab)
			}
		}
	}



	// 图片列表渲染
	const getTaskImageComponent = useMemoizedFn(
		(image: any) => {
			// 展平所有的image数组获取预览图片列表
			let previewImageList = imageList?.flatMap((item: any) =>
				item.image.map((img: any) => img.taskImage)
			) || [];
			// 计算当前图片在previewImageList中的索引
			const currentIndex = previewImageList.findIndex(url => url === image.taskImage);
			// 计算当前图片的选中状态
			const isCheckedArray = previewImageList?.map((url: string) =>
				selectedImages.includes(url)
			) || [];
			// 处理选中状态变化的回调函数
			const handleCheckChange = (checked: boolean, changedIndex: number) => {
				const imageUrl = previewImageList[changedIndex];
				if (imageUrl) {
					if (checked) {
						setSelectedImages(prev => [...prev, imageUrl]);
					} else {
						setSelectedImages(prev => prev.filter(url => url !== imageUrl));
					}
					setShowBatchActions(checked ? true : selectedImages.length > 0);
				}
			};
			return (
				<div
					className={
						'aspect-square w-full h-full'
					}
				>
					<LikeImage
						type={1}
						imageId={''}
						taskId={''}
						taskOrdinalId={''}
						imgUrl={image.taskImage}
						oriImgUrl={image.taskImage}
						smallImgUrl={image.small}
						markImgUrl={image.taskImage}
						progress={100}
						previewImages={previewImageList}
						index={currentIndex !== -1 ? currentIndex : 0} // 如果找不到则默认0
						seed={-1}
						delVisible={false}
						likeVisible={false}
						downloadVisible={false}
						comparison={false}
						canChecked={true}
						isCheckedArray={isCheckedArray}
						onCheckChange={handleCheckChange}
					/>
				</div>
			)
		}
	)
	const [downloadLoading, setDownloadLoading] = useState(false);
	// 下载图片
	const handleDownloadImgages = () => {
		setDownloadLoading(true);
		batchZipUrl({ imageUrls: selectedImages, type: 8 }).then((res: any) => {
			if (res) {
				window.open(res, '_blank'); // 在新标签页打开下载链接
			} else {
				messageApi.error('获取下载链接失败');
			}
		}).catch(err => {
			messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
		}).finally(() => {
			setDownloadLoading(false);
		});
		// batchDownloadImages(selectedImages);
	};

	const synchronizeItems = [
		{
			key: 1,
			label: '上传设计器',
		},
		{
			key: 2,
			label: '上传设计平台',
		},
	];

	const [synchronizeType, setSynchronizeType] = useState<number>(1);

	const [uploadLoading, setUploadLoading] = useState(false);
	const [tagModalVisible, setTagModalVisible] = useState(false);
	const [tagValue, setTagValue] = useState('');

	// 展示标签输入框
	const showTagModal = () => {
		setTagModalVisible(true);
		setTagValue('');
	};

	// 上传设计器
	const handleUploadDesigner = () => {
		setUploadLoading(true);
		uploadSynchronization({ imageUrls: selectedImages, tags: tagValue, platform: synchronizeType }).then((res: any) => {
			messageApi.success(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}成功`);
			fetchDetail(detailPage, 50)
		}).catch(err => {
			messageApi.error(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}失败: ${err?.data?.msg || err?.msg}, 请重试`);
		}).finally(() => {
			setUploadLoading(false);
			setTagModalVisible(false);
			setTagValue('');
		})
	};

	const [detailPage, setDetailPage] = useState(1);
	const handleDetailPage = (page: number, pageSize: number) => {
		setDetailPage(page);  // 更新页码状态
		setSelectedImages([]); // 清空已选图片
		fetchDetail(page, pageSize)
	}

	// 是否免抠图生成（默认否）
	const [isMattingFree, setIsMattingFree] = useState(false)
	const onSwitchChange = (checked: boolean) => {
		console.log(`switch to ${checked}`);
		setIsMattingFree(checked)
	};

	const [isGuideModalVisible, setIsGuideModalVisible] = useState(false)
	// 是否勾选
	const [isChecked, setIsChecked] = useState(false)
	// 同意侵权风险过滤功能使用须知
	const handleAgree = () => {
		setIsChecked(true)
		setIsGuideModalVisible(false)
	}

	// 创建工作流任务 start

	const [workflowTemplateList, setWorkflowTemplateList] = useState<any[]>([])
	//获取工作流模板列表
	const fetchWorkflowTemplateList = (
		pageNum: number,
		pageSize: number,
		groupId: any,
	) => {
		getWorkflowTemplateList({
			pageNum,
			pageSize,
			groupId: groupId == '0' ? '' : groupId,
		}).then((res: any) => {
			if (res.data) {
				setWorkflowTemplateList(res.data)
			}
		}).catch(err => {
			messageApi.error(`获取工作流模板列表失败:${err?.msg || err?.data?.msg || ''}`)
		})
	}
	const [groups, setGroups] = useState<any[]>([]);

	//获取分组列表
	const fetchGroupListData = (groupName: string) => {
		getTemplateGroupList({ pageNum: 1, pageSize: 99, groupName }).then((res: any) => {
			setGroups([{ id: 0, groupName: "全部" }, ...res.data]);
			fetchWorkflowTemplateList(1, 99, '')
		}).catch(err => {
			messageApi.error(`获取分组列表失败:${err?.msg || err?.data?.msg || ''}`)
		})
	}


	const [activeTab, setActiveTab] = useState({ id: 0, groupName: "全部" });
	const [isModalVisible, setIsModalVisible] = useState(false)
	const goCreat = () => {
		setIsModalVisible(false)
		sessionStorage.setItem('workflowParams', JSON.stringify({ from: 'workflow' }));
		window.location.href = '/workspace/teamTools/workflowTemplate'
	}

	const [currentTemplate, setCurrentTemplate] = useState<any>({});

	const showCreateWorkflow = () => {
		setIsModalVisible(true)
		setCurrentTemplate({})
		setActiveTab(groups[0])
	}
	const [creatWorkflowLoading, setCreatWorkflowLoading] = useState(false)

	// 创建任务
	const handleOk = async () => {
		if (!currentTemplate?.id) {
			messageApi.error('请先选择模板')
			return
		}
		try {
			setCreatWorkflowLoading(true)
			createWorkflowByUrls({ materialUrls: selectedImages.map(item => item), templateId: currentTemplate?.id }).then((data) => {
				messageApi.success('工作流创建成功')
				setIsModalVisible(false)
				userinfoService.refresh() // 刷新用户积分
			}).catch((err) => {
				messageApi.error(`创建失败: ${err?.msg || err?.data?.msg || ''}`)
			}).finally(() => {
				setCreatWorkflowLoading(false)
			})
		} catch (err) {
			messageApi.error('创建失败')
			setCreatWorkflowLoading(false)
		}
	}

	const handleCancel = () => {
		setIsModalVisible(false)
	}

	// 创建工作流 end


	return (
		<div className="h-full w-full p-[20px]">
			{contextHolder} {/* 这里确保 Modal 挂载 */}
			{messageContextHolder} {/* 这里确保 Message 挂载 */}
			{pageLoading ? (
				<div className="flex justify-center items-center h-full">
					<Spin size="large" />
				</div>
			) : (<>
				{/* 标签输入弹窗 start */}
				<Modal
					title={synchronizeType === 1 ? '上传设计器标签' : '上传设计平台标签'}
					open={tagModalVisible}
					onOk={handleUploadDesigner}
					onCancel={() => {
						setTagModalVisible(false);
						setTagValue('');
					}}
					okText="确认上传"
					cancelText="取消"
					confirmLoading={uploadLoading}
					centered
				>
					<Input
						placeholder="请输入标签"
						value={tagValue}
						onChange={(e) => setTagValue(e.target.value)}
					/>
				</Modal>
				{/* 标签输入弹窗 end */}
				{/* 新建工作流任务弹窗 start */}
				<Modal
					title="新建工作流"
					open={isModalVisible}
					onOk={handleOk}
					onCancel={handleCancel}
					width={600}
					centered
					okText="添加"
					confirmLoading={creatWorkflowLoading}
				>
					<p className='text-gray-500 font-bold mt-5'>添加素材<span style={{ color: '#32649f', fontWeight: 'normal', marginLeft: '10px' }}>已选择 {selectedImages?.length} 张图片</span></p>


					<div className='flex justify-between items-center  mt-5 mb-6'>
						<p className='text-gray-500 font-bold'>选择流程</p>
						<p className='text-[#F06A34] cursor-pointer' onClick={goCreat}>去创建工作流</p>
					</div>
					<Tabs
						activeKey={activeTab?.id?.toString()}
						onChange={(key: any) => {
							const selectedGroup = groups.find(g => g.id.toString() === key);
							setActiveTab(selectedGroup);
							// 根据选中的分组ID查询数据
							fetchWorkflowTemplateList(1, 99, key);
						}}
						items={groups.map((group, index) => ({
							label: group?.groupName || '',
							key: group?.id?.toString(),
							children: (
								<>
									{workflowTemplateList.length > 0 ? (
										<div className='bg-[#f5f5f5] p-2 flex flex-wrap gap-2 h-[320px] overflow-y-scroll scrollbar-container scrollbar-hide '>
											{workflowTemplateList.map((item, index) => (
												<div key={index} className={`bg-white w-full  p-4 rounded-lg border  ${currentTemplate?.id && currentTemplate?.id == item?.id ? 'border-[#F06A34]' : 'border-white'}`}
													style={{ height: 'fit-content' }}
													onClick={() => setCurrentTemplate(item)}>
													<p className='text-gray-500 '>{item?.templateName}</p>
													<div className="flex flex-wrap gap-1 min-h-[64px] pt-1">
														{item?.nodeList.map((node: any, inx: number) => (
															<div key={inx} className="flex items-center">
																<span className='border border-primary text-primary pl-2 pr-2 rounded-[4px]'>
																	{node?.taskType ? allNodeList.find(n => n.taskType === node.taskType)?.label : '未知'}
																</span>
																{inx < item?.nodeList.length - 1 && (<RightOutlined className='text-[9px] text-gray-500 ml-1' />)}
															</div>
														))}
													</div>
												</div>))}
										</div>) : <div className='h-[320px] flex justify-center items-center'>
										<Empty description="暂无模板，快去新增吧～" />
									</div>}
								</>
							),
						}))}
					/>
				</Modal>
				{/* 新建工作流任务弹窗 end */}
				{/* 创建任务弹窗 start */}
				<Modal
					title={`新建${taskInfo.typeName}任务`} okText="创建" confirmLoading={creatLoading} open={taskInfo.isModalOpen} width={520} centered onOk={createTask} onCancel={() => setTaskInfo((prev) => ({ ...prev, isModalOpen: false }))}>
					<div className=" min-h-[140px] flex  flex-col items-center justify-center">
						<p>
							将对选中的{' '}
							<span style={{ color: '#32649f' }}>{selectedImages?.length}</span>{' '}
							张图片素材执行{taskInfo.typeName}操作
						</p>
						{(taskInfo.type === '8' || taskInfo.type === '6') && (
							<div style={{ width: '423px' }}>
								{taskInfo.type === '6' && <>
									<div className='flex justify-between items-center mt-8'>风格选择
										<Radio.Group style={{ width: '260px' }} block options={[
											{ label: '不选风格', value: '1' },
											{ label: '风格选择', value: '2' },
										]} value={styleRadioValue} onChange={(e) => handleStyleRadioChange(e)} />
									</div>
									{styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setStyleModalOpen(true)}>
										<img className='w-[80px] h-[80px] mr-[10px]' style={{ objectFit: 'cover' }} src={currentStyle?.originImgUrl || currentStyle?.thumbnailImgUrl || ""} />
										<div className='w-[200px]'>
											<p className='text-[18px] mb-[12px]'>选中风格</p>
											<p>{currentStyle ? currentStyle.style : ''}</p>
										</div>
										<img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
									</div>}
								</>}
								{taskInfo.type === '8' && <>
									<div className='flex justify-between items-center mt-8'>IP角色选择
										<Radio.Group style={{ width: '260px' }} block options={[
											{ label: '不选IP', value: '1' },
											{ label: 'IP选择', value: '2' },
										]} value={fashIpRadioValue} onChange={(e) => {
											setFashIpRadioValue(e.target.value)
											if (e.target.value == '1') {
												setCurrentFashIP({})
											} else {
												// 请求动漫IP分类列表
												fetchIpCateData()
											}
										}} />
									</div>
									{fashIpRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
										onClick={() => {
											setFilterTab('1')
											// 请求动漫IP分类列表
											fetchIpCateData()
											setIsModalOpen(true)
											setStyleOrIp('ip')
										}}>

										<img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentFashIP?.thumbnailImgUrl || currentFashIP?.ipUrl || ""} alt="" />
										<div className='w-[230px]'>
											<p className='text-[18px] mb-[12px]'>选中IP</p>
											<p>{currentFashIP?.name || '动漫IP'}</p>
										</div>
										<img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
									</div>}
									<div className='flex justify-between items-center mt-8'>风格选择
										<Radio.Group style={{ width: '260px' }} block options={[
											{ label: '不选风格', value: '1' },
											{ label: '风格选择', value: '2' },
										]} value={styleRadioValue} onChange={(e) => {
											setStyleRadioValue(e.target.value)
											if (e.target.value == '1') {
												setCurrentStyle({})
											} else {
												// 请求风格分类列表
												fetchStyleCateData()
											}
										}} />
									</div>
									{styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
										onClick={() => {
											setFilterTab('1')
											// 请求风格分类列表
											fetchStyleCateData()
											setIsModalOpen(true)
											setStyleOrIp('style')
										}}>
										<img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentStyle?.thumbnailImgUrl || currentStyle?.styleUrl || ""} alt="" />
										<div className='w-[230px]'>
											<p className='text-[18px] mb-[12px]'>选中风格</p>
											<p>{currentStyle?.name || '自定义风格'}</p>
										</div>
										<img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
									</div>}

									<div className='flex  justify-between items-center  text-normal mt-[20px] mb-[10px]'>
										<p>免抠图生成<Tooltip title="利用AI自动从图片中精准分离出前景对象并去除背景,可以省去用户手动抠图的操作。"><QuestionCircleOutlined className=' ml-[6px]' /></Tooltip></p>
										<Switch checked={isMattingFree} onChange={onSwitchChange} />
									</div>
								</>}
								<div className='flex justify-between items-center  mt-4 mb-8'>
									<p className='w-[100px]'>生成图片比例</p>
									<Select
										defaultValue="1:1"
										style={{ width: 280 }}
										onChange={handleChange}
										value={imageScale}
										options={[
											{ value: '1:1', label: '1 : 1' },
											{ value: '2:3', label: '2 : 3' },
											{ value: '3:2', label: '3 : 2' },
											{ value: '3:4', label: '3 : 4' },
											{ value: '4:3', label: '4 : 3' },
											{ value: '9:16', label: '9 : 16' },
											{ value: '16:9', label: '16 : 9' },
										]}
									/>
								</div>

								{/* 参考风格弹窗 start */}
								<Modal
									width={800}
									title=""
									footer={null}
									open={styleModalOpen}
									onCancel={() => setStyleModalOpen(false)}
								>
									<div>
										<div className="flex flex-wrap mt-[20px] h-[340px]">
											{commonStyleListEL}
										</div>
										<label>
											<ul className="flex justify-center  mt-[20px]">
												{Array.from(
													{ length: commonStyleTotal },
													(_, index) => (
														<li
															className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
															style={{ lineHeight: '18px' }}
															onClick={() => {
																commonStylePageChange(index + 1)
															}}
														>
															{index + 1}
														</li>
													)
												)}
												<li>{'共' + commonStyleTotal + '页'}</li>
											</ul>
										</label>
									</div>
								</Modal>
								{/* 参考风格弹窗 end */}
								{/* 设置风格/IP弹窗 start */}
								<Modal width={800} footer={null} title={styleOrIp == 'style' ? '设置作品风格' : '设置动漫IP'} open={isModalOpen} centered onCancel={() => setIsModalOpen(false)}>
									<div className='h-[452px]  relative'>
										<div className="flex items-center gap-x-4">
											{
												filterTabs.map((item, index) => (
													<p className={`${filterTab == item.key ? 'bg-black text-white' : ''} w-[70px] h-[30px] rounded-full cursor-pointer bg-[#eee] `}
														style={{ lineHeight: '30px', textAlign: 'center' }} onClick={() => changeFilterTab(item.key)}
													>{item.label}</p>
												))
											}
										</div>
										{filterTab == '1' && (<Tabs defaultActiveKey="1" items={styleTabs} activeKey={activeStyleTab}
											onChange={(key: any) => {
												setActiveStyleTab(key);
												// 根据选中的分类ID查询数据
												if (styleOrIp == 'style') {
													fetchStyleData(1, 12, key);
												} else {
													fetchFashIpData(1, 12, key);

												}

											}} />)}

										<div className=' h-[340px]' >
											<div className='flex flex-wrap mt-[20px]'>
												{commonStyleIPListEL}
											</div>
											<label>
												<ul className='flex justify-center mt-[20px] w-full  absolute bottom-[8px] left-0'>
													{Array.from({ length: commonStyleTotal }, (_, index) => (
														<li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
															style={{ lineHeight: '18px' }} onClick={() => { StyleIpPageChange(index + 1) }}>{index + 1}</li>
													))}
													<li>{'共 ' + commonStyleTotal + ' 页'}</li>
												</ul>
											</label>
										</div>
									</div>
								</Modal>
								{/* 设置风格/IP弹窗 end */}
							</div>
						)}
						{taskInfo.type === '9' && (
							<div>
								<div className="flex justify-between items-center mt-4">
									<p className="w-[110px]">原图相似度</p>
									<span>低</span>
									<Slider
										styles={{
											rail: {
												background: '#ddd'
											},
											track: {
												background: 'var(--primary-color)'
											},
											handle: {
												background: 'var(--primary-color)'
											}
										}}
										className="w-[220px]"
										min={0.3}
										max={0.8}
										step={0.05}
										value={similarity}
										onChange={onSimilarityChange}
										tooltip={{ open: false }}
									/>
									<span>高</span>
								</div>
								<div className="flex justify-between items-center  mt-4">
									<p className="w-[110px]">生成图片比例</p>
									<Select
										defaultValue="原图比例"
										style={{ width: 260 }}
										onChange={handleChange}
										value={imageScale}
										options={[
											{ value: '原图比例', label: '原图比例' },
											{ value: '1:1', label: '1 : 1' },
											{ value: '2:3', label: '2 : 3' },
											{ value: '3:2', label: '3 : 2' },
											{ value: '3:4', label: '3 : 4' },
											{ value: '4:3', label: '4 : 3' },
											{ value: '9:16', label: '9 : 16' },
											{ value: '16:9', label: '16 : 9' }
										]}
									/>
								</div>
								<div className='flex justify-between items-center text-normal mt-[20px] mb-[10px]'>
									<p>仅裂变图案</p>
									<Switch
										checked={onlyFissionPattern}
										onChange={(checked) => setOnlyFissionPattern(checked)}
									/>
								</div>
							</div>
						)}
						{taskInfo.type === '12' && (
							<div>
								<p className="w-[360px] flex items-center justify-between text-normal mt-[26px] ">
									放大倍数
									<Radio.Group
										size="large"
										block
										options={generateQuantityOptions}
										onChange={handleQuantityChange}
										defaultValue="1"
										value={generateQuantity}
										optionType="button"
										buttonStyle="solid"
										className="w-[280px]"
									/>
								</p>
							</div>
						)}
						{taskInfo.type === '18' &&
							<div>
								<div className='flex justify-between items-center mt-4'>
									<p className='w-[110px]'>标题模板</p>
									<Select
										defaultValue={1}
										style={{ width: 260 }}
										onChange={(value) => setTaskType(value)}
										value={taskType}
										options={[
											{ value: 1, label: '合肥' },
											{ value: 2, label: '贵阳' }
										]}
									/>
								</div>
							</div>
						}
					</div>
					{taskInfo.type === '17' &&
						<div className="w-full flex items-center">
							<Checkbox
								checked={isChecked}
								onChange={(e) => {
									if (isChecked) {
										setIsChecked(false);
									} else {
										setIsGuideModalVisible(true);
									}
								}}
							/>
							<span className='ml-[10px] cursor-pointer' onClick={() => {
								if (isChecked) {
									setIsChecked(false);
								} else {
									setIsGuideModalVisible(true);
								}
							}}>侵权风险过滤功能使用须知</span>
						</div>
					}
				</Modal>
				{/* 创建任务弹窗 end */}
				{/* 侵权风险过滤功能使用须知弹窗 start */}
				<Modal
					title="侵权风险检测功能使用须知"
					open={isGuideModalVisible}
					onOk={handleAgree}
					onCancel={() => setIsGuideModalVisible(false)}
					width={660}
					centered
					okText="我已知晓，同意"
				>
					<div className='border-[1px] border-normal p-[16px] border-[#999] bg-[#fafafa] mt-4 mb-4' style={{ borderRadius: '8px' }}>
						<p className='mb-4'>在您使用本网站提供的侵权风险过滤服务前，请您仔细阅读本声明的所有条款，您一旦开始使用该服务，即表明您无条件地接受本免责声明，您应遵守本声明和相关法律的规定。</p>
						<p className='mb-4'>1. 我们提供的侵权风险过滤服务（包括但不限于文字、图片等内容的侵权风险分析）均基于自动化算法及公开数据，结果仅供参考，不构成任何形式的法律意见或专业建议。</p>
						<p className='mb-4'>2. 本服务无法保证结果的绝对准确性、完整性或时效性，可能存在漏判、误判或因法律法规变化导致的偏差。</p>
						<p className='mb-4'>3. 您需自行判断过滤检测结果的适用性，并承担因依赖该结果而产生的全部风险。对于您依据本服务做出的任何行为（如内容发布、下架、商业决策等），我们不承担法律责任。</p>
						<p className='mb-4'>4. 如您不同意本声明内容，应立即停止使用侵权风险过滤服务。继续使用视为接受全部条款。</p>
						<p className='mb-4'>生成页面建议提示内容：检测结果仅供参考，不构成任何形式的法律意见或专业建议。您应做出独立的判断，本网站对您依据本服务做出的决策不承担责任。</p>
					</div>
				</Modal>
				{/* 侵权风险过滤功能使用须知弹窗 end */}
				<Button
					type="primary"
					style={{ display: 'block', marginLeft: 'auto' }}
					onClick={toggleBatchActions}
				>
					{showBatchActions ? '取消批量操作' : '批量操作'}
				</Button>
				<div className="w-full flex items-center justify-start h-[60px] border-b-[1px] border-normal">
					<p className="mr-[20px]">批次: {record?.batch?.batchNumber}</p>
					<p className="mr-[20px]">
						创建时间：{dayjs(record?.batch?.createTime).format('YYYY-MM-DD HH:mm:ss')}
					</p>
					<p className="mr-[20px]">风格：{style}</p>
					{/* <p className="mr-[20px]">比例尺寸：{ }</p> */}
					<p>
						总数：{record?.batch?.totalAmount}
						<span style={{ color: '#389e0d', marginLeft: '6px' }}>
							成功：{record?.batch?.successAmount}
						</span>
						{record?.batch?.failAmount > 0 && (
							<span style={{ color: '#CF1322', marginLeft: '6px' }}>
								失败：{record?.batch?.failAmount}
							</span>
						)}
					</p>
				</div>
				<div className="bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide">
					<div className="flex flex-wrap pt-[10px]">
						{imageList.map((item: any, index) => {
							return (
								<div className="aspect-square  w-[25%] pr-[12px] p-2 relative">
									<Checkbox
										className="itemCheckbox"
										/* checked={selectedItems.includes(index)} 同样能实现但异步会导致 慢操作一拍*/
										checked={item.image.every((img: any) =>
											selectedImages.includes(img.taskImage)
										)}
										onChange={(e) => {
											if (e.target.checked) {
												setSelectedImages((prev) => {
													let newArray = [...prev, ...item.image.map((img: any) => img.taskImage)]
													return Array.from(new Set(newArray))
												})
												setSelectedItems([...selectedItems, index])
											} else {
												setSelectedImages((prev) =>
													prev.filter(
														(img) =>
															!item.image
																.map((img: any) => img.taskImage)
																.includes(img)
													)
												)
												setSelectedItems(
													selectedItems.filter((i) => i !== index)
												)
											}
										}}
									/>
									<div className="text-keyWord" title={item.keyword} style={{ cursor: 'pointer' }}>提示词：{item.keyword}</div>
									<CopyOutlined
										className="copy-icon"
										onClick={() => handleCopy(item.keyword)}
									/>
									<div
										className="w-full h-full flex  pt-[120px] rounded-lg items-center justify-center  relative group bg-[#d5d5d5] overflow-hidden"
										style={{ flexDirection: 'row', flexWrap: 'wrap', height: 'fit-content' }}
									>
										{item.image.map((imageUrl: any, index: any) => (
											<div className="max-w-[300px]   aspect-square  w-[50%] pr-[12px] p-2">
												<div
													key={index}
													className="w-full h-full flex min-h-[120px] rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden"
													style={{ flexDirection: 'row' }}
												>
													<Checkbox
														className="absolute top-4 left-4 z-10 "
														style={{ transform: 'scale(1.25)' }} // 放大1.5倍
														checked={selectedImages.includes(
															imageUrl.taskImage
														)}
														onChange={() => handleSelect(imageUrl.taskImage)}
													/>
													{imageUrl.hasUploaded && <p className="absolute bottom-4  z-10"
														style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
													{getTaskImageComponent(imageUrl)}
												</div>
											</div>
										))}
									</div>
								</div>
							)
						})}
					</div>
					<Pagination align="center" style={{ margin: '20px 0 120px' }} current={detailPage} pageSize={50} onChange={handleDetailPage} total={detailTotal} showSizeChanger={false} />
					{showBatchActions && (
						<div
							className="fixed w-[60%] bottom-16 left-[20%] right-0 bg-white border-t border-gray-200 p-4 shadow-md "
							style={{ zIndex: '100' }}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<Checkbox
										checked={
											selectedImages.length == imageList.length * 4 ||
											selectedImages.length == 48
										} /*imageList[0].image.length需要替换成图片总数  */
										onChange={(e) => {
											console.log(e.target.checked)

											if (e.target.checked) {
												/* 小于48个图直接全选4*12=48  大于48个图只选前48个 */
												// if (imageList.length < 13) {
												setSelectedImages((prevSelectedImages) => {
													let checkArray: any = []
													for (let i = 0; i < imageList.length; i++) {
														imageList[i].image.map(
															(item: any, index: number) => {
																checkArray.push(item.taskImage)
															}
														)
													}
													return [
														...new Set([...prevSelectedImages, ...checkArray])
													] // 去重后返回
												})
												// } else {
												// 	setSelectedImages((prevSelectedImages) => {
												// 		let checkArray: string[] = []
												// 		for (let i = 0; i < imageList.length; i++) {
												// 			imageList[i].image.map(
												// 				(item: any, index: number) => {
												// 					checkArray.push(item.taskImage)
												// 				}
												// 			)
												// 		}
												// 		// 去重后返回
												// 		let urlArray: any = [
												// 			...new Set([...prevSelectedImages, ...checkArray])
												// 		]
												// 		// 只取前48个
												// 		return [...urlArray.slice(0, 48)]
												// 	})
												// }
											} else {
												setSelectedImages([])
											}
										}}
									>
										全选
									</Checkbox>
									{/* {selectedImages.length > 0 && ( */}
									<span className="text-gray-600">
										已选择：{selectedImages.length}项
									</span>
									{/* )} */}
								</div>
								<div className="flex space-x-2">
									<Dropdown
										menu={{
											items: synchronizeItems,
											onClick: ({ key }) => {
												setSynchronizeType(Number(key));
												showTagModal();
											}
										}}
										placement="top"
									>
										<Button type="primary" disabled={selectedImages.length === 0}>
											同步图片
										</Button>
									</Dropdown>
									<Button
										type="primary"
										disabled={selectedImages.length === 0}
										onClick={handleDownloadImgages}
										loading={downloadLoading}
									>
										下载
									</Button>
									<Dropdown
										menu={{
											items,
											onClick: ({ key }) => selectTaskType(key)
											// {
											//     const action = items.find(item => item.key === key)?.label;
											//     Modal.info({
											//         title: `执行${action}`,
											//         content: `将对选中的${selectedImages.length}张图片执行${action}操作`,
											//     });
											// }
										}}
										placement="top"
									>
										<Button
											type="primary"
											disabled={selectedImages.length === 0}
										>
											图片操作
										</Button>
									</Dropdown>
									<Button type="primary" onClick={cancelSelection}>
										取消选择
									</Button>
								</div>
							</div>
						</div>
					)}
				</div>
			</>)}
		</div>
	)
};

export default TextToImgDetail;





