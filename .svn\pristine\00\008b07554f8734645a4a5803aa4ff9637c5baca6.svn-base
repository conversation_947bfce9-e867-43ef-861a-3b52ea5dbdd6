package com.dataxai.web.task.queue.processor;

import com.alibaba.fastjson2.JSONObject;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.task.queue.AbstractQueueTaskProcessor;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.task.queue.socket.SocketIOUtils;
import com.dataxai.web.task.queue.api.ApiClientFactory;
import com.dataxai.web.task.queue.api.ApiParameters;
import com.dataxai.web.task.queue.api.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.Set;

/**
 * 蒙版队列处理器
 *
 * <p>该处理器负责处理蒙版相关的任务，主要功能包括：</p>
 * <ul>
 *   <li>对缩放后的图片进行蒙版切割处理</li>
 *   <li>调用蒙版切割接口获取分割数据</li>
 *   <li>更新任务的seg_data字段为分割数据</li>
 *   <li>通过WebSocket推送处理结果给前端</li>
 *   <li>管理任务状态和进度通知</li>
 * </ul>
 *
 * <p>处理流程：</p>
 * <ol>
 *   <li>检查任务的mark_img_url字段（缩放后的图片地址）</li>
 *   <li>调用蒙版切割接口进行图片分割</li>
 *   <li>解析返回的分割数据</li>
 *   <li>更新任务状态和seg_data字段</li>
 *   <li>通过WebSocket推送结果给前端</li>
 * </ol>
 */
@Slf4j
@Component
public class MaskQueueProcessor extends AbstractQueueTaskProcessor {

    /**
     * 支持的任务类型集合
     * 包含所有需要蒙版处理的任务类型
     *
     * <p>支持的任务类型包括：</p>
     * <ul>
     *   <li>-1：蒙版任务类型（队列类型）</li>
     *   <li>1：真人图任务</li>
     *   <li>2：人台图任务</li>
     *   <li>3：白板图任务</li>
     * </ul>
     */
    private static final Set<String> SUPPORTED_TASK_TYPES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
        "-1", // 蒙版任务类型（队列类型）
        String.valueOf(Constants.TASK_TYPE_REAL_PERSON),    // 真人图
        String.valueOf(Constants.TASK_TYPE_MANNEQUIN),      // 人台图
        String.valueOf(Constants.TASK_TYPE_WHITEBOARD)      // 白板图
    )));

    /**
     * WebSocket工具类，用于推送任务处理结果给前端
     */
    @Autowired
    private SocketIOUtils webSocketUtils;

    /**
     * API客户端工厂，用于获取不同类型的API客户端
     */
    @Autowired
    private ApiClientFactory apiClientFactory;

    /**
     * 获取队列类型
     * @return 返回mask队列类型标识
     */
    @Override
    public String getQueueType() {
        return Constants.QUEUE_MASK;
    }

    /**
     * 获取队列描述
     * @return 返回队列的中文描述
     */
    @Override
    public String getQueueDescription() {
        return "蒙版队列";
    }

    /**
     * 检查是否支持指定的任务类型
     * @param taskType 任务类型字符串
     * @return 如果支持该任务类型返回true，否则返回false
     */
    @Override
    public boolean supportsTaskType(String taskType) {
        return SUPPORTED_TASK_TYPES.contains(taskType);
    }

    @Override
    public void process(Task task, TaskOrdinal taskOrdinal, String modelUrl) {
        // 检查任务状态是否为终止状态
        if (shouldSkipDueToTermination(task, taskOrdinal)) {
            log.info("任务已终止，跳过API调用 - TaskID: {}, TaskOrdinalID: {}",
                     task != null ? task.getTaskId() : "null",
                     taskOrdinal != null ? taskOrdinal.getTaskOrdinalId() : "null");
            return;
        }

        // 蒙版队列只处理Task类型的任务
        if (task != null) {
            processTask(task, modelUrl);
        } else if (taskOrdinal != null) {
            log.debug("MaskQueueProcessor不处理TaskOrdinal类型的任务");
        }
    }

    /**
     * 处理Task类型的蒙版任务
     *
     * <p>主要处理流程：</p>
     * <ol>
     *   <li>检查任务的mark_img_url字段（缩放后的图片地址）</li>
     *   <li>调用蒙版切割接口进行图片分割</li>
     *   <li>解析返回的分割数据</li>
     *   <li>更新任务状态和seg_data字段</li>
     *   <li>通过WebSocket推送结果给前端</li>
     * </ol>
     *
     * @param task 待处理的任务对象
     * @param requestUrl 蒙版切割接口的请求URL
     */
    private void processTask(Task task, String requestUrl) {
        log.info("开始处理蒙版任务: {}", task.getTaskId());

        try {
            // 创建任务DTO对象用于接口调用
            TaskDTO taskDTO = new TaskDTO();
            BeanUtils.copyProperties(task, taskDTO);

            // 从任务的mark_img_url字段获取缩放后的图片地址
            String resultImageUrl = task.getMarkImgUrl();
            log.info("任务id {} 的mark_img_url: {}", task.getTaskId(), resultImageUrl);

            if (resultImageUrl == null || resultImageUrl.isEmpty()) {
                log.error("任务id {} 的mark_img_url为空，无法进行蒙版处理", task.getTaskId());
                // 更新任务状态为失败
                updateTaskStatus(task, Constants.TASK_STATUS_FAILED);
                return;
            }

            log.info("任务id {} 使用缩放后的图片地址进行蒙版处理: {}", task.getTaskId(), resultImageUrl);

            // 构建请求参数，调用蒙版切割接口
            taskDTO.setOriginalUrl(CommonUtils.subCosPrefix(resultImageUrl));
            String paramBody = JSONObject.from(taskDTO).toString();

            log.info("任务id {} 请求蒙版切割图片接口参数: {}", taskDTO.getTaskId(), paramBody);
            long startTime = System.currentTimeMillis();

            // 使用API客户端工厂调用蒙版API
            ApiParameters apiParameters = ApiParameters.builder()
                    .requestUrl(requestUrl)
                    .requestBody(paramBody)
                    .method("POST")
                    .timeout(Constants.HTTP_TIME_OUT)
                    .build();

            ApiResponse apiResponse = executeApiWithLogging(
                    apiClientFactory.getClient("mask"), apiParameters, 
                    task.getTaskId(), null, getQueueType());
            long endTime = System.currentTimeMillis();
            log.info("任务id {} 请求蒙版切割图片接口耗时: {}秒", taskDTO.getTaskId(), (endTime - startTime) / 1000);

            // 处理蒙版切割接口返回的结果
            if (apiResponse.isSuccess()) {
                Object responseData = apiResponse.getData();
                log.info("任务id {} 蒙版切割接口返回数据: {}", task.getTaskId(), responseData);

                // 检查responseData类型并转换为SegData
                SegData segData = null;
                if (responseData instanceof SegData) {
                    segData = (SegData) responseData;
                } else if (responseData instanceof String) {
                    // 如果是字符串，尝试解析为SegData
                    try {
                        segData = JSONObject.parseObject((String) responseData, SegData.class);
                    } catch (Exception e) {
                        log.error("任务id {} 解析响应数据为SegData失败: {}", task.getTaskId(), e.getMessage());
                    }
                } else {
                    log.error("任务id {} 响应数据类型不支持: {}", task.getTaskId(), responseData.getClass().getName());
                }

                // 构建任务分割结果对象
                TaskSegDatResult taskSegDatResult = new TaskSegDatResult();
                taskSegDatResult.setSeg_data(segData);
                taskSegDatResult.setStatus(0); // 新格式默认成功状态
                taskSegDatResult.setUserId(task.getUserId());
                taskSegDatResult.setTaskId(task.getTaskId());
                taskSegDatResult.setType(task.getType());

                if (segData != null) {
                    // 更新任务状态和分割数据
                    Task updateTask = new Task();
                    updateTask.setUserId(task.getUserId());
                    updateTask.setTaskId(task.getTaskId());
                    updateTask.setStatus(Constants.TASK_STATUS_EDIT);
                    updateTask.setCurrentQueue(null); // 处理成功，清空当前队列
                    updateTask.setSegData(JSONObject.from(segData).toString());
                    updateTask.setUpdateTime(DateUtils.getNowDate());

                    int updated = taskMapper.updateTask(updateTask);
                    if (updated > 0) {
                        // 任务状态更新成功，进行WebSocket进度推送
                        pushMaskTaskData(JSONObject.from(taskSegDatResult).toString(), taskDTO, Constants.PUSH_EVENT_TYPE_SEG);
                        long m = System.currentTimeMillis();
                        log.info("蒙版任务处理完成，总耗时: {}秒", (m - startTime) / 1000);
                    } else {
                        log.error("任务id {} 任务状态更新失败", task.getTaskId());
                    }
                } else {
                    log.error("任务id {} 分割数据为空", task.getTaskId());
                }
            } else {
                log.error("任务id {} 蒙版切割接口调用失败: {}", task.getTaskId(), apiResponse.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("任务id {} 获取蒙版切割图片信息失败: {}", task.getTaskId(), e.getMessage());
        }
    }

    /**
     * 推送蒙版任务数据给前端
     *
     * <p>通过WebSocket将蒙版任务的处理结果推送给前端，支持两种推送类型：</p>
     * <ul>
     *   <li>PUSH_EVENT_TYPE_SEG：分割数据推送</li>
     *   <li>IMAGE_CHANGE_TEXT：图片裁剪数据推送</li>
     * </ul>
     *
     * <p>推送流程：</p>
     * <ol>
     *   <li>检查用户在线状态</li>
     *   <li>根据推送类型构建推送数据</li>
     *   <li>调用WebSocket推送方法</li>
     * </ol>
     *
     * @param sendPost 要推送的原始数据
     * @param taskDTO 任务DTO对象，包含用户ID等信息
     * @param type 推送类型（PUSH_EVENT_TYPE_SEG 或 IMAGE_CHANGE_TEXT）
     */
    private void pushMaskTaskData(String sendPost, TaskDTO taskDTO, Integer type) {
        // 检查用户连接状态
        try {
            boolean isOnline = webSocketUtils.isUserOnline(String.valueOf(taskDTO.getUserId()));
            log.info("用户 {} 在线状态检查: {}", taskDTO.getUserId(), isOnline);

            if (!isOnline) {
                log.warn("用户 {} 不在线，但将继续尝试推送", taskDTO.getUserId());
            }
        } catch (Exception e) {
            log.error("检查用户在线状态失败: userId={}", taskDTO.getUserId(), e);
        }

        // 根据推送类型构建推送数据
        String pushDataStr = null;
        if (type == Constants.PUSH_EVENT_TYPE_SEG) {
            // 构建分割数据推送
            PushDTO<TaskSegDatResult> pushDTO = new PushDTO<>();
            pushDTO.setUserId(taskDTO.getUserId());
            pushDTO.setPushType(Constants.PUSH_EVENT_TYPE_SEG);
            TaskSegDatResult taskSegDatResult = JSONObject.parseObject(sendPost, TaskSegDatResult.class);
            pushDTO.setMessage(taskSegDatResult);
            pushDataStr = JSONObject.toJSONString(pushDTO);
            log.info("构建的分割数据推送: {}", pushDataStr);
        } else if (type == Constants.IMAGE_CHANGE_TEXT) {
            // 构建图片裁剪数据推送
            PushDTO<CroppingData> pushDTO = new PushDTO<>();
            pushDTO.setUserId(taskDTO.getUserId());
            pushDTO.setPushType(Constants.IMAGE_CHANGE_TEXT);
            CroppingData croppingData = JSONObject.parseObject(sendPost, CroppingData.class);
            pushDTO.setMessage(croppingData);
            pushDataStr = JSONObject.toJSONString(pushDTO);
            log.info("构建的图片裁剪数据推送: {}", pushDataStr);
        }

        log.info("准备调用realPushData，推送数据长度: {}", pushDataStr != null ? pushDataStr.length() : 0);
        // 推送数据
        realPushData(null, pushDataStr);
        log.info("=== 推送蒙版任务数据完成 ===");
    }

}