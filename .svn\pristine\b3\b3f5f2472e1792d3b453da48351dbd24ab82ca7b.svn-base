package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialIpHistory;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface MaterialIpHistoryMapper {
    
    /**
     * 插入使用历史记录
     */
    int insert(MaterialIpHistory record);
    
    /**
     * 根据用户ID获取最近7天使用的IP素材（分页）
     */
    List<MaterialIpHistory> selectRecentByUser(@Param("userId") Integer userId,
                                             @Param("offset") int offset,
                                             @Param("pageSize") int pageSize);
    
    /**
     * 统计用户最近7天使用的IP素材数量
     */
    int countRecentByUser(@Param("userId") Integer userId);
    
    /**
     * 获取用户最近使用的IP映射 (ipId -> historyId)
     * 用于在素材列表中标记最近使用状态
     */
    List<Map<String, Object>> selectUserRecentMap(@Param("userId") Integer userId, 
                                                @Param("ipIds") List<Integer> ipIds);
    
    /**
     * 检查用户是否在最近7天使用过某个IP素材
     */
    int checkRecentUsed(@Param("userId") Integer userId, @Param("ipId") Integer ipId);
    
    /**
     * 删除7天前的历史记录（定时清理）
     */
    int deleteOldRecords();

    /**
     * 根据IP ID删除所有历史记录
     */
    int deleteByIpId(@Param("ipId") Integer ipId);
}
