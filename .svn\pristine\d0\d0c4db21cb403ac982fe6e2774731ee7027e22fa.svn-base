import { useNavigate, useLocation } from 'react-router-dom'
import { message } from 'antd'
import { CheckCircleFilled } from '@ant-design/icons'
import { useEffect, useState } from 'react'
import { ContinuousDetail } from '@/pages/batchTools/continuous/detail'
import { TextToImgDetail } from '@/pages/batchTools/textToImg/detail'
import { SimilarDetail } from '@/pages/batchTools/similar/detail'
import { CutDetail } from '@/pages/batchTools/cut/detail'
import { BlockingOutDetail } from '@/pages/batchTools/blockingOut/detail'
import { ClearDetail } from '@/pages/batchTools/clear/detail'
import { FilterDetail } from '@/pages/batchTools/filter/detail'
import { TitleExtractionDetail } from '@/pages/batchTools/titleExtraction/detail'
import { ExtractDetail } from '@/pages/batchTools/extract/detail'
import { divide } from 'lodash'

export const Result = () => {
    const navigate = useNavigate()
    const location = useLocation()
    const [type, setType] = useState<string>('')
    const { nodeExecutionList, taskType, id } = location.state || {};

    useEffect(() => {
        // 从路由参数中获取类型
        const path = location.pathname.split('/').pop()
        setType(path || '')
        console.log('Current type:', path) // 调试用，确认路由参数是否正确
    }, [location.pathname, location.search])

    const renderDetailComponent = () => {
        const urlParams = new URLSearchParams(location.search);
        const batchId = urlParams.get('batchId');

        switch (type) {
            case 'textToImg':
                return <TextToImgDetail key={batchId} />
            case 'similar':
                return <SimilarDetail key={batchId} />
            case 'continuous':
                return <ContinuousDetail key={batchId} />
            case 'cut':
                return <CutDetail key={batchId} />
            case 'blockingOut':
                return <BlockingOutDetail key={batchId} />
            case 'clear':
                return <ClearDetail key={batchId} />
            case 'extract':
                return <ExtractDetail key={batchId} />
            case 'filter':
                return <FilterDetail key={batchId} />
            case 'titleExtraction':
                return <TitleExtractionDetail key={batchId} />
            default:
                return <div>未知类型</div>
        }
    }
    const pathMap: Record<number, string> = {
        8: 'textToImg',    // 文生图
        9: 'similar',      // 相似图裂变
        6: 'continuous',     // 平铺图-文生图
        7: 'continuous',      // 平铺图-图生图
        14: 'cut',         // 图案裁剪
        11: 'blockingOut',     // 图片去背景
        12: 'clear',      // 图片变清晰
        52: 'extract',      // 印花图提取
        17: 'filter',       // 侵权风险过滤
        18: 'titleExtraction'       // 标题提取
    };

    const typeMap: Record<number, string> = {
        8: '文生图',    // 文生图
        9: '相似图裂变',      // 相似图裂变
        6: '平铺图-文生图',     // 平铺图-文生图
        7: '平铺图-图生图',      // 平铺图-图生图
        14: '图案裁剪',         // 图案裁剪
        11: '图片去背景',     // 图片去背景
        12: '图片变清晰',      // 图片变清晰
        52: '印花图提取',      // 印花图提取
        17: '侵权风险过滤',       // 侵权风险过滤
        18: '标题提取'       // 标题提取
    };

    return (
        <div className="h-[calc(100vh-124px)] box-border relative">
            <div className="ml-5 flex items-center absolute top-6 max-w-[calc(100vw-220px)] flex-wrap">
                {nodeExecutionList && nodeExecutionList.map((item: any, inx: number) => (
                    <div key={inx} className="flex items-center">
                        <span className={`${item.status == 2 ? 'cursor-pointer' : 'cursor-not-allowed'} ${item.id == id ? 'text-[#f06A34]' : 'text-[#999]'}`}
                            onClick={() => {
                                if (item.status == 2) {
                                    const detailType = pathMap[item.taskType] || 'unknown';
                                    navigate(`/workspace/teamTools/result/${detailType}?batchId=${item.batchId}`, {
                                        state: {
                                            batchId: item.batchId,
                                            nodeExecutionList: nodeExecutionList,
                                            taskType: item.taskType,
                                            id: item.id
                                        },
                                        replace: false
                                    });
                                } else {
                                    return
                                }
                            }}>
                            <CheckCircleFilled className='mr-1' />{typeMap[item.taskType]}
                        </span>
                        {inx < nodeExecutionList?.length - 1 && (<div className={`w-[60px] h-[2px] m-2 ${item.id == id ? 'bg-[#f06A34]' : 'bg-[#dcdcdc]'}`} ></div>)}
                    </div>
                ))}
            </div>
            {renderDetailComponent()}
        </div>
    )
}
export default Result;
