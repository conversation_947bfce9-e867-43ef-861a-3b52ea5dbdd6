<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.CropExtractCategoryMapper">
	<resultMap id="BaseResultMap" type="com.dataxai.web.domain.CropExtractCategory">
		<id property="id" column="id"/>
		<result property="name" column="name"/>
		<result property="sortOrder" column="sort_order"/>
		<result property="status" column="status"/>
	</resultMap>

	<select id="selectByCondition" resultMap="BaseResultMap">
		SELECT id, name, sort_order, status
		FROM t_crop_extract_category
		WHERE 1=1
		<if test="name != null and name != ''"> AND name LIKE CONCAT('%', #{name}, '%')</if>
		<if test="status != null"> AND status = #{status}</if>
		ORDER BY sort_order ASC, id ASC
	</select>

	<select id="selectById" parameterType="int" resultMap="BaseResultMap">
		SELECT id, name, sort_order, status
		FROM t_crop_extract_category
		WHERE id = #{id}
	</select>

	<insert id="insert" parameterType="com.dataxai.web.domain.CropExtractCategory">
		INSERT INTO t_crop_extract_category (name, sort_order, status)
		VALUES (#{name}, #{sortOrder}, #{status})
	</insert>

	<update id="update" parameterType="com.dataxai.web.domain.CropExtractCategory">
		UPDATE t_crop_extract_category
		<set>
			<if test="name != null">name = #{name},</if>
			<if test="sortOrder != null">sort_order = #{sortOrder},</if>
			<if test="status != null">status = #{status},</if>
		</set>
		WHERE id = #{id}
	</update>

	<delete id="deleteById" parameterType="int">
		DELETE FROM t_crop_extract_category WHERE id = #{id}
	</delete>

	<select id="countByCondition" resultType="int">
		SELECT COUNT(1) FROM t_crop_extract_category
		WHERE 1=1
		<if test="name != null and name != ''"> AND name LIKE CONCAT('%', #{name}, '%')</if>
		<if test="status != null"> AND status = #{status}</if>
	</select>

	<select id="selectPageByCondition" resultMap="BaseResultMap">
		SELECT id, name, sort_order, status
		FROM t_crop_extract_category
		WHERE 1=1
		<if test="name != null and name != ''"> AND name LIKE CONCAT('%', #{name}, '%')</if>
		<if test="status != null"> AND status = #{status}</if>
		ORDER BY sort_order ASC, id ASC
		LIMIT #{offset}, #{pageSize}
	</select>
</mapper> 