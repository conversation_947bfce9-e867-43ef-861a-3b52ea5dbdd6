/**
 * @Author: wuyang
 * @Date: 2024/1/15
 * @Description: ""
 */
import { ZoomInOutlined, ZoomOutOutlined,ScissorOutlined} from '@ant-design/icons'
import { Button, Spin , Input, message, Modal, Popconfirm,App } from 'antd'
import { useTaskService } from '@/common/services/task/taskContext'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { useState,useRef,useEffect,useMemo } from 'react'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import {imageconvertText,confirmDo} from '@/api/image'
import { DataXProgress } from '@/component/data-x-progress/DataXProgress'
import { uploadFile } from '@/utils/uploadFile'
import { UPLOAD_SERVER_MAX_SIZE } from '@/constant'
import { ITaskSeg } from '@/types/task'


const { TextArea } = Input;

export const TaskTexture = () => {

  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false)
  const [isDrawing, setIsDrawing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startY, setStartY] = useState(0);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const [imgWidth, setImgWidth] = useState(0);
  const [imgHeight, setImgHeight] = useState(0);
  const [spinning, setSpinning] = useState(false);
  const [markImgUrl, setMarkImgUrl] = useState('');


  const taskService = useTaskService()

  const canvasRef = useRef(null);
  const canvas: any = canvasRef.current;

  const { message } = App.useApp()

  // const img = useRef(null);

  const handleMouseEnter  = () => {
      // 选择图片元素
       const img: Element | null = document.querySelector('.img');
      // 获取图片的计算样式
      let style
      if(img){
       style = window.getComputedStyle(img);
      }
      // 获取图片的实际宽度和高度
      if(style){
        console.log(style,'style111');
        const width = parseFloat(style.width);
      const height = parseFloat(style.height);
      console.log('图片的实际渲染尺寸:', style.width, 'x', style.height);
      setImgWidth(width)
      setImgHeight(height)
      // 输出图片的实际渲染尺寸
      console.log(canvas,'canvas.width');
     if(canvas){
      canvas.width = width;
      canvas.height = height;
     }
      }
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    setStartX(e.nativeEvent.offsetX);
    setStartY(e.nativeEvent.offsetY);
  };
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
      setWidth(e.nativeEvent.offsetX - startX)
      setHeight(e.nativeEvent.offsetY - startY)
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.beginPath();
    ctx.rect(startX, startY, e.nativeEvent.offsetX - startX, e.nativeEvent.offsetY - startY);
    ctx.stroke();
  };


  const handleMouseUp = () => {
    setIsDrawing(false);
  };



  const [originalUrl] = useAtomMethod(taskService.originalUrl)
  const [taskStatus] = useAtomMethod(taskService.taskStatus)
  const [seg] = useAtomMethod(taskService.seg)

// 裁剪图片
const [croppedImageData,setCroppedImageData] = useState('')


const deforeCopy = useRef(null)
const CopyResult = useRef(null)

const img = useRef(null)


const handleCopy = async () => {
  if(!width || !height){
    return message.warning('请先选择裁剪区域！')
  }
  setLoading(true)
  const deforeCopyDom :any = deforeCopy.current
  // 创建一个 file 对象，内容为图片数据
  const blob = await fetch(originalUrl as any).then(response => response.blob());
  const file = new File([blob], 'image.jpg', { type: blob.type });
  if(deforeCopyDom){
    // deforeCopyDom.style.display = 'none'
    const afterCopyDom = document.createElement('canvas');
    const ctx :any= afterCopyDom.getContext('2d');
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event :any) => {
      const dataURL = event.target.result; //base64
      const img: any = new Image()
      img.src = dataURL;
      img.onload = () => {
        afterCopyDom.width = imgWidth
        afterCopyDom.height = imgHeight
      ctx.drawImage(img, 0, 0, imgWidth, imgHeight);
      //获取裁剪图片
      setCroppedImageData(ctx.getImageData(startX, startY, width, height))
      setSpinning(true)
    };
    }

  }
}

    // 在新的canvas上绘制裁剪后的图片
    useEffect(() => {
        if(croppedImageData){
          const CopyResultDom :any = CopyResult.current
          CopyResultDom.width = Math.abs(width)
          CopyResultDom.height = Math.abs(height)
          const ctxResult :any= CopyResultDom.getContext('2d');
          ctxResult.putImageData(croppedImageData, 0, 0);
          //转化成base64格式
          const base64Image = CopyResultDom.toDataURL('image/png');
          console.log(base64Image,'base64Image')
          uploadFile(base64Image,UPLOAD_SERVER_MAX_SIZE).then(async(res) => {
              const paramsObj = {
              image_cut_url:res.url,
              type:4,
              taskId:taskService.taskId,
              frameCoordinates :''
            }
            const text =await imageconvertText(paramsObj)
            setValue(text.desc)
            setMarkImgUrl(res.url)
            setLoading(false)
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          })
        }
    }, [croppedImageData]);

  // 确认执行
  const confirmDraw = async () => {
    setLoading(true)
    const checkExecute = taskService.checkExecute()
    // 判断是否可以执行任务
    if (checkExecute.isSuccess) {
        // 此处先将 mask 上传到服务器再进行执行任务
              taskService
                .execute({
                  markImgUrl,
                  prompt:value
                })
                .then((res) => {
                  console.log(res);
                  userinfoService.refresh()  //刷新用户积分
                  return taskService.refresh() //刷新任务列表
                })
                .catch((err) => {
                    message.warning(err.data.msg)
                  setLoading(false)
                })
    } else {
      setLoading(false)
      message.warning(checkExecute.message)
    }




    // const params = {
    //   taskId:taskService.taskId,
    //   type:4,
    //   desType:'0',
    //   markImgUrl:markImgUrl,
    //   originImgUrl:originalUrl || '',
    //   prompt:value,
    // }
    //  const res = await confirmDo(params)
    //  console.log('taskStatus:',taskStatus)
  }

  	// 等待处理切割图信息
	const rle: ITaskSeg = useMemo(() => {
		return {
			height: parseInt(String(seg?.height)) ?? 0,
			width: parseInt(String(seg?.width)) ?? 0,
			mask_list: seg?.mask_list ?? [],
			proceing: seg?.proceing ?? 0
		}
	}, [seg])

	return (
    <div style={{width:'100%'}}>
      {/* {contextHolder} */}
		<div style={{height:'750px'}} className='flex h-full'>
      {/* 左侧描述 */}
      <div className='w-2/5 h-full border border-gray-300 rounded-md p-2 m-2 mr-10 pt-10'>
        <div className='mb-6'>图片描述（仅支持英文）</div>
        <TextArea
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="Exquisite Japanese textile designs"
        autoSize={{ minRows: 10, maxRows: 10 }}
        maxLength={2000}
        showCount 
      />
      </div>
    {/* 右侧图片裁剪 */}
      <div className='w-3/5 mt-2'>
        <div className='relative w-full h-4/5 bg-gray-100 pt-3' style={{borderRadius: '10px',display: 'flex',flexDirection: 'column',justifyContent: 'space-evenly',alignItems: 'center'}}>
            {/* <div className='relative  mt-10 ml-5'> */}
                {/* 显示图片 */}
              <div ref={deforeCopy} style={{
                	width: '1024px',
                  height: '760px',
                  transform: `scale(0.4) `,
              }}>
              {/* <div>
              <ZoomInOutlined />
              </div>
             <div>
             <ZoomOutOutlined />
             </div> */}
                <canvas
                    ref={canvasRef}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    style={{position: 'absolute'}}
                  />
                <img className='w-full h-full' ref={img} onMouseMove={handleMouseEnter}  src={originalUrl} alt=""/>
              </div>
              {/* <canvas style={{display: 'none'}} ref={afterCopy}></canvas> */}
              <canvas style={{display: 'none'}} ref={CopyResult}></canvas>
                <div onClick={handleCopy} >
                     <Button className='w-24 ' icon={ <ScissorOutlined />}  shape="round" size='large'>
                        裁剪
                     </Button>
                </div>
                {/* <div>
                  <PlusCircleOutlined className='absolute  right-5' style={{top: '15px',color: '#847e7e',cursor: 'pointer'}}/>
                  <MinusCircleOutlined className='absolute right-5' style={{top: '50px',color: '#847e7e',cursor: 'pointer'}}/>
                </div> */}
            {/* </div> */}

        </div>
        {/* 确认执行 */}
        <div className='flex justify-center mt-10'>
        <Button disabled={markImgUrl == ''} type={'primary'} className={'!w-[160px] !h-[60px] '}>
					<div onClick={confirmDraw}>
						<div className={'text-[18px]'}>确认执行</div>
						<div className={'text-[12px]'}>(消耗4积分)</div>
					</div>
				</Button>
        </div>
      </div>

    </div>

    <Spin
				spinning={loading}
				fullscreen={true}
				indicator={
					<div style={{ width: 120, height: 100 }}>
						<DataXProgress percent={100} />
						<span style={{ color: 'white', width: 120 }}>提交AI处理...</span>
					</div>
				}
			></Spin>
    </div>
	)
}
