package com.dataxai.web.service.impl;

import com.dataxai.web.controller.ImageController.PageResult;
import com.dataxai.web.domain.MaterialIp;
import com.dataxai.web.domain.MaterialIpUser;
import com.dataxai.web.domain.MaterialStyle;
import com.dataxai.web.domain.MaterialStyleUser;
import com.dataxai.web.dto.MaterialIpWithFavoriteDTO;
import com.dataxai.web.dto.MaterialStyleWithFavoriteDTO;
import com.dataxai.web.mapper.MaterialIpUserMapper;
import com.dataxai.web.mapper.MaterialStyleUserMapper;
import com.dataxai.web.mapper.MaterialIpHistoryMapper;
import com.dataxai.web.mapper.MaterialStyleHistoryMapper;
import com.dataxai.web.domain.MaterialIpHistory;
import com.dataxai.web.domain.MaterialStyleHistory;
import com.dataxai.web.util.TypeConvertUtil;
import com.dataxai.web.service.MaterialIpService;
import com.dataxai.web.service.MaterialStyleService;
import com.dataxai.web.service.MaterialWithFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MaterialWithFavoriteServiceImpl implements MaterialWithFavoriteService {

    @Autowired
    private MaterialIpService materialIpService;
    
    @Autowired
    private MaterialStyleService materialStyleService;
    
    @Autowired
    private MaterialIpUserMapper materialIpUserMapper;
    
    @Autowired
    private MaterialStyleUserMapper materialStyleUserMapper;

    @Autowired
    private MaterialIpHistoryMapper materialIpHistoryMapper;

    @Autowired
    private MaterialStyleHistoryMapper materialStyleHistoryMapper;

    @Override
    public PageResult<MaterialIpWithFavoriteDTO> queryIpPageWithFavorite(
            Integer userId, Integer pageNum, Integer pageSize, 
            String name, Integer status, Integer categoryId) {
        
        // 获取IP素材列表
        List<MaterialIp> ipList = materialIpService.queryPage(pageNum, pageSize, name, status, categoryId);
        int total = materialIpService.countByCondition(name, status, categoryId);
        
        // 转换为DTO并设置收藏状态
        List<MaterialIpWithFavoriteDTO> dtoList = convertIpListWithFavorite(ipList, userId);
        
        PageResult<MaterialIpWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        return result;
    }

    @Override
    public PageResult<MaterialStyleWithFavoriteDTO> queryStylePageWithFavorite(
            Integer userId, Integer pageNum, Integer pageSize, 
            String name, Integer status, Integer categoryId, Integer taskType) {
        
        // 获取风格素材列表
        List<MaterialStyle> styleList = materialStyleService.queryPage(pageNum, pageSize, name, status, categoryId, taskType);
        int total = materialStyleService.countByCondition(name, status, categoryId, taskType);
        
        // 转换为DTO并设置收藏状态
        List<MaterialStyleWithFavoriteDTO> dtoList = convertStyleListWithFavorite(styleList, userId, taskType);
        
        PageResult<MaterialStyleWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        return result;
    }

    @Override
    public PageResult<MaterialIpWithFavoriteDTO> getUserFavoriteIps(
            Integer userId, Integer pageNum, Integer pageSize) {
        
        // 获取用户收藏的IP记录
        int offset = (pageNum - 1) * pageSize;
        List<MaterialIpUser> favoriteList = materialIpUserMapper.selectByUser(userId, offset, pageSize);
        int total = materialIpUserMapper.countByUser(userId);
        
        // 获取对应的IP素材详情
        List<MaterialIpWithFavoriteDTO> dtoList = new ArrayList<>();
        for (MaterialIpUser favorite : favoriteList) {
            MaterialIp materialIp = materialIpService.getById(favorite.getMaterialIpId());
            if (materialIp != null) {
                MaterialIpWithFavoriteDTO dto = MaterialIpWithFavoriteDTO.fromMaterialIp(materialIp);
                dto.setIsFavorited(true);
                dto.setFavoriteId(favorite.getId());
                dtoList.add(dto);
            }
        }
        
        PageResult<MaterialIpWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        return result;
    }

    @Override
    public PageResult<MaterialStyleWithFavoriteDTO> getUserFavoriteStyles(
            Integer userId, Integer pageNum, Integer pageSize, Integer taskType) {
        
        // 获取用户收藏的风格记录
        int offset = (pageNum - 1) * pageSize;
        List<MaterialStyleUser> favoriteList = materialStyleUserMapper.selectByUser(userId, offset, pageSize, taskType);
        int total = materialStyleUserMapper.countByUser(userId, taskType);
        
        // 获取对应的风格素材详情
        List<MaterialStyleWithFavoriteDTO> dtoList = new ArrayList<>();
        for (MaterialStyleUser favorite : favoriteList) {
            MaterialStyle materialStyle = materialStyleService.getById(favorite.getMaterialStyleId());
            if (materialStyle != null) {
                MaterialStyleWithFavoriteDTO dto = MaterialStyleWithFavoriteDTO.fromMaterialStyle(materialStyle);
                dto.setIsFavorited(true);
                dto.setFavoriteId(favorite.getId());
                dtoList.add(dto);
            }
        }
        
        PageResult<MaterialStyleWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        
        return result;
    }

    @Override
    public PageResult<MaterialIpWithFavoriteDTO> getUserRecentIps(
            Integer userId, Integer pageNum, Integer pageSize) {

        // 获取用户最近使用的IP记录
        int offset = (pageNum - 1) * pageSize;
        List<MaterialIpHistory> recentList = materialIpHistoryMapper.selectRecentByUser(userId, offset, pageSize);
        int total = materialIpHistoryMapper.countRecentByUser(userId);

        // 获取对应的IP素材详情
        List<MaterialIpWithFavoriteDTO> dtoList = new ArrayList<>();
        for (MaterialIpHistory history : recentList) {
            MaterialIp materialIp = materialIpService.getById(history.getMaterialIpId());
            if (materialIp != null) {
                MaterialIpWithFavoriteDTO dto = MaterialIpWithFavoriteDTO.fromMaterialIp(materialIp);

                // 设置最近使用状态
                dto.setIsRecentUsed(true);
                dto.setHistoryId(history.getId());

                // 检查收藏状态并获取favoriteId
                MaterialIpUser favorite = materialIpUserMapper.selectByUserAndIp(userId, materialIp.getId());
                if (favorite != null) {
                    dto.setIsFavorited(true);
                    dto.setFavoriteId(favorite.getId());
                } else {
                    dto.setIsFavorited(false);
                    dto.setFavoriteId(null);
                }

                dtoList.add(dto);
            }
        }

        PageResult<MaterialIpWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);

        return result;
    }

    @Override
    public PageResult<MaterialStyleWithFavoriteDTO> getUserRecentStyles(
            Integer userId, Integer pageNum, Integer pageSize, Integer taskType) {

        // 获取用户最近使用的风格记录
        int offset = (pageNum - 1) * pageSize;
        List<MaterialStyleHistory> recentList = materialStyleHistoryMapper.selectRecentByUser(userId, offset, pageSize, taskType);
        int total = materialStyleHistoryMapper.countRecentByUser(userId, taskType);

        // 获取对应的风格素材详情
        List<MaterialStyleWithFavoriteDTO> dtoList = new ArrayList<>();
        for (MaterialStyleHistory history : recentList) {
            MaterialStyle materialStyle = materialStyleService.getById(history.getMaterialStyleId());
            if (materialStyle != null) {
                MaterialStyleWithFavoriteDTO dto = MaterialStyleWithFavoriteDTO.fromMaterialStyle(materialStyle);

                // 设置最近使用状态
                dto.setIsRecentUsed(true);
                dto.setHistoryId(history.getId());

                // 检查收藏状态并获取favoriteId
                MaterialStyleUser favorite = materialStyleUserMapper.selectByUserAndStyle(userId, materialStyle.getId(), taskType);
                if (favorite != null) {
                    dto.setIsFavorited(true);
                    dto.setFavoriteId(favorite.getId());
                } else {
                    dto.setIsFavorited(false);
                    dto.setFavoriteId(null);
                }

                dtoList.add(dto);
            }
        }

        PageResult<MaterialStyleWithFavoriteDTO> result = new PageResult<>();
        result.setList(dtoList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);

        return result;
    }

    /**
     * 转换IP列表并设置收藏状态和最近使用状态
     */
    private List<MaterialIpWithFavoriteDTO> convertIpListWithFavorite(List<MaterialIp> ipList, Integer userId) {
        if (ipList == null || ipList.isEmpty()) {
            return ipList.stream()
                    .map(MaterialIpWithFavoriteDTO::fromMaterialIp)
                    .collect(Collectors.toList());
        }

        // 获取IP ID列表
        List<Integer> ipIds = ipList.stream()
                .map(MaterialIp::getId)
                .collect(Collectors.toList());

        Map<Integer, Integer> favoriteIdMap = new HashMap<>();
        Map<Integer, Integer> recentHistoryMap = new HashMap<>();

        if (userId != null) {
            // 获取用户收藏映射
            List<Map<String, Object>> favoriteMap = materialIpUserMapper.selectUserFavoriteMap(userId, ipIds);
            for (Map<String, Object> map : favoriteMap) {
                Integer ipId = TypeConvertUtil.objectToInteger(map.get("ipId"));
                Integer favoriteId = TypeConvertUtil.objectToInteger(map.get("favoriteId"));
                favoriteIdMap.put(ipId, favoriteId);
            }

            // 获取用户最近使用映射
            List<Map<String, Object>> recentMap = materialIpHistoryMapper.selectUserRecentMap(userId, ipIds);
            for (Map<String, Object> map : recentMap) {
                Integer ipId = TypeConvertUtil.objectToInteger(map.get("ipId"));
                Integer historyId = TypeConvertUtil.objectToInteger(map.get("historyId"));
                recentHistoryMap.put(ipId, historyId);
            }
        }

        // 转换为DTO并设置收藏状态和最近使用状态
        return ipList.stream().map(ip -> {
            MaterialIpWithFavoriteDTO dto = MaterialIpWithFavoriteDTO.fromMaterialIp(ip);

            // 设置收藏状态
            Integer favoriteId = favoriteIdMap.get(ip.getId());
            dto.setIsFavorited(favoriteId != null);
            dto.setFavoriteId(favoriteId);

            // 设置最近使用状态
            Integer historyId = recentHistoryMap.get(ip.getId());
            dto.setIsRecentUsed(historyId != null);
            dto.setHistoryId(historyId);

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 转换风格列表并设置收藏状态和最近使用状态
     */
    private List<MaterialStyleWithFavoriteDTO> convertStyleListWithFavorite(List<MaterialStyle> styleList, Integer userId, Integer taskType) {
        if (styleList == null || styleList.isEmpty()) {
            return styleList.stream()
                    .map(MaterialStyleWithFavoriteDTO::fromMaterialStyle)
                    .collect(Collectors.toList());
        }

        // 获取风格ID列表
        List<Integer> styleIds = styleList.stream()
                .map(MaterialStyle::getId)
                .collect(Collectors.toList());

        Map<Integer, Integer> favoriteIdMap = new HashMap<>();
        Map<Integer, Integer> recentHistoryMap = new HashMap<>();

        if (userId != null) {
            // 获取用户收藏映射
            List<Map<String, Object>> favoriteMap = materialStyleUserMapper.selectUserFavoriteMap(userId, styleIds, taskType);
            for (Map<String, Object> map : favoriteMap) {
                Integer styleId = TypeConvertUtil.objectToInteger(map.get("styleId"));
                Integer favoriteId = TypeConvertUtil.objectToInteger(map.get("favoriteId"));
                favoriteIdMap.put(styleId, favoriteId);
            }

            // 获取用户最近使用映射
            List<Map<String, Object>> recentMap = materialStyleHistoryMapper.selectUserRecentMap(userId, styleIds, taskType);
            for (Map<String, Object> map : recentMap) {
                Integer styleId = TypeConvertUtil.objectToInteger(map.get("styleId"));
                Integer historyId = TypeConvertUtil.objectToInteger(map.get("historyId"));
                recentHistoryMap.put(styleId, historyId);
            }
        }

        // 转换为DTO并设置收藏状态和最近使用状态
        return styleList.stream().map(style -> {
            MaterialStyleWithFavoriteDTO dto = MaterialStyleWithFavoriteDTO.fromMaterialStyle(style);

            // 设置收藏状态
            Integer favoriteId = favoriteIdMap.get(style.getId());
            dto.setIsFavorited(favoriteId != null);
            dto.setFavoriteId(favoriteId);

            // 设置最近使用状态
            Integer historyId = recentHistoryMap.get(style.getId());
            dto.setIsRecentUsed(historyId != null);
            dto.setHistoryId(historyId);

            return dto;
        }).collect(Collectors.toList());
    }
}
