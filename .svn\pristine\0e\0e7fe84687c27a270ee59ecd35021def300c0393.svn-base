package com.dataxai.web.gtask.factory;

import com.dataxai.web.gtask.processor.GTaskProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GTask处理器工厂
 * 使用工厂模式，根据任务类型创建对应处理器
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Slf4j
@Component
public class GTaskProcessorFactory {

    @Autowired
    private List<GTaskProcessor<?>> processors;

    // 任务类型到处理器的映射
    private final Map<String, GTaskProcessor<?>> processorMap = new HashMap<>();

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initProcessorMap() {
        if (processors != null) {
            for (GTaskProcessor<?> processor : processors) {
                String taskType = processor.getTaskType();
                processorMap.put(taskType, processor);
                log.info("注册GTask处理器: {} - {}", taskType, processor.getTaskDescription());
            }
        }
        log.info("GTask处理器工厂初始化完成，共注册{}个处理器", processorMap.size());
    }

    /**
     * 根据任务类型获取处理器
     *
     * @param taskType 任务类型
     * @return 处理器实例
     */
    @SuppressWarnings("unchecked")
    public <T> GTaskProcessor<T> getProcessor(String taskType) {
        GTaskProcessor<?> processor = processorMap.get(taskType);
        if (processor == null) {
            log.warn("未找到任务类型 {} 对应的GTask处理器", taskType);
            return null;
        }
        return (GTaskProcessor<T>) processor;
    }

    /**
     * 获取所有已注册的任务类型
     *
     * @return 任务类型集合
     */
    public java.util.Set<String> getAllTaskTypes() {
        return processorMap.keySet();
    }

    /**
     * 检查是否支持指定任务类型
     *
     * @param taskType 任务类型
     * @return 是否支持
     */
    public boolean supportsTaskType(String taskType) {
        return processorMap.containsKey(taskType);
    }

    /**
     * 获取处理器统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getProcessorStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalProcessors", processorMap.size());
        statistics.put("supportedTaskTypes", processorMap.keySet());

        Map<String, String> processorDescriptions = new HashMap<>();
        for (Map.Entry<String, GTaskProcessor<?>> entry : processorMap.entrySet()) {
            processorDescriptions.put(entry.getKey(), entry.getValue().getTaskDescription());
        }
        statistics.put("processorDescriptions", processorDescriptions);

        return statistics;
    }
}
