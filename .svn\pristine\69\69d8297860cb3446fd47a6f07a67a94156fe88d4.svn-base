package com.dataxai.web.utils;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Iterator;


import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 通用 WebP 转换工具类。
 * 功能：
 * - 从 URL 或 BufferedImage 生成 WebP 图片
 * - 可按最长边等比例缩放（maxSize）
 * - 有损（Lossy）模式下可设置质量（quality: 0.0~1.0），无损模式忽略质量
 * 依赖：需在运行环境中注册 WebP ImageIO 写入器（如 luciad/webp-imageio 或 TwelveMonkeys）
 * 注意：内部会优先选择 "Lossy" 压缩类型；若不可用，则回退为第一个可用类型
 */
@Slf4j
@Component
public class JpgUrlToWebpConverter {
    /**
     * 支持 jpg/jpeg/png 的 URL 图片转换为 webp 字节数组
     * - 自动根据 URL 后缀或 Content-Type 判定输入类型
     * - 若非上述类型，则仍尝试用 ImageIO 读取并写为 webp
     */
    // ---- 通用入口 ----
    public static byte[] urlToWebpBytes(String url, String taskType) throws IOException {
        int maxSize =256;
        float quality = 0.8f;
        java.net.HttpURLConnection conn = null;
        try {
            log.info("开始下载并处理图片: {}, 任务类型: {}", url, taskType);
            URL u = new URL(url);
            conn = (java.net.HttpURLConnection) u.openConnection();
            conn.setConnectTimeout(15000); // 15秒连接超时
            conn.setReadTimeout(30000);    // 30秒读取超时
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            conn.setInstanceFollowRedirects(true);

            // 处理HTTP重定向
            int status = conn.getResponseCode();
            if (status == java.net.HttpURLConnection.HTTP_MOVED_TEMP || status == java.net.HttpURLConnection.HTTP_MOVED_PERM || status == java.net.HttpURLConnection.HTTP_SEE_OTHER) {
                String newUrl = conn.getHeaderField("Location");
                conn.disconnect();
                log.info("图片链接重定向: {} -> {}", url, newUrl);
                u = new URL(newUrl);
                conn = (java.net.HttpURLConnection) u.openConnection();
                conn.setConnectTimeout(15000);
                conn.setReadTimeout(30000);
                conn.setRequestProperty("User-Agent", "Mozilla/5.0");
            }

            try (InputStream inputStream = conn.getInputStream()) {
                BufferedImage img = ImageIO.read(inputStream);
                if (img == null) {
                    throw new IOException("无法读取图片，可能不是受支持的格式: " + url);
                }
                BufferedImage scaled = scaleToMaxSize(img, maxSize);
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                    writeWebp(scaled, baos, quality);
                    return baos.toByteArray();
                }
            }
        } catch (Exception e) {
            log.error("下载并处理图片异常: {}, 任务类型: {}", url, taskType, e);
            return null;
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }

    }
    /**
     * 将远程图片转换为 WebP 文件。
     *
     * @param url     图片的 URL
     * @param maxSize 最长边尺寸上限（像素），<=0 表示不缩放
     * @param quality 压缩质量（0.0~1.0，仅在 Lossy 模式生效）
     * @param outFile 输出文件路径
     * @throws IOException 网络/读写异常
     */
    public static void urlToWebpFile(String url, int maxSize, float quality, File outFile) throws IOException {


        BufferedImage img = ImageIO.read(new URL(url));
        BufferedImage scaled = scaleToMaxSize(img, maxSize);
        convertToWebp(scaled, outFile, quality);
    }
    /**
     *
     * @param img     输入图片（RGB/ARGB）
     * @param maxSize 最长边尺寸上限（像素），<=0 表示不缩放
     * @param quality 压缩质量（0.0~1.0，仅在 Lossy 模式生效）
     * @param outFile 输出文件路径
     * @throws IOException 编码/写入异常
     */
    public static void imageToWebpFile(BufferedImage img, int maxSize, float quality, File outFile) throws IOException {
        BufferedImage scaled = scaleToMaxSize(img, maxSize);
        convertToWebp(scaled, outFile, quality);
    }
    /**
     * 将内存中的图片转换为 WebP 文件。
     /**
     * 将图片写为 WebP 到输出流。
     * 内部会优先选择 "Lossy" 压缩类型；若不可用，则回退为第一个可用类型。
     * 仅在非 "Lossless" 模式下设置质量。
     *
     * @param img     输入图片（RGB/ARGB）
     * @param os      输出流（调用方负责关闭）
     * @param quality 压缩质量（0.0~1.0，仅在 Lossy 模式生效）
     * @throws IOException 编码/写入异常
     */
    public static void writeWebp(BufferedImage img, OutputStream os, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByMIMEType("image/webp");
        if (!writers.hasNext()) {
            throw new IllegalStateException("没有找到 WebP 写入器，请确保 webp-imageio 已正确安装！");
        }
        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        if (param.canWriteCompressed()) {
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            String[] types = param.getCompressionTypes();
            if (types != null && types.length > 0) {
                String chosen = null;
                for (String t : types) {
                    if ("Lossy".equalsIgnoreCase(t)) { chosen = t; break; }
                }
                if (chosen == null) chosen = types[0];
                param.setCompressionType(chosen);
                if (!"Lossless".equalsIgnoreCase(chosen)) {
                    float q = Math.max(0f, Math.min(1f, quality));
                    param.setCompressionQuality(q);
                }
            }
        }
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(os)) {
            writer.setOutput(ios);
            writer.write(null, new IIOImage(img, null, null), param);
        }
    }

    public static void convertToWebp(BufferedImage image, File output, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByMIMEType("image/webp");
        if (!writers.hasNext()) {
            throw new IllegalStateException("没有找到 WebP 写入器，请确保 webp-imageio 已正确安装！");
        }

        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();

        // 设置 WebP 压缩：先选择压缩类型，再设置质量（部分实现要求先指定类型）
        if (param.canWriteCompressed()) {
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            String[] types = param.getCompressionTypes();
            if (types != null && types.length > 0) {
                String chosen = null;
                for (String t : types) {
                    if ("Lossy".equalsIgnoreCase(t)) { chosen = t; break; }
                }
                if (chosen == null) chosen = types[0];
                param.setCompressionType(chosen);
                // 仅在有损模式下设置质量；范围限制在 [0,1]
                if (!"Lossless".equalsIgnoreCase(chosen)) {
                    float q = Math.max(0f, Math.min(1f, quality));
                    param.setCompressionQuality(q);
                }
            }
        }

        try (ImageOutputStream ios = ImageIO.createImageOutputStream(output)) {
            writer.setOutput(ios);
            // 正确的写入调用需要提供 IIOMetadata/缩略图参数位，至少传 null
            writer.write(null, new IIOImage(image, null, null), param);
        }
    }

    /**
     * 将图像按比例缩放，使其最长边不超过 maxSize
     */
    private static BufferedImage scaleToMaxSize(BufferedImage src, int maxSize) {
        if (src == null) return null;
        int w = src.getWidth();
        int h = src.getHeight();
        if (w <= 0 || h <= 0) return src;
        int max = Math.max(w, h);
        if (max <= maxSize) return src; // 无需缩放
        double scale = maxSize / (double) max;
        int nw = Math.max(1, (int) Math.round(w * scale));
        int nh = Math.max(1, (int) Math.round(h * scale));
        BufferedImage out = new BufferedImage(nw, nh, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = out.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.drawImage(src, 0, 0, nw, nh, null);
        g.dispose();
        return out;
    }
//        writer.dispose();

}