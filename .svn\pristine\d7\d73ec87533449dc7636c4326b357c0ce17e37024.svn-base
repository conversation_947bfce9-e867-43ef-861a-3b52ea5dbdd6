package com.dataxai.web.controller.front;

import com.dataxai.common.core.domain.R;
import com.dataxai.web.domain.NumberCodeResponse;
import com.dataxai.web.domain.TokenResponse;
import com.dataxai.web.domain.VerificationCodeDTO;
import com.dataxai.web.service.VerificationCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.security.NoSuchAlgorithmException;

/**
 * @version 1.0
 * @Author:xg
 */
@Api(tags = "短信验证码")
@RestController
@RequestMapping("verification")
public class VerificationCodeController {

    @Autowired
    VerificationCodeService verificationCodeService;

    @ApiOperation("获取短信验证码")
    @GetMapping("/code")
    public R<NumberCodeResponse> getVerificationCode(String phone){
        System.out.println("收到的手机号是：    "+ phone);
        return verificationCodeService.getVerificationCode(phone);
    }

    @PostMapping("/code-check")
    @ApiOperation("验证短信验证")
    public R<TokenResponse> verificationCodeCheck(@RequestBody VerificationCodeDTO verificationCodeDTO) throws NoSuchAlgorithmException {
        String frontUserPhone = verificationCodeDTO.getFrontUserPhone();
        String verificationCode = verificationCodeDTO.getVerificationCode();
        String openId = verificationCodeDTO.getOpenId();
        Integer type = verificationCodeDTO.getType();

        if(StringUtils.isEmpty(frontUserPhone)){
            return R.fail("手机号不能为空");
        }
        if(type == 1 && StringUtils.isEmpty(verificationCode)){
            return R.fail("验证码不能为空");
        }
        if(type == 2 && StringUtils.isEmpty(verificationCode)){
            return R.fail("密码不能为空");
        }
        String token = verificationCodeService.verificationCodeCheck(frontUserPhone, verificationCode, openId, type);
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToke(token);
        tokenResponse.setFlag(true);
        return R.ok(tokenResponse);
    }

}
