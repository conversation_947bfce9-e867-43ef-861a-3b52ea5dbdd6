package com.dataxai.web.controller.ImageController;

import com.dataxai.web.domain.MaterialStyle;
import com.dataxai.web.service.MaterialStyleService;
import com.dataxai.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/material/style")
@Api(tags = "素材风格管理")
public class MaterialStyleController {

    @Autowired
    private MaterialStyleService materialStyleService;

    @GetMapping("/page")
    @ApiOperation("分页查询素材风格")
    public R<PageResult<MaterialStyle>> queryPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) Integer taskType) {

        List<MaterialStyle> list = materialStyleService.queryPage(pageNum, pageSize, name, status, categoryId, taskType);
        int total = materialStyleService.countByCondition(name, status, categoryId, taskType);

        PageResult<MaterialStyle> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);

        return R.ok(pageResult);
    }

    @PostMapping
    @ApiOperation("新增素材风格")
    public R<Boolean> addMaterialStyle(@RequestBody MaterialStyle materialStyle) {
        boolean result = materialStyleService.addMaterialStyle(materialStyle);
        return result ? R.ok(true) : R.fail("新增失败");
    }

    @PutMapping
    @ApiOperation("修改素材风格")
    public R<Boolean> updateMaterialStyle(@RequestBody MaterialStyle materialStyle) {
        boolean result = materialStyleService.updateMaterialStyle(materialStyle);
        return result ? R.ok(true) : R.fail("修改失败");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除素材风格")
    public R<Boolean> deleteMaterialStyle(@PathVariable Integer id) {
        boolean result = materialStyleService.deleteMaterialStyle(id);
        return result ? R.ok(true) : R.fail("删除失败");
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取素材风格详情")
    public R<MaterialStyle> getById(@PathVariable Integer id) {
        MaterialStyle materialStyle = materialStyleService.getById(id);
        return materialStyle != null ? R.ok(materialStyle) : R.fail("数据不存在");
    }
}