package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.domain.*;
import com.dataxai.domain.dto.CreateWorkflowRequest;
import com.dataxai.domain.dto.TaskStatisticsDTO;
import com.dataxai.domain.dto.WorkflowNodeTaskDTO;
// 新增导入顶级任务摘要DTO
import com.dataxai.domain.dto.TaskSummaryDTO;
import com.dataxai.domain.dto.WorkflowDetailDTO;
import com.dataxai.service.ITeamUserService;
import com.dataxai.service.IWorkflowService;
import com.dataxai.service.IWorkflowTemplateService;
import com.dataxai.web.domain.Task;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * 平台API-工作流接口
 */
@Api(tags = "平台API工作流接口")
@RestController
@RequestMapping("/platform/workflow/workflow")
public class PlatformWorkflowController extends BaseController {

    @Autowired
    private IWorkflowService workflowService;
    @Autowired
    private IWorkflowTemplateService workflowTemplateService;
    @Autowired
    private RiskDetectionImageUploadService riskDetectionImageUploadService;
    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;
    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;

    private boolean isTeamAdmin(Long userId) {
        TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
        return teamUser != null && Boolean.TRUE.equals(teamUser.getIsAdmin());
    }

    private boolean isUrlAccessible(String urlString) {
        if (urlString == null || urlString.trim().isEmpty()) {
            return false;
        }
        try {
            URL url = new URL(urlString.trim());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            connection.setInstanceFollowRedirects(true);
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            logger.warn("URL不可用: {}, 错误: {}", urlString, e.getMessage());
            return false;
        }
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId(TUser currentUser) {
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());

        Long teamId = currentUser.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 查询工作流列表（分页）
     */
    @ApiOperation("查询工作流列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(
            HttpServletRequest request,
            Workflow workflow
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            // 验证团队模式并使用当前登录用户所属的团队ID
            Long currentUserTeamId = validateAndGetTeamId(user);
            workflow.setTeamId(currentUserTeamId);
            Long userId = user.getUserId();
            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
            if (teamUser.getIsAdmin() != true){
                workflow.setUserId(userId);
            }
            startPage();
            List<Workflow> list = workflowService.selectWorkflowList(workflow);

            // 为每个工作流查询节点执行信息
            List<WorkflowDetailDTO> resultList = new ArrayList<>();
            for (Workflow wf : list) {
                WorkflowDetailDTO detailDTO = new WorkflowDetailDTO();
                // 复制工作流基本信息
                detailDTO.setId(wf.getId());
                detailDTO.setWorkflowName(wf.getWorkflowName());
                detailDTO.setTemplateId(wf.getTemplateId());
                detailDTO.setTeamId(wf.getTeamId());
                detailDTO.setUserId(wf.getUserId());
                detailDTO.setStatus(wf.getStatus());
                detailDTO.setCreateTime(wf.getCreateTime());
                detailDTO.setUpdateTime(wf.getUpdateTime());

                // 查询关联的模板信息
                if (wf.getTemplateId() != null) {
                    WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(wf.getTemplateId());
                    if (template != null) {
                        detailDTO.setTemplateName(template.getTemplateName());
                    }
                }

                // 查询节点执行记录列表
                List<WorkflowNodeExecution> nodeExecutionList = workflowNodeExecutionMapper.selectByWorkflowId(wf.getId());

                // 统计节点执行状态
                int totalNodes = nodeExecutionList.size();
                int pendingNodes = 0;
                int runningNodes = 0;
                int completedNodes = 0;
                int failedNodes = 0;

                for (WorkflowNodeExecution nodeExecution : nodeExecutionList) {
                    Integer status = nodeExecution.getStatus();
                    if (status == null || status == 0) {
                        pendingNodes++;
                    } else if (status == 1) {
                        runningNodes++;
                    } else if (status == 2) {
                        completedNodes++;
                    } else if (status == 3) {
                        failedNodes++;
                    }
                }

                detailDTO.setNodeExecutionList(nodeExecutionList);
                detailDTO.setTotalNodes(totalNodes);
                detailDTO.setPendingNodes(pendingNodes);
                detailDTO.setRunningNodes(runningNodes);
                detailDTO.setCompletedNodes(completedNodes);
                detailDTO.setFailedNodes(failedNodes);

                resultList.add(detailDTO);
            }

            // 组装自定义分页结构
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", new PageInfo<>(list).getTotal());
            pageData.put("data", resultList);
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流详细信息
     */
    @ApiOperation("获取工作流详细信息")
    @GetMapping(value = "/{id}")
    public R<Workflow> getInfo(HttpServletRequest request, @ApiParam("工作流ID") @PathVariable("id") Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            Workflow workflow = workflowService.selectWorkflowById(id);
            if (workflow == null) {
                return R.fail("工作流不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(workflow.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限访问此工作流");
                }
                if (!admin && !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            } else {
                if (!Objects.equals(workflow.getTeamId(), 0L) || !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            }

            return R.ok(workflow);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流详细信息（包含素材和节点执行记录）
     */
    @ApiOperation("获取工作流详细信息（包含素材和节点执行记录）")
    @GetMapping(value = "/details/{id}")
    public R<Object> getDetails(HttpServletRequest request, @ApiParam("工作流ID") @PathVariable("id") Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            Workflow workflow = workflowService.selectWorkflowById(id);
            if (workflow == null) {
                return R.fail("工作流不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(workflow.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限访问此工作流");
                }
                if (!admin && !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            } else {
                if (!Objects.equals(workflow.getTeamId(), 0L) || !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            }

            Object result = workflowService.selectWorkflowWithDetails(id);

            if (result instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resultMap = (Map<String, Object>) result;
                @SuppressWarnings("unchecked")
                List<WorkflowNodeTaskDTO> nodeTaskList = (List<WorkflowNodeTaskDTO>) resultMap.get("nodeTaskList");
                if (nodeTaskList != null) {
                    for (WorkflowNodeTaskDTO nodeTask : nodeTaskList) {
                        Integer taskType = nodeTask.getTaskType();
                        if (taskType != null && (taskType == 6 || taskType == 7 || taskType == 8 ||
                                taskType == 9 || taskType == 11 || taskType == 12 || taskType == 14)) {
                            try {
                                List<Task> tasks = new ArrayList<>();
                                WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(nodeTask.getNodeExecutionId());
                                if (nodeExecution != null && nodeExecution.getBatchId() != null && nodeExecution.getBatchId() > 0) {
                                    Task taskQuery = new Task();
                                    taskQuery.setBatchId(nodeExecution.getBatchId().toString());
                                    tasks = taskMapper.selectTaskList(taskQuery);
                                }
                                List<TaskSummaryDTO> taskSummaryList = nodeTask.getTaskList();
                                if (taskSummaryList == null) {
                                    taskSummaryList = new ArrayList<>();
                                    nodeTask.setTaskList(taskSummaryList);
                                }
                                for (Task task : tasks) {
                                    TaskSummaryDTO dto = new TaskSummaryDTO();
                                    dto.setTaskId(task.getTaskId());
                                    dto.setTaskName(task.getTaskName());
                                    dto.setTaskType(taskType);
                                    dto.setStatus(task.getStatus().intValue());
                                    dto.setStatusDesc(getTaskStatusDescription(task.getStatus().intValue()));
                                    dto.setCreateTime(task.getCreateTime());
                                    dto.setUpdateTime(task.getUpdateTime());
                                    dto.setOriginalUrl(task.getOriginalUrl());
                                    dto.setThumbnailUrl(task.getThumbnailUrl());
                                    dto.setBatchId(task.getBatchId());
                                    taskSummaryList.add(dto);
                                }
                                nodeTask.setTaskStatistics(calculateTaskStatistics(taskSummaryList));
                            } catch (Exception e) {
                                logger.warn("查询节点{}的t_task任务失败: {}", nodeTask.getNodeExecutionId(), e.getMessage());
                            }
                        }
                    }
                }
            }

            return R.ok(result);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建工作流（通过文件上传）
     */
    @ApiOperation("创建工作流（通过文件上传）")
    @Log(title = "平台API-创建工作流（文件）", businessType = BusinessType.INSERT)
    @PostMapping("/createByFiles")
    public R<Map<String, Object>> createByFiles(
            HttpServletRequest request,
            @ApiParam("模板ID") @RequestParam("templateId") Long templateId,
            @ApiParam("素材文件") @RequestParam(value = "files", required = false) List<MultipartFile> files,
            @ApiParam("素材文件数组") @RequestParam(value = "files[]", required = false) List<MultipartFile> filesArray
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }

            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(templateId);
            if (template == null) {
                return R.fail("指定的工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;
            if (!Objects.equals(template.getTeamId(), resolvedTeamId)) {
                return R.fail("无权使用该模板");
            }

            List<MultipartFile> fileList = files != null && !files.isEmpty() ? files : filesArray;
            if (fileList == null || fileList.isEmpty()) {
                return R.fail("素材文件不能为空");
            }

            logger.info("开始通过文件创建工作流，文件数量: {}", fileList.size());
            List<String> imageUrls = riskDetectionImageUploadService.uploadImages(fileList);
            logger.info("图片上传完成，成功上传 {} 个文件", imageUrls.size());
            if (imageUrls.isEmpty()) {
                logger.error("没有图片上传成功，无法创建工作流");
                return R.fail("图片上传失败，无法创建工作流");
            }

            Workflow workflow = new Workflow();
            workflow.setWorkflowName(generateWorkflowName());
            workflow.setTemplateId(templateId);
            workflow.setTeamId(resolvedTeamId);
            workflow.setUserId(user.getUserId());

            int result = workflowService.createWorkflowByUrls(workflow, imageUrls);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowId", workflow.getId());
            resultData.put("workflowName", workflow.getWorkflowName());
            resultData.put("totalFiles", fileList.size());
            resultData.put("uploadedImages", imageUrls.size());

            return result > 0 ? R.ok(resultData) : R.fail("创建失败");
        } catch (Exception e) {
            logger.error("通过文件创建工作流失败", e);
            return R.fail("创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建工作流（通过URL列表）
     */
    @ApiOperation("创建工作流（通过URL列表）")
    @Log(title = "平台API-创建工作流（URL）", businessType = BusinessType.INSERT)
    @PostMapping("/createByUrls")
    public R<Map<String, Object>> createByUrls(HttpServletRequest request, @RequestBody CreateWorkflowRequest body) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (body.getTemplateId() == null) {
                return R.fail("模板ID不能为空");
            }
            if (body.getMaterialUrls() == null || body.getMaterialUrls().isEmpty()) {
                return R.fail("素材URL列表不能为空");
            }

            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(body.getTemplateId());
            if (template == null) {
                return R.fail("指定的工作流模板不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (info.isTeamMode() && info.getTeamId() != null) ? info.getTeamId() : 0L;
            if (!Objects.equals(template.getTeamId(), resolvedTeamId)) {
                return R.fail("无权使用该模板");
            }

            List<String> validUrls = new ArrayList<>();
            List<String> invalidUrls = new ArrayList<>();
            for (String url : body.getMaterialUrls()) {
                if (url != null && !url.trim().isEmpty()) {
                    String trimmedUrl = url.trim();
                    if (isUrlAccessible(trimmedUrl)) {
                        validUrls.add(trimmedUrl);
                    } else {
                        invalidUrls.add(trimmedUrl);
                    }
                }
            }
            if (validUrls.isEmpty()) {
                return R.fail("没有可用的素材URL");
            }

            List<String> processedUrls = riskDetectionImageUploadService.processImageUrls(validUrls);
            if (processedUrls.isEmpty()) {
                return R.fail("图片预处理失败，没有可用的图片");
            }

            Workflow workflow = new Workflow();
            workflow.setWorkflowName(generateWorkflowName());
            workflow.setTemplateId(body.getTemplateId());
            workflow.setTeamId(resolvedTeamId);
            workflow.setUserId(user.getUserId());

            int result = workflowService.createWorkflowByUrls(workflow, processedUrls);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowId", workflow.getId());
            resultData.put("workflowName", workflow.getWorkflowName());
            resultData.put("totalUrls", body.getMaterialUrls().size());
            resultData.put("validUrls", validUrls.size());
            resultData.put("processedUrls", processedUrls.size());
            resultData.put("invalidUrls", invalidUrls.size());
            resultData.put("failedProcessUrls", validUrls.size() - processedUrls.size());
            if (!invalidUrls.isEmpty()) {
                resultData.put("invalidUrlList", invalidUrls);
            }

            return result > 0 ? R.ok(resultData) : R.fail("创建失败");
        } catch (Exception e) {
            logger.error("通过URL创建工作流失败", e);
            return R.fail("创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流执行进度
     */
    @ApiOperation("获取工作流执行进度")
    @GetMapping("/progress/{id}")
    public R<Object> getProgress(HttpServletRequest request, @ApiParam("工作流ID") @PathVariable("id") Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            Workflow workflow = workflowService.selectWorkflowById(id);
            if (workflow == null) {
                return R.fail("工作流不存在");
            }

            UserTeamInfoDTO info = userTeamInfoService.getUserTeamInfo(user.getUserId());
            boolean admin = isTeamAdmin(user.getUserId());
            if (info.isTeamMode() && info.getTeamId() != null) {
                if (!Objects.equals(workflow.getTeamId(), info.getTeamId())) {
                    return R.fail("无权限访问此工作流");
                }
                if (!admin && !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            } else {
                if (!Objects.equals(workflow.getTeamId(), 0L) || !Objects.equals(workflow.getUserId(), user.getUserId())) {
                    return R.fail("无权限访问此工作流");
                }
            }

            Object progress = workflowService.getWorkflowProgress(id);

            if (progress instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> progressMap = (Map<String, Object>) progress;
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> nodeProgressList = (List<Map<String, Object>>) progressMap.get("nodeProgressList");
                if (nodeProgressList != null) {
                    for (Map<String, Object> nodeProgress : nodeProgressList) {
                        Integer taskType = (Integer) nodeProgress.get("taskType");
                        Long nodeExecutionId = (Long) nodeProgress.get("nodeExecutionId");
                        if (taskType != null && nodeExecutionId != null &&
                                (taskType == 6 || taskType == 7 || taskType == 8 || taskType == 9 || taskType == 11 || taskType == 12 || taskType == 14)) {
                            try {
                                List<Task> tasks = new ArrayList<>();
                                WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(nodeExecutionId);
                                if (nodeExecution != null && nodeExecution.getBatchId() != null && nodeExecution.getBatchId() > 0) {
                                    Task taskQuery = new Task();
                                    taskQuery.setBatchId(nodeExecution.getBatchId().toString());
                                    tasks = taskMapper.selectTaskList(taskQuery);
                                }
                                @SuppressWarnings("unchecked")
                                List<TaskSummaryDTO> taskList = (List<TaskSummaryDTO>) nodeProgress.get("taskList");
                                if (taskList == null) {
                                    taskList = new ArrayList<>();
                                    nodeProgress.put("taskList", taskList);
                                }
                                for (Task task : tasks) {
                                    TaskSummaryDTO dto = new TaskSummaryDTO();
                                    dto.setTaskId(task.getTaskId());
                                    dto.setTaskName(task.getTaskName());
                                    dto.setTaskType(taskType);
                                    dto.setStatus(task.getStatus().intValue());
                                    dto.setStatusDesc(getTaskStatusDescription(task.getStatus().intValue()));
                                    dto.setCreateTime(task.getCreateTime());
                                    dto.setUpdateTime(task.getUpdateTime());
                                    dto.setOriginalUrl(task.getOriginalUrl());
                                    dto.setThumbnailUrl(task.getThumbnailUrl());
                                    dto.setBatchId(task.getBatchId());
                                    taskList.add(dto);
                                }
                                TaskStatisticsDTO taskStats = calculateTaskStatistics(taskList);
                                progressMap.put("taskCount", taskStats.getTotalTasks());
                                nodeProgress.put("taskStatistics", taskStats);
                            } catch (Exception e) {
                                logger.warn("查询节点{}的t_task任务进度失败: {}", nodeExecutionId, e.getMessage());
                            }
                        }
                    }
                }
            }

            return R.ok(progress);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    private String generateWorkflowName() {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        String timestamp = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%03d", (int)(Math.random() * 1000));
        return "WF" + timestamp + random;
    }

    private String getTaskStatusDescription(int status) {
        switch (status) {
            case 1: return "成功";
            case 2: return "失败";
            case 3: return "执行中";
            case 4: return "排队中";
            default: return "未知状态(" + status + ")";
        }
    }

    private TaskStatisticsDTO calculateTaskStatistics(List<TaskSummaryDTO> taskList) {
        TaskStatisticsDTO statistics = new TaskStatisticsDTO();
        if (taskList == null || taskList.isEmpty()) {
            statistics.setTotalTasks(0);
            statistics.setPendingTasks(0);
            statistics.setRunningTasks(0);
            statistics.setCompletedTasks(0);
            statistics.setFailedTasks(0);
            statistics.setProgressPercentage(0.0);
            return statistics;
        }
        int total = taskList.size();
        int pending = 0;
        int running = 0;
        int completed = 0;
        int failed = 0;
        for (TaskSummaryDTO task : taskList) {
            Integer status = task.getStatus();
            if (status == null || status == 4) {
                pending++;
            } else if (status == 3) {
                running++;
            } else if (status == 1) {
                completed++;
            } else if (status == 2) {
                failed++;
            }
        }
        statistics.setTotalTasks(total);
        statistics.setPendingTasks(pending);
        statistics.setRunningTasks(running);
        statistics.setCompletedTasks(completed);
        statistics.setFailedTasks(failed);
        double progress = total > 0 ? (double) completed / total * 100 : 0.0;
        statistics.setProgressPercentage(Math.round(progress * 100.0) / 100.0);
        return statistics;
    }
}