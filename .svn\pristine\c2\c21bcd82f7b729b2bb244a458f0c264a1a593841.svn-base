import { useEffect, useState, useMemo } from 'react';
import { Modal, Button, Checkbox, Image, message, Spin, Tag, Input, Empty, Tabs, Table, Pagination, Select, Switch } from 'antd';
import { DoubleRightOutlined } from '@ant-design/icons';
import { useMemoizedFn } from 'ahooks'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import {
    getRiskFilterDetail,
    deleteRiskFilterDetail,
} from '@/api/task'
import { batchZipUrl } from '@/api/common'
import { useLocation, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import './index.scss';
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const FilterDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [messageApi, messageContextHolder] = message.useMessage()

    const [selectedIds, setSelectedIds] = useState<any[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    const { batchId } = location.state || {};

    const [record, setRecord] = useState<any>();
    const [pageLoading, setPageLoading] = useState(false);

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 200,
        total: 0,
        riskLevel: '' // 保存当前的风险等级筛选条件
    });

    const fetchDetail = (pageNum: any, riskLevel: any) => {
        setPageLoading(true)
        getRiskFilterDetail({ id: batchId, riskLevel, pageNum, pageSize: 200 }).then((res: any) => {
            setRecord(res)
            // 更新分页信息
            setPagination(prev => ({
                ...prev,
                current: pageNum,
                total: res.total || 0,
                riskLevel: riskLevel
            }));
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.msg || err?.data?.msg || ''}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }

    useEffect(() => {
        fetchDetail(1, '')
    }, []);

    useEffect(() => {
        if (userInfo?.currentMode == 2) {
            // 工作流功能已移至 BottomActionBar 组件
        }
    }, [userInfo?.currentMode]);

    const handleSelect = (row: any) => {
        setSelectedIds(prev => {
            const newSelected = prev.includes(row.id)
                ? prev.filter(id => id !== row.id)
                : [...prev, row.id];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });

        setSelectedRows(prev => {
            const newSelected = prev.includes(row)
                ? prev.filter(item => item.id !== row.id)
                : [...prev, row];
            return newSelected;
        });
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedIds([]); // 清空已选图片
        setSelectedRows([]);
    };

    const cancelSelection = () => {
        setSelectedIds([]);
        setSelectedRows([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };

    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        const imgUrls = selectedRows.map(item => item?.imageUrl);
        setDownloadLoading(true);
        batchZipUrl({ imageUrls: imgUrls, type: 17 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败:${err?.msg || err?.data?.msg || ''}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false); // 下载完成后重置加载状态
        })
    };

    const [deleteLoading, setDeleteLoading] = useState(false);
    // 删除图片
    const handleDelete = async () => {
        try {
            setDeleteLoading(true);
            await deleteRiskFilterDetail({ ids: selectedIds.join(',') });
            messageApi.success('删除成功');
            fetchDetail(pagination.current, pagination.riskLevel);
            setSelectedIds([]);
            setSelectedRows([]);
            setShowBatchActions(false);
        } catch (err: any) {
            messageApi.error(`删除失败: ${err?.data?.msg || err?.msg}`);
        } finally {
            setDeleteLoading(false);
        }
    };

    // 导出相关状态和函数已移至 BottomActionBar 组件内部

    const handlePageChange = (page: number, pageSize: number) => {
        fetchDetail(page, pagination.riskLevel);
    };


    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        type={image.id}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={image.imageUrl || ''}
                        oriImgUrl={image.imageUrl || ''}
                        smallImgUrl={image.imageUrl || ''}
                        markImgUrl={image.imageUrl || ''}
                        progress={1}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={true}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    const plainOptions = ['低风险', '中风险', '高风险'];
    const [checkedValues, setCheckedValues] = useState<string[]>([]);
    const onChange = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
        const riskLevels = checkedValues.map(value => {
            switch (value) {
                case '低风险': return '低';
                case '中风险': return '中';
                case '高风险': return '高';
                default: return '';
            }
        }).filter(Boolean).join(',');
        // 重置到第一页
        fetchDetail(1, riskLevels)
    };

    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : (<>
                <div className='w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal'>
                    <div className='flex items-center'>
                        <p className='mr-[20px]'>批次: {record?.batchNumber}</p>
                        <p className='mr-[20px]'>创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                        <p>总数：{record?.totalAmount}
                            <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.successAmount}</span>
                            {record?.failAmount > 0 && <span style={{ color: '#cf1322', marginLeft: '6px' }}>失败：{record?.failAmount}</span>}
                        </p>
                    </div>
                    <Button
                        type="primary"
                        onClick={toggleBatchActions}
                    >
                        {showBatchActions || selectedIds.length > 0 ? '取消批量操作' : '批量操作'}
                    </Button>
                </div>
                <Checkbox.Group style={{ marginTop: '10px' }} options={plainOptions} defaultValue={checkedValues} onChange={onChange} />
                <div className='bg-[#eee] w-full  mt-[10px] border border-normal  rounded-lg h-[calc(100vh-232px)] overflow-y-scroll scrollbar-container scrollbar-hide' style={{ scrollbarWidth: 'thin' }}>
                    {record?.detailList.length > 0 ? <div className="flex flex-wrap gap-[10px] p-[10px] pr-[0px]">
                        {record?.detailList.map((task: any, index: number) => (
                            <div className='h-[378px] flex align-center   rounded-lg justify-between bg-[#fff]  p-4 filter-img-box'>
                                <div key={index} className="flex-1  h-full flex  items-center justify-center  relative group bg-[#eef2ff] overflow-hidden">
                                    <Checkbox
                                        className="absolute top-4 left-4 z-10"
                                        style={{ transform: 'scale(1.25)' }}  // 放大1.25倍
                                        checked={selectedIds.includes(task.id)}
                                        onChange={() => handleSelect(task)}
                                        disabled={checkImageDisabled(task, 'filter')}
                                    />
                                    {task.hasUploaded && <p className="absolute bottom-4  z-10"
                                        style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                    {getTaskImageComponent(task)}
                                </div>
                                <div className='flex align-center justify-center text-[20px] text-[#666] m-3'><DoubleRightOutlined /></div>
                                <div className="flex-1 text-[12px]">
                                    <p className='flex align-center mb-3 mt-1'>
                                        <p className='font-bold'>侵权概率</p>
                                        <Tag style={{ marginLeft: '10px', padding: '0 10px' }} color={task.riskLevel == '低' ? 'green' : task.riskLevel == '中' ? 'orange' : 'red'} >{task.riskLevel}</Tag>
                                    </p>
                                    <div className='h-[306px] overflow-y-auto scrollbar-container scrollbar-hide' style={{ scrollbarWidth: 'thin' }}>
                                        <p className='font-bold'>检测元素</p>
                                        <div className='flex flex-wrap mt-2 mb-2 tag-box' style={{ gap: '8px 0' }}>
                                            {task?.elements && JSON.parse(task.elements).map((item: any) => (
                                                <Tag key={item} color="#507eab">{item}</Tag>
                                            ))}
                                        </div>
                                        <p className='font-bold'>建议</p>
                                        <p className='text-[12px]'>{task?.suggestion || '暂无'}</p>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div> : <div className='flex items-center w-full h-full justify-center'><Empty /></div>}
                    {record?.detailList.length > 0 && <Pagination
                        align="center" style={{ margin: '20px 0 120px' }}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        onChange={handlePageChange}
                        total={pagination.total}
                        showSizeChanger={false}
                    />}
                    {/* 底部操作栏 start */}
                    <BottomActionBar
                        visible={selectedIds.length > 0 || showBatchActions}
                        selectedCount={selectedIds.length}
                        isAllSelected={record?.detailList.length > 0 && record?.detailList.filter((item: any) => !checkImageDisabled(item, 'filter')).every((item: any) => selectedIds.includes(item.id))}
                        onToggleSelectAll={() => {
                            const enabledItems = record?.detailList.filter((item: any) => !checkImageDisabled(item, 'filter')) || [];
                            const enabledIds = enabledItems.map((item: any) => item.id);
                            const allEnabledSelected = enabledIds.every((id: any) => selectedIds.includes(id));

                            if (enabledItems.length > 0 && allEnabledSelected) {
                                // 取消全选
                                const newSelectedIds = selectedIds.filter(id => !enabledIds.includes(id));
                                const newSelectedRows = selectedRows.filter(row => !enabledIds.includes(row.id));
                                setSelectedIds(newSelectedIds);
                                setSelectedRows(newSelectedRows);
                            } else {
                                // 全选
                                const newSelectedRows = [...selectedRows];
                                const newSelectedIds = [...selectedIds];
                                enabledItems.forEach((item: any) => {
                                    if (!newSelectedIds.includes(item.id)) {
                                        newSelectedIds.push(item.id);
                                        newSelectedRows.push(item);
                                    }
                                });
                                setSelectedIds(newSelectedIds);
                                setSelectedRows(newSelectedRows);
                            }
                        }}
                        onCancelSelection={cancelSelection}

                        // 同步功能
                        syncEnabled={true}
                        selectedItems={selectedRows}
                        extractImageUrl={(item: any) => item?.imageUrl}
                        syncExtraParams={{ type: 17 }}

                        // 下载功能
                        onDownload={handleDownloadImgages}
                        downloadLoading={downloadLoading}
                        downloadDisabled={selectedIds.length === 0}

                        // 任务创建功能
                        enableWorkflow={userInfo?.currentMode == 2}

                        // 启用过滤页专用功能
                        enableGatherExport={true}
                        enableGatherExportTemplate={userInfo?.currentMode == 2}
                        enableGatherDelete={true}
                        extractGatherId={(item: any) => item.id}
                        extractGatherImageUrl={(item: any) => item.imageUrl}
                        extractGatherTitle={(item: any) => item.productTitle || ''}
                        onDelete={handleDelete}
                        deleteLoading={deleteLoading}

                        // 完成回调
                        onActionFinished={() => {
                            // 刷新积分或数据
                            userinfoService.refresh();
                            fetchDetail(pagination.current, pagination.riskLevel);
                            setSelectedIds([]);
                            setSelectedRows([]);
                            setShowBatchActions(false);
                        }}
                        exportType={2}
                        actionDisabled={selectedIds.length === 0}
                    />
                    {/* 底部操作栏 end */}
                </div>
            </>)}
        </div >
    );
};

export default FilterDetail;








