package com.dataxai.web.gtask.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;

/**
 * 图片处理工具类
 * 用于图片格式转换、尺寸调整和质量压缩
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Slf4j
@Component
public class ImageProcessUtils {

    /**
     * 下载图片并转换为WebP格式，最大边256px，质量80%
     *
     * @param imageUrl 原始图片URL
     * @param taskType 任务类型（用于日志）
     * @return 处理后的图片字节数组，失败返回null
     */
    public byte[] downloadAndProcessImage(String imageUrl, String taskType) {
        try {
            log.info("开始下载并处理图片: {}, 任务类型: {}", imageUrl, taskType);

            // 1. 下载原始图片
            byte[] originalImageData = downloadImage(imageUrl);
            if (originalImageData == null) {
                log.error("下载图片失败: {}, 任务类型: {}", imageUrl, taskType);
                return null;
            }

            // 2. 处理图片（调整尺寸、转换格式、压缩质量）
            byte[] processedImageData = processImage(originalImageData, 256, 80);
            if (processedImageData == null) {
                log.error("处理图片失败: {}, 任务类型: {}", imageUrl, taskType);
                return null;
            }

            log.info("图片处理完成，任务类型: {}, 原始大小: {} bytes, 处理后大小: {} bytes",
                taskType, originalImageData.length, processedImageData.length);

            return processedImageData;

        } catch (Exception e) {
            log.error("下载并处理图片异常: {}, 任务类型: {}", imageUrl, taskType, e);
            return null;
        }
    }

    /**
     * 下载图片
     *
     * @param imageUrl 图片URL
     * @return 图片字节数组
     */
    private byte[] downloadImage(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream()) {
                    // 使用ByteArrayOutputStream读取所有字节（兼容Java 8）
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    return buffer.toByteArray();
                }
            } else {
                log.error("下载图片失败，响应码: {}", responseCode);
                return null;
            }
        } catch (Exception e) {
            log.error("下载图片异常: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 处理图片：调整尺寸、转换格式、压缩质量
     *
     * @param imageData 原始图片数据
     * @param maxSize 最大边长
     * @param quality 质量（0-100）
     * @return 处理后的图片数据
     */
    private byte[] processImage(byte[] imageData, int maxSize, int quality) {
        try {
            // 1. 读取原始图片
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (originalImage == null) {
                log.error("无法读取图片数据");
                return null;
            }

            // 2. 计算新尺寸（保持宽高比）
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            
            int newWidth, newHeight;
            if (originalWidth > originalHeight) {
                // 宽度较大，以宽度为准
                newWidth = Math.min(originalWidth, maxSize);
                newHeight = (int) ((double) originalHeight * newWidth / originalWidth);
            } else {
                // 高度较大，以高度为准
                newHeight = Math.min(originalHeight, maxSize);
                newWidth = (int) ((double) originalWidth * newHeight / originalHeight);
            }

            // 3. 调整图片尺寸
            BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            
            // 设置高质量渲染
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
            g2d.dispose();

            // 4. 转换为JPEG格式并压缩（WebP在Java中支持有限，使用JPEG替代）
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 获取JPEG写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
            if (!writers.hasNext()) {
                log.error("系统不支持JPEG格式");
                return null;
            }
            
            ImageWriter writer = writers.next();
            ImageWriteParam param = writer.getDefaultWriteParam();
            
            // 设置压缩质量
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(quality / 100.0f);
            }
            
            try (ImageOutputStream ios = ImageIO.createImageOutputStream(outputStream)) {
                writer.setOutput(ios);
                writer.write(null, new javax.imageio.IIOImage(resizedImage, null, null), param);
            } finally {
                writer.dispose();
            }

            return outputStream.toByteArray();

        } catch (IOException e) {
            log.error("处理图片异常", e);
            return null;
        }
    }

    /**
     * 将处理后的图片数据转换为ByteArrayResource
     *
     * @param imageData 图片数据
     * @return ByteArrayResource
     */
    public ByteArrayResource createImageResource(byte[] imageData) {
        return new ByteArrayResource(imageData) {
            @Override
            public String getFilename() {
                return "processed_image_" + System.currentTimeMillis() + ".jpg";
            }
        };
    }

    /**
     * 验证图片URL格式
     *
     * @param imageUrl 图片URL
     * @return 是否为有效的图片URL
     */
    public boolean isValidImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return false;
        }

        // 检查是否以http或https开头
        if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
            return false;
        }

        // 检查是否包含图片扩展名
        String lowerUrl = imageUrl.toLowerCase();
        return lowerUrl.contains(".jpg") || lowerUrl.contains(".jpeg") || 
               lowerUrl.contains(".png") || lowerUrl.contains(".gif") || 
               lowerUrl.contains(".bmp") || lowerUrl.contains(".webp");
    }
}
