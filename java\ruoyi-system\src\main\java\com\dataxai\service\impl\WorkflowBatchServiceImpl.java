package com.dataxai.service.impl;

import com.dataxai.service.IWorkflowBatchService;
import com.dataxai.service.IWorkflowTaskCreationService;
import com.dataxai.domain.WorkflowNodeExecution;
import com.dataxai.domain.WorkflowMaterial;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.dataxai.mapper.WorkflowMaterialMapper;

import com.dataxai.common.utils.DateUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 工作流批次管理服务实现
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class WorkflowBatchServiceImpl implements IWorkflowBatchService
{
    private static final Logger logger = LoggerFactory.getLogger(WorkflowBatchServiceImpl.class);

    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;

    @Autowired
    private WorkflowMaterialMapper workflowMaterialMapper;

    @Autowired
    private IWorkflowTaskCreationService workflowTaskCreationService;



    /**
     * 为工作流节点创建批次
     */
    @Override
    @Transactional
    public String createBatchForWorkflowNode(WorkflowNodeExecution nodeExecution, List<String> materialUrls) {
        try {
            logger.info("开始为工作流节点创建批次，节点ID: {}, 任务类型: {}, 素材数量: {}",
                    nodeExecution.getId(), nodeExecution.getTaskType(), materialUrls.size());

            String batchId = null;
            Integer taskType = nodeExecution.getTaskType();

            // 根据任务类型调用不同的批次创建方法
            if (isMaterialCreationTask(taskType)) {
                // 素材创作类型(包含任务类型:6,7,8,9,11,12,14)
                batchId = createMaterialCreationBatch(nodeExecution, materialUrls);
            } else if (isRiskDetectionTask(taskType)) {
                // 风险检测类型(包含任务类型:17)
                batchId = createRiskDetectionBatch(nodeExecution, materialUrls);
            } else if (isTitleExtractionTask(taskType)) {
                // 标题提取类型(包含任务类型:18)
                batchId = createTitleExtractionBatch(nodeExecution, materialUrls);
            } else {
                throw new RuntimeException("不支持的任务类型: " + taskType);
            }

            // 更新工作流节点和批次的关联关系
            if (batchId != null) {
                updateNodeBatchRelation(nodeExecution.getId(), batchId);
                logger.info("工作流节点批次创建成功，节点ID: {}, 批次ID: {}", nodeExecution.getId(), batchId);
            }

            return batchId;

        } catch (Exception e) {
            logger.error("为工作流节点创建批次失败，节点ID: {}", nodeExecution.getId(), e);
            throw new RuntimeException("创建批次失败: " + e.getMessage());
        }
    }

    /**
     * 为工作流第一个节点创建批次
     */
    @Override
    @Transactional
    public String createFirstNodeBatch(WorkflowNodeExecution nodeExecution, Long workflowId) {
        try {
            logger.info("开始为工作流第一个节点创建批次，工作流ID: {}, 节点ID: {}", workflowId, nodeExecution.getId());

            // 获取工作流素材
            List<WorkflowMaterial> materials = workflowMaterialMapper.selectByWorkflowId(workflowId);
            List<String> materialUrls = materials.stream()
                    .map(WorkflowMaterial::getMaterialUrl)
                    .collect(Collectors.toList());

            if (materialUrls.isEmpty()) {
                throw new RuntimeException("工作流素材为空，无法创建批次");
            }

            return createBatchForWorkflowNode(nodeExecution, materialUrls);

        } catch (Exception e) {
            logger.error("为工作流第一个节点创建批次失败，工作流ID: {}, 节点ID: {}", workflowId, nodeExecution.getId(), e);
            throw new RuntimeException("创建第一个节点批次失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务类型获取素材URL列表
     */
    @Override
    public List<String> getMaterialUrlsByTaskType(WorkflowNodeExecution nodeExecution) {
        try {
            logger.info("根据任务类型获取素材URL列表，节点ID: {}, 任务类型: {}",
                    nodeExecution.getId(), nodeExecution.getTaskType());

            Integer taskType = nodeExecution.getTaskType();
            List<String> materialUrls = new ArrayList<>();

            if (isMaterialCreationTask(taskType)) {
                // 素材创作类型：从t_ordinal_img_result表获取
                materialUrls = getMaterialCreationTaskUrls(nodeExecution);
            } else if (isRiskDetectionTask(taskType)) {
                // 风险检测类型：从risk_detection_task_detail表获取
                materialUrls = getRiskDetectionTaskUrls(nodeExecution);
            } else if (isTitleExtractionTask(taskType)) {
                // 标题提取类型：从title_extraction_task_detail表获取
                materialUrls = getTitleExtractionTaskUrls(nodeExecution);
            } else {
                logger.warn("不支持的任务类型: {}", taskType);
            }

            logger.info("获取到素材URL数量: {}", materialUrls.size());
            return materialUrls;

        } catch (Exception e) {
            logger.error("根据任务类型获取素材URL列表失败，节点ID: {}", nodeExecution.getId(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新工作流节点和批次的关联关系
     */
    @Override
    @Transactional
    public void updateNodeBatchRelation(Long nodeExecutionId, String batchId) {
        try {
            logger.info("开始更新工作流节点批次关联，节点ID: {}, 批次ID: {}", nodeExecutionId, batchId);

            // 更新工作流节点的batch_id字段
            WorkflowNodeExecution nodeExecution = new WorkflowNodeExecution();
            nodeExecution.setId(nodeExecutionId);

            // 确保批次ID转换正确
            Long batchIdLong = Long.valueOf(batchId);
            nodeExecution.setBatchId(batchIdLong);
            nodeExecution.setUpdateTime(DateUtils.getNowDate());

            int updateCount = workflowNodeExecutionMapper.updateWorkflowNodeExecution(nodeExecution);

            if (updateCount > 0) {
                logger.info("更新工作流节点批次关联成功，节点ID: {}, 批次ID: {}, 更新记录数: {}",
                        nodeExecutionId, batchId, updateCount);

                // 验证更新结果
                WorkflowNodeExecution updatedNode = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(nodeExecutionId);
                if (updatedNode != null) {
                    logger.info("验证更新结果，节点ID: {}, 实际batch_id: {}",
                            nodeExecutionId, updatedNode.getBatchId());
                } else {
                    logger.warn("验证更新结果失败，未找到节点，节点ID: {}", nodeExecutionId);
                }
            } else {
                logger.warn("更新工作流节点批次关联失败，没有记录被更新，节点ID: {}, 批次ID: {}",
                        nodeExecutionId, batchId);
            }

        } catch (NumberFormatException e) {
            logger.error("批次ID格式错误，节点ID: {}, 批次ID: {}", nodeExecutionId, batchId, e);
            throw new RuntimeException("批次ID格式错误: " + batchId);
        } catch (Exception e) {
            logger.error("更新工作流节点批次关联失败，节点ID: {}, 批次ID: {}", nodeExecutionId, batchId, e);
            throw new RuntimeException("更新节点批次关联失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否为素材创作类型任务
     */
    private boolean isMaterialCreationTask(Integer taskType) {
        return taskType != null && (taskType == 6 || taskType == 7 || taskType == 8 ||
                taskType == 9 || taskType == 11 || taskType == 12 || taskType == 14 || taskType == 52);
    }

    /**
     * 判断是否为风险检测类型任务
     */
    private boolean isRiskDetectionTask(Integer taskType) {
        return taskType != null && taskType == 17;
    }

    /**
     * 判断是否为标题提取类型任务
     */
    private boolean isTitleExtractionTask(Integer taskType) {
        return taskType != null && taskType == 18;
    }

    /**
     * 创建素材创作类型批次
     */
    private String createMaterialCreationBatch(WorkflowNodeExecution nodeExecution, List<String> materialUrls) {
        try {
            logger.info("创建素材创作类型批次，任务类型: {}, 素材数量: {}", nodeExecution.getTaskType(), materialUrls.size());

            String batchId = workflowTaskCreationService.createMaterialCreationBatch(
                    nodeExecution.getTaskType(),
                    materialUrls,
                    nodeExecution.getId()
            );

            logger.info("素材创作类型批次创建成功，批次ID: {}", batchId);
            return batchId;

        } catch (Exception e) {
            logger.error("创建素材创作类型批次失败，节点ID: {}", nodeExecution.getId(), e);
            throw new RuntimeException("创建素材创作批次失败: " + e.getMessage());
        }
    }

    /**
     * 创建风险检测类型批次
     */
    private String createRiskDetectionBatch(WorkflowNodeExecution nodeExecution, List<String> materialUrls) {
        try {
            logger.info("创建风险检测类型批次，任务类型: {}, 素材数量: {}", nodeExecution.getTaskType(), materialUrls.size());

            String taskId = workflowTaskCreationService.createRiskDetectionTask(
                    materialUrls,
                    nodeExecution.getId()
            );

            logger.info("风险检测类型任务创建成功，任务ID: {}", taskId);
            return taskId;

        } catch (Exception e) {
            logger.error("创建风险检测类型任务失败，节点ID: {}", nodeExecution.getId(), e);
            throw new RuntimeException("创建风险检测任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建标题提取类型批次
     */
    private String createTitleExtractionBatch(WorkflowNodeExecution nodeExecution, List<String> materialUrls) {
        try {
            logger.info("创建标题提取类型批次，任务类型: {}, 素材数量: {}", nodeExecution.getTaskType(), materialUrls.size());

            String taskId = workflowTaskCreationService.createTitleExtractionTask(
                    materialUrls,
                    nodeExecution.getId()
            );

            logger.info("标题提取类型任务创建成功，任务ID: {}", taskId);
            return taskId;

        } catch (Exception e) {
            logger.error("创建标题提取类型任务失败，节点ID: {}", nodeExecution.getId(), e);
            throw new RuntimeException("创建标题提取任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取素材创作类型任务的素材URL列表
     */
    private List<String> getMaterialCreationTaskUrls(WorkflowNodeExecution nodeExecution) {
        try {
            // 根据文档：先通过workflow_node_execution表关联到t_batch表，再关联到t_task表，
            // 再关联到t_task_ordinal表，最后关联到t_ordinal_img_result表，取字段"res_img_url"的值

            // 由于我们现在有batch_id字段，可以直接通过batch_id查询
            if (nodeExecution.getBatchId() == null || nodeExecution.getBatchId() == 0) {
                logger.warn("节点没有关联的批次ID，无法获取素材，节点ID: {}", nodeExecution.getId());
                return new ArrayList<>();
            }

            // 这里需要实现通过batch_id查询t_ordinal_img_result的逻辑
            // 由于OrdinalImgResultMapper在web模块中，我们暂时返回空列表
            // 在实际实现中需要注入OrdinalImgResultMapper并调用selectImageResult方法
            logger.info("获取素材创作类型任务的素材URL，批次ID: {}", nodeExecution.getBatchId());
            return new ArrayList<>();

        } catch (Exception e) {
            logger.error("获取素材创作类型任务的素材URL失败，节点ID: {}", nodeExecution.getId(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取风险检测类型任务的素材URL列表
     */
    private List<String> getRiskDetectionTaskUrls(WorkflowNodeExecution nodeExecution) {
        try {
            // 由于风险检测任务现在不直接关联工作流节点，而是通过批次关联
            // 我们需要通过其他方式来获取素材
            // 这里暂时返回空列表，实际实现需要根据具体的业务逻辑来确定
            logger.info("获取风险检测类型任务的素材URL，节点ID: {}", nodeExecution.getId());
            return new ArrayList<>();

        } catch (Exception e) {
            logger.error("获取风险检测类型任务的素材URL失败，节点ID: {}", nodeExecution.getId(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取标题提取类型任务的素材URL列表
     */
    private List<String> getTitleExtractionTaskUrls(WorkflowNodeExecution nodeExecution) {
        try {
            // 由于标题提取任务现在不直接关联工作流节点，而是通过批次关联
            // 我们需要通过其他方式来获取素材
            // 这里暂时返回空列表，实际实现需要根据具体的业务逻辑来确定
            logger.info("获取标题提取类型任务的素材URL，节点ID: {}", nodeExecution.getId());
            return new ArrayList<>();

        } catch (Exception e) {
            logger.error("获取标题提取类型任务的素材URL失败，节点ID: {}", nodeExecution.getId(), e);
            return new ArrayList<>();
        }
    }
}
