/*! For license information please see 823.0e83f83b.chunk.js.LICENSE.txt */
(self.webpackChunkai_console=self.webpackChunkai_console||[]).push([[823],{772:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M676 565c-50.8 0-92 41.2-92 92s41.2 92 92 92 92-41.2 92-92-41.2-92-92-92zm0 126c-18.8 0-34-15.2-34-34s15.2-34 34-34 34 15.2 34 34-15.2 34-34 34zm204-523H668c0-30.9-25.1-56-56-56h-80c-30.9 0-56 25.1-56 56H264c-17.7 0-32 14.3-32 32v200h-88c-17.7 0-32 14.3-32 32v448c0 17.7 14.3 32 32 32h336c17.7 0 32-14.3 32-32v-16h368c17.7 0 32-14.3 32-32V200c0-17.7-14.3-32-32-32zm-412 64h72v-56h64v56h72v48H468v-48zm-20 616H176V616h272v232zm0-296H176v-88h272v88zm392 240H512V432c0-17.7-14.3-32-32-32H304V240h100v104h336V240h100v552zM704 408v96c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-96c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zM592 512h48c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8z"}}]},name:"reconciliation",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},3993:function(t,e,i){"use strict";function n(t,e){if(t===e)return!0;for(var i=0;i<t.length;i++)if(!Object.is(t[i],e[i]))return!1;return!0}i.d(e,{A:function(){return n}})},8619:function(t,e,i){"use strict";var n=i(9939);e.A=function(){return n.createElement("svg",{width:"251",height:"294"},n.createElement("title",null,"Unauthorized"),n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("path",{d:"M0 129.023v-2.084C0 58.364 55.591 2.774 124.165 2.774h2.085c68.574 0 124.165 55.59 124.165 124.165v2.084c0 68.575-55.59 124.166-124.165 124.166h-2.085C55.591 253.189 0 197.598 0 129.023",fill:"#E4EBF7"}),n.createElement("path",{d:"M41.417 132.92a8.231 8.231 0 1 1-16.38-1.65 8.231 8.231 0 0 1 16.38 1.65",fill:"#FFF"}),n.createElement("path",{d:"M38.652 136.36l10.425 5.91M49.989 148.505l-12.58 10.73",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{d:"M41.536 161.28a5.636 5.636 0 1 1-11.216-1.13 5.636 5.636 0 0 1 11.216 1.13M59.154 145.261a5.677 5.677 0 1 1-11.297-1.138 5.677 5.677 0 0 1 11.297 1.138M100.36 29.516l29.66-.013a4.562 4.562 0 1 0-.004-9.126l-29.66.013a4.563 4.563 0 0 0 .005 9.126M111.705 47.754l29.659-.013a4.563 4.563 0 1 0-.004-9.126l-29.66.013a4.563 4.563 0 1 0 .005 9.126",fill:"#FFF"}),n.createElement("path",{d:"M114.066 29.503V29.5l15.698-.007a4.563 4.563 0 1 0 .004 9.126l-15.698.007v-.002a4.562 4.562 0 0 0-.004-9.122M185.405 137.723c-.55 5.455-5.418 9.432-10.873 8.882-5.456-.55-9.432-5.418-8.882-10.873.55-5.455 5.418-9.432 10.873-8.882 5.455.55 9.432 5.418 8.882 10.873",fill:"#FFF"}),n.createElement("path",{d:"M180.17 143.772l12.572 7.129M193.841 158.42L178.67 171.36",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{d:"M185.55 171.926a6.798 6.798 0 1 1-13.528-1.363 6.798 6.798 0 0 1 13.527 1.363M204.12 155.285a6.848 6.848 0 1 1-13.627-1.375 6.848 6.848 0 0 1 13.626 1.375",fill:"#FFF"}),n.createElement("path",{d:"M152.988 194.074a2.21 2.21 0 1 1-4.42 0 2.21 2.21 0 0 1 4.42 0zM225.931 118.217a2.21 2.21 0 1 1-4.421 0 2.21 2.21 0 0 1 4.421 0zM217.09 153.051a2.21 2.21 0 1 1-4.421 0 2.21 2.21 0 0 1 4.42 0zM177.84 109.842a2.21 2.21 0 1 1-4.422 0 2.21 2.21 0 0 1 4.421 0zM196.114 94.454a2.21 2.21 0 1 1-4.421 0 2.21 2.21 0 0 1 4.421 0zM202.844 182.523a2.21 2.21 0 1 1-4.42 0 2.21 2.21 0 0 1 4.42 0z",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{stroke:"#FFF",strokeWidth:"2",d:"M215.125 155.262l-1.902 20.075-10.87 5.958M174.601 176.636l-6.322 9.761H156.98l-4.484 6.449M175.874 127.28V111.56M221.51 119.404l-12.77 7.859-15.228-7.86V96.668"}),n.createElement("path",{d:"M180.68 29.32C180.68 13.128 193.806 0 210 0c16.193 0 29.32 13.127 29.32 29.32 0 16.194-13.127 29.322-29.32 29.322-16.193 0-29.32-13.128-29.32-29.321",fill:"#A26EF4"}),n.createElement("path",{d:"M221.45 41.706l-21.563-.125a1.744 1.744 0 0 1-1.734-1.754l.071-12.23a1.744 1.744 0 0 1 1.754-1.734l21.562.125c.964.006 1.74.791 1.735 1.755l-.071 12.229a1.744 1.744 0 0 1-1.754 1.734",fill:"#FFF"}),n.createElement("path",{d:"M215.106 29.192c-.015 2.577-2.049 4.654-4.543 4.64-2.494-.014-4.504-2.115-4.489-4.693l.04-6.925c.016-2.577 2.05-4.654 4.543-4.64 2.494.015 4.504 2.116 4.49 4.693l-.04 6.925zm-4.53-14.074a6.877 6.877 0 0 0-6.916 6.837l-.043 7.368a6.877 6.877 0 0 0 13.754.08l.042-7.368a6.878 6.878 0 0 0-6.837-6.917zM167.566 68.367h-3.93a4.73 4.73 0 0 1-4.717-4.717 4.73 4.73 0 0 1 4.717-4.717h3.93a4.73 4.73 0 0 1 4.717 4.717 4.73 4.73 0 0 1-4.717 4.717",fill:"#FFF"}),n.createElement("path",{d:"M168.214 248.838a6.611 6.611 0 0 1-6.61-6.611v-66.108a6.611 6.611 0 0 1 13.221 0v66.108a6.611 6.611 0 0 1-6.61 6.61",fill:"#5BA02E"}),n.createElement("path",{d:"M176.147 248.176a6.611 6.611 0 0 1-6.61-6.61v-33.054a6.611 6.611 0 1 1 13.221 0v33.053a6.611 6.611 0 0 1-6.61 6.611",fill:"#92C110"}),n.createElement("path",{d:"M185.994 293.89h-27.376a3.17 3.17 0 0 1-3.17-3.17v-45.887a3.17 3.17 0 0 1 3.17-3.17h27.376a3.17 3.17 0 0 1 3.17 3.17v45.886a3.17 3.17 0 0 1-3.17 3.17",fill:"#F2D7AD"}),n.createElement("path",{d:"M81.972 147.673s6.377-.927 17.566-1.28c11.729-.371 17.57 1.086 17.57 1.086s3.697-3.855.968-8.424c1.278-12.077 5.982-32.827.335-48.273-1.116-1.339-3.743-1.512-7.536-.62-1.337.315-7.147-.149-7.983-.1l-15.311-.347s-3.487-.17-8.035-.508c-1.512-.113-4.227-1.683-5.458-.338-.406.443-2.425 5.669-1.97 16.077l8.635 35.642s-3.141 3.61 1.219 7.085",fill:"#FFF"}),n.createElement("path",{d:"M75.768 73.325l-.9-6.397 11.982-6.52s7.302-.118 8.038 1.205c.737 1.324-5.616.993-5.616.993s-1.836 1.388-2.615 2.5c-1.654 2.363-.986 6.471-8.318 5.986-1.708.284-2.57 2.233-2.57 2.233",fill:"#FFC6A0"}),n.createElement("path",{d:"M52.44 77.672s14.217 9.406 24.973 14.444c1.061.497-2.094 16.183-11.892 11.811-7.436-3.318-20.162-8.44-21.482-14.496-.71-3.258 2.543-7.643 8.401-11.76M141.862 80.113s-6.693 2.999-13.844 6.876c-3.894 2.11-10.137 4.704-12.33 7.988-6.224 9.314 3.536 11.22 12.947 7.503 6.71-2.651 28.999-12.127 13.227-22.367",fill:"#FFB594"}),n.createElement("path",{d:"M76.166 66.36l3.06 3.881s-2.783 2.67-6.31 5.747c-7.103 6.195-12.803 14.296-15.995 16.44-3.966 2.662-9.754 3.314-12.177-.118-3.553-5.032.464-14.628 31.422-25.95",fill:"#FFC6A0"}),n.createElement("path",{d:"M64.674 85.116s-2.34 8.413-8.912 14.447c.652.548 18.586 10.51 22.144 10.056 5.238-.669 6.417-18.968 1.145-20.531-.702-.208-5.901-1.286-8.853-2.167-.87-.26-1.611-1.71-3.545-.936l-1.98-.869zM128.362 85.826s5.318 1.956 7.325 13.734c-.546.274-17.55 12.35-21.829 7.805-6.534-6.94-.766-17.393 4.275-18.61 4.646-1.121 5.03-1.37 10.23-2.929",fill:"#FFF"}),n.createElement("path",{d:"M78.18 94.656s.911 7.41-4.914 13.078",stroke:"#E4EBF7",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M87.397 94.68s3.124 2.572 10.263 2.572c7.14 0 9.074-3.437 9.074-3.437",stroke:"#E4EBF7",strokeWidth:".932",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M117.184 68.639l-6.781-6.177s-5.355-4.314-9.223-.893c-3.867 3.422 4.463 2.083 5.653 4.165 1.19 2.082.848 1.143-2.083.446-5.603-1.331-2.082.893 2.975 5.355 2.091 1.845 6.992.955 6.992.955l2.467-3.851z",fill:"#FFC6A0"}),n.createElement("path",{d:"M105.282 91.315l-.297-10.937-15.918-.027-.53 10.45c-.026.403.17.788.515.999 2.049 1.251 9.387 5.093 15.799.424.287-.21.443-.554.431-.91",fill:"#FFB594"}),n.createElement("path",{d:"M107.573 74.24c.817-1.147.982-9.118 1.015-11.928a1.046 1.046 0 0 0-.965-1.055l-4.62-.365c-7.71-1.044-17.071.624-18.253 6.346-5.482 5.813-.421 13.244-.421 13.244s1.963 3.566 4.305 6.791c.756 1.041.398-3.731 3.04-5.929 5.524-4.594 15.899-7.103 15.899-7.103",fill:"#5C2552"}),n.createElement("path",{d:"M88.426 83.206s2.685 6.202 11.602 6.522c7.82.28 8.973-7.008 7.434-17.505l-.909-5.483c-6.118-2.897-15.478.54-15.478.54s-.576 2.044-.19 5.504c-2.276 2.066-1.824 5.618-1.824 5.618s-.905-1.922-1.98-2.321c-.86-.32-1.897.089-2.322 1.98-1.04 4.632 3.667 5.145 3.667 5.145",fill:"#FFC6A0"}),n.createElement("path",{stroke:"#DB836E",strokeWidth:"1.145",strokeLinecap:"round",strokeLinejoin:"round",d:"M100.843 77.099l1.701-.928-1.015-4.324.674-1.406"}),n.createElement("path",{d:"M105.546 74.092c-.022.713-.452 1.279-.96 1.263-.51-.016-.904-.607-.882-1.32.021-.713.452-1.278.96-1.263.51.016.904.607.882 1.32M97.592 74.349c-.022.713-.452 1.278-.961 1.263-.509-.016-.904-.607-.882-1.32.022-.713.452-1.279.961-1.263.51.016.904.606.882 1.32",fill:"#552950"}),n.createElement("path",{d:"M91.132 86.786s5.269 4.957 12.679 2.327",stroke:"#DB836E",strokeWidth:"1.145",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M99.776 81.903s-3.592.232-1.44-2.79c1.59-1.496 4.897-.46 4.897-.46s1.156 3.906-3.457 3.25",fill:"#DB836E"}),n.createElement("path",{d:"M102.88 70.6s2.483.84 3.402.715M93.883 71.975s2.492-1.144 4.778-1.073",stroke:"#5C2552",strokeWidth:"1.526",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M86.32 77.374s.961.879 1.458 2.106c-.377.48-1.033 1.152-.236 1.809M99.337 83.719s1.911.151 2.509-.254",stroke:"#DB836E",strokeWidth:"1.145",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M87.782 115.821l15.73-3.012M100.165 115.821l10.04-2.008",stroke:"#E4EBF7",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M66.508 86.763s-1.598 8.83-6.697 14.078",stroke:"#E4EBF7",strokeWidth:"1.114",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M128.31 87.934s3.013 4.121 4.06 11.785",stroke:"#E4EBF7",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M64.09 84.816s-6.03 9.912-13.607 9.903",stroke:"#DB836E",strokeWidth:".795",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M112.366 65.909l-.142 5.32s5.993 4.472 11.945 9.202c4.482 3.562 8.888 7.455 10.985 8.662 4.804 2.766 8.9 3.355 11.076 1.808 4.071-2.894 4.373-9.878-8.136-15.263-4.271-1.838-16.144-6.36-25.728-9.73",fill:"#FFC6A0"}),n.createElement("path",{d:"M130.532 85.488s4.588 5.757 11.619 6.214",stroke:"#DB836E",strokeWidth:".75",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M121.708 105.73s-.393 8.564-1.34 13.612",stroke:"#E4EBF7",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M115.784 161.512s-3.57-1.488-2.678-7.14",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M101.52 290.246s4.326 2.057 7.408 1.03c2.842-.948 4.564.673 7.132 1.186 2.57.514 6.925 1.108 11.772-1.269-.104-5.551-6.939-4.01-12.048-6.763-2.582-1.39-3.812-4.757-3.625-8.863h-9.471s-1.402 10.596-1.169 14.68",fill:"#CBD1D1"}),n.createElement("path",{d:"M101.496 290.073s2.447 1.281 6.809.658c3.081-.44 3.74.485 7.479 1.039 3.739.554 10.802-.07 11.91-.9.415 1.108-.347 2.077-.347 2.077s-1.523.608-4.847.831c-2.045.137-5.843.293-7.663-.507-1.8-1.385-5.286-1.917-5.77-.243-3.947.958-7.41-.288-7.41-.288l-.16-2.667z",fill:"#2B0849"}),n.createElement("path",{d:"M108.824 276.19h3.116s-.103 6.751 4.57 8.62c-4.673.624-8.62-2.32-7.686-8.62",fill:"#A4AABA"}),n.createElement("path",{d:"M57.65 272.52s-2.122 7.47-4.518 12.396c-1.811 3.724-4.255 7.548 5.505 7.548 6.698 0 9.02-.483 7.479-6.648-1.541-6.164.268-13.296.268-13.296H57.65z",fill:"#CBD1D1"}),n.createElement("path",{d:"M51.54 290.04s2.111 1.178 6.682 1.178c6.128 0 8.31-1.662 8.31-1.662s.605 1.122-.624 2.18c-1 .862-3.624 1.603-7.444 1.559-4.177-.049-5.876-.57-6.786-1.177-.831-.554-.692-1.593-.138-2.078",fill:"#2B0849"}),n.createElement("path",{d:"M58.533 274.438s.034 1.529-.315 2.95c-.352 1.431-1.087 3.127-1.139 4.17-.058 1.16 4.57 1.592 5.194.035.623-1.559 1.303-6.475 1.927-7.306.622-.831-4.94-2.135-5.667.15",fill:"#A4AABA"}),n.createElement("path",{d:"M100.885 277.015l13.306.092s1.291-54.228 1.843-64.056c.552-9.828 3.756-43.13.997-62.788l-12.48-.64-22.725.776s-.433 3.944-1.19 9.921c-.062.493-.677.838-.744 1.358-.075.582.42 1.347.318 1.956-2.35 14.003-6.343 32.926-8.697 46.425-.116.663-1.227 1.004-1.45 2.677-.04.3.21 1.516.112 1.785-6.836 18.643-10.89 47.584-14.2 61.551l14.528-.014s2.185-8.524 4.008-16.878c2.796-12.817 22.987-84.553 22.987-84.553l3-.517 1.037 46.1s-.223 1.228.334 2.008c.558.782-.556 1.117-.39 2.233l.39 1.784s-.446 7.14-.892 11.826c-.446 4.685-.092 38.954-.092 38.954",fill:"#7BB2F9"}),n.createElement("path",{d:"M77.438 220.434c1.146.094 4.016-2.008 6.916-4.91M107.55 223.931s2.758-1.103 6.069-3.862",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M108.459 220.905s2.759-1.104 6.07-3.863",stroke:"#648BD8",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M76.099 223.557s2.608-.587 6.47-3.346M87.33 150.82c-.27 3.088.297 8.478-4.315 9.073M104.829 149.075s.11 13.936-1.286 14.983c-2.207 1.655-2.975 1.934-2.975 1.934M101.014 149.63s.035 12.81-1.19 24.245M94.93 174.965s7.174-1.655 9.38-1.655M75.671 204.754c-.316 1.55-.64 3.067-.973 4.535 0 0-1.45 1.822-1.003 3.756.446 1.934-.943 2.034-4.96 15.273-1.686 5.559-4.464 18.49-6.313 27.447-.078.38-4.018 18.06-4.093 18.423M77.043 196.743a313.269 313.269 0 0 1-.877 4.729M83.908 151.414l-1.19 10.413s-1.091.148-.496 2.23c.111 1.34-2.66 15.692-5.153 30.267M57.58 272.94h13.238",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M117.377 147.423s-16.955-3.087-35.7.199c.157 2.501-.002 4.128-.002 4.128s14.607-2.802 35.476-.31c.251-2.342.226-4.017.226-4.017",fill:"#192064"}),n.createElement("path",{d:"M107.511 150.353l.004-4.885a.807.807 0 0 0-.774-.81c-2.428-.092-5.04-.108-7.795-.014a.814.814 0 0 0-.784.81l-.003 4.88c0 .456.371.82.827.808a140.76 140.76 0 0 1 7.688.017.81.81 0 0 0 .837-.806",fill:"#FFF"}),n.createElement("path",{d:"M106.402 149.426l.002-3.06a.64.64 0 0 0-.616-.643 94.135 94.135 0 0 0-5.834-.009.647.647 0 0 0-.626.643l-.001 3.056c0 .36.291.648.651.64 1.78-.04 3.708-.041 5.762.012.36.009.662-.279.662-.64",fill:"#192064"}),n.createElement("path",{d:"M101.485 273.933h12.272M102.652 269.075c.006 3.368.04 5.759.11 6.47M102.667 263.125c-.009 1.53-.015 2.98-.016 4.313M102.204 174.024l.893 44.402s.669 1.561-.224 2.677c-.892 1.116 2.455.67.893 2.231-1.562 1.562.893 1.116 0 3.347-.592 1.48-.988 20.987-1.09 34.956",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"})))}},12373:function(t,e,i){"use strict";var n=i(66035),r=i(46415),o=i.n(r),s=i(9939),a=i(36514),c=i(54831),l=i(46499),h=i(13164);e.A=function(t,e){var i;h.A&&((0,l.Tn)(t)||console.error("useThrottleFn expected parameter is a function, got ".concat(typeof t)));var r=(0,a.A)(t),u=null!==(i=null===e||void 0===e?void 0:e.wait)&&void 0!==i?i:1e3,f=(0,s.useMemo)((function(){return o()((function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return r.current.apply(r,(0,n.fX)([],(0,n.zs)(t),!1))}),u,e)}),[]);return(0,c.A)((function(){f.cancel()})),{run:f,cancel:f.cancel,flush:f.flush}}},12563:function(t,e,i){"use strict";i.d(e,{Kq:function(){return c},Xr:function(){return p},fp:function(){return v},md:function(){return g}});var n=i(27569),r=i(9939),o=i(67867),s=(0,r.createContext)(void 0),a=function(t){var e=(0,r.useContext)(s);return(null==t?void 0:t.store)||e||(0,o.zp)()},c=function(t){var e=t.children,i=t.store,n=(0,r.useRef)(void 0);return i||n.current||(n.current=(0,o.y$)()),(0,r.createElement)(s.Provider,{value:i||n.current},e)},l=function(t){return"function"===typeof(null==t?void 0:t.then)},h=function(t){t.status="pending",t.then((function(e){t.status="fulfilled",t.value=e}),(function(e){t.status="rejected",t.reason=e}))},u=r.use||function(t){if("pending"===t.status)throw t;if("fulfilled"===t.status)return t.value;throw"rejected"===t.status?t.reason:(h(t),t)},f=new WeakMap,d=function(t){var e=f.get(t);return e||(e=new Promise((function(i,n){var r=t,o=function(t){return function(e){r===t&&i(e)}},s=function(t){return function(e){r===t&&n(e)}},a=function(t){"onCancel"in t&&"function"===typeof t.onCancel&&t.onCancel((function(n){if(n===t)throw new Error("[Bug] p is not updated even after cancelation");l(n)?(f.set(n,e),r=n,n.then(o(n),s(n)),a(n)):i(n)}))};t.then(o(t),s(t)),a(t)})),f.set(t,e)),e};function g(t,e){var i=a(e),o=(0,r.useReducer)((function(e){var n=i.get(t);return Object.is(e[0],n)&&e[1]===i&&e[2]===t?e:[n,i,t]}),void 0,(function(){return[i.get(t),i,t]})),s=(0,n.A)(o,2),c=(0,n.A)(s[0],3),f=c[0],g=c[1],p=c[2],v=s[1],m=f;g===i&&p===t||(v(),m=i.get(t));var y=null==e?void 0:e.delay;if((0,r.useEffect)((function(){var e=i.sub(t,(function(){if("number"===typeof y){var e=i.get(t);return l(e)&&h(d(e)),void setTimeout(v,y)}v()}));return v(),e}),[i,t,y]),(0,r.useDebugValue)(m),l(m)){var b=d(m);return u(b)}return m}function p(t,e){var i=a(e),n=(0,r.useCallback)((function(){if(!("write"in t))throw new Error("not writable atom");for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return i.set.apply(i,[t].concat(n))}),[i,t]);return n}function v(t,e){return[g(t,e),p(t,e)]}},16396:function(t,e,i){"use strict";var n=i(9939);e.A=function(){return n.createElement("svg",{width:"252",height:"294"},n.createElement("title",null,"No Found"),n.createElement("defs",null,n.createElement("path",{d:"M0 .387h251.772v251.772H0z"})),n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("g",{transform:"translate(0 .012)"},n.createElement("mask",{fill:"#fff"}),n.createElement("path",{d:"M0 127.32v-2.095C0 56.279 55.892.387 124.838.387h2.096c68.946 0 124.838 55.892 124.838 124.838v2.096c0 68.946-55.892 124.838-124.838 124.838h-2.096C55.892 252.16 0 196.267 0 127.321",fill:"#E4EBF7",mask:"url(#b)"})),n.createElement("path",{d:"M39.755 130.84a8.276 8.276 0 1 1-16.468-1.66 8.276 8.276 0 0 1 16.468 1.66",fill:"#FFF"}),n.createElement("path",{d:"M36.975 134.297l10.482 5.943M48.373 146.508l-12.648 10.788",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{d:"M39.875 159.352a5.667 5.667 0 1 1-11.277-1.136 5.667 5.667 0 0 1 11.277 1.136M57.588 143.247a5.708 5.708 0 1 1-11.358-1.145 5.708 5.708 0 0 1 11.358 1.145M99.018 26.875l29.82-.014a4.587 4.587 0 1 0-.003-9.175l-29.82.013a4.587 4.587 0 1 0 .003 9.176M110.424 45.211l29.82-.013a4.588 4.588 0 0 0-.004-9.175l-29.82.013a4.587 4.587 0 1 0 .004 9.175",fill:"#FFF"}),n.createElement("path",{d:"M112.798 26.861v-.002l15.784-.006a4.588 4.588 0 1 0 .003 9.175l-15.783.007v-.002a4.586 4.586 0 0 0-.004-9.172M184.523 135.668c-.553 5.485-5.447 9.483-10.931 8.93-5.485-.553-9.483-5.448-8.93-10.932.552-5.485 5.447-9.483 10.932-8.93 5.485.553 9.483 5.447 8.93 10.932",fill:"#FFF"}),n.createElement("path",{d:"M179.26 141.75l12.64 7.167M193.006 156.477l-15.255 13.011",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{d:"M184.668 170.057a6.835 6.835 0 1 1-13.6-1.372 6.835 6.835 0 0 1 13.6 1.372M203.34 153.325a6.885 6.885 0 1 1-13.7-1.382 6.885 6.885 0 0 1 13.7 1.382",fill:"#FFF"}),n.createElement("path",{d:"M151.931 192.324a2.222 2.222 0 1 1-4.444 0 2.222 2.222 0 0 1 4.444 0zM225.27 116.056a2.222 2.222 0 1 1-4.445 0 2.222 2.222 0 0 1 4.444 0zM216.38 151.08a2.223 2.223 0 1 1-4.446-.001 2.223 2.223 0 0 1 4.446 0zM176.917 107.636a2.223 2.223 0 1 1-4.445 0 2.223 2.223 0 0 1 4.445 0zM195.291 92.165a2.223 2.223 0 1 1-4.445 0 2.223 2.223 0 0 1 4.445 0zM202.058 180.711a2.223 2.223 0 1 1-4.446 0 2.223 2.223 0 0 1 4.446 0z",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{stroke:"#FFF",strokeWidth:"2",d:"M214.404 153.302l-1.912 20.184-10.928 5.99M173.661 174.792l-6.356 9.814h-11.36l-4.508 6.484M174.941 125.168v-15.804M220.824 117.25l-12.84 7.901-15.31-7.902V94.39"}),n.createElement("path",{d:"M166.588 65.936h-3.951a4.756 4.756 0 0 1-4.743-4.742 4.756 4.756 0 0 1 4.743-4.743h3.951a4.756 4.756 0 0 1 4.743 4.743 4.756 4.756 0 0 1-4.743 4.742",fill:"#FFF"}),n.createElement("path",{d:"M174.823 30.03c0-16.281 13.198-29.48 29.48-29.48 16.28 0 29.48 13.199 29.48 29.48 0 16.28-13.2 29.48-29.48 29.48-16.282 0-29.48-13.2-29.48-29.48",fill:"#1677ff"}),n.createElement("path",{d:"M205.952 38.387c.5.5.785 1.142.785 1.928s-.286 1.465-.785 1.964c-.572.5-1.214.75-2 .75-.785 0-1.429-.285-1.929-.785-.572-.5-.82-1.143-.82-1.929s.248-1.428.82-1.928c.5-.5 1.144-.75 1.93-.75.785 0 1.462.25 1.999.75m4.285-19.463c1.428 1.249 2.143 2.963 2.143 5.142 0 1.712-.427 3.13-1.219 4.25-.067.096-.137.18-.218.265-.416.429-1.41 1.346-2.956 2.699a5.07 5.07 0 0 0-1.428 1.75 5.207 5.207 0 0 0-.536 2.357v.5h-4.107v-.5c0-1.357.215-2.536.714-3.5.464-.964 1.857-2.464 4.178-4.536l.43-.5c.643-.785.964-1.643.964-2.535 0-1.18-.358-2.108-1-2.785-.678-.68-1.643-1.001-2.858-1.001-1.536 0-2.642.464-3.357 1.43-.37.5-.621 1.135-.76 1.904a1.999 1.999 0 0 1-1.971 1.63h-.004c-1.277 0-2.257-1.183-1.98-2.43.337-1.518 1.02-2.78 2.073-3.784 1.536-1.5 3.607-2.25 6.25-2.25 2.32 0 4.214.607 5.642 1.894",fill:"#FFF"}),n.createElement("path",{d:"M52.04 76.131s21.81 5.36 27.307 15.945c5.575 10.74-6.352 9.26-15.73 4.935-10.86-5.008-24.7-11.822-11.577-20.88",fill:"#FFB594"}),n.createElement("path",{d:"M90.483 67.504l-.449 2.893c-.753.49-4.748-2.663-4.748-2.663l-1.645.748-1.346-5.684s6.815-4.589 8.917-5.018c2.452-.501 9.884.94 10.7 2.278 0 0 1.32.486-2.227.69-3.548.203-5.043.447-6.79 3.132-1.747 2.686-2.412 3.624-2.412 3.624",fill:"#FFC6A0"}),n.createElement("path",{d:"M128.055 111.367c-2.627-7.724-6.15-13.18-8.917-15.478-3.5-2.906-9.34-2.225-11.366-4.187-1.27-1.231-3.215-1.197-3.215-1.197s-14.98-3.158-16.828-3.479c-2.37-.41-2.124-.714-6.054-1.405-1.57-1.907-2.917-1.122-2.917-1.122l-7.11-1.383c-.853-1.472-2.423-1.023-2.423-1.023l-2.468-.897c-1.645 9.976-7.74 13.796-7.74 13.796 1.795 1.122 15.703 8.3 15.703 8.3l5.107 37.11s-3.321 5.694 1.346 9.109c0 0 19.883-3.743 34.921-.329 0 0 3.047-2.546.972-8.806.523-3.01 1.394-8.263 1.736-11.622.385.772 2.019 1.918 3.14 3.477 0 0 9.407-7.365 11.052-14.012-.832-.723-1.598-1.585-2.267-2.453-.567-.736-.358-2.056-.765-2.717-.669-1.084-1.804-1.378-1.907-1.682",fill:"#FFF"}),n.createElement("path",{d:"M101.09 289.998s4.295 2.041 7.354 1.021c2.821-.94 4.53.668 7.08 1.178 2.55.51 6.874 1.1 11.686-1.26-.103-5.51-6.889-3.98-11.96-6.713-2.563-1.38-3.784-4.722-3.598-8.799h-9.402s-1.392 10.52-1.16 14.573",fill:"#CBD1D1"}),n.createElement("path",{d:"M101.067 289.826s2.428 1.271 6.759.653c3.058-.437 3.712.481 7.423 1.031 3.712.55 10.724-.069 11.823-.894.413 1.1-.343 2.063-.343 2.063s-1.512.603-4.812.824c-2.03.136-5.8.291-7.607-.503-1.787-1.375-5.247-1.903-5.728-.241-3.918.95-7.355-.286-7.355-.286l-.16-2.647z",fill:"#2B0849"}),n.createElement("path",{d:"M108.341 276.044h3.094s-.103 6.702 4.536 8.558c-4.64.618-8.558-2.303-7.63-8.558",fill:"#A4AABA"}),n.createElement("path",{d:"M57.542 272.401s-2.107 7.416-4.485 12.306c-1.798 3.695-4.225 7.492 5.465 7.492 6.648 0 8.953-.48 7.423-6.599-1.53-6.12.266-13.199.266-13.199h-8.669z",fill:"#CBD1D1"}),n.createElement("path",{d:"M51.476 289.793s2.097 1.169 6.633 1.169c6.083 0 8.249-1.65 8.249-1.65s.602 1.114-.619 2.165c-.993.855-3.597 1.591-7.39 1.546-4.145-.048-5.832-.566-6.736-1.168-.825-.55-.687-1.58-.137-2.062",fill:"#2B0849"}),n.createElement("path",{d:"M58.419 274.304s.033 1.519-.314 2.93c-.349 1.42-1.078 3.104-1.13 4.139-.058 1.151 4.537 1.58 5.155.034.62-1.547 1.294-6.427 1.913-7.252.619-.825-4.903-2.119-5.624.15",fill:"#A4AABA"}),n.createElement("path",{d:"M99.66 278.514l13.378.092s1.298-54.52 1.853-64.403c.554-9.882 3.776-43.364 1.002-63.128l-12.547-.644-22.849.78s-.434 3.966-1.195 9.976c-.063.496-.682.843-.749 1.365-.075.585.423 1.354.32 1.966-2.364 14.08-6.377 33.104-8.744 46.677-.116.666-1.234 1.009-1.458 2.691-.04.302.211 1.525.112 1.795-6.873 18.744-10.949 47.842-14.277 61.885l14.607-.014s2.197-8.57 4.03-16.97c2.811-12.886 23.111-85.01 23.111-85.01l3.016-.521 1.043 46.35s-.224 1.234.337 2.02c.56.785-.56 1.123-.392 2.244l.392 1.794s-.449 7.178-.898 11.89c-.448 4.71-.092 39.165-.092 39.165",fill:"#7BB2F9"}),n.createElement("path",{d:"M76.085 221.626c1.153.094 4.038-2.019 6.955-4.935M106.36 225.142s2.774-1.11 6.103-3.883",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M107.275 222.1s2.773-1.11 6.102-3.884",stroke:"#648BD8",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M74.74 224.767s2.622-.591 6.505-3.365M86.03 151.634c-.27 3.106.3 8.525-4.336 9.123M103.625 149.88s.11 14.012-1.293 15.065c-2.219 1.664-2.99 1.944-2.99 1.944M99.79 150.438s.035 12.88-1.196 24.377M93.673 175.911s7.212-1.664 9.431-1.664M74.31 205.861a212.013 212.013 0 0 1-.979 4.56s-1.458 1.832-1.009 3.776c.449 1.944-.947 2.045-4.985 15.355-1.696 5.59-4.49 18.591-6.348 27.597l-.231 1.12M75.689 197.807a320.934 320.934 0 0 1-.882 4.754M82.591 152.233L81.395 162.7s-1.097.15-.5 2.244c.113 1.346-2.674 15.775-5.18 30.43M56.12 274.418h13.31",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M116.241 148.22s-17.047-3.104-35.893.2c.158 2.514-.003 4.15-.003 4.15s14.687-2.818 35.67-.312c.252-2.355.226-4.038.226-4.038",fill:"#192064"}),n.createElement("path",{d:"M106.322 151.165l.003-4.911a.81.81 0 0 0-.778-.815c-2.44-.091-5.066-.108-7.836-.014a.818.818 0 0 0-.789.815l-.003 4.906a.81.81 0 0 0 .831.813c2.385-.06 4.973-.064 7.73.017a.815.815 0 0 0 .842-.81",fill:"#FFF"}),n.createElement("path",{d:"M105.207 150.233l.002-3.076a.642.642 0 0 0-.619-.646 94.321 94.321 0 0 0-5.866-.01.65.65 0 0 0-.63.647v3.072a.64.64 0 0 0 .654.644 121.12 121.12 0 0 1 5.794.011c.362.01.665-.28.665-.642",fill:"#192064"}),n.createElement("path",{d:"M100.263 275.415h12.338M101.436 270.53c.006 3.387.042 5.79.111 6.506M101.451 264.548a915.75 915.75 0 0 0-.015 4.337M100.986 174.965l.898 44.642s.673 1.57-.225 2.692c-.897 1.122 2.468.673.898 2.243-1.57 1.57.897 1.122 0 3.365-.596 1.489-.994 21.1-1.096 35.146",stroke:"#648BD8",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M46.876 83.427s-.516 6.045 7.223 5.552c11.2-.712 9.218-9.345 31.54-21.655-.786-2.708-2.447-4.744-2.447-4.744s-11.068 3.11-22.584 8.046c-6.766 2.9-13.395 6.352-13.732 12.801M104.46 91.057l.941-5.372-8.884-11.43-5.037 5.372-1.74 7.834a.321.321 0 0 0 .108.32c.965.8 6.5 5.013 14.347 3.544a.332.332 0 0 0 .264-.268",fill:"#FFC6A0"}),n.createElement("path",{d:"M93.942 79.387s-4.533-2.853-2.432-6.855c1.623-3.09 4.513 1.133 4.513 1.133s.52-3.642 3.121-3.642c.52-1.04 1.561-4.162 1.561-4.162s11.445 2.601 13.526 3.121c0 5.203-2.304 19.424-7.84 19.861-8.892.703-12.449-9.456-12.449-9.456",fill:"#FFC6A0"}),n.createElement("path",{d:"M113.874 73.446c2.601-2.081 3.47-9.722 3.47-9.722s-2.479-.49-6.64-2.05c-4.683-2.081-12.798-4.747-17.48.976-9.668 3.223-2.05 19.823-2.05 19.823l2.713-3.021s-3.935-3.287-2.08-6.243c2.17-3.462 3.92 1.073 3.92 1.073s.637-2.387 3.581-3.342c.355-.71 1.036-2.674 1.432-3.85a1.073 1.073 0 0 1 1.263-.704c2.4.558 8.677 2.019 11.356 2.662.522.125.871.615.82 1.15l-.305 3.248z",fill:"#520038"}),n.createElement("path",{d:"M104.977 76.064c-.103.61-.582 1.038-1.07.956-.489-.083-.801-.644-.698-1.254.103-.61.582-1.038 1.07-.956.488.082.8.644.698 1.254M112.132 77.694c-.103.61-.582 1.038-1.07.956-.488-.083-.8-.644-.698-1.254.103-.61.582-1.038 1.07-.956.488.082.8.643.698 1.254",fill:"#552950"}),n.createElement("path",{stroke:"#DB836E",strokeWidth:"1.118",strokeLinecap:"round",strokeLinejoin:"round",d:"M110.13 74.84l-.896 1.61-.298 4.357h-2.228"}),n.createElement("path",{d:"M110.846 74.481s1.79-.716 2.506.537",stroke:"#5C2552",strokeWidth:"1.118",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M92.386 74.282s.477-1.114 1.113-.716c.637.398 1.274 1.433.558 1.99-.717.556.159 1.67.159 1.67",stroke:"#DB836E",strokeWidth:"1.118",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M103.287 72.93s1.83 1.113 4.137.954",stroke:"#5C2552",strokeWidth:"1.118",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M103.685 81.762s2.227 1.193 4.376 1.193M104.64 84.308s.954.398 1.511.318M94.693 81.205s2.308 7.4 10.424 7.639",stroke:"#DB836E",strokeWidth:"1.118",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M81.45 89.384s.45 5.647-4.935 12.787M69 82.654s-.726 9.282-8.204 14.206",stroke:"#E4EBF7",strokeWidth:"1.101",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M129.405 122.865s-5.272 7.403-9.422 10.768",stroke:"#E4EBF7",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M119.306 107.329s.452 4.366-2.127 32.062",stroke:"#E4EBF7",strokeWidth:"1.101",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M150.028 151.232h-49.837a1.01 1.01 0 0 1-1.01-1.01v-31.688c0-.557.452-1.01 1.01-1.01h49.837c.558 0 1.01.453 1.01 1.01v31.688a1.01 1.01 0 0 1-1.01 1.01",fill:"#F2D7AD"}),n.createElement("path",{d:"M150.29 151.232h-19.863v-33.707h20.784v32.786a.92.92 0 0 1-.92.92",fill:"#F4D19D"}),n.createElement("path",{d:"M123.554 127.896H92.917a.518.518 0 0 1-.425-.816l6.38-9.113c.193-.277.51-.442.85-.442h31.092l-7.26 10.371z",fill:"#F2D7AD"}),n.createElement("path",{fill:"#CC9B6E",d:"M123.689 128.447H99.25v-.519h24.169l7.183-10.26.424.298z"}),n.createElement("path",{d:"M158.298 127.896h-18.669a2.073 2.073 0 0 1-1.659-.83l-7.156-9.541h19.965c.49 0 .95.23 1.244.622l6.69 8.92a.519.519 0 0 1-.415.83",fill:"#F4D19D"}),n.createElement("path",{fill:"#CC9B6E",d:"M157.847 128.479h-19.384l-7.857-10.475.415-.31 7.7 10.266h19.126zM130.554 150.685l-.032-8.177.519-.002.032 8.177z"}),n.createElement("path",{fill:"#CC9B6E",d:"M130.511 139.783l-.08-21.414.519-.002.08 21.414zM111.876 140.932l-.498-.143 1.479-5.167.498.143zM108.437 141.06l-2.679-2.935 2.665-3.434.41.318-2.397 3.089 2.384 2.612zM116.607 141.06l-.383-.35 2.383-2.612-2.397-3.089.41-.318 2.665 3.434z"}),n.createElement("path",{d:"M154.316 131.892l-3.114-1.96.038 3.514-1.043.092c-1.682.115-3.634.23-4.789.23-1.902 0-2.693 2.258 2.23 2.648l-2.645-.596s-2.168 1.317.504 2.3c0 0-1.58 1.217.561 2.58-.584 3.504 5.247 4.058 7.122 3.59 1.876-.47 4.233-2.359 4.487-5.16.28-3.085-.89-5.432-3.35-7.238",fill:"#FFC6A0"}),n.createElement("path",{d:"M153.686 133.577s-6.522.47-8.36.372c-1.836-.098-1.904 2.19 2.359 2.264 3.739.15 5.451-.044 5.451-.044",stroke:"#DB836E",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M145.16 135.877c-1.85 1.346.561 2.355.561 2.355s3.478.898 6.73.617",stroke:"#DB836E",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M151.89 141.71s-6.28.111-6.73-2.132c-.223-1.346.45-1.402.45-1.402M146.114 140.868s-1.103 3.16 5.44 3.533M151.202 129.932v3.477M52.838 89.286c3.533-.337 8.423-1.248 13.582-7.754",stroke:"#DB836E",strokeWidth:"1.051",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M168.567 248.318a6.647 6.647 0 0 1-6.647-6.647v-66.466a6.647 6.647 0 1 1 13.294 0v66.466a6.647 6.647 0 0 1-6.647 6.647",fill:"#5BA02E"}),n.createElement("path",{d:"M176.543 247.653a6.647 6.647 0 0 1-6.646-6.647v-33.232a6.647 6.647 0 1 1 13.293 0v33.232a6.647 6.647 0 0 1-6.647 6.647",fill:"#92C110"}),n.createElement("path",{d:"M186.443 293.613H158.92a3.187 3.187 0 0 1-3.187-3.187v-46.134a3.187 3.187 0 0 1 3.187-3.187h27.524a3.187 3.187 0 0 1 3.187 3.187v46.134a3.187 3.187 0 0 1-3.187 3.187",fill:"#F2D7AD"}),n.createElement("path",{d:"M88.979 89.48s7.776 5.384 16.6 2.842",stroke:"#E4EBF7",strokeWidth:"1.101",strokeLinecap:"round",strokeLinejoin:"round"})))}},16798:function(t,e,i){"use strict";i.d(e,{A:function(){return a}});var n,r=i(9939),o=i(72047),s=i.n(o),a=(n=r.useEffect,function(t,e){var i,o,a=(0,r.useRef)(),c=(0,r.useRef)(0);void 0!==e&&(i=e,o=a.current,void 0===i&&(i=[]),void 0===o&&(o=[]),s()(i,o))||(c.current+=1),a.current=e,n(t,[c.current])})},19357:function(t,e,i){"use strict";var n=i(94423),r=i(27569),o=i(9939),s=i(92237),a=i(58761),c=i(58564),l=i(38657),h=i(76787),u=i.n(h),f=i(45847),d=i(16396),g=i(51894),p=i(58199),v=i(8619),m={success:s.A,error:a.A,info:c.A,warning:l.A},y={404:d.A,500:g.A,403:v.A},b=Object.keys(y),x=function(t){var e=t.prefixCls,i=t.icon,n=t.status,r=u()("".concat(e,"-icon"));if(b.includes("".concat(n))){var s=y[n];return o.createElement("div",{className:"".concat(r," ").concat(e,"-image")},o.createElement(s,null))}var a=o.createElement(m[n]);return null===i||!1===i?null:o.createElement("div",{className:r},i||a)},_=function(t){var e=t.prefixCls,i=t.extra;return i?o.createElement("div",{className:"".concat(e,"-extra")},i):null},C=function(t){var e=t.prefixCls,i=t.className,s=t.rootClassName,a=t.subTitle,c=t.title,l=t.style,h=t.children,d=t.status,g=void 0===d?"info":d,v=t.icon,m=t.extra,y=o.useContext(f.QO),b=y.getPrefixCls,C=y.direction,S=y.result,w=b("result",e),T=(0,p.A)(w),O=(0,r.A)(T,3),E=O[0],k=O[1],M=O[2],A=u()(w,"".concat(w,"-").concat(g),i,null===S||void 0===S?void 0:S.className,s,(0,n.A)({},"".concat(w,"-rtl"),"rtl"===C),k,M),D=Object.assign(Object.assign({},null===S||void 0===S?void 0:S.style),l);return E(o.createElement("div",{className:A,style:D},o.createElement(x,{prefixCls:w,status:g,icon:v}),o.createElement("div",{className:"".concat(w,"-title")},c),a&&o.createElement("div",{className:"".concat(w,"-subtitle")},a),o.createElement(_,{prefixCls:w,extra:m}),h&&o.createElement("div",{className:"".concat(w,"-content")},h)))};C.PRESENTED_IMAGE_403=y[403],C.PRESENTED_IMAGE_404=y[404],C.PRESENTED_IMAGE_500=y[500],e.Ay=C},19860:function(t,e){"use strict";e.A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"warning",theme:"filled"}},21887:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M603.3 327.5l-246 178a7.95 7.95 0 000 12.9l246 178c5.3 3.8 12.7 0 12.7-6.5V643c0-10.2-4.9-19.9-13.2-25.9L457.4 512l145.4-105.2c8.3-6 13.2-15.6 13.2-25.9V334c0-6.5-7.4-10.3-12.7-6.5z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"left-circle",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},25007:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M145.71 752c2 0 4-.2 5.98-.5L319.9 722c1.99-.4 3.88-1.3 5.28-2.8l423.91-423.87a9.93 9.93 0 000-14.06L582.88 114.9C581 113 578.5 112 575.82 112s-5.18 1-7.08 2.9L144.82 538.76c-1.5 1.5-2.4 3.29-2.8 5.28l-29.5 168.17a33.59 33.59 0 009.37 29.81c6.58 6.48 14.95 9.97 23.82 9.97m51.75-85.43l15.65-88.92 362.7-362.67 73.28 73.27-362.7 362.67zm401.37-98.64c27.69-14.81 57.29-20.85 85.54-15.52 32.37 6.1 59.72 26.53 78.96 59.4 29.97 51.22 21.64 102.34-18.48 144.26-17.58 18.36-41.07 35.01-70 50.3l-.3.15.86.26a147.88 147.88 0 0041.54 6.2l1.17.01c61.07 0 100.98-22.1 125.28-67.87a36 36 0 0163.6 33.76C869.7 849.1 804.9 885 718.12 885c-47.69 0-91.94-15.03-128.19-41.36l-1.05-.78-1.36.47c-46.18 16-98.74 29.95-155.37 41.94l-2.24.47a1931.1 1931.1 0 01-139.16 23.96 36 36 0 11-9.5-71.38 1860.1 1860.1 0 00133.84-23.04c42.8-9 83-19.13 119.35-30.34l.24-.08-.44-.69c-16.46-26.45-25.86-55.43-26.14-83.24v-1.3c0-49.9 39.55-104.32 90.73-131.7M671 623.17c-10.74-2.03-24.1.7-38.22 8.26-29.55 15.8-52.7 47.64-52.7 68.2 0 18.2 8.9 40.14 24.71 59.73l.24.3 1.22-.52c39.17-16.58 68.49-34.27 85.93-52.18l.64-.67c18.74-19.57 21.39-35.84 8.36-58.1-9.06-15.47-19.03-22.92-30.18-25.02"}}]},name:"signature",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},36831:function(t,e){"use strict";var i=!("undefined"===typeof window||!window.document||!window.document.createElement);e.A=i},38317:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"unlock",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},38509:function(t,e,i){var n,r=r||{version:"5.4.2"};if(e.fabric=r,"undefined"!==typeof document&&"undefined"!==typeof window)document instanceof("undefined"!==typeof HTMLDocument?HTMLDocument:Document)?r.document=document:r.document=document.implementation.createHTMLDocument(""),r.window=window;else{var o=new(i(23859).JSDOM)(decodeURIComponent("%3C!DOCTYPE%20html%3E%3Chtml%3E%3Chead%3E%3C%2Fhead%3E%3Cbody%3E%3C%2Fbody%3E%3C%2Fhtml%3E"),{features:{FetchExternalResources:["img"]},resources:"usable"}).window;r.document=o.document,r.jsdomImplForWrapper=i(24447).implForWrapper,r.nodeCanvas=i(61903).Canvas,r.window=o,DOMParser=r.window.DOMParser}function s(t,e){var i=t.canvas,n=e.targetCanvas,r=n.getContext("2d");r.translate(0,n.height),r.scale(1,-1);var o=i.height-n.height;r.drawImage(i,0,o,n.width,n.height,0,0,n.width,n.height)}function a(t,e){var i=e.targetCanvas.getContext("2d"),n=e.destinationWidth,r=e.destinationHeight,o=n*r*4,s=new Uint8Array(this.imageBuffer,0,o),a=new Uint8ClampedArray(this.imageBuffer,0,o);t.readPixels(0,0,n,r,t.RGBA,t.UNSIGNED_BYTE,s);var c=new ImageData(a,n,r);i.putImageData(c,0,0)}r.isTouchSupported="ontouchstart"in r.window||"ontouchstart"in r.document||r.window&&r.window.navigator&&r.window.navigator.maxTouchPoints>0,r.isLikelyNode="undefined"!==typeof Buffer&&"undefined"===typeof window,r.SHARED_ATTRIBUTES=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"],r.DPI=96,r.reNum="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:[eE][-+]?\\d+)?)",r.commaWsp="(?:\\s+,?\\s*|,\\s*)",r.rePathCommand=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:[eE][-+]?\d+)?)/gi,r.reNonWord=/[ \n\.,;!\?\-]/,r.fontPaths={},r.iMatrix=[1,0,0,1,0,0],r.svgNS="http://www.w3.org/2000/svg",r.perfLimitSizeTotal=2097152,r.maxCacheSideLimit=4096,r.minCacheSideLimit=256,r.charWidthsCache={},r.textureSize=2048,r.disableStyleCopyPaste=!1,r.enableGLFiltering=!0,r.devicePixelRatio=r.window.devicePixelRatio||r.window.webkitDevicePixelRatio||r.window.mozDevicePixelRatio||1,r.browserShadowBlurConstant=1,r.arcToSegmentsCache={},r.boundsOfCurveCache={},r.cachesBoundsOfCurve=!0,r.forceGLPutImageData=!1,r.initFilterBackend=function(){return r.enableGLFiltering&&r.isWebglSupported&&r.isWebglSupported(r.textureSize)?new r.WebglFilterBackend({tileSize:r.textureSize}):r.Canvas2dFilterBackend?new r.Canvas2dFilterBackend:void 0},"undefined"!==typeof document&&"undefined"!==typeof window&&(window.fabric=r),function(){function t(t,e){if(this.__eventListeners[t]){var i=this.__eventListeners[t];e?i[i.indexOf(e)]=!1:r.util.array.fill(i,!1)}}function e(t,e){var i=function(){e.apply(this,arguments),this.off(t,i)}.bind(this);this.on(t,i)}r.Observable={fire:function(t,e){if(!this.__eventListeners)return this;var i=this.__eventListeners[t];if(!i)return this;for(var n=0,r=i.length;n<r;n++)i[n]&&i[n].call(this,e||{});return this.__eventListeners[t]=i.filter((function(t){return!1!==t})),this},on:function(t,e){if(this.__eventListeners||(this.__eventListeners={}),1===arguments.length)for(var i in t)this.on(i,t[i]);else this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(e);return this},once:function(t,i){if(1===arguments.length)for(var n in t)e.call(this,n,t[n]);else e.call(this,t,i);return this},off:function(e,i){if(!this.__eventListeners)return this;if(0===arguments.length)for(e in this.__eventListeners)t.call(this,e);else if(1===arguments.length&&"object"===typeof arguments[0])for(var n in e)t.call(this,n,e[n]);else t.call(this,e,i);return this}}}(),r.Collection={_objects:[],add:function(){if(this._objects.push.apply(this._objects,arguments),this._onObjectAdded)for(var t=0,e=arguments.length;t<e;t++)this._onObjectAdded(arguments[t]);return this.renderOnAddRemove&&this.requestRenderAll(),this},insertAt:function(t,e,i){var n=this._objects;return i?n[e]=t:n.splice(e,0,t),this._onObjectAdded&&this._onObjectAdded(t),this.renderOnAddRemove&&this.requestRenderAll(),this},remove:function(){for(var t,e=this._objects,i=!1,n=0,r=arguments.length;n<r;n++)-1!==(t=e.indexOf(arguments[n]))&&(i=!0,e.splice(t,1),this._onObjectRemoved&&this._onObjectRemoved(arguments[n]));return this.renderOnAddRemove&&i&&this.requestRenderAll(),this},forEachObject:function(t,e){for(var i=this.getObjects(),n=0,r=i.length;n<r;n++)t.call(e,i[n],n,i);return this},getObjects:function(t){return"undefined"===typeof t?this._objects.concat():this._objects.filter((function(e){return e.type===t}))},item:function(t){return this._objects[t]},isEmpty:function(){return 0===this._objects.length},size:function(){return this._objects.length},contains:function(t,e){return this._objects.indexOf(t)>-1||!!e&&this._objects.some((function(e){return"function"===typeof e.contains&&e.contains(t,!0)}))},complexity:function(){return this._objects.reduce((function(t,e){return t+=e.complexity?e.complexity():0}),0)}},r.CommonMethods={_setOptions:function(t){for(var e in t)this.set(e,t[e])},_initGradient:function(t,e){!t||!t.colorStops||t instanceof r.Gradient||this.set(e,new r.Gradient(t))},_initPattern:function(t,e,i){!t||!t.source||t instanceof r.Pattern?i&&i():this.set(e,new r.Pattern(t,i))},_setObject:function(t){for(var e in t)this._set(e,t[e])},set:function(t,e){return"object"===typeof t?this._setObject(t):this._set(t,e),this},_set:function(t,e){this[t]=e},toggle:function(t){var e=this.get(t);return"boolean"===typeof e&&this.set(t,!e),this},get:function(t){return this[t]}},function(t){var e=Math.sqrt,i=Math.atan2,n=Math.pow,o=Math.PI/180,s=Math.PI/2;r.util={cos:function(t){if(0===t)return 1;switch(t<0&&(t=-t),t/s){case 1:case 3:return 0;case 2:return-1}return Math.cos(t)},sin:function(t){if(0===t)return 0;var e=1;switch(t<0&&(e=-1),t/s){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(t)},removeFromArray:function(t,e){var i=t.indexOf(e);return-1!==i&&t.splice(i,1),t},getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},degreesToRadians:function(t){return t*o},radiansToDegrees:function(t){return t/o},rotatePoint:function(t,e,i){var n=new r.Point(t.x-e.x,t.y-e.y),o=r.util.rotateVector(n,i);return new r.Point(o.x,o.y).addEquals(e)},rotateVector:function(t,e){var i=r.util.sin(e),n=r.util.cos(e);return{x:t.x*n-t.y*i,y:t.x*i+t.y*n}},createVector:function(t,e){return new r.Point(e.x-t.x,e.y-t.y)},calcAngleBetweenVectors:function(t,e){return Math.acos((t.x*e.x+t.y*e.y)/(Math.hypot(t.x,t.y)*Math.hypot(e.x,e.y)))},getHatVector:function(t){return new r.Point(t.x,t.y).multiply(1/Math.hypot(t.x,t.y))},getBisector:function(t,e,i){var n=r.util.createVector(t,e),o=r.util.createVector(t,i),s=r.util.calcAngleBetweenVectors(n,o),a=s*(0===r.util.calcAngleBetweenVectors(r.util.rotateVector(n,s),o)?1:-1)/2;return{vector:r.util.getHatVector(r.util.rotateVector(n,a)),angle:s}},projectStrokeOnPoints:function(t,e,i){var n=[],o=e.strokeWidth/2,s=e.strokeUniform?new r.Point(1/e.scaleX,1/e.scaleY):new r.Point(1,1),a=function(t){var e=o/Math.hypot(t.x,t.y);return new r.Point(t.x*e*s.x,t.y*e*s.y)};return t.length<=1||t.forEach((function(c,l){var h,u,f=new r.Point(c.x,c.y);0===l?(u=t[l+1],h=i?a(r.util.createVector(u,f)).addEquals(f):t[t.length-1]):l===t.length-1?(h=t[l-1],u=i?a(r.util.createVector(h,f)).addEquals(f):t[0]):(h=t[l-1],u=t[l+1]);var d,g,p=r.util.getBisector(f,h,u),v=p.vector,m=p.angle;if("miter"===e.strokeLineJoin&&(d=-o/Math.sin(m/2),g=new r.Point(v.x*d*s.x,v.y*d*s.y),Math.hypot(g.x,g.y)/o<=e.strokeMiterLimit))return n.push(f.add(g)),void n.push(f.subtract(g));d=-o*Math.SQRT2,g=new r.Point(v.x*d*s.x,v.y*d*s.y),n.push(f.add(g)),n.push(f.subtract(g))})),n},transformPoint:function(t,e,i){return i?new r.Point(e[0]*t.x+e[2]*t.y,e[1]*t.x+e[3]*t.y):new r.Point(e[0]*t.x+e[2]*t.y+e[4],e[1]*t.x+e[3]*t.y+e[5])},makeBoundingBoxFromPoints:function(t,e){if(e)for(var i=0;i<t.length;i++)t[i]=r.util.transformPoint(t[i],e);var n=[t[0].x,t[1].x,t[2].x,t[3].x],o=r.util.array.min(n),s=r.util.array.max(n)-o,a=[t[0].y,t[1].y,t[2].y,t[3].y],c=r.util.array.min(a);return{left:o,top:c,width:s,height:r.util.array.max(a)-c}},invertTransform:function(t){var e=1/(t[0]*t[3]-t[1]*t[2]),i=[e*t[3],-e*t[1],-e*t[2],e*t[0]],n=r.util.transformPoint({x:t[4],y:t[5]},i,!0);return i[4]=-n.x,i[5]=-n.y,i},toFixed:function(t,e){return parseFloat(Number(t).toFixed(e))},parseUnit:function(t,e){var i=/\D{0,2}$/.exec(t),n=parseFloat(t);switch(e||(e=r.Text.DEFAULT_SVG_FONT_SIZE),i[0]){case"mm":return n*r.DPI/25.4;case"cm":return n*r.DPI/2.54;case"in":return n*r.DPI;case"pt":return n*r.DPI/72;case"pc":return n*r.DPI/72*12;case"em":return n*e;default:return n}},falseFunction:function(){return!1},getKlass:function(t,e){return t=r.util.string.camelize(t.charAt(0).toUpperCase()+t.slice(1)),r.util.resolveNamespace(e)[t]},getSvgAttributes:function(t){var e=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":e=e.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);break;case"radialGradient":e=e.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);break;case"stop":e=e.concat(["offset","stop-color","stop-opacity"])}return e},resolveNamespace:function(e){if(!e)return r;var i,n=e.split("."),o=n.length,s=t||r.window;for(i=0;i<o;++i)s=s[n[i]];return s},loadImage:function(t,e,i,n){if(t){var o=r.util.createImage(),s=function(){e&&e.call(i,o,!1),o=o.onload=o.onerror=null};o.onload=s,o.onerror=function(){r.log("Error loading "+o.src),e&&e.call(i,null,!0),o=o.onload=o.onerror=null},0!==t.indexOf("data")&&void 0!==n&&null!==n&&(o.crossOrigin=n),"data:image/svg"===t.substring(0,14)&&(o.onload=null,r.util.loadImageInDom(o,s)),o.src=t}else e&&e.call(i,t)},loadImageInDom:function(t,e){var i=r.document.createElement("div");i.style.width=i.style.height="1px",i.style.left=i.style.top="-100%",i.style.position="absolute",i.appendChild(t),r.document.querySelector("body").appendChild(i),t.onload=function(){e(),i.parentNode.removeChild(i),i=null}},enlivenObjects:function(t,e,i,n){var o=[],s=0,a=(t=t||[]).length;function c(){++s===a&&e&&e(o.filter((function(t){return t})))}a?t.forEach((function(t,e){t&&t.type?r.util.getKlass(t.type,i).fromObject(t,(function(i,r){r||(o[e]=i),n&&n(t,i,r),c()})):c()})):e&&e(o)},enlivenObjectEnlivables:function(t,e,i){var n=r.Object.ENLIVEN_PROPS.filter((function(e){return!!t[e]}));r.util.enlivenObjects(n.map((function(e){return t[e]})),(function(t){var r={};n.forEach((function(i,n){r[i]=t[n],e&&(e[i]=t[n])})),i&&i(r)}))},enlivenPatterns:function(t,e){function i(){++o===s&&e&&e(n)}var n=[],o=0,s=(t=t||[]).length;s?t.forEach((function(t,e){t&&t.source?new r.Pattern(t,(function(t){n[e]=t,i()})):(n[e]=t,i())})):e&&e(n)},groupSVGElements:function(t,e,i){var n;return t&&1===t.length?("undefined"!==typeof i&&(t[0].sourcePath=i),t[0]):(e&&(e.width&&e.height?e.centerPoint={x:e.width/2,y:e.height/2}:(delete e.width,delete e.height)),n=new r.Group(t,e),"undefined"!==typeof i&&(n.sourcePath=i),n)},populateWithProperties:function(t,e,i){if(i&&Array.isArray(i))for(var n=0,r=i.length;n<r;n++)i[n]in t&&(e[i[n]]=t[i[n]])},createCanvasElement:function(){return r.document.createElement("canvas")},copyCanvasElement:function(t){var e=r.util.createCanvasElement();return e.width=t.width,e.height=t.height,e.getContext("2d").drawImage(t,0,0),e},toDataURL:function(t,e,i){return t.toDataURL("image/"+e,i)},createImage:function(){return r.document.createElement("img")},multiplyTransformMatrices:function(t,e,i){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],i?0:t[0]*e[4]+t[2]*e[5]+t[4],i?0:t[1]*e[4]+t[3]*e[5]+t[5]]},qrDecompose:function(t){var r=i(t[1],t[0]),s=n(t[0],2)+n(t[1],2),a=e(s),c=(t[0]*t[3]-t[2]*t[1])/a,l=i(t[0]*t[2]+t[1]*t[3],s);return{angle:r/o,scaleX:a,scaleY:c,skewX:l/o,skewY:0,translateX:t[4],translateY:t[5]}},calcRotateMatrix:function(t){if(!t.angle)return r.iMatrix.concat();var e=r.util.degreesToRadians(t.angle),i=r.util.cos(e),n=r.util.sin(e);return[i,n,-n,i,0,0]},calcDimensionsMatrix:function(t){var e="undefined"===typeof t.scaleX?1:t.scaleX,i="undefined"===typeof t.scaleY?1:t.scaleY,n=[t.flipX?-e:e,0,0,t.flipY?-i:i,0,0],o=r.util.multiplyTransformMatrices,s=r.util.degreesToRadians;return t.skewX&&(n=o(n,[1,0,Math.tan(s(t.skewX)),1],!0)),t.skewY&&(n=o(n,[1,Math.tan(s(t.skewY)),0,1],!0)),n},composeMatrix:function(t){var e=[1,0,0,1,t.translateX||0,t.translateY||0],i=r.util.multiplyTransformMatrices;return t.angle&&(e=i(e,r.util.calcRotateMatrix(t))),(1!==t.scaleX||1!==t.scaleY||t.skewX||t.skewY||t.flipX||t.flipY)&&(e=i(e,r.util.calcDimensionsMatrix(t))),e},resetObjectTransform:function(t){t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},saveObjectTransform:function(t){return{scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}},isTransparent:function(t,e,i,n){n>0&&(e>n?e-=n:e=0,i>n?i-=n:i=0);var r,o=!0,s=t.getImageData(e,i,2*n||1,2*n||1),a=s.data.length;for(r=3;r<a&&!1!==(o=s.data[r]<=0);r+=4);return s=null,o},parsePreserveAspectRatioAttribute:function(t){var e,i="meet",n=t.split(" ");return n&&n.length&&("meet"!==(i=n.pop())&&"slice"!==i?(e=i,i="meet"):n.length&&(e=n.pop())),{meetOrSlice:i,alignX:"none"!==e?e.slice(1,4):"none",alignY:"none"!==e?e.slice(5,8):"none"}},clearFabricFontCache:function(t){(t=(t||"").toLowerCase())?r.charWidthsCache[t]&&delete r.charWidthsCache[t]:r.charWidthsCache={}},limitDimsByArea:function(t,e){var i=Math.sqrt(e*t),n=Math.floor(e/i);return{x:Math.floor(i),y:n}},capValue:function(t,e,i){return Math.max(t,Math.min(e,i))},findScaleToFit:function(t,e){return Math.min(e.width/t.width,e.height/t.height)},findScaleToCover:function(t,e){return Math.max(e.width/t.width,e.height/t.height)},matrixToSVG:function(t){return"matrix("+t.map((function(t){return r.util.toFixed(t,r.Object.NUM_FRACTION_DIGITS)})).join(" ")+")"},removeTransformFromObject:function(t,e){var i=r.util.invertTransform(e),n=r.util.multiplyTransformMatrices(i,t.calcOwnMatrix());r.util.applyTransformToObject(t,n)},addTransformToObject:function(t,e){r.util.applyTransformToObject(t,r.util.multiplyTransformMatrices(e,t.calcOwnMatrix()))},applyTransformToObject:function(t,e){var i=r.util.qrDecompose(e),n=new r.Point(i.translateX,i.translateY);t.flipX=!1,t.flipY=!1,t.set("scaleX",i.scaleX),t.set("scaleY",i.scaleY),t.skewX=i.skewX,t.skewY=i.skewY,t.angle=i.angle,t.setPositionByOrigin(n,"center","center")},sizeAfterTransform:function(t,e,i){var n=t/2,o=e/2,s=[{x:-n,y:-o},{x:n,y:-o},{x:-n,y:o},{x:n,y:o}],a=r.util.calcDimensionsMatrix(i),c=r.util.makeBoundingBoxFromPoints(s,a);return{x:c.width,y:c.height}},mergeClipPaths:function(t,e){var i=t,n=e;i.inverted&&!n.inverted&&(i=e,n=t),r.util.applyTransformToObject(n,r.util.multiplyTransformMatrices(r.util.invertTransform(i.calcTransformMatrix()),n.calcTransformMatrix()));var o=i.inverted&&n.inverted;return o&&(i.inverted=n.inverted=!1),new r.Group([i],{clipPath:n,inverted:o})},hasStyleChanged:function(t,e,i){return i=i||!1,t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.textBackgroundColor!==e.textBackgroundColor||t.deltaY!==e.deltaY||i&&(t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough)},stylesToArray:function(t,e){t=r.util.object.clone(t,!0);for(var i=e.split("\n"),n=-1,o={},s=[],a=0;a<i.length;a++)if(t[a])for(var c=0;c<i[a].length;c++){n++;var l=t[a][c];if(l&&Object.keys(l).length>0)r.util.hasStyleChanged(o,l,!0)?s.push({start:n,end:n+1,style:l}):s[s.length-1].end++;o=l||{}}else n+=i[a].length;return s},stylesFromArray:function(t,e){if(!Array.isArray(t))return t;for(var i=e.split("\n"),n=-1,r=0,o={},s=0;s<i.length;s++)for(var a=0;a<i[s].length;a++)n++,t[r]&&t[r].start<=n&&n<t[r].end&&(o[s]=o[s]||{},o[s][a]=Object.assign({},t[r].style),n===t[r].end-1&&r++);return o}}}(e),function(){var t=Array.prototype.join,e={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},i={m:"l",M:"L"};function n(t,e,i,n,o,s,a,c,l,h,u){var f=r.util.cos(t),d=r.util.sin(t),g=r.util.cos(e),p=r.util.sin(e),v=i*o*g-n*s*p+a,m=n*o*g+i*s*p+c;return["C",h+l*(-i*o*d-n*s*f),u+l*(-n*o*d+i*s*f),v+l*(i*o*p+n*s*g),m+l*(n*o*p-i*s*g),v,m]}function o(t,e,i,n){var r=Math.atan2(e,t),o=Math.atan2(n,i);return o>=r?o-r:2*Math.PI-(r-o)}function s(t,e,i){for(var s=i[1],a=i[2],c=i[3],l=i[4],h=i[5],u=function(t,e,i,s,a,c,l){var h=Math.PI,u=l*h/180,f=r.util.sin(u),d=r.util.cos(u),g=0,p=0,v=-d*t*.5-f*e*.5,m=-d*e*.5+f*t*.5,y=(i=Math.abs(i))*i,b=(s=Math.abs(s))*s,x=m*m,_=v*v,C=y*b-y*x-b*_,S=0;if(C<0){var w=Math.sqrt(1-C/(y*b));i*=w,s*=w}else S=(a===c?-1:1)*Math.sqrt(C/(y*x+b*_));var T=S*i*m/s,O=-S*s*v/i,E=d*T-f*O+.5*t,k=f*T+d*O+.5*e,M=o(1,0,(v-T)/i,(m-O)/s),A=o((v-T)/i,(m-O)/s,(-v-T)/i,(-m-O)/s);0===c&&A>0?A-=2*h:1===c&&A<0&&(A+=2*h);for(var D=Math.ceil(Math.abs(A/h*2)),P=[],F=A/D,j=8/3*Math.sin(F/4)*Math.sin(F/4)/Math.sin(F/2),L=M+F,I=0;I<D;I++)P[I]=n(M,L,d,f,i,s,E,k,j,g,p),g=P[I][5],p=P[I][6],M=L,L+=F;return P}(i[6]-t,i[7]-e,s,a,l,h,c),f=0,d=u.length;f<d;f++)u[f][1]+=t,u[f][2]+=e,u[f][3]+=t,u[f][4]+=e,u[f][5]+=t,u[f][6]+=e;return u}function a(t,e,i,n){return Math.sqrt((i-t)*(i-t)+(n-e)*(n-e))}function c(t,e,i,n,r,o,s,a){return function(c){var l,h=(l=c)*l*l,u=function(t){return 3*t*t*(1-t)}(c),f=function(t){return 3*t*(1-t)*(1-t)}(c),d=function(t){return(1-t)*(1-t)*(1-t)}(c);return{x:s*h+r*u+i*f+t*d,y:a*h+o*u+n*f+e*d}}}function l(t,e,i,n,r,o,s,a){return function(c){var l=1-c,h=3*l*l*(i-t)+6*l*c*(r-i)+3*c*c*(s-r),u=3*l*l*(n-e)+6*l*c*(o-n)+3*c*c*(a-o);return Math.atan2(u,h)}}function h(t,e,i,n,r,o){return function(s){var a,c=(a=s)*a,l=function(t){return 2*t*(1-t)}(s),h=function(t){return(1-t)*(1-t)}(s);return{x:r*c+i*l+t*h,y:o*c+n*l+e*h}}}function u(t,e,i,n,r,o){return function(s){var a=1-s,c=2*a*(i-t)+2*s*(r-i),l=2*a*(n-e)+2*s*(o-n);return Math.atan2(l,c)}}function f(t,e,i){var n,r,o={x:e,y:i},s=0;for(r=1;r<=100;r+=1)n=t(r/100),s+=a(o.x,o.y,n.x,n.y),o=n;return s}function d(t,e){for(var i,n,r,o=0,s=0,c=t.iterator,l={x:t.x,y:t.y},h=.01,u=t.angleFinder;s<e&&h>1e-4;)i=c(o),r=o,(n=a(l.x,l.y,i.x,i.y))+s>e?(o-=h,h/=2):(l=i,o+=h,s+=n);return i.angle=u(r),i}function g(t){for(var e,i,n,r,o=0,s=t.length,d=0,g=0,p=0,v=0,m=[],y=0;y<s;y++){switch(n={x:d,y:g,command:(e=t[y])[0]},e[0]){case"M":n.length=0,p=d=e[1],v=g=e[2];break;case"L":n.length=a(d,g,e[1],e[2]),d=e[1],g=e[2];break;case"C":i=c(d,g,e[1],e[2],e[3],e[4],e[5],e[6]),r=l(d,g,e[1],e[2],e[3],e[4],e[5],e[6]),n.iterator=i,n.angleFinder=r,n.length=f(i,d,g),d=e[5],g=e[6];break;case"Q":i=h(d,g,e[1],e[2],e[3],e[4]),r=u(d,g,e[1],e[2],e[3],e[4]),n.iterator=i,n.angleFinder=r,n.length=f(i,d,g),d=e[3],g=e[4];break;case"Z":case"z":n.destX=p,n.destY=v,n.length=a(d,g,p,v),d=p,g=v}o+=n.length,m.push(n)}return m.push({length:o,x:d,y:g}),m}r.util.joinPath=function(t){return t.map((function(t){return t.join(" ")})).join(" ")},r.util.parsePath=function(t){var n,o,s,a,c,l=[],h=[],u=r.rePathCommand,f="[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?\\s*",d="("+f+")"+r.commaWsp,g="([01])"+r.commaWsp+"?",p=new RegExp(d+"?"+d+"?"+d+g+g+d+"?("+f+")","g");if(!t||!t.match)return l;for(var v,m=0,y=(c=t.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi)).length;m<y;m++){a=(n=c[m]).slice(1).trim(),h.length=0;var b=n.charAt(0);if(v=[b],"a"===b.toLowerCase())for(var x;x=p.exec(a);)for(var _=1;_<x.length;_++)h.push(x[_]);else for(;s=u.exec(a);)h.push(s[0]);_=0;for(var C=h.length;_<C;_++)o=parseFloat(h[_]),isNaN(o)||v.push(o);var S=e[b.toLowerCase()],w=i[b]||b;if(v.length-1>S)for(var T=1,O=v.length;T<O;T+=S)l.push([b].concat(v.slice(T,T+S))),b=w;else l.push(v)}return l},r.util.makePathSimpler=function(t){var e,i,n,r,o,a,c=0,l=0,h=t.length,u=0,f=0,d=[];for(i=0;i<h;++i){switch(n=!1,(e=t[i].slice(0))[0]){case"l":e[0]="L",e[1]+=c,e[2]+=l;case"L":c=e[1],l=e[2];break;case"h":e[1]+=c;case"H":e[0]="L",e[2]=l,c=e[1];break;case"v":e[1]+=l;case"V":e[0]="L",l=e[1],e[1]=c,e[2]=l;break;case"m":e[0]="M",e[1]+=c,e[2]+=l;case"M":c=e[1],l=e[2],u=e[1],f=e[2];break;case"c":e[0]="C",e[1]+=c,e[2]+=l,e[3]+=c,e[4]+=l,e[5]+=c,e[6]+=l;case"C":o=e[3],a=e[4],c=e[5],l=e[6];break;case"s":e[0]="S",e[1]+=c,e[2]+=l,e[3]+=c,e[4]+=l;case"S":"C"===r?(o=2*c-o,a=2*l-a):(o=c,a=l),c=e[3],l=e[4],e[0]="C",e[5]=e[3],e[6]=e[4],e[3]=e[1],e[4]=e[2],e[1]=o,e[2]=a,o=e[3],a=e[4];break;case"q":e[0]="Q",e[1]+=c,e[2]+=l,e[3]+=c,e[4]+=l;case"Q":o=e[1],a=e[2],c=e[3],l=e[4];break;case"t":e[0]="T",e[1]+=c,e[2]+=l;case"T":"Q"===r?(o=2*c-o,a=2*l-a):(o=c,a=l),e[0]="Q",c=e[1],l=e[2],e[1]=o,e[2]=a,e[3]=c,e[4]=l;break;case"a":e[0]="A",e[6]+=c,e[7]+=l;case"A":n=!0,d=d.concat(s(c,l,e)),c=e[6],l=e[7];break;case"z":case"Z":c=u,l=f}n||d.push(e),r=e[0]}return d},r.util.getSmoothPathFromPoints=function(t,e){var i,n=[],o=new r.Point(t[0].x,t[0].y),s=new r.Point(t[1].x,t[1].y),a=t.length,c=1,l=0,h=a>2;for(e=e||0,h&&(c=t[2].x<s.x?-1:t[2].x===s.x?0:1,l=t[2].y<s.y?-1:t[2].y===s.y?0:1),n.push(["M",o.x-c*e,o.y-l*e]),i=1;i<a;i++){if(!o.eq(s)){var u=o.midPointFrom(s);n.push(["Q",o.x,o.y,u.x,u.y])}o=t[i],i+1<t.length&&(s=t[i+1])}return h&&(c=o.x>t[i-2].x?1:o.x===t[i-2].x?0:-1,l=o.y>t[i-2].y?1:o.y===t[i-2].y?0:-1),n.push(["L",o.x+c*e,o.y+l*e]),n},r.util.getPathSegmentsInfo=g,r.util.getBoundsOfCurve=function(e,i,n,o,s,a,c,l){var h;if(r.cachesBoundsOfCurve&&(h=t.call(arguments),r.boundsOfCurveCache[h]))return r.boundsOfCurveCache[h];var u,f,d,g,p,v,m,y,b=Math.sqrt,x=Math.min,_=Math.max,C=Math.abs,S=[],w=[[],[]];f=6*e-12*n+6*s,u=-3*e+9*n-9*s+3*c,d=3*n-3*e;for(var T=0;T<2;++T)if(T>0&&(f=6*i-12*o+6*a,u=-3*i+9*o-9*a+3*l,d=3*o-3*i),C(u)<1e-12){if(C(f)<1e-12)continue;0<(g=-d/f)&&g<1&&S.push(g)}else(m=f*f-4*d*u)<0||(0<(p=(-f+(y=b(m)))/(2*u))&&p<1&&S.push(p),0<(v=(-f-y)/(2*u))&&v<1&&S.push(v));for(var O,E,k,M=S.length,A=M;M--;)O=(k=1-(g=S[M]))*k*k*e+3*k*k*g*n+3*k*g*g*s+g*g*g*c,w[0][M]=O,E=k*k*k*i+3*k*k*g*o+3*k*g*g*a+g*g*g*l,w[1][M]=E;w[0][A]=e,w[1][A]=i,w[0][A+1]=c,w[1][A+1]=l;var D=[{x:x.apply(null,w[0]),y:x.apply(null,w[1])},{x:_.apply(null,w[0]),y:_.apply(null,w[1])}];return r.cachesBoundsOfCurve&&(r.boundsOfCurveCache[h]=D),D},r.util.getPointOnPath=function(t,e,i){i||(i=g(t));for(var n=0;e-i[n].length>0&&n<i.length-2;)e-=i[n].length,n++;var o,s=i[n],a=e/s.length,c=s.command,l=t[n];switch(c){case"M":return{x:s.x,y:s.y,angle:0};case"Z":case"z":return(o=new r.Point(s.x,s.y).lerp(new r.Point(s.destX,s.destY),a)).angle=Math.atan2(s.destY-s.y,s.destX-s.x),o;case"L":return(o=new r.Point(s.x,s.y).lerp(new r.Point(l[1],l[2]),a)).angle=Math.atan2(l[2]-s.y,l[1]-s.x),o;case"C":case"Q":return d(s,e)}},r.util.transformPath=function(t,e,i){return i&&(e=r.util.multiplyTransformMatrices(e,[1,0,0,1,-i.x,-i.y])),t.map((function(t){for(var i=t.slice(0),n={},o=1;o<t.length-1;o+=2)n.x=t[o],n.y=t[o+1],n=r.util.transformPoint(n,e),i[o]=n.x,i[o+1]=n.y;return i}))}}(),function(){var t=Array.prototype.slice;function e(t,e,i){if(t&&0!==t.length){var n=t.length-1,r=e?t[n][e]:t[n];if(e)for(;n--;)i(t[n][e],r)&&(r=t[n][e]);else for(;n--;)i(t[n],r)&&(r=t[n]);return r}}r.util.array={fill:function(t,e){for(var i=t.length;i--;)t[i]=e;return t},invoke:function(e,i){for(var n=t.call(arguments,2),r=[],o=0,s=e.length;o<s;o++)r[o]=n.length?e[o][i].apply(e[o],n):e[o][i].call(e[o]);return r},min:function(t,i){return e(t,i,(function(t,e){return t<e}))},max:function(t,i){return e(t,i,(function(t,e){return t>=e}))}}}(),function(){function t(e,i,n){if(n)if(!r.isLikelyNode&&i instanceof Element)e=i;else if(i instanceof Array){e=[];for(var o=0,s=i.length;o<s;o++)e[o]=t({},i[o],n)}else if(i&&"object"===typeof i)for(var a in i)"canvas"===a||"group"===a?e[a]=null:i.hasOwnProperty(a)&&(e[a]=t({},i[a],n));else e=i;else for(var a in i)e[a]=i[a];return e}r.util.object={extend:t,clone:function(e,i){return t({},e,i)}},r.util.object.extend(r.util,r.Observable)}(),function(){function t(t,e){var i=t.charCodeAt(e);if(isNaN(i))return"";if(i<55296||i>57343)return t.charAt(e);if(55296<=i&&i<=56319){if(t.length<=e+1)throw"High surrogate without following low surrogate";var n=t.charCodeAt(e+1);if(56320>n||n>57343)throw"High surrogate without following low surrogate";return t.charAt(e)+t.charAt(e+1)}if(0===e)throw"Low surrogate without preceding high surrogate";var r=t.charCodeAt(e-1);if(55296>r||r>56319)throw"Low surrogate without preceding high surrogate";return!1}r.util.string={camelize:function(t){return t.replace(/-+(.)?/g,(function(t,e){return e?e.toUpperCase():""}))},capitalize:function(t,e){return t.charAt(0).toUpperCase()+(e?t.slice(1):t.slice(1).toLowerCase())},escapeXml:function(t){return t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},graphemeSplit:function(e){var i,n=0,r=[];for(n=0;n<e.length;n++)!1!==(i=t(e,n))&&r.push(i);return r}}}(),function(){var t=Array.prototype.slice,e=function(){},i=function(){for(var t in{toString:1})if("toString"===t)return!1;return!0}(),n=function(t,e,n){for(var r in e)r in t.prototype&&"function"===typeof t.prototype[r]&&(e[r]+"").indexOf("callSuper")>-1?t.prototype[r]=function(t){return function(){var i=this.constructor.superclass;this.constructor.superclass=n;var r=e[t].apply(this,arguments);if(this.constructor.superclass=i,"initialize"!==t)return r}}(r):t.prototype[r]=e[r],i&&(e.toString!==Object.prototype.toString&&(t.prototype.toString=e.toString),e.valueOf!==Object.prototype.valueOf&&(t.prototype.valueOf=e.valueOf))};function o(){}function s(e){for(var i=null,n=this;n.constructor.superclass;){var r=n.constructor.superclass.prototype[e];if(n[e]!==r){i=r;break}n=n.constructor.superclass.prototype}if(i)return arguments.length>1?i.apply(this,t.call(arguments,1)):i.call(this)}r.util.createClass=function(){var i=null,r=t.call(arguments,0);function a(){this.initialize.apply(this,arguments)}"function"===typeof r[0]&&(i=r.shift()),a.superclass=i,a.subclasses=[],i&&(o.prototype=i.prototype,a.prototype=new o,i.subclasses.push(a));for(var c=0,l=r.length;c<l;c++)n(a,r[c],i);return a.prototype.initialize||(a.prototype.initialize=e),a.prototype.constructor=a,a.prototype.callSuper=s,a}}(),function(){var t=!!r.document.createElement("div").attachEvent,e=["touchstart","touchmove","touchend"];r.util.addListener=function(e,i,n,r){e&&e.addEventListener(i,n,!t&&r)},r.util.removeListener=function(e,i,n,r){e&&e.removeEventListener(i,n,!t&&r)},r.util.getPointer=function(t){var e=t.target,i=r.util.getScrollLeftTop(e),n=function(t){var e=t.changedTouches;return e&&e[0]?e[0]:t}(t);return{x:n.clientX+i.left,y:n.clientY+i.top}},r.util.isTouchEvent=function(t){return e.indexOf(t.type)>-1||"touch"===t.pointerType}}(),function(){var t=r.document.createElement("div"),e="string"===typeof t.style.opacity,i="string"===typeof t.style.filter,n=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,o=function(t){return t};e?o=function(t,e){return t.style.opacity=e,t}:i&&(o=function(t,e){var i=t.style;return t.currentStyle&&!t.currentStyle.hasLayout&&(i.zoom=1),n.test(i.filter)?(e=e>=.9999?"":"alpha(opacity="+100*e+")",i.filter=i.filter.replace(n,e)):i.filter+=" alpha(opacity="+100*e+")",t}),r.util.setStyle=function(t,e){var i=t.style;if(!i)return t;if("string"===typeof e)return t.style.cssText+=";"+e,e.indexOf("opacity")>-1?o(t,e.match(/opacity:\s*(\d?\.?\d*)/)[1]):t;for(var n in e)if("opacity"===n)o(t,e[n]);else{var r="float"===n||"cssFloat"===n?"undefined"===typeof i.styleFloat?"cssFloat":"styleFloat":n;i.setProperty(r,e[n])}return t}}(),function(){var t=Array.prototype.slice;var e,i,n=function(e){return t.call(e,0)};try{e=n(r.document.childNodes)instanceof Array}catch(a){}function o(t,e){var i=r.document.createElement(t);for(var n in e)"class"===n?i.className=e[n]:"for"===n?i.htmlFor=e[n]:i.setAttribute(n,e[n]);return i}function s(t){for(var e=0,i=0,n=r.document.documentElement,o=r.document.body||{scrollLeft:0,scrollTop:0};t&&(t.parentNode||t.host)&&((t=t.parentNode||t.host)===r.document?(e=o.scrollLeft||n.scrollLeft||0,i=o.scrollTop||n.scrollTop||0):(e+=t.scrollLeft||0,i+=t.scrollTop||0),1!==t.nodeType||"fixed"!==t.style.position););return{left:e,top:i}}e||(n=function(t){for(var e=new Array(t.length),i=t.length;i--;)e[i]=t[i];return e}),i=r.document.defaultView&&r.document.defaultView.getComputedStyle?function(t,e){var i=r.document.defaultView.getComputedStyle(t,null);return i?i[e]:void 0}:function(t,e){var i=t.style[e];return!i&&t.currentStyle&&(i=t.currentStyle[e]),i},function(){var t=r.document.documentElement.style,e="userSelect"in t?"userSelect":"MozUserSelect"in t?"MozUserSelect":"WebkitUserSelect"in t?"WebkitUserSelect":"KhtmlUserSelect"in t?"KhtmlUserSelect":"";r.util.makeElementUnselectable=function(t){return"undefined"!==typeof t.onselectstart&&(t.onselectstart=r.util.falseFunction),e?t.style[e]="none":"string"===typeof t.unselectable&&(t.unselectable="on"),t},r.util.makeElementSelectable=function(t){return"undefined"!==typeof t.onselectstart&&(t.onselectstart=null),e?t.style[e]="":"string"===typeof t.unselectable&&(t.unselectable=""),t}}(),r.util.setImageSmoothing=function(t,e){t.imageSmoothingEnabled=t.imageSmoothingEnabled||t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled||t.oImageSmoothingEnabled,t.imageSmoothingEnabled=e},r.util.getById=function(t){return"string"===typeof t?r.document.getElementById(t):t},r.util.toArray=n,r.util.addClass=function(t,e){t&&-1===(" "+t.className+" ").indexOf(" "+e+" ")&&(t.className+=(t.className?" ":"")+e)},r.util.makeElement=o,r.util.wrapElement=function(t,e,i){return"string"===typeof e&&(e=o(e,i)),t.parentNode&&t.parentNode.replaceChild(e,t),e.appendChild(t),e},r.util.getScrollLeftTop=s,r.util.getElementOffset=function(t){var e,n,r=t&&t.ownerDocument,o={left:0,top:0},a={left:0,top:0},c={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!r)return a;for(var l in c)a[c[l]]+=parseInt(i(t,l),10)||0;return e=r.documentElement,"undefined"!==typeof t.getBoundingClientRect&&(o=t.getBoundingClientRect()),n=s(t),{left:o.left+n.left-(e.clientLeft||0)+a.left,top:o.top+n.top-(e.clientTop||0)+a.top}},r.util.getNodeCanvas=function(t){var e=r.jsdomImplForWrapper(t);return e._canvas||e._image},r.util.cleanUpJsdomNode=function(t){if(r.isLikelyNode){var e=r.jsdomImplForWrapper(t);e&&(e._image=null,e._canvas=null,e._currentSrc=null,e._attributes=null,e._classList=null)}}}(),function(){function t(){}r.util.request=function(e,i){i||(i={});var n=i.method?i.method.toUpperCase():"GET",o=i.onComplete||function(){},s=new r.window.XMLHttpRequest,a=i.body||i.parameters;return s.onreadystatechange=function(){4===s.readyState&&(o(s),s.onreadystatechange=t)},"GET"===n&&(a=null,"string"===typeof i.parameters&&(e=function(t,e){return t+(/\?/.test(t)?"&":"?")+e}(e,i.parameters))),s.open(n,e,!0),"POST"!==n&&"PUT"!==n||s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.send(a),s}}(),r.log=function(){},r.warn=console.warn,function(){var t=r.util.object.extend,e=r.util.object.clone,i=[];function n(){return!1}function o(t,e,i,n){return-i*Math.cos(t/n*(Math.PI/2))+i+e}r.util.object.extend(i,{cancelAll:function(){var t=this.splice(0);return t.forEach((function(t){t.cancel()})),t},cancelByCanvas:function(t){if(!t)return[];var e=this.filter((function(e){return"object"===typeof e.target&&e.target.canvas===t}));return e.forEach((function(t){t.cancel()})),e},cancelByTarget:function(t){var e=this.findAnimationsByTarget(t);return e.forEach((function(t){t.cancel()})),e},findAnimationIndex:function(t){return this.indexOf(this.findAnimation(t))},findAnimation:function(t){return this.find((function(e){return e.cancel===t}))},findAnimationsByTarget:function(t){return t?this.filter((function(e){return e.target===t})):[]}});var s=r.window.requestAnimationFrame||r.window.webkitRequestAnimationFrame||r.window.mozRequestAnimationFrame||r.window.oRequestAnimationFrame||r.window.msRequestAnimationFrame||function(t){return r.window.setTimeout(t,1e3/60)},a=r.window.cancelAnimationFrame||r.window.clearTimeout;function c(){return s.apply(r.window,arguments)}r.util.animate=function(i){i||(i={});var s,a=!1,l=function(){var t=r.runningAnimations.indexOf(s);return t>-1&&r.runningAnimations.splice(t,1)[0]};return s=t(e(i),{cancel:function(){return a=!0,l()},currentValue:"startValue"in i?i.startValue:0,completionRate:0,durationRate:0}),r.runningAnimations.push(s),c((function(t){var e,r=t||+new Date,h=i.duration||500,u=r+h,f=i.onChange||n,d=i.abort||n,g=i.onComplete||n,p=i.easing||o,v="startValue"in i&&i.startValue.length>0,m="startValue"in i?i.startValue:0,y="endValue"in i?i.endValue:100,b=i.byValue||(v?m.map((function(t,e){return y[e]-m[e]})):y-m);i.onStart&&i.onStart(),function t(i){var n=(e=i||+new Date)>u?h:e-r,o=n/h,x=v?m.map((function(t,e){return p(n,m[e],b[e],h)})):p(n,m,b,h),_=v?Math.abs((x[0]-m[0])/b[0]):Math.abs((x-m)/b);if(s.currentValue=v?x.slice():x,s.completionRate=_,s.durationRate=o,!a){if(!d(x,_,o))return e>u?(s.currentValue=v?y.slice():y,s.completionRate=1,s.durationRate=1,f(v?y.slice():y,1,1),g(y,1,1),void l()):(f(x,_,o),void c(t));l()}}(r)})),s.cancel},r.util.requestAnimFrame=c,r.util.cancelAnimFrame=function(){return a.apply(r.window,arguments)},r.runningAnimations=i}(),function(){function t(t,e,i){var n="rgba("+parseInt(t[0]+i*(e[0]-t[0]),10)+","+parseInt(t[1]+i*(e[1]-t[1]),10)+","+parseInt(t[2]+i*(e[2]-t[2]),10);return n+=","+(t&&e?parseFloat(t[3]+i*(e[3]-t[3])):1),n+=")"}r.util.animateColor=function(e,i,n,o){var s=new r.Color(e).getSource(),a=new r.Color(i).getSource(),c=o.onComplete,l=o.onChange;return o=o||{},r.util.animate(r.util.object.extend(o,{duration:n||500,startValue:s,endValue:a,byValue:a,easing:function(e,i,n,r){return t(i,n,o.colorEasing?o.colorEasing(e,r):1-Math.cos(e/r*(Math.PI/2)))},onComplete:function(e,i,n){if(c)return c(t(a,a,0),i,n)},onChange:function(e,i,n){if(l){if(Array.isArray(e))return l(t(e,e,0),i,n);l(e,i,n)}}}))}}(),function(){function t(t,e,i,n){return t<Math.abs(e)?(t=e,n=i/4):n=0===e&&0===t?i/(2*Math.PI)*Math.asin(1):i/(2*Math.PI)*Math.asin(e/t),{a:t,c:e,p:i,s:n}}function e(t,e,i){return t.a*Math.pow(2,10*(e-=1))*Math.sin((e*i-t.s)*(2*Math.PI)/t.p)}function i(t,e,i,r){return i-n(r-t,0,i,r)+e}function n(t,e,i,n){return(t/=n)<1/2.75?i*(7.5625*t*t)+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e}r.util.ease={easeInQuad:function(t,e,i,n){return i*(t/=n)*t+e},easeOutQuad:function(t,e,i,n){return-i*(t/=n)*(t-2)+e},easeInOutQuad:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e},easeInCubic:function(t,e,i,n){return i*(t/=n)*t*t+e},easeOutCubic:function(t,e,i,n){return i*((t=t/n-1)*t*t+1)+e},easeInOutCubic:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t+e:i/2*((t-=2)*t*t+2)+e},easeInQuart:function(t,e,i,n){return i*(t/=n)*t*t*t+e},easeOutQuart:function(t,e,i,n){return-i*((t=t/n-1)*t*t*t-1)+e},easeInOutQuart:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t*t+e:-i/2*((t-=2)*t*t*t-2)+e},easeInQuint:function(t,e,i,n){return i*(t/=n)*t*t*t*t+e},easeOutQuint:function(t,e,i,n){return i*((t=t/n-1)*t*t*t*t+1)+e},easeInOutQuint:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e},easeInSine:function(t,e,i,n){return-i*Math.cos(t/n*(Math.PI/2))+i+e},easeOutSine:function(t,e,i,n){return i*Math.sin(t/n*(Math.PI/2))+e},easeInOutSine:function(t,e,i,n){return-i/2*(Math.cos(Math.PI*t/n)-1)+e},easeInExpo:function(t,e,i,n){return 0===t?e:i*Math.pow(2,10*(t/n-1))+e},easeOutExpo:function(t,e,i,n){return t===n?e+i:i*(1-Math.pow(2,-10*t/n))+e},easeInOutExpo:function(t,e,i,n){return 0===t?e:t===n?e+i:(t/=n/2)<1?i/2*Math.pow(2,10*(t-1))+e:i/2*(2-Math.pow(2,-10*--t))+e},easeInCirc:function(t,e,i,n){return-i*(Math.sqrt(1-(t/=n)*t)-1)+e},easeOutCirc:function(t,e,i,n){return i*Math.sqrt(1-(t=t/n-1)*t)+e},easeInOutCirc:function(t,e,i,n){return(t/=n/2)<1?-i/2*(Math.sqrt(1-t*t)-1)+e:i/2*(Math.sqrt(1-(t-=2)*t)+1)+e},easeInElastic:function(i,n,r,o){var s=0;return 0===i?n:1===(i/=o)?n+r:(s||(s=.3*o),-e(t(r,r,s,1.70158),i,o)+n)},easeOutElastic:function(e,i,n,r){var o=0;if(0===e)return i;if(1===(e/=r))return i+n;o||(o=.3*r);var s=t(n,n,o,1.70158);return s.a*Math.pow(2,-10*e)*Math.sin((e*r-s.s)*(2*Math.PI)/s.p)+s.c+i},easeInOutElastic:function(i,n,r,o){var s=0;if(0===i)return n;if(2===(i/=o/2))return n+r;s||(s=o*(.3*1.5));var a=t(r,r,s,1.70158);return i<1?-.5*e(a,i,o)+n:a.a*Math.pow(2,-10*(i-=1))*Math.sin((i*o-a.s)*(2*Math.PI)/a.p)*.5+a.c+n},easeInBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),i*(t/=n)*t*((r+1)*t-r)+e},easeOutBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),i*((t=t/n-1)*t*((r+1)*t+r)+1)+e},easeInOutBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),(t/=n/2)<1?i/2*(t*t*((1+(r*=1.525))*t-r))+e:i/2*((t-=2)*t*((1+(r*=1.525))*t+r)+2)+e},easeInBounce:i,easeOutBounce:n,easeInOutBounce:function(t,e,r,o){return t<o/2?.5*i(2*t,0,r,o)+e:.5*n(2*t-o,0,r,o)+.5*r+e}}}(),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r=e.util.toFixed,o=e.util.parseUnit,s=e.util.multiplyTransformMatrices,a={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},c={stroke:"strokeOpacity",fill:"fillOpacity"},l="font-size",h="clip-path";function u(t){return t in a?a[t]:t}function f(t,i,n,r){var a,c=Array.isArray(i);if("fill"!==t&&"stroke"!==t||"none"!==i){if("strokeUniform"===t)return"non-scaling-stroke"===i;if("strokeDashArray"===t)i="none"===i?null:i.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===t)i=n&&n.transformMatrix?s(n.transformMatrix,e.parseTransformAttribute(i)):e.parseTransformAttribute(i);else if("visible"===t)i="none"!==i&&"hidden"!==i,n&&!1===n.visible&&(i=!1);else if("opacity"===t)i=parseFloat(i),n&&"undefined"!==typeof n.opacity&&(i*=n.opacity);else if("textAnchor"===t)i="start"===i?"left":"end"===i?"right":"center";else if("charSpacing"===t)a=o(i,r)/r*1e3;else if("paintFirst"===t){var l=i.indexOf("fill"),h=i.indexOf("stroke");i="fill";(l>-1&&h>-1&&h<l||-1===l&&h>-1)&&(i="stroke")}else{if("href"===t||"xlink:href"===t||"font"===t)return i;if("imageSmoothing"===t)return"optimizeQuality"===i;a=c?i.map(o):o(i,r)}}else i="";return!c&&isNaN(a)?i:a}function d(t){return new RegExp("^("+t.join("|")+")\\b","i")}function g(t,e){var i,n,r,o,s=[];for(r=0,o=e.length;r<o;r++)i=e[r],n=t.getElementsByTagName(i),s=s.concat(Array.prototype.slice.call(n));return s}function p(t,e){var i,n=!0;return(i=v(t,e.pop()))&&e.length&&(n=function(t,e){var i,n=!0;for(;t.parentNode&&1===t.parentNode.nodeType&&e.length;)n&&(i=e.pop()),n=v(t=t.parentNode,i);return 0===e.length}(t,e)),i&&n&&0===e.length}function v(t,e){var i,n,r=t.nodeName,o=t.getAttribute("class"),s=t.getAttribute("id");if(i=new RegExp("^"+r,"i"),e=e.replace(i,""),s&&e.length&&(i=new RegExp("#"+s+"(?![a-zA-Z\\-]+)","i"),e=e.replace(i,"")),o&&e.length)for(n=(o=o.split(" ")).length;n--;)i=new RegExp("\\."+o[n]+"(?![a-zA-Z\\-]+)","i"),e=e.replace(i,"");return 0===e.length}function m(t,e){var i;if(t.getElementById&&(i=t.getElementById(e)),i)return i;var n,r,o,s=t.getElementsByTagName("*");for(r=0,o=s.length;r<o;r++)if(e===(n=s[r]).getAttribute("id"))return n}e.svgValidTagNamesRegEx=d(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]),e.svgViewBoxElementsRegEx=d(["symbol","image","marker","pattern","view","svg"]),e.svgInvalidAncestorsRegEx=d(["pattern","defs","symbol","metadata","clipPath","mask","desc"]),e.svgValidParentsRegEx=d(["symbol","g","a","svg","clipPath","defs"]),e.cssRules={},e.gradientDefs={},e.clipPaths={},e.parseTransformAttribute=function(){function t(t,i,n){t[n]=Math.tan(e.util.degreesToRadians(i[0]))}var i=e.iMatrix,n=e.reNum,r=e.commaWsp,o="(?:"+("(?:(matrix)\\s*\\(\\s*("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")\\s*\\))")+"|"+("(?:(translate)\\s*\\(\\s*("+n+")(?:"+r+"("+n+"))?\\s*\\))")+"|"+("(?:(scale)\\s*\\(\\s*("+n+")(?:"+r+"("+n+"))?\\s*\\))")+"|"+("(?:(rotate)\\s*\\(\\s*("+n+")(?:"+r+"("+n+")"+r+"("+n+"))?\\s*\\))")+"|"+("(?:(skewX)\\s*\\(\\s*("+n+")\\s*\\))")+"|"+("(?:(skewY)\\s*\\(\\s*("+n+")\\s*\\))")+")",s=new RegExp("^\\s*(?:"+("(?:"+o+"(?:"+r+"*"+o+")*)")+"?)\\s*$"),a=new RegExp(o,"g");return function(n){var r=i.concat(),c=[];if(!n||n&&!s.test(n))return r;n.replace(a,(function(n){var s=new RegExp(o).exec(n).filter((function(t){return!!t})),a=s[1],l=s.slice(2).map(parseFloat);switch(a){case"translate":!function(t,e){t[4]=e[0],2===e.length&&(t[5]=e[1])}(r,l);break;case"rotate":l[0]=e.util.degreesToRadians(l[0]),function(t,i){var n=e.util.cos(i[0]),r=e.util.sin(i[0]),o=0,s=0;3===i.length&&(o=i[1],s=i[2]),t[0]=n,t[1]=r,t[2]=-r,t[3]=n,t[4]=o-(n*o-r*s),t[5]=s-(r*o+n*s)}(r,l);break;case"scale":!function(t,e){var i=e[0],n=2===e.length?e[1]:e[0];t[0]=i,t[3]=n}(r,l);break;case"skewX":t(r,l,2);break;case"skewY":t(r,l,1);break;case"matrix":r=l}c.push(r.concat()),r=i.concat()}));for(var l=c[0];c.length>1;)c.shift(),l=e.util.multiplyTransformMatrices(l,c[0]);return l}}();var y=new RegExp("^\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*$");function b(t){if(!e.svgViewBoxElementsRegEx.test(t.nodeName))return{};var i,n,r,s,a,c,l=t.getAttribute("viewBox"),h=1,u=1,f=t.getAttribute("width"),d=t.getAttribute("height"),g=t.getAttribute("x")||0,p=t.getAttribute("y")||0,v=t.getAttribute("preserveAspectRatio")||"",m=!l||!(l=l.match(y)),b=!f||!d||"100%"===f||"100%"===d,x=m&&b,_={},C="",S=0,w=0;if(_.width=0,_.height=0,_.toBeParsed=x,m&&(g||p)&&t.parentNode&&"#document"!==t.parentNode.nodeName&&(C=" translate("+o(g)+" "+o(p)+") ",a=(t.getAttribute("transform")||"")+C,t.setAttribute("transform",a),t.removeAttribute("x"),t.removeAttribute("y")),x)return _;if(m)return _.width=o(f),_.height=o(d),_;if(i=-parseFloat(l[1]),n=-parseFloat(l[2]),r=parseFloat(l[3]),s=parseFloat(l[4]),_.minX=i,_.minY=n,_.viewBoxWidth=r,_.viewBoxHeight=s,b?(_.width=r,_.height=s):(_.width=o(f),_.height=o(d),h=_.width/r,u=_.height/s),"none"!==(v=e.util.parsePreserveAspectRatioAttribute(v)).alignX&&("meet"===v.meetOrSlice&&(u=h=h>u?u:h),"slice"===v.meetOrSlice&&(u=h=h>u?h:u),S=_.width-r*h,w=_.height-s*h,"Mid"===v.alignX&&(S/=2),"Mid"===v.alignY&&(w/=2),"Min"===v.alignX&&(S=0),"Min"===v.alignY&&(w=0)),1===h&&1===u&&0===i&&0===n&&0===g&&0===p)return _;if((g||p)&&"#document"!==t.parentNode.nodeName&&(C=" translate("+o(g)+" "+o(p)+") "),a=C+" matrix("+h+" 0 0 "+u+" "+(i*h+S)+" "+(n*u+w)+") ","svg"===t.nodeName){for(c=t.ownerDocument.createElementNS(e.svgNS,"g");t.firstChild;)c.appendChild(t.firstChild);t.appendChild(c)}else(c=t).removeAttribute("x"),c.removeAttribute("y"),a=c.getAttribute("transform")+a;return c.setAttribute("transform",a),_}function x(t,e){var i="xlink:href",n=m(t,e.getAttribute(i).slice(1));if(n&&n.getAttribute(i)&&x(t,n),["gradientTransform","x1","x2","y1","y2","gradientUnits","cx","cy","r","fx","fy"].forEach((function(t){n&&!e.hasAttribute(t)&&n.hasAttribute(t)&&e.setAttribute(t,n.getAttribute(t))})),!e.children.length)for(var r=n.cloneNode(!0);r.firstChild;)e.appendChild(r.firstChild);e.removeAttribute(i)}e.parseSVGDocument=function(t,i,r,o){if(t){!function(t){for(var i=g(t,["use","svg:use"]),n=0;i.length&&n<i.length;){var r=i[n],o=r.getAttribute("xlink:href")||r.getAttribute("href");if(null===o)return;var s,a,c,l,h=o.slice(1),u=r.getAttribute("x")||0,f=r.getAttribute("y")||0,d=m(t,h).cloneNode(!0),p=(d.getAttribute("transform")||"")+" translate("+u+", "+f+")",v=i.length,y=e.svgNS;if(b(d),/^svg$/i.test(d.nodeName)){var x=d.ownerDocument.createElementNS(y,"g");for(a=0,l=(c=d.attributes).length;a<l;a++)s=c.item(a),x.setAttributeNS(y,s.nodeName,s.nodeValue);for(;d.firstChild;)x.appendChild(d.firstChild);d=x}for(a=0,l=(c=r.attributes).length;a<l;a++)"x"!==(s=c.item(a)).nodeName&&"y"!==s.nodeName&&"xlink:href"!==s.nodeName&&"href"!==s.nodeName&&("transform"===s.nodeName?p=s.nodeValue+" "+p:d.setAttribute(s.nodeName,s.nodeValue));d.setAttribute("transform",p),d.setAttribute("instantiated_by_use","1"),d.removeAttribute("id"),r.parentNode.replaceChild(d,r),i.length===v&&n++}}(t);var s,a,c=e.Object.__uid++,l=b(t),h=e.util.toArray(t.getElementsByTagName("*"));if(l.crossOrigin=o&&o.crossOrigin,l.svgUid=c,0===h.length&&e.isLikelyNode){var u=[];for(s=0,a=(h=t.selectNodes('//*[name(.)!="svg"]')).length;s<a;s++)u[s]=h[s];h=u}var f=h.filter((function(t){return b(t),e.svgValidTagNamesRegEx.test(t.nodeName.replace("svg:",""))&&!function(t,e){for(;t&&(t=t.parentNode);)if(t.nodeName&&e.test(t.nodeName.replace("svg:",""))&&!t.getAttribute("instantiated_by_use"))return!0;return!1}(t,e.svgInvalidAncestorsRegEx)}));if(!f||f&&!f.length)i&&i([],{});else{var d={};h.filter((function(t){return"clipPath"===t.nodeName.replace("svg:","")})).forEach((function(t){var i=t.getAttribute("id");d[i]=e.util.toArray(t.getElementsByTagName("*")).filter((function(t){return e.svgValidTagNamesRegEx.test(t.nodeName.replace("svg:",""))}))})),e.gradientDefs[c]=e.getGradientDefs(t),e.cssRules[c]=e.getCSSRules(t),e.clipPaths[c]=d,e.parseElements(f,(function(t,n){i&&(i(t,l,n,h),delete e.gradientDefs[c],delete e.cssRules[c],delete e.clipPaths[c])}),n(l),r,o)}}};var _=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+e.reNum+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+e.reNum+"))?\\s+(.*)");i(e,{parseFontDeclaration:function(t,e){var i=t.match(_);if(i){var n=i[1],r=i[3],s=i[4],a=i[5],c=i[6];n&&(e.fontStyle=n),r&&(e.fontWeight=isNaN(parseFloat(r))?r:parseFloat(r)),s&&(e.fontSize=o(s)),c&&(e.fontFamily=c),a&&(e.lineHeight="normal"===a?1:a)}},getGradientDefs:function(t){var e,i=g(t,["linearGradient","radialGradient","svg:linearGradient","svg:radialGradient"]),n=0,r={};for(n=i.length;n--;)(e=i[n]).getAttribute("xlink:href")&&x(t,e),r[e.getAttribute("id")]=e;return r},parseAttributes:function(t,n,s){if(t){var a,d,g,v={};"undefined"===typeof s&&(s=t.getAttribute("svgUid")),t.parentNode&&e.svgValidParentsRegEx.test(t.parentNode.nodeName)&&(v=e.parseAttributes(t.parentNode,n,s));var m=n.reduce((function(e,i){return(a=t.getAttribute(i))&&(e[i]=a),e}),{}),y=i(function(t,i){var n={};for(var r in e.cssRules[i])if(p(t,r.split(" ")))for(var o in e.cssRules[i][r])n[o]=e.cssRules[i][r][o];return n}(t,s),e.parseStyleAttribute(t));m=i(m,y),y[h]&&t.setAttribute(h,y[h]),d=g=v.fontSize||e.Text.DEFAULT_SVG_FONT_SIZE,m[l]&&(m[l]=d=o(m[l],g));var b,x,_={};for(var C in m)x=f(b=u(C),m[C],v,d),_[b]=x;_&&_.font&&e.parseFontDeclaration(_.font,_);var S=i(v,_);return e.svgValidParentsRegEx.test(t.nodeName)?S:function(t){for(var i in c)if("undefined"!==typeof t[c[i]]&&""!==t[i]){if("undefined"===typeof t[i]){if(!e.Object.prototype[i])continue;t[i]=e.Object.prototype[i]}if(0!==t[i].indexOf("url(")){var n=new e.Color(t[i]);t[i]=n.setAlpha(r(n.getAlpha()*t[c[i]],2)).toRgba()}}return t}(S)}},parseElements:function(t,i,n,r,o){new e.ElementsParser(t,i,n,r,o).parse()},parseStyleAttribute:function(t){var e={},i=t.getAttribute("style");return i?("string"===typeof i?function(t,e){var i,n;t.replace(/;\s*$/,"").split(";").forEach((function(t){var r=t.split(":");i=r[0].trim().toLowerCase(),n=r[1].trim(),e[i]=n}))}(i,e):function(t,e){var i,n;for(var r in t)"undefined"!==typeof t[r]&&(i=r.toLowerCase(),n=t[r],e[i]=n)}(i,e),e):e},parsePointsAttribute:function(t){if(!t)return null;var e,i,n=[];for(e=0,i=(t=(t=t.replace(/,/g," ").trim()).split(/\s+/)).length;e<i;e+=2)n.push({x:parseFloat(t[e]),y:parseFloat(t[e+1])});return n},getCSSRules:function(t){var i,n,r=t.getElementsByTagName("style"),o={};for(i=0,n=r.length;i<n;i++){var s=r[i].textContent;""!==(s=s.replace(/\/\*[\s\S]*?\*\//g,"")).trim()&&s.split("}").filter((function(t){return t.trim()})).forEach((function(t){var r=t.split("{"),s={},a=r[1].trim().split(";").filter((function(t){return t.trim()}));for(i=0,n=a.length;i<n;i++){var c=a[i].split(":"),l=c[0].trim(),h=c[1].trim();s[l]=h}(t=r[0].trim()).split(",").forEach((function(t){""!==(t=t.replace(/^svg/i,"").trim())&&(o[t]?e.util.object.extend(o[t],s):o[t]=e.util.object.clone(s))}))}))}return o},loadSVGFromURL:function(t,i,n,r){t=t.replace(/^\n\s*/,"").trim(),new e.util.request(t,{method:"get",onComplete:function(t){var o=t.responseXML;if(!o||!o.documentElement)return i&&i(null),!1;e.parseSVGDocument(o.documentElement,(function(t,e,n,r){i&&i(t,e,n,r)}),n,r)}})},loadSVGFromString:function(t,i,n,r){var o=(new e.window.DOMParser).parseFromString(t.trim(),"text/xml");e.parseSVGDocument(o.documentElement,(function(t,e,n,r){i(t,e,n,r)}),n,r)}})}(e),r.ElementsParser=function(t,e,i,n,r,o){this.elements=t,this.callback=e,this.options=i,this.reviver=n,this.svgUid=i&&i.svgUid||0,this.parsingOptions=r,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=o},(n=r.ElementsParser.prototype).parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},n.createObjects=function(){var t=this;this.elements.forEach((function(e,i){e.setAttribute("svgUid",t.svgUid),t.createObject(e,i)}))},n.findTag=function(t){return r[r.util.string.capitalize(t.tagName.replace("svg:",""))]},n.createObject=function(t,e){var i=this.findTag(t);if(i&&i.fromElement)try{i.fromElement(t,this.createCallback(e,t),this.options)}catch(n){r.log(n)}else this.checkIfDone()},n.createCallback=function(t,e){var i=this;return function(n){var o;i.resolveGradient(n,e,"fill"),i.resolveGradient(n,e,"stroke"),n instanceof r.Image&&n._originalElement&&(o=n.parsePreserveAspectRatioAttribute(e)),n._removeTransformMatrix(o),i.resolveClipPath(n,e),i.reviver&&i.reviver(e,n),i.instances[t]=n,i.checkIfDone()}},n.extractPropertyDefinition=function(t,e,i){var n=t[e],o=this.regexUrl;if(o.test(n)){o.lastIndex=0;var s=o.exec(n)[1];return o.lastIndex=0,r[i][this.svgUid][s]}},n.resolveGradient=function(t,e,i){var n=this.extractPropertyDefinition(t,i,"gradientDefs");if(n){var o=e.getAttribute(i+"-opacity"),s=r.Gradient.fromElement(n,t,o,this.options);t.set(i,s)}},n.createClipPathCallback=function(t,e){return function(t){t._removeTransformMatrix(),t.fillRule=t.clipRule,e.push(t)}},n.resolveClipPath=function(t,e){var i,n,o,s,a=this.extractPropertyDefinition(t,"clipPath","clipPaths");if(a){o=[],n=r.util.invertTransform(t.calcTransformMatrix());for(var c=a[0].parentNode,l=e;l.parentNode&&l.getAttribute("clip-path")!==t.clipPath;)l=l.parentNode;l.parentNode.appendChild(c);for(var h=0;h<a.length;h++)i=a[h],this.findTag(i).fromElement(i,this.createClipPathCallback(t,o),this.options);a=1===o.length?o[0]:new r.Group(o),s=r.util.multiplyTransformMatrices(n,a.calcTransformMatrix()),a.clipPath&&this.resolveClipPath(a,l);var u=r.util.qrDecompose(s);a.flipX=!1,a.flipY=!1,a.set("scaleX",u.scaleX),a.set("scaleY",u.scaleY),a.angle=u.angle,a.skewX=u.skewX,a.skewY=0,a.setPositionByOrigin({x:u.translateX,y:u.translateY},"center","center"),t.clipPath=a}else delete t.clipPath},n.checkIfDone=function(){0===--this.numElements&&(this.instances=this.instances.filter((function(t){return null!=t})),this.callback(this.instances,this.elements))},function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t,e){this.x=t,this.y=e}e.Point?e.warn("fabric.Point is already defined"):(e.Point=i,i.prototype={type:"point",constructor:i,add:function(t){return new i(this.x+t.x,this.y+t.y)},addEquals:function(t){return this.x+=t.x,this.y+=t.y,this},scalarAdd:function(t){return new i(this.x+t,this.y+t)},scalarAddEquals:function(t){return this.x+=t,this.y+=t,this},subtract:function(t){return new i(this.x-t.x,this.y-t.y)},subtractEquals:function(t){return this.x-=t.x,this.y-=t.y,this},scalarSubtract:function(t){return new i(this.x-t,this.y-t)},scalarSubtractEquals:function(t){return this.x-=t,this.y-=t,this},multiply:function(t){return new i(this.x*t,this.y*t)},multiplyEquals:function(t){return this.x*=t,this.y*=t,this},divide:function(t){return new i(this.x/t,this.y/t)},divideEquals:function(t){return this.x/=t,this.y/=t,this},eq:function(t){return this.x===t.x&&this.y===t.y},lt:function(t){return this.x<t.x&&this.y<t.y},lte:function(t){return this.x<=t.x&&this.y<=t.y},gt:function(t){return this.x>t.x&&this.y>t.y},gte:function(t){return this.x>=t.x&&this.y>=t.y},lerp:function(t,e){return"undefined"===typeof e&&(e=.5),e=Math.max(Math.min(1,e),0),new i(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)},distanceFrom:function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},midPointFrom:function(t){return this.lerp(t)},min:function(t){return new i(Math.min(this.x,t.x),Math.min(this.y,t.y))},max:function(t){return new i(Math.max(this.x,t.x),Math.max(this.y,t.y))},toString:function(){return this.x+","+this.y},setXY:function(t,e){return this.x=t,this.y=e,this},setX:function(t){return this.x=t,this},setY:function(t){return this.y=t,this},setFromPoint:function(t){return this.x=t.x,this.y=t.y,this},swap:function(t){var e=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=i},clone:function(){return new i(this.x,this.y)}})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t){this.status=t,this.points=[]}e.Intersection?e.warn("fabric.Intersection is already defined"):(e.Intersection=i,e.Intersection.prototype={constructor:i,appendPoint:function(t){return this.points.push(t),this},appendPoints:function(t){return this.points=this.points.concat(t),this}},e.Intersection.intersectLineLine=function(t,n,r,o){var s,a=(o.x-r.x)*(t.y-r.y)-(o.y-r.y)*(t.x-r.x),c=(n.x-t.x)*(t.y-r.y)-(n.y-t.y)*(t.x-r.x),l=(o.y-r.y)*(n.x-t.x)-(o.x-r.x)*(n.y-t.y);if(0!==l){var h=a/l,u=c/l;0<=h&&h<=1&&0<=u&&u<=1?(s=new i("Intersection")).appendPoint(new e.Point(t.x+h*(n.x-t.x),t.y+h*(n.y-t.y))):s=new i}else s=new i(0===a||0===c?"Coincident":"Parallel");return s},e.Intersection.intersectLinePolygon=function(t,e,n){var r,o,s,a,c=new i,l=n.length;for(a=0;a<l;a++)r=n[a],o=n[(a+1)%l],s=i.intersectLineLine(t,e,r,o),c.appendPoints(s.points);return c.points.length>0&&(c.status="Intersection"),c},e.Intersection.intersectPolygonPolygon=function(t,e){var n,r=new i,o=t.length;for(n=0;n<o;n++){var s=t[n],a=t[(n+1)%o],c=i.intersectLinePolygon(s,a,e);r.appendPoints(c.points)}return r.points.length>0&&(r.status="Intersection"),r},e.Intersection.intersectPolygonRectangle=function(t,n,r){var o=n.min(r),s=n.max(r),a=new e.Point(s.x,o.y),c=new e.Point(o.x,s.y),l=i.intersectLinePolygon(o,a,t),h=i.intersectLinePolygon(a,s,t),u=i.intersectLinePolygon(s,c,t),f=i.intersectLinePolygon(c,o,t),d=new i;return d.appendPoints(l.points),d.appendPoints(h.points),d.appendPoints(u.points),d.appendPoints(f.points),d.points.length>0&&(d.status="Intersection"),d})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t){t?this._tryParsingColor(t):this.setSource([0,0,0,1])}function n(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}e.Color?e.warn("fabric.Color is already defined."):(e.Color=i,e.Color.prototype={_tryParsingColor:function(t){var e;t in i.colorNameMap&&(t=i.colorNameMap[t]),"transparent"===t&&(e=[255,255,255,0]),e||(e=i.sourceFromHex(t)),e||(e=i.sourceFromRgb(t)),e||(e=i.sourceFromHsl(t)),e||(e=[0,0,0,1]),e&&this.setSource(e)},_rgbToHsl:function(t,i,n){t/=255,i/=255,n/=255;var r,o,s,a=e.util.array.max([t,i,n]),c=e.util.array.min([t,i,n]);if(s=(a+c)/2,a===c)r=o=0;else{var l=a-c;switch(o=s>.5?l/(2-a-c):l/(a+c),a){case t:r=(i-n)/l+(i<n?6:0);break;case i:r=(n-t)/l+2;break;case n:r=(t-i)/l+4}r/=6}return[Math.round(360*r),Math.round(100*o),Math.round(100*s)]},getSource:function(){return this._source},setSource:function(t){this._source=t},toRgb:function(){var t=this.getSource();return"rgb("+t[0]+","+t[1]+","+t[2]+")"},toRgba:function(){var t=this.getSource();return"rgba("+t[0]+","+t[1]+","+t[2]+","+t[3]+")"},toHsl:function(){var t=this.getSource(),e=this._rgbToHsl(t[0],t[1],t[2]);return"hsl("+e[0]+","+e[1]+"%,"+e[2]+"%)"},toHsla:function(){var t=this.getSource(),e=this._rgbToHsl(t[0],t[1],t[2]);return"hsla("+e[0]+","+e[1]+"%,"+e[2]+"%,"+t[3]+")"},toHex:function(){var t,e,i,n=this.getSource();return t=1===(t=n[0].toString(16)).length?"0"+t:t,e=1===(e=n[1].toString(16)).length?"0"+e:e,i=1===(i=n[2].toString(16)).length?"0"+i:i,t.toUpperCase()+e.toUpperCase()+i.toUpperCase()},toHexa:function(){var t,e=this.getSource();return t=1===(t=(t=Math.round(255*e[3])).toString(16)).length?"0"+t:t,this.toHex()+t.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(t){var e=this.getSource();return e[3]=t,this.setSource(e),this},toGrayscale:function(){var t=this.getSource(),e=parseInt((.3*t[0]+.59*t[1]+.11*t[2]).toFixed(0),10),i=t[3];return this.setSource([e,e,e,i]),this},toBlackWhite:function(t){var e=this.getSource(),i=(.3*e[0]+.59*e[1]+.11*e[2]).toFixed(0),n=e[3];return t=t||127,i=Number(i)<Number(t)?0:255,this.setSource([i,i,i,n]),this},overlayWith:function(t){t instanceof i||(t=new i(t));var e,n=[],r=this.getAlpha(),o=this.getSource(),s=t.getSource();for(e=0;e<3;e++)n.push(Math.round(.5*o[e]+.5*s[e]));return n[3]=r,this.setSource(n),this}},e.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*((?:\d*\.?\d+)?)\s*)?\)$/i,e.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/i,e.Color.reHex=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,e.Color.colorNameMap={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"},e.Color.fromRgb=function(t){return i.fromSource(i.sourceFromRgb(t))},e.Color.sourceFromRgb=function(t){var e=t.match(i.reRGBa);if(e){var n=parseInt(e[1],10)/(/%$/.test(e[1])?100:1)*(/%$/.test(e[1])?255:1),r=parseInt(e[2],10)/(/%$/.test(e[2])?100:1)*(/%$/.test(e[2])?255:1),o=parseInt(e[3],10)/(/%$/.test(e[3])?100:1)*(/%$/.test(e[3])?255:1);return[parseInt(n,10),parseInt(r,10),parseInt(o,10),e[4]?parseFloat(e[4]):1]}},e.Color.fromRgba=i.fromRgb,e.Color.fromHsl=function(t){return i.fromSource(i.sourceFromHsl(t))},e.Color.sourceFromHsl=function(t){var e=t.match(i.reHSLa);if(e){var r,o,s,a=(parseFloat(e[1])%360+360)%360/360,c=parseFloat(e[2])/(/%$/.test(e[2])?100:1),l=parseFloat(e[3])/(/%$/.test(e[3])?100:1);if(0===c)r=o=s=l;else{var h=l<=.5?l*(c+1):l+c-l*c,u=2*l-h;r=n(u,h,a+1/3),o=n(u,h,a),s=n(u,h,a-1/3)}return[Math.round(255*r),Math.round(255*o),Math.round(255*s),e[4]?parseFloat(e[4]):1]}},e.Color.fromHsla=i.fromHsl,e.Color.fromHex=function(t){return i.fromSource(i.sourceFromHex(t))},e.Color.sourceFromHex=function(t){if(t.match(i.reHex)){var e=t.slice(t.indexOf("#")+1),n=3===e.length||4===e.length,r=8===e.length||4===e.length,o=n?e.charAt(0)+e.charAt(0):e.substring(0,2),s=n?e.charAt(1)+e.charAt(1):e.substring(2,4),a=n?e.charAt(2)+e.charAt(2):e.substring(4,6),c=r?n?e.charAt(3)+e.charAt(3):e.substring(6,8):"FF";return[parseInt(o,16),parseInt(s,16),parseInt(a,16),parseFloat((parseInt(c,16)/255).toFixed(2))]}},e.Color.fromSource=function(t){var e=new i;return e.setSource(t),e})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=["e","se","s","sw","w","nw","n","ne","e"],n=["ns","nesw","ew","nwse"],r={},o="left",s="top",a="right",c="bottom",l="center",h={top:c,bottom:s,left:a,right:o,center:l},u=e.util.radiansToDegrees,f=Math.sign||function(t){return(t>0)-(t<0)||+t};function d(t,e){var i=t.angle+u(Math.atan2(e.y,e.x))+360;return Math.round(i%360/45)}function g(t,i){var n=i.transform.target,r=n.canvas,o=e.util.object.clone(i);o.target=n,r&&r.fire("object:"+t,o),n.fire(t,i)}function p(t,e){var i=e.canvas,n=t[i.uniScaleKey];return i.uniformScaling&&!n||!i.uniformScaling&&n}function v(t){return t.originX===l&&t.originY===l}function m(t,e,i){var n=t.lockScalingX,r=t.lockScalingY;return!(!n||!r)||(!(e||!n&&!r||!i)||(!(!n||"x"!==e)||!(!r||"y"!==e)))}function y(t,e,i,n){return{e:t,transform:e,pointer:{x:i,y:n}}}function b(t){return function(e,i,n,r){var o=i.target,s=o.getCenterPoint(),a=o.translateToOriginPoint(s,i.originX,i.originY),c=t(e,i,n,r);return o.setPositionByOrigin(a,i.originX,i.originY),c}}function x(t,e){return function(i,n,r,o){var s=e(i,n,r,o);return s&&g(t,y(i,n,r,o)),s}}function _(t,i,n,r,o){var s=t.target,a=s.controls[t.corner],c=s.canvas.getZoom(),l=s.padding/c,h=s.toLocalPoint(new e.Point(r,o),i,n);return h.x>=l&&(h.x-=l),h.x<=-l&&(h.x+=l),h.y>=l&&(h.y-=l),h.y<=l&&(h.y+=l),h.x-=a.offsetX,h.y-=a.offsetY,h}function C(t){return t.flipX!==t.flipY}function S(t,e,i,n,r){if(0!==t[e]){var o=r/t._getTransformedDimensions()[n]*t[i];t.set(i,o)}}function w(t,e,i,n){var r,l=e.target,h=l._getTransformedDimensions(0,l.skewY),f=_(e,e.originX,e.originY,i,n),d=Math.abs(2*f.x)-h.x,g=l.skewX;d<2?r=0:(r=u(Math.atan2(d/l.scaleX,h.y/l.scaleY)),e.originX===o&&e.originY===c&&(r=-r),e.originX===a&&e.originY===s&&(r=-r),C(l)&&(r=-r));var p=g!==r;if(p){var v=l._getTransformedDimensions().y;l.set("skewX",r),S(l,"skewY","scaleY","y",v)}return p}function T(t,e,i,n){var r,l=e.target,h=l._getTransformedDimensions(l.skewX,0),f=_(e,e.originX,e.originY,i,n),d=Math.abs(2*f.y)-h.y,g=l.skewY;d<2?r=0:(r=u(Math.atan2(d/l.scaleY,h.x/l.scaleX)),e.originX===o&&e.originY===c&&(r=-r),e.originX===a&&e.originY===s&&(r=-r),C(l)&&(r=-r));var p=g!==r;if(p){var v=l._getTransformedDimensions().x;l.set("skewY",r),S(l,"skewX","scaleX","x",v)}return p}function O(t,e,i,n,r){r=r||{};var o,s,a,c,l,u,d=e.target,g=d.lockScalingX,y=d.lockScalingY,b=r.by,x=p(t,d),C=m(d,b,x),S=e.gestureScale;if(C)return!1;if(S)s=e.scaleX*S,a=e.scaleY*S;else{if(o=_(e,e.originX,e.originY,i,n),l="y"!==b?f(o.x):1,u="x"!==b?f(o.y):1,e.signX||(e.signX=l),e.signY||(e.signY=u),d.lockScalingFlip&&(e.signX!==l||e.signY!==u))return!1;if(c=d._getTransformedDimensions(),x&&!b){var w=Math.abs(o.x)+Math.abs(o.y),T=e.original,O=w/(Math.abs(c.x*T.scaleX/d.scaleX)+Math.abs(c.y*T.scaleY/d.scaleY));s=T.scaleX*O,a=T.scaleY*O}else s=Math.abs(o.x*d.scaleX/c.x),a=Math.abs(o.y*d.scaleY/c.y);v(e)&&(s*=2,a*=2),e.signX!==l&&"y"!==b&&(e.originX=h[e.originX],s*=-1,e.signX=l),e.signY!==u&&"x"!==b&&(e.originY=h[e.originY],a*=-1,e.signY=u)}var E=d.scaleX,k=d.scaleY;return b?("x"===b&&d.set("scaleX",s),"y"===b&&d.set("scaleY",a)):(!g&&d.set("scaleX",s),!y&&d.set("scaleY",a)),E!==d.scaleX||k!==d.scaleY}r.scaleCursorStyleHandler=function(t,e,n){var r=p(t,n),o="";if(0!==e.x&&0===e.y?o="x":0===e.x&&0!==e.y&&(o="y"),m(n,o,r))return"not-allowed";var s=d(n,e);return i[s]+"-resize"},r.skewCursorStyleHandler=function(t,e,i){var r="not-allowed";if(0!==e.x&&i.lockSkewingY)return r;if(0!==e.y&&i.lockSkewingX)return r;var o=d(i,e)%4;return n[o]+"-resize"},r.scaleSkewCursorStyleHandler=function(t,e,i){return t[i.canvas.altActionKey]?r.skewCursorStyleHandler(t,e,i):r.scaleCursorStyleHandler(t,e,i)},r.rotationWithSnapping=x("rotating",b((function(t,e,i,n){var r=e,o=r.target,s=o.translateToOriginPoint(o.getCenterPoint(),r.originX,r.originY);if(o.lockRotation)return!1;var a,c=Math.atan2(r.ey-s.y,r.ex-s.x),l=Math.atan2(n-s.y,i-s.x),h=u(l-c+r.theta);if(o.snapAngle>0){var f=o.snapAngle,d=o.snapThreshold||f,g=Math.ceil(h/f)*f,p=Math.floor(h/f)*f;Math.abs(h-p)<d?h=p:Math.abs(h-g)<d&&(h=g)}return h<0&&(h=360+h),h%=360,a=o.angle!==h,o.angle=h,a}))),r.scalingEqually=x("scaling",b((function(t,e,i,n){return O(t,e,i,n)}))),r.scalingX=x("scaling",b((function(t,e,i,n){return O(t,e,i,n,{by:"x"})}))),r.scalingY=x("scaling",b((function(t,e,i,n){return O(t,e,i,n,{by:"y"})}))),r.scalingYOrSkewingX=function(t,e,i,n){return t[e.target.canvas.altActionKey]?r.skewHandlerX(t,e,i,n):r.scalingY(t,e,i,n)},r.scalingXOrSkewingY=function(t,e,i,n){return t[e.target.canvas.altActionKey]?r.skewHandlerY(t,e,i,n):r.scalingX(t,e,i,n)},r.changeWidth=x("resizing",b((function(t,e,i,n){var r=e.target,o=_(e,e.originX,e.originY,i,n),s=r.strokeWidth/(r.strokeUniform?r.scaleX:1),a=v(e)?2:1,c=r.width,l=Math.abs(o.x*a/r.scaleX)-s;return r.set("width",Math.max(l,0)),c!==l}))),r.skewHandlerX=function(t,e,i,n){var r,c=e.target,h=c.skewX,u=e.originY;return!c.lockSkewingX&&(0===h?r=_(e,l,l,i,n).x>0?o:a:(h>0&&(r=u===s?o:a),h<0&&(r=u===s?a:o),C(c)&&(r=r===o?a:o)),e.originX=r,x("skewing",b(w))(t,e,i,n))},r.skewHandlerY=function(t,e,i,n){var r,a=e.target,h=a.skewY,u=e.originX;return!a.lockSkewingY&&(0===h?r=_(e,l,l,i,n).y>0?s:c:(h>0&&(r=u===o?s:c),h<0&&(r=u===o?c:s),C(a)&&(r=r===s?c:s)),e.originY=r,x("skewing",b(T))(t,e,i,n))},r.dragHandler=function(t,e,i,n){var r=e.target,o=i-e.offsetX,s=n-e.offsetY,a=!r.get("lockMovementX")&&r.left!==o,c=!r.get("lockMovementY")&&r.top!==s;return a&&r.set("left",o),c&&r.set("top",s),(a||c)&&g("moving",y(t,e,i,n)),a||c},r.scaleOrSkewActionName=function(t,e,i){var n=t[i.canvas.altActionKey];return 0===e.x?n?"skewX":"scaleY":0===e.y?n?"skewY":"scaleX":void 0},r.rotationStyleHandler=function(t,e,i){return i.lockRotation?"not-allowed":e.cursorStyle},r.fireEvent=g,r.wrapWithFixedAnchor=b,r.wrapWithFireEvent=x,r.getLocalPoint=_,e.controlsUtils=r}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.degreesToRadians,n=e.controlsUtils;n.renderCircleControl=function(t,e,i,n,r){n=n||{};var o,s=this.sizeX||n.cornerSize||r.cornerSize,a=this.sizeY||n.cornerSize||r.cornerSize,c="undefined"!==typeof n.transparentCorners?n.transparentCorners:r.transparentCorners,l=c?"stroke":"fill",h=!c&&(n.cornerStrokeColor||r.cornerStrokeColor),u=e,f=i;t.save(),t.fillStyle=n.cornerColor||r.cornerColor,t.strokeStyle=n.cornerStrokeColor||r.cornerStrokeColor,s>a?(o=s,t.scale(1,a/s),f=i*s/a):a>s?(o=a,t.scale(s/a,1),u=e*a/s):o=s,t.lineWidth=1,t.beginPath(),t.arc(u,f,o/2,0,2*Math.PI,!1),t[l](),h&&t.stroke(),t.restore()},n.renderSquareControl=function(t,e,n,r,o){r=r||{};var s=this.sizeX||r.cornerSize||o.cornerSize,a=this.sizeY||r.cornerSize||o.cornerSize,c="undefined"!==typeof r.transparentCorners?r.transparentCorners:o.transparentCorners,l=c?"stroke":"fill",h=!c&&(r.cornerStrokeColor||o.cornerStrokeColor),u=s/2,f=a/2;t.save(),t.fillStyle=r.cornerColor||o.cornerColor,t.strokeStyle=r.cornerStrokeColor||o.cornerStrokeColor,t.lineWidth=1,t.translate(e,n),t.rotate(i(o.angle)),t[l+"Rect"](-u,-f,s,a),h&&t.strokeRect(-u,-f,s,a),t.restore()}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Control=function(t){for(var e in t)this[e]=t[e]},e.Control.prototype={visible:!0,actionName:"scale",angle:0,x:0,y:0,offsetX:0,offsetY:0,sizeX:null,sizeY:null,touchSizeX:null,touchSizeY:null,cursorStyle:"crosshair",withConnection:!1,actionHandler:function(){},mouseDownHandler:function(){},mouseUpHandler:function(){},getActionHandler:function(){return this.actionHandler},getMouseDownHandler:function(){return this.mouseDownHandler},getMouseUpHandler:function(){return this.mouseUpHandler},cursorStyleHandler:function(t,e){return e.cursorStyle},getActionName:function(t,e){return e.actionName},getVisibility:function(t,e){var i=t._controlsVisibility;return i&&"undefined"!==typeof i[e]?i[e]:this.visible},setVisibility:function(t){this.visible=t},positionHandler:function(t,i){return e.util.transformPoint({x:this.x*t.x+this.offsetX,y:this.y*t.y+this.offsetY},i)},calcCornerCoords:function(t,i,n,r,o){var s,a,c,l,h=o?this.touchSizeX:this.sizeX,u=o?this.touchSizeY:this.sizeY;if(h&&u&&h!==u){var f=Math.atan2(u,h),d=Math.sqrt(h*h+u*u)/2,g=f-e.util.degreesToRadians(t),p=Math.PI/2-f-e.util.degreesToRadians(t);s=d*e.util.cos(g),a=d*e.util.sin(g),c=d*e.util.cos(p),l=d*e.util.sin(p)}else{d=.7071067812*(h&&u?h:i);g=e.util.degreesToRadians(45-t);s=c=d*e.util.cos(g),a=l=d*e.util.sin(g)}return{tl:{x:n-l,y:r-c},tr:{x:n+s,y:r-a},bl:{x:n-s,y:r+a},br:{x:n+l,y:r+c}}},render:function(t,i,n,r,o){if("circle"===((r=r||{}).cornerStyle||o.cornerStyle))e.controlsUtils.renderCircleControl.call(this,t,i,n,r,o);else e.controlsUtils.renderSquareControl.call(this,t,i,n,r,o)}}}(e),function(){function t(t,e){var i,n,o,s,a=t.getAttribute("style"),c=t.getAttribute("offset")||0;if(c=(c=parseFloat(c)/(/%$/.test(c)?100:1))<0?0:c>1?1:c,a){var l=a.split(/\s*;\s*/);for(""===l[l.length-1]&&l.pop(),s=l.length;s--;){var h=l[s].split(/\s*:\s*/),u=h[0].trim(),f=h[1].trim();"stop-color"===u?i=f:"stop-opacity"===u&&(o=f)}}return i||(i=t.getAttribute("stop-color")||"rgb(0,0,0)"),o||(o=t.getAttribute("stop-opacity")),n=(i=new r.Color(i)).getAlpha(),o=isNaN(parseFloat(o))?1:parseFloat(o),o*=n*e,{offset:c,color:i.toRgb(),opacity:o}}var e=r.util.object.clone;r.Gradient=r.util.createClass({offsetX:0,offsetY:0,gradientTransform:null,gradientUnits:"pixels",type:"linear",initialize:function(t){t||(t={}),t.coords||(t.coords={});var e,i=this;Object.keys(t).forEach((function(e){i[e]=t[e]})),this.id?this.id+="_"+r.Object.__uid++:this.id=r.Object.__uid++,e={x1:t.coords.x1||0,y1:t.coords.y1||0,x2:t.coords.x2||0,y2:t.coords.y2||0},"radial"===this.type&&(e.r1=t.coords.r1||0,e.r2=t.coords.r2||0),this.coords=e,this.colorStops=t.colorStops.slice()},addColorStop:function(t){for(var e in t){var i=new r.Color(t[e]);this.colorStops.push({offset:parseFloat(e),color:i.toRgb(),opacity:i.getAlpha()})}return this},toObject:function(t){var e={type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?this.gradientTransform.concat():this.gradientTransform};return r.util.populateWithProperties(this,e,t),e},toSVG:function(t,i){var n,o,s,a,c=e(this.coords,!0),l=(i=i||{},e(this.colorStops,!0)),h=c.r1>c.r2,u=this.gradientTransform?this.gradientTransform.concat():r.iMatrix.concat(),f=-this.offsetX,d=-this.offsetY,g=!!i.additionalTransform,p="pixels"===this.gradientUnits?"userSpaceOnUse":"objectBoundingBox";if(l.sort((function(t,e){return t.offset-e.offset})),"objectBoundingBox"===p?(f/=t.width,d/=t.height):(f+=t.width/2,d+=t.height/2),"path"===t.type&&"percentage"!==this.gradientUnits&&(f-=t.pathOffset.x,d-=t.pathOffset.y),u[4]-=f,u[5]-=d,a='id="SVGID_'+this.id+'" gradientUnits="'+p+'"',a+=' gradientTransform="'+(g?i.additionalTransform+" ":"")+r.util.matrixToSVG(u)+'" ',"linear"===this.type?s=["<linearGradient ",a,' x1="',c.x1,'" y1="',c.y1,'" x2="',c.x2,'" y2="',c.y2,'">\n']:"radial"===this.type&&(s=["<radialGradient ",a,' cx="',h?c.x1:c.x2,'" cy="',h?c.y1:c.y2,'" r="',h?c.r1:c.r2,'" fx="',h?c.x2:c.x1,'" fy="',h?c.y2:c.y1,'">\n']),"radial"===this.type){if(h)for((l=l.concat()).reverse(),n=0,o=l.length;n<o;n++)l[n].offset=1-l[n].offset;var v=Math.min(c.r1,c.r2);if(v>0){var m=v/Math.max(c.r1,c.r2);for(n=0,o=l.length;n<o;n++)l[n].offset+=m*(1-l[n].offset)}}for(n=0,o=l.length;n<o;n++){var y=l[n];s.push("<stop ",'offset="',100*y.offset+"%",'" style="stop-color:',y.color,"undefined"!==typeof y.opacity?";stop-opacity: "+y.opacity:";",'"/>\n')}return s.push("linear"===this.type?"</linearGradient>\n":"</radialGradient>\n"),s.join("")},toLive:function(t){var e,i,n,o=r.util.object.clone(this.coords);if(this.type){for("linear"===this.type?e=t.createLinearGradient(o.x1,o.y1,o.x2,o.y2):"radial"===this.type&&(e=t.createRadialGradient(o.x1,o.y1,o.r1,o.x2,o.y2,o.r2)),i=0,n=this.colorStops.length;i<n;i++){var s=this.colorStops[i].color,a=this.colorStops[i].opacity,c=this.colorStops[i].offset;"undefined"!==typeof a&&(s=new r.Color(s).setAlpha(a).toRgba()),e.addColorStop(c,s)}return e}}}),r.util.object.extend(r.Gradient,{fromElement:function(e,i,n,o){var s=parseFloat(n)/(/%$/.test(n)?100:1);s=s<0?0:s>1?1:s,isNaN(s)&&(s=1);var a,c,l,h,u=e.getElementsByTagName("stop"),f="userSpaceOnUse"===e.getAttribute("gradientUnits")?"pixels":"percentage",d=e.getAttribute("gradientTransform")||"",g=[],p=0,v=0;for("linearGradient"===e.nodeName||"LINEARGRADIENT"===e.nodeName?(a="linear",c=function(t){return{x1:t.getAttribute("x1")||0,y1:t.getAttribute("y1")||0,x2:t.getAttribute("x2")||"100%",y2:t.getAttribute("y2")||0}}(e)):(a="radial",c=function(t){return{x1:t.getAttribute("fx")||t.getAttribute("cx")||"50%",y1:t.getAttribute("fy")||t.getAttribute("cy")||"50%",r1:0,x2:t.getAttribute("cx")||"50%",y2:t.getAttribute("cy")||"50%",r2:t.getAttribute("r")||"50%"}}(e)),l=u.length;l--;)g.push(t(u[l],s));return h=r.parseTransformAttribute(d),function(t,e,i,n){var r,o;Object.keys(e).forEach((function(t){"Infinity"===(r=e[t])?o=1:"-Infinity"===r?o=0:(o=parseFloat(e[t],10),"string"===typeof r&&/^(\d+\.\d+)%|(\d+)%$/.test(r)&&(o*=.01,"pixels"===n&&("x1"!==t&&"x2"!==t&&"r2"!==t||(o*=i.viewBoxWidth||i.width),"y1"!==t&&"y2"!==t||(o*=i.viewBoxHeight||i.height)))),e[t]=o}))}(0,c,o,f),"pixels"===f&&(p=-i.left,v=-i.top),new r.Gradient({id:e.getAttribute("id"),type:a,coords:c,colorStops:g,gradientUnits:f,gradientTransform:h,offsetX:p,offsetY:v})}})}(),function(){"use strict";var t=r.util.toFixed;r.Pattern=r.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,crossOrigin:"",patternTransform:null,initialize:function(t,e){if(t||(t={}),this.id=r.Object.__uid++,this.setOptions(t),!t.source||t.source&&"string"!==typeof t.source)e&&e(this);else{var i=this;this.source=r.util.createImage(),r.util.loadImage(t.source,(function(t,n){i.source=t,e&&e(i,n)}),null,this.crossOrigin)}},toObject:function(e){var i,n,o=r.Object.NUM_FRACTION_DIGITS;return"string"===typeof this.source.src?i=this.source.src:"object"===typeof this.source&&this.source.toDataURL&&(i=this.source.toDataURL()),n={type:"pattern",source:i,repeat:this.repeat,crossOrigin:this.crossOrigin,offsetX:t(this.offsetX,o),offsetY:t(this.offsetY,o),patternTransform:this.patternTransform?this.patternTransform.concat():null},r.util.populateWithProperties(this,n,e),n},toSVG:function(t){var e="function"===typeof this.source?this.source():this.source,i=e.width/t.width,n=e.height/t.height,r=this.offsetX/t.width,o=this.offsetY/t.height,s="";return"repeat-x"!==this.repeat&&"no-repeat"!==this.repeat||(n=1,o&&(n+=Math.abs(o))),"repeat-y"!==this.repeat&&"no-repeat"!==this.repeat||(i=1,r&&(i+=Math.abs(r))),e.src?s=e.src:e.toDataURL&&(s=e.toDataURL()),'<pattern id="SVGID_'+this.id+'" x="'+r+'" y="'+o+'" width="'+i+'" height="'+n+'">\n<image x="0" y="0" width="'+e.width+'" height="'+e.height+'" xlink:href="'+s+'"></image>\n</pattern>\n'},setOptions:function(t){for(var e in t)this[e]=t[e]},toLive:function(t){var e=this.source;if(!e)return"";if("undefined"!==typeof e.src){if(!e.complete)return"";if(0===e.naturalWidth||0===e.naturalHeight)return""}return t.createPattern(e,this.repeat)}})}(),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.toFixed;e.Shadow?e.warn("fabric.Shadow is already defined."):(e.Shadow=e.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1,initialize:function(t){for(var i in"string"===typeof t&&(t=this._parseShadow(t)),t)this[i]=t[i];this.id=e.Object.__uid++},_parseShadow:function(t){var i=t.trim(),n=e.Shadow.reOffsetsAndBlur.exec(i)||[];return{color:(i.replace(e.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)").trim(),offsetX:parseFloat(n[1],10)||0,offsetY:parseFloat(n[2],10)||0,blur:parseFloat(n[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toSVG:function(t){var n=40,r=40,o=e.Object.NUM_FRACTION_DIGITS,s=e.util.rotateVector({x:this.offsetX,y:this.offsetY},e.util.degreesToRadians(-t.angle)),a=new e.Color(this.color);return t.width&&t.height&&(n=100*i((Math.abs(s.x)+this.blur)/t.width,o)+20,r=100*i((Math.abs(s.y)+this.blur)/t.height,o)+20),t.flipX&&(s.x*=-1),t.flipY&&(s.y*=-1),'<filter id="SVGID_'+this.id+'" y="-'+r+'%" height="'+(100+2*r)+'%" x="-'+n+'%" width="'+(100+2*n)+'%" >\n\t<feGaussianBlur in="SourceAlpha" stdDeviation="'+i(this.blur?this.blur/2:0,o)+'"></feGaussianBlur>\n\t<feOffset dx="'+i(s.x,o)+'" dy="'+i(s.y,o)+'" result="oBlur" ></feOffset>\n\t<feFlood flood-color="'+a.toRgb()+'" flood-opacity="'+a.getAlpha()+'"/>\n\t<feComposite in2="oBlur" operator="in" />\n\t<feMerge>\n\t\t<feMergeNode></feMergeNode>\n\t\t<feMergeNode in="SourceGraphic"></feMergeNode>\n\t</feMerge>\n</filter>\n'},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling};var t={},i=e.Shadow.prototype;return["color","blur","offsetX","offsetY","affectStroke","nonScaling"].forEach((function(e){this[e]!==i[e]&&(t[e]=this[e])}),this),t}}),e.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(\d+(?:\.\d*)?(?:px)?)?(?:\s?|$)(?:$|\s)/)}(e),function(){"use strict";if(r.StaticCanvas)r.warn("fabric.StaticCanvas is already defined.");else{var t=r.util.object.extend,e=r.util.getElementOffset,i=r.util.removeFromArray,n=r.util.toFixed,o=r.util.transformPoint,s=r.util.invertTransform,a=r.util.getNodeCanvas,c=r.util.createCanvasElement,l=new Error("Could not initialize `canvas` element");r.StaticCanvas=r.util.createClass(r.CommonMethods,{initialize:function(t,e){e||(e={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(t,e)},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!1,renderOnAddRemove:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:r.iMatrix.concat(),backgroundVpt:!0,overlayVpt:!0,enableRetinaScaling:!0,vptCoords:{},skipOffscreen:!0,clipPath:void 0,_initStatic:function(t,e){var i=this.requestRenderAllBound;this._objects=[],this._createLowerCanvas(t),this._initOptions(e),this.interactive||this._initRetinaScaling(),e.overlayImage&&this.setOverlayImage(e.overlayImage,i),e.backgroundImage&&this.setBackgroundImage(e.backgroundImage,i),e.backgroundColor&&this.setBackgroundColor(e.backgroundColor,i),e.overlayColor&&this.setOverlayColor(e.overlayColor,i),this.calcOffset()},_isRetinaScaling:function(){return r.devicePixelRatio>1&&this.enableRetinaScaling},getRetinaScaling:function(){return this._isRetinaScaling()?Math.max(1,r.devicePixelRatio):1},_initRetinaScaling:function(){if(this._isRetinaScaling()){var t=r.devicePixelRatio;this.__initRetinaScaling(t,this.lowerCanvasEl,this.contextContainer),this.upperCanvasEl&&this.__initRetinaScaling(t,this.upperCanvasEl,this.contextTop)}},__initRetinaScaling:function(t,e,i){e.setAttribute("width",this.width*t),e.setAttribute("height",this.height*t),i.scale(t,t)},calcOffset:function(){return this._offset=e(this.lowerCanvasEl),this},setOverlayImage:function(t,e,i){return this.__setBgOverlayImage("overlayImage",t,e,i)},setBackgroundImage:function(t,e,i){return this.__setBgOverlayImage("backgroundImage",t,e,i)},setOverlayColor:function(t,e){return this.__setBgOverlayColor("overlayColor",t,e)},setBackgroundColor:function(t,e){return this.__setBgOverlayColor("backgroundColor",t,e)},__setBgOverlayImage:function(t,e,i,n){return"string"===typeof e?r.util.loadImage(e,(function(e,o){if(e){var s=new r.Image(e,n);this[t]=s,s.canvas=this}i&&i(e,o)}),this,n&&n.crossOrigin):(n&&e.setOptions(n),this[t]=e,e&&(e.canvas=this),i&&i(e,!1)),this},__setBgOverlayColor:function(t,e,i){return this[t]=e,this._initGradient(e,t),this._initPattern(e,t,i),this},_createCanvasElement:function(){var t=c();if(!t)throw l;if(t.style||(t.style={}),"undefined"===typeof t.getContext)throw l;return t},_initOptions:function(t){var e=this.lowerCanvasEl;this._setOptions(t),this.width=this.width||parseInt(e.width,10)||0,this.height=this.height||parseInt(e.height,10)||0,this.lowerCanvasEl.style&&(e.width=this.width,e.height=this.height,e.style.width=this.width+"px",e.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(t){t&&t.getContext?this.lowerCanvasEl=t:this.lowerCanvasEl=r.util.getById(t)||this._createCanvasElement(),r.util.addClass(this.lowerCanvasEl,"lower-canvas"),this._originalCanvasStyle=this.lowerCanvasEl.style,this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(t,e){return this.setDimensions({width:t},e)},setHeight:function(t,e){return this.setDimensions({height:t},e)},setDimensions:function(t,e){var i;for(var n in e=e||{},t)i=t[n],e.cssOnly||(this._setBackstoreDimension(n,t[n]),i+="px",this.hasLostContext=!0),e.backstoreOnly||this._setCssDimension(n,i);return this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop),this._initRetinaScaling(),this.calcOffset(),e.cssOnly||this.requestRenderAll(),this},_setBackstoreDimension:function(t,e){return this.lowerCanvasEl[t]=e,this.upperCanvasEl&&(this.upperCanvasEl[t]=e),this.cacheCanvasEl&&(this.cacheCanvasEl[t]=e),this[t]=e,this},_setCssDimension:function(t,e){return this.lowerCanvasEl.style[t]=e,this.upperCanvasEl&&(this.upperCanvasEl.style[t]=e),this.wrapperEl&&(this.wrapperEl.style[t]=e),this},getZoom:function(){return this.viewportTransform[0]},setViewportTransform:function(t){var e,i,n,r=this._activeObject,o=this.backgroundImage,s=this.overlayImage;for(this.viewportTransform=t,i=0,n=this._objects.length;i<n;i++)(e=this._objects[i]).group||e.setCoords(!0);return r&&r.setCoords(),o&&o.setCoords(!0),s&&s.setCoords(!0),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll(),this},zoomToPoint:function(t,e){var i=t,n=this.viewportTransform.slice(0);t=o(t,s(this.viewportTransform)),n[0]=e,n[3]=e;var r=o(t,n);return n[4]+=i.x-r.x,n[5]+=i.y-r.y,this.setViewportTransform(n)},setZoom:function(t){return this.zoomToPoint(new r.Point(0,0),t),this},absolutePan:function(t){var e=this.viewportTransform.slice(0);return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)},relativePan:function(t){return this.absolutePan(new r.Point(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},_onObjectAdded:function(t){this.stateful&&t.setupState(),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added")},_onObjectRemoved:function(t){this.fire("object:removed",{target:t}),t.fire("removed"),delete t.canvas},clearContext:function(t){return t.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this.remove.apply(this,this.getObjects()),this.backgroundImage=null,this.overlayImage=null,this.backgroundColor="",this.overlayColor="",this._hasITextHandlers&&(this.off("mouse:up",this._mouseUpITextHandler),this._iTextInstances=null,this._hasITextHandlers=!1),this.clearContext(this.contextContainer),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll(),this},renderAll:function(){var t=this.contextContainer;return this.renderCanvas(t,this._objects),this},renderAndReset:function(){this.isRendering=0,this.renderAll()},requestRenderAll:function(){return this.isRendering||(this.isRendering=r.util.requestAnimFrame(this.renderAndResetBound)),this},calcViewportBoundaries:function(){var t={},e=this.width,i=this.height,n=s(this.viewportTransform);return t.tl=o({x:0,y:0},n),t.br=o({x:e,y:i},n),t.tr=new r.Point(t.br.x,t.tl.y),t.bl=new r.Point(t.tl.x,t.br.y),this.vptCoords=t,t},cancelRequestedRender:function(){this.isRendering&&(r.util.cancelAnimFrame(this.isRendering),this.isRendering=0)},renderCanvas:function(t,e){var i=this.viewportTransform,n=this.clipPath;this.cancelRequestedRender(),this.calcViewportBoundaries(),this.clearContext(t),r.util.setImageSmoothing(t,this.imageSmoothingEnabled),this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this._renderObjects(t,e),t.restore(),!this.controlsAboveOverlay&&this.interactive&&this.drawControls(t),n&&(n.canvas=this,n.shouldCache(),n._transformDone=!0,n.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t)),this._renderOverlay(t),this.controlsAboveOverlay&&this.interactive&&this.drawControls(t),this.fire("after:render",{ctx:t})},drawClipPathOnCanvas:function(t){var e=this.viewportTransform,i=this.clipPath;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t.globalCompositeOperation="destination-in",i.transform(t),t.scale(1/i.zoomX,1/i.zoomY),t.drawImage(i._cacheCanvas,-i.cacheTranslationX,-i.cacheTranslationY),t.restore()},_renderObjects:function(t,e){var i,n;for(i=0,n=e.length;i<n;++i)e[i]&&e[i].render(t)},_renderBackgroundOrOverlay:function(t,e){var i=this[e+"Color"],n=this[e+"Image"],r=this.viewportTransform,o=this[e+"Vpt"];if(i||n){if(i){t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=i.toLive?i.toLive(t,this):i,o&&t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),t.transform(1,0,0,1,i.offsetX||0,i.offsetY||0);var s=i.gradientTransform||i.patternTransform;s&&t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),t.fill(),t.restore()}if(n){t.save();var a=this.skipOffscreen;this.skipOffscreen=o,o&&t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),n.render(t),this.skipOffscreen=a,t.restore()}}},_renderBackground:function(t){this._renderBackgroundOrOverlay(t,"background")},_renderOverlay:function(t){this._renderBackgroundOrOverlay(t,"overlay")},getCenter:function(){return{top:this.height/2,left:this.width/2}},getCenterPoint:function(){return new r.Point(this.width/2,this.height/2)},centerObjectH:function(t){return this._centerObject(t,new r.Point(this.getCenterPoint().x,t.getCenterPoint().y))},centerObjectV:function(t){return this._centerObject(t,new r.Point(t.getCenterPoint().x,this.getCenterPoint().y))},centerObject:function(t){var e=this.getCenterPoint();return this._centerObject(t,e)},viewportCenterObject:function(t){var e=this.getVpCenter();return this._centerObject(t,e)},viewportCenterObjectH:function(t){var e=this.getVpCenter();return this._centerObject(t,new r.Point(e.x,t.getCenterPoint().y)),this},viewportCenterObjectV:function(t){var e=this.getVpCenter();return this._centerObject(t,new r.Point(t.getCenterPoint().x,e.y))},getVpCenter:function(){var t=this.getCenterPoint(),e=s(this.viewportTransform);return o(t,e)},_centerObject:function(t,e){return t.setPositionByOrigin(e,"center","center"),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll(),this},toDatalessJSON:function(t){return this.toDatalessObject(t)},toObject:function(t){return this._toObjectMethod("toObject",t)},toDatalessObject:function(t){return this._toObjectMethod("toDatalessObject",t)},_toObjectMethod:function(e,i){var n=this.clipPath,o={version:r.version,objects:this._toObjects(e,i)};return n&&!n.excludeFromExport&&(o.clipPath=this._toObject(this.clipPath,e,i)),t(o,this.__serializeBgOverlay(e,i)),r.util.populateWithProperties(this,o,i),o},_toObjects:function(t,e){return this._objects.filter((function(t){return!t.excludeFromExport})).map((function(i){return this._toObject(i,t,e)}),this)},_toObject:function(t,e,i){var n;this.includeDefaultValues||(n=t.includeDefaultValues,t.includeDefaultValues=!1);var r=t[e](i);return this.includeDefaultValues||(t.includeDefaultValues=n),r},__serializeBgOverlay:function(t,e){var i={},n=this.backgroundImage,r=this.overlayImage,o=this.backgroundColor,s=this.overlayColor;return o&&o.toObject?o.excludeFromExport||(i.background=o.toObject(e)):o&&(i.background=o),s&&s.toObject?s.excludeFromExport||(i.overlay=s.toObject(e)):s&&(i.overlay=s),n&&!n.excludeFromExport&&(i.backgroundImage=this._toObject(n,t,e)),r&&!r.excludeFromExport&&(i.overlayImage=this._toObject(r,t,e)),i},svgViewportTransformation:!0,toSVG:function(t,e){t||(t={}),t.reviver=e;var i=[];return this._setSVGPreamble(i,t),this._setSVGHeader(i,t),this.clipPath&&i.push('<g clip-path="url(#'+this.clipPath.clipPathId+')" >\n'),this._setSVGBgOverlayColor(i,"background"),this._setSVGBgOverlayImage(i,"backgroundImage",e),this._setSVGObjects(i,e),this.clipPath&&i.push("</g>\n"),this._setSVGBgOverlayColor(i,"overlay"),this._setSVGBgOverlayImage(i,"overlayImage",e),i.push("</svg>"),i.join("")},_setSVGPreamble:function(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",'" standalone="no" ?>\n','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ','"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n')},_setSVGHeader:function(t,e){var i,o=e.width||this.width,s=e.height||this.height,a='viewBox="0 0 '+this.width+" "+this.height+'" ',c=r.Object.NUM_FRACTION_DIGITS;e.viewBox?a='viewBox="'+e.viewBox.x+" "+e.viewBox.y+" "+e.viewBox.width+" "+e.viewBox.height+'" ':this.svgViewportTransformation&&(i=this.viewportTransform,a='viewBox="'+n(-i[4]/i[0],c)+" "+n(-i[5]/i[3],c)+" "+n(this.width/i[0],c)+" "+n(this.height/i[3],c)+'" '),t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',o,'" ','height="',s,'" ',a,'xml:space="preserve">\n',"<desc>Created with Fabric.js ",r.version,"</desc>\n","<defs>\n",this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),"</defs>\n")},createSVGClipPathMarkup:function(t){var e=this.clipPath;return e?(e.clipPathId="CLIPPATH_"+r.Object.__uid++,'<clipPath id="'+e.clipPathId+'" >\n'+this.clipPath.toClipPathSVG(t.reviver)+"</clipPath>\n"):""},createSVGRefElementsMarkup:function(){var t=this;return["background","overlay"].map((function(e){var i=t[e+"Color"];if(i&&i.toLive){var n=t[e+"Vpt"],o=t.viewportTransform,s={width:t.width/(n?o[0]:1),height:t.height/(n?o[3]:1)};return i.toSVG(s,{additionalTransform:n?r.util.matrixToSVG(o):""})}})).join("")},createSVGFontFacesMarkup:function(){var t,e,i,n,o,s,a,c,l="",h={},u=r.fontPaths,f=[];for(this._objects.forEach((function t(e){f.push(e),e._objects&&e._objects.forEach(t)})),a=0,c=f.length;a<c;a++)if(e=(t=f[a]).fontFamily,-1!==t.type.indexOf("text")&&!h[e]&&u[e]&&(h[e]=!0,t.styles))for(o in i=t.styles)for(s in n=i[o])!h[e=n[s].fontFamily]&&u[e]&&(h[e]=!0);for(var d in h)l+=["\t\t@font-face {\n","\t\t\tfont-family: '",d,"';\n","\t\t\tsrc: url('",u[d],"');\n","\t\t}\n"].join("");return l&&(l=['\t<style type="text/css">',"<![CDATA[\n",l,"]]>","</style>\n"].join("")),l},_setSVGObjects:function(t,e){var i,n,r,o=this._objects;for(n=0,r=o.length;n<r;n++)(i=o[n]).excludeFromExport||this._setSVGObject(t,i,e)},_setSVGObject:function(t,e,i){t.push(e.toSVG(i))},_setSVGBgOverlayImage:function(t,e,i){this[e]&&!this[e].excludeFromExport&&this[e].toSVG&&t.push(this[e].toSVG(i))},_setSVGBgOverlayColor:function(t,e){var i=this[e+"Color"],n=this.viewportTransform,o=this.width,s=this.height;if(i)if(i.toLive){var a=i.repeat,c=r.util.invertTransform(n),l=this[e+"Vpt"]?r.util.matrixToSVG(c):"";t.push('<rect transform="'+l+" translate(",o/2,",",s/2,')"',' x="',i.offsetX-o/2,'" y="',i.offsetY-s/2,'" ','width="',"repeat-y"===a||"no-repeat"===a?i.source.width:o,'" height="',"repeat-x"===a||"no-repeat"===a?i.source.height:s,'" fill="url(#SVGID_'+i.id+')"',"></rect>\n")}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',i,'"',"></rect>\n")},sendToBack:function(t){if(!t)return this;var e,n,r,o=this._activeObject;if(t===o&&"activeSelection"===t.type)for(e=(r=o._objects).length;e--;)n=r[e],i(this._objects,n),this._objects.unshift(n);else i(this._objects,t),this._objects.unshift(t);return this.renderOnAddRemove&&this.requestRenderAll(),this},bringToFront:function(t){if(!t)return this;var e,n,r,o=this._activeObject;if(t===o&&"activeSelection"===t.type)for(r=o._objects,e=0;e<r.length;e++)n=r[e],i(this._objects,n),this._objects.push(n);else i(this._objects,t),this._objects.push(t);return this.renderOnAddRemove&&this.requestRenderAll(),this},sendBackwards:function(t,e){if(!t)return this;var n,r,o,s,a,c=this._activeObject,l=0;if(t===c&&"activeSelection"===t.type)for(a=c._objects,n=0;n<a.length;n++)r=a[n],(o=this._objects.indexOf(r))>0+l&&(s=o-1,i(this._objects,r),this._objects.splice(s,0,r)),l++;else 0!==(o=this._objects.indexOf(t))&&(s=this._findNewLowerIndex(t,o,e),i(this._objects,t),this._objects.splice(s,0,t));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewLowerIndex:function(t,e,i){var n,r;if(i)for(n=e,r=e-1;r>=0;--r){if(t.intersectsWithObject(this._objects[r])||t.isContainedWithinObject(this._objects[r])||this._objects[r].isContainedWithinObject(t)){n=r;break}}else n=e-1;return n},bringForward:function(t,e){if(!t)return this;var n,r,o,s,a,c=this._activeObject,l=0;if(t===c&&"activeSelection"===t.type)for(n=(a=c._objects).length;n--;)r=a[n],(o=this._objects.indexOf(r))<this._objects.length-1-l&&(s=o+1,i(this._objects,r),this._objects.splice(s,0,r)),l++;else(o=this._objects.indexOf(t))!==this._objects.length-1&&(s=this._findNewUpperIndex(t,o,e),i(this._objects,t),this._objects.splice(s,0,t));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewUpperIndex:function(t,e,i){var n,r,o;if(i)for(n=e,r=e+1,o=this._objects.length;r<o;++r){if(t.intersectsWithObject(this._objects[r])||t.isContainedWithinObject(this._objects[r])||this._objects[r].isContainedWithinObject(t)){n=r;break}}else n=e+1;return n},moveTo:function(t,e){return i(this._objects,t),this._objects.splice(e,0,t),this.renderOnAddRemove&&this.requestRenderAll()},dispose:function(){return this.isRendering&&(r.util.cancelAnimFrame(this.isRendering),this.isRendering=0),this.forEachObject((function(t){t.dispose&&t.dispose()})),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose&&this.backgroundImage.dispose(),this.backgroundImage=null,this.overlayImage&&this.overlayImage.dispose&&this.overlayImage.dispose(),this.overlayImage=null,this._iTextInstances=null,this.contextContainer=null,this.lowerCanvasEl.classList.remove("lower-canvas"),r.util.setStyle(this.lowerCanvasEl,this._originalCanvasStyle),delete this._originalCanvasStyle,this.lowerCanvasEl.setAttribute("width",this.width),this.lowerCanvasEl.setAttribute("height",this.height),r.util.cleanUpJsdomNode(this.lowerCanvasEl),this.lowerCanvasEl=void 0,this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this._objects.length+" }>"}}),t(r.StaticCanvas.prototype,r.Observable),t(r.StaticCanvas.prototype,r.Collection),t(r.StaticCanvas.prototype,r.DataURLExporter),t(r.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(t){var e=c();if(!e||!e.getContext)return null;var i=e.getContext("2d");return i&&"setLineDash"===t?"undefined"!==typeof i.setLineDash:null}}),r.StaticCanvas.prototype.toJSON=r.StaticCanvas.prototype.toObject,r.isLikelyNode&&(r.StaticCanvas.prototype.createPNGStream=function(){var t=a(this.lowerCanvasEl);return t&&t.createPNGStream()},r.StaticCanvas.prototype.createJPEGStream=function(t){var e=a(this.lowerCanvasEl);return e&&e.createJPEGStream(t)})}}(),r.BaseBrush=r.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",strokeMiterLimit:10,strokeDashArray:null,limitedToCanvasSize:!1,_setBrushStyles:function(t){t.strokeStyle=this.color,t.lineWidth=this.width,t.lineCap=this.strokeLineCap,t.miterLimit=this.strokeMiterLimit,t.lineJoin=this.strokeLineJoin,t.setLineDash(this.strokeDashArray||[])},_saveAndTransform:function(t){var e=this.canvas.viewportTransform;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5])},_setShadow:function(){if(this.shadow){var t=this.canvas,e=this.shadow,i=t.contextTop,n=t.getZoom();t&&t._isRetinaScaling()&&(n*=r.devicePixelRatio),i.shadowColor=e.color,i.shadowBlur=e.blur*n,i.shadowOffsetX=e.offsetX*n,i.shadowOffsetY=e.offsetY*n}},needsFullRender:function(){return new r.Color(this.color).getAlpha()<1||!!this.shadow},_resetShadow:function(){var t=this.canvas.contextTop;t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0},_isOutSideCanvas:function(t){return t.x<0||t.x>this.canvas.getWidth()||t.y<0||t.y>this.canvas.getHeight()}}),r.PencilBrush=r.util.createClass(r.BaseBrush,{decimate:.4,drawStraightLine:!1,straightLineKey:"shiftKey",initialize:function(t){this.canvas=t,this._points=[]},needsFullRender:function(){return this.callSuper("needsFullRender")||this._hasStraightLine},_drawSegment:function(t,e,i){var n=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,n.x,n.y),n},onMouseDown:function(t,e){this.canvas._isMainEvent(e.e)&&(this.drawStraightLine=e.e[this.straightLineKey],this._prepareForDrawing(t),this._captureDrawingPath(t),this._render())},onMouseMove:function(t,e){if(this.canvas._isMainEvent(e.e)&&(this.drawStraightLine=e.e[this.straightLineKey],(!0!==this.limitedToCanvasSize||!this._isOutSideCanvas(t))&&this._captureDrawingPath(t)&&this._points.length>1))if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{var i=this._points,n=i.length,r=this.canvas.contextTop;this._saveAndTransform(r),this.oldEnd&&(r.beginPath(),r.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=this._drawSegment(r,i[n-2],i[n-1],!0),r.stroke(),r.restore()}},onMouseUp:function(t){return!this.canvas._isMainEvent(t.e)||(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1)},_prepareForDrawing:function(t){var e=new r.Point(t.x,t.y);this._reset(),this._addPoint(e),this.canvas.contextTop.moveTo(e.x,e.y)},_addPoint:function(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1]))&&(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)},_reset:function(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1},_captureDrawingPath:function(t){var e=new r.Point(t.x,t.y);return this._addPoint(e)},_render:function(t){var e,i,n=this._points[0],o=this._points[1];if(t=t||this.canvas.contextTop,this._saveAndTransform(t),t.beginPath(),2===this._points.length&&n.x===o.x&&n.y===o.y){var s=this.width/1e3;n=new r.Point(n.x,n.y),o=new r.Point(o.x,o.y),n.x-=s,o.x+=s}for(t.moveTo(n.x,n.y),e=1,i=this._points.length;e<i;e++)this._drawSegment(t,n,o),n=this._points[e],o=this._points[e+1];t.lineTo(n.x,n.y),t.stroke(),t.restore()},convertPointsToSVGPath:function(t){var e=this.width/1e3;return r.util.getSmoothPathFromPoints(t,e)},_isEmptySVGPath:function(t){return"M 0 0 Q 0 0 0 0 L 0 0"===r.util.joinPath(t)},createPath:function(t){var e=new r.Path(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new r.Shadow(this.shadow)),e},decimatePoints:function(t,e){if(t.length<=2)return t;var i,n=this.canvas.getZoom(),r=Math.pow(e/n,2),o=t.length-1,s=t[0],a=[s];for(i=1;i<o-1;i++)Math.pow(s.x-t[i].x,2)+Math.pow(s.y-t[i].y,2)>=r&&(s=t[i],a.push(s));return a.push(t[o]),a},_finalizeAndAddPath:function(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));var t=this.convertPointsToSVGPath(this._points);if(this._isEmptySVGPath(t))this.canvas.requestRenderAll();else{var e=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.requestRenderAll(),e.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:e})}}}),r.CircleBrush=r.util.createClass(r.BaseBrush,{width:10,initialize:function(t){this.canvas=t,this.points=[]},drawDot:function(t){var e=this.addPoint(t),i=this.canvas.contextTop;this._saveAndTransform(i),this.dot(i,e),i.restore()},dot:function(t,e){t.fillStyle=e.fill,t.beginPath(),t.arc(e.x,e.y,e.radius,0,2*Math.PI,!1),t.closePath(),t.fill()},onMouseDown:function(t){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(t)},_render:function(){var t,e,i=this.canvas.contextTop,n=this.points;for(this._saveAndTransform(i),t=0,e=n.length;t<e;t++)this.dot(i,n[t]);i.restore()},onMouseMove:function(t){!0===this.limitedToCanvasSize&&this._isOutSideCanvas(t)||(this.needsFullRender()?(this.canvas.clearContext(this.canvas.contextTop),this.addPoint(t),this._render()):this.drawDot(t))},onMouseUp:function(){var t,e,i=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;var n=[];for(t=0,e=this.points.length;t<e;t++){var o=this.points[t],s=new r.Circle({radius:o.radius,left:o.x,top:o.y,originX:"center",originY:"center",fill:o.fill});this.shadow&&(s.shadow=new r.Shadow(this.shadow)),n.push(s)}var a=new r.Group(n);a.canvas=this.canvas,this.canvas.fire("before:path:created",{path:a}),this.canvas.add(a),this.canvas.fire("path:created",{path:a}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=i,this.canvas.requestRenderAll()},addPoint:function(t){var e=new r.Point(t.x,t.y),i=r.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,n=new r.Color(this.color).setAlpha(r.util.getRandomInt(0,100)/100).toRgba();return e.radius=i,e.fill=n,this.points.push(e),e}}),r.SprayBrush=r.util.createClass(r.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(t){this.canvas=t,this.sprayChunks=[]},onMouseDown:function(t){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(t),this.render(this.sprayChunkPoints)},onMouseMove:function(t){!0===this.limitedToCanvasSize&&this._isOutSideCanvas(t)||(this.addSprayChunk(t),this.render(this.sprayChunkPoints))},onMouseUp:function(){var t=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var e=[],i=0,n=this.sprayChunks.length;i<n;i++)for(var o=this.sprayChunks[i],s=0,a=o.length;s<a;s++){var c=new r.Rect({width:o[s].width,height:o[s].width,left:o[s].x+1,top:o[s].y+1,originX:"center",originY:"center",fill:this.color});e.push(c)}this.optimizeOverlapping&&(e=this._getOptimizedRects(e));var l=new r.Group(e);this.shadow&&l.set("shadow",new r.Shadow(this.shadow)),this.canvas.fire("before:path:created",{path:l}),this.canvas.add(l),this.canvas.fire("path:created",{path:l}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=t,this.canvas.requestRenderAll()},_getOptimizedRects:function(t){var e,i,n,r={};for(i=0,n=t.length;i<n;i++)r[e=t[i].left+""+t[i].top]||(r[e]=t[i]);var o=[];for(e in r)o.push(r[e]);return o},render:function(t){var e,i,n=this.canvas.contextTop;for(n.fillStyle=this.color,this._saveAndTransform(n),e=0,i=t.length;e<i;e++){var r=t[e];"undefined"!==typeof r.opacity&&(n.globalAlpha=r.opacity),n.fillRect(r.x,r.y,r.width,r.width)}n.restore()},_render:function(){var t,e,i=this.canvas.contextTop;for(i.fillStyle=this.color,this._saveAndTransform(i),t=0,e=this.sprayChunks.length;t<e;t++)this.render(this.sprayChunks[t]);i.restore()},addSprayChunk:function(t){this.sprayChunkPoints=[];var e,i,n,o,s=this.width/2;for(o=0;o<this.density;o++){e=r.util.getRandomInt(t.x-s,t.x+s),i=r.util.getRandomInt(t.y-s,t.y+s),n=this.dotWidthVariance?r.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):this.dotWidth;var a=new r.Point(e,i);a.width=n,this.randomOpacity&&(a.opacity=r.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(a)}this.sprayChunks.push(this.sprayChunkPoints)}}),r.PatternBrush=r.util.createClass(r.PencilBrush,{getPatternSrc:function(){var t=r.util.createCanvasElement(),e=t.getContext("2d");return t.width=t.height=25,e.fillStyle=this.color,e.beginPath(),e.arc(10,10,10,0,2*Math.PI,!1),e.closePath(),e.fill(),t},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(t){return t.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(t){this.callSuper("_setBrushStyles",t),t.strokeStyle=this.getPattern(t)},createPath:function(t){var e=this.callSuper("createPath",t),i=e._getLeftTopCoords().scalarAdd(e.strokeWidth/2);return e.stroke=new r.Pattern({source:this.source||this.getPatternSrcFunction(),offsetX:-i.x,offsetY:-i.y}),e}}),function(){var t=r.util.getPointer,e=r.util.degreesToRadians,i=r.util.isTouchEvent;for(var n in r.Canvas=r.util.createClass(r.StaticCanvas,{initialize:function(t,e){e||(e={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(t,e),this._initInteractive(),this._createCacheCanvas()},uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",interactive:!0,selection:!0,selectionKey:"shiftKey",altSelectionKey:null,selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",containerClass:"canvas-container",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,isDrawingMode:!1,preserveObjectStacking:!1,snapAngle:0,snapThreshold:null,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,targets:[],enablePointerEvents:!1,_hoveredTarget:null,_hoveredTargets:[],_initInteractive:function(){this._currentTransform=null,this._groupSelector=null,this._initWrapperElement(),this._createUpperCanvas(),this._initEventListeners(),this._initRetinaScaling(),this.freeDrawingBrush=r.PencilBrush&&new r.PencilBrush(this),this.calcOffset()},_chooseObjectsToRender:function(){var t,e,i,n=this.getActiveObjects();if(n.length>0&&!this.preserveObjectStacking){e=[],i=[];for(var r=0,o=this._objects.length;r<o;r++)t=this._objects[r],-1===n.indexOf(t)?e.push(t):i.push(t);n.length>1&&(this._activeObject._objects=i),e.push.apply(e,i)}else e=this._objects;return e},renderAll:function(){!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1);var t=this.contextContainer;return this.renderCanvas(t,this._chooseObjectsToRender()),this},renderTopLayer:function(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()},renderTop:function(){var t=this.contextTop;return this.clearContext(t),this.renderTopLayer(t),this.fire("after:render"),this},_normalizePointer:function(t,e){var i=t.calcTransformMatrix(),n=r.util.invertTransform(i),o=this.restorePointerVpt(e);return r.util.transformPoint(o,n)},isTargetTransparent:function(t,e,i){if(t.shouldCache()&&t._cacheCanvas&&t!==this._activeObject){var n=this._normalizePointer(t,{x:e,y:i}),o=Math.max(t.cacheTranslationX+n.x*t.zoomX,0),s=Math.max(t.cacheTranslationY+n.y*t.zoomY,0);return r.util.isTransparent(t._cacheContext,Math.round(o),Math.round(s),this.targetFindTolerance)}var a=this.contextCache,c=t.selectionBackgroundColor,l=this.viewportTransform;return t.selectionBackgroundColor="",this.clearContext(a),a.save(),a.transform(l[0],l[1],l[2],l[3],l[4],l[5]),t.render(a),a.restore(),t.selectionBackgroundColor=c,r.util.isTransparent(a,e,i,this.targetFindTolerance)},_isSelectionKeyPressed:function(t){return Array.isArray(this.selectionKey)?!!this.selectionKey.find((function(e){return!0===t[e]})):t[this.selectionKey]},_shouldClearSelection:function(t,e){var i=this.getActiveObjects(),n=this._activeObject;return!e||e&&n&&i.length>1&&-1===i.indexOf(e)&&n!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&n&&n!==e},_shouldCenterTransform:function(t,e,i){var n;if(t)return"scale"===e||"scaleX"===e||"scaleY"===e||"resizing"===e?n=this.centeredScaling||t.centeredScaling:"rotate"===e&&(n=this.centeredRotation||t.centeredRotation),n?!i:i},_getOriginFromCorner:function(t,e){var i={x:t.originX,y:t.originY};return"ml"===e||"tl"===e||"bl"===e?i.x="right":"mr"!==e&&"tr"!==e&&"br"!==e||(i.x="left"),"tl"===e||"mt"===e||"tr"===e?i.y="bottom":"bl"!==e&&"mb"!==e&&"br"!==e||(i.y="top"),i},_getActionFromCorner:function(t,e,i,n){if(!e||!t)return"drag";var r=n.controls[e];return r.getActionName(i,r,n)},_setupCurrentTransform:function(t,i,n){if(i){var o=this.getPointer(t),s=i.__corner,a=i.controls[s],c=n&&s?a.getActionHandler(t,i,a):r.controlsUtils.dragHandler,l=this._getActionFromCorner(n,s,t,i),h=this._getOriginFromCorner(i,s),u=t[this.centeredKey],f={target:i,action:l,actionHandler:c,corner:s,scaleX:i.scaleX,scaleY:i.scaleY,skewX:i.skewX,skewY:i.skewY,offsetX:o.x-i.left,offsetY:o.y-i.top,originX:h.x,originY:h.y,ex:o.x,ey:o.y,lastX:o.x,lastY:o.y,theta:e(i.angle),width:i.width*i.scaleX,shiftKey:t.shiftKey,altKey:u,original:r.util.saveObjectTransform(i)};this._shouldCenterTransform(i,l,u)&&(f.originX="center",f.originY="center"),f.original.originX=h.x,f.original.originY=h.y,this._currentTransform=f,this._beforeTransform(t)}},setCursor:function(t){this.upperCanvasEl.style.cursor=t},_drawSelection:function(t){var e=this._groupSelector,i=new r.Point(e.ex,e.ey),n=r.util.transformPoint(i,this.viewportTransform),o=new r.Point(e.ex+e.left,e.ey+e.top),s=r.util.transformPoint(o,this.viewportTransform),a=Math.min(n.x,s.x),c=Math.min(n.y,s.y),l=Math.max(n.x,s.x),h=Math.max(n.y,s.y),u=this.selectionLineWidth/2;this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(a,c,l-a,h-c)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,a+=u,c+=u,l-=u,h-=u,r.Object.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(a,c,l-a,h-c))},findTarget:function(t,e){if(!this.skipTargetFind){var n,r,o=this.getPointer(t,!0),s=this._activeObject,a=this.getActiveObjects(),c=i(t),l=a.length>1&&!e||1===a.length;if(this.targets=[],l&&s._findTargetCorner(o,c))return s;if(a.length>1&&!e&&s===this._searchPossibleTargets([s],o))return s;if(1===a.length&&s===this._searchPossibleTargets([s],o)){if(!this.preserveObjectStacking)return s;n=s,r=this.targets,this.targets=[]}var h=this._searchPossibleTargets(this._objects,o);return t[this.altSelectionKey]&&h&&n&&h!==n&&(h=n,this.targets=r),h}},_checkTarget:function(t,e,i){if(e&&e.visible&&e.evented&&e.containsPoint(t)){if(!this.perPixelTargetFind&&!e.perPixelTargetFind||e.isEditing)return!0;if(!this.isTargetTransparent(e,i.x,i.y))return!0}},_searchPossibleTargets:function(t,e){for(var i,n,o=t.length;o--;){var s=t[o],a=s.group?this._normalizePointer(s.group,e):e;if(this._checkTarget(a,s,e)){(i=t[o]).subTargetCheck&&i instanceof r.Group&&(n=this._searchPossibleTargets(i._objects,e))&&this.targets.push(n);break}}return i},restorePointerVpt:function(t){return r.util.transformPoint(t,r.util.invertTransform(this.viewportTransform))},getPointer:function(e,i){if(this._absolutePointer&&!i)return this._absolutePointer;if(this._pointer&&i)return this._pointer;var n,r=t(e),o=this.upperCanvasEl,s=o.getBoundingClientRect(),a=s.width||0,c=s.height||0;a&&c||("top"in s&&"bottom"in s&&(c=Math.abs(s.top-s.bottom)),"right"in s&&"left"in s&&(a=Math.abs(s.right-s.left))),this.calcOffset(),r.x=r.x-this._offset.left,r.y=r.y-this._offset.top,i||(r=this.restorePointerVpt(r));var l=this.getRetinaScaling();return 1!==l&&(r.x/=l,r.y/=l),n=0===a||0===c?{width:1,height:1}:{width:o.width/a,height:o.height/c},{x:r.x*n.width,y:r.y*n.height}},_createUpperCanvas:function(){var t=this.lowerCanvasEl.className.replace(/\s*lower-canvas\s*/,""),e=this.lowerCanvasEl,i=this.upperCanvasEl;i?i.className="":(i=this._createCanvasElement(),this.upperCanvasEl=i),r.util.addClass(i,"upper-canvas "+t),this.wrapperEl.appendChild(i),this._copyCanvasStyle(e,i),this._applyCanvasStyle(i),this.contextTop=i.getContext("2d")},getTopContext:function(){return this.contextTop},_createCacheCanvas:function(){this.cacheCanvasEl=this._createCanvasElement(),this.cacheCanvasEl.setAttribute("width",this.width),this.cacheCanvasEl.setAttribute("height",this.height),this.contextCache=this.cacheCanvasEl.getContext("2d")},_initWrapperElement:function(){this.wrapperEl=r.util.wrapElement(this.lowerCanvasEl,"div",{class:this.containerClass}),r.util.setStyle(this.wrapperEl,{width:this.width+"px",height:this.height+"px",position:"relative"}),r.util.makeElementUnselectable(this.wrapperEl)},_applyCanvasStyle:function(t){var e=this.width||t.width,i=this.height||t.height;r.util.setStyle(t,{position:"absolute",width:e+"px",height:i+"px",left:0,top:0,"touch-action":this.allowTouchScrolling?"manipulation":"none","-ms-touch-action":this.allowTouchScrolling?"manipulation":"none"}),t.width=e,t.height=i,r.util.makeElementUnselectable(t)},_copyCanvasStyle:function(t,e){e.style.cssText=t.style.cssText},getSelectionContext:function(){return this.contextTop},getSelectionElement:function(){return this.upperCanvasEl},getActiveObject:function(){return this._activeObject},getActiveObjects:function(){var t=this._activeObject;return t?"activeSelection"===t.type&&t._objects?t._objects.slice(0):[t]:[]},_onObjectRemoved:function(t){t===this._activeObject&&(this.fire("before:selection:cleared",{target:t}),this._discardActiveObject(),this.fire("selection:cleared",{target:t}),t.fire("deselected")),t===this._hoveredTarget&&(this._hoveredTarget=null,this._hoveredTargets=[]),this.callSuper("_onObjectRemoved",t)},_fireSelectionEvents:function(t,e){var i=!1,n=this.getActiveObjects(),r=[],o=[];t.forEach((function(t){-1===n.indexOf(t)&&(i=!0,t.fire("deselected",{e:e,target:t}),o.push(t))})),n.forEach((function(n){-1===t.indexOf(n)&&(i=!0,n.fire("selected",{e:e,target:n}),r.push(n))})),t.length>0&&n.length>0?i&&this.fire("selection:updated",{e:e,selected:r,deselected:o}):n.length>0?this.fire("selection:created",{e:e,selected:r}):t.length>0&&this.fire("selection:cleared",{e:e,deselected:o})},setActiveObject:function(t,e){var i=this.getActiveObjects();return this._setActiveObject(t,e),this._fireSelectionEvents(i,e),this},_setActiveObject:function(t,e){return this._activeObject!==t&&(!!this._discardActiveObject(e,t)&&(!t.onSelect({e:e})&&(this._activeObject=t,!0)))},_discardActiveObject:function(t,e){var i=this._activeObject;if(i){if(i.onDeselect({e:t,object:e}))return!1;this._activeObject=null}return!0},discardActiveObject:function(t){var e=this.getActiveObjects(),i=this.getActiveObject();return e.length&&this.fire("before:selection:cleared",{target:i,e:t}),this._discardActiveObject(t),this._fireSelectionEvents(e,t),this},dispose:function(){var t=this.wrapperEl;return this.removeListeners(),t.removeChild(this.upperCanvasEl),t.removeChild(this.lowerCanvasEl),this.contextCache=null,this.contextTop=null,["upperCanvasEl","cacheCanvasEl"].forEach(function(t){r.util.cleanUpJsdomNode(this[t]),this[t]=void 0}.bind(this)),t.parentNode&&t.parentNode.replaceChild(this.lowerCanvasEl,this.wrapperEl),delete this.wrapperEl,r.StaticCanvas.prototype.dispose.call(this),this},clear:function(){return this.discardActiveObject(),this.clearContext(this.contextTop),this.callSuper("clear")},drawControls:function(t){var e=this._activeObject;e&&e._renderControls(t)},_toObject:function(t,e,i){var n=this._realizeGroupTransformOnObject(t),r=this.callSuper("_toObject",t,e,i);return this._unwindGroupTransformOnObject(t,n),r},_realizeGroupTransformOnObject:function(t){if(t.group&&"activeSelection"===t.group.type&&this._activeObject===t.group){var e={};return["angle","flipX","flipY","left","scaleX","scaleY","skewX","skewY","top"].forEach((function(i){e[i]=t[i]})),r.util.addTransformToObject(t,this._activeObject.calcOwnMatrix()),e}return null},_unwindGroupTransformOnObject:function(t,e){e&&t.set(e)},_setSVGObject:function(t,e,i){var n=this._realizeGroupTransformOnObject(e);this.callSuper("_setSVGObject",t,e,i),this._unwindGroupTransformOnObject(e,n)},setViewportTransform:function(t){this.renderOnAddRemove&&this._activeObject&&this._activeObject.isEditing&&this._activeObject.clearContextTop(),r.StaticCanvas.prototype.setViewportTransform.call(this,t)}}),r.StaticCanvas)"prototype"!==n&&(r.Canvas[n]=r.StaticCanvas[n])}(),function(){var t=r.util.addListener,e=r.util.removeListener,i={passive:!1};function n(t,e){return t.button&&t.button===e-1}r.util.object.extend(r.Canvas.prototype,{mainTouchId:null,_initEventListeners:function(){this.removeListeners(),this._bindEvents(),this.addOrRemove(t,"add")},_getEventPrefix:function(){return this.enablePointerEvents?"pointer":"mouse"},addOrRemove:function(t,e){var n=this.upperCanvasEl,o=this._getEventPrefix();t(r.window,"resize",this._onResize),t(n,o+"down",this._onMouseDown),t(n,o+"move",this._onMouseMove,i),t(n,o+"out",this._onMouseOut),t(n,o+"enter",this._onMouseEnter),t(n,"wheel",this._onMouseWheel),t(n,"contextmenu",this._onContextMenu),t(n,"dblclick",this._onDoubleClick),t(n,"dragover",this._onDragOver),t(n,"dragenter",this._onDragEnter),t(n,"dragleave",this._onDragLeave),t(n,"drop",this._onDrop),this.enablePointerEvents||t(n,"touchstart",this._onTouchStart,i),"undefined"!==typeof eventjs&&e in eventjs&&(eventjs[e](n,"gesture",this._onGesture),eventjs[e](n,"drag",this._onDrag),eventjs[e](n,"orientation",this._onOrientationChange),eventjs[e](n,"shake",this._onShake),eventjs[e](n,"longpress",this._onLongPress))},removeListeners:function(){this.addOrRemove(e,"remove");var t=this._getEventPrefix();e(r.document,t+"up",this._onMouseUp),e(r.document,"touchend",this._onTouchEnd,i),e(r.document,t+"move",this._onMouseMove,i),e(r.document,"touchmove",this._onMouseMove,i)},_bindEvents:function(){this.eventsBound||(this._onMouseDown=this._onMouseDown.bind(this),this._onTouchStart=this._onTouchStart.bind(this),this._onMouseMove=this._onMouseMove.bind(this),this._onMouseUp=this._onMouseUp.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onResize=this._onResize.bind(this),this._onGesture=this._onGesture.bind(this),this._onDrag=this._onDrag.bind(this),this._onShake=this._onShake.bind(this),this._onLongPress=this._onLongPress.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._onMouseWheel=this._onMouseWheel.bind(this),this._onMouseOut=this._onMouseOut.bind(this),this._onMouseEnter=this._onMouseEnter.bind(this),this._onContextMenu=this._onContextMenu.bind(this),this._onDoubleClick=this._onDoubleClick.bind(this),this._onDragOver=this._onDragOver.bind(this),this._onDragEnter=this._simpleEventHandler.bind(this,"dragenter"),this._onDragLeave=this._simpleEventHandler.bind(this,"dragleave"),this._onDrop=this._onDrop.bind(this),this.eventsBound=!0)},_onGesture:function(t,e){this.__onTransformGesture&&this.__onTransformGesture(t,e)},_onDrag:function(t,e){this.__onDrag&&this.__onDrag(t,e)},_onMouseWheel:function(t){this.__onMouseWheel(t)},_onMouseOut:function(t){var e=this._hoveredTarget;this.fire("mouse:out",{target:e,e:t}),this._hoveredTarget=null,e&&e.fire("mouseout",{e:t});var i=this;this._hoveredTargets.forEach((function(e){i.fire("mouse:out",{target:e,e:t}),e&&e.fire("mouseout",{e:t})})),this._hoveredTargets=[]},_onMouseEnter:function(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",{target:null,e:t}),this._hoveredTarget=null,this._hoveredTargets=[])},_onOrientationChange:function(t,e){this.__onOrientationChange&&this.__onOrientationChange(t,e)},_onShake:function(t,e){this.__onShake&&this.__onShake(t,e)},_onLongPress:function(t,e){this.__onLongPress&&this.__onLongPress(t,e)},_onDragOver:function(t){t.preventDefault();var e=this._simpleEventHandler("dragover",t);this._fireEnterLeaveEvents(e,t)},_onDrop:function(t){return this._simpleEventHandler("drop:before",t),this._simpleEventHandler("drop",t)},_onContextMenu:function(t){return this.stopContextMenu&&(t.stopPropagation(),t.preventDefault()),!1},_onDoubleClick:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"dblclick"),this._resetTransformEventData(t)},getPointerId:function(t){var e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1},_isMainEvent:function(t){return!0===t.isPrimary||!1!==t.isPrimary&&("touchend"===t.type&&0===t.touches.length||(!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId))},_onTouchStart:function(n){n.preventDefault(),null===this.mainTouchId&&(this.mainTouchId=this.getPointerId(n)),this.__onMouseDown(n),this._resetTransformEventData();var o=this.upperCanvasEl,s=this._getEventPrefix();t(r.document,"touchend",this._onTouchEnd,i),t(r.document,"touchmove",this._onMouseMove,i),e(o,s+"down",this._onMouseDown)},_onMouseDown:function(n){this.__onMouseDown(n),this._resetTransformEventData();var o=this.upperCanvasEl,s=this._getEventPrefix();e(o,s+"move",this._onMouseMove,i),t(r.document,s+"up",this._onMouseUp),t(r.document,s+"move",this._onMouseMove,i)},_onTouchEnd:function(n){if(!(n.touches.length>0)){this.__onMouseUp(n),this._resetTransformEventData(),this.mainTouchId=null;var o=this._getEventPrefix();e(r.document,"touchend",this._onTouchEnd,i),e(r.document,"touchmove",this._onMouseMove,i);var s=this;this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout((function(){t(s.upperCanvasEl,o+"down",s._onMouseDown),s._willAddMouseDown=0}),400)}},_onMouseUp:function(n){this.__onMouseUp(n),this._resetTransformEventData();var o=this.upperCanvasEl,s=this._getEventPrefix();this._isMainEvent(n)&&(e(r.document,s+"up",this._onMouseUp),e(r.document,s+"move",this._onMouseMove,i),t(o,s+"move",this._onMouseMove,i))},_onMouseMove:function(t){!this.allowTouchScrolling&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)},_onResize:function(){this.calcOffset()},_shouldRender:function(t){var e=this._activeObject;return!!(!!e!==!!t||e&&t&&e!==t)||(e&&e.isEditing,!1)},__onMouseUp:function(t){var e,i=this._currentTransform,o=this._groupSelector,s=!1,a=!o||0===o.left&&0===o.top;if(this._cacheTransformEventData(t),e=this._target,this._handleEvent(t,"up:before"),n(t,3))this.fireRightClick&&this._handleEvent(t,"up",3,a);else{if(n(t,2))return this.fireMiddleClick&&this._handleEvent(t,"up",2,a),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)this._onMouseUpInDrawingMode(t);else if(this._isMainEvent(t)){if(i&&(this._finalizeCurrentTransform(t),s=i.actionPerformed),!a){var c=e===this._activeObject;this._maybeGroupObjects(t),s||(s=this._shouldRender(e)||!c&&e===this._activeObject)}var l,h;if(e){if(l=e._findTargetCorner(this.getPointer(t,!0),r.util.isTouchEvent(t)),e.selectable&&e!==this._activeObject&&"up"===e.activeOn)this.setActiveObject(e,t),s=!0;else{var u=e.controls[l],f=u&&u.getMouseUpHandler(t,e,u);f&&f(t,i,(h=this.getPointer(t)).x,h.y)}e.isMoving=!1}if(i&&(i.target!==e||i.corner!==l)){var d=i.target&&i.target.controls[i.corner],g=d&&d.getMouseUpHandler(t,e,u);h=h||this.getPointer(t),g&&g(t,i,h.x,h.y)}this._setCursorFromEvent(t,e),this._handleEvent(t,"up",1,a),this._groupSelector=null,this._currentTransform=null,e&&(e.__corner=0),s?this.requestRenderAll():a||this.renderTop()}}},_simpleEventHandler:function(t,e){var i=this.findTarget(e),n=this.targets,r={e:e,target:i,subTargets:n};if(this.fire(t,r),i&&i.fire(t,r),!n)return i;for(var o=0;o<n.length;o++)n[o].fire(t,r);return i},_handleEvent:function(t,e,i,n){var r=this._target,o=this.targets||[],s={e:t,target:r,subTargets:o,button:i||1,isClick:n||!1,pointer:this._pointer,absolutePointer:this._absolutePointer,transform:this._currentTransform};"up"===e&&(s.currentTarget=this.findTarget(t),s.currentSubTargets=this.targets),this.fire("mouse:"+e,s),r&&r.fire("mouse"+e,s);for(var a=0;a<o.length;a++)o[a].fire("mouse"+e,s)},_finalizeCurrentTransform:function(t){var e=this._currentTransform,i=e.target,n={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),(e.actionPerformed||this.stateful&&i.hasStateChanged())&&this._fire("modified",n)},_onMouseDownInDrawingMode:function(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&this.discardActiveObject(t).requestRenderAll();var e=this.getPointer(t);this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down")},_onMouseMoveInDrawingMode:function(t){if(this._isCurrentlyDrawing){var e=this.getPointer(t);this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")},_onMouseUpInDrawingMode:function(t){var e=this.getPointer(t);this._isCurrentlyDrawing=this.freeDrawingBrush.onMouseUp({e:t,pointer:e}),this._handleEvent(t,"up")},__onMouseDown:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"down:before");var e=this._target;if(n(t,3))this.fireRightClick&&this._handleEvent(t,"down",3);else if(n(t,2))this.fireMiddleClick&&this._handleEvent(t,"down",2);else if(this.isDrawingMode)this._onMouseDownInDrawingMode(t);else if(this._isMainEvent(t)&&!this._currentTransform){var i=this._pointer;this._previousPointer=i;var o=this._shouldRender(e),s=this._shouldGroup(t,e);if(this._shouldClearSelection(t,e)?this.discardActiveObject(t):s&&(this._handleGrouping(t,e),e=this._activeObject),!this.selection||e&&(e.selectable||e.isEditing||e===this._activeObject)||(this._groupSelector={ex:this._absolutePointer.x,ey:this._absolutePointer.y,top:0,left:0}),e){var a=e===this._activeObject;e.selectable&&"down"===e.activeOn&&this.setActiveObject(e,t);var c=e._findTargetCorner(this.getPointer(t,!0),r.util.isTouchEvent(t));if(e.__corner=c,e===this._activeObject&&(c||!s)){this._setupCurrentTransform(t,e,a);var l=e.controls[c],h=(i=this.getPointer(t),l&&l.getMouseDownHandler(t,e,l));h&&h(t,this._currentTransform,i.x,i.y)}}this._handleEvent(t,"down"),(o||s)&&this.requestRenderAll()}},_resetTransformEventData:function(){this._target=null,this._pointer=null,this._absolutePointer=null},_cacheTransformEventData:function(t){this._resetTransformEventData(),this._pointer=this.getPointer(t,!0),this._absolutePointer=this.restorePointerVpt(this._pointer),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)||null},_beforeTransform:function(t){var e=this._currentTransform;this.stateful&&e.target.saveState(),this.fire("before:transform",{e:t,transform:e})},__onMouseMove:function(t){var e,i;if(this._handleEvent(t,"move:before"),this._cacheTransformEventData(t),this.isDrawingMode)this._onMouseMoveInDrawingMode(t);else if(this._isMainEvent(t)){var n=this._groupSelector;n?(i=this._absolutePointer,n.left=i.x-n.ex,n.top=i.y-n.ey,this.renderTop()):this._currentTransform?this._transformObject(t):(e=this.findTarget(t)||null,this._setCursorFromEvent(t,e),this._fireOverOutEvents(e,t)),this._handleEvent(t,"move"),this._resetTransformEventData()}},_fireOverOutEvents:function(t,e){var i=this._hoveredTarget,n=this._hoveredTargets,r=this.targets,o=Math.max(n.length,r.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"mouseout",canvasEvtOut:"mouse:out",evtIn:"mouseover",canvasEvtIn:"mouse:over"});for(var s=0;s<o;s++)this.fireSyntheticInOutEvents(r[s],e,{oldTarget:n[s],evtOut:"mouseout",evtIn:"mouseover"});this._hoveredTarget=t,this._hoveredTargets=this.targets.concat()},_fireEnterLeaveEvents:function(t,e){var i=this._draggedoverTarget,n=this._hoveredTargets,r=this.targets,o=Math.max(n.length,r.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"dragleave",evtIn:"dragenter"});for(var s=0;s<o;s++)this.fireSyntheticInOutEvents(r[s],e,{oldTarget:n[s],evtOut:"dragleave",evtIn:"dragenter"});this._draggedoverTarget=t},fireSyntheticInOutEvents:function(t,e,i){var n,r,o,s=i.oldTarget,a=s!==t,c=i.canvasEvtIn,l=i.canvasEvtOut;a&&(n={e:e,target:t,previousTarget:s},r={e:e,target:s,nextTarget:t}),o=t&&a,s&&a&&(l&&this.fire(l,r),s.fire(i.evtOut,r)),o&&(c&&this.fire(c,n),t.fire(i.evtIn,n))},__onMouseWheel:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()},_transformObject:function(t){var e=this.getPointer(t),i=this._currentTransform;i.reset=!1,i.shiftKey=t.shiftKey,i.altKey=t[this.centeredKey],this._performTransformAction(t,i,e),i.actionPerformed&&this.requestRenderAll()},_performTransformAction:function(t,e,i){var n=i.x,r=i.y,o=e.action,s=!1,a=e.actionHandler;a&&(s=a(t,e,n,r)),"drag"===o&&s&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||s},_fire:r.controlsUtils.fireEvent,_setCursorFromEvent:function(t,e){if(!e)return this.setCursor(this.defaultCursor),!1;var i=e.hoverCursor||this.hoverCursor,n=this._activeObject&&"activeSelection"===this._activeObject.type?this._activeObject:null,r=(!n||!n.contains(e))&&e._findTargetCorner(this.getPointer(t,!0));r?this.setCursor(this.getCornerCursor(r,e,t)):(e.subTargetCheck&&this.targets.concat().reverse().map((function(t){i=t.hoverCursor||i})),this.setCursor(i))},getCornerCursor:function(t,e,i){var n=e.controls[t];return n.cursorStyleHandler(i,n,e)}})}(),function(){var t=Math.min,e=Math.max;r.util.object.extend(r.Canvas.prototype,{_shouldGroup:function(t,e){var i=this._activeObject;return i&&this._isSelectionKeyPressed(t)&&e&&e.selectable&&this.selection&&(i!==e||"activeSelection"===i.type)&&!e.onSelect({e:t})},_handleGrouping:function(t,e){var i=this._activeObject;i.__corner||(e!==i||(e=this.findTarget(t,!0))&&e.selectable)&&(i&&"activeSelection"===i.type?this._updateActiveSelection(e,t):this._createActiveSelection(e,t))},_updateActiveSelection:function(t,e){var i=this._activeObject,n=i._objects.slice(0);i.contains(t)?(i.removeWithUpdate(t),this._hoveredTarget=t,this._hoveredTargets=this.targets.concat(),1===i.size()&&this._setActiveObject(i.item(0),e)):(i.addWithUpdate(t),this._hoveredTarget=i,this._hoveredTargets=this.targets.concat()),this._fireSelectionEvents(n,e)},_createActiveSelection:function(t,e){var i=this.getActiveObjects(),n=this._createGroup(t);this._hoveredTarget=n,this._setActiveObject(n,e),this._fireSelectionEvents(i,e)},_createGroup:function(t){var e=this._objects,i=e.indexOf(this._activeObject)<e.indexOf(t)?[this._activeObject,t]:[t,this._activeObject];return this._activeObject.isEditing&&this._activeObject.exitEditing(),new r.ActiveSelection(i,{canvas:this})},_groupSelectedObjects:function(t){var e,i=this._collectObjects(t);1===i.length?this.setActiveObject(i[0],t):i.length>1&&(e=new r.ActiveSelection(i.reverse(),{canvas:this}),this.setActiveObject(e,t))},_collectObjects:function(i){for(var n,o=[],s=this._groupSelector.ex,a=this._groupSelector.ey,c=s+this._groupSelector.left,l=a+this._groupSelector.top,h=new r.Point(t(s,c),t(a,l)),u=new r.Point(e(s,c),e(a,l)),f=!this.selectionFullyContained,d=s===c&&a===l,g=this._objects.length;g--&&!((n=this._objects[g])&&n.selectable&&n.visible&&(f&&n.intersectsWithRect(h,u,!0)||n.isContainedWithinRect(h,u,!0)||f&&n.containsPoint(h,null,!0)||f&&n.containsPoint(u,null,!0))&&(o.push(n),d)););return o.length>1&&(o=o.filter((function(t){return!t.onSelect({e:i})}))),o},_maybeGroupObjects:function(t){this.selection&&this._groupSelector&&this._groupSelectedObjects(t),this.setCursor(this.defaultCursor),this._groupSelector=null}})}(),r.util.object.extend(r.StaticCanvas.prototype,{toDataURL:function(t){t||(t={});var e=t.format||"png",i=t.quality||1,n=(t.multiplier||1)*(t.enableRetinaScaling?this.getRetinaScaling():1),o=this.toCanvasElement(n,t);return r.util.toDataURL(o,e,i)},toCanvasElement:function(t,e){t=t||1;var i=((e=e||{}).width||this.width)*t,n=(e.height||this.height)*t,o=this.getZoom(),s=this.width,a=this.height,c=o*t,l=this.viewportTransform,h=(l[4]-(e.left||0))*t,u=(l[5]-(e.top||0))*t,f=this.interactive,d=[c,0,0,c,h,u],g=this.enableRetinaScaling,p=r.util.createCanvasElement(),v=this.contextTop;return p.width=i,p.height=n,this.contextTop=null,this.enableRetinaScaling=!1,this.interactive=!1,this.viewportTransform=d,this.width=i,this.height=n,this.calcViewportBoundaries(),this.renderCanvas(p.getContext("2d"),this._objects),this.viewportTransform=l,this.width=s,this.height=a,this.calcViewportBoundaries(),this.interactive=f,this.enableRetinaScaling=g,this.contextTop=v,p}}),r.util.object.extend(r.StaticCanvas.prototype,{loadFromJSON:function(t,e,i){if(t){var n="string"===typeof t?JSON.parse(t):r.util.object.clone(t),o=this,s=n.clipPath,a=this.renderOnAddRemove;return this.renderOnAddRemove=!1,delete n.clipPath,this._enlivenObjects(n.objects,(function(t){o.clear(),o._setBgOverlay(n,(function(){s?o._enlivenObjects([s],(function(i){o.clipPath=i[0],o.__setupCanvas.call(o,n,t,a,e)})):o.__setupCanvas.call(o,n,t,a,e)}))}),i),this}},__setupCanvas:function(t,e,i,n){var r=this;e.forEach((function(t,e){r.insertAt(t,e)})),this.renderOnAddRemove=i,delete t.objects,delete t.backgroundImage,delete t.overlayImage,delete t.background,delete t.overlay,this._setOptions(t),this.renderAll(),n&&n()},_setBgOverlay:function(t,e){var i={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(t.backgroundImage||t.overlayImage||t.background||t.overlay){var n=function(){i.backgroundImage&&i.overlayImage&&i.backgroundColor&&i.overlayColor&&e&&e()};this.__setBgOverlay("backgroundImage",t.backgroundImage,i,n),this.__setBgOverlay("overlayImage",t.overlayImage,i,n),this.__setBgOverlay("backgroundColor",t.background,i,n),this.__setBgOverlay("overlayColor",t.overlay,i,n)}else e&&e()},__setBgOverlay:function(t,e,i,n){var o=this;if(!e)return i[t]=!0,void(n&&n());"backgroundImage"===t||"overlayImage"===t?r.util.enlivenObjects([e],(function(e){o[t]=e[0],i[t]=!0,n&&n()})):this["set"+r.util.string.capitalize(t,!0)](e,(function(){i[t]=!0,n&&n()}))},_enlivenObjects:function(t,e,i){t&&0!==t.length?r.util.enlivenObjects(t,(function(t){e&&e(t)}),null,i):e&&e([])},_toDataURL:function(t,e){this.clone((function(i){e(i.toDataURL(t))}))},_toDataURLWithMultiplier:function(t,e,i){this.clone((function(n){i(n.toDataURLWithMultiplier(t,e))}))},clone:function(t,e){var i=JSON.stringify(this.toJSON(e));this.cloneWithoutData((function(e){e.loadFromJSON(i,(function(){t&&t(e)}))}))},cloneWithoutData:function(t){var e=r.util.createCanvasElement();e.width=this.width,e.height=this.height;var i=new r.Canvas(e);this.backgroundImage?(i.setBackgroundImage(this.backgroundImage.src,(function(){i.renderAll(),t&&t(i)})),i.backgroundImageOpacity=this.backgroundImageOpacity,i.backgroundImageStretch=this.backgroundImageStretch):t&&t(i)}}),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r=e.util.toFixed,o=e.util.string.capitalize,s=e.util.degreesToRadians,a=!e.isLikelyNode;e.Object||(e.Object=e.util.createClass(e.CommonMethods,{type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,skewX:0,skewY:0,cornerSize:13,touchCornerSize:24,transparentCorners:!0,hoverCursor:null,moveCursor:null,padding:0,borderColor:"rgb(178,204,255)",borderDashArray:null,cornerColor:"rgb(178,204,255)",cornerStrokeColor:null,cornerStyle:"rect",cornerDashArray:null,centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"nonzero",globalCompositeOperation:"source-over",backgroundColor:"",selectionBackgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,minScaleLimit:0,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,perPixelTargetFind:!1,includeDefaultValues:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,excludeFromExport:!1,objectCaching:a,statefullCache:!1,noScaleCache:!0,strokeUniform:!1,dirty:!0,__corner:0,paintFirst:"fill",activeOn:"down",stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit angle opacity fill globalCompositeOperation shadow visible backgroundColor skewX skewY fillRule paintFirst clipPath strokeUniform".split(" "),cacheProperties:"fill stroke strokeWidth strokeDashArray width height paintFirst strokeUniform strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit backgroundColor clipPath".split(" "),colorProperties:"fill stroke backgroundColor".split(" "),clipPath:void 0,inverted:!1,absolutePositioned:!1,initialize:function(t){t&&this.setOptions(t)},_createCacheCanvas:function(){this._cacheProperties={},this._cacheCanvas=e.util.createCanvasElement(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0},_limitCacheSize:function(t){var i=e.perfLimitSizeTotal,n=t.width,r=t.height,o=e.maxCacheSideLimit,s=e.minCacheSideLimit;if(n<=o&&r<=o&&n*r<=i)return n<s&&(t.width=s),r<s&&(t.height=s),t;var a=n/r,c=e.util.limitDimsByArea(a,i),l=e.util.capValue,h=l(s,c.x,o),u=l(s,c.y,o);return n>h&&(t.zoomX/=n/h,t.width=h,t.capped=!0),r>u&&(t.zoomY/=r/u,t.height=u,t.capped=!0),t},_getCacheCanvasDimensions:function(){var t=this.getTotalObjectScaling(),e=this._getTransformedDimensions(0,0),i=e.x*t.scaleX/this.scaleX,n=e.y*t.scaleY/this.scaleY;return{width:Math.ceil(i+2),height:Math.ceil(n+2),zoomX:t.scaleX,zoomY:t.scaleY,x:i,y:n}},_updateCacheCanvas:function(){var t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){var e=t._currentTransform.target,i=t._currentTransform.action;if(this===e&&i.slice&&"scale"===i.slice(0,5))return!1}var n,r,o=this._cacheCanvas,s=this._limitCacheSize(this._getCacheCanvasDimensions()),a=s.width,c=s.height,l=s.zoomX,h=s.zoomY,u=a!==this.cacheWidth||c!==this.cacheHeight,f=this.zoomX!==l||this.zoomY!==h;return!(!u&&!f)&&(u?(o.width=a,o.height=c):(this._cacheContext.setTransform(1,0,0,1,0,0),this._cacheContext.clearRect(0,0,o.width,o.height)),n=s.x/2,r=s.y/2,this.cacheTranslationX=Math.round(o.width/2-n)+n,this.cacheTranslationY=Math.round(o.height/2-r)+r,this.cacheWidth=a,this.cacheHeight=c,this._cacheContext.translate(this.cacheTranslationX,this.cacheTranslationY),this._cacheContext.scale(l,h),this.zoomX=l,this.zoomY=h,!0)},setOptions:function(t){this._setOptions(t),this._initGradient(t.fill,"fill"),this._initGradient(t.stroke,"stroke"),this._initPattern(t.fill,"fill"),this._initPattern(t.stroke,"stroke")},transform:function(t){var e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,i=this.calcTransformMatrix(!e);t.transform(i[0],i[1],i[2],i[3],i[4],i[5])},toObject:function(t){var i=e.Object.NUM_FRACTION_DIGITS,n={type:this.type,version:e.version,originX:this.originX,originY:this.originY,left:r(this.left,i),top:r(this.top,i),width:r(this.width,i),height:r(this.height,i),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:r(this.strokeWidth,i),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:r(this.strokeMiterLimit,i),scaleX:r(this.scaleX,i),scaleY:r(this.scaleY,i),angle:r(this.angle,i),flipX:this.flipX,flipY:this.flipY,opacity:r(this.opacity,i),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:r(this.skewX,i),skewY:r(this.skewY,i)};return this.clipPath&&!this.clipPath.excludeFromExport&&(n.clipPath=this.clipPath.toObject(t),n.clipPath.inverted=this.clipPath.inverted,n.clipPath.absolutePositioned=this.clipPath.absolutePositioned),e.util.populateWithProperties(this,n,t),this.includeDefaultValues||(n=this._removeDefaultValues(n)),n},toDatalessObject:function(t){return this.toObject(t)},_removeDefaultValues:function(t){var i=e.util.getKlass(t.type).prototype;return i.stateProperties.forEach((function(e){"left"!==e&&"top"!==e&&(t[e]===i[e]&&delete t[e],Array.isArray(t[e])&&Array.isArray(i[e])&&0===t[e].length&&0===i[e].length&&delete t[e])})),t},toString:function(){return"#<fabric."+o(this.type)+">"},getObjectScaling:function(){if(!this.group)return{scaleX:this.scaleX,scaleY:this.scaleY};var t=e.util.qrDecompose(this.calcTransformMatrix());return{scaleX:Math.abs(t.scaleX),scaleY:Math.abs(t.scaleY)}},getTotalObjectScaling:function(){var t=this.getObjectScaling(),e=t.scaleX,i=t.scaleY;if(this.canvas){var n=this.canvas.getZoom(),r=this.canvas.getRetinaScaling();e*=n*r,i*=n*r}return{scaleX:e,scaleY:i}},getObjectOpacity:function(){var t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t},_set:function(t,i){var n="scaleX"===t||"scaleY"===t,r=this[t]!==i,o=!1;return n&&(i=this._constrainScale(i)),"scaleX"===t&&i<0?(this.flipX=!this.flipX,i*=-1):"scaleY"===t&&i<0?(this.flipY=!this.flipY,i*=-1):"shadow"!==t||!i||i instanceof e.Shadow?"dirty"===t&&this.group&&this.group.set("dirty",i):i=new e.Shadow(i),this[t]=i,r&&(o=this.group&&this.group.isOnACache(),this.cacheProperties.indexOf(t)>-1?(this.dirty=!0,o&&this.group.set("dirty",!0)):o&&this.stateProperties.indexOf(t)>-1&&this.group.set("dirty",!0)),this},setOnGroup:function(){},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:e.iMatrix.concat()},isNotVisible:function(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible},render:function(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t,this),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.dirty=!1,this.drawObject(t),this.objectCaching&&this.statefullCache&&this.saveState({propertySet:"cacheProperties"})),t.restore())},renderCache:function(t){t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&(this.statefullCache&&this.saveState({propertySet:"cacheProperties"}),this.drawObject(this._cacheContext,t.forClipping),this.dirty=!1)},_removeCacheCanvas:function(){this._cacheCanvas=null,this._cacheContext=null,this.cacheWidth=0,this.cacheHeight=0},hasStroke:function(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth},hasFill:function(){return this.fill&&"transparent"!==this.fill},needsItsOwnCache:function(){return!("stroke"!==this.paintFirst||!this.hasFill()||!this.hasStroke()||"object"!==typeof this.shadow)||!!this.clipPath},shouldCache:function(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.group||!this.group.isOnACache()),this.ownCaching},willDrawShadow:function(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)},drawClipPathOnCache:function(t,i){if(t.save(),i.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",i.absolutePositioned){var n=e.util.invertTransform(this.calcTransformMatrix());t.transform(n[0],n[1],n[2],n[3],n[4],n[5])}i.transform(t),t.scale(1/i.zoomX,1/i.zoomY),t.drawImage(i._cacheCanvas,-i.cacheTranslationX,-i.cacheTranslationY),t.restore()},drawObject:function(t,e){var i=this.fill,n=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath),this.fill=i,this.stroke=n},_drawClipPath:function(t,e){e&&(e.canvas=this.canvas,e.shouldCache(),e._transformDone=!0,e.renderCache({forClipping:!0}),this.drawClipPathOnCache(t,e))},drawCacheOnCanvas:function(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)},isCacheDirty:function(t){if(this.isNotVisible())return!1;if(this._cacheCanvas&&this._cacheContext&&!t&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned||this.statefullCache&&this.hasStateChanged("cacheProperties")){if(this._cacheCanvas&&this._cacheContext&&!t){var e=this.cacheWidth/this.zoomX,i=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-e/2,-i/2,e,i)}return!0}return!1},_renderBackground:function(t){if(this.backgroundColor){var e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}},_setOpacity:function(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity},_setStrokeStyles:function(t,e){var i=e.stroke;i&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,i.toLive?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?this._applyPatternForTransformedGradient(t,i):(t.strokeStyle=i.toLive(t,this),this._applyPatternGradientTransform(t,i)):t.strokeStyle=e.stroke)},_setFillStyles:function(t,e){var i=e.fill;i&&(i.toLive?(t.fillStyle=i.toLive(t,this),this._applyPatternGradientTransform(t,e.fill)):t.fillStyle=i)},_setClippingProperties:function(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"},_setLineDash:function(t,e){e&&0!==e.length&&(1&e.length&&e.push.apply(e,e),t.setLineDash(e))},_renderControls:function(t,i){var n,r,o,a=this.getViewportTransform(),c=this.calcTransformMatrix();r="undefined"!==typeof(i=i||{}).hasBorders?i.hasBorders:this.hasBorders,o="undefined"!==typeof i.hasControls?i.hasControls:this.hasControls,c=e.util.multiplyTransformMatrices(a,c),n=e.util.qrDecompose(c),t.save(),t.translate(n.translateX,n.translateY),t.lineWidth=1*this.borderScaleFactor,this.group||(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(n.angle-=180),t.rotate(s(this.group?n.angle:this.angle)),i.forActiveSelection||this.group?r&&this.drawBordersInGroup(t,n,i):r&&this.drawBorders(t,i),o&&this.drawControls(t,i),t.restore()},_setShadow:function(t){if(this.shadow){var i,n=this.shadow,r=this.canvas,o=r&&r.viewportTransform[0]||1,s=r&&r.viewportTransform[3]||1;i=n.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),r&&r._isRetinaScaling()&&(o*=e.devicePixelRatio,s*=e.devicePixelRatio),t.shadowColor=n.color,t.shadowBlur=n.blur*e.browserShadowBlurConstant*(o+s)*(i.scaleX+i.scaleY)/4,t.shadowOffsetX=n.offsetX*o*i.scaleX,t.shadowOffsetY=n.offsetY*s*i.scaleY}},_removeShadow:function(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)},_applyPatternGradientTransform:function(t,e){if(!e||!e.toLive)return{offsetX:0,offsetY:0};var i=e.gradientTransform||e.patternTransform,n=-this.width/2+e.offsetX||0,r=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,n,r):t.transform(1,0,0,1,n,r),i&&t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),{offsetX:n,offsetY:r}},_renderPaintInOrder:function(t){"stroke"===this.paintFirst?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))},_render:function(){},_renderFill:function(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())},_renderStroke:function(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform&&this.group){var e=this.getObjectScaling();t.scale(1/e.scaleX,1/e.scaleY)}else this.strokeUniform&&t.scale(1/this.scaleX,1/this.scaleY);this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}},_applyPatternForTransformedGradient:function(t,i){var n,r=this._limitCacheSize(this._getCacheCanvasDimensions()),o=e.util.createCanvasElement(),s=this.canvas.getRetinaScaling(),a=r.x/this.scaleX/s,c=r.y/this.scaleY/s;o.width=Math.ceil(a),o.height=Math.ceil(c),(n=o.getContext("2d")).beginPath(),n.moveTo(0,0),n.lineTo(a,0),n.lineTo(a,c),n.lineTo(0,c),n.closePath(),n.translate(a/2,c/2),n.scale(r.zoomX/this.scaleX/s,r.zoomY/this.scaleY/s),this._applyPatternGradientTransform(n,i),n.fillStyle=i.toLive(t),n.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(s*this.scaleX/r.zoomX,s*this.scaleY/r.zoomY),t.strokeStyle=n.createPattern(o,"no-repeat")},_findCenterFromElement:function(){return{x:this.left+this.width/2,y:this.top+this.height/2}},_assignTransformMatrixProps:function(){if(this.transformMatrix){var t=e.util.qrDecompose(this.transformMatrix);this.flipX=!1,this.flipY=!1,this.set("scaleX",t.scaleX),this.set("scaleY",t.scaleY),this.angle=t.angle,this.skewX=t.skewX,this.skewY=0}},_removeTransformMatrix:function(t){var i=this._findCenterFromElement();this.transformMatrix&&(this._assignTransformMatrixProps(),i=e.util.transformPoint(i,this.transformMatrix)),this.transformMatrix=null,t&&(this.scaleX*=t.scaleX,this.scaleY*=t.scaleY,this.cropX=t.cropX,this.cropY=t.cropY,i.x+=t.offsetLeft,i.y+=t.offsetTop,this.width=t.width,this.height=t.height),this.setPositionByOrigin(i,"center","center")},clone:function(t,i){var n=this.toObject(i);this.constructor.fromObject?this.constructor.fromObject(n,t):e.Object._fromObject("Object",n,t)},cloneAsImage:function(t,i){var n=this.toCanvasElement(i);return t&&t(new e.Image(n)),this},toCanvasElement:function(t){t||(t={});var i=e.util,n=i.saveObjectTransform(this),r=this.group,o=this.shadow,s=Math.abs,a=(t.multiplier||1)*(t.enableRetinaScaling?e.devicePixelRatio:1);delete this.group,t.withoutTransform&&i.resetObjectTransform(this),t.withoutShadow&&(this.shadow=null);var c,l,h,u,f=e.util.createCanvasElement(),d=this.getBoundingRect(!0,!0),g=this.shadow,p={x:0,y:0};g&&(l=g.blur,c=g.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),p.x=2*Math.round(s(g.offsetX)+l)*s(c.scaleX),p.y=2*Math.round(s(g.offsetY)+l)*s(c.scaleY)),h=d.width+p.x,u=d.height+p.y,f.width=Math.ceil(h),f.height=Math.ceil(u);var v=new e.StaticCanvas(f,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1});"jpeg"===t.format&&(v.backgroundColor="#fff"),this.setPositionByOrigin(new e.Point(v.width/2,v.height/2),"center","center");var m=this.canvas;v.add(this);var y=v.toCanvasElement(a||1,t);return this.shadow=o,this.set("canvas",m),r&&(this.group=r),this.set(n).setCoords(),v._objects=[],v.dispose(),v=null,y},toDataURL:function(t){return t||(t={}),e.util.toDataURL(this.toCanvasElement(t),t.format||"png",t.quality||1)},isType:function(t){return arguments.length>1?Array.from(arguments).includes(this.type):this.type===t},complexity:function(){return 1},toJSON:function(t){return this.toObject(t)},rotate:function(t){var e=("center"!==this.originX||"center"!==this.originY)&&this.centeredRotation;return e&&this._setOriginToCenter(),this.set("angle",t),e&&this._resetOrigin(),this},centerH:function(){return this.canvas&&this.canvas.centerObjectH(this),this},viewportCenterH:function(){return this.canvas&&this.canvas.viewportCenterObjectH(this),this},centerV:function(){return this.canvas&&this.canvas.centerObjectV(this),this},viewportCenterV:function(){return this.canvas&&this.canvas.viewportCenterObjectV(this),this},center:function(){return this.canvas&&this.canvas.centerObject(this),this},viewportCenter:function(){return this.canvas&&this.canvas.viewportCenterObject(this),this},getLocalPointer:function(t,i){i=i||this.canvas.getPointer(t);var n=new e.Point(i.x,i.y),r=this._getLeftTopCoords();return this.angle&&(n=e.util.rotatePoint(n,r,s(-this.angle))),{x:n.x-r.x,y:n.y-r.y}},_setupCompositeOperation:function(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)},dispose:function(){e.runningAnimations&&e.runningAnimations.cancelByTarget(this)}}),e.util.createAccessors&&e.util.createAccessors(e.Object),i(e.Object.prototype,e.Observable),e.Object.NUM_FRACTION_DIGITS=2,e.Object.ENLIVEN_PROPS=["clipPath"],e.Object._fromObject=function(t,i,r,o){var s=e[t];i=n(i,!0),e.util.enlivenPatterns([i.fill,i.stroke],(function(t){"undefined"!==typeof t[0]&&(i.fill=t[0]),"undefined"!==typeof t[1]&&(i.stroke=t[1]),e.util.enlivenObjectEnlivables(i,i,(function(){var t=o?new s(i[o],i):new s(i);r&&r(t)}))}))},e.Object.__uid=0)}(e),function(){var t=r.util.degreesToRadians,e={left:-.5,center:0,right:.5},i={top:-.5,center:0,bottom:.5};r.util.object.extend(r.Object.prototype,{translateToGivenOrigin:function(t,n,o,s,a){var c,l,h,u=t.x,f=t.y;return"string"===typeof n?n=e[n]:n-=.5,"string"===typeof s?s=e[s]:s-=.5,"string"===typeof o?o=i[o]:o-=.5,"string"===typeof a?a=i[a]:a-=.5,l=a-o,((c=s-n)||l)&&(h=this._getTransformedDimensions(),u=t.x+c*h.x,f=t.y+l*h.y),new r.Point(u,f)},translateToCenterPoint:function(e,i,n){var o=this.translateToGivenOrigin(e,i,n,"center","center");return this.angle?r.util.rotatePoint(o,e,t(this.angle)):o},translateToOriginPoint:function(e,i,n){var o=this.translateToGivenOrigin(e,"center","center",i,n);return this.angle?r.util.rotatePoint(o,e,t(this.angle)):o},getCenterPoint:function(){var t=new r.Point(this.left,this.top);return this.translateToCenterPoint(t,this.originX,this.originY)},getPointByOrigin:function(t,e){var i=this.getCenterPoint();return this.translateToOriginPoint(i,t,e)},toLocalPoint:function(e,i,n){var o,s,a=this.getCenterPoint();return o="undefined"!==typeof i&&"undefined"!==typeof n?this.translateToGivenOrigin(a,"center","center",i,n):new r.Point(this.left,this.top),s=new r.Point(e.x,e.y),this.angle&&(s=r.util.rotatePoint(s,a,-t(this.angle))),s.subtractEquals(o)},setPositionByOrigin:function(t,e,i){var n=this.translateToCenterPoint(t,e,i),r=this.translateToOriginPoint(n,this.originX,this.originY);this.set("left",r.x),this.set("top",r.y)},adjustPosition:function(i){var n,o,s=t(this.angle),a=this.getScaledWidth(),c=r.util.cos(s)*a,l=r.util.sin(s)*a;n="string"===typeof this.originX?e[this.originX]:this.originX-.5,o="string"===typeof i?e[i]:i-.5,this.left+=c*(o-n),this.top+=l*(o-n),this.setCoords(),this.originX=i},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var t=this.getCenterPoint();this.originX="center",this.originY="center",this.left=t.x,this.top=t.y},_resetOrigin:function(){var t=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=t.x,this.top=t.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","top")}})}(),function(){var t=r.util,e=t.degreesToRadians,i=t.multiplyTransformMatrices,n=t.transformPoint;t.object.extend(r.Object.prototype,{oCoords:null,aCoords:null,lineCoords:null,ownMatrixCache:null,matrixCache:null,controls:{},_getCoords:function(t,e){return e?t?this.calcACoords():this.calcLineCoords():(this.aCoords&&this.lineCoords||this.setCoords(!0),t?this.aCoords:this.lineCoords)},getCoords:function(t,e){return i=this._getCoords(t,e),[new r.Point(i.tl.x,i.tl.y),new r.Point(i.tr.x,i.tr.y),new r.Point(i.br.x,i.br.y),new r.Point(i.bl.x,i.bl.y)];var i},intersectsWithRect:function(t,e,i,n){var o=this.getCoords(i,n);return"Intersection"===r.Intersection.intersectPolygonRectangle(o,t,e).status},intersectsWithObject:function(t,e,i){return"Intersection"===r.Intersection.intersectPolygonPolygon(this.getCoords(e,i),t.getCoords(e,i)).status||t.isContainedWithinObject(this,e,i)||this.isContainedWithinObject(t,e,i)},isContainedWithinObject:function(t,e,i){for(var n=this.getCoords(e,i),r=e?t.aCoords:t.lineCoords,o=0,s=t._getImageLines(r);o<4;o++)if(!t.containsPoint(n[o],s))return!1;return!0},isContainedWithinRect:function(t,e,i,n){var r=this.getBoundingRect(i,n);return r.left>=t.x&&r.left+r.width<=e.x&&r.top>=t.y&&r.top+r.height<=e.y},containsPoint:function(t,e,i,n){var r=this._getCoords(i,n),o=(e=e||this._getImageLines(r),this._findCrossPoints(t,e));return 0!==o&&o%2===1},isOnScreen:function(t){if(!this.canvas)return!1;var e=this.canvas.vptCoords.tl,i=this.canvas.vptCoords.br;return!!this.getCoords(!0,t).some((function(t){return t.x<=i.x&&t.x>=e.x&&t.y<=i.y&&t.y>=e.y}))||(!!this.intersectsWithRect(e,i,!0,t)||this._containsCenterOfCanvas(e,i,t))},_containsCenterOfCanvas:function(t,e,i){var n={x:(t.x+e.x)/2,y:(t.y+e.y)/2};return!!this.containsPoint(n,null,!0,i)},isPartiallyOnScreen:function(t){if(!this.canvas)return!1;var e=this.canvas.vptCoords.tl,i=this.canvas.vptCoords.br;return!!this.intersectsWithRect(e,i,!0,t)||this.getCoords(!0,t).every((function(t){return(t.x>=i.x||t.x<=e.x)&&(t.y>=i.y||t.y<=e.y)}))&&this._containsCenterOfCanvas(e,i,t)},_getImageLines:function(t){return{topline:{o:t.tl,d:t.tr},rightline:{o:t.tr,d:t.br},bottomline:{o:t.br,d:t.bl},leftline:{o:t.bl,d:t.tl}}},_findCrossPoints:function(t,e){var i,n,r,o=0;for(var s in e)if(!((r=e[s]).o.y<t.y&&r.d.y<t.y)&&!(r.o.y>=t.y&&r.d.y>=t.y)&&(r.o.x===r.d.x&&r.o.x>=t.x?n=r.o.x:(i=(r.d.y-r.o.y)/(r.d.x-r.o.x),n=-(t.y-0*t.x-(r.o.y-i*r.o.x))/(0-i)),n>=t.x&&(o+=1),2===o))break;return o},getBoundingRect:function(e,i){var n=this.getCoords(e,i);return t.makeBoundingBoxFromPoints(n)},getScaledWidth:function(){return this._getTransformedDimensions().x},getScaledHeight:function(){return this._getTransformedDimensions().y},_constrainScale:function(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:0===t?1e-4:t},scale:function(t){return this._set("scaleX",t),this._set("scaleY",t),this.setCoords()},scaleToWidth:function(t,e){var i=this.getBoundingRect(e).width/this.getScaledWidth();return this.scale(t/this.width/i)},scaleToHeight:function(t,e){var i=this.getBoundingRect(e).height/this.getScaledHeight();return this.scale(t/this.height/i)},calcLineCoords:function(){var i=this.getViewportTransform(),r=this.padding,o=e(this.angle),s=t.cos(o)*r,a=t.sin(o)*r,c=s+a,l=s-a,h=this.calcACoords(),u={tl:n(h.tl,i),tr:n(h.tr,i),bl:n(h.bl,i),br:n(h.br,i)};return r&&(u.tl.x-=l,u.tl.y-=c,u.tr.x+=c,u.tr.y-=l,u.bl.x-=c,u.bl.y+=l,u.br.x+=l,u.br.y+=c),u},calcOCoords:function(){var t=this._calcRotateMatrix(),e=this._calcTranslateMatrix(),n=this.getViewportTransform(),r=i(n,e),o=i(r,t),s=(o=i(o,[1/n[0],0,0,1/n[3],0,0]),this._calculateCurrentDimensions()),a={};return this.forEachControl((function(t,e,i){a[e]=t.positionHandler(s,o,i)})),a},calcACoords:function(){var t=this._calcRotateMatrix(),e=this._calcTranslateMatrix(),r=i(e,t),o=this._getTransformedDimensions(),s=o.x/2,a=o.y/2;return{tl:n({x:-s,y:-a},r),tr:n({x:s,y:-a},r),bl:n({x:-s,y:a},r),br:n({x:s,y:a},r)}},setCoords:function(t){return this.aCoords=this.calcACoords(),this.lineCoords=this.group?this.aCoords:this.calcLineCoords(),t||(this.oCoords=this.calcOCoords(),this._setCornerCoords&&this._setCornerCoords()),this},_calcRotateMatrix:function(){return t.calcRotateMatrix(this)},_calcTranslateMatrix:function(){var t=this.getCenterPoint();return[1,0,0,1,t.x,t.y]},transformMatrixKey:function(t){var e="_",i="";return!t&&this.group&&(i=this.group.transformMatrixKey(t)+e),i+this.top+e+this.left+e+this.scaleX+e+this.scaleY+e+this.skewX+e+this.skewY+e+this.angle+e+this.originX+e+this.originY+e+this.width+e+this.height+e+this.strokeWidth+this.flipX+this.flipY},calcTransformMatrix:function(t){var e=this.calcOwnMatrix();if(t||!this.group)return e;var n=this.transformMatrixKey(t),r=this.matrixCache||(this.matrixCache={});return r.key===n?r.value:(this.group&&(e=i(this.group.calcTransformMatrix(!1),e)),r.key=n,r.value=e,e)},calcOwnMatrix:function(){var e=this.transformMatrixKey(!0),i=this.ownMatrixCache||(this.ownMatrixCache={});if(i.key===e)return i.value;var n=this._calcTranslateMatrix(),r={angle:this.angle,translateX:n[4],translateY:n[5],scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY};return i.key=e,i.value=t.composeMatrix(r),i.value},_getNonTransformedDimensions:function(){var t=this.strokeWidth;return{x:this.width+t,y:this.height+t}},_getTransformedDimensions:function(e,i){"undefined"===typeof e&&(e=this.skewX),"undefined"===typeof i&&(i=this.skewY);var n,r,o,s=0===e&&0===i;if(this.strokeUniform?(r=this.width,o=this.height):(r=(n=this._getNonTransformedDimensions()).x,o=n.y),s)return this._finalizeDimensions(r*this.scaleX,o*this.scaleY);var a=t.sizeAfterTransform(r,o,{scaleX:this.scaleX,scaleY:this.scaleY,skewX:e,skewY:i});return this._finalizeDimensions(a.x,a.y)},_finalizeDimensions:function(t,e){return this.strokeUniform?{x:t+this.strokeWidth,y:e+this.strokeWidth}:{x:t,y:e}},_calculateCurrentDimensions:function(){var t=this.getViewportTransform(),e=this._getTransformedDimensions();return n(e,t,!0).scalarAdd(2*this.padding)}})}(),r.util.object.extend(r.Object.prototype,{sendToBack:function(){return this.group?r.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas&&this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?r.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas&&this.canvas.bringToFront(this),this},sendBackwards:function(t){return this.group?r.StaticCanvas.prototype.sendBackwards.call(this.group,this,t):this.canvas&&this.canvas.sendBackwards(this,t),this},bringForward:function(t){return this.group?r.StaticCanvas.prototype.bringForward.call(this.group,this,t):this.canvas&&this.canvas.bringForward(this,t),this},moveTo:function(t){return this.group&&"activeSelection"!==this.group.type?r.StaticCanvas.prototype.moveTo.call(this.group,this,t):this.canvas&&this.canvas.moveTo(this,t),this}}),function(){function t(t,e){if(e){if(e.toLive)return t+": url(#SVGID_"+e.id+"); ";var i=new r.Color(e),n=t+": "+i.toRgb()+"; ",o=i.getAlpha();return 1!==o&&(n+=t+"-opacity: "+o.toString()+"; "),n}return t+": none; "}var e=r.util.toFixed;r.util.object.extend(r.Object.prototype,{getSvgStyles:function(e){var i=this.fillRule?this.fillRule:"nonzero",n=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):"none",o=this.strokeDashOffset?this.strokeDashOffset:"0",s=this.strokeLineCap?this.strokeLineCap:"butt",a=this.strokeLineJoin?this.strokeLineJoin:"miter",c=this.strokeMiterLimit?this.strokeMiterLimit:"4",l="undefined"!==typeof this.opacity?this.opacity:"1",h=this.visible?"":" visibility: hidden;",u=e?"":this.getSvgFilter(),f=t("fill",this.fill);return[t("stroke",this.stroke),"stroke-width: ",n,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",s,"; ","stroke-dashoffset: ",o,"; ","stroke-linejoin: ",a,"; ","stroke-miterlimit: ",c,"; ",f,"fill-rule: ",i,"; ","opacity: ",l,";",u,h].join("")},getSvgSpanStyles:function(e,i){var n="; ",r=e.fontFamily?"font-family: "+(-1===e.fontFamily.indexOf("'")&&-1===e.fontFamily.indexOf('"')?"'"+e.fontFamily+"'":e.fontFamily)+n:"",o=e.strokeWidth?"stroke-width: "+e.strokeWidth+n:"",s=(r=r,e.fontSize?"font-size: "+e.fontSize+"px"+n:""),a=e.fontStyle?"font-style: "+e.fontStyle+n:"",c=e.fontWeight?"font-weight: "+e.fontWeight+n:"",l=e.fill?t("fill",e.fill):"",h=e.stroke?t("stroke",e.stroke):"",u=this.getSvgTextDecoration(e);return u&&(u="text-decoration: "+u+n),[h,o,r,s,a,c,u,l,e.deltaY?"baseline-shift: "+-e.deltaY+"; ":"",i?"white-space: pre; ":""].join("")},getSvgTextDecoration:function(t){return["overline","underline","line-through"].filter((function(e){return t[e.replace("-","")]})).join(" ")},getSvgFilter:function(){return this.shadow?"filter: url(#SVGID_"+this.shadow.id+");":""},getSvgCommons:function(){return[this.id?'id="'+this.id+'" ':"",this.clipPath?'clip-path="url(#'+this.clipPath.clipPathId+')" ':""].join("")},getSvgTransform:function(t,e){var i=t?this.calcTransformMatrix():this.calcOwnMatrix();return'transform="'+r.util.matrixToSVG(i)+(e||"")+'" '},_setSVGBg:function(t){if(this.backgroundColor){var i=r.Object.NUM_FRACTION_DIGITS;t.push("\t\t<rect ",this._getFillAttributes(this.backgroundColor),' x="',e(-this.width/2,i),'" y="',e(-this.height/2,i),'" width="',e(this.width,i),'" height="',e(this.height,i),'"></rect>\n')}},toSVG:function(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})},toClipPathSVG:function(t){return"\t"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})},_createBaseClipPathSVGMarkup:function(t,e){var i=(e=e||{}).reviver,n=e.additionalTransform||"",r=[this.getSvgTransform(!0,n),this.getSvgCommons()].join(""),o=t.indexOf("COMMON_PARTS");return t[o]=r,i?i(t.join("")):t.join("")},_createBaseSVGMarkup:function(t,e){var i,n,o=(e=e||{}).noStyle,s=e.reviver,a=o?"":'style="'+this.getSvgStyles()+'" ',c=e.withShadow?'style="'+this.getSvgFilter()+'" ':"",l=this.clipPath,h=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",u=l&&l.absolutePositioned,f=this.stroke,d=this.fill,g=this.shadow,p=[],v=t.indexOf("COMMON_PARTS"),m=e.additionalTransform;return l&&(l.clipPathId="CLIPPATH_"+r.Object.__uid++,n='<clipPath id="'+l.clipPathId+'" >\n'+l.toClipPathSVG(s)+"</clipPath>\n"),u&&p.push("<g ",c,this.getSvgCommons()," >\n"),p.push("<g ",this.getSvgTransform(!1),u?"":c+this.getSvgCommons()," >\n"),i=[a,h,o?"":this.addPaintOrder()," ",m?'transform="'+m+'" ':""].join(""),t[v]=i,d&&d.toLive&&p.push(d.toSVG(this)),f&&f.toLive&&p.push(f.toSVG(this)),g&&p.push(g.toSVG(this)),l&&p.push(n),p.push(t.join("")),p.push("</g>\n"),u&&p.push("</g>\n"),s?s(p.join("")):p.join("")},addPaintOrder:function(){return"fill"!==this.paintFirst?' paint-order="'+this.paintFirst+'" ':""}})}(),function(){var t=r.util.object.extend,e="stateProperties";function i(e,i,n){var r={};n.forEach((function(t){r[t]=e[t]})),t(e[i],r,!0)}function n(t,e,i){if(t===e)return!0;if(Array.isArray(t)){if(!Array.isArray(e)||t.length!==e.length)return!1;for(var r=0,o=t.length;r<o;r++)if(!n(t[r],e[r]))return!1;return!0}if(t&&"object"===typeof t){var s,a=Object.keys(t);if(!e||"object"!==typeof e||!i&&a.length!==Object.keys(e).length)return!1;for(r=0,o=a.length;r<o;r++)if("canvas"!==(s=a[r])&&"group"!==s&&!n(t[s],e[s]))return!1;return!0}}r.util.object.extend(r.Object.prototype,{hasStateChanged:function(t){var i="_"+(t=t||e);return Object.keys(this[i]).length<this[t].length||!n(this[i],this,!0)},saveState:function(t){var n=t&&t.propertySet||e,r="_"+n;return this[r]?(i(this,r,this[n]),t&&t.stateProperties&&i(this,r,t.stateProperties),this):this.setupState(t)},setupState:function(t){var i=(t=t||{}).propertySet||e;return t.propertySet=i,this["_"+i]={},this.saveState(t),this}})}(),function(){var t=r.util.degreesToRadians;r.util.object.extend(r.Object.prototype,{_findTargetCorner:function(t,e){if(!this.hasControls||this.group||!this.canvas||this.canvas._activeObject!==this)return!1;var i,n,r,o=t.x,s=t.y,a=Object.keys(this.oCoords),c=a.length-1;for(this.__corner=0;c>=0;c--)if(r=a[c],this.isControlVisible(r)&&(n=this._getImageLines(e?this.oCoords[r].touchCorner:this.oCoords[r].corner),0!==(i=this._findCrossPoints({x:o,y:s},n))&&i%2===1))return this.__corner=r,r;return!1},forEachControl:function(t){for(var e in this.controls)t(this.controls[e],e,this)},_setCornerCoords:function(){var t=this.oCoords;for(var e in t){var i=this.controls[e];t[e].corner=i.calcCornerCoords(this.angle,this.cornerSize,t[e].x,t[e].y,!1),t[e].touchCorner=i.calcCornerCoords(this.angle,this.touchCornerSize,t[e].x,t[e].y,!0)}},drawSelectionBackground:function(e){if(!this.selectionBackgroundColor||this.canvas&&!this.canvas.interactive||this.canvas&&this.canvas._activeObject!==this)return this;e.save();var i=this.getCenterPoint(),n=this._calculateCurrentDimensions(),r=this.canvas.viewportTransform;return e.translate(i.x,i.y),e.scale(1/r[0],1/r[3]),e.rotate(t(this.angle)),e.fillStyle=this.selectionBackgroundColor,e.fillRect(-n.x/2,-n.y/2,n.x,n.y),e.restore(),this},drawBorders:function(t,e){e=e||{};var i=this._calculateCurrentDimensions(),n=this.borderScaleFactor,r=i.x+n,o=i.y+n,s="undefined"!==typeof e.hasControls?e.hasControls:this.hasControls,a=!1;return t.save(),t.strokeStyle=e.borderColor||this.borderColor,this._setLineDash(t,e.borderDashArray||this.borderDashArray),t.strokeRect(-r/2,-o/2,r,o),s&&(t.beginPath(),this.forEachControl((function(e,i,n){e.withConnection&&e.getVisibility(n,i)&&(a=!0,t.moveTo(e.x*r,e.y*o),t.lineTo(e.x*r+e.offsetX,e.y*o+e.offsetY))})),a&&t.stroke()),t.restore(),this},drawBordersInGroup:function(t,e,i){i=i||{};var n=r.util.sizeAfterTransform(this.width,this.height,e),o=this.strokeWidth,s=this.strokeUniform,a=this.borderScaleFactor,c=n.x+o*(s?this.canvas.getZoom():e.scaleX)+a,l=n.y+o*(s?this.canvas.getZoom():e.scaleY)+a;return t.save(),this._setLineDash(t,i.borderDashArray||this.borderDashArray),t.strokeStyle=i.borderColor||this.borderColor,t.strokeRect(-c/2,-l/2,c,l),t.restore(),this},drawControls:function(t,e){e=e||{},t.save();var i,n,o=1;return this.canvas&&(o=this.canvas.getRetinaScaling()),t.setTransform(o,0,0,o,0,0),t.strokeStyle=t.fillStyle=e.cornerColor||this.cornerColor,this.transparentCorners||(t.strokeStyle=e.cornerStrokeColor||this.cornerStrokeColor),this._setLineDash(t,e.cornerDashArray||this.cornerDashArray),this.setCoords(),this.group&&(i=this.group.calcTransformMatrix()),this.forEachControl((function(o,s,a){n=a.oCoords[s],o.getVisibility(a,s)&&(i&&(n=r.util.transformPoint(n,i)),o.render(t,n.x,n.y,e,a))})),t.restore(),this},isControlVisible:function(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)},setControlVisible:function(t,e){return this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e,this},setControlsVisibility:function(t){for(var e in t||(t={}),t)this.setControlVisible(e,t[e]);return this},onDeselect:function(){},onSelect:function(){}})}(),r.util.object.extend(r.StaticCanvas.prototype,{FX_DURATION:500,fxCenterObjectH:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,o=e.onChange||i,s=this;return r.util.animate({target:this,startValue:t.left,endValue:this.getCenterPoint().x,duration:this.FX_DURATION,onChange:function(e){t.set("left",e),s.requestRenderAll(),o()},onComplete:function(){t.setCoords(),n()}})},fxCenterObjectV:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,o=e.onChange||i,s=this;return r.util.animate({target:this,startValue:t.top,endValue:this.getCenterPoint().y,duration:this.FX_DURATION,onChange:function(e){t.set("top",e),s.requestRenderAll(),o()},onComplete:function(){t.setCoords(),n()}})},fxRemove:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,o=e.onChange||i,s=this;return r.util.animate({target:this,startValue:t.opacity,endValue:0,duration:this.FX_DURATION,onChange:function(e){t.set("opacity",e),s.requestRenderAll(),o()},onComplete:function(){s.remove(t),n()}})}}),r.util.object.extend(r.Object.prototype,{animate:function(){if(arguments[0]&&"object"===typeof arguments[0]){var t,e,i=[],n=[];for(t in arguments[0])i.push(t);for(var r=0,o=i.length;r<o;r++)t=i[r],e=r!==o-1,n.push(this._animate(t,arguments[0][t],arguments[1],e));return n}return this._animate.apply(this,arguments)},_animate:function(t,e,i,n){var o,s=this;e=e.toString(),i=i?r.util.object.clone(i):{},~t.indexOf(".")&&(o=t.split("."));var a=s.colorProperties.indexOf(t)>-1||o&&s.colorProperties.indexOf(o[1])>-1,c=o?this.get(o[0])[o[1]]:this.get(t);"from"in i||(i.from=c),a||(e=~e.indexOf("=")?c+parseFloat(e.replace("=","")):parseFloat(e));var l={target:this,startValue:i.from,endValue:e,byValue:i.by,easing:i.easing,duration:i.duration,abort:i.abort&&function(t,e,n){return i.abort.call(s,t,e,n)},onChange:function(e,r,a){o?s[o[0]][o[1]]=e:s.set(t,e),n||i.onChange&&i.onChange(e,r,a)},onComplete:function(t,e,r){n||(s.setCoords(),i.onComplete&&i.onComplete(t,e,r))}};return a?r.util.animateColor(l.startValue,l.endValue,l.duration,l):r.util.animate(l)}}),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r={x1:1,x2:1,y1:1,y2:1};function o(t,e){var i=t.origin,n=t.axis1,r=t.axis2,o=t.dimension,s=e.nearest,a=e.center,c=e.farthest;return function(){switch(this.get(i)){case s:return Math.min(this.get(n),this.get(r));case a:return Math.min(this.get(n),this.get(r))+.5*this.get(o);case c:return Math.max(this.get(n),this.get(r))}}}e.Line?e.warn("fabric.Line is already defined"):(e.Line=e.util.createClass(e.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,cacheProperties:e.Object.prototype.cacheProperties.concat("x1","x2","y1","y2"),initialize:function(t,e){t||(t=[0,0,0,0]),this.callSuper("initialize",e),this.set("x1",t[0]),this.set("y1",t[1]),this.set("x2",t[2]),this.set("y2",t[3]),this._setWidthHeight(e)},_setWidthHeight:function(t){t||(t={}),this.width=Math.abs(this.x2-this.x1),this.height=Math.abs(this.y2-this.y1),this.left="left"in t?t.left:this._getLeftToOriginX(),this.top="top"in t?t.top:this._getTopToOriginY()},_set:function(t,e){return this.callSuper("_set",t,e),"undefined"!==typeof r[t]&&this._setWidthHeight(),this},_getLeftToOriginX:o({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:o({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(t){t.beginPath();var e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2),t.lineWidth=this.strokeWidth;var i=t.strokeStyle;t.strokeStyle=this.stroke||t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=i},_findCenterFromElement:function(){return{x:(this.x1+this.x2)/2,y:(this.y1+this.y2)/2}},toObject:function(t){return i(this.callSuper("toObject",t),this.calcLinePoints())},_getNonTransformedDimensions:function(){var t=this.callSuper("_getNonTransformedDimensions");return"butt"===this.strokeLineCap&&(0===this.width&&(t.y-=this.strokeWidth),0===this.height&&(t.x-=this.strokeWidth)),t},calcLinePoints:function(){var t=this.x1<=this.x2?-1:1,e=this.y1<=this.y2?-1:1,i=t*this.width*.5,n=e*this.height*.5;return{x1:i,x2:t*this.width*-.5,y1:n,y2:e*this.height*-.5}},_toSVG:function(){var t=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="',t.x1,'" y1="',t.y1,'" x2="',t.x2,'" y2="',t.y2,'" />\n']}}),e.Line.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x1 y1 x2 y2".split(" ")),e.Line.fromElement=function(t,n,r){r=r||{};var o=e.parseAttributes(t,e.Line.ATTRIBUTE_NAMES),s=[o.x1||0,o.y1||0,o.x2||0,o.y2||0];n(new e.Line(s,i(o,r)))},e.Line.fromObject=function(t,i){var r=n(t,!0);r.points=[t.x1,t.y1,t.x2,t.y2],e.Object._fromObject("Line",r,(function(t){delete t.points,i&&i(t)}),"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.degreesToRadians;e.Circle?e.warn("fabric.Circle is already defined."):(e.Circle=e.util.createClass(e.Object,{type:"circle",radius:0,startAngle:0,endAngle:360,cacheProperties:e.Object.prototype.cacheProperties.concat("radius","startAngle","endAngle"),_set:function(t,e){return this.callSuper("_set",t,e),"radius"===t&&this.setRadius(e),this},toObject:function(t){return this.callSuper("toObject",["radius","startAngle","endAngle"].concat(t))},_toSVG:function(){var t,n=(this.endAngle-this.startAngle)%360;if(0===n)t=["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',this.radius,'" />\n'];else{var r=i(this.startAngle),o=i(this.endAngle),s=this.radius;t=['<path d="M '+e.util.cos(r)*s+" "+e.util.sin(r)*s," A "+s+" "+s," 0 ",+(n>180?"1":"0")+" 1"," "+e.util.cos(o)*s+" "+e.util.sin(o)*s,'" ',"COMMON_PARTS"," />\n"]}return t},_render:function(t){t.beginPath(),t.arc(0,0,this.radius,i(this.startAngle),i(this.endAngle),!1),this._renderPaintInOrder(t)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(t){return this.radius=t,this.set("width",2*t).set("height",2*t)}}),e.Circle.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("cx cy r".split(" ")),e.Circle.fromElement=function(t,i){var n,r=e.parseAttributes(t,e.Circle.ATTRIBUTE_NAMES);if(!("radius"in(n=r)&&n.radius>=0))throw new Error("value of `r` attribute is required and can not be negative");r.left=(r.left||0)-r.radius,r.top=(r.top||0)-r.radius,i(new e.Circle(r))},e.Circle.fromObject=function(t,i){e.Object._fromObject("Circle",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Triangle?e.warn("fabric.Triangle is already defined"):(e.Triangle=e.util.createClass(e.Object,{type:"triangle",width:100,height:100,_render:function(t){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)},_toSVG:function(){var t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',[-t+" "+e,"0 "+-e,t+" "+e].join(","),'" />']}}),e.Triangle.fromObject=function(t,i){return e.Object._fromObject("Triangle",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=2*Math.PI;e.Ellipse?e.warn("fabric.Ellipse is already defined."):(e.Ellipse=e.util.createClass(e.Object,{type:"ellipse",rx:0,ry:0,cacheProperties:e.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(t){this.callSuper("initialize",t),this.set("rx",t&&t.rx||0),this.set("ry",t&&t.ry||0)},_set:function(t,e){switch(this.callSuper("_set",t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this},getRx:function(){return this.get("rx")*this.get("scaleX")},getRy:function(){return this.get("ry")*this.get("scaleY")},toObject:function(t){return this.callSuper("toObject",["rx","ry"].concat(t))},_toSVG:function(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" ','rx="',this.rx,'" ry="',this.ry,'" />\n']},_render:function(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,i,!1),t.restore(),this._renderPaintInOrder(t)}}),e.Ellipse.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("cx cy rx ry".split(" ")),e.Ellipse.fromElement=function(t,i){var n=e.parseAttributes(t,e.Ellipse.ATTRIBUTE_NAMES);n.left=(n.left||0)-n.rx,n.top=(n.top||0)-n.ry,i(new e.Ellipse(n))},e.Ellipse.fromObject=function(t,i){e.Object._fromObject("Ellipse",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend;e.Rect?e.warn("fabric.Rect is already defined"):(e.Rect=e.util.createClass(e.Object,{stateProperties:e.Object.prototype.stateProperties.concat("rx","ry"),type:"rect",rx:0,ry:0,cacheProperties:e.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(t){this.callSuper("initialize",t),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(t){var e=this.rx?Math.min(this.rx,this.width/2):0,i=this.ry?Math.min(this.ry,this.height/2):0,n=this.width,r=this.height,o=-this.width/2,s=-this.height/2,a=0!==e||0!==i,c=.4477152502;t.beginPath(),t.moveTo(o+e,s),t.lineTo(o+n-e,s),a&&t.bezierCurveTo(o+n-c*e,s,o+n,s+c*i,o+n,s+i),t.lineTo(o+n,s+r-i),a&&t.bezierCurveTo(o+n,s+r-c*i,o+n-c*e,s+r,o+n-e,s+r),t.lineTo(o+e,s+r),a&&t.bezierCurveTo(o+c*e,s+r,o,s+r-c*i,o,s+r-i),t.lineTo(o,s+i),a&&t.bezierCurveTo(o,s+c*i,o+c*e,s,o+e,s),t.closePath(),this._renderPaintInOrder(t)},toObject:function(t){return this.callSuper("toObject",["rx","ry"].concat(t))},_toSVG:function(){return["<rect ","COMMON_PARTS",'x="',-this.width/2,'" y="',-this.height/2,'" rx="',this.rx,'" ry="',this.ry,'" width="',this.width,'" height="',this.height,'" />\n']}}),e.Rect.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x y rx ry width height".split(" ")),e.Rect.fromElement=function(t,n,r){if(!t)return n(null);r=r||{};var o=e.parseAttributes(t,e.Rect.ATTRIBUTE_NAMES);o.left=o.left||0,o.top=o.top||0,o.height=o.height||0,o.width=o.width||0;var s=new e.Rect(i(r?e.util.object.clone(r):{},o));s.visible=s.visible&&s.width>0&&s.height>0,n(s)},e.Rect.fromObject=function(t,i){return e.Object._fromObject("Rect",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.array.min,r=e.util.array.max,o=e.util.toFixed,s=e.util.projectStrokeOnPoints;e.Polyline?e.warn("fabric.Polyline is already defined"):(e.Polyline=e.util.createClass(e.Object,{type:"polyline",points:null,exactBoundingBox:!1,cacheProperties:e.Object.prototype.cacheProperties.concat("points"),initialize:function(t,e){e=e||{},this.points=t||[],this.callSuper("initialize",e),this._setPositionDimensions(e)},_projectStrokeOnPoints:function(){return s(this.points,this,!0)},_setPositionDimensions:function(t){var e,i=this._calcDimensions(t),n=this.exactBoundingBox?this.strokeWidth:0;this.width=i.width-n,this.height=i.height-n,t.fromSVG||(e=this.translateToGivenOrigin({x:i.left-this.strokeWidth/2+n/2,y:i.top-this.strokeWidth/2+n/2},"left","top",this.originX,this.originY)),"undefined"===typeof t.left&&(this.left=t.fromSVG?i.left:e.x),"undefined"===typeof t.top&&(this.top=t.fromSVG?i.top:e.y),this.pathOffset={x:i.left+this.width/2+n/2,y:i.top+this.height/2+n/2}},_calcDimensions:function(){var t=this.exactBoundingBox?this._projectStrokeOnPoints():this.points,e=n(t,"x")||0,i=n(t,"y")||0;return{left:e,top:i,width:(r(t,"x")||0)-e,height:(r(t,"y")||0)-i}},toObject:function(t){return i(this.callSuper("toObject",t),{points:this.points.concat()})},_toSVG:function(){for(var t=[],i=this.pathOffset.x,n=this.pathOffset.y,r=e.Object.NUM_FRACTION_DIGITS,s=0,a=this.points.length;s<a;s++)t.push(o(this.points[s].x-i,r),",",o(this.points[s].y-n,r)," ");return["<"+this.type+" ","COMMON_PARTS",'points="',t.join(""),'" />\n']},commonRender:function(t){var e,i=this.points.length,n=this.pathOffset.x,r=this.pathOffset.y;if(!i||isNaN(this.points[i-1].y))return!1;t.beginPath(),t.moveTo(this.points[0].x-n,this.points[0].y-r);for(var o=0;o<i;o++)e=this.points[o],t.lineTo(e.x-n,e.y-r);return!0},_render:function(t){this.commonRender(t)&&this._renderPaintInOrder(t)},complexity:function(){return this.get("points").length}}),e.Polyline.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(),e.Polyline.fromElementGenerator=function(t){return function(n,r,o){if(!n)return r(null);o||(o={});var s=e.parsePointsAttribute(n.getAttribute("points")),a=e.parseAttributes(n,e[t].ATTRIBUTE_NAMES);a.fromSVG=!0,r(new e[t](s,i(a,o)))}},e.Polyline.fromElement=e.Polyline.fromElementGenerator("Polyline"),e.Polyline.fromObject=function(t,i){return e.Object._fromObject("Polyline",t,i,"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.projectStrokeOnPoints;e.Polygon?e.warn("fabric.Polygon is already defined"):(e.Polygon=e.util.createClass(e.Polyline,{type:"polygon",_projectStrokeOnPoints:function(){return i(this.points,this)},_render:function(t){this.commonRender(t)&&(t.closePath(),this._renderPaintInOrder(t))}}),e.Polygon.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(),e.Polygon.fromElement=e.Polyline.fromElementGenerator("Polygon"),e.Polygon.fromObject=function(t,i){e.Object._fromObject("Polygon",t,i,"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.array.min,n=e.util.array.max,r=e.util.object.extend,o=e.util.object.clone,s=e.util.toFixed;e.Path?e.warn("fabric.Path is already defined"):(e.Path=e.util.createClass(e.Object,{type:"path",path:null,cacheProperties:e.Object.prototype.cacheProperties.concat("path","fillRule"),stateProperties:e.Object.prototype.stateProperties.concat("path"),initialize:function(t,e){delete(e=o(e||{})).path,this.callSuper("initialize",e),this._setPath(t||[],e)},_setPath:function(t,i){this.path=e.util.makePathSimpler(Array.isArray(t)?t:e.util.parsePath(t)),e.Polyline.prototype._setPositionDimensions.call(this,i||{})},_renderPathCommands:function(t){var e,i=0,n=0,r=0,o=0,s=0,a=0,c=-this.pathOffset.x,l=-this.pathOffset.y;t.beginPath();for(var h=0,u=this.path.length;h<u;++h)switch((e=this.path[h])[0]){case"L":r=e[1],o=e[2],t.lineTo(r+c,o+l);break;case"M":i=r=e[1],n=o=e[2],t.moveTo(r+c,o+l);break;case"C":r=e[5],o=e[6],s=e[3],a=e[4],t.bezierCurveTo(e[1]+c,e[2]+l,s+c,a+l,r+c,o+l);break;case"Q":t.quadraticCurveTo(e[1]+c,e[2]+l,e[3]+c,e[4]+l),r=e[3],o=e[4],s=e[1],a=e[2];break;case"z":case"Z":r=i,o=n,t.closePath()}},_render:function(t){this._renderPathCommands(t),this._renderPaintInOrder(t)},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(t){return r(this.callSuper("toObject",t),{path:this.path.map((function(t){return t.slice()}))})},toDatalessObject:function(t){var e=this.toObject(["sourcePath"].concat(t));return e.sourcePath&&delete e.path,e},_toSVG:function(){return["<path ","COMMON_PARTS",'d="',e.util.joinPath(this.path),'" stroke-linecap="round" ',"/>\n"]},_getOffsetTransform:function(){var t=e.Object.NUM_FRACTION_DIGITS;return" translate("+s(-this.pathOffset.x,t)+", "+s(-this.pathOffset.y,t)+")"},toClipPathSVG:function(t){var e=this._getOffsetTransform();return"\t"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})},toSVG:function(t){var e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})},complexity:function(){return this.path.length},_calcDimensions:function(){for(var t,r,o=[],s=[],a=0,c=0,l=0,h=0,u=0,f=this.path.length;u<f;++u){switch((t=this.path[u])[0]){case"L":l=t[1],h=t[2],r=[];break;case"M":a=l=t[1],c=h=t[2],r=[];break;case"C":r=e.util.getBoundsOfCurve(l,h,t[1],t[2],t[3],t[4],t[5],t[6]),l=t[5],h=t[6];break;case"Q":r=e.util.getBoundsOfCurve(l,h,t[1],t[2],t[1],t[2],t[3],t[4]),l=t[3],h=t[4];break;case"z":case"Z":l=a,h=c}r.forEach((function(t){o.push(t.x),s.push(t.y)})),o.push(l),s.push(h)}var d=i(o)||0,g=i(s)||0;return{left:d,top:g,width:(n(o)||0)-d,height:(n(s)||0)-g}}}),e.Path.fromObject=function(t,i){if("string"===typeof t.sourcePath){var n=t.sourcePath;e.loadSVGFromURL(n,(function(n){var r=n[0];r.setOptions(t),t.clipPath?e.util.enlivenObjects([t.clipPath],(function(t){r.clipPath=t[0],i&&i(r)})):i&&i(r)}))}else e.Object._fromObject("Path",t,i,"path")},e.Path.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(["d"]),e.Path.fromElement=function(t,i,n){var o=e.parseAttributes(t,e.Path.ATTRIBUTE_NAMES);o.fromSVG=!0,i(new e.Path(o.d,r(o,n)))})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.array.min,n=e.util.array.max;e.Group||(e.Group=e.util.createClass(e.Object,e.Collection,{type:"group",strokeWidth:0,subTargetCheck:!1,cacheProperties:[],useSetOnGroup:!1,initialize:function(t,e,i){e=e||{},this._objects=[],i&&this.callSuper("initialize",e),this._objects=t||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;if(i)this._updateObjectsACoords();else{var r=e&&e.centerPoint;void 0!==e.originX&&(this.originX=e.originX),void 0!==e.originY&&(this.originY=e.originY),r||this._calcBounds(),this._updateObjectsCoords(r),delete e.centerPoint,this.callSuper("initialize",e)}this.setCoords()},_updateObjectsACoords:function(){for(var t=this._objects.length;t--;)this._objects[t].setCoords(true)},_updateObjectsCoords:function(t){t=t||this.getCenterPoint();for(var e=this._objects.length;e--;)this._updateObjectCoords(this._objects[e],t)},_updateObjectCoords:function(t,e){var i=t.left,n=t.top;t.set({left:i-e.x,top:n-e.y}),t.group=this,t.setCoords(!0)},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(t){var i=!!this.group;return this._restoreObjectsState(),e.util.resetObjectTransform(this),t&&(i&&e.util.removeTransformFromObject(t,this.group.calcTransformMatrix()),this._objects.push(t),t.group=this,t._set("canvas",this.canvas)),this._calcBounds(),this._updateObjectsCoords(),this.dirty=!0,i?this.group.addWithUpdate():this.setCoords(),this},removeWithUpdate:function(t){return this._restoreObjectsState(),e.util.resetObjectTransform(this),this.remove(t),this._calcBounds(),this._updateObjectsCoords(),this.setCoords(),this.dirty=!0,this},_onObjectAdded:function(t){this.dirty=!0,t.group=this,t._set("canvas",this.canvas)},_onObjectRemoved:function(t){this.dirty=!0,delete t.group},_set:function(t,i){var n=this._objects.length;if(this.useSetOnGroup)for(;n--;)this._objects[n].setOnGroup(t,i);if("canvas"===t)for(;n--;)this._objects[n]._set(t,i);e.Object.prototype._set.call(this,t,i)},toObject:function(t){var i=this.includeDefaultValues,n=this._objects.filter((function(t){return!t.excludeFromExport})).map((function(e){var n=e.includeDefaultValues;e.includeDefaultValues=i;var r=e.toObject(t);return e.includeDefaultValues=n,r})),r=e.Object.prototype.toObject.call(this,t);return r.objects=n,r},toDatalessObject:function(t){var i,n=this.sourcePath;if(n)i=n;else{var r=this.includeDefaultValues;i=this._objects.map((function(e){var i=e.includeDefaultValues;e.includeDefaultValues=r;var n=e.toDatalessObject(t);return e.includeDefaultValues=i,n}))}var o=e.Object.prototype.toDatalessObject.call(this,t);return o.objects=i,o},render:function(t){this._transformDone=!0,this.callSuper("render",t),this._transformDone=!1},shouldCache:function(){var t=e.Object.prototype.shouldCache.call(this);if(t)for(var i=0,n=this._objects.length;i<n;i++)if(this._objects[i].willDrawShadow())return this.ownCaching=!1,!1;return t},willDrawShadow:function(){if(e.Object.prototype.willDrawShadow.call(this))return!0;for(var t=0,i=this._objects.length;t<i;t++)if(this._objects[t].willDrawShadow())return!0;return!1},isOnACache:function(){return this.ownCaching||this.group&&this.group.isOnACache()},drawObject:function(t){for(var e=0,i=this._objects.length;e<i;e++)this._objects[e].render(t);this._drawClipPath(t,this.clipPath)},isCacheDirty:function(t){if(this.callSuper("isCacheDirty",t))return!0;if(!this.statefullCache)return!1;for(var e=0,i=this._objects.length;e<i;e++)if(this._objects[e].isCacheDirty(!0)){if(this._cacheCanvas){var n=this.cacheWidth/this.zoomX,r=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-n/2,-r/2,n,r)}return!0}return!1},_restoreObjectsState:function(){var t=this.calcOwnMatrix();return this._objects.forEach((function(i){e.util.addTransformToObject(i,t),delete i.group,i.setCoords()})),this},destroy:function(){return this._objects.forEach((function(t){t.set("dirty",!0)})),this._restoreObjectsState()},dispose:function(){this.callSuper("dispose"),this.forEachObject((function(t){t.dispose&&t.dispose()})),this._objects=[]},toActiveSelection:function(){if(this.canvas){var t=this._objects,i=this.canvas;this._objects=[];var n=this.toObject();delete n.objects;var r=new e.ActiveSelection([]);return r.set(n),r.type="activeSelection",i.remove(this),t.forEach((function(t){t.group=r,t.dirty=!0,i.add(t)})),r.canvas=i,r._objects=t,i._activeObject=r,r.setCoords(),r}},ungroupOnCanvas:function(){return this._restoreObjectsState()},setObjectsCoords:function(){return this.forEachObject((function(t){t.setCoords(true)})),this},_calcBounds:function(t){for(var e,i,n,r,o=[],s=[],a=["tr","br","bl","tl"],c=0,l=this._objects.length,h=a.length;c<l;++c){for(n=(e=this._objects[c]).calcACoords(),r=0;r<h;r++)i=a[r],o.push(n[i].x),s.push(n[i].y);e.aCoords=n}this._getBounds(o,s,t)},_getBounds:function(t,r,o){var s=new e.Point(i(t),i(r)),a=new e.Point(n(t),n(r)),c=s.y||0,l=s.x||0,h=a.x-s.x||0,u=a.y-s.y||0;this.width=h,this.height=u,o||this.setPositionByOrigin({x:l,y:c},"left","top")},_toSVG:function(t){for(var e=["<g ","COMMON_PARTS"," >\n"],i=0,n=this._objects.length;i<n;i++)e.push("\t\t",this._objects[i].toSVG(t));return e.push("</g>\n"),e},getSvgStyles:function(){var t="undefined"!==typeof this.opacity&&1!==this.opacity?"opacity: "+this.opacity+";":"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")},toClipPathSVG:function(t){for(var e=[],i=0,n=this._objects.length;i<n;i++)e.push("\t",this._objects[i].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}}),e.Group.fromObject=function(t,i){var n=t.objects,r=e.util.object.clone(t,!0);delete r.objects,"string"!==typeof n?e.util.enlivenObjects(n,(function(n){e.util.enlivenObjectEnlivables(t,r,(function(){i&&i(new e.Group(n,r,!0))}))})):e.loadSVGFromURL(n,(function(o){var s=e.util.groupSVGElements(o,t,n),a=r.clipPath;delete r.clipPath,s.set(r),a?e.util.enlivenObjects([a],(function(t){s.clipPath=t[0],i&&i(s)})):i&&i(s)}))})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.ActiveSelection||(e.ActiveSelection=e.util.createClass(e.Group,{type:"activeSelection",initialize:function(t,i){i=i||{},this._objects=t||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;i.originX&&(this.originX=i.originX),i.originY&&(this.originY=i.originY),this._calcBounds(),this._updateObjectsCoords(),e.Object.prototype.initialize.call(this,i),this.setCoords()},toGroup:function(){var t=this._objects.concat();this._objects=[];var i=e.Object.prototype.toObject.call(this),n=new e.Group([]);if(delete i.type,n.set(i),t.forEach((function(t){t.canvas.remove(t),t.group=n})),n._objects=t,!this.canvas)return n;var r=this.canvas;return r.add(n),r._activeObject=n,n.setCoords(),n},onDeselect:function(){return this.destroy(),!1},toString:function(){return"#<fabric.ActiveSelection: ("+this.complexity()+")>"},shouldCache:function(){return!1},isOnACache:function(){return!1},_renderControls:function(t,e,i){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,"undefined"===typeof(i=i||{}).hasControls&&(i.hasControls=!1),i.forActiveSelection=!0;for(var n=0,r=this._objects.length;n<r;n++)this._objects[n]._renderControls(t,i);this.callSuper("_renderControls",t,e),t.restore()}}),e.ActiveSelection.fromObject=function(t,i){e.util.enlivenObjects(t.objects,(function(n){delete t.objects,i&&i(new e.ActiveSelection(n,t,!0))}))})}(e),function(t){"use strict";var e=r.util.object.extend;t.fabric||(t.fabric={}),t.fabric.Image?r.warn("fabric.Image is already defined."):(r.Image=r.util.createClass(r.Object,{type:"image",strokeWidth:0,srcFromAttribute:!1,_lastScaleX:1,_lastScaleY:1,_filterScalingX:1,_filterScalingY:1,minimumScaleTrigger:.5,stateProperties:r.Object.prototype.stateProperties.concat("cropX","cropY"),cacheProperties:r.Object.prototype.cacheProperties.concat("cropX","cropY"),cacheKey:"",cropX:0,cropY:0,imageSmoothing:!0,initialize:function(t,e){e||(e={}),this.filters=[],this.cacheKey="texture"+r.Object.__uid++,this.callSuper("initialize",e),this._initElement(t,e)},getElement:function(){return this._element||{}},setElement:function(t,e){return this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._element=t,this._originalElement=t,this._initConfig(e),0!==this.filters.length&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters(),this},removeTexture:function(t){var e=r.filterBackend;e&&e.evictCachesForKey&&e.evictCachesForKey(t)},dispose:function(){this.callSuper("dispose"),this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._cacheContext=void 0,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(function(t){r.util.cleanUpJsdomNode(this[t]),this[t]=void 0}.bind(this))},getCrossOrigin:function(){return this._originalElement&&(this._originalElement.crossOrigin||null)},getOriginalSize:function(){var t=this.getElement();return{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}},_stroke:function(t){if(this.stroke&&0!==this.strokeWidth){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}},toObject:function(t){var i=[];this.filters.forEach((function(t){t&&i.push(t.toObject())}));var n=e(this.callSuper("toObject",["cropX","cropY"].concat(t)),{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:i});return this.resizeFilter&&(n.resizeFilter=this.resizeFilter.toObject()),n},hasCrop:function(){return this.cropX||this.cropY||this.width<this._element.width||this.height<this._element.height},_toSVG:function(){var t,e=[],i=[],n=this._element,o=-this.width/2,s=-this.height/2,a="",c="";if(!n)return[];if(this.hasCrop()){var l=r.Object.__uid++;e.push('<clipPath id="imageCrop_'+l+'">\n','\t<rect x="'+o+'" y="'+s+'" width="'+this.width+'" height="'+this.height+'" />\n',"</clipPath>\n"),a=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(c='" image-rendering="optimizeSpeed'),i.push("\t<image ","COMMON_PARTS",'xlink:href="',this.getSvgSrc(!0),'" x="',o-this.cropX,'" y="',s-this.cropY,'" width="',n.width||n.naturalWidth,'" height="',n.height||n.height,c,'"',a,"></image>\n"),this.stroke||this.strokeDashArray){var h=this.fill;this.fill=null,t=["\t<rect ",'x="',o,'" y="',s,'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),'"/>\n'],this.fill=h}return e="fill"!==this.paintFirst?e.concat(t,i):e.concat(i,t)},getSrc:function(t){var e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src"):e.src:this.src||""},setSrc:function(t,e,i){return r.util.loadImage(t,(function(t,n){this.setElement(t,i),this._setWidthHeight(),e&&e(this,n)}),this,i&&i.crossOrigin),this},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},applyResizeFilters:function(){var t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),n=i.scaleX,o=i.scaleY,s=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||n>e&&o>e)return this._element=s,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=n,void(this._lastScaleY=o);r.filterBackend||(r.filterBackend=r.initFilterBackend());var a=r.util.createCanvasElement(),c=this._filteredEl?this.cacheKey+"_filtered":this.cacheKey,l=s.width,h=s.height;a.width=l,a.height=h,this._element=a,this._lastScaleX=t.scaleX=n,this._lastScaleY=t.scaleY=o,r.filterBackend.applyFilters([t],s,l,h,this._element,c),this._filterScalingX=a.width/this._originalElement.width,this._filterScalingY=a.height/this._originalElement.height},applyFilters:function(t){if(t=(t=t||this.filters||[]).filter((function(t){return t&&!t.isNeutralState()})),this.set("dirty",!0),this.removeTexture(this.cacheKey+"_filtered"),0===t.length)return this._element=this._originalElement,this._filteredEl=null,this._filterScalingX=1,this._filterScalingY=1,this;var e=this._originalElement,i=e.naturalWidth||e.width,n=e.naturalHeight||e.height;if(this._element===this._originalElement){var o=r.util.createCanvasElement();o.width=i,o.height=n,this._element=o,this._filteredEl=o}else this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,n),this._lastScaleX=1,this._lastScaleY=1;return r.filterBackend||(r.filterBackend=r.initFilterBackend()),r.filterBackend.applyFilters(t,this._originalElement,i,n,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height),this},_render:function(t){r.util.setImageSmoothing(t,this.imageSmoothing),!0!==this.isMoving&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)},drawCacheOnCanvas:function(t){r.util.setImageSmoothing(t,this.imageSmoothing),r.Object.prototype.drawCacheOnCanvas.call(this,t)},shouldCache:function(){return this.needsItsOwnCache()},_renderFill:function(t){var e=this._element;if(e){var i=this._filterScalingX,n=this._filterScalingY,r=this.width,o=this.height,s=Math.min,a=Math.max,c=a(this.cropX,0),l=a(this.cropY,0),h=e.naturalWidth||e.width,u=e.naturalHeight||e.height,f=c*i,d=l*n,g=s(r*i,h-f),p=s(o*n,u-d),v=-r/2,m=-o/2,y=s(r,h/i-c),b=s(o,u/n-l);e&&t.drawImage(e,f,d,g,p,v,m,y,b)}},_needsResize:function(){var t=this.getTotalObjectScaling();return t.scaleX!==this._lastScaleX||t.scaleY!==this._lastScaleY},_resetWidthHeight:function(){this.set(this.getOriginalSize())},_initElement:function(t,e){this.setElement(r.util.getById(t),e),r.util.addClass(this.getElement(),r.Image.CSS_CANVAS)},_initConfig:function(t){t||(t={}),this.setOptions(t),this._setWidthHeight(t)},_initFilters:function(t,e){t&&t.length?r.util.enlivenObjects(t,(function(t){e&&e(t)}),"fabric.Image.filters"):e&&e()},_setWidthHeight:function(t){t||(t={});var e=this.getElement();this.width=t.width||e.naturalWidth||e.width||0,this.height=t.height||e.naturalHeight||e.height||0},parsePreserveAspectRatioAttribute:function(){var t,e=r.util.parsePreserveAspectRatioAttribute(this.preserveAspectRatio||""),i=this._element.width,n=this._element.height,o=1,s=1,a=0,c=0,l=0,h=0,u=this.width,f=this.height,d={width:u,height:f};return!e||"none"===e.alignX&&"none"===e.alignY?(o=u/i,s=f/n):("meet"===e.meetOrSlice&&(t=(u-i*(o=s=r.util.findScaleToFit(this._element,d)))/2,"Min"===e.alignX&&(a=-t),"Max"===e.alignX&&(a=t),t=(f-n*s)/2,"Min"===e.alignY&&(c=-t),"Max"===e.alignY&&(c=t)),"slice"===e.meetOrSlice&&(t=i-u/(o=s=r.util.findScaleToCover(this._element,d)),"Mid"===e.alignX&&(l=t/2),"Max"===e.alignX&&(l=t),t=n-f/s,"Mid"===e.alignY&&(h=t/2),"Max"===e.alignY&&(h=t),i=u/o,n=f/s)),{width:i,height:n,scaleX:o,scaleY:s,offsetLeft:a,offsetTop:c,cropX:l,cropY:h}}}),r.Image.CSS_CANVAS="canvas-img",r.Image.prototype.getSvgSrc=r.Image.prototype.getSrc,r.Image.fromObject=function(t,e){var i=r.util.object.clone(t);r.util.loadImage(i.src,(function(t,n){n?e&&e(null,!0):r.Image.prototype._initFilters.call(i,i.filters,(function(n){i.filters=n||[],r.Image.prototype._initFilters.call(i,[i.resizeFilter],(function(n){i.resizeFilter=n[0],r.util.enlivenObjectEnlivables(i,i,(function(){var n=new r.Image(t,i);e(n,!1)}))}))}))}),null,i.crossOrigin)},r.Image.fromURL=function(t,e,i){r.util.loadImage(t,(function(t,n){e&&e(new r.Image(t,i),n)}),null,i&&i.crossOrigin)},r.Image.ATTRIBUTE_NAMES=r.SHARED_ATTRIBUTES.concat("x y width height preserveAspectRatio xlink:href crossOrigin image-rendering".split(" ")),r.Image.fromElement=function(t,i,n){var o=r.parseAttributes(t,r.Image.ATTRIBUTE_NAMES);r.Image.fromURL(o["xlink:href"],i,e(n?r.util.object.clone(n):{},o))})}(e),r.util.object.extend(r.Object.prototype,{_getAngleValueForStraighten:function(){var t=this.angle%360;return t>0?90*Math.round((t-1)/90):90*Math.round(t/90)},straighten:function(){return this.rotate(this._getAngleValueForStraighten())},fxStraighten:function(t){var e=function(){},i=(t=t||{}).onComplete||e,n=t.onChange||e,o=this;return r.util.animate({target:this,startValue:this.get("angle"),endValue:this._getAngleValueForStraighten(),duration:this.FX_DURATION,onChange:function(t){o.rotate(t),n()},onComplete:function(){o.setCoords(),i()}})}}),r.util.object.extend(r.StaticCanvas.prototype,{straightenObject:function(t){return t.straighten(),this.requestRenderAll(),this},fxStraightenObject:function(t){return t.fxStraighten({onChange:this.requestRenderAllBound})}}),function(){"use strict";function t(t,e){var i="precision "+e+" float;\nvoid main(){}",n=t.createShader(t.FRAGMENT_SHADER);return t.shaderSource(n,i),t.compileShader(n),!!t.getShaderParameter(n,t.COMPILE_STATUS)}function e(t){t&&t.tileSize&&(this.tileSize=t.tileSize),this.setupGLContext(this.tileSize,this.tileSize),this.captureGPUInfo()}r.isWebglSupported=function(e){if(r.isLikelyNode)return!1;e=e||r.WebglFilterBackend.prototype.tileSize;var i=document.createElement("canvas"),n=i.getContext("webgl")||i.getContext("experimental-webgl"),o=!1;if(n){r.maxTextureSize=n.getParameter(n.MAX_TEXTURE_SIZE),o=r.maxTextureSize>=e;for(var s=["highp","mediump","lowp"],a=0;a<3;a++)if(t(n,s[a])){r.webGlPrecision=s[a];break}}return this.isSupported=o,o},r.WebglFilterBackend=e,e.prototype={tileSize:2048,resources:{},setupGLContext:function(t,e){this.dispose(),this.createWebGLCanvas(t,e),this.aPosition=new Float32Array([0,0,0,1,1,0,1,1]),this.chooseFastestCopyGLTo2DMethod(t,e)},chooseFastestCopyGLTo2DMethod:function(t,e){var i,n="undefined"!==typeof window.performance;try{new ImageData(1,1),i=!0}catch(g){i=!1}var o="undefined"!==typeof ArrayBuffer,c="undefined"!==typeof Uint8ClampedArray;if(n&&i&&o&&c){var l=r.util.createCanvasElement(),h=new ArrayBuffer(t*e*4);if(r.forceGLPutImageData)return this.imageBuffer=h,void(this.copyGLTo2D=a);var u,f,d={imageBuffer:h,destinationWidth:t,destinationHeight:e,targetCanvas:l};l.width=t,l.height=e,u=window.performance.now(),s.call(d,this.gl,d),f=window.performance.now()-u,u=window.performance.now(),a.call(d,this.gl,d),f>window.performance.now()-u?(this.imageBuffer=h,this.copyGLTo2D=a):this.copyGLTo2D=s}},createWebGLCanvas:function(t,e){var i=r.util.createCanvasElement();i.width=t,i.height=e;var n={alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1},o=i.getContext("webgl",n);o||(o=i.getContext("experimental-webgl",n)),o&&(o.clearColor(0,0,0,0),this.canvas=i,this.gl=o)},applyFilters:function(t,e,i,n,r,o){var s,a=this.gl;o&&(s=this.getCachedTexture(o,e));var c={originalWidth:e.width||e.originalWidth,originalHeight:e.height||e.originalHeight,sourceWidth:i,sourceHeight:n,destinationWidth:i,destinationHeight:n,context:a,sourceTexture:this.createTexture(a,i,n,!s&&e),targetTexture:this.createTexture(a,i,n),originalTexture:s||this.createTexture(a,i,n,!s&&e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:r},l=a.createFramebuffer();return a.bindFramebuffer(a.FRAMEBUFFER,l),t.forEach((function(t){t&&t.applyTo(c)})),function(t){var e=t.targetCanvas,i=e.width,n=e.height,r=t.destinationWidth,o=t.destinationHeight;i===r&&n===o||(e.width=r,e.height=o)}(c),this.copyGLTo2D(a,c),a.bindTexture(a.TEXTURE_2D,null),a.deleteTexture(c.sourceTexture),a.deleteTexture(c.targetTexture),a.deleteFramebuffer(l),r.getContext("2d").setTransform(1,0,0,1,0,0),c},dispose:function(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()},clearWebGLCaches:function(){this.programCache={},this.textureCache={}},createTexture:function(t,e,i,n,r){var o=t.createTexture();return t.bindTexture(t.TEXTURE_2D,o),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,r||t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,r||t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),n?t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,n):t.texImage2D(t.TEXTURE_2D,0,t.RGBA,e,i,0,t.RGBA,t.UNSIGNED_BYTE,null),o},getCachedTexture:function(t,e){if(this.textureCache[t])return this.textureCache[t];var i=this.createTexture(this.gl,e.width,e.height,e);return this.textureCache[t]=i,i},evictCachesForKey:function(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])},copyGLTo2D:s,captureGPUInfo:function(){if(this.gpuInfo)return this.gpuInfo;var t=this.gl,e={renderer:"",vendor:""};if(!t)return e;var i=t.getExtension("WEBGL_debug_renderer_info");if(i){var n=t.getParameter(i.UNMASKED_RENDERER_WEBGL),r=t.getParameter(i.UNMASKED_VENDOR_WEBGL);n&&(e.renderer=n.toLowerCase()),r&&(e.vendor=r.toLowerCase())}return this.gpuInfo=e,e}}}(),function(){"use strict";var t=function(){};function e(){}r.Canvas2dFilterBackend=e,e.prototype={evictCachesForKey:t,dispose:t,clearWebGLCaches:t,resources:{},applyFilters:function(t,e,i,n,r){var o=r.getContext("2d");o.drawImage(e,0,0,i,n);var s={sourceWidth:i,sourceHeight:n,imageData:o.getImageData(0,0,i,n),originalEl:e,originalImageData:o.getImageData(0,0,i,n),canvasEl:r,ctx:o,filterBackend:this};return t.forEach((function(t){t.applyTo(s)})),s.imageData.width===i&&s.imageData.height===n||(r.width=s.imageData.width,r.height=s.imageData.height),o.putImageData(s.imageData,0,0),s}}}(),r.Image=r.Image||{},r.Image.filters=r.Image.filters||{},r.Image.filters.BaseFilter=r.util.createClass({type:"BaseFilter",vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvoid main() {\nvTexCoord = aPosition;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:"precision highp float;\nvarying vec2 vTexCoord;\nuniform sampler2D uTexture;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\n}",initialize:function(t){t&&this.setOptions(t)},setOptions:function(t){for(var e in t)this[e]=t[e]},createProgram:function(t,e,i){e=e||this.fragmentSource,i=i||this.vertexSource,"highp"!==r.webGlPrecision&&(e=e.replace(/precision highp float/g,"precision "+r.webGlPrecision+" float"));var n=t.createShader(t.VERTEX_SHADER);if(t.shaderSource(n,i),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new Error("Vertex shader compile error for "+this.type+": "+t.getShaderInfoLog(n));var o=t.createShader(t.FRAGMENT_SHADER);if(t.shaderSource(o,e),t.compileShader(o),!t.getShaderParameter(o,t.COMPILE_STATUS))throw new Error("Fragment shader compile error for "+this.type+": "+t.getShaderInfoLog(o));var s=t.createProgram();if(t.attachShader(s,n),t.attachShader(s,o),t.linkProgram(s),!t.getProgramParameter(s,t.LINK_STATUS))throw new Error('Shader link error for "${this.type}" '+t.getProgramInfoLog(s));var a=this.getAttributeLocations(t,s),c=this.getUniformLocations(t,s)||{};return c.uStepW=t.getUniformLocation(s,"uStepW"),c.uStepH=t.getUniformLocation(s,"uStepH"),{program:s,attributeLocations:a,uniformLocations:c}},getAttributeLocations:function(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}},getUniformLocations:function(){return{}},sendAttributeData:function(t,e,i){var n=e.aPosition,r=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,r),t.enableVertexAttribArray(n),t.vertexAttribPointer(n,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)},_setupFrameBuffer:function(t){var e,i,n=t.context;t.passes>1?(e=t.destinationWidth,i=t.destinationHeight,t.sourceWidth===e&&t.sourceHeight===i||(n.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(n,e,i)),n.framebufferTexture2D(n.FRAMEBUFFER,n.COLOR_ATTACHMENT0,n.TEXTURE_2D,t.targetTexture,0)):(n.bindFramebuffer(n.FRAMEBUFFER,null),n.finish())},_swapTextures:function(t){t.passes--,t.pass++;var e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e},isNeutralState:function(){var t=this.mainParameter,e=r.Image.filters[this.type].prototype;if(t){if(Array.isArray(e[t])){for(var i=e[t].length;i--;)if(this[t][i]!==e[t][i])return!1;return!0}return e[t]===this[t]}return!1},applyTo:function(t){t.webgl?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},retrieveShader:function(t){return t.programCache.hasOwnProperty(this.type)||(t.programCache[this.type]=this.createProgram(t.context)),t.programCache[this.type]},applyToWebGL:function(t){var e=t.context,i=this.retrieveShader(t);0===t.pass&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)},bindAdditionalTexture:function(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)},unbindAdditionalTexture:function(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)},getMainParameter:function(){return this[this.mainParameter]},setMainParameter:function(t){this[this.mainParameter]=t},sendUniformData:function(){},createHelpLayer:function(t){if(!t.helpLayer){var e=document.createElement("canvas");e.width=t.sourceWidth,e.height=t.sourceHeight,t.helpLayer=e}},toObject:function(){var t={type:this.type},e=this.mainParameter;return e&&(t[e]=this[e]),t},toJSON:function(){return this.toObject()}}),r.Image.filters.BaseFilter.fromObject=function(t,e){var i=new r.Image.filters[t.type](t);return e&&e(i),i},function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.ColorMatrix=n(i.BaseFilter,{type:"ColorMatrix",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nuniform mat4 uColorMatrix;\nuniform vec4 uConstants;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor *= uColorMatrix;\ncolor += uConstants;\ngl_FragColor = color;\n}",matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0,initialize:function(t){this.callSuper("initialize",t),this.matrix=this.matrix.slice(0)},applyTo2d:function(t){var e,i,n,r,o,s=t.imageData.data,a=s.length,c=this.matrix,l=this.colorsOnly;for(o=0;o<a;o+=4)e=s[o],i=s[o+1],n=s[o+2],l?(s[o]=e*c[0]+i*c[1]+n*c[2]+255*c[4],s[o+1]=e*c[5]+i*c[6]+n*c[7]+255*c[9],s[o+2]=e*c[10]+i*c[11]+n*c[12]+255*c[14]):(r=s[o+3],s[o]=e*c[0]+i*c[1]+n*c[2]+r*c[3]+255*c[4],s[o+1]=e*c[5]+i*c[6]+n*c[7]+r*c[8]+255*c[9],s[o+2]=e*c[10]+i*c[11]+n*c[12]+r*c[13]+255*c[14],s[o+3]=e*c[15]+i*c[16]+n*c[17]+r*c[18]+255*c[19])},getUniformLocations:function(t,e){return{uColorMatrix:t.getUniformLocation(e,"uColorMatrix"),uConstants:t.getUniformLocation(e,"uConstants")}},sendUniformData:function(t,e){var i=this.matrix,n=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],r=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,n),t.uniform4fv(e.uConstants,r)}}),e.Image.filters.ColorMatrix.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Brightness=n(i.BaseFilter,{type:"Brightness",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBrightness;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += uBrightness;\ngl_FragColor = color;\n}",brightness:0,mainParameter:"brightness",applyTo2d:function(t){if(0!==this.brightness){var e,i=t.imageData.data,n=i.length,r=Math.round(255*this.brightness);for(e=0;e<n;e+=4)i[e]=i[e]+r,i[e+1]=i[e+1]+r,i[e+2]=i[e+2]+r}},getUniformLocations:function(t,e){return{uBrightness:t.getUniformLocation(e,"uBrightness")}},sendUniformData:function(t,e){t.uniform1f(e.uBrightness,this.brightness)}}),e.Image.filters.Brightness.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.Convolute=r(n.BaseFilter,{type:"Convolute",opaque:!1,matrix:[0,0,0,0,1,0,0,0,0],fragmentSource:{Convolute_3_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_3_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_5_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_5_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_7_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_7_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_9_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_9_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}"},retrieveShader:function(t){var e=Math.sqrt(this.matrix.length),i=this.type+"_"+e+"_"+(this.opaque?1:0),n=this.fragmentSource[i];return t.programCache.hasOwnProperty(i)||(t.programCache[i]=this.createProgram(t.context,n)),t.programCache[i]},applyTo2d:function(t){var e,i,n,r,o,s,a,c,l,h,u,f,d,g=t.imageData,p=g.data,v=this.matrix,m=Math.round(Math.sqrt(v.length)),y=Math.floor(m/2),b=g.width,x=g.height,_=t.ctx.createImageData(b,x),C=_.data,S=this.opaque?1:0;for(u=0;u<x;u++)for(h=0;h<b;h++){for(o=4*(u*b+h),e=0,i=0,n=0,r=0,d=0;d<m;d++)for(f=0;f<m;f++)s=h+f-y,(a=u+d-y)<0||a>=x||s<0||s>=b||(c=4*(a*b+s),l=v[d*m+f],e+=p[c]*l,i+=p[c+1]*l,n+=p[c+2]*l,S||(r+=p[c+3]*l));C[o]=e,C[o+1]=i,C[o+2]=n,C[o+3]=S?p[o+3]:r}t.imageData=_},getUniformLocations:function(t,e){return{uMatrix:t.getUniformLocation(e,"uMatrix"),uOpaque:t.getUniformLocation(e,"uOpaque"),uHalfSize:t.getUniformLocation(e,"uHalfSize"),uSize:t.getUniformLocation(e,"uSize")}},sendUniformData:function(t,e){t.uniform1fv(e.uMatrix,this.matrix)},toObject:function(){return i(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),e.Image.filters.Convolute.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Grayscale=n(i.BaseFilter,{type:"Grayscale",fragmentSource:{average:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat average = (color.r + color.b + color.g) / 3.0;\ngl_FragColor = vec4(average, average, average, color.a);\n}",lightness:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;\ngl_FragColor = vec4(average, average, average, col.a);\n}",luminosity:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;\ngl_FragColor = vec4(average, average, average, col.a);\n}"},mode:"average",mainParameter:"mode",applyTo2d:function(t){var e,i,n=t.imageData.data,r=n.length,o=this.mode;for(e=0;e<r;e+=4)"average"===o?i=(n[e]+n[e+1]+n[e+2])/3:"lightness"===o?i=(Math.min(n[e],n[e+1],n[e+2])+Math.max(n[e],n[e+1],n[e+2]))/2:"luminosity"===o&&(i=.21*n[e]+.72*n[e+1]+.07*n[e+2]),n[e]=i,n[e+1]=i,n[e+2]=i},retrieveShader:function(t){var e=this.type+"_"+this.mode;if(!t.programCache.hasOwnProperty(e)){var i=this.fragmentSource[this.mode];t.programCache[e]=this.createProgram(t.context,i)}return t.programCache[e]},getUniformLocations:function(t,e){return{uMode:t.getUniformLocation(e,"uMode")}},sendUniformData:function(t,e){t.uniform1i(e.uMode,1)},isNeutralState:function(){return!1}}),e.Image.filters.Grayscale.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Invert=n(i.BaseFilter,{type:"Invert",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uInvert;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nif (uInvert == 1) {\ngl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);\n} else {\ngl_FragColor = color;\n}\n}",invert:!0,mainParameter:"invert",applyTo2d:function(t){var e,i=t.imageData.data,n=i.length;for(e=0;e<n;e+=4)i[e]=255-i[e],i[e+1]=255-i[e+1],i[e+2]=255-i[e+2]},isNeutralState:function(){return!this.invert},getUniformLocations:function(t,e){return{uInvert:t.getUniformLocation(e,"uInvert")}},sendUniformData:function(t,e){t.uniform1i(e.uInvert,this.invert)}}),e.Image.filters.Invert.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.Noise=r(n.BaseFilter,{type:"Noise",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uStepH;\nuniform float uNoise;\nuniform float uSeed;\nvarying vec2 vTexCoord;\nfloat rand(vec2 co, float seed, float vScale) {\nreturn fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);\n}\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;\ngl_FragColor = color;\n}",mainParameter:"noise",noise:0,applyTo2d:function(t){if(0!==this.noise){var e,i,n=t.imageData.data,r=n.length,o=this.noise;for(e=0,r=n.length;e<r;e+=4)i=(.5-Math.random())*o,n[e]+=i,n[e+1]+=i,n[e+2]+=i}},getUniformLocations:function(t,e){return{uNoise:t.getUniformLocation(e,"uNoise"),uSeed:t.getUniformLocation(e,"uSeed")}},sendUniformData:function(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())},toObject:function(){return i(this.callSuper("toObject"),{noise:this.noise})}}),e.Image.filters.Noise.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Pixelate=n(i.BaseFilter,{type:"Pixelate",blocksize:4,mainParameter:"blocksize",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBlocksize;\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nfloat blockW = uBlocksize * uStepW;\nfloat blockH = uBlocksize * uStepW;\nint posX = int(vTexCoord.x / blockW);\nint posY = int(vTexCoord.y / blockH);\nfloat fposX = float(posX);\nfloat fposY = float(posY);\nvec2 squareCoords = vec2(fposX * blockW, fposY * blockH);\nvec4 color = texture2D(uTexture, squareCoords);\ngl_FragColor = color;\n}",applyTo2d:function(t){var e,i,n,r,o,s,a,c,l,h,u,f=t.imageData,d=f.data,g=f.height,p=f.width;for(i=0;i<g;i+=this.blocksize)for(n=0;n<p;n+=this.blocksize)for(r=d[e=4*i*p+4*n],o=d[e+1],s=d[e+2],a=d[e+3],h=Math.min(i+this.blocksize,g),u=Math.min(n+this.blocksize,p),c=i;c<h;c++)for(l=n;l<u;l++)d[e=4*c*p+4*l]=r,d[e+1]=o,d[e+2]=s,d[e+3]=a},isNeutralState:function(){return 1===this.blocksize},getUniformLocations:function(t,e){return{uBlocksize:t.getUniformLocation(e,"uBlocksize"),uStepW:t.getUniformLocation(e,"uStepW"),uStepH:t.getUniformLocation(e,"uStepH")}},sendUniformData:function(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}),e.Image.filters.Pixelate.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.RemoveColor=r(n.BaseFilter,{type:"RemoveColor",color:"#FFFFFF",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uLow;\nuniform vec4 uHigh;\nvarying vec2 vTexCoord;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\nif(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {\ngl_FragColor.a = 0.0;\n}\n}",distance:.02,useAlpha:!1,applyTo2d:function(t){var i,n,r,o,s=t.imageData.data,a=255*this.distance,c=new e.Color(this.color).getSource(),l=[c[0]-a,c[1]-a,c[2]-a],h=[c[0]+a,c[1]+a,c[2]+a];for(i=0;i<s.length;i+=4)n=s[i],r=s[i+1],o=s[i+2],n>l[0]&&r>l[1]&&o>l[2]&&n<h[0]&&r<h[1]&&o<h[2]&&(s[i+3]=0)},getUniformLocations:function(t,e){return{uLow:t.getUniformLocation(e,"uLow"),uHigh:t.getUniformLocation(e,"uHigh")}},sendUniformData:function(t,i){var n=new e.Color(this.color).getSource(),r=parseFloat(this.distance),o=[0+n[0]/255-r,0+n[1]/255-r,0+n[2]/255-r,1],s=[n[0]/255+r,n[1]/255+r,n[2]/255+r,1];t.uniform4fv(i.uLow,o),t.uniform4fv(i.uHigh,s)},toObject:function(){return i(this.callSuper("toObject"),{color:this.color,distance:this.distance})}}),e.Image.filters.RemoveColor.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass,r={Brownie:[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0],Vintage:[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0],Kodachrome:[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0],Technicolor:[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0],Polaroid:[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],Sepia:[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0],BlackWhite:[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]};for(var o in r)i[o]=n(i.ColorMatrix,{type:o,matrix:r[o],mainParameter:!1,colorsOnly:!0}),e.Image.filters[o].fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric,i=e.Image.filters,n=e.util.createClass;i.BlendColor=n(i.BaseFilter,{type:"BlendColor",color:"#F95C63",mode:"multiply",alpha:1,fragmentSource:{multiply:"gl_FragColor.rgb *= uColor.rgb;\n",screen:"gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);\n",add:"gl_FragColor.rgb += uColor.rgb;\n",diff:"gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);\n",subtract:"gl_FragColor.rgb -= uColor.rgb;\n",lighten:"gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);\n",darken:"gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);\n",exclusion:"gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);\n",overlay:"if (uColor.r < 0.5) {\ngl_FragColor.r *= 2.0 * uColor.r;\n} else {\ngl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);\n}\nif (uColor.g < 0.5) {\ngl_FragColor.g *= 2.0 * uColor.g;\n} else {\ngl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);\n}\nif (uColor.b < 0.5) {\ngl_FragColor.b *= 2.0 * uColor.b;\n} else {\ngl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);\n}\n",tint:"gl_FragColor.rgb *= (1.0 - uColor.a);\ngl_FragColor.rgb += uColor.rgb;\n"},buildSource:function(t){return"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ngl_FragColor = color;\nif (color.a > 0.0) {\n"+this.fragmentSource[t]+"}\n}"},retrieveShader:function(t){var e,i=this.type+"_"+this.mode;return t.programCache.hasOwnProperty(i)||(e=this.buildSource(this.mode),t.programCache[i]=this.createProgram(t.context,e)),t.programCache[i]},applyTo2d:function(t){var i,n,r,o,s,a,c,l=t.imageData.data,h=l.length,u=1-this.alpha;i=(c=new e.Color(this.color).getSource())[0]*this.alpha,n=c[1]*this.alpha,r=c[2]*this.alpha;for(var f=0;f<h;f+=4)switch(o=l[f],s=l[f+1],a=l[f+2],this.mode){case"multiply":l[f]=o*i/255,l[f+1]=s*n/255,l[f+2]=a*r/255;break;case"screen":l[f]=255-(255-o)*(255-i)/255,l[f+1]=255-(255-s)*(255-n)/255,l[f+2]=255-(255-a)*(255-r)/255;break;case"add":l[f]=o+i,l[f+1]=s+n,l[f+2]=a+r;break;case"diff":case"difference":l[f]=Math.abs(o-i),l[f+1]=Math.abs(s-n),l[f+2]=Math.abs(a-r);break;case"subtract":l[f]=o-i,l[f+1]=s-n,l[f+2]=a-r;break;case"darken":l[f]=Math.min(o,i),l[f+1]=Math.min(s,n),l[f+2]=Math.min(a,r);break;case"lighten":l[f]=Math.max(o,i),l[f+1]=Math.max(s,n),l[f+2]=Math.max(a,r);break;case"overlay":l[f]=i<128?2*o*i/255:255-2*(255-o)*(255-i)/255,l[f+1]=n<128?2*s*n/255:255-2*(255-s)*(255-n)/255,l[f+2]=r<128?2*a*r/255:255-2*(255-a)*(255-r)/255;break;case"exclusion":l[f]=i+o-2*i*o/255,l[f+1]=n+s-2*n*s/255,l[f+2]=r+a-2*r*a/255;break;case"tint":l[f]=i+o*u,l[f+1]=n+s*u,l[f+2]=r+a*u}},getUniformLocations:function(t,e){return{uColor:t.getUniformLocation(e,"uColor")}},sendUniformData:function(t,i){var n=new e.Color(this.color).getSource();n[0]=this.alpha*n[0]/255,n[1]=this.alpha*n[1]/255,n[2]=this.alpha*n[2]/255,n[3]=this.alpha,t.uniform4fv(i.uColor,n)},toObject:function(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendColor.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric,i=e.Image.filters,n=e.util.createClass;i.BlendImage=n(i.BaseFilter,{type:"BlendImage",image:null,mode:"multiply",alpha:1,vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nuniform mat3 uTransformMatrix;\nvoid main() {\nvTexCoord = aPosition;\nvTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:{multiply:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.rgba *= color2.rgba;\ngl_FragColor = color;\n}",mask:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.a = color2.a;\ngl_FragColor = color;\n}"},retrieveShader:function(t){var e=this.type+"_"+this.mode,i=this.fragmentSource[this.mode];return t.programCache.hasOwnProperty(e)||(t.programCache[e]=this.createProgram(t.context,i)),t.programCache[e]},applyToWebGL:function(t){var e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),this.callSuper("applyToWebGL",t),this.unbindAdditionalTexture(e,e.TEXTURE1)},createTexture:function(t,e){return t.getCachedTexture(e.cacheKey,e._element)},calculateMatrix:function(){var t=this.image,e=t._element.width,i=t._element.height;return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]},applyTo2d:function(t){var i,n,r,o,s,a,c,l,h,u,f,d=t.imageData,g=t.filterBackend.resources,p=d.data,v=p.length,m=d.width,y=d.height,b=this.image;g.blendImage||(g.blendImage=e.util.createCanvasElement()),u=(h=g.blendImage).getContext("2d"),h.width!==m||h.height!==y?(h.width=m,h.height=y):u.clearRect(0,0,m,y),u.setTransform(b.scaleX,0,0,b.scaleY,b.left,b.top),u.drawImage(b._element,0,0,m,y),f=u.getImageData(0,0,m,y).data;for(var x=0;x<v;x+=4)switch(s=p[x],a=p[x+1],c=p[x+2],l=p[x+3],i=f[x],n=f[x+1],r=f[x+2],o=f[x+3],this.mode){case"multiply":p[x]=s*i/255,p[x+1]=a*n/255,p[x+2]=c*r/255,p[x+3]=l*o/255;break;case"mask":p[x+3]=o}},getUniformLocations:function(t,e){return{uTransformMatrix:t.getUniformLocation(e,"uTransformMatrix"),uImage:t.getUniformLocation(e,"uImage")}},sendUniformData:function(t,e){var i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)},toObject:function(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendImage.fromObject=function(t,i){e.Image.fromObject(t.image,(function(n){var r=e.util.object.clone(t);r.image=n,i(new e.Image.filters.BlendImage(r))}))}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=Math.pow,n=Math.floor,r=Math.sqrt,o=Math.abs,s=Math.round,a=Math.sin,c=Math.ceil,l=e.Image.filters,h=e.util.createClass;l.Resize=h(l.BaseFilter,{type:"Resize",resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,getUniformLocations:function(t,e){return{uDelta:t.getUniformLocation(e,"uDelta"),uTaps:t.getUniformLocation(e,"uTaps")}},sendUniformData:function(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)},retrieveShader:function(t){var e=this.getFilterWindow(),i=this.type+"_"+e;if(!t.programCache.hasOwnProperty(i)){var n=this.generateShader(e);t.programCache[i]=this.createProgram(t.context,n)}return t.programCache[i]},getFilterWindow:function(){var t=this.tempScale;return Math.ceil(this.lanczosLobes/t)},getTaps:function(){for(var t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),n=new Array(i),r=1;r<=i;r++)n[r-1]=t(r*e);return n},generateShader:function(t){for(var e=new Array(t),i=this.fragmentSourceTOP,n=1;n<=t;n++)e[n-1]=n+".0 * uDelta";return i+="uniform float uTaps["+t+"];\n",i+="void main() {\n",i+="  vec4 color = texture2D(uTexture, vTexCoord);\n",i+="  float sum = 1.0;\n",e.forEach((function(t,e){i+="  color += texture2D(uTexture, vTexCoord + "+t+") * uTaps["+e+"];\n",i+="  color += texture2D(uTexture, vTexCoord - "+t+") * uTaps["+e+"];\n",i+="  sum += 2.0 * uTaps["+e+"];\n"})),i+="  gl_FragColor = color / sum;\n",i+="}"},fragmentSourceTOP:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\n",applyTo:function(t){t.webgl?(t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceHeight=t.destinationHeight):this.applyTo2d(t)},isNeutralState:function(){return 1===this.scaleX&&1===this.scaleY},lanczosCreate:function(t){return function(e){if(e>=t||e<=-t)return 0;if(e<1.1920929e-7&&e>-1.1920929e-7)return 1;var i=(e*=Math.PI)/t;return a(e)/e*a(i)/i}},applyTo2d:function(t){var e=t.imageData,i=this.scaleX,n=this.scaleY;this.rcpScaleX=1/i,this.rcpScaleY=1/n;var r,o=e.width,a=e.height,c=s(o*i),l=s(a*n);"sliceHack"===this.resizeType?r=this.sliceByTwo(t,o,a,c,l):"hermite"===this.resizeType?r=this.hermiteFastResize(t,o,a,c,l):"bilinear"===this.resizeType?r=this.bilinearFiltering(t,o,a,c,l):"lanczos"===this.resizeType&&(r=this.lanczosResize(t,o,a,c,l)),t.imageData=r},sliceByTwo:function(t,i,r,o,s){var a,c,l=t.imageData,h=.5,u=!1,f=!1,d=i*h,g=r*h,p=e.filterBackend.resources,v=0,m=0,y=i,b=0;for(p.sliceByTwo||(p.sliceByTwo=document.createElement("canvas")),((a=p.sliceByTwo).width<1.5*i||a.height<r)&&(a.width=1.5*i,a.height=r),(c=a.getContext("2d")).clearRect(0,0,1.5*i,r),c.putImageData(l,0,0),o=n(o),s=n(s);!u||!f;)i=d,r=g,o<n(d*h)?d=n(d*h):(d=o,u=!0),s<n(g*h)?g=n(g*h):(g=s,f=!0),c.drawImage(a,v,m,i,r,y,b,d,g),v=y,m=b,b+=g;return c.getImageData(v,m,o,s)},lanczosResize:function(t,e,s,a,l){var h=t.imageData.data,u=t.ctx.createImageData(a,l),f=u.data,d=this.lanczosCreate(this.lanczosLobes),g=this.rcpScaleX,p=this.rcpScaleY,v=2/this.rcpScaleX,m=2/this.rcpScaleY,y=c(g*this.lanczosLobes/2),b=c(p*this.lanczosLobes/2),x={},_={},C={};return function t(c){var S,w,T,O,E,k,M,A,D,P,F;for(_.x=(c+.5)*g,C.x=n(_.x),S=0;S<l;S++){for(_.y=(S+.5)*p,C.y=n(_.y),E=0,k=0,M=0,A=0,D=0,w=C.x-y;w<=C.x+y;w++)if(!(w<0||w>=e)){P=n(1e3*o(w-_.x)),x[P]||(x[P]={});for(var j=C.y-b;j<=C.y+b;j++)j<0||j>=s||(F=n(1e3*o(j-_.y)),x[P][F]||(x[P][F]=d(r(i(P*v,2)+i(F*m,2))/1e3)),(T=x[P][F])>0&&(E+=T,k+=T*h[O=4*(j*e+w)],M+=T*h[O+1],A+=T*h[O+2],D+=T*h[O+3]))}f[O=4*(S*a+c)]=k/E,f[O+1]=M/E,f[O+2]=A/E,f[O+3]=D/E}return++c<a?t(c):u}(0)},bilinearFiltering:function(t,e,i,r,o){var s,a,c,l,h,u,f,d,g,p=0,v=this.rcpScaleX,m=this.rcpScaleY,y=4*(e-1),b=t.imageData.data,x=t.ctx.createImageData(r,o),_=x.data;for(c=0;c<o;c++)for(l=0;l<r;l++)for(h=v*l-(s=n(v*l)),u=m*c-(a=n(m*c)),g=4*(a*e+s),f=0;f<4;f++)d=b[g+f]*(1-h)*(1-u)+b[g+4+f]*h*(1-u)+b[g+y+f]*u*(1-h)+b[g+y+4+f]*h*u,_[p++]=d;return x},hermiteFastResize:function(t,e,i,s,a){for(var l=this.rcpScaleX,h=this.rcpScaleY,u=c(l/2),f=c(h/2),d=t.imageData.data,g=t.ctx.createImageData(s,a),p=g.data,v=0;v<a;v++)for(var m=0;m<s;m++){for(var y=4*(m+v*s),b=0,x=0,_=0,C=0,S=0,w=0,T=0,O=(v+.5)*h,E=n(v*h);E<(v+1)*h;E++)for(var k=o(O-(E+.5))/f,M=(m+.5)*l,A=k*k,D=n(m*l);D<(m+1)*l;D++){var P=o(M-(D+.5))/u,F=r(A+P*P);F>1&&F<-1||(b=2*F*F*F-3*F*F+1)>0&&(T+=b*d[(P=4*(D+E*e))+3],_+=b,d[P+3]<255&&(b=b*d[P+3]/250),C+=b*d[P],S+=b*d[P+1],w+=b*d[P+2],x+=b)}p[y]=C/x,p[y+1]=S/x,p[y+2]=w/x,p[y+3]=T/_}return g},toObject:function(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}),e.Image.filters.Resize.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Contrast=n(i.BaseFilter,{type:"Contrast",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uContrast;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));\ncolor.rgb = contrastF * (color.rgb - 0.5) + 0.5;\ngl_FragColor = color;\n}",contrast:0,mainParameter:"contrast",applyTo2d:function(t){if(0!==this.contrast){var e,i=t.imageData.data,n=i.length,r=Math.floor(255*this.contrast),o=259*(r+255)/(255*(259-r));for(e=0;e<n;e+=4)i[e]=o*(i[e]-128)+128,i[e+1]=o*(i[e+1]-128)+128,i[e+2]=o*(i[e+2]-128)+128}},getUniformLocations:function(t,e){return{uContrast:t.getUniformLocation(e,"uContrast")}},sendUniformData:function(t,e){t.uniform1f(e.uContrast,this.contrast)}}),e.Image.filters.Contrast.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Saturation=n(i.BaseFilter,{type:"Saturation",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uSaturation;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat rgMax = max(color.r, color.g);\nfloat rgbMax = max(rgMax, color.b);\ncolor.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;\ncolor.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;\ncolor.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;\ngl_FragColor = color;\n}",saturation:0,mainParameter:"saturation",applyTo2d:function(t){if(0!==this.saturation){var e,i,n=t.imageData.data,r=n.length,o=-this.saturation;for(e=0;e<r;e+=4)i=Math.max(n[e],n[e+1],n[e+2]),n[e]+=i!==n[e]?(i-n[e])*o:0,n[e+1]+=i!==n[e+1]?(i-n[e+1])*o:0,n[e+2]+=i!==n[e+2]?(i-n[e+2])*o:0}},getUniformLocations:function(t,e){return{uSaturation:t.getUniformLocation(e,"uSaturation")}},sendUniformData:function(t,e){t.uniform1f(e.uSaturation,-this.saturation)}}),e.Image.filters.Saturation.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Vibrance=n(i.BaseFilter,{type:"Vibrance",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uVibrance;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat max = max(color.r, max(color.g, color.b));\nfloat avg = (color.r + color.g + color.b) / 3.0;\nfloat amt = (abs(max - avg) * 2.0) * uVibrance;\ncolor.r += max != color.r ? (max - color.r) * amt : 0.00;\ncolor.g += max != color.g ? (max - color.g) * amt : 0.00;\ncolor.b += max != color.b ? (max - color.b) * amt : 0.00;\ngl_FragColor = color;\n}",vibrance:0,mainParameter:"vibrance",applyTo2d:function(t){if(0!==this.vibrance){var e,i,n,r,o=t.imageData.data,s=o.length,a=-this.vibrance;for(e=0;e<s;e+=4)i=Math.max(o[e],o[e+1],o[e+2]),n=(o[e]+o[e+1]+o[e+2])/3,r=2*Math.abs(i-n)/255*a,o[e]+=i!==o[e]?(i-o[e])*r:0,o[e+1]+=i!==o[e+1]?(i-o[e+1])*r:0,o[e+2]+=i!==o[e+2]?(i-o[e+2])*r:0}},getUniformLocations:function(t,e){return{uVibrance:t.getUniformLocation(e,"uVibrance")}},sendUniformData:function(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}}),e.Image.filters.Vibrance.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Blur=n(i.BaseFilter,{type:"Blur",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\nconst float nSamples = 15.0;\nvec3 v3offset = vec3(12.9898, 78.233, 151.7182);\nfloat random(vec3 scale) {\nreturn fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);\n}\nvoid main() {\nvec4 color = vec4(0.0);\nfloat total = 0.0;\nfloat offset = random(v3offset);\nfor (float t = -nSamples; t <= nSamples; t++) {\nfloat percent = (t + offset - 0.5) / nSamples;\nfloat weight = 1.0 - abs(percent);\ncolor += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;\ntotal += weight;\n}\ngl_FragColor = color / total;\n}",blur:0,mainParameter:"blur",applyTo:function(t){t.webgl?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},applyTo2d:function(t){t.imageData=this.simpleBlur(t)},simpleBlur:function(t){var i,n,r=t.filterBackend.resources,o=t.imageData.width,s=t.imageData.height;r.blurLayer1||(r.blurLayer1=e.util.createCanvasElement(),r.blurLayer2=e.util.createCanvasElement()),i=r.blurLayer1,n=r.blurLayer2,i.width===o&&i.height===s||(n.width=i.width=o,n.height=i.height=s);var a,c,l,h,u=i.getContext("2d"),f=n.getContext("2d"),d=15,g=.06*this.blur*.5;for(u.putImageData(t.imageData,0,0),f.clearRect(0,0,o,s),h=-15;h<=d;h++)l=g*(c=h/d)*o+(a=(Math.random()-.5)/4),f.globalAlpha=1-Math.abs(c),f.drawImage(i,l,a),u.drawImage(n,0,0),f.globalAlpha=1,f.clearRect(0,0,n.width,n.height);for(h=-15;h<=d;h++)l=g*(c=h/d)*s+(a=(Math.random()-.5)/4),f.globalAlpha=1-Math.abs(c),f.drawImage(i,a,l),u.drawImage(n,0,0),f.globalAlpha=1,f.clearRect(0,0,n.width,n.height);t.ctx.drawImage(i,0,0);var p=t.ctx.getImageData(0,0,i.width,i.height);return u.globalAlpha=1,u.clearRect(0,0,i.width,i.height),p},getUniformLocations:function(t,e){return{delta:t.getUniformLocation(e,"uDelta")}},sendUniformData:function(t,e){var i=this.chooseRightDelta();t.uniform2fv(e.delta,i)},chooseRightDelta:function(){var t,e=1,i=[0,0];return this.horizontal?this.aspectRatio>1&&(e=1/this.aspectRatio):this.aspectRatio<1&&(e=this.aspectRatio),t=e*this.blur*.12,this.horizontal?i[0]=t:i[1]=t,i}}),i.Blur.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Gamma=n(i.BaseFilter,{type:"Gamma",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec3 uGamma;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec3 correction = (1.0 / uGamma);\ncolor.r = pow(color.r, correction.r);\ncolor.g = pow(color.g, correction.g);\ncolor.b = pow(color.b, correction.b);\ngl_FragColor = color;\ngl_FragColor.rgb *= color.a;\n}",gamma:[1,1,1],mainParameter:"gamma",initialize:function(t){this.gamma=[1,1,1],i.BaseFilter.prototype.initialize.call(this,t)},applyTo2d:function(t){var e,i=t.imageData.data,n=this.gamma,r=i.length,o=1/n[0],s=1/n[1],a=1/n[2];for(this.rVals||(this.rVals=new Uint8Array(256),this.gVals=new Uint8Array(256),this.bVals=new Uint8Array(256)),e=0,r=256;e<r;e++)this.rVals[e]=255*Math.pow(e/255,o),this.gVals[e]=255*Math.pow(e/255,s),this.bVals[e]=255*Math.pow(e/255,a);for(e=0,r=i.length;e<r;e+=4)i[e]=this.rVals[i[e]],i[e+1]=this.gVals[i[e+1]],i[e+2]=this.bVals[i[e+2]]},getUniformLocations:function(t,e){return{uGamma:t.getUniformLocation(e,"uGamma")}},sendUniformData:function(t,e){t.uniform3fv(e.uGamma,this.gamma)}}),e.Image.filters.Gamma.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Composed=n(i.BaseFilter,{type:"Composed",subFilters:[],initialize:function(t){this.callSuper("initialize",t),this.subFilters=this.subFilters.slice(0)},applyTo:function(t){t.passes+=this.subFilters.length-1,this.subFilters.forEach((function(e){e.applyTo(t)}))},toObject:function(){return e.util.object.extend(this.callSuper("toObject"),{subFilters:this.subFilters.map((function(t){return t.toObject()}))})},isNeutralState:function(){return!this.subFilters.some((function(t){return!t.isNeutralState()}))}}),e.Image.filters.Composed.fromObject=function(t,i){var n=(t.subFilters||[]).map((function(t){return new e.Image.filters[t.type](t)})),r=new e.Image.filters.Composed({subFilters:n});return i&&i(r),r}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.HueRotation=n(i.ColorMatrix,{type:"HueRotation",rotation:0,mainParameter:"rotation",calculateMatrix:function(){var t=this.rotation*Math.PI,i=e.util.cos(t),n=e.util.sin(t),r=1/3,o=Math.sqrt(r)*n,s=1-i;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=i+s/3,this.matrix[1]=r*s-o,this.matrix[2]=r*s+o,this.matrix[5]=r*s+o,this.matrix[6]=i+r*s,this.matrix[7]=r*s-o,this.matrix[10]=r*s-o,this.matrix[11]=r*s+o,this.matrix[12]=i+r*s},isNeutralState:function(t){return this.calculateMatrix(),i.BaseFilter.prototype.isNeutralState.call(this,t)},applyTo:function(t){this.calculateMatrix(),i.BaseFilter.prototype.applyTo.call(this,t)}}),e.Image.filters.HueRotation.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.clone;if(e.Text)e.warn("fabric.Text is already defined");else{var n="fontFamily fontWeight fontSize text underline overline linethrough textAlign fontStyle lineHeight textBackgroundColor charSpacing styles direction path pathStartOffset pathSide pathAlign".split(" ");e.Text=e.util.createClass(e.Object,{_dimensionAffectingProps:["fontSize","fontWeight","fontFamily","fontStyle","lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],_reNewline:/\r?\n/,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:"left",fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stateProperties:e.Object.prototype.stateProperties.concat(n),cacheProperties:e.Object.prototype.cacheProperties.concat(n),stroke:null,shadow:null,path:null,pathStartOffset:0,pathSide:"left",pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,styles:null,_measuringContext:null,deltaY:0,direction:"ltr",_styleProperties:["stroke","strokeWidth","fill","fontFamily","fontSize","fontWeight","fontStyle","underline","overline","linethrough","deltaY","textBackgroundColor"],__charBounds:[],CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,initialize:function(t,e){this.styles=e&&e.styles||{},this.text=t,this.__skipDimension=!0,this.callSuper("initialize",e),this.path&&this.setPathInfo(),this.__skipDimension=!1,this.initDimensions(),this.setCoords(),this.setupState({propertySet:"_dimensionAffectingProps"})},setPathInfo:function(){var t=this.path;t&&(t.segmentsInfo=e.util.getPathSegmentsInfo(t.path))},getMeasuringContext:function(){return e._measuringContext||(e._measuringContext=this.canvas&&this.canvas.contextCache||e.util.createCanvasElement().getContext("2d")),e._measuringContext},_splitText:function(){var t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t},initDimensions:function(){if(!this.__skipDimension){if(this._splitText(),this._clearCache(),this.path){var t=1.1*this.getHeightOfLine(0);this.width=this.path.width+t,this.height=this.path.height+t}else this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight();-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.saveState({propertySet:"_dimensionAffectingProps"})}},enlargeSpaces:function(){for(var t,e,i,n,r,o,s,a=0,c=this._textLines.length;a<c;a++)if(("justify"===this.textAlign||a!==c-1&&!this.isEndOfWrapping(a))&&(n=0,r=this._textLines[a],(e=this.getLineWidth(a))<this.width&&(s=this.textLines[a].match(this._reSpacesAndTabs)))){i=s.length,t=(this.width-e)/i;for(var l=0,h=r.length;l<=h;l++)o=this.__charBounds[a][l],this._reSpaceAndTab.test(r[l])?(o.width+=t,o.kernedWidth+=t,o.left+=n,n+=t):o.left+=n}},isEndOfWrapping:function(t){return t===this._textLines.length-1},missingNewlineOffset:function(){return 1},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_getCacheCanvasDimensions:function(){var t=this.callSuper("_getCacheCanvasDimensions"),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t},_render:function(t){var e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")},_renderText:function(t){"stroke"===this.paintFirst?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))},_setTextStyles:function(t,e,i){if(t.textBaseline="alphabetical",this.path)switch(this.pathAlign){case"center":t.textBaseline="middle";break;case"ascender":t.textBaseline="top";break;case"descender":t.textBaseline="bottom"}t.font=this._getFontDeclaration(e,i)},calcTextWidth:function(){for(var t=this.getLineWidth(0),e=1,i=this._textLines.length;e<i;e++){var n=this.getLineWidth(e);n>t&&(t=n)}return t},_renderTextLine:function(t,e,i,n,r,o){this._renderChars(t,e,i,n,r,o)},_renderTextLinesBackground:function(t){if(this.textBackgroundColor||this.styleHas("textBackgroundColor")){for(var e,i,n,r,o,s,a,c=t.fillStyle,l=this._getLeftOffset(),h=this._getTopOffset(),u=0,f=0,d=this.path,g=0,p=this._textLines.length;g<p;g++)if(e=this.getHeightOfLine(g),this.textBackgroundColor||this.styleHas("textBackgroundColor",g)){n=this._textLines[g],i=this._getLineLeftOffset(g),f=0,u=0,r=this.getValueOfPropertyAt(g,0,"textBackgroundColor");for(var v=0,m=n.length;v<m;v++)o=this.__charBounds[g][v],s=this.getValueOfPropertyAt(g,v,"textBackgroundColor"),d?(t.save(),t.translate(o.renderLeft,o.renderTop),t.rotate(o.angle),t.fillStyle=s,s&&t.fillRect(-o.width/2,-e/this.lineHeight*(1-this._fontSizeFraction),o.width,e/this.lineHeight),t.restore()):s!==r?(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=r,r&&t.fillRect(a,h,f,e/this.lineHeight),u=o.left,f=o.width,r=s):f+=o.kernedWidth;s&&!d&&(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=s,t.fillRect(a,h,f,e/this.lineHeight)),h+=e}else h+=e;t.fillStyle=c,this._removeShadow(t)}},getFontCache:function(t){var i=t.fontFamily.toLowerCase();e.charWidthsCache[i]||(e.charWidthsCache[i]={});var n=e.charWidthsCache[i],r=t.fontStyle.toLowerCase()+"_"+(t.fontWeight+"").toLowerCase();return n[r]||(n[r]={}),n[r]},_measureChar:function(t,e,i,n){var r,o,s,a,c=this.getFontCache(e),l=i+t,h=this._getFontDeclaration(e)===this._getFontDeclaration(n),u=e.fontSize/this.CACHE_FONT_SIZE;if(i&&void 0!==c[i]&&(s=c[i]),void 0!==c[t]&&(a=r=c[t]),h&&void 0!==c[l]&&(a=(o=c[l])-s),void 0===r||void 0===s||void 0===o){var f=this.getMeasuringContext();this._setTextStyles(f,e,!0)}return void 0===r&&(a=r=f.measureText(t).width,c[t]=r),void 0===s&&h&&i&&(s=f.measureText(i).width,c[i]=s),h&&void 0===o&&(o=f.measureText(l).width,c[l]=o,a=o-s),{width:r*u,kernedWidth:a*u}},getHeightOfChar:function(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")},measureLine:function(t){var e=this._measureLine(t);return 0!==this.charSpacing&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e},_measureLine:function(t){var i,n,r,o,s,a,c=0,l=this._textLines[t],h=new Array(l.length),u=0,f=this.path,d="right"===this.pathSide;for(this.__charBounds[t]=h,i=0;i<l.length;i++)n=l[i],o=this._getGraphemeBox(n,t,i,r),h[i]=o,c+=o.kernedWidth,r=n;if(h[i]={left:o?o.left+o.width:0,width:0,kernedWidth:0,height:this.fontSize},f){switch(a=f.segmentsInfo[f.segmentsInfo.length-1].length,(s=e.util.getPointOnPath(f.path,0,f.segmentsInfo)).x+=f.pathOffset.x,s.y+=f.pathOffset.y,this.textAlign){case"left":u=d?a-c:0;break;case"center":u=(a-c)/2;break;case"right":u=d?0:a-c}for(u+=this.pathStartOffset*(d?-1:1),i=d?l.length-1:0;d?i>=0:i<l.length;d?i--:i++)o=h[i],u>a?u%=a:u<0&&(u+=a),this._setGraphemeOnPath(u,o,s),u+=o.kernedWidth}return{width:c,numOfSpaces:0}},_setGraphemeOnPath:function(t,i,n){var r=t+i.kernedWidth/2,o=this.path,s=e.util.getPointOnPath(o.path,r,o.segmentsInfo);i.renderLeft=s.x-n.x,i.renderTop=s.y-n.y,i.angle=s.angle+("right"===this.pathSide?Math.PI:0)},_getGraphemeBox:function(t,e,i,n,r){var o,s=this.getCompleteStyleDeclaration(e,i),a=n?this.getCompleteStyleDeclaration(e,i-1):{},c=this._measureChar(t,s,n,a),l=c.kernedWidth,h=c.width;0!==this.charSpacing&&(h+=o=this._getWidthOfCharSpacing(),l+=o);var u={width:h,left:0,height:s.fontSize,kernedWidth:l,deltaY:s.deltaY};if(i>0&&!r){var f=this.__charBounds[e][i-1];u.left=f.left+f.width+c.kernedWidth-c.width}return u},getHeightOfLine:function(t){if(this.__lineHeights[t])return this.__lineHeights[t];for(var e=this._textLines[t],i=this.getHeightOfChar(t,0),n=1,r=e.length;n<r;n++)i=Math.max(this.getHeightOfChar(t,n),i);return this.__lineHeights[t]=i*this.lineHeight*this._fontSizeMult},calcTextHeight:function(){for(var t,e=0,i=0,n=this._textLines.length;i<n;i++)t=this.getHeightOfLine(i),e+=i===n-1?t/this.lineHeight:t;return e},_getLeftOffset:function(){return"ltr"===this.direction?-this.width/2:this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextCommon:function(t,e){t.save();for(var i=0,n=this._getLeftOffset(),r=this._getTopOffset(),o=0,s=this._textLines.length;o<s;o++){var a=this.getHeightOfLine(o),c=a/this.lineHeight,l=this._getLineLeftOffset(o);this._renderTextLine(e,t,this._textLines[o],n+l,r+i+c,o),i+=a}t.restore()},_renderTextFill:function(t){(this.fill||this.styleHas("fill"))&&this._renderTextCommon(t,"fillText")},_renderTextStroke:function(t){(this.stroke&&0!==this.strokeWidth||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())},_renderChars:function(t,i,n,r,o,s){var a,c,l,h,u,f=this.getHeightOfLine(s),d=-1!==this.textAlign.indexOf("justify"),g="",p=0,v=this.path,m=!d&&0===this.charSpacing&&this.isEmptyStyles(s)&&!v,y="ltr"===this.direction,b="ltr"===this.direction?1:-1,x=i.canvas.getAttribute("dir");if(i.save(),x!==this.direction&&(i.canvas.setAttribute("dir",y?"ltr":"rtl"),i.direction=y?"ltr":"rtl",i.textAlign=y?"left":"right"),o-=f*this._fontSizeFraction/this.lineHeight,m)return this._renderChar(t,i,s,0,n.join(""),r,o,f),void i.restore();for(var _=0,C=n.length-1;_<=C;_++)h=_===C||this.charSpacing||v,g+=n[_],l=this.__charBounds[s][_],0===p?(r+=b*(l.kernedWidth-l.width),p+=l.width):p+=l.kernedWidth,d&&!h&&this._reSpaceAndTab.test(n[_])&&(h=!0),h||(a=a||this.getCompleteStyleDeclaration(s,_),c=this.getCompleteStyleDeclaration(s,_+1),h=e.util.hasStyleChanged(a,c,!1)),h&&(v?(i.save(),i.translate(l.renderLeft,l.renderTop),i.rotate(l.angle),this._renderChar(t,i,s,_,g,-p/2,0,f),i.restore()):(u=r,this._renderChar(t,i,s,_,g,u,o,f)),g="",a=c,r+=b*p,p=0);i.restore()},_applyPatternGradientTransformText:function(t){var i,n=e.util.createCanvasElement(),r=this.width+this.strokeWidth,o=this.height+this.strokeWidth;return n.width=r,n.height=o,(i=n.getContext("2d")).beginPath(),i.moveTo(0,0),i.lineTo(r,0),i.lineTo(r,o),i.lineTo(0,o),i.closePath(),i.translate(r/2,o/2),i.fillStyle=t.toLive(i),this._applyPatternGradientTransform(i,t),i.fill(),i.createPattern(n,"no-repeat")},handleFiller:function(t,e,i){var n,r;return i.toLive?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?(n=-this.width/2,r=-this.height/2,t.translate(n,r),t[e]=this._applyPatternGradientTransformText(i),{offsetX:n,offsetY:r}):(t[e]=i.toLive(t,this),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})},_setStrokeStyles:function(t,e){return t.lineWidth=e.strokeWidth,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",e.stroke)},_setFillStyles:function(t,e){return this.handleFiller(t,"fillStyle",e.fill)},_renderChar:function(t,e,i,n,r,o,s){var a,c,l=this._getStyleDeclaration(i,n),h=this.getCompleteStyleDeclaration(i,n),u="fillText"===t&&h.fill,f="strokeText"===t&&h.stroke&&h.strokeWidth;(f||u)&&(e.save(),u&&(a=this._setFillStyles(e,h)),f&&(c=this._setStrokeStyles(e,h)),e.font=this._getFontDeclaration(h),l&&l.textBackgroundColor&&this._removeShadow(e),l&&l.deltaY&&(s+=l.deltaY),u&&e.fillText(r,o-a.offsetX,s-a.offsetY),f&&e.strokeText(r,o-c.offsetX,s-c.offsetY),e.restore())},setSuperscript:function(t,e){return this._setScript(t,e,this.superscript)},setSubscript:function(t,e){return this._setScript(t,e,this.subscript)},_setScript:function(t,e,i){var n=this.get2DCursorLocation(t,!0),r=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"fontSize"),o=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"deltaY"),s={fontSize:r*i.size,deltaY:o+r*i.baseline};return this.setSelectionStyles(s,t,e),this},_getLineLeftOffset:function(t){var e=this.getLineWidth(t),i=this.width-e,n=this.textAlign,r=this.direction,o=0,s=this.isEndOfWrapping(t);return"justify"===n||"justify-center"===n&&!s||"justify-right"===n&&!s||"justify-left"===n&&!s?0:("center"===n&&(o=i/2),"right"===n&&(o=i),"justify-center"===n&&(o=i/2),"justify-right"===n&&(o=i),"rtl"===r&&(o-=i),o)},_clearCache:function(){this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]},_shouldClearDimensionCache:function(){var t=this._forceClearCache;return t||(t=this.hasStateChanged("_dimensionAffectingProps")),t&&(this.dirty=!0,this._forceClearCache=!1),t},getLineWidth:function(t){if(void 0!==this.__lineWidths[t])return this.__lineWidths[t];var e=this.measureLine(t).width;return this.__lineWidths[t]=e,e},_getWidthOfCharSpacing:function(){return 0!==this.charSpacing?this.fontSize*this.charSpacing/1e3:0},getValueOfPropertyAt:function(t,e,i){var n=this._getStyleDeclaration(t,e);return n&&"undefined"!==typeof n[i]?n[i]:this[i]},_renderTextDecoration:function(t,e){if(this[e]||this.styleHas(e)){t.save(),"overline"!==e&&"linethrough"!==e||this._removeShadow(t);for(var i,n,r,o,s,a,c,l,h,u,f,d,g,p,v,m,y=this._getLeftOffset(),b=this._getTopOffset(),x=this.path,_=this._getWidthOfCharSpacing(),C=this.offsets[e],S=0,w=this._textLines.length;S<w;S++)if(i=this.getHeightOfLine(S),this[e]||this.styleHas(e,S)){c=this._textLines[S],p=i/this.lineHeight,o=this._getLineLeftOffset(S),u=0,f=0,l=this.getValueOfPropertyAt(S,0,e),m=this.getValueOfPropertyAt(S,0,"fill"),h=b+p*(1-this._fontSizeFraction),n=this.getHeightOfChar(S,0),s=this.getValueOfPropertyAt(S,0,"deltaY");for(var T=0,O=c.length;T<O;T++)if(d=this.__charBounds[S][T],g=this.getValueOfPropertyAt(S,T,e),v=this.getValueOfPropertyAt(S,T,"fill"),r=this.getHeightOfChar(S,T),a=this.getValueOfPropertyAt(S,T,"deltaY"),x&&g&&v)t.save(),t.fillStyle=m,t.translate(d.renderLeft,d.renderTop),t.rotate(d.angle),t.fillRect(-d.kernedWidth/2,C*r+a,d.kernedWidth,this.fontSize/15),t.restore();else if((g!==l||v!==m||r!==n||a!==s)&&f>0){var E=y+o+u;"rtl"===this.direction&&(E=this.width-E-f),l&&m&&(t.fillStyle=m,t.fillRect(E,h+C*n+s,f,this.fontSize/15)),u=d.left,f=d.width,l=g,m=v,n=r,s=a}else f+=d.kernedWidth;E=y+o+u;"rtl"===this.direction&&(E=this.width-E-f),t.fillStyle=v,g&&v&&t.fillRect(E,h+C*n+s,f-_,this.fontSize/15),b+=i}else b+=i;t.restore()}},_getFontDeclaration:function(t,i){var n=t||this,r=this.fontFamily,o=e.Text.genericFonts.indexOf(r.toLowerCase())>-1,s=void 0===r||r.indexOf("'")>-1||r.indexOf(",")>-1||r.indexOf('"')>-1||o?n.fontFamily:'"'+n.fontFamily+'"';return[e.isLikelyNode?n.fontWeight:n.fontStyle,e.isLikelyNode?n.fontStyle:n.fontWeight,i?this.CACHE_FONT_SIZE+"px":n.fontSize+"px",s].join(" ")},render:function(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._shouldClearDimensionCache()&&this.initDimensions(),this.callSuper("render",t)))},_splitTextIntoLines:function(t){for(var i=t.split(this._reNewline),n=new Array(i.length),r=["\n"],o=[],s=0;s<i.length;s++)n[s]=e.util.string.graphemeSplit(i[s]),o=o.concat(n[s],r);return o.pop(),{_unwrappedLines:n,lines:i,graphemeText:o,graphemeLines:n}},toObject:function(t){var i=n.concat(t),r=this.callSuper("toObject",i);return r.styles=e.util.stylesToArray(this.styles,this.text),r.path&&(r.path=this.path.toObject()),r},set:function(t,e){this.callSuper("set",t,e);var i=!1,n=!1;if("object"===typeof t)for(var r in t)"path"===r&&this.setPathInfo(),i=i||-1!==this._dimensionAffectingProps.indexOf(r),n=n||"path"===r;else i=-1!==this._dimensionAffectingProps.indexOf(t),n="path"===t;return n&&this.setPathInfo(),i&&(this.initDimensions(),this.setCoords()),this},complexity:function(){return 1}}),e.Text.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x y dx dy font-family font-style font-weight font-size letter-spacing text-decoration text-anchor".split(" ")),e.Text.DEFAULT_SVG_FONT_SIZE=16,e.Text.fromElement=function(t,n,r){if(!t)return n(null);var o=e.parseAttributes(t,e.Text.ATTRIBUTE_NAMES),s=o.textAnchor||"left";if((r=e.util.object.extend(r?i(r):{},o)).top=r.top||0,r.left=r.left||0,o.textDecoration){var a=o.textDecoration;-1!==a.indexOf("underline")&&(r.underline=!0),-1!==a.indexOf("overline")&&(r.overline=!0),-1!==a.indexOf("line-through")&&(r.linethrough=!0),delete r.textDecoration}"dx"in o&&(r.left+=o.dx),"dy"in o&&(r.top+=o.dy),"fontSize"in r||(r.fontSize=e.Text.DEFAULT_SVG_FONT_SIZE);var c="";"textContent"in t?c=t.textContent:"firstChild"in t&&null!==t.firstChild&&"data"in t.firstChild&&null!==t.firstChild.data&&(c=t.firstChild.data),c=c.replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," ");var l=r.strokeWidth;r.strokeWidth=0;var h=new e.Text(c,r),u=h.getScaledHeight()/h.height,f=((h.height+h.strokeWidth)*h.lineHeight-h.height)*u,d=h.getScaledHeight()+f,g=0;"center"===s&&(g=h.getScaledWidth()/2),"right"===s&&(g=h.getScaledWidth()),h.set({left:h.left-g,top:h.top-(d-h.fontSize*(.07+h._fontSizeFraction))/h.lineHeight,strokeWidth:"undefined"!==typeof l?l:1}),n(h)},e.Text.fromObject=function(t,n){var r=i(t),o=t.path;return delete r.path,e.Object._fromObject("Text",r,(function(i){i.styles=e.util.stylesFromArray(t.styles,t.text),o?e.Object._fromObject("Path",o,(function(t){i.set("path",t),n(i)}),"path"):n(i)}),"text")},e.Text.genericFonts=["sans-serif","serif","cursive","fantasy","monospace"],e.util.createAccessors&&e.util.createAccessors(e.Text)}}(e),r.util.object.extend(r.Text.prototype,{isEmptyStyles:function(t){if(!this.styles)return!0;if("undefined"!==typeof t&&!this.styles[t])return!0;var e="undefined"===typeof t?this.styles:{line:this.styles[t]};for(var i in e)for(var n in e[i])for(var r in e[i][n])return!1;return!0},styleHas:function(t,e){if(!this.styles||!t||""===t)return!1;if("undefined"!==typeof e&&!this.styles[e])return!1;var i="undefined"===typeof e?this.styles:{0:this.styles[e]};for(var n in i)for(var r in i[n])if("undefined"!==typeof i[n][r][t])return!0;return!1},cleanStyle:function(t){if(!this.styles||!t||""===t)return!1;var e,i,n=this.styles,r=0,o=!0,s=0;for(var a in n){for(var c in e=0,n[a]){var l;r++,(l=n[a][c]).hasOwnProperty(t)?(i?l[t]!==i&&(o=!1):i=l[t],l[t]===this[t]&&delete l[t]):o=!1,0!==Object.keys(l).length?e++:delete n[a][c]}0===e&&delete n[a]}for(var h=0;h<this._textLines.length;h++)s+=this._textLines[h].length;o&&r===s&&(this[t]=i,this.removeStyle(t))},removeStyle:function(t){if(this.styles&&t&&""!==t){var e,i,n,r=this.styles;for(i in r){for(n in e=r[i])delete e[n][t],0===Object.keys(e[n]).length&&delete e[n];0===Object.keys(e).length&&delete r[i]}}},_extendStyles:function(t,e){var i=this.get2DCursorLocation(t);this._getLineStyle(i.lineIndex)||this._setLineStyle(i.lineIndex),this._getStyleDeclaration(i.lineIndex,i.charIndex)||this._setStyleDeclaration(i.lineIndex,i.charIndex,{}),r.util.object.extend(this._getStyleDeclaration(i.lineIndex,i.charIndex),e)},get2DCursorLocation:function(t,e){"undefined"===typeof t&&(t=this.selectionStart);for(var i=e?this._unwrappedTextLines:this._textLines,n=i.length,r=0;r<n;r++){if(t<=i[r].length)return{lineIndex:r,charIndex:t};t-=i[r].length+this.missingNewlineOffset(r,e)}return{lineIndex:r-1,charIndex:i[r-1].length<t?i[r-1].length:t}},getSelectionStyles:function(t,e,i){"undefined"===typeof t&&(t=this.selectionStart||0),"undefined"===typeof e&&(e=this.selectionEnd||t);for(var n=[],r=t;r<e;r++)n.push(this.getStyleAtPosition(r,i));return n},getStyleAtPosition:function(t,e){var i=this.get2DCursorLocation(t);return(e?this.getCompleteStyleDeclaration(i.lineIndex,i.charIndex):this._getStyleDeclaration(i.lineIndex,i.charIndex))||{}},setSelectionStyles:function(t,e,i){"undefined"===typeof e&&(e=this.selectionStart||0),"undefined"===typeof i&&(i=this.selectionEnd||e);for(var n=e;n<i;n++)this._extendStyles(n,t);return this._forceClearCache=!0,this},_getStyleDeclaration:function(t,e){var i=this.styles&&this.styles[t];return i?i[e]:null},getCompleteStyleDeclaration:function(t,e){for(var i,n=this._getStyleDeclaration(t,e)||{},r={},o=0;o<this._styleProperties.length;o++)r[i=this._styleProperties[o]]="undefined"===typeof n[i]?this[i]:n[i];return r},_setStyleDeclaration:function(t,e,i){this.styles[t][e]=i},_deleteStyleDeclaration:function(t,e){delete this.styles[t][e]},_getLineStyle:function(t){return!!this.styles[t]},_setLineStyle:function(t){this.styles[t]={}},_deleteLineStyle:function(t){delete this.styles[t]}}),function(){function t(t){t.textDecoration&&(t.textDecoration.indexOf("underline")>-1&&(t.underline=!0),t.textDecoration.indexOf("line-through")>-1&&(t.linethrough=!0),t.textDecoration.indexOf("overline")>-1&&(t.overline=!0),delete t.textDecoration)}r.IText=r.util.createClass(r.Text,r.Observable,{type:"i-text",selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,_reSpace:/\s|\n/,_currentCursorOpacity:0,_selectionDirection:null,_abortCursorAnimation:!1,__widthOfSpace:[],inCompositionMode:!1,initialize:function(t,e){this.callSuper("initialize",t,e),this.initBehavior()},setSelectionStart:function(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)},setSelectionEnd:function(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)},_updateAndFire:function(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()},_fireSelectionChanged:function(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})},initDimensions:function(){this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this.callSuper("initDimensions")},render:function(t){this.clearContextTop(),this.callSuper("render",t),this.cursorOffsetCache={},this.renderCursorOrSelection()},_render:function(t){this.callSuper("_render",t)},clearContextTop:function(t){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var e=this.canvas.contextTop,i=this.canvas.viewportTransform;e.save(),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this.transform(e),this._clearTextArea(e),t||e.restore()}},renderCursorOrSelection:function(){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var t=this._getCursorBoundaries(),e=this.canvas.contextTop;this.clearContextTop(!0),this.selectionStart===this.selectionEnd?this.renderCursor(t,e):this.renderSelection(t,e),e.restore()}},_clearTextArea:function(t){var e=this.width+4,i=this.height+4;t.clearRect(-e/2,-i/2,e,i)},_getCursorBoundaries:function(t){"undefined"===typeof t&&(t=this.selectionStart);var e=this._getLeftOffset(),i=this._getTopOffset(),n=this._getCursorBoundariesOffsets(t);return{left:e,top:i,leftOffset:n.left,topOffset:n.top}},_getCursorBoundariesOffsets:function(t){if(this.cursorOffsetCache&&"top"in this.cursorOffsetCache)return this.cursorOffsetCache;var e,i,n,r,o=0,s=0,a=this.get2DCursorLocation(t);n=a.charIndex,i=a.lineIndex;for(var c=0;c<i;c++)o+=this.getHeightOfLine(c);e=this._getLineLeftOffset(i);var l=this.__charBounds[i][n];return l&&(s=l.left),0!==this.charSpacing&&n===this._textLines[i].length&&(s-=this._getWidthOfCharSpacing()),r={top:o,left:e+(s>0?s:0)},"rtl"===this.direction&&(r.left*=-1),this.cursorOffsetCache=r,this.cursorOffsetCache},renderCursor:function(t,e){var i=this.get2DCursorLocation(),n=i.lineIndex,r=i.charIndex>0?i.charIndex-1:0,o=this.getValueOfPropertyAt(n,r,"fontSize"),s=this.scaleX*this.canvas.getZoom(),a=this.cursorWidth/s,c=t.topOffset,l=this.getValueOfPropertyAt(n,r,"deltaY");c+=(1-this._fontSizeFraction)*this.getHeightOfLine(n)/this.lineHeight-o*(1-this._fontSizeFraction),this.inCompositionMode&&this.renderSelection(t,e),e.fillStyle=this.cursorColor||this.getValueOfPropertyAt(n,r,"fill"),e.globalAlpha=this.__isMousedown?1:this._currentCursorOpacity,e.fillRect(t.left+t.leftOffset-a/2,c+t.top+l,a,o)},renderSelection:function(t,e){for(var i=this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,n=this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd,r=-1!==this.textAlign.indexOf("justify"),o=this.get2DCursorLocation(i),s=this.get2DCursorLocation(n),a=o.lineIndex,c=s.lineIndex,l=o.charIndex<0?0:o.charIndex,h=s.charIndex<0?0:s.charIndex,u=a;u<=c;u++){var f,d=this._getLineLeftOffset(u)||0,g=this.getHeightOfLine(u),p=0,v=0;if(u===a&&(p=this.__charBounds[a][l].left),u>=a&&u<c)v=r&&!this.isEndOfWrapping(u)?this.width:this.getLineWidth(u)||5;else if(u===c)if(0===h)v=this.__charBounds[c][h].left;else{var m=this._getWidthOfCharSpacing();v=this.__charBounds[c][h-1].left+this.__charBounds[c][h-1].width-m}f=g,(this.lineHeight<1||u===c&&this.lineHeight>1)&&(g/=this.lineHeight);var y=t.left+d+p,b=v-p,x=g,_=0;this.inCompositionMode?(e.fillStyle=this.compositionColor||"black",x=1,_=g):e.fillStyle=this.selectionColor,"rtl"===this.direction&&(y=this.width-y-b),e.fillRect(y,t.top+t.topOffset+_,b,x),t.topOffset+=f}},getCurrentCharFontSize:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")},getCurrentCharColor:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fill")},_getCurrentCharIndex:function(){var t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}}),r.IText.fromObject=function(e,i){var n=r.util.stylesFromArray(e.styles,e.text),o=Object.assign({},e,{styles:n});if(delete o.path,t(o),o.styles)for(var s in o.styles)for(var a in o.styles[s])t(o.styles[s][a]);r.Object._fromObject("IText",o,(function(t){e.path?r.Object._fromObject("Path",e.path,(function(e){t.set("path",e),i(t)}),"path"):i(t)}),"text")}}(),function(){var t=r.util.object.clone;r.util.object.extend(r.IText.prototype,{initBehavior:function(){this.initAddedHandler(),this.initRemovedHandler(),this.initCursorSelectionHandlers(),this.initDoubleClickSimulation(),this.mouseMoveHandler=this.mouseMoveHandler.bind(this)},onDeselect:function(){this.isEditing&&this.exitEditing(),this.selected=!1},initAddedHandler:function(){var t=this;this.on("added",(function(){var e=t.canvas;e&&(e._hasITextHandlers||(e._hasITextHandlers=!0,t._initCanvasHandlers(e)),e._iTextInstances=e._iTextInstances||[],e._iTextInstances.push(t))}))},initRemovedHandler:function(){var t=this;this.on("removed",(function(){var e=t.canvas;e&&(e._iTextInstances=e._iTextInstances||[],r.util.removeFromArray(e._iTextInstances,t),0===e._iTextInstances.length&&(e._hasITextHandlers=!1,t._removeCanvasHandlers(e)))}))},_initCanvasHandlers:function(t){t._mouseUpITextHandler=function(){t._iTextInstances&&t._iTextInstances.forEach((function(t){t.__isMousedown=!1}))},t.on("mouse:up",t._mouseUpITextHandler)},_removeCanvasHandlers:function(t){t.off("mouse:up",t._mouseUpITextHandler)},_tick:function(){this._currentTickState=this._animateCursor(this,1,this.cursorDuration,"_onTickComplete")},_animateCursor:function(t,e,i,n){var r;return r={isAborted:!1,abort:function(){this.isAborted=!0}},t.animate("_currentCursorOpacity",e,{duration:i,onComplete:function(){r.isAborted||t[n]()},onChange:function(){t.canvas&&t.selectionStart===t.selectionEnd&&t.renderCursorOrSelection()},abort:function(){return r.isAborted}}),r},_onTickComplete:function(){var t=this;this._cursorTimeout1&&clearTimeout(this._cursorTimeout1),this._cursorTimeout1=setTimeout((function(){t._currentTickCompleteState=t._animateCursor(t,0,this.cursorDuration/2,"_tick")}),100)},initDelayedCursor:function(t){var e=this,i=t?0:this.cursorDelay;this.abortCursorAnimation(),this._currentCursorOpacity=1,this._cursorTimeout2=setTimeout((function(){e._tick()}),i)},abortCursorAnimation:function(){var t=this._currentTickState||this._currentTickCompleteState,e=this.canvas;this._currentTickState&&this._currentTickState.abort(),this._currentTickCompleteState&&this._currentTickCompleteState.abort(),clearTimeout(this._cursorTimeout1),clearTimeout(this._cursorTimeout2),this._currentCursorOpacity=0,t&&e&&e.clearContext(e.contextTop||e.contextContainer)},selectAll:function(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this},getSelectedText:function(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")},findWordBoundaryLeft:function(t){var e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findWordBoundaryRight:function(t){var e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},findLineBoundaryLeft:function(t){for(var e=0,i=t-1;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findLineBoundaryRight:function(t){for(var e=0,i=t;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},searchWordBoundary:function(t,e){for(var i=this._text,n=this._reSpace.test(i[t])?t-1:t,o=i[n],s=r.reNonWord;!s.test(o)&&n>0&&n<i.length;)o=i[n+=e];return s.test(o)&&(n+=1===e?0:1),n},selectWord:function(t){t=t||this.selectionStart;var e=this.searchWordBoundary(t,-1),i=this.searchWordBoundary(t,1);this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()},selectLine:function(t){t=t||this.selectionStart;var e=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);return this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this},enterEditing:function(t){if(!this.isEditing&&this.editable)return this.canvas&&(this.canvas.calcOffset(),this.exitEditingOnOthers(this.canvas)),this.isEditing=!0,this.initHiddenTextarea(t),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered"),this._fireSelectionChanged(),this.canvas?(this.canvas.fire("text:editing:entered",{target:this}),this.initMouseMoveHandler(),this.canvas.requestRenderAll(),this):this},exitEditingOnOthers:function(t){t._iTextInstances&&t._iTextInstances.forEach((function(t){t.selected=!1,t.isEditing&&t.exitEditing()}))},initMouseMoveHandler:function(){this.canvas.on("mouse:move",this.mouseMoveHandler)},mouseMoveHandler:function(t){if(this.__isMousedown&&this.isEditing){document.activeElement!==this.hiddenTextarea&&this.hiddenTextarea.focus();var e=this.getSelectionStartFromPointer(t.e),i=this.selectionStart,n=this.selectionEnd;(e===this.__selectionStartOnMouseDown&&i!==n||i!==e&&n!==e)&&(e>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=e):(this.selectionStart=e,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===i&&this.selectionEnd===n||(this.restartCursorIfNeeded(),this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}},_setEditingProps:function(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0},fromStringToGraphemeSelection:function(t,e,i){var n=i.slice(0,t),o=r.util.string.graphemeSplit(n).length;if(t===e)return{selectionStart:o,selectionEnd:o};var s=i.slice(t,e);return{selectionStart:o,selectionEnd:o+r.util.string.graphemeSplit(s).length}},fromGraphemeToStringSelection:function(t,e,i){var n=i.slice(0,t).join("").length;return t===e?{selectionStart:n,selectionEnd:n}:{selectionStart:n,selectionEnd:n+i.slice(t,e).join("").length}},_updateTextarea:function(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){var t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}},updateFromTextArea:function(){if(this.hiddenTextarea){this.cursorOffsetCache={},this.text=this.hiddenTextarea.value,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords());var t=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value);this.selectionEnd=this.selectionStart=t.selectionEnd,this.inCompositionMode||(this.selectionStart=t.selectionStart),this.updateTextareaPosition()}},updateTextareaPosition:function(){if(this.selectionStart===this.selectionEnd){var t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}},_calcTextareaPosition:function(){if(!this.canvas)return{x:1,y:1};var t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),n=i.lineIndex,o=i.charIndex,s=this.getValueOfPropertyAt(n,o,"fontSize")*this.lineHeight,a=e.leftOffset,c=this.calcTransformMatrix(),l={x:e.left+a,y:e.top+e.topOffset+s},h=this.canvas.getRetinaScaling(),u=this.canvas.upperCanvasEl,f=u.width/h,d=u.height/h,g=f-s,p=d-s,v=u.clientWidth/f,m=u.clientHeight/d;return l=r.util.transformPoint(l,c),(l=r.util.transformPoint(l,this.canvas.viewportTransform)).x*=v,l.y*=m,l.x<0&&(l.x=0),l.x>g&&(l.x=g),l.y<0&&(l.y=0),l.y>p&&(l.y=p),l.x+=this.canvas._offset.left,l.y+=this.canvas._offset.top,{left:l.x+"px",top:l.y+"px",fontSize:s+"px",charHeight:s}},_saveEditingProps:function(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}},_restoreEditingProps:function(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor))},exitEditing:function(){var t=this._textBeforeEdit!==this.text,e=this.hiddenTextarea;return this.selected=!1,this.isEditing=!1,this.selectionEnd=this.selectionStart,e&&(e.blur&&e.blur(),e.parentNode&&e.parentNode.removeChild(e)),this.hiddenTextarea=null,this.abortCursorAnimation(),this._restoreEditingProps(),this._currentCursorOpacity=0,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),t&&this.fire("modified"),this.canvas&&(this.canvas.off("mouse:move",this.mouseMoveHandler),this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this},_removeExtraneousStyles:function(){for(var t in this.styles)this._textLines[t]||delete this.styles[t]},removeStyleFromTo:function(t,e){var i,n,r=this.get2DCursorLocation(t,!0),o=this.get2DCursorLocation(e,!0),s=r.lineIndex,a=r.charIndex,c=o.lineIndex,l=o.charIndex;if(s!==c){if(this.styles[s])for(i=a;i<this._unwrappedTextLines[s].length;i++)delete this.styles[s][i];if(this.styles[c])for(i=l;i<this._unwrappedTextLines[c].length;i++)(n=this.styles[c][i])&&(this.styles[s]||(this.styles[s]={}),this.styles[s][a+i-l]=n);for(i=s+1;i<=c;i++)delete this.styles[i];this.shiftLineStyles(c,s-c)}else if(this.styles[s]){n=this.styles[s];var h,u,f=l-a;for(i=a;i<l;i++)delete n[i];for(u in this.styles[s])(h=parseInt(u,10))>=l&&(n[h-f]=n[u],delete n[u])}},shiftLineStyles:function(e,i){var n=t(this.styles);for(var r in this.styles){var o=parseInt(r,10);o>e&&(this.styles[o+i]=n[o],n[o-i]||delete this.styles[o])}},restartCursorIfNeeded:function(){this._currentTickState&&!this._currentTickState.isAborted&&this._currentTickCompleteState&&!this._currentTickCompleteState.isAborted||this.initDelayedCursor()},insertNewlineStyleObject:function(e,i,n,r){var o,s={},a=!1,c=this._unwrappedTextLines[e].length,l=c===i;for(var h in n||(n=1),this.shiftLineStyles(e,n),this.styles[e]&&(o=this.styles[e][0===i?i:i-1]),this.styles[e]){var u=parseInt(h,10);u>=i&&(a=!0,s[u-i]=this.styles[e][h],l&&0===i||delete this.styles[e][h])}var f=!1;for(a&&!l&&(this.styles[e+n]=s,f=!0),(f||c>i)&&n--;n>0;)r&&r[n-1]?this.styles[e+n]={0:t(r[n-1])}:o?this.styles[e+n]={0:t(o)}:delete this.styles[e+n],n--;this._forceClearCache=!0},insertCharStyleObject:function(e,i,n,r){this.styles||(this.styles={});var o=this.styles[e],s=o?t(o):{};for(var a in n||(n=1),s){var c=parseInt(a,10);c>=i&&(o[c+n]=s[c],s[c-n]||delete o[c])}if(this._forceClearCache=!0,r)for(;n--;)Object.keys(r[n]).length&&(this.styles[e]||(this.styles[e]={}),this.styles[e][i+n]=t(r[n]));else if(o)for(var l=o[i?i-1:1];l&&n--;)this.styles[e][i+n]=t(l)},insertNewStyleBlock:function(t,e,i){for(var n=this.get2DCursorLocation(e,!0),r=[0],o=0,s=0;s<t.length;s++)"\n"===t[s]?r[++o]=0:r[o]++;r[0]>0&&(this.insertCharStyleObject(n.lineIndex,n.charIndex,r[0],i),i=i&&i.slice(r[0]+1)),o&&this.insertNewlineStyleObject(n.lineIndex,n.charIndex+r[0],o);for(s=1;s<o;s++)r[s]>0?this.insertCharStyleObject(n.lineIndex+s,0,r[s],i):i&&this.styles[n.lineIndex+s]&&i[0]&&(this.styles[n.lineIndex+s][0]=i[0]),i=i&&i.slice(r[s]+1);r[s]>0&&this.insertCharStyleObject(n.lineIndex+s,0,r[s],i)},setSelectionStartEndWithShift:function(t,e,i){i<=t?(e===t?this._selectionDirection="left":"right"===this._selectionDirection&&(this._selectionDirection="left",this.selectionEnd=t),this.selectionStart=i):i>t&&i<e?"right"===this._selectionDirection?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection="right":"left"===this._selectionDirection&&(this._selectionDirection="right",this.selectionStart=e),this.selectionEnd=i)},setSelectionInBoundaries:function(){var t=this.text.length;this.selectionStart>t?this.selectionStart=t:this.selectionStart<0&&(this.selectionStart=0),this.selectionEnd>t?this.selectionEnd=t:this.selectionEnd<0&&(this.selectionEnd=0)}})}(),r.util.object.extend(r.IText.prototype,{initDoubleClickSimulation:function(){this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown)},onMouseDown:function(t){if(this.canvas){this.__newClickTime=+new Date;var e=t.pointer;this.isTripleClick(e)&&(this.fire("tripleclick",t),this._stopEvent(t.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=e,this.__lastIsEditing=this.isEditing,this.__lastSelected=this.selected}},isTripleClick:function(t){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===t.x&&this.__lastPointer.y===t.y},_stopEvent:function(t){t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation()},initCursorSelectionHandlers:function(){this.initMousedownHandler(),this.initMouseupHandler(),this.initClicks()},doubleClickHandler:function(t){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(t.e))},tripleClickHandler:function(t){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(t.e))},initClicks:function(){this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler)},_mouseDownHandler:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.__isMousedown=!0,this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(t.e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()))},_mouseDownHandlerBefore:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.selected=this===this.canvas._activeObject)},initMousedownHandler:function(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore)},initMouseupHandler:function(){this.on("mouseup",this.mouseUpHandler)},mouseUpHandler:function(t){if(this.__isMousedown=!1,!(!this.editable||this.group||t.transform&&t.transform.actionPerformed||t.e.button&&1!==t.e.button)){if(this.canvas){var e=this.canvas._activeObject;if(e&&e!==this)return}this.__lastSelected&&!this.__corner?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(t.e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0}},setCursorByClick:function(t){var e=this.getSelectionStartFromPointer(t),i=this.selectionStart,n=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,n,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())},getSelectionStartFromPointer:function(t){for(var e,i=this.getLocalPointer(t),n=0,r=0,o=0,s=0,a=0,c=0,l=this._textLines.length;c<l&&o<=i.y;c++)o+=this.getHeightOfLine(c)*this.scaleY,a=c,c>0&&(s+=this._textLines[c-1].length+this.missingNewlineOffset(c-1));r=this._getLineLeftOffset(a)*this.scaleX,e=this._textLines[a],"rtl"===this.direction&&(i.x=this.width*this.scaleX-i.x+r);for(var h=0,u=e.length;h<u&&(n=r,(r+=this.__charBounds[a][h].kernedWidth*this.scaleX)<=i.x);h++)s++;return this._getNewSelectionStartFromOffset(i,n,r,s,u)},_getNewSelectionStartFromOffset:function(t,e,i,n,r){var o=t.x-e,s=i-t.x,a=n+(s>o||s<0?0:1);return this.flipX&&(a=r-a),a>this._text.length&&(a=this._text.length),a}}),r.util.object.extend(r.IText.prototype,{initHiddenTextarea:function(){this.hiddenTextarea=r.document.createElement("textarea"),this.hiddenTextarea.setAttribute("autocapitalize","off"),this.hiddenTextarea.setAttribute("autocorrect","off"),this.hiddenTextarea.setAttribute("autocomplete","off"),this.hiddenTextarea.setAttribute("spellcheck","false"),this.hiddenTextarea.setAttribute("data-fabric-hiddentextarea",""),this.hiddenTextarea.setAttribute("wrap","off");var t=this._calcTextareaPosition();this.hiddenTextarea.style.cssText="position: absolute; top: "+t.top+"; left: "+t.left+"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: "+t.fontSize+";",this.hiddenTextareaContainer?this.hiddenTextareaContainer.appendChild(this.hiddenTextarea):r.document.body.appendChild(this.hiddenTextarea),r.util.addListener(this.hiddenTextarea,"keydown",this.onKeyDown.bind(this)),r.util.addListener(this.hiddenTextarea,"keyup",this.onKeyUp.bind(this)),r.util.addListener(this.hiddenTextarea,"input",this.onInput.bind(this)),r.util.addListener(this.hiddenTextarea,"copy",this.copy.bind(this)),r.util.addListener(this.hiddenTextarea,"cut",this.copy.bind(this)),r.util.addListener(this.hiddenTextarea,"paste",this.paste.bind(this)),r.util.addListener(this.hiddenTextarea,"compositionstart",this.onCompositionStart.bind(this)),r.util.addListener(this.hiddenTextarea,"compositionupdate",this.onCompositionUpdate.bind(this)),r.util.addListener(this.hiddenTextarea,"compositionend",this.onCompositionEnd.bind(this)),!this._clickHandlerInitialized&&this.canvas&&(r.util.addListener(this.canvas.upperCanvasEl,"click",this.onClick.bind(this)),this._clickHandlerInitialized=!0)},keysMap:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorRight",36:"moveCursorLeft",37:"moveCursorLeft",38:"moveCursorUp",39:"moveCursorRight",40:"moveCursorDown"},keysMapRtl:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorLeft",36:"moveCursorRight",37:"moveCursorRight",38:"moveCursorUp",39:"moveCursorLeft",40:"moveCursorDown"},ctrlKeysMapUp:{67:"copy",88:"cut"},ctrlKeysMapDown:{65:"selectAll"},onClick:function(){this.hiddenTextarea&&this.hiddenTextarea.focus()},onKeyDown:function(t){if(this.isEditing){var e="rtl"===this.direction?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}},onKeyUp:function(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())},onInput:function(t){var e=this.fromPaste;if(this.fromPaste=!1,t&&t.stopPropagation(),this.isEditing){var i,n,o,s,a,c=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,l=this._text.length,h=c.length,u=h-l,f=this.selectionStart,d=this.selectionEnd,g=f!==d;if(""===this.hiddenTextarea.value)return this.styles={},this.updateFromTextArea(),this.fire("changed"),void(this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll()));var p=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),v=f>p.selectionStart;g?(i=this._text.slice(f,d),u+=d-f):h<l&&(i=v?this._text.slice(d+u,d):this._text.slice(f,f-u)),n=c.slice(p.selectionEnd-u,p.selectionEnd),i&&i.length&&(n.length&&(o=this.getSelectionStyles(f,f+1,!1),o=n.map((function(){return o[0]}))),g?(s=f,a=d):v?(s=d-i.length,a=d):(s=d,a=d+i.length),this.removeStyleFromTo(s,a)),n.length&&(e&&n.join("")===r.copiedText&&!r.disableStyleCopyPaste&&(o=r.copiedTextStyle),this.insertNewStyleBlock(n,f,o)),this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())}},onCompositionStart:function(){this.inCompositionMode=!0},onCompositionEnd:function(){this.inCompositionMode=!1},onCompositionUpdate:function(t){this.compositionStart=t.target.selectionStart,this.compositionEnd=t.target.selectionEnd,this.updateTextareaPosition()},copy:function(){this.selectionStart!==this.selectionEnd&&(r.copiedText=this.getSelectedText(),r.disableStyleCopyPaste?r.copiedTextStyle=null:r.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0)},paste:function(){this.fromPaste=!0},_getClipboardData:function(t){return t&&t.clipboardData||r.window.clipboardData},_getWidthBeforeCursor:function(t,e){var i,n=this._getLineLeftOffset(t);return e>0&&(n+=(i=this.__charBounds[t][e-1]).left+i.width),n},getDownCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),n=this.get2DCursorLocation(i),r=n.lineIndex;if(r===this._textLines.length-1||t.metaKey||34===t.keyCode)return this._text.length-i;var o=n.charIndex,s=this._getWidthBeforeCursor(r,o),a=this._getIndexOnLine(r+1,s);return this._textLines[r].slice(o).length+a+1+this.missingNewlineOffset(r)},_getSelectionForOffset:function(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart},getUpCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),n=this.get2DCursorLocation(i),r=n.lineIndex;if(0===r||t.metaKey||33===t.keyCode)return-i;var o=n.charIndex,s=this._getWidthBeforeCursor(r,o),a=this._getIndexOnLine(r-1,s),c=this._textLines[r].slice(0,o),l=this.missingNewlineOffset(r-1);return-this._textLines[r-1].length+a-c.length+(1-l)},_getIndexOnLine:function(t,e){for(var i,n,r=this._textLines[t],o=this._getLineLeftOffset(t),s=0,a=0,c=r.length;a<c;a++)if((o+=i=this.__charBounds[t][a].width)>e){n=!0;var l=o-i,h=o,u=Math.abs(l-e);s=Math.abs(h-e)<u?a:a-1;break}return n||(s=r.length-1),s},moveCursorDown:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)},moveCursorUp:function(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorUpOrDown("Up",t)},_moveCursorUpOrDown:function(t,e){var i=this["get"+t+"CursorOffset"](e,"right"===this._selectionDirection);e.shiftKey?this.moveCursorWithShift(i):this.moveCursorWithoutShift(i),0!==i&&(this.setSelectionInBoundaries(),this.abortCursorAnimation(),this._currentCursorOpacity=1,this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorWithShift:function(t){var e="left"===this._selectionDirection?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),0!==t},moveCursorWithoutShift:function(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),0!==t},moveCursorLeft:function(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorLeftOrRight("Left",t)},_move:function(t,e,i){var n;if(t.altKey)n=this["findWordBoundary"+i](this[e]);else{if(!t.metaKey&&35!==t.keyCode&&36!==t.keyCode)return this[e]+="Left"===i?-1:1,!0;n=this["findLineBoundary"+i](this[e])}if("undefined"!==typeof n&&this[e]!==n)return this[e]=n,!0},_moveLeft:function(t,e){return this._move(t,e,"Left")},_moveRight:function(t,e){return this._move(t,e,"Right")},moveCursorLeftWithoutShift:function(t){var e=!0;return this._selectionDirection="left",this.selectionEnd===this.selectionStart&&0!==this.selectionStart&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e},moveCursorLeftWithShift:function(t){return"right"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):0!==this.selectionStart?(this._selectionDirection="left",this._moveLeft(t,"selectionStart")):void 0},moveCursorRight:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)},_moveCursorLeftOrRight:function(t,e){var i="moveCursor"+t+"With";this._currentCursorOpacity=1,e.shiftKey?i+="Shift":i+="outShift",this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorRightWithShift:function(t){return"left"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection="right",this._moveRight(t,"selectionEnd")):void 0},moveCursorRightWithoutShift:function(t){var e=!0;return this._selectionDirection="right",this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e},removeChars:function(t,e){"undefined"===typeof e&&(e=t+1),this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()},insertChars:function(t,e,i,n){"undefined"===typeof n&&(n=i),n>i&&this.removeStyleFromTo(i,n);var o=r.util.string.graphemeSplit(t);this.insertNewStyleBlock(o,i,e),this._text=[].concat(this._text.slice(0,i),o,this._text.slice(n)),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()}}),function(){var t=r.util.toFixed,e=r.util.radiansToDegrees,i=r.util.calcRotateMatrix,n=r.util.transformPoint,o=/  +/g;r.util.object.extend(r.Text.prototype,{_toSVG:function(){var t=this._getSVGLeftTopOffsets(),e=this._getSVGTextAndBg(t.textTop,t.textLeft);return this._wrapSVGTextAndBg(e)},toSVG:function(t){var e=this._createBaseSVGMarkup(this._toSVG(),{reviver:t,noStyle:!0,withShadow:!0}),i=this.path;return i?e+i._createBaseSVGMarkup(i._toSVG(),{reviver:t,withShadow:!0}):e},_getSVGLeftTopOffsets:function(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}},_wrapSVGTextAndBg:function(t){var e=this.getSvgTextDecoration(this);return[t.textBgRects.join(""),'\t\t<text xml:space="preserve" ',this.fontFamily?'font-family="'+this.fontFamily.replace(/"/g,"'")+'" ':"",this.fontSize?'font-size="'+this.fontSize+'" ':"",this.fontStyle?'font-style="'+this.fontStyle+'" ':"",this.fontWeight?'font-weight="'+this.fontWeight+'" ':"",e?'text-decoration="'+e+'" ':"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",t.textSpans.join(""),"</text>\n"]},_getSVGTextAndBg:function(t,e){var i,n=[],r=[],o=t;this._setSVGBg(r);for(var s=0,a=this._textLines.length;s<a;s++)i=this._getLineLeftOffset(s),(this.textBackgroundColor||this.styleHas("textBackgroundColor",s))&&this._setSVGTextLineBg(r,s,e+i,o),this._setSVGTextLineText(n,s,e+i,o),o+=this.getHeightOfLine(s);return{textSpans:n,textBgRects:r}},_createTextCharSpan:function(s,a,c,l,h){var u=s!==s.trim()||s.match(o),f=this.getSvgSpanStyles(a,u),d=f?'style="'+f+'"':"",g=a.deltaY,p="",v=r.Object.NUM_FRACTION_DIGITS,m="";if(g&&(p=' dy="'+t(g,v)+'" '),void 0!==h.renderLeft){var y=h.angle;m=' rotate="'+t(e(y),r.Object.NUM_FRACTION_DIGITS)+'" ';var b=h.width/2,x=i({angle:e(y)});x[4]=h.renderLeft,x[5]=h.renderTop;var _=n({x:-b,y:0},x);c=_.x,l=_.y}return['<tspan x="',t(c,v),'" y="',t(l,v),'" ',p,d,m,">",r.util.string.escapeXml(s),"</tspan>"].join("")},_setSVGTextLineText:function(t,e,i,n){var o,s,a,c,l,h=this.getHeightOfLine(e),u=-1!==this.textAlign.indexOf("justify"),f="",d=0,g=this._textLines[e];n+=h*(1-this._fontSizeFraction)/this.lineHeight;for(var p=0,v=g.length-1;p<=v;p++)l=p===v||this.charSpacing||this.path,f+=g[p],a=this.__charBounds[e][p],0===d?(i+=a.kernedWidth-a.width,d+=a.width):d+=a.kernedWidth,u&&!l&&this._reSpaceAndTab.test(g[p])&&(l=!0),l||(o=o||this.getCompleteStyleDeclaration(e,p),s=this.getCompleteStyleDeclaration(e,p+1),l=r.util.hasStyleChanged(o,s,!0)),l&&(c=this._getStyleDeclaration(e,p)||{},t.push(this._createTextCharSpan(f,c,i,n,a)),f="",o=s,i+=d,d=0)},_pushTextBgRect:function(e,i,n,o,s,a){var c=r.Object.NUM_FRACTION_DIGITS;e.push("\t\t<rect ",this._getFillAttributes(i),' x="',t(n,c),'" y="',t(o,c),'" width="',t(s,c),'" height="',t(a,c),'"></rect>\n')},_setSVGTextLineBg:function(t,e,i,n){for(var r,o,s=this._textLines[e],a=this.getHeightOfLine(e)/this.lineHeight,c=0,l=0,h=this.getValueOfPropertyAt(e,0,"textBackgroundColor"),u=0,f=s.length;u<f;u++)r=this.__charBounds[e][u],(o=this.getValueOfPropertyAt(e,u,"textBackgroundColor"))!==h?(h&&this._pushTextBgRect(t,h,i+l,n,c,a),l=r.left,c=r.width,h=o):c+=r.kernedWidth;o&&this._pushTextBgRect(t,o,i+l,n,c,a)},_getFillAttributes:function(t){var e=t&&"string"===typeof t?new r.Color(t):"";return e&&e.getSource()&&1!==e.getAlpha()?'opacity="'+e.getAlpha()+'" fill="'+e.setAlpha(1).toRgb()+'"':'fill="'+t+'"'},_getSVGLineTopOffset:function(t){for(var e,i=0,n=0;n<t;n++)i+=this.getHeightOfLine(n);return e=this.getHeightOfLine(n),{lineTop:i,offset:(this._fontSizeMult-this._fontSizeFraction)*e/(this.lineHeight*this._fontSizeMult)}},getSvgStyles:function(t){return r.Object.prototype.getSvgStyles.call(this,t)+" white-space: pre;"}})}(),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Textbox=e.util.createClass(e.IText,e.Observable,{type:"textbox",minWidth:20,dynamicMinWidth:2,__cachedLines:null,lockScalingFlip:!0,noScaleCache:!1,_dimensionAffectingProps:e.Text.prototype._dimensionAffectingProps.concat("width"),_wordJoiners:/[ \t\r]/,splitByGrapheme:!1,initDimensions:function(){this.__skipDimension||(this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.height=this.calcTextHeight(),this.saveState({propertySet:"_dimensionAffectingProps"}))},_generateStyleMap:function(t){for(var e=0,i=0,n=0,r={},o=0;o<t.graphemeLines.length;o++)"\n"===t.graphemeText[n]&&o>0?(i=0,n++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[n])&&o>0&&(i++,n++),r[o]={line:e,offset:i},n+=t.graphemeLines[o].length,i+=t.graphemeLines[o].length;return r},styleHas:function(t,i){if(this._styleMap&&!this.isWrapping){var n=this._styleMap[i];n&&(i=n.line)}return e.Text.prototype.styleHas.call(this,t,i)},isEmptyStyles:function(t){if(!this.styles)return!0;var e,i,n=0,r=!1,o=this._styleMap[t],s=this._styleMap[t+1];for(var a in o&&(t=o.line,n=o.offset),s&&(r=s.line===t,e=s.offset),i="undefined"===typeof t?this.styles:{line:this.styles[t]})for(var c in i[a])if(c>=n&&(!r||c<e))for(var l in i[a][c])return!1;return!0},_getStyleDeclaration:function(t,e){if(this._styleMap&&!this.isWrapping){var i=this._styleMap[t];if(!i)return null;t=i.line,e=i.offset+e}return this.callSuper("_getStyleDeclaration",t,e)},_setStyleDeclaration:function(t,e,i){var n=this._styleMap[t];t=n.line,e=n.offset+e,this.styles[t][e]=i},_deleteStyleDeclaration:function(t,e){var i=this._styleMap[t];t=i.line,e=i.offset+e,delete this.styles[t][e]},_getLineStyle:function(t){var e=this._styleMap[t];return!!this.styles[e.line]},_setLineStyle:function(t){var e=this._styleMap[t];this.styles[e.line]={}},_wrapText:function(t,e){var i,n=[];for(this.isWrapping=!0,i=0;i<t.length;i++)n=n.concat(this._wrapLine(t[i],i,e));return this.isWrapping=!1,n},_measureWord:function(t,e,i){var n,r=0;i=i||0;for(var o=0,s=t.length;o<s;o++){r+=this._getGraphemeBox(t[o],e,o+i,n,true).kernedWidth,n=t[o]}return r},_wrapLine:function(t,i,n,r){var o=0,s=this.splitByGrapheme,a=[],c=[],l=s?e.util.string.graphemeSplit(t):t.split(this._wordJoiners),h="",u=0,f=s?"":" ",d=0,g=0,p=0,v=!0,m=this._getWidthOfCharSpacing();r=r||0;0===l.length&&l.push([]),n-=r;for(var y=0;y<l.length;y++)h=s?l[y]:e.util.string.graphemeSplit(l[y]),d=this._measureWord(h,i,u),u+=h.length,(o+=g+d-m)>n&&!v?(a.push(c),c=[],o=d,v=!0):o+=m,v||s||c.push(f),c=c.concat(h),g=s?0:this._measureWord([f],i,u),u++,v=!1,d>p&&(p=d);return y&&a.push(c),p+r>this.dynamicMinWidth&&(this.dynamicMinWidth=p-m+r),a},isEndOfWrapping:function(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line},missingNewlineOffset:function(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1},_splitTextIntoLines:function(t){for(var i=e.Text.prototype._splitTextIntoLines.call(this,t),n=this._wrapText(i.lines,this.width),r=new Array(n.length),o=0;o<n.length;o++)r[o]=n[o].join("");return i.lines=r,i.graphemeLines=n,i},getMinWidth:function(){return Math.max(this.minWidth,this.dynamicMinWidth)},_removeExtraneousStyles:function(){var t={};for(var e in this._styleMap)this._textLines[e]&&(t[this._styleMap[e].line]=1);for(var e in this.styles)t[e]||delete this.styles[e]},toObject:function(t){return this.callSuper("toObject",["minWidth","splitByGrapheme"].concat(t))}}),e.Textbox.fromObject=function(t,i){var n=e.util.stylesFromArray(t.styles,t.text),r=Object.assign({},t,{styles:n});return delete r.path,e.Object._fromObject("Textbox",r,(function(n){t.path?e.Object._fromObject("Path",t.path,(function(t){n.set("path",t),i(n)}),"path"):i(n)}),"text")}}(e),function(){var t=r.controlsUtils,e=t.scaleSkewCursorStyleHandler,i=t.scaleCursorStyleHandler,n=t.scalingEqually,o=t.scalingYOrSkewingX,s=t.scalingXOrSkewingY,a=t.scaleOrSkewActionName,c=r.Object.prototype.controls;if(c.ml=new r.Control({x:-.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:a}),c.mr=new r.Control({x:.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:a}),c.mb=new r.Control({x:0,y:.5,cursorStyleHandler:e,actionHandler:o,getActionName:a}),c.mt=new r.Control({x:0,y:-.5,cursorStyleHandler:e,actionHandler:o,getActionName:a}),c.tl=new r.Control({x:-.5,y:-.5,cursorStyleHandler:i,actionHandler:n}),c.tr=new r.Control({x:.5,y:-.5,cursorStyleHandler:i,actionHandler:n}),c.bl=new r.Control({x:-.5,y:.5,cursorStyleHandler:i,actionHandler:n}),c.br=new r.Control({x:.5,y:.5,cursorStyleHandler:i,actionHandler:n}),c.mtr=new r.Control({x:0,y:-.5,actionHandler:t.rotationWithSnapping,cursorStyleHandler:t.rotationStyleHandler,offsetY:-40,withConnection:!0,actionName:"rotate"}),r.Textbox){var l=r.Textbox.prototype.controls={};l.mtr=c.mtr,l.tr=c.tr,l.br=c.br,l.tl=c.tl,l.bl=c.bl,l.mt=c.mt,l.mb=c.mb,l.mr=new r.Control({x:.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"}),l.ml=new r.Control({x:-.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"})}}()},38657:function(t,e,i){"use strict";var n=i(48524),r=i(9939),o=i(19860),s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o.A}))},c=r.forwardRef(a);e.A=c},39388:function(t,e,i){"use strict";i.d(e,{A:function(){return v}});var n=i(66035),r=i(61434),o=i(9939),s=i(54831);var a=function(t){var e=(0,o.useRef)(0),i=(0,n.zs)((0,o.useState)(t),2),r=i[0],a=i[1],c=(0,o.useCallback)((function(t){cancelAnimationFrame(e.current),e.current=requestAnimationFrame((function(){a(t)}))}),[]);return(0,s.A)((function(){cancelAnimationFrame(e.current)})),[r,c]},c=i(46499),l=i(36831);function h(t,e){if(l.A)return t?(0,c.Tn)(t)?t():"current"in t?t.current:t:e}var u=i(3993),f=function(t){return function(e,i,n){var r=(0,o.useRef)(!1),a=(0,o.useRef)([]),c=(0,o.useRef)([]),l=(0,o.useRef)();t((function(){var t,o=(Array.isArray(n)?n:[n]).map((function(t){return h(t)}));if(!r.current)return r.current=!0,a.current=o,c.current=i,void(l.current=e());o.length===a.current.length&&(0,u.A)(a.current,o)&&(0,u.A)(c.current,i)||(null===(t=l.current)||void 0===t||t.call(l),a.current=o,c.current=i,l.current=e())})),(0,s.A)((function(){var t;null===(t=l.current)||void 0===t||t.call(l),r.current=!1}))}},d=f(o.useEffect),g=f(o.useLayoutEffect),p=l.A?g:d;var v=function(t){var e=(0,n.zs)(a((function(){var e=h(t);return e?{width:e.clientWidth,height:e.clientHeight}:void 0})),2),i=e[0],o=e[1];return p((function(){var e=h(t);if(e){var i=new r.A((function(t){t.forEach((function(t){var e=t.target,i=e.clientWidth,n=e.clientHeight;o({width:i,height:n})}))}));return i.observe(e),function(){i.disconnect()}}}),[],t),i}},46415:function(t,e,i){var n=i(66332),r=i(4192);t.exports=function(t,e,i){var o=!0,s=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return r(i)&&(o="leading"in i?!!i.leading:o,s="trailing"in i?!!i.trailing:s),n(t,e,{leading:o,maxWait:e,trailing:s})}},47656:function(t,e,i){"use strict";i.d(e,{Mp:function(){return xe},PM:function(){return we}});var n=i(95239),r=i(33743),o=i(48281),s=i(64239),a=i(10511),c=i(94423),l=i(94862),h=i(33801),u=i(45726),f=i(68305),d=i(91161),g=i(68887),p=i(87817),v=i(27569),m=i(9939),y=i(46916);var b="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;function x(t){var e=Object.prototype.toString.call(t);return"[object Window]"===e||"[object global]"===e}function _(t){return"nodeType"in t}function C(t){var e,i;return t?x(t)?t:_(t)&&null!=(e=null==(i=t.ownerDocument)?void 0:i.defaultView)?e:window:window}function S(t){return t instanceof C(t).Document}function w(t){return!x(t)&&t instanceof C(t).HTMLElement}function T(t){return t instanceof C(t).SVGElement}function O(t){return t?x(t)?t.document:_(t)?S(t)?t:w(t)||T(t)?t.ownerDocument:document:document:document}var E=b?m.useLayoutEffect:m.useEffect;function k(t){var e=(0,m.useRef)(t);return E((function(){e.current=t})),(0,m.useCallback)((function(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];return null==e.current?void 0:e.current.apply(e,i)}),[])}function M(t,e){void 0===e&&(e=[t]);var i=(0,m.useRef)(t);return E((function(){i.current!==t&&(i.current=t)}),e),i}function A(t,e){var i=(0,m.useRef)();return(0,m.useMemo)((function(){var e=t(i.current);return i.current=e,e}),(0,u.A)(e))}function D(t){var e=k(t),i=(0,m.useRef)(null),n=(0,m.useCallback)((function(t){t!==i.current&&(null==e||e(t,i.current)),i.current=t}),[]);return[i,n]}function P(t){var e=(0,m.useRef)();return(0,m.useEffect)((function(){e.current=t}),[t]),e.current}var F={};function j(t,e){return(0,m.useMemo)((function(){if(e)return e;var i=null==F[t]?0:F[t]+1;return F[t]=i,t+"-"+i}),[t,e])}function L(t){return function(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return n.reduce((function(e,i){for(var n=0,r=Object.entries(i);n<r.length;n++){var o=(0,v.A)(r[n],2),s=o[0],a=o[1],c=e[s];null!=c&&(e[s]=c+t*a)}return e}),(0,g.A)({},e))}}var I=L(1),R=L(-1);function B(t){if(!t)return!1;var e=C(t.target).KeyboardEvent;return e&&t instanceof e}function W(t){if(function(t){if(!t)return!1;var e=C(t.target).TouchEvent;return e&&t instanceof e}(t)){if(t.touches&&t.touches.length){var e=t.touches[0];return{x:e.clientX,y:e.clientY}}if(t.changedTouches&&t.changedTouches.length){var i=t.changedTouches[0];return{x:i.clientX,y:i.clientY}}}return function(t){return"clientX"in t&&"clientY"in t}(t)?{x:t.clientX,y:t.clientY}:null}var z="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function X(t){return t.matches(z)?t:t.querySelector(z)}var N={display:"none"};function H(t){var e=t.id,i=t.value;return m.createElement("div",{id:e,style:N},i)}function Y(t){var e=t.id,i=t.announcement,n=t.ariaLiveType,r=void 0===n?"assertive":n;return m.createElement("div",{id:e,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},i)}var V=["transform"],U=["id","accessibility","autoScroll","children","sensors","collisionDetection","measuring","modifiers"],G=(0,m.createContext)(null);var q,K={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},J={onDragStart:function(t){return"Picked up draggable item "+t.active.id+"."},onDragOver:function(t){var e=t.active,i=t.over;return i?"Draggable item "+e.id+" was moved over droppable area "+i.id+".":"Draggable item "+e.id+" is no longer over a droppable area."},onDragEnd:function(t){var e=t.active,i=t.over;return i?"Draggable item "+e.id+" was dropped over droppable area "+i.id:"Draggable item "+e.id+" was dropped."},onDragCancel:function(t){return"Dragging was cancelled. Draggable item "+t.active.id+" was dropped."}};function $(t){var e=t.announcements,i=void 0===e?J:e,n=t.container,r=t.hiddenTextDescribedById,o=t.screenReaderInstructions,s=void 0===o?K:o,a=function(){var t=(0,m.useState)(""),e=(0,v.A)(t,2),i=e[0],n=e[1];return{announce:(0,m.useCallback)((function(t){null!=t&&n(t)}),[]),announcement:i}}(),c=a.announce,l=a.announcement,h=j("DndLiveRegion"),u=(0,m.useState)(!1),f=(0,v.A)(u,2),d=f[0],g=f[1];if((0,m.useEffect)((function(){g(!0)}),[]),function(t){var e=(0,m.useContext)(G);(0,m.useEffect)((function(){if(!e)throw new Error("useDndMonitor must be used within a children of <DndContext>");return e(t)}),[t,e])}((0,m.useMemo)((function(){return{onDragStart:function(t){var e=t.active;c(i.onDragStart({active:e}))},onDragMove:function(t){var e=t.active,n=t.over;i.onDragMove&&c(i.onDragMove({active:e,over:n}))},onDragOver:function(t){var e=t.active,n=t.over;c(i.onDragOver({active:e,over:n}))},onDragEnd:function(t){var e=t.active,n=t.over;c(i.onDragEnd({active:e,over:n}))},onDragCancel:function(t){var e=t.active,n=t.over;c(i.onDragCancel({active:e,over:n}))}}}),[c,i])),!d)return null;var p=m.createElement(m.Fragment,null,m.createElement(H,{id:r,value:s.draggable}),m.createElement(Y,{id:h,announcement:l}));return n?(0,y.createPortal)(p,n):p}function Z(){}!function(t){t.DragStart="dragStart",t.DragMove="dragMove",t.DragEnd="dragEnd",t.DragCancel="dragCancel",t.DragOver="dragOver",t.RegisterDroppable="registerDroppable",t.SetDroppableDisabled="setDroppableDisabled",t.UnregisterDroppable="unregisterDroppable"}(q||(q={}));var Q=Object.freeze({x:0,y:0});function tt(t,e){var i=t.data.value;return e.data.value-i}function et(t,e){var i=Math.max(e.top,t.top),n=Math.max(e.left,t.left),r=Math.min(e.left+e.width,t.left+t.width),o=Math.min(e.top+e.height,t.top+t.height),s=r-n,a=o-i;if(n<r&&i<o){var c=e.width*e.height,l=t.width*t.height,h=s*a;return Number((h/(c+l-h)).toFixed(4))}return 0}var it=function(t){var e,i=t.collisionRect,n=t.droppableRects,r=t.droppableContainers,o=[],s=(0,p.A)(r);try{for(s.s();!(e=s.n()).done;){var a=e.value,c=a.id,l=n.get(c);if(l){var h=et(l,i);h>0&&o.push({id:c,data:{droppableContainer:a,value:h}})}}}catch(u){s.e(u)}finally{s.f()}return o.sort(tt)};function nt(t,e){return t&&e?{x:t.left-e.left,y:t.top-e.top}:Q}function rt(t){return function(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return n.reduce((function(e,i){return(0,g.A)((0,g.A)({},e),{},{top:e.top+t*i.y,bottom:e.bottom+t*i.y,left:e.left+t*i.x,right:e.right+t*i.x})}),(0,g.A)({},e))}}var ot=rt(1);function st(t){if(t.startsWith("matrix3d(")){var e=t.slice(9,-1).split(/, /);return{x:+e[12],y:+e[13],scaleX:+e[0],scaleY:+e[5]}}if(t.startsWith("matrix(")){var i=t.slice(7,-1).split(/, /);return{x:+i[4],y:+i[5],scaleX:+i[0],scaleY:+i[3]}}return null}var at,ct={ignoreTransform:!1};function lt(t,e){void 0===e&&(e=ct);var i=t.getBoundingClientRect();if(e.ignoreTransform){var n=C(t).getComputedStyle(t),r=n.transform,o=n.transformOrigin;r&&(i=function(t,e,i){var n=st(e);if(!n)return t;var r=n.scaleX,o=n.scaleY,s=n.x,a=n.y,c=t.left-s-(1-r)*parseFloat(i),l=t.top-a-(1-o)*parseFloat(i.slice(i.indexOf(" ")+1)),h=r?t.width/r:t.width,u=o?t.height/o:t.height;return{width:h,height:u,top:l,right:c+h,bottom:l+u,left:c}}(i,r,o))}var s=i;return{top:s.top,left:s.left,width:s.width,height:s.height,bottom:s.bottom,right:s.right}}function ht(t){return lt(t,{ignoreTransform:!0})}function ut(t,e){var i=[];return t?function n(r){if(null!=e&&i.length>=e)return i;if(!r)return i;if(S(r)&&null!=r.scrollingElement&&!i.includes(r.scrollingElement))return i.push(r.scrollingElement),i;if(!w(r)||T(r))return i;if(i.includes(r))return i;var o=C(t).getComputedStyle(r);return r!==t&&function(t,e){void 0===e&&(e=C(t).getComputedStyle(t));var i=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((function(t){var n=e[t];return"string"===typeof n&&i.test(n)}))}(r,o)&&i.push(r),function(t,e){return void 0===e&&(e=C(t).getComputedStyle(t)),"fixed"===e.position}(r,o)?i:n(r.parentNode)}(t):i}function ft(t){var e=ut(t,1),i=(0,v.A)(e,1)[0];return null!=i?i:null}function dt(t){return b&&t?x(t)?t:_(t)?S(t)||t===O(t).scrollingElement?window:w(t)?t:null:null:null}function gt(t){return x(t)?t.scrollX:t.scrollLeft}function pt(t){return x(t)?t.scrollY:t.scrollTop}function vt(t){return{x:gt(t),y:pt(t)}}function mt(t){return!(!b||!t)&&t===document.scrollingElement}function yt(t){var e={x:0,y:0},i=mt(t)?{height:window.innerHeight,width:window.innerWidth}:{height:t.clientHeight,width:t.clientWidth},n={x:t.scrollWidth-i.width,y:t.scrollHeight-i.height};return{isTop:t.scrollTop<=e.y,isLeft:t.scrollLeft<=e.x,isBottom:t.scrollTop>=n.y,isRight:t.scrollLeft>=n.x,maxScroll:n,minScroll:e}}!function(t){t[t.Forward=1]="Forward",t[t.Backward=-1]="Backward"}(at||(at={}));var bt={x:.2,y:.2};function xt(t,e,i,n,r){var o=i.top,s=i.left,a=i.right,c=i.bottom;void 0===n&&(n=10),void 0===r&&(r=bt);var l=yt(t),h=l.isTop,u=l.isBottom,f=l.isLeft,d=l.isRight,g={x:0,y:0},p={x:0,y:0},v=e.height*r.y,m=e.width*r.x;return!h&&o<=e.top+v?(g.y=at.Backward,p.y=n*Math.abs((e.top+v-o)/v)):!u&&c>=e.bottom-v&&(g.y=at.Forward,p.y=n*Math.abs((e.bottom-v-c)/v)),!d&&a>=e.right-m?(g.x=at.Forward,p.x=n*Math.abs((e.right-m-a)/m)):!f&&s<=e.left+m&&(g.x=at.Backward,p.x=n*Math.abs((e.left+m-s)/m)),{direction:g,speed:p}}function _t(t){if(t===document.scrollingElement){var e=window,i=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:i,bottom:n,width:i,height:n}}var r=t.getBoundingClientRect();return{top:r.top,left:r.left,right:r.right,bottom:r.bottom,width:t.clientWidth,height:t.clientHeight}}function Ct(t){return t.reduce((function(t,e){return I(t,vt(e))}),Q)}function St(t,e){if(void 0===e&&(e=lt),t){var i=e(t),n=i.top,r=i.left,o=i.bottom,s=i.right;ft(t)&&(o<=0||s<=0||n>=window.innerHeight||r>=window.innerWidth)&&t.scrollIntoView({block:"center",inline:"center"})}}var wt,Tt,Ot=[["x",["left","right"],function(t){return t.reduce((function(t,e){return t+gt(e)}),0)}],["y",["top","bottom"],function(t){return t.reduce((function(t,e){return t+pt(e)}),0)}]],Et=(0,f.A)((function t(e,i){var n=this;(0,d.A)(this,t),this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;var r=ut(i),o=Ct(r);this.rect=(0,g.A)({},e),this.width=e.width,this.height=e.height;for(var s=function(){var t,e=(0,v.A)(c[a],3),i=e[0],s=e[1],l=e[2],h=(0,p.A)(s);try{var u=function(){var e=t.value;Object.defineProperty(n,e,{get:function(){var t=l(r),s=o[i]-t;return n.rect[e]+s},enumerable:!0})};for(h.s();!(t=h.n()).done;)u()}catch(f){h.e(f)}finally{h.f()}},a=0,c=Ot;a<c.length;a++)s();Object.defineProperty(this,"rect",{enumerable:!1})})),kt=function(){return(0,f.A)((function t(e){var i=this;(0,d.A)(this,t),this.target=void 0,this.listeners=[],this.removeAll=function(){i.listeners.forEach((function(t){var e,n;return null==(n=i.target)?void 0:(e=n).removeEventListener.apply(e,(0,u.A)(t))}))},this.target=e}),[{key:"add",value:function(t,e,i){var n;null==(n=this.target)||n.addEventListener(t,e,i),this.listeners.push([t,e,i])}}])}();function Mt(t,e){var i=Math.abs(t.x),n=Math.abs(t.y);return"number"===typeof e?Math.sqrt(Math.pow(i,2)+Math.pow(n,2))>e:"x"in e&&"y"in e?i>e.x&&n>e.y:"x"in e?i>e.x:"y"in e&&n>e.y}function At(t){t.preventDefault()}function Dt(t){t.stopPropagation()}!function(t){t.Click="click",t.DragStart="dragstart",t.Keydown="keydown",t.ContextMenu="contextmenu",t.Resize="resize",t.SelectionChange="selectionchange",t.VisibilityChange="visibilitychange"}(wt||(wt={})),function(t){t.Space="Space",t.Down="ArrowDown",t.Right="ArrowRight",t.Left="ArrowLeft",t.Up="ArrowUp",t.Esc="Escape",t.Enter="Enter",t.Tab="Tab"}(Tt||(Tt={}));var Pt={start:[Tt.Space,Tt.Enter],cancel:[Tt.Esc],end:[Tt.Space,Tt.Enter,Tt.Tab]},Ft=function(t,e){var i=e.currentCoordinates;switch(t.code){case Tt.Right:return(0,g.A)((0,g.A)({},i),{},{x:i.x+25});case Tt.Left:return(0,g.A)((0,g.A)({},i),{},{x:i.x-25});case Tt.Down:return(0,g.A)((0,g.A)({},i),{},{y:i.y+25});case Tt.Up:return(0,g.A)((0,g.A)({},i),{},{y:i.y-25})}},jt=function(){return(0,f.A)((function t(e){(0,d.A)(this,t),this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;var i=e.event.target;this.props=e,this.listeners=new kt(O(i)),this.windowListeners=new kt(C(i)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}),[{key:"attach",value:function(){var t=this;this.handleStart(),this.windowListeners.add(wt.Resize,this.handleCancel),this.windowListeners.add(wt.VisibilityChange,this.handleCancel),setTimeout((function(){return t.listeners.add(wt.Keydown,t.handleKeyDown)}))}},{key:"handleStart",value:function(){var t=this.props,e=t.activeNode,i=t.onStart,n=e.node.current;n&&St(n),i(Q)}},{key:"handleKeyDown",value:function(t){if(B(t)){var e=this.props,i=e.active,n=e.context,r=e.options,o=r.keyboardCodes,s=void 0===o?Pt:o,a=r.coordinateGetter,c=void 0===a?Ft:a,l=r.scrollBehavior,h=void 0===l?"smooth":l,u=t.code;if(s.end.includes(u))return void this.handleEnd(t);if(s.cancel.includes(u))return void this.handleCancel(t);var f=n.current.collisionRect,d=f?{x:f.left,y:f.top}:Q;this.referenceCoordinates||(this.referenceCoordinates=d);var g=c(t,{active:i,context:n.current,currentCoordinates:d});if(g){var v,m=R(g,d),y={x:0,y:0},b=n.current.scrollableAncestors,x=(0,p.A)(b);try{for(x.s();!(v=x.n()).done;){var _=v.value,C=t.code,S=yt(_),w=S.isTop,T=S.isRight,O=S.isLeft,E=S.isBottom,k=S.maxScroll,M=S.minScroll,A=_t(_),D={x:Math.min(C===Tt.Right?A.right-A.width/2:A.right,Math.max(C===Tt.Right?A.left:A.left+A.width/2,g.x)),y:Math.min(C===Tt.Down?A.bottom-A.height/2:A.bottom,Math.max(C===Tt.Down?A.top:A.top+A.height/2,g.y))},P=C===Tt.Right&&!T||C===Tt.Left&&!O,F=C===Tt.Down&&!E||C===Tt.Up&&!w;if(P&&D.x!==g.x){var j=_.scrollLeft+m.x,L=C===Tt.Right&&j<=k.x||C===Tt.Left&&j>=M.x;if(L&&!m.y)return void _.scrollTo({left:j,behavior:h});y.x=L?_.scrollLeft-j:C===Tt.Right?_.scrollLeft-k.x:_.scrollLeft-M.x,y.x&&_.scrollBy({left:-y.x,behavior:h});break}if(F&&D.y!==g.y){var W=_.scrollTop+m.y,z=C===Tt.Down&&W<=k.y||C===Tt.Up&&W>=M.y;if(z&&!m.x)return void _.scrollTo({top:W,behavior:h});y.y=z?_.scrollTop-W:C===Tt.Down?_.scrollTop-k.y:_.scrollTop-M.y,y.y&&_.scrollBy({top:-y.y,behavior:h});break}}}catch(X){x.e(X)}finally{x.f()}this.handleMove(t,I(R(g,this.referenceCoordinates),y))}}}},{key:"handleMove",value:function(t,e){var i=this.props.onMove;t.preventDefault(),i(e)}},{key:"handleEnd",value:function(t){var e=this.props.onEnd;t.preventDefault(),this.detach(),e()}},{key:"handleCancel",value:function(t){var e=this.props.onCancel;t.preventDefault(),this.detach(),e()}},{key:"detach",value:function(){this.listeners.removeAll(),this.windowListeners.removeAll()}}])}();function Lt(t){return Boolean(t&&"distance"in t)}function It(t){return Boolean(t&&"delay"in t)}jt.activators=[{eventName:"onKeyDown",handler:function(t,e,i){var n=e.keyboardCodes,r=void 0===n?Pt:n,o=e.onActivation,s=i.active,a=t.nativeEvent.code;if(r.start.includes(a)){var c=s.activatorNode.current;return(!c||t.target===c)&&(t.preventDefault(),null==o||o({event:t.nativeEvent}),!0)}return!1}}];var Rt=function(){return(0,f.A)((function t(e,i,n){var r;(0,d.A)(this,t),void 0===n&&(n=function(t){return t instanceof C(t).EventTarget?t:O(t)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=i;var o=e.event,s=o.target;this.props=e,this.events=i,this.document=O(s),this.documentListeners=new kt(this.document),this.listeners=new kt(n),this.windowListeners=new kt(C(s)),this.initialCoordinates=null!=(r=W(o))?r:Q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}),[{key:"attach",value:function(){var t=this.events,e=this.props.options,i=e.activationConstraint,n=e.bypassActivationConstraint;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(wt.Resize,this.handleCancel),this.windowListeners.add(wt.DragStart,At),this.windowListeners.add(wt.VisibilityChange,this.handleCancel),this.windowListeners.add(wt.ContextMenu,At),this.documentListeners.add(wt.Keydown,this.handleKeydown),i){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(It(i))return this.timeoutId=setTimeout(this.handleStart,i.delay),void this.handlePending(i);if(Lt(i))return void this.handlePending(i)}this.handleStart()}},{key:"detach",value:function(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}},{key:"handlePending",value:function(t,e){var i=this.props,n=i.active;(0,i.onPending)(n,t,this.initialCoordinates,e)}},{key:"handleStart",value:function(){var t=this.initialCoordinates,e=this.props.onStart;t&&(this.activated=!0,this.documentListeners.add(wt.Click,Dt,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(wt.SelectionChange,this.removeTextSelection),e(t))}},{key:"handleMove",value:function(t){var e,i=this.activated,n=this.initialCoordinates,r=this.props,o=r.onMove,s=r.options.activationConstraint;if(n){var a=null!=(e=W(t))?e:Q,c=R(n,a);if(!i&&s){if(Lt(s)){if(null!=s.tolerance&&Mt(c,s.tolerance))return this.handleCancel();if(Mt(c,s.distance))return this.handleStart()}return It(s)&&Mt(c,s.tolerance)?this.handleCancel():void this.handlePending(s,c)}t.cancelable&&t.preventDefault(),o(a)}}},{key:"handleEnd",value:function(){var t=this.props,e=t.onAbort,i=t.onEnd;this.detach(),this.activated||e(this.props.active),i()}},{key:"handleCancel",value:function(){var t=this.props,e=t.onAbort,i=t.onCancel;this.detach(),this.activated||e(this.props.active),i()}},{key:"handleKeydown",value:function(t){t.code===Tt.Esc&&this.handleCancel()}},{key:"removeTextSelection",value:function(){var t;null==(t=this.document.getSelection())||t.removeAllRanges()}}])}(),Bt={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}},Wt=function(t){function e(t){(0,d.A)(this,e);var i=O(t.event.target);return(0,l.A)(this,e,[t,Bt,i])}return(0,h.A)(e,t),(0,f.A)(e)}(Rt);Wt.activators=[{eventName:"onPointerDown",handler:function(t,e){var i=t.nativeEvent,n=e.onActivation;return!(!i.isPrimary||0!==i.button)&&(null==n||n({event:i}),!0)}}];var zt,Xt={move:{name:"mousemove"},end:{name:"mouseup"}};!function(t){t[t.RightClick=2]="RightClick"}(zt||(zt={})),(function(t){function e(t){return(0,d.A)(this,e),(0,l.A)(this,e,[t,Xt,O(t.event.target)])}return(0,h.A)(e,t),(0,f.A)(e)}(Rt)).activators=[{eventName:"onMouseDown",handler:function(t,e){var i=t.nativeEvent,n=e.onActivation;return i.button!==zt.RightClick&&(null==n||n({event:i}),!0)}}];var Nt,Ht,Yt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}},Vt=function(t){function e(t){return(0,d.A)(this,e),(0,l.A)(this,e,[t,Yt])}return(0,h.A)(e,t),(0,f.A)(e,null,[{key:"setup",value:function(){return window.addEventListener(Yt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(Yt.move.name,t)};function t(){}}}])}(Rt);function Ut(t){var e=t.acceleration,i=t.activator,n=void 0===i?Nt.Pointer:i,r=t.canScroll,o=t.draggingRect,s=t.enabled,a=t.interval,l=void 0===a?5:a,h=t.order,f=void 0===h?Ht.TreeOrder:h,d=t.pointerCoordinates,g=t.scrollableAncestors,y=t.scrollableAncestorRects,b=t.delta,x=t.threshold,_=function(t){var e=t.delta,i=t.disabled,n=P(e);return A((function(t){if(i||!n||!t)return Kt;var r={x:Math.sign(e.x-n.x),y:Math.sign(e.y-n.y)};return{x:(0,c.A)((0,c.A)({},at.Backward,t.x[at.Backward]||-1===r.x),at.Forward,t.x[at.Forward]||1===r.x),y:(0,c.A)((0,c.A)({},at.Backward,t.y[at.Backward]||-1===r.y),at.Forward,t.y[at.Forward]||1===r.y)}}),[i,e,n])}({delta:b,disabled:!s}),C=function(){var t=(0,m.useRef)(null);return[(0,m.useCallback)((function(e,i){t.current=setInterval(e,i)}),[]),(0,m.useCallback)((function(){null!==t.current&&(clearInterval(t.current),t.current=null)}),[])]}(),S=(0,v.A)(C,2),w=S[0],T=S[1],O=(0,m.useRef)({x:0,y:0}),E=(0,m.useRef)({x:0,y:0}),k=(0,m.useMemo)((function(){switch(n){case Nt.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case Nt.DraggableRect:return o}}),[n,o,d]),M=(0,m.useRef)(null),D=(0,m.useCallback)((function(){var t=M.current;if(t){var e=O.current.x*E.current.x,i=O.current.y*E.current.y;t.scrollBy(e,i)}}),[]),F=(0,m.useMemo)((function(){return f===Ht.TreeOrder?(0,u.A)(g).reverse():g}),[f,g]);(0,m.useEffect)((function(){if(s&&g.length&&k){var t,i=(0,p.A)(F);try{for(i.s();!(t=i.n()).done;){var n=t.value;if(!1!==(null==r?void 0:r(n))){var o=g.indexOf(n),a=y[o];if(a){for(var c=xt(n,a,k,e,x),h=c.direction,u=c.speed,f=0,d=["x","y"];f<d.length;f++){var v=d[f];_[v][h[v]]||(u[v]=0,h[v]=0)}if(u.x>0||u.y>0)return T(),M.current=n,w(D,l),O.current=u,void(E.current=h)}}}}catch(m){i.e(m)}finally{i.f()}O.current={x:0,y:0},E.current={x:0,y:0},T()}else T()}),[e,D,r,T,s,l,JSON.stringify(k),JSON.stringify(_),w,g,F,y,JSON.stringify(x)])}Vt.activators=[{eventName:"onTouchStart",handler:function(t,e){var i=t.nativeEvent,n=e.onActivation;return!(i.touches.length>1)&&(null==n||n({event:i}),!0)}}],function(t){t[t.Pointer=0]="Pointer",t[t.DraggableRect=1]="DraggableRect"}(Nt||(Nt={})),function(t){t[t.TreeOrder=0]="TreeOrder",t[t.ReversedTreeOrder=1]="ReversedTreeOrder"}(Ht||(Ht={}));var Gt,qt,Kt={x:(0,c.A)((0,c.A)({},at.Backward,!1),at.Forward,!1),y:(0,c.A)((0,c.A)({},at.Backward,!1),at.Forward,!1)};!function(t){t[t.Always=0]="Always",t[t.BeforeDragging=1]="BeforeDragging",t[t.WhileDragging=2]="WhileDragging"}(Gt||(Gt={})),function(t){t.Optimized="optimized"}(qt||(qt={}));var Jt=new Map;function $t(t,e){return A((function(i){return t?i||("function"===typeof e?e(t):t):null}),[e,t])}function Zt(t){var e=t.callback,i=t.disabled,n=k(e),r=(0,m.useMemo)((function(){if(!i&&"undefined"!==typeof window&&"undefined"!==typeof window.ResizeObserver)return new(0,window.ResizeObserver)(n)}),[i]);return(0,m.useEffect)((function(){return function(){return null==r?void 0:r.disconnect()}}),[r]),r}function Qt(t){return new Et(lt(t),t)}function te(t,e,i){void 0===e&&(e=Qt);var n=(0,m.useState)(null),r=(0,v.A)(n,2),o=r[0],s=r[1];function a(){s((function(n){if(!t)return null;var r;if(!1===t.isConnected)return null!=(r=null!=n?n:i)?r:null;var o=e(t);return JSON.stringify(n)===JSON.stringify(o)?n:o}))}var c=function(t){var e=t.callback,i=t.disabled,n=k(e),r=(0,m.useMemo)((function(){if(!i&&"undefined"!==typeof window&&"undefined"!==typeof window.MutationObserver)return new(0,window.MutationObserver)(n)}),[n,i]);return(0,m.useEffect)((function(){return function(){return null==r?void 0:r.disconnect()}}),[r]),r}({callback:function(e){if(t){var i,n=(0,p.A)(e);try{for(n.s();!(i=n.n()).done;){var r=i.value,o=r.type,s=r.target;if("childList"===o&&s instanceof HTMLElement&&s.contains(t)){a();break}}}catch(c){n.e(c)}finally{n.f()}}}}),l=Zt({callback:a});return E((function(){a(),t?(null==l||l.observe(t),null==c||c.observe(document.body,{childList:!0,subtree:!0})):(null==l||l.disconnect(),null==c||c.disconnect())}),[t]),o}var ee=[];function ie(t,e){void 0===e&&(e=[]);var i=(0,m.useRef)(null);return(0,m.useEffect)((function(){i.current=null}),e),(0,m.useEffect)((function(){var e=t!==Q;e&&!i.current&&(i.current=t),!e&&i.current&&(i.current=null)}),[t]),i.current?R(t,i.current):Q}function ne(t){return(0,m.useMemo)((function(){return t?function(t){var e=t.innerWidth,i=t.innerHeight;return{top:0,left:0,right:e,bottom:i,width:e,height:i}}(t):null}),[t])}var re=[];function oe(t){if(!t)return null;if(t.children.length>1)return t;var e=t.children[0];return w(e)?e:t}var se=[{sensor:Wt,options:{}},{sensor:jt,options:{}}],ae={current:{}},ce={draggable:{measure:ht},droppable:{measure:ht,strategy:Gt.WhileDragging,frequency:qt.Optimized},dragOverlay:{measure:lt}},le=function(t){function e(){return(0,d.A)(this,e),(0,l.A)(this,e,arguments)}return(0,h.A)(e,t),(0,f.A)(e,[{key:"get",value:function(t){var i;return null!=t&&null!=(i=(0,s.A)(e,"get",this,3)([t]))?i:void 0}},{key:"toArray",value:function(){return Array.from(this.values())}},{key:"getEnabled",value:function(){return this.toArray().filter((function(t){return!t.disabled}))}},{key:"getNodeFor",value:function(t){var e,i;return null!=(e=null==(i=this.get(t))?void 0:i.node.current)?e:void 0}}])}((0,a.A)(Map)),he={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new le,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Z},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:ce,measureDroppableContainers:Z,windowRect:null,measuringScheduled:!1},ue={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Z,draggableNodes:new Map,over:null,measureDroppableContainers:Z},fe=(0,m.createContext)(ue),de=(0,m.createContext)(he);function ge(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new le}}}function pe(t,e){switch(e.type){case q.DragStart:return(0,g.A)((0,g.A)({},t),{},{draggable:(0,g.A)((0,g.A)({},t.draggable),{},{initialCoordinates:e.initialCoordinates,active:e.active})});case q.DragMove:return null==t.draggable.active?t:(0,g.A)((0,g.A)({},t),{},{draggable:(0,g.A)((0,g.A)({},t.draggable),{},{translate:{x:e.coordinates.x-t.draggable.initialCoordinates.x,y:e.coordinates.y-t.draggable.initialCoordinates.y}})});case q.DragEnd:case q.DragCancel:return(0,g.A)((0,g.A)({},t),{},{draggable:(0,g.A)((0,g.A)({},t.draggable),{},{active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}})});case q.RegisterDroppable:var i=e.element,n=i.id,r=new le(t.droppable.containers);return r.set(n,i),(0,g.A)((0,g.A)({},t),{},{droppable:(0,g.A)((0,g.A)({},t.droppable),{},{containers:r})});case q.SetDroppableDisabled:var o=e.id,s=e.key,a=e.disabled,c=t.droppable.containers.get(o);if(!c||s!==c.key)return t;var l=new le(t.droppable.containers);return l.set(o,(0,g.A)((0,g.A)({},c),{},{disabled:a})),(0,g.A)((0,g.A)({},t),{},{droppable:(0,g.A)((0,g.A)({},t.droppable),{},{containers:l})});case q.UnregisterDroppable:var h=e.id,u=e.key,f=t.droppable.containers.get(h);if(!f||u!==f.key)return t;var d=new le(t.droppable.containers);return d.delete(h),(0,g.A)((0,g.A)({},t),{},{droppable:(0,g.A)((0,g.A)({},t.droppable),{},{containers:d})});default:return t}}function ve(t){var e=t.disabled,i=(0,m.useContext)(fe),n=i.active,r=i.activatorEvent,o=i.draggableNodes,s=P(r),a=P(null==n?void 0:n.id);return(0,m.useEffect)((function(){if(!e&&!r&&s&&null!=a){if(!B(s))return;if(document.activeElement===s.target)return;var t=o.get(a);if(!t)return;var i=t.activatorNode,n=t.node;if(!i.current&&!n.current)return;requestAnimationFrame((function(){for(var t=0,e=[i.current,n.current];t<e.length;t++){var r=e[t];if(r){var o=X(r);if(o){o.focus();break}}}}))}}),[r,e,o,a,s]),null}function me(t,e){var i=e.transform,n=(0,o.A)(e,V);return null!=t&&t.length?t.reduce((function(t,e){return e((0,g.A)({transform:t},n))}),i):i}var ye,be=(0,m.createContext)((0,g.A)((0,g.A)({},Q),{},{scaleX:1,scaleY:1}));!function(t){t[t.Uninitialized=0]="Uninitialized",t[t.Initializing=1]="Initializing",t[t.Initialized=2]="Initialized"}(ye||(ye={}));var xe=(0,m.memo)((function(t){var e,i,s,a,c,l=t.id,h=t.accessibility,f=t.autoScroll,d=void 0===f||f,x=t.children,_=t.sensors,S=void 0===_?se:_,T=t.collisionDetection,O=void 0===T?it:T,k=t.measuring,P=t.modifiers,F=(0,o.A)(t,U),L=(0,m.useReducer)(pe,void 0,ge),R=(0,v.A)(L,2),B=R[0],z=R[1],X=function(){var t=(0,m.useState)((function(){return new Set})),e=(0,v.A)(t,1)[0],i=(0,m.useCallback)((function(t){return e.add(t),function(){return e.delete(t)}}),[e]);return[(0,m.useCallback)((function(t){var i=t.type,n=t.event;e.forEach((function(t){var e;return null==(e=t[i])?void 0:e.call(t,n)}))}),[e]),i]}(),N=(0,v.A)(X,2),H=N[0],Y=N[1],V=(0,m.useState)(ye.Uninitialized),K=(0,v.A)(V,2),J=K[0],Z=K[1],tt=J===ye.Initialized,et=B.draggable,rt=et.active,st=et.nodes,at=et.translate,ct=B.droppable.containers,ht=null!=rt?st.get(rt):null,gt=(0,m.useRef)({initial:null,translated:null}),pt=(0,m.useMemo)((function(){var t;return null!=rt?{id:rt,data:null!=(t=null==ht?void 0:ht.data)?t:ae,rect:gt}:null}),[rt,ht]),yt=(0,m.useRef)(null),bt=(0,m.useState)(null),xt=(0,v.A)(bt,2),_t=xt[0],St=xt[1],wt=(0,m.useState)(null),Tt=(0,v.A)(wt,2),Ot=Tt[0],kt=Tt[1],Mt=M(F,Object.values(F)),At=j("DndDescribedBy",l),Dt=(0,m.useMemo)((function(){return ct.getEnabled()}),[ct]),Pt=(c=k,(0,m.useMemo)((function(){return{draggable:(0,g.A)((0,g.A)({},ce.draggable),null==c?void 0:c.draggable),droppable:(0,g.A)((0,g.A)({},ce.droppable),null==c?void 0:c.droppable),dragOverlay:(0,g.A)((0,g.A)({},ce.dragOverlay),null==c?void 0:c.dragOverlay)}}),[null==c?void 0:c.draggable,null==c?void 0:c.droppable,null==c?void 0:c.dragOverlay])),Ft=function(t,e){var i=e.dragging,n=e.dependencies,r=e.config,o=(0,m.useState)(null),s=(0,v.A)(o,2),a=s[0],c=s[1],l=r.frequency,h=r.measure,f=r.strategy,d=(0,m.useRef)(t),g=function(){switch(f){case Gt.Always:return!1;case Gt.BeforeDragging:return i;default:return!i}}(),y=M(g),b=(0,m.useCallback)((function(t){void 0===t&&(t=[]),y.current||c((function(e){return null===e?t:e.concat(t.filter((function(t){return!e.includes(t)})))}))}),[y]),x=(0,m.useRef)(null),_=A((function(e){if(g&&!i)return Jt;if(!e||e===Jt||d.current!==t||null!=a){var n,r=new Map,o=(0,p.A)(t);try{for(o.s();!(n=o.n()).done;){var s=n.value;if(s)if(a&&a.length>0&&!a.includes(s.id)&&s.rect.current)r.set(s.id,s.rect.current);else{var c=s.node.current,l=c?new Et(h(c),c):null;s.rect.current=l,l&&r.set(s.id,l)}}}catch(u){o.e(u)}finally{o.f()}return r}return e}),[t,a,i,g,h]);return(0,m.useEffect)((function(){d.current=t}),[t]),(0,m.useEffect)((function(){g||b()}),[i,g]),(0,m.useEffect)((function(){a&&a.length>0&&c(null)}),[JSON.stringify(a)]),(0,m.useEffect)((function(){g||"number"!==typeof l||null!==x.current||(x.current=setTimeout((function(){b(),x.current=null}),l))}),[l,g,b].concat((0,u.A)(n))),{droppableRects:_,measureDroppableContainers:b,measuringScheduled:null!=a}}(Dt,{dragging:tt,dependencies:[at.x,at.y],config:Pt.droppable}),jt=Ft.droppableRects,Lt=Ft.measureDroppableContainers,It=Ft.measuringScheduled,Rt=function(t,e){var i=null!=e?t.get(e):void 0,n=i?i.node.current:null;return A((function(t){var i;return null==e?null:null!=(i=null!=n?n:t)?i:null}),[n,e])}(st,rt),Bt=(0,m.useMemo)((function(){return Ot?W(Ot):null}),[Ot]),Wt=function(){var t=!1===(null==_t?void 0:_t.autoScrollEnabled),e="object"===typeof d?!1===d.enabled:!1===d,i=tt&&!t&&!e;if("object"===typeof d)return(0,g.A)((0,g.A)({},d),{},{enabled:i});return{enabled:i}}(),zt=function(t,e){return $t(t,e)}(Rt,Pt.draggable.measure);!function(t){var e=t.activeNode,i=t.measure,n=t.initialRect,r=t.config,o=void 0===r||r,s=(0,m.useRef)(!1),a="boolean"===typeof o?{x:o,y:o}:o,c=a.x,l=a.y;E((function(){if((c||l)&&e){if(!s.current&&n){var t=null==e?void 0:e.node.current;if(t&&!1!==t.isConnected){var r=nt(i(t),n);if(c||(r.x=0),l||(r.y=0),s.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){var o=ft(t);o&&o.scrollBy({top:r.y,left:r.x})}}}}else s.current=!1}),[e,c,l,n,i])}({activeNode:null!=rt?st.get(rt):null,config:Wt.layoutShiftCompensation,initialRect:zt,measure:Pt.draggable.measure});var Xt,Nt=te(Rt,Pt.draggable.measure,zt),Ht=te(Rt?Rt.parentElement:null),Yt=(0,m.useRef)({activatorEvent:null,active:null,activeNode:Rt,collisionRect:null,collisions:null,droppableRects:jt,draggableNodes:st,draggingNode:null,draggingNodeRect:null,droppableContainers:ct,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Vt=ct.getNodeFor(null==(e=Yt.current.over)?void 0:e.id),qt=function(t){var e=t.measure,i=(0,m.useState)(null),n=(0,v.A)(i,2),r=n[0],o=n[1],s=Zt({callback:(0,m.useCallback)((function(t){var i,n=(0,p.A)(t);try{var r=function(){var t=i.value.target;if(w(t))return o((function(i){var n=e(t);return i?(0,g.A)((0,g.A)({},i),{},{width:n.width,height:n.height}):n})),1};for(n.s();!(i=n.n()).done&&!r(););}catch(s){n.e(s)}finally{n.f()}}),[e])}),a=D((0,m.useCallback)((function(t){var i=oe(t);null==s||s.disconnect(),i&&(null==s||s.observe(i)),o(i?e(i):null)}),[e,s])),c=(0,v.A)(a,2),l=c[0],h=c[1];return(0,m.useMemo)((function(){return{nodeRef:l,rect:r,setRef:h}}),[r,l,h])}({measure:Pt.dragOverlay.measure}),Kt=null!=(i=qt.nodeRef.current)?i:Rt,Qt=tt?null!=(s=qt.rect)?s:Nt:null,le=Boolean(qt.nodeRef.current&&qt.rect),he=nt(Xt=le?null:Nt,$t(Xt)),ue=ne(Kt?C(Kt):null),xe=function(t){var e=(0,m.useRef)(t),i=A((function(i){return t?i&&i!==ee&&t&&e.current&&t.parentNode===e.current.parentNode?i:ut(t):ee}),[t]);return(0,m.useEffect)((function(){e.current=t}),[t]),i}(tt?null!=Vt?Vt:Rt:null),_e=function(t,e){void 0===e&&(e=lt);var i=(0,v.A)(t,1)[0],n=ne(i?C(i):null),r=(0,m.useState)(re),o=(0,v.A)(r,2),s=o[0],a=o[1];function c(){a((function(){return t.length?t.map((function(t){return mt(t)?n:new Et(e(t),t)})):re}))}var l=Zt({callback:c});return E((function(){null==l||l.disconnect(),c(),t.forEach((function(t){return null==l?void 0:l.observe(t)}))}),[t]),s}(xe),Ce=me(P,{transform:{x:at.x-he.x,y:at.y-he.y,scaleX:1,scaleY:1},activatorEvent:Ot,active:pt,activeNodeRect:Nt,containerNodeRect:Ht,draggingNodeRect:Qt,over:Yt.current.over,overlayNodeRect:qt.rect,scrollableAncestors:xe,scrollableAncestorRects:_e,windowRect:ue}),Se=Bt?I(Bt,at):null,we=function(t){var e=(0,m.useState)(null),i=(0,v.A)(e,2),n=i[0],r=i[1],o=(0,m.useRef)(t),s=(0,m.useCallback)((function(t){var e=dt(t.target);e&&r((function(t){return t?(t.set(e,vt(e)),new Map(t)):null}))}),[]);return(0,m.useEffect)((function(){var e=o.current;if(t!==e){n(e);var i=t.map((function(t){var e=dt(t);return e?(e.addEventListener("scroll",s,{passive:!0}),[e,vt(e)]):null})).filter((function(t){return null!=t}));r(i.length?new Map(i):null),o.current=t}return function(){n(t),n(e)};function n(t){t.forEach((function(t){var e=dt(t);null==e||e.removeEventListener("scroll",s)}))}}),[s,t]),(0,m.useMemo)((function(){return t.length?n?Array.from(n.values()).reduce((function(t,e){return I(t,e)}),Q):Ct(t):Q}),[t,n])}(xe),Te=ie(we),Oe=ie(we,[Nt]),Ee=I(Ce,Te),ke=Qt?ot(Qt,Ce):null,Me=pt&&ke?O({active:pt,collisionRect:ke,droppableRects:jt,droppableContainers:Dt,pointerCoordinates:Se}):null,Ae=function(t,e){if(!t||0===t.length)return null;var i=(0,v.A)(t,1)[0];return e?i[e]:i}(Me,"id"),De=(0,m.useState)(null),Pe=(0,v.A)(De,2),Fe=Pe[0],je=Pe[1],Le=function(t,e,i){return(0,g.A)((0,g.A)({},t),{},{scaleX:e&&i?e.width/i.width:1,scaleY:e&&i?e.height/i.height:1})}(le?Ce:I(Ce,Oe),null!=(a=null==Fe?void 0:Fe.rect)?a:null,Nt),Ie=(0,m.useRef)(null),Re=(0,m.useCallback)((function(t,e){var i=e.sensor,o=e.options;if(null!=yt.current){var s=st.get(yt.current);if(s){var a=t.nativeEvent,c=new i({active:yt.current,activeNode:s,event:a,options:o,context:Yt,onAbort:function(t){if(st.get(t)){var e=Mt.current.onDragAbort,i={id:t};null==e||e(i),H({type:"onDragAbort",event:i})}},onPending:function(t,e,i,n){if(st.get(t)){var r=Mt.current.onDragPending,o={id:t,constraint:e,initialCoordinates:i,offset:n};null==r||r(o),H({type:"onDragPending",event:o})}},onStart:function(t){var e=yt.current;if(null!=e){var i=st.get(e);if(i){var n=Mt.current.onDragStart,r={activatorEvent:a,active:{id:e,data:i.data,rect:gt}};(0,y.unstable_batchedUpdates)((function(){null==n||n(r),Z(ye.Initializing),z({type:q.DragStart,initialCoordinates:t,active:e}),H({type:"onDragStart",event:r}),St(Ie.current),kt(a)}))}}},onMove:function(t){z({type:q.DragMove,coordinates:t})},onEnd:l(q.DragEnd),onCancel:l(q.DragCancel)});Ie.current=c}}function l(t){return function(){var e=(0,r.A)((0,n.A)().mark((function e(){var i,r,o,s,c,l,h;return(0,n.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=Yt.current,r=i.active,o=i.collisions,s=i.over,c=i.scrollAdjustedTranslate,l=null,!r||!c){e.next=10;break}if(h=Mt.current.cancelDrop,l={activatorEvent:a,active:r,collisions:o,delta:c,over:s},t!==q.DragEnd||"function"!==typeof h){e.next=10;break}return e.next=8,Promise.resolve(h(l));case 8:e.sent&&(t=q.DragCancel);case 10:yt.current=null,(0,y.unstable_batchedUpdates)((function(){z({type:t}),Z(ye.Uninitialized),je(null),St(null),kt(null),Ie.current=null;var e=t===q.DragEnd?"onDragEnd":"onDragCancel";if(l){var i=Mt.current[e];null==i||i(l),H({type:e,event:l})}}));case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()}}),[st]),Be=(0,m.useCallback)((function(t,e){return function(i,n){var r=i.nativeEvent,o=st.get(n);if(null===yt.current&&o&&!r.dndKit&&!r.defaultPrevented){var s={active:o};!0===t(i,e.options,s)&&(r.dndKit={capturedBy:e.sensor},yt.current=n,Re(i,e))}}}),[st,Re]),We=function(t,e){return(0,m.useMemo)((function(){return t.reduce((function(t,i){var n=i.sensor.activators.map((function(t){return{eventName:t.eventName,handler:e(t.handler,i)}}));return[].concat((0,u.A)(t),(0,u.A)(n))}),[])}),[t,e])}(S,Be);!function(t){(0,m.useEffect)((function(){if(b){var e=t.map((function(t){var e=t.sensor;return null==e.setup?void 0:e.setup()}));return function(){var t,i=(0,p.A)(e);try{for(i.s();!(t=i.n()).done;){var n=t.value;null==n||n()}}catch(r){i.e(r)}finally{i.f()}}}}),t.map((function(t){return t.sensor})))}(S),E((function(){Nt&&J===ye.Initializing&&Z(ye.Initialized)}),[Nt,J]),(0,m.useEffect)((function(){var t=Mt.current.onDragMove,e=Yt.current,i=e.active,n=e.activatorEvent,r=e.collisions,o=e.over;if(i&&n){var s={active:i,activatorEvent:n,collisions:r,delta:{x:Ee.x,y:Ee.y},over:o};(0,y.unstable_batchedUpdates)((function(){null==t||t(s),H({type:"onDragMove",event:s})}))}}),[Ee.x,Ee.y]),(0,m.useEffect)((function(){var t=Yt.current,e=t.active,i=t.activatorEvent,n=t.collisions,r=t.droppableContainers,o=t.scrollAdjustedTranslate;if(e&&null!=yt.current&&i&&o){var s=Mt.current.onDragOver,a=r.get(Ae),c=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,l={active:e,activatorEvent:i,collisions:n,delta:{x:o.x,y:o.y},over:c};(0,y.unstable_batchedUpdates)((function(){je(c),null==s||s(l),H({type:"onDragOver",event:l})}))}}),[Ae]),E((function(){Yt.current={activatorEvent:Ot,active:pt,activeNode:Rt,collisionRect:ke,collisions:Me,droppableRects:jt,draggableNodes:st,draggingNode:Kt,draggingNodeRect:Qt,droppableContainers:ct,over:Fe,scrollableAncestors:xe,scrollAdjustedTranslate:Ee},gt.current={initial:Qt,translated:ke}}),[pt,Rt,Me,ke,st,Kt,Qt,jt,ct,Fe,xe,Ee]),Ut((0,g.A)((0,g.A)({},Wt),{},{delta:at,draggingRect:ke,pointerCoordinates:Se,scrollableAncestors:xe,scrollableAncestorRects:_e}));var ze=(0,m.useMemo)((function(){return{active:pt,activeNode:Rt,activeNodeRect:Nt,activatorEvent:Ot,collisions:Me,containerNodeRect:Ht,dragOverlay:qt,draggableNodes:st,droppableContainers:ct,droppableRects:jt,over:Fe,measureDroppableContainers:Lt,scrollableAncestors:xe,scrollableAncestorRects:_e,measuringConfiguration:Pt,measuringScheduled:It,windowRect:ue}}),[pt,Rt,Nt,Ot,Me,Ht,qt,st,ct,jt,Fe,Lt,xe,_e,Pt,It,ue]),Xe=(0,m.useMemo)((function(){return{activatorEvent:Ot,activators:We,active:pt,activeNodeRect:Nt,ariaDescribedById:{draggable:At},dispatch:z,draggableNodes:st,over:Fe,measureDroppableContainers:Lt}}),[Ot,We,pt,Nt,z,At,st,Fe,Lt]);return m.createElement(G.Provider,{value:Y},m.createElement(fe.Provider,{value:Xe},m.createElement(de.Provider,{value:ze},m.createElement(be.Provider,{value:Le},x)),m.createElement(ve,{disabled:!1===(null==h?void 0:h.restoreFocus)})),m.createElement($,(0,g.A)((0,g.A)({},h),{},{hiddenTextDescribedById:At})))})),_e=(0,m.createContext)(null),Ce="button",Se="Draggable";function we(t){var e=t.id,i=t.data,n=t.disabled,r=void 0!==n&&n,o=t.attributes,s=j(Se),a=(0,m.useContext)(fe),c=a.activators,l=a.activatorEvent,h=a.active,u=a.activeNodeRect,f=a.ariaDescribedById,d=a.draggableNodes,g=a.over,p=null!=o?o:{},y=p.role,b=void 0===y?Ce:y,x=p.roleDescription,_=void 0===x?"draggable":x,C=p.tabIndex,S=void 0===C?0:C,w=(null==h?void 0:h.id)===e,T=(0,m.useContext)(w?be:_e),O=D(),k=(0,v.A)(O,2),A=k[0],P=k[1],F=D(),L=(0,v.A)(F,2),I=L[0],R=L[1],B=function(t,e){return(0,m.useMemo)((function(){return t.reduce((function(t,i){var n=i.eventName,r=i.handler;return t[n]=function(t){r(t,e)},t}),{})}),[t,e])}(c,e),W=M(i);return E((function(){return d.set(e,{id:e,key:s,node:A,activatorNode:I,data:W}),function(){var t=d.get(e);t&&t.key===s&&d.delete(e)}}),[d,e]),{active:h,activatorEvent:l,activeNodeRect:u,attributes:(0,m.useMemo)((function(){return{role:b,tabIndex:S,"aria-disabled":r,"aria-pressed":!(!w||b!==Ce)||void 0,"aria-roledescription":_,"aria-describedby":f.draggable}}),[r,b,S,w,_,f.draggable]),isDragging:w,listeners:r?void 0:B,node:A,over:g,setNodeRef:P,setActivatorNodeRef:R,transform:T}}},51894:function(t,e,i){"use strict";var n=i(9939);e.A=function(){return n.createElement("svg",{width:"254",height:"294"},n.createElement("title",null,"Server Error"),n.createElement("defs",null,n.createElement("path",{d:"M0 .335h253.49v253.49H0z"}),n.createElement("path",{d:"M0 293.665h253.49V.401H0z"})),n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("g",{transform:"translate(0 .067)"},n.createElement("mask",{fill:"#fff"}),n.createElement("path",{d:"M0 128.134v-2.11C0 56.608 56.273.334 125.69.334h2.11c69.416 0 125.69 56.274 125.69 125.69v2.11c0 69.417-56.274 125.69-125.69 125.69h-2.11C56.273 253.824 0 197.551 0 128.134",fill:"#E4EBF7",mask:"url(#b)"})),n.createElement("path",{d:"M39.989 132.108a8.332 8.332 0 1 1-16.581-1.671 8.332 8.332 0 0 1 16.58 1.671",fill:"#FFF"}),n.createElement("path",{d:"M37.19 135.59l10.553 5.983M48.665 147.884l-12.734 10.861",stroke:"#FFF",strokeWidth:"2"}),n.createElement("path",{d:"M40.11 160.816a5.706 5.706 0 1 1-11.354-1.145 5.706 5.706 0 0 1 11.354 1.145M57.943 144.6a5.747 5.747 0 1 1-11.436-1.152 5.747 5.747 0 0 1 11.436 1.153M99.656 27.434l30.024-.013a4.619 4.619 0 1 0-.004-9.238l-30.024.013a4.62 4.62 0 0 0 .004 9.238M111.14 45.896l30.023-.013a4.62 4.62 0 1 0-.004-9.238l-30.024.013a4.619 4.619 0 1 0 .004 9.238",fill:"#FFF"}),n.createElement("path",{d:"M113.53 27.421v-.002l15.89-.007a4.619 4.619 0 1 0 .005 9.238l-15.892.007v-.002a4.618 4.618 0 0 0-.004-9.234M150.167 70.091h-3.979a4.789 4.789 0 0 1-4.774-4.775 4.788 4.788 0 0 1 4.774-4.774h3.979a4.789 4.789 0 0 1 4.775 4.774 4.789 4.789 0 0 1-4.775 4.775",fill:"#FFF"}),n.createElement("path",{d:"M171.687 30.234c0-16.392 13.289-29.68 29.681-29.68 16.392 0 29.68 13.288 29.68 29.68 0 16.393-13.288 29.681-29.68 29.681s-29.68-13.288-29.68-29.68",fill:"#FF603B"}),n.createElement("path",{d:"M203.557 19.435l-.676 15.035a1.514 1.514 0 0 1-3.026 0l-.675-15.035a2.19 2.19 0 1 1 4.377 0m-.264 19.378c.513.477.77 1.1.77 1.87s-.257 1.393-.77 1.907c-.55.476-1.21.733-1.943.733a2.545 2.545 0 0 1-1.87-.77c-.55-.514-.806-1.136-.806-1.87 0-.77.256-1.393.806-1.87.513-.513 1.137-.733 1.87-.733.77 0 1.43.22 1.943.733",fill:"#FFF"}),n.createElement("path",{d:"M119.3 133.275c4.426-.598 3.612-1.204 4.079-4.778.675-5.18-3.108-16.935-8.262-25.118-1.088-10.72-12.598-11.24-12.598-11.24s4.312 4.895 4.196 16.199c1.398 5.243.804 14.45.804 14.45s5.255 11.369 11.78 10.487",fill:"#FFB594"}),n.createElement("path",{d:"M100.944 91.61s1.463-.583 3.211.582c8.08 1.398 10.368 6.706 11.3 11.368 1.864 1.282 1.864 2.33 1.864 3.496.365.777 1.515 3.03 1.515 3.03s-7.225 1.748-10.954 6.758c-1.399-6.41-6.936-25.235-6.936-25.235",fill:"#FFF"}),n.createElement("path",{d:"M94.008 90.5l1.019-5.815-9.23-11.874-5.233 5.581-2.593 9.863s8.39 5.128 16.037 2.246",fill:"#FFB594"}),n.createElement("path",{d:"M82.931 78.216s-4.557-2.868-2.445-6.892c1.632-3.107 4.537 1.139 4.537 1.139s.524-3.662 3.139-3.662c.523-1.046 1.569-4.184 1.569-4.184s11.507 2.615 13.6 3.138c-.001 5.23-2.317 19.529-7.884 19.969-8.94.706-12.516-9.508-12.516-9.508",fill:"#FFC6A0"}),n.createElement("path",{d:"M102.971 72.243c2.616-2.093 3.489-9.775 3.489-9.775s-2.492-.492-6.676-2.062c-4.708-2.092-12.867-4.771-17.575.982-9.54 4.41-2.062 19.93-2.062 19.93l2.729-3.037s-3.956-3.304-2.092-6.277c2.183-3.48 3.943 1.08 3.943 1.08s.64-2.4 3.6-3.36c.356-.714 1.04-2.69 1.44-3.872a1.08 1.08 0 0 1 1.27-.707c2.41.56 8.723 2.03 11.417 2.676.524.126.876.619.825 1.156l-.308 3.266z",fill:"#520038"}),n.createElement("path",{d:"M101.22 76.514c-.104.613-.585 1.044-1.076.96-.49-.082-.805-.646-.702-1.26.104-.613.585-1.044 1.076-.961.491.083.805.647.702 1.26M94.26 75.074c-.104.613-.585 1.044-1.076.96-.49-.082-.805-.646-.702-1.26.104-.613.585-1.044 1.076-.96.491.082.805.646.702 1.26",fill:"#552950"}),n.createElement("path",{stroke:"#DB836E",strokeWidth:"1.063",strokeLinecap:"round",strokeLinejoin:"round",d:"M99.206 73.644l-.9 1.62-.3 4.38h-2.24"}),n.createElement("path",{d:"M99.926 73.284s1.8-.72 2.52.54",stroke:"#5C2552",strokeWidth:"1.117",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M81.367 73.084s.48-1.12 1.12-.72c.64.4 1.28 1.44.56 2s.16 1.68.16 1.68",stroke:"#DB836E",strokeWidth:"1.117",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M92.326 71.724s1.84 1.12 4.16.96",stroke:"#5C2552",strokeWidth:"1.117",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M92.726 80.604s2.24 1.2 4.4 1.2M93.686 83.164s.96.4 1.52.32M83.687 80.044s1.786 6.547 9.262 7.954",stroke:"#DB836E",strokeWidth:"1.063",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M95.548 91.663s-1.068 2.821-8.298 2.105c-7.23-.717-10.29-5.044-10.29-5.044",stroke:"#E4EBF7",strokeWidth:"1.136",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M78.126 87.478s6.526 4.972 16.47 2.486c0 0 9.577 1.02 11.536 5.322 5.36 11.77.543 36.835 0 39.962 3.496 4.055-.466 8.483-.466 8.483-15.624-3.548-35.81-.6-35.81-.6-4.849-3.546-1.223-9.044-1.223-9.044L62.38 110.32c-2.485-15.227.833-19.803 3.549-20.743 3.03-1.049 8.04-1.282 8.04-1.282.496-.058 1.08-.076 1.37-.233 2.36-1.282 2.787-.583 2.787-.583",fill:"#FFF"}),n.createElement("path",{d:"M65.828 89.81s-6.875.465-7.59 8.156c-.466 8.857 3.03 10.954 3.03 10.954s6.075 22.102 16.796 22.957c8.39-2.176 4.758-6.702 4.661-11.42-.233-11.304-7.108-16.897-7.108-16.897s-4.212-13.75-9.789-13.75",fill:"#FFC6A0"}),n.createElement("path",{d:"M71.716 124.225s.855 11.264 9.828 6.486c4.765-2.536 7.581-13.828 9.789-22.568 1.456-5.768 2.58-12.197 2.58-12.197l-4.973-1.709s-2.408 5.516-7.769 12.275c-4.335 5.467-9.144 11.11-9.455 17.713",fill:"#FFC6A0"}),n.createElement("path",{d:"M108.463 105.191s1.747 2.724-2.331 30.535c2.376 2.216 1.053 6.012-.233 7.51",stroke:"#E4EBF7",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M123.262 131.527s-.427 2.732-11.77 1.981c-15.187-1.006-25.326-3.25-25.326-3.25l.933-5.8s.723.215 9.71-.068c11.887-.373 18.714-6.07 24.964-1.022 4.039 3.263 1.489 8.16 1.489 8.16",fill:"#FFC6A0"}),n.createElement("path",{d:"M70.24 90.974s-5.593-4.739-11.054 2.68c-3.318 7.223.517 15.284 2.664 19.578-.31 3.729 2.33 4.311 2.33 4.311s.108.895 1.516 2.68c4.078-7.03 6.72-9.166 13.711-12.546-.328-.656-1.877-3.265-1.825-3.767.175-1.69-1.282-2.623-1.282-2.623s-.286-.156-1.165-2.738c-.788-2.313-2.036-5.177-4.895-7.575",fill:"#FFF"}),n.createElement("path",{d:"M90.232 288.027s4.855 2.308 8.313 1.155c3.188-1.063 5.12.755 8.002 1.331 2.881.577 7.769 1.243 13.207-1.424-.117-6.228-7.786-4.499-13.518-7.588-2.895-1.56-4.276-5.336-4.066-9.944H91.544s-1.573 11.89-1.312 16.47",fill:"#CBD1D1"}),n.createElement("path",{d:"M90.207 287.833s2.745 1.437 7.639.738c3.456-.494 3.223.66 7.418 1.282 4.195.621 13.092-.194 14.334-1.126.466 1.242-.388 2.33-.388 2.33s-1.709.682-5.438.932c-2.295.154-8.098.276-10.14-.621-2.02-1.554-4.894-1.515-6.06-.234-4.427 1.075-7.184-.31-7.184-.31l-.181-2.991z",fill:"#2B0849"}),n.createElement("path",{d:"M98.429 272.257h3.496s-.117 7.574 5.127 9.671c-5.244.7-9.672-2.602-8.623-9.671",fill:"#A4AABA"}),n.createElement("path",{d:"M44.425 272.046s-2.208 7.774-4.702 12.899c-1.884 3.874-4.428 7.854 5.729 7.854 6.97 0 9.385-.503 7.782-6.917-1.604-6.415.279-13.836.279-13.836h-9.088z",fill:"#CBD1D1"}),n.createElement("path",{d:"M38.066 290.277s2.198 1.225 6.954 1.225c6.376 0 8.646-1.73 8.646-1.73s.63 1.168-.649 2.27c-1.04.897-3.77 1.668-7.745 1.621-4.347-.05-6.115-.593-7.062-1.224-.864-.577-.72-1.657-.144-2.162",fill:"#2B0849"}),n.createElement("path",{d:"M45.344 274.041s.035 1.592-.329 3.07c-.365 1.49-1.13 3.255-1.184 4.34-.061 1.206 4.755 1.657 5.403.036.65-1.622 1.357-6.737 2.006-7.602.648-.865-5.14-2.222-5.896.156",fill:"#A4AABA"}),n.createElement("path",{d:"M89.476 277.57l13.899.095s1.349-56.643 1.925-66.909c.576-10.267 3.923-45.052 1.042-65.585l-13.037-.669-23.737.81s-.452 4.12-1.243 10.365c-.065.515-.708.874-.777 1.417-.078.608.439 1.407.332 2.044-2.455 14.627-5.797 32.736-8.256 46.837-.121.693-1.282 1.048-1.515 2.796-.042.314.22 1.584.116 1.865-7.14 19.473-12.202 52.601-15.66 67.19l15.176-.015s2.282-10.145 4.185-18.871c2.922-13.389 24.012-88.32 24.012-88.32l3.133-.954-.158 48.568s-.233 1.282.35 2.098c.583.815-.581 1.167-.408 2.331l.408 1.864s-.466 7.458-.932 12.352c-.467 4.895 1.145 40.69 1.145 40.69",fill:"#7BB2F9"}),n.createElement("path",{d:"M64.57 218.881c1.197.099 4.195-2.097 7.225-5.127M96.024 222.534s2.881-1.152 6.34-4.034",stroke:"#648BD8",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M96.973 219.373s2.882-1.153 6.34-4.034",stroke:"#648BD8",strokeWidth:"1.032",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M63.172 222.144s2.724-.614 6.759-3.496M74.903 146.166c-.281 3.226.31 8.856-4.506 9.478M93.182 144.344s.115 14.557-1.344 15.65c-2.305 1.73-3.107 2.02-3.107 2.02M89.197 144.923s.269 13.144-1.01 25.088M83.525 170.71s6.81-1.051 9.116-1.051M46.026 270.045l-.892 4.538M46.937 263.289l-.815 4.157M62.725 202.503c-.33 1.618-.102 1.904-.449 3.438 0 0-2.756 1.903-2.29 3.923.466 2.02-.31 3.424-4.505 17.252-1.762 5.807-4.233 18.922-6.165 28.278-.03.144-.521 2.646-1.14 5.8M64.158 194.136c-.295 1.658-.6 3.31-.917 4.938M71.33 146.787l-1.244 10.877s-1.14.155-.519 2.33c.117 1.399-2.778 16.39-5.382 31.615M44.242 273.727H58.07",stroke:"#648BD8",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M106.18 142.117c-3.028-.489-18.825-2.744-36.219.2a.625.625 0 0 0-.518.644c.063 1.307.044 2.343.015 2.995a.617.617 0 0 0 .716.636c3.303-.534 17.037-2.412 35.664-.266.347.04.66-.214.692-.56.124-1.347.16-2.425.17-3.029a.616.616 0 0 0-.52-.62",fill:"#192064"}),n.createElement("path",{d:"M96.398 145.264l.003-5.102a.843.843 0 0 0-.809-.847 114.104 114.104 0 0 0-8.141-.014.85.85 0 0 0-.82.847l-.003 5.097c0 .476.388.857.864.845 2.478-.064 5.166-.067 8.03.017a.848.848 0 0 0 .876-.843",fill:"#FFF"}),n.createElement("path",{d:"M95.239 144.296l.002-3.195a.667.667 0 0 0-.643-.672c-1.9-.061-3.941-.073-6.094-.01a.675.675 0 0 0-.654.672l-.002 3.192c0 .376.305.677.68.669 1.859-.042 3.874-.043 6.02.012.376.01.69-.291.691-.668",fill:"#192064"}),n.createElement("path",{d:"M90.102 273.522h12.819M91.216 269.761c.006 3.519-.072 5.55 0 6.292M90.923 263.474c-.009 1.599-.016 2.558-.016 4.505M90.44 170.404l.932 46.38s.7 1.631-.233 2.796c-.932 1.166 2.564.7.932 2.33-1.63 1.633.933 1.166 0 3.497-.618 1.546-1.031 21.921-1.138 36.513",stroke:"#648BD8",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M73.736 98.665l2.214 4.312s2.098.816 1.865 2.68l.816 2.214M64.297 116.611c.233-.932 2.176-7.147 12.585-10.488M77.598 90.042s7.691 6.137 16.547 2.72",stroke:"#E4EBF7",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M91.974 86.954s5.476-.816 7.574-4.545c1.297-.345.72 2.212-.33 3.671-.7.971-1.01 1.554-1.01 1.554s.194.31.155.816c-.053.697-.175.653-.272 1.048-.081.335.108.657 0 1.049-.046.17-.198.5-.382.878-.12.249-.072.687-.2.948-.231.469-1.562 1.87-2.622 2.855-3.826 3.554-5.018 1.644-6.001-.408-.894-1.865-.661-5.127-.874-6.875-.35-2.914-2.622-3.03-1.923-4.429.343-.685 2.87.69 3.263 1.748.757 2.04 2.952 1.807 2.622 1.69",fill:"#FFC6A0"}),n.createElement("path",{d:"M99.8 82.429c-.465.077-.35.272-.97 1.243-.622.971-4.817 2.932-6.39 3.224-2.589.48-2.278-1.56-4.254-2.855-1.69-1.107-3.562-.638-1.398 1.398.99.932.932 1.107 1.398 3.205.335 1.506-.64 3.67.7 5.593",stroke:"#DB836E",strokeWidth:".774",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M79.543 108.673c-2.1 2.926-4.266 6.175-5.557 8.762",stroke:"#E59788",strokeWidth:".774",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M87.72 124.768s-2.098-1.942-5.127-2.719c-3.03-.777-3.574-.155-5.516.078-1.942.233-3.885-.932-3.652.7.233 1.63 5.05 1.01 5.206 2.097.155 1.087-6.37 2.796-8.313 2.175-.777.777.466 1.864 2.02 2.175.233 1.554 2.253 1.554 2.253 1.554s.699 1.01 2.641 1.088c2.486 1.32 8.934-.7 10.954-1.554 2.02-.855-.466-5.594-.466-5.594",fill:"#FFC6A0"}),n.createElement("path",{d:"M73.425 122.826s.66 1.127 3.167 1.418c2.315.27 2.563.583 2.563.583s-2.545 2.894-9.07 2.272M72.416 129.274s3.826.097 4.933-.718M74.98 130.75s1.961.136 3.36-.505M77.232 131.916s1.748.019 2.914-.505M73.328 122.321s-.595-1.032 1.262-.427c1.671.544 2.833.055 5.128.155 1.389.061 3.067-.297 3.982.15 1.606.784 3.632 2.181 3.632 2.181s10.526 1.204 19.033-1.127M78.864 108.104s-8.39 2.758-13.168 12.12",stroke:"#E59788",strokeWidth:".774",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M109.278 112.533s3.38-3.613 7.575-4.662",stroke:"#E4EBF7",strokeWidth:"1.085",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M107.375 123.006s9.697-2.745 11.445-.88",stroke:"#E59788",strokeWidth:".774",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M194.605 83.656l3.971-3.886M187.166 90.933l3.736-3.655M191.752 84.207l-4.462-4.56M198.453 91.057l-4.133-4.225M129.256 163.074l3.718-3.718M122.291 170.039l3.498-3.498M126.561 163.626l-4.27-4.27M132.975 170.039l-3.955-3.955",stroke:"#BFCDDD",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M190.156 211.779h-1.604a4.023 4.023 0 0 1-4.011-4.011V175.68a4.023 4.023 0 0 1 4.01-4.01h1.605a4.023 4.023 0 0 1 4.011 4.01v32.088a4.023 4.023 0 0 1-4.01 4.01",fill:"#A3B4C6"}),n.createElement("path",{d:"M237.824 212.977a4.813 4.813 0 0 1-4.813 4.813h-86.636a4.813 4.813 0 0 1 0-9.626h86.636a4.813 4.813 0 0 1 4.813 4.813",fill:"#A3B4C6"}),n.createElement("mask",{fill:"#fff"}),n.createElement("path",{fill:"#A3B4C6",mask:"url(#d)",d:"M154.098 190.096h70.513v-84.617h-70.513z"}),n.createElement("path",{d:"M224.928 190.096H153.78a3.219 3.219 0 0 1-3.208-3.209V167.92a3.219 3.219 0 0 1 3.208-3.21h71.148a3.219 3.219 0 0 1 3.209 3.21v18.967a3.219 3.219 0 0 1-3.21 3.209M224.928 130.832H153.78a3.218 3.218 0 0 1-3.208-3.208v-18.968a3.219 3.219 0 0 1 3.208-3.209h71.148a3.219 3.219 0 0 1 3.209 3.21v18.967a3.218 3.218 0 0 1-3.21 3.208",fill:"#BFCDDD",mask:"url(#d)"}),n.createElement("path",{d:"M159.563 120.546a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M166.98 120.546a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M174.397 120.546a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M222.539 120.546h-22.461a.802.802 0 0 1-.802-.802v-3.208c0-.443.359-.803.802-.803h22.46c.444 0 .803.36.803.803v3.208c0 .443-.36.802-.802.802",fill:"#FFF",mask:"url(#d)"}),n.createElement("path",{d:"M224.928 160.464H153.78a3.218 3.218 0 0 1-3.208-3.209v-18.967a3.219 3.219 0 0 1 3.208-3.209h71.148a3.219 3.219 0 0 1 3.209 3.209v18.967a3.218 3.218 0 0 1-3.21 3.209",fill:"#BFCDDD",mask:"url(#d)"}),n.createElement("path",{d:"M173.455 130.832h49.301M164.984 130.832h6.089M155.952 130.832h6.75M173.837 160.613h49.3M165.365 160.613h6.089M155.57 160.613h6.751",stroke:"#7C90A5",strokeWidth:"1.124",strokeLinecap:"round",strokeLinejoin:"round",mask:"url(#d)"}),n.createElement("path",{d:"M159.563 151.038a2.407 2.407 0 1 1 0-4.814 2.407 2.407 0 0 1 0 4.814M166.98 151.038a2.407 2.407 0 1 1 0-4.814 2.407 2.407 0 0 1 0 4.814M174.397 151.038a2.407 2.407 0 1 1 .001-4.814 2.407 2.407 0 0 1 0 4.814M222.539 151.038h-22.461a.802.802 0 0 1-.802-.802v-3.209c0-.443.359-.802.802-.802h22.46c.444 0 .803.36.803.802v3.209c0 .443-.36.802-.802.802M159.563 179.987a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M166.98 179.987a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M174.397 179.987a2.407 2.407 0 1 1 0-4.813 2.407 2.407 0 0 1 0 4.813M222.539 179.987h-22.461a.802.802 0 0 1-.802-.802v-3.209c0-.443.359-.802.802-.802h22.46c.444 0 .803.36.803.802v3.209c0 .443-.36.802-.802.802",fill:"#FFF",mask:"url(#d)"}),n.createElement("path",{d:"M203.04 221.108h-27.372a2.413 2.413 0 0 1-2.406-2.407v-11.448a2.414 2.414 0 0 1 2.406-2.407h27.372a2.414 2.414 0 0 1 2.407 2.407V218.7a2.413 2.413 0 0 1-2.407 2.407",fill:"#BFCDDD",mask:"url(#d)"}),n.createElement("path",{d:"M177.259 207.217v11.52M201.05 207.217v11.52",stroke:"#A3B4C6",strokeWidth:"1.124",strokeLinecap:"round",strokeLinejoin:"round",mask:"url(#d)"}),n.createElement("path",{d:"M162.873 267.894a9.422 9.422 0 0 1-9.422-9.422v-14.82a9.423 9.423 0 0 1 18.845 0v14.82a9.423 9.423 0 0 1-9.423 9.422",fill:"#5BA02E",mask:"url(#d)"}),n.createElement("path",{d:"M171.22 267.83a9.422 9.422 0 0 1-9.422-9.423v-3.438a9.423 9.423 0 0 1 18.845 0v3.438a9.423 9.423 0 0 1-9.422 9.423",fill:"#92C110",mask:"url(#d)"}),n.createElement("path",{d:"M181.31 293.666h-27.712a3.209 3.209 0 0 1-3.209-3.21V269.79a3.209 3.209 0 0 1 3.209-3.21h27.711a3.209 3.209 0 0 1 3.209 3.21v20.668a3.209 3.209 0 0 1-3.209 3.209",fill:"#F2D7AD",mask:"url(#d)"})))}},58199:function(t,e,i){"use strict";var n=i(94423),r=i(54942),o=i(80894),s=i(47749),a=function(t){var e=t.componentCls,i=t.lineHeightHeading3,o=t.iconCls,s=t.padding,a=t.paddingXL,c=t.paddingXS,l=t.paddingLG,h=t.marginXS,u=t.lineHeight;return(0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)((0,n.A)({},e,{padding:"".concat((0,r.zA)(t.calc(l).mul(2).equal())," ").concat((0,r.zA)(a)),"&-rtl":{direction:"rtl"}}),"".concat(e," ").concat(e,"-image"),{width:t.imageWidth,height:t.imageHeight,margin:"auto"}),"".concat(e," ").concat(e,"-icon"),(0,n.A)({marginBottom:l,textAlign:"center"},"& > ".concat(o),{fontSize:t.iconFontSize})),"".concat(e," ").concat(e,"-title"),{color:t.colorTextHeading,fontSize:t.titleFontSize,lineHeight:i,marginBlock:h,textAlign:"center"}),"".concat(e," ").concat(e,"-subtitle"),{color:t.colorTextDescription,fontSize:t.subtitleFontSize,lineHeight:u,textAlign:"center"}),"".concat(e," ").concat(e,"-content"),{marginTop:l,padding:"".concat((0,r.zA)(l)," ").concat((0,r.zA)(t.calc(s).mul(2.5).equal())),backgroundColor:t.colorFillAlter}),"".concat(e," ").concat(e,"-extra"),{margin:t.extraMargin,textAlign:"center","& > *":{marginInlineEnd:c,"&:last-child":{marginInlineEnd:0}}})},c=function(t){var e=t.componentCls,i=t.iconCls;return(0,n.A)((0,n.A)((0,n.A)((0,n.A)({},"".concat(e,"-success ").concat(e,"-icon > ").concat(i),{color:t.resultSuccessIconColor}),"".concat(e,"-error ").concat(e,"-icon > ").concat(i),{color:t.resultErrorIconColor}),"".concat(e,"-info ").concat(e,"-icon > ").concat(i),{color:t.resultInfoIconColor}),"".concat(e,"-warning ").concat(e,"-icon > ").concat(i),{color:t.resultWarningIconColor})},l=function(t){return function(t){return[a(t),c(t)]}(t)};e.A=(0,o.OF)("Result",(function(t){var e=t.colorInfo,i=t.colorError,n=t.colorSuccess,r=t.colorWarning,o=(0,s.oX)(t,{resultInfoIconColor:e,resultErrorIconColor:i,resultSuccessIconColor:n,resultWarningIconColor:r,imageWidth:250,imageHeight:295});return[l(o)]}),(function(t){return{titleFontSize:t.fontSizeHeading3,subtitleFontSize:t.fontSize,iconFontSize:3*t.fontSizeHeading3,extraMargin:"".concat(t.paddingLG,"px 0 0 0")}}))},63793:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},66770:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},72047:function(t){var e="undefined"!==typeof Element,i="function"===typeof Map,n="function"===typeof Set,r="function"===typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;var a,c,l,h;if(Array.isArray(t)){if((a=t.length)!=s.length)return!1;for(c=a;0!==c--;)if(!o(t[c],s[c]))return!1;return!0}if(i&&t instanceof Map&&s instanceof Map){if(t.size!==s.size)return!1;for(h=t.entries();!(c=h.next()).done;)if(!s.has(c.value[0]))return!1;for(h=t.entries();!(c=h.next()).done;)if(!o(c.value[1],s.get(c.value[0])))return!1;return!0}if(n&&t instanceof Set&&s instanceof Set){if(t.size!==s.size)return!1;for(h=t.entries();!(c=h.next()).done;)if(!s.has(c.value[0]))return!1;return!0}if(r&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(s)){if((a=t.length)!=s.length)return!1;for(c=a;0!==c--;)if(t[c]!==s[c])return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf&&"function"===typeof t.valueOf&&"function"===typeof s.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString&&"function"===typeof t.toString&&"function"===typeof s.toString)return t.toString()===s.toString();if((a=(l=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(c=a;0!==c--;)if(!Object.prototype.hasOwnProperty.call(s,l[c]))return!1;if(e&&t instanceof Element)return!1;for(c=a;0!==c--;)if(("_owner"!==l[c]&&"__v"!==l[c]&&"__o"!==l[c]||!t.$$typeof)&&!o(t[l[c]],s[l[c]]))return!1;return!0}return t!==t&&s!==s}t.exports=function(t,e){try{return o(t,e)}catch(i){if((i.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw i}}},73784:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"}}]},name:"picture",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},80730:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},91942:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},93718:function(t,e,i){"use strict";i.d(e,{A:function(){return c}});var n=i(48524),r=i(9939),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},s=i(93659),a=function(t,e){return r.createElement(s.A,(0,n.A)({},t,{ref:e,icon:o}))};var c=r.forwardRef(a)},94860:function(t,e,i){"use strict";i.d(e,{A:function(){return k}});var n=i(27569),r=i(9939),o=i(58564),s=i(76787),a=i.n(s),c=i(8076),l=i(42292),h=i(45847),u=i(4409),f=i(63471),d=i(31867),g=i(14480),p=i(55401),v=i(48739),m=i(40998),y=i(14405),b=i(94423),x=i(80894),_=(0,x.OF)("Popconfirm",(function(t){return function(t){var e=t.componentCls,i=t.iconCls,n=t.antCls,r=t.zIndexPopup,o=t.colorText,s=t.colorWarning,a=t.marginXXS,c=t.marginXS,l=t.fontSize,h=t.fontWeightStrong,u=t.colorTextHeading;return(0,b.A)({},e,(0,b.A)((0,b.A)((0,b.A)({zIndex:r},"&".concat(n,"-popover"),{fontSize:l}),"".concat(e,"-message"),(0,b.A)((0,b.A)((0,b.A)({marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start"},"> ".concat(e,"-message-icon ").concat(i),{color:s,fontSize:l,lineHeight:1,marginInlineEnd:c}),"".concat(e,"-title"),{fontWeight:h,color:u,"&:only-child":{fontWeight:"normal"}}),"".concat(e,"-description"),{marginTop:a,color:o})),"".concat(e,"-buttons"),{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}))}(t)}),(function(t){return{zIndexPopup:t.zIndexPopupBase+60}}),{resetStyle:!1}),C=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(i[n[r]]=t[n[r]])}return i},S=function(t){var e=t.prefixCls,i=t.okButtonProps,s=t.cancelButtonProps,a=t.title,c=t.description,l=t.cancelText,u=t.okText,y=t.okType,b=void 0===y?"primary":y,x=t.icon,_=void 0===x?r.createElement(o.A,null):x,C=t.showCancel,S=void 0===C||C,w=t.close,T=t.onConfirm,O=t.onCancel,E=t.onPopupClick,k=r.useContext(h.QO).getPrefixCls,M=(0,v.A)("Popconfirm",m.A.Popconfirm),A=(0,n.A)(M,1)[0],D=(0,d.b)(a),P=(0,d.b)(c);return r.createElement("div",{className:"".concat(e,"-inner-content"),onClick:E},r.createElement("div",{className:"".concat(e,"-message")},_&&r.createElement("span",{className:"".concat(e,"-message-icon")},_),r.createElement("div",{className:"".concat(e,"-message-text")},D&&r.createElement("div",{className:"".concat(e,"-title")},D),P&&r.createElement("div",{className:"".concat(e,"-description")},P))),r.createElement("div",{className:"".concat(e,"-buttons")},S&&r.createElement(g.Ay,Object.assign({onClick:O,size:"small"},s),l||(null===A||void 0===A?void 0:A.cancelText)),r.createElement(f.A,{buttonProps:Object.assign(Object.assign({size:"small"},(0,p.DU)(b)),i),actionFn:T,close:w,prefixCls:k("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},u||(null===A||void 0===A?void 0:A.okText))))},w=function(t){var e=t.prefixCls,i=t.placement,o=t.className,s=t.style,c=C(t,["prefixCls","placement","className","style"]),l=(0,r.useContext(h.QO).getPrefixCls)("popconfirm",e),u=_(l);return(0,(0,n.A)(u,1)[0])(r.createElement(y.Ay,{placement:i,className:a()(l,o),style:s,content:r.createElement(S,Object.assign({prefixCls:l},c))}))},T=void 0,O=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(i[n[r]]=t[n[r]])}return i},E=r.forwardRef((function(t,e){var i,s,f=t.prefixCls,d=t.placement,g=void 0===d?"top":d,p=t.trigger,v=void 0===p?"click":p,m=t.okType,y=void 0===m?"primary":m,b=t.icon,x=void 0===b?r.createElement(o.A,null):b,C=t.children,w=t.overlayClassName,E=t.onOpenChange,k=t.onVisibleChange,M=t.overlayStyle,A=t.styles,D=t.classNames,P=O(t,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),F=(0,h.TP)("popconfirm"),j=F.getPrefixCls,L=F.className,I=F.style,R=F.classNames,B=F.styles,W=(0,c.A)(!1,{value:null!==(i=t.open)&&void 0!==i?i:t.visible,defaultValue:null!==(s=t.defaultOpen)&&void 0!==s?s:t.defaultVisible}),z=(0,n.A)(W,2),X=z[0],N=z[1],H=function(t,e){N(t,!0),null===k||void 0===k||k(t),null===E||void 0===E||E(t,e)},Y=j("popconfirm",f),V=a()(Y,L,w,R.root,null===D||void 0===D?void 0:D.root),U=a()(R.body,null===D||void 0===D?void 0:D.body),G=_(Y);return(0,(0,n.A)(G,1)[0])(r.createElement(u.A,Object.assign({},(0,l.A)(P,["title"]),{trigger:v,placement:g,onOpenChange:function(e,i){var n=t.disabled;void 0!==n&&n||H(e,i)},open:X,ref:e,classNames:{root:V,body:U},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),I),M),null===A||void 0===A?void 0:A.root),body:Object.assign(Object.assign({},B.body),null===A||void 0===A?void 0:A.body)},content:r.createElement(S,Object.assign({okType:y,icon:x},t,{prefixCls:Y,close:function(t){H(!1,t)},onConfirm:function(e){var i;return null===(i=t.onConfirm)||void 0===i?void 0:i.call(T,e)},onCancel:function(e){var i;H(!1,e),null===(i=t.onCancel)||void 0===i||i.call(T,e)}})),"data-popover-inject":!0}),C))}));E._InternalPanelDoNotUseOrYouWillBeFired=w;var k=E}}]);
//# sourceMappingURL=823.0e83f83b.chunk.js.map