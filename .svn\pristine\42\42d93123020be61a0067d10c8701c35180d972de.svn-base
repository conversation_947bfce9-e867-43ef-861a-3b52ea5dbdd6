package com.dataxai.web.gtask.manager;

import com.dataxai.web.gtask.config.GTaskConfig;
import com.dataxai.web.gtask.factory.GTaskProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GTask任务管理器
 * 统一管理所有GTask任务的执行状态和统计信息
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Slf4j
@Component
public class GTaskManager {

    @Autowired
    private GTaskConfig gTaskConfig;

    @Autowired
    private GTaskProcessorFactory processorFactory;

    // 任务执行状态
    private final Map<String, Boolean> taskExecutionStatus = new ConcurrentHashMap<>();

    // 任务执行统计
    private final Map<String, TaskExecutionStatistics> taskStatistics = new ConcurrentHashMap<>();

    /**
     * 检查任务是否正在执行
     *
     * @param taskType 任务类型
     * @return 是否正在执行
     */
    public boolean isTaskExecuting(String taskType) {
        return taskExecutionStatus.getOrDefault(taskType, false);
    }

    /**
     * 设置任务执行状态
     *
     * @param taskType 任务类型
     * @param executing 是否正在执行
     */
    public void setTaskExecuting(String taskType, boolean executing) {
        taskExecutionStatus.put(taskType, executing);
        if (executing) {
            // 记录任务开始时间
            getOrCreateStatistics(taskType).recordStart();
        }
    }

    /**
     * 记录任务执行结果
     *
     * @param taskType 任务类型
     * @param success 成功数量
     * @param failed 失败数量
     * @param duration 执行时长（毫秒）
     */
    public void recordTaskResult(String taskType, int success, int failed, long duration) {
        TaskExecutionStatistics statistics = getOrCreateStatistics(taskType);
        statistics.recordExecution(success, failed, duration);
        setTaskExecuting(taskType, false);
    }

    /**
     * 获取或创建任务统计信息
     *
     * @param taskType 任务类型
     * @return 统计信息
     */
    private TaskExecutionStatistics getOrCreateStatistics(String taskType) {
        return taskStatistics.computeIfAbsent(taskType, k -> new TaskExecutionStatistics());
    }

    /**
     * 获取任务统计信息
     *
     * @param taskType 任务类型
     * @return 统计信息
     */
    public TaskExecutionStatistics getTaskStatistics(String taskType) {
        return taskStatistics.get(taskType);
    }

    /**
     * 获取所有任务的统计信息
     *
     * @return 所有任务统计信息
     */
    public Map<String, TaskExecutionStatistics> getAllTaskStatistics() {
        return new HashMap<>(taskStatistics);
    }


    /**
     * 强制清除所有任务的执行状态锁。
     * 通常在应用启动时调用，用于解决因异常停机导致的锁状态残留。
     */
    public void clearAllExecutionStatus() {
        if (!taskExecutionStatus.isEmpty()) {
            log.warn("发现残留的GTask执行状态锁，将全部清理...");
            taskExecutionStatus.clear();
            log.info("所有GTask执行状态锁已清理。");
        }
    }

    /**
     * 获取系统状态报告
     *
     * @return 状态报告
     */
    public Map<String, Object> getSystemStatusReport() {
        Map<String, Object> report = new HashMap<>();
        report.put("enabled", gTaskConfig.isEnabled());
        report.put("defaultConcurrency", gTaskConfig.getDefaultConcurrency());
        report.put("supportedTaskTypes", processorFactory.getAllTaskTypes());
        report.put("executionStatus", new HashMap<>(taskExecutionStatus));
        report.put("statistics", getAllTaskStatistics());
        report.put("timestamp", System.currentTimeMillis());
        return report;
    }

    /**
     * 清理统计信息
     *
     * @param taskType 任务类型，null表示清理所有
     */
    public void clearStatistics(String taskType) {
        if (taskType == null) {
            taskStatistics.clear();
            log.info("已清理所有GTask统计信息");
        } else {
            taskStatistics.remove(taskType);
            log.info("已清理{}任务统计信息", taskType);
        }
    }

    /**
     * 关闭管理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("GTask任务管理器正在关闭...");
        // 等待所有任务完成
        for (String taskType : taskExecutionStatus.keySet()) {
            if (isTaskExecuting(taskType)) {
                log.info("等待{}任务完成...", taskType);
                // 这里可以添加等待逻辑
            }
        }
        log.info("GTask任务管理器已关闭");
    }

    /**
     * 任务执行统计信息
     */
    public static class TaskExecutionStatistics {
        private long totalExecutions = 0;
        private long totalSuccess = 0;
        private long totalFailed = 0;
        private long totalDuration = 0;
        private long lastExecutionTime = 0;
        private long lastStartTime = 0;

        public void recordStart() {
            this.lastStartTime = System.currentTimeMillis();
        }

        public void recordExecution(int success, int failed, long duration) {
            this.totalExecutions++;
            this.totalSuccess += success;
            this.totalFailed += failed;
            this.totalDuration += duration;
            this.lastExecutionTime = System.currentTimeMillis();
        }

        // Getters
        public long getTotalExecutions() { return totalExecutions; }
        public long getTotalSuccess() { return totalSuccess; }
        public long getTotalFailed() { return totalFailed; }
        public long getTotalDuration() { return totalDuration; }
        public long getLastExecutionTime() { return lastExecutionTime; }
        public long getLastStartTime() { return lastStartTime; }

        public double getAverageDuration() {
            return totalExecutions > 0 ? (double) totalDuration / totalExecutions : 0;
        }

        public double getSuccessRate() {
            long total = totalSuccess + totalFailed;
            return total > 0 ? (double) totalSuccess / total * 100 : 0;
        }
    }
}
