package com.dataxai.web.service;

import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.Task;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.PlatformBatchRequestDTO;

import com.dataxai.web.dto.ZIPDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_batch(任务批次表)】的数据库操作Service
* @createDate 2025-04-10 09:45:16
*/
public interface BatchService  {

    Batch add(BatchDTO dto , List<MultipartFile> files, MultipartFile table,List<Path> pathList) throws IOException;

    Batch getOne(String batchId);

    Integer logicDeleteTaskByTaskIds(Long userId, String batchId);

    Integer stop(Long userId, String batchId);

    Integer updateImage(MultipartFile file, String imageId) throws IOException;

    List<Task> getTaskInfo(List<Task> list);

    String synchronization(ZIPDTO dto) throws Exception;

    /**
     * 修复批次统计数据
     * @param batchId 批次ID
     * @return 修复结果
     */
    Map<String, Object> fixBatchStatistics(String batchId);

    /**
     * 更新批次信息
     * @param batch 批次对象
     * @return 更新结果
     */
    Integer updateBat(Batch batch);

    /**
     * 为工作流创建批次（不扣除积分）
     * @param dto 批次DTO
     * @param userId 用户ID
     * @param teamId 团队ID（可为null）
     * @return 创建的批次对象
     */
    Batch createWorkflowBatch(BatchDTO dto, Long userId, Long teamId);

    /**
     * 创建批次（完整流程）- 包含数据处理、记录创建，返回批次创建结果
     * @param table Excel表格文件（可选）
     * @param type 批次类型
     * @param imgUrls 图片URL列表（可选）
     * @param remark 批次备注信息（可选）
     * @param taskParam 任务参数（可选）
     * @param files 上传的文件集合（可选）
     * @param userId 用户ID
     * @return 批次创建结果对象
     */
    BatchCreationResult createBatchWithFullProcess(MultipartFile table, Long type, List<String> imgUrls, String remark, String taskParam, List<MultipartFile> files, Long userId);
    /**
     * 创建批次（完整流程）- 包含数据处理、记录创建，返回批次创建结果
     * @param table Excel表格文件（可选）
     * @param type 批次类型
     * @param imgUrls 图片URL列表（可选）
     * @param remark 批次备注信息（可选）
     * @param taskParam 任务参数（可选）
     * @param files 上传的文件集合（可选）
     * @param userId 用户ID
     * @return 批次创建结果对象
     * 防止出现接口异常复制接口用于新增接口创建批次接口
     */
    BatchCreationResult createBatchWithFullProcesscope(MultipartFile table, Long type, List<String> imgUrls, String remark, String taskParam, List<MultipartFile> files, Long userId);

    /**
     * 平台API创建批次（基于图片URL）
     * @param requestDTO 平台API请求参数
     * @param userId 用户ID
     * @return 批次创建结果对象
     */
    BatchCreationResult createPlatformBatch(PlatformBatchRequestDTO requestDTO, Long userId);

    /**
     * 异步处理后续任务（公共方法，供各控制器复用）
     * @param result 批次创建结果
     * @param userId 用户ID（平台API场景需显式传入）
     */
    void processAsyncTasks(BatchCreationResult result, Long userId);

    /**
     * 批次创建结果类
     */
    class BatchCreationResult {
        private final Batch batch;
        private final BatchDTO batchDTO;
        private final List<byte[]> fileBytesList;
        private final byte[] tableBytes;
        private final List<MultipartFile> originalFiles;
        private final List<Path> pathList;
//        private final Long imageNumber;

        public BatchCreationResult(Batch batch, BatchDTO batchDTO, List<byte[]> fileBytesList,
                                   byte[] tableBytes, List<MultipartFile> originalFiles, List<Path> pathList) {
            this.batch = batch;
            this.batchDTO = batchDTO;
            this.fileBytesList = fileBytesList;
            this.tableBytes = tableBytes;
            this.originalFiles = originalFiles;
            this.pathList = pathList;
//            this.imageNumber = imageNumber;
        }

        public Batch getBatch() { return batch; }
        public BatchDTO getBatchDTO() { return batchDTO; }
        public List<byte[]> getFileBytesList() { return fileBytesList; }
        public byte[] getTableBytes() { return tableBytes; }
        public List<MultipartFile> getOriginalFiles() { return originalFiles; }
        public List<Path> getPathList() { return pathList; }
//        public Long imageNumber() { return imageNumber; }
    }

}
