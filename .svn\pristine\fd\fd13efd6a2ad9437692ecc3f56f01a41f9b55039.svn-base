
import { useMemo, useEffect, useState, useContext, useRef } from 'react';
import { Table, Tag, Progress, Image, Upload, Modal, Button, Radio, Input, message, DatePicker, Tooltip, Switch, Select, Empty, Tabs } from 'antd';

import { CloudUploadOutlined, ExclamationCircleFilled, PlusOutlined, UploadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getCommonStyleList, } from '@/api/task'
import { useNavigate } from 'react-router-dom';
import {
	getBatchList, addBatch, deleteBatch, stopBatch, getTextToImgStyleList, getTextToImgStyleCategoryList, getFavoriteStyleList, getRecentStyleList, addStyleFavorite, delStyleFavorite,
	getFashIpList, getFashIpCategoryList, getFavoriteIpList, getRecentIpList, addIpFavorite, delIpFavorite,getBatchRemark,updateTaskRemark
} from '@/api/task'
import dayjs from 'dayjs'
import { batchZipUrl } from '@/api/common'
import { ReactComponent as FollowSvg } from '@/asset/svg/follow.svg'
import { ReactComponent as FollowActiveSvg } from '@/asset/svg/follow-active.svg'
import FilterBar, { FilterParams } from '@/component/filter';

export const BatchToolsTextToImg = () => {
	const navigate = useNavigate()

	const [modal, contextHolder] = Modal.useModal()
	const [messageApi, messageContextHolder] = message.useMessage()

	useEffect(() => { }, [])

	const [batchNo, setBatchNo] = useState('') // 批次号
	const [dateRange, setDateRange] = useState<any>(null) // 日期范围
	const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

	const [isModalVisible, setIsModalVisible] = useState(false) // 控制弹窗的显示与隐藏
	const [fileList, setFileList] = useState<UploadFile[]>([]) // 上传的图片列表
	const [fileListCsv, setFileListCsv] = useState<File | null>(null);// 上传的csv文件列表

	const [styleOrIp, setStyleOrIp] = useState('style');
    const [openModal, setOpenModal] = useState<any>(false)//remark modal type
    const [remarkText, setRemarkText] = useState('')
    const [remarkId, setRemarkId] = useState(null)//修改备注需要的id
    //提交备注信息
    const editRemark = ()=>{
        updateTaskRemark({remark:remarkText,id:remarkId,taskType: 'batch'}).then(res=>{ 
            setOpenModal(false);
        })
    }


	// 表格数据
	const [dataSource, setDataSource] = useState([
		{
			key: '',
			batchNo: '',
			taskCount: null,
			progress: null,
			status: '',
			createTime: ''
		},
	])

	// 表格列配置
	const columns = [
		{
			title: '任务批次',
			dataIndex: 'batchNumber',
			key: 'batchNumber',
			ellipsis: true,
			align: 'center' as const,
			render: (taskBatch: string, record: any) => (
				<>
					<p>{taskBatch}</p>
					<p>{record.remark}</p>
				</>
			)
		},
		{
			title: '任务数量',
			dataIndex: 'totalAmount',
			key: 'totalAmount',
			width: 140,
			align: 'center' as const,
			render: (totalAmount: number, record: any) => (
				<>
					<p>总数：{totalAmount}</p>
					<p>成功：{record.successAmount}</p>
					{(record.status == 1 || record.status == 3 || record.status == 6) && record.failAmount > 0 && (
						<p style={{ color: '#CF1322' }}>失败：{record.failAmount}</p>
					)}
				</>
			)
		},
		{
			title: '任务进度',
			dataIndex: 'successAmount',
			key: 'successAmount',
			align: 'center' as const,
			render: (successAmount: number, record: any) => (
				<Progress percent={
					(record.status == 1 || record.status == 3 || record.status == 6)
						? Math.round(((successAmount + record.failAmount) / record.totalAmount) * 100)
						: 0
				}
					strokeColor={record.status == '6' ? '#cf1322' : undefined} />
			)
		},
		{
			title: '任务状态',
			dataIndex: 'status',
			key: 'status',
			width: 200,
			align: 'center' as const,
			render: (status: string, record: any) => {
				let color = ''
				if (status == '1') {
					color = 'green'
				} else if (status == '3') {
					color = 'blue'
				} else if (status == '4') {
					color = 'orange'
				} else {
					color = 'red'
				}
				return (
					<Tag color={color}>
						{status == '1'
							? '已完成'
							: status == '3'
								? '执行中'
								: status == '4' ? (record.waitTime == 0 ? '排队中 预估1分钟以内' : record.waitTime > 0 ? `排队中 预估(${record.waitTime}分钟)` : '排队中')
									: status == '5'
										? '准备中'
										: '手动终止'}
					</Tag>
				)
			}
		},

		{
			title: '创建时间',
			dataIndex: 'createTime',
			key: 'createTime',
			align: 'center' as const,
			render: (createTime: string) => (
				<p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
			)
		},
		{
			title: '操作',
			key: 'action',
			align: 'center' as const,
			render: (_: any, record: any) => (
				<div className='flex justify-center  gap-2'>
					{<Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
						onClick={() => goDetail(record)} size="small">查看详情</Button>}
					{<Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
						onClick={() => handleDownloadImgages(record.imgResults, record.batchId)}
						size="small"
						loading={downloadLoading === record.batchId}>下载</Button>}
					{record.status == 4 && <Button type="link" size="small" style={{ color: '#cf1322' }} onClick={() => handleDelBatch(record.batchId)} >删除</Button>}
					{record.status == 3 && <Button type="link" size="small" style={{ color: '#cf1322' }} onClick={() => handleStopBatch(record.batchId)} >手动终止</Button>}
                    
                    {<Button type="link" style={{  color: '#32649f' }} onClick={() => {
                        //record.id 任务id
                        getBatchRemark(record.batchId ).then((res: any) => {
                            console.log(res);
                                setRemarkText(res.remark)
                            })
                            //console.log(record);
                            setRemarkId(record.batchId);
                            setOpenModal(!openModal);
                                
                        }}
                        size="small"
                        >
                            备注
                    </Button>}
				</div>
			)
		}
	]
	//分页状态
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0
	})
	// 表格loading
	const [tableLoading, setTableLoading] = useState(false);

	// 查询条件存储key
	const QUERY_PARAMS_KEY = 'upload_image_query_params';

	//是否首次加载
	const [isFirstLoad, setIsFirstLoad] = useState(true);

	//获取文生图批次列表
	const fetchBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number,
		batchNumber: string,
		remark: string,
		userId: string,
		startTime: string,
		endTime: string
	) => {
		setDataSource([])
		setTableLoading(true)
		try {
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber,
				remark,
				userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response, '获取图片任务批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
			}
		} catch (error) {
			console.error('获取数据时出错：', error)
		} finally {
			setTableLoading(false)
			setIsFirstLoad(false)
		}
	}
	// Refs 声明（必须放在组件顶层）
	const timerRef = useRef<NodeJS.Timeout>();
	const fetchRef = useRef<typeof refreshBatchListData>();
	const paginationRef = useRef(pagination);
	const isFirstLoadRef = useRef(isFirstLoad);
	// 同步最新状态到 ref
	useEffect(() => {
		paginationRef.current = pagination;
		isFirstLoadRef.current = isFirstLoad;
	});
	useEffect(() => {
		// 更新函数引用
		fetchRef.current = refreshBatchListData;

		const tick = () => {
			if (!isFirstLoadRef.current) {
				console.log('定时刷新');
				fetchRef.current?.(8, paginationRef.current.current, 10);
			}
		};

		// 清除旧定时器
		if (timerRef.current) clearInterval(timerRef.current);
		// 启动新定时器
		timerRef.current = setInterval(tick, 20000);
		// 清理函数
		return () => {
			if (timerRef.current) {
				clearInterval(timerRef.current);
			}
		};
	}, [isFirstLoad]); // 依赖项

	//刷新文生图批次列表
	const refreshBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number
	) => {
		try {
			const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
			let queryParams = {
				batchNumber: '',
				remark: '',
				userId: '',
				stringDateRange: [] as string[]
			};

			if (savedParams) {
				queryParams = JSON.parse(savedParams);
			}

			let startTime = '';
			let endTime = '';
			if (queryParams.stringDateRange.length > 0) {
				[startTime, endTime] = queryParams.stringDateRange;
				startTime = startTime ? `${startTime} 00:00:00` : '';
				endTime = endTime ? `${endTime} 23:59:59` : '';
			}
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber: queryParams.batchNumber,
				remark: queryParams.remark,
				userId: queryParams.userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response, '刷新图片任务批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
			}
		} catch (error) {
			console.error('刷新数据时出错：', error)
		} finally {
			setTableLoading(false)
		}
	}
	// 创建批次任务
	const handleOk = async () => {
		try {
			if (fileList.length === 0 && caozuo == 2) {
				messageApi.error('请先上传图片')
				return
			}
			setCreatLoading(true)
			let params = {
				isMattingFree: isMattingFree ? 1 : 0,
				imageScale: imageScale,
				imageNumber: Number(generateQuantity),
			}
			const files = fileList.map((file) => file.originFileObj as File)
			if (caozuo == 1) {
				addBatch({
					table: fileListCsv,
					type: 8,
					materialStyleId: styleRadioValue == '1' ? '' : currentStyle?.id,
					materialIpId: fashIpRadioValue == '1' ? '' : currentFashIP?.id,
					taskParam: params ? JSON.stringify(params) : ''
				}).then((res: any) => {
					if (res.url) {
						modal.confirm({
							centered: true,
							title: '导入表格文件有错误信息，是否下载错误信息?',
							icon: null,
							okText: '确认下载',
							cancelText: '取消',
							onOk() {
								window.open(res.url, '_blank');
							},
							onCancel() {
								console.log('Cancel')
							}
						})
					} else {
						messageApi.success('文生图任务新建成功')
						setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
						handleSearch(1, 10) // 刷新批次列表数据
					}
					userinfoService.refresh() // 刷新用户积分
					setIsModalVisible(false)
				}).catch((err) => {
					messageApi.error(`创建失败: ${err?.data?.msg}`)
				}).finally(() => {
					setCreatLoading(false)
				})
			} else if (caozuo == 2) {
				addBatch({
					files, type: 8,
					materialStyleId: styleRadioValue == '1' ? '' : currentStyle?.id,
					materialIpId: fashIpRadioValue == '1' ? '' : currentFashIP?.id,
					taskParam: params ? JSON.stringify(params) : ''
				}).then((data) => {
					messageApi.success('文生图任务新建成功')
					setIsModalVisible(false)
					setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
					handleSearch(1, 10) // 刷新批次列表数据
					userinfoService.refresh() // 刷新用户积分
				})
					.catch((err) => {
						messageApi.error(`创建失败: ${err?.data?.msg}`)
					})
					.finally(() => {
						setCreatLoading(false)
					})


			}

		} catch (err) {
			messageApi.error('创建失败')
			setCreatLoading(false)
		}
	}
	// 删除批次
	const handleDelBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: (
				<div className={'text-[18px] text-normal'}> 确认删除该批次？</div>
			),
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				deleteBatch(id).then(res => {
					message.success('批次删除成功')
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				}).catch(err => {
					message.error(`批次删除失败：${err?.data?.msg}`)
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				})
			},
			onCancel() {
				console.log('Cancel')
			}
		})

	};
	// 手动终止批次
	const handleStopBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: (
				<div className={'text-[18px] text-normal'}> 确认手动终止该批次？</div>
			),
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				stopBatch(id).then(res => {
					message.success('该批次手动终止成功')
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				}).catch(err => {
					message.error(`该批次手动终止失败：${err?.data?.msg}`)
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				})
			},
			onCancel() {
				console.log('Cancel')
			}
		})
	};

	const [downloadLoading, setDownloadLoading] = useState(false);
	// 下载图片
	const handleDownloadImgages = (imgResults: any[], batchId: any) => {
		const imgUrls = imgResults.map((img) => img?.resImgUrl)
		setDownloadLoading(batchId);
		batchZipUrl({ imageUrls: imgUrls, type: 8 }).then((res: any) => {
			if (res) {
				window.open(res, '_blank'); // 在新标签页打开下载链接
			} else {
				messageApi.error('获取下载链接失败');
			}
		}).catch(err => {
			messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
		}).finally(() => {
			setDownloadLoading(false);
		})
	}
	// 查询处理函数
	const handleSearch = (pageNum: number, pageSize: number) => {
		// 存储查询条件到sessionStorage
		const queryParams = {
			batchNumber: batchNo,
			remark: '',
			userId: '',
			stringDateRange
		};
		sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
		if (stringDateRange.length > 0) {
			let [startTime, endTime] = stringDateRange
			startTime = startTime ? `${startTime} 00:00:00` : ''
			endTime = endTime ? `${endTime} 23:59:59` : ''
			fetchBatchListData(8, pageNum, pageSize, batchNo, queryParams.remark, queryParams.userId, startTime, endTime)
		} else {
			fetchBatchListData(8, pageNum, pageSize, batchNo, queryParams.remark, queryParams.userId, '', '')
		}
	}
	//初始化调用获取数据
	useEffect(() => {
		handleSearch(1, 10)
	}, [])

	// 在组件中添加分页变化处理函数
	const handleTableChange = (pagination: any) => {
		setPagination({
			...pagination,
			current: pagination.current,
			pageSize: pagination.pageSize
		});
		handleSearch(pagination.current, pagination.pageSize);
	};
	const [creatLoading, setCreatLoading] = useState(false)

	const goDetail = (record: any) => {
		navigate('/workspace/batchTools/textToImg/detail', {
			state: { batchId: record.batchId }
		})
	}
	const showModal = () => {
		setIsModalVisible(true)
		setIsMattingFree(false) // 免抠图生成（默认否）
		setImageScale('1:1')// 生成图片比例(1:1)
		setGenerateQuantity('1') // 图片生成数量
		setFileList([]) // 清空图片列表
		setFileListCsv(null)// 清空表格列表
		setStyleRadioValue('1'); //初始化风格选项为不选风格并清空当前风格
		setFashIpRadioValue('1'); //初始化动漫Ip选项为不选IP并清空当前IP

		setCurrentStyle({})
		setCurrentFashIP({})
	}

	const handleCancel = () => {
		setIsModalVisible(false)
	}

	let prevFileList: any[] = [];
	// 图片上传
	const handleUploadChange = ({ fileList: newFileList }: { fileList: any[] }) => {
		// 比较新旧文件列表，如果相同则不处理
		if (newFileList.length === prevFileList.length &&
			newFileList.every((file, index) => file.uid === prevFileList[index].uid)) {
			return;
		}

		prevFileList = newFileList;
		const validFiles: UploadFile[] = [];

		const promises = newFileList.map((file) => {
			const isImage = /^image\//.test(file.type || '');

			if (!isImage) {
				messageApi.error('请检查文件类型');
				return Promise.resolve(false);
			}

			return new Promise<boolean>((resolve) => {
				const reader = new FileReader();
				const originFileObj = file.originFileObj;
				if (!originFileObj) {
					messageApi.error('无法获取文件对象,请检查文件');
					resolve(false);
					return;
				}
				reader.readAsArrayBuffer(originFileObj);
				reader.onload = async () => {
					try {
						const blob = new Blob([reader.result as ArrayBuffer]);
						const img = await createImageBitmap(blob);
						const { width, height } = img;
						const isValidSize = width <= 4096 && height <= 4096;
						if (!isValidSize) {
							messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过');
							resolve(false);
						} else {
							resolve(true);
						}
					} catch (error) {
						messageApi.error('图片加载失败，请检查图片格式');
						resolve(false);
					}
				};
				reader.onerror = () => {
					messageApi.error('读取文件失败，请检查文件格式');
					resolve(false);
				};
			});
		});

		Promise.all(promises).then((results) => {
			newFileList.forEach((file, index) => {
				if (results[index]) {
					validFiles.push(file);
				}
			});

			const totalFiles = fileList.length + validFiles.length;
			if (totalFiles > 50) {
				messageApi.error('最多只能上传50张图片');
				return;
			}

			if (validFiles.length > 0) {
				messageApi.success(`成功上传 ${validFiles.length} 张图片`);
				setFileList(prev => [...prev, ...validFiles]);
			}
		});
	};

	// 是否选择风格
	const [styleRadioValue, setStyleRadioValue] = useState('1');
	// 是否选择动漫IP
	const [fashIpRadioValue, setFashIpRadioValue] = useState('1');



	// 参考风格列表
	const [commonStyleList, setCommonStyleList] = useState<any[]>([])
	// 无风格数据详情
	const [noStyleData, setNoStyleData] = useState<null | {
		stylePrompt: string
	}>(null)
	// 参考风格总页数
	const [commonStyleTotal, setCommonStyleTotal] = useState(0)
	// 参考风格当前页
	const [commonStylePage, setCommonStylePage] = useState(1)

	// 当前选中的风格
	const [currentStyle, setCurrentStyle] = useState<any>(null)
	// 当前选中的动漫IP
	const [currentFashIP, setCurrentFashIP] = useState<any>(null)

	//获取风格配置列表
	const fetchStyleData = async (pageNum: number, pageSize: number, categoryId: any) => {
		try {
			const response: any = await getTextToImgStyleList({ pageNum, pageSize, categoryId });
			setCommonStyleList(response.list);

			setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
			if (!currentStyle?.id && response.list?.length > 0) {
				setCurrentStyle(response.list[0]);
			}
		} catch (err: any) {
			messageApi.error(`获取风格配置列表失败: ${err?.data?.msg || err?.msg}`);
		}
	};
	//获取收藏风格列表
	const fetchFavoriteStyleData = (pageNum: number, pageSize: number) => {
		getFavoriteStyleList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);

		})

	};
	//获取用户最近7天使用风格列表
	const fetchRecentStyleData = (pageNum: number, pageSize: number) => {

		getRecentStyleList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);

		})
	};


	const [styleTabs, setStyleTabs] = useState<any[]>([]);
	const [activeStyleTab, setActiveStyleTab] = useState('');

	// 获取风格分类列表
	const fetchStyleCateData = () => {
		getTextToImgStyleCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
			const tabs = res?.list?.map((item: any) => ({
				key: item.id,
				label: item.name,
			})) || [];
			setStyleTabs(tabs);
			setActiveStyleTab(tabs[0]?.key)
			// 请求风格配置列表
			fetchStyleData(1, 12, tabs[0]?.key);
		});
	}

	const [filterTab, setFilterTab] = useState('1');
	const filterTabs = [
		{
			key: '1',
			label: '所有',
		},
		{
			key: '2',
			label: '收藏',
		},
		{
			key: '3',
			label: '最近',
		},
	];

	//获取动漫IP列表
	const fetchFashIpData = async (pageNum: number, pageSize: number, categoryId: any) => {
		try {
			const response: any = await getFashIpList({ pageNum, pageSize, categoryId });
			setCommonStyleList(response.list);
			setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
			if (!currentFashIP?.id && response.list?.length > 0) {
				setCurrentFashIP(response.list[0]);
			}
		} catch (err: any) {
			messageApi.error(`获取动漫IP列表失败: ${err?.data?.msg || err?.msg}`);
		}
	};
	//获取收藏动漫IP列表
	const fetchFavoriteIpData = (pageNum: number, pageSize: number) => {
		getFavoriteIpList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
		})

	};
	//获取用户最近7天使用动漫IP列表
	const fetchRecentIpData = (pageNum: number, pageSize: number) => {
		getRecentIpList({ pageNum, pageSize }).then((res: any) => {
			setCommonStyleList(res.list);
			setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
			setCommonStylePage(pageNum);
		})
	};

	// 获取动漫IP分类列表
	const fetchIpCateData = () => {
		getFashIpCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
			const tabs = res?.list?.map((item: any) => ({
				key: item.id,
				label: item.name,
			})) || [];
			setStyleTabs(tabs);
			setActiveStyleTab(tabs[0]?.key)
			// 请求动漫IP配置列表
			fetchFashIpData(1, 12, tabs[0]?.key);
		});
	}

	// 收藏/取消收藏风格或IP
	const handleFollow = (item: any) => {
		const isFavorited = item.isFavorited;
		const isStyle = styleOrIp === 'style';

		// 根据类型和收藏状态确定要调用的API函数和提示信息
		const apiCall = isStyle
			? (isFavorited ? () => delStyleFavorite(item.favoriteId) : () => addStyleFavorite({ materialStyleId: item.id }))
			: (isFavorited ? () => delIpFavorite(item.favoriteId) : () => addIpFavorite({ materialIpId: item.id }));

		const successMessage = isStyle
			? (isFavorited ? '取消收藏风格成功' : '风格收藏成功')
			: (isFavorited ? '取消收藏IP成功' : 'IP收藏成功');

		const errorMessage = isStyle
			? (isFavorited ? '取消收藏风格失败' : '风格收藏失败')
			: (isFavorited ? '取消收藏IP失败' : 'IP收藏失败');

		apiCall()
			.then(() => {
				if (styleOrIp === 'style') {
					if (filterTab == '2') {
						fetchFavoriteStyleData(1, 12)
					} else if (filterTab == '3') {
						fetchRecentStyleData(1, 12)
					} else {
						fetchStyleData(1, 12, activeStyleTab)
					}
				} else {
					if (filterTab == '2') {
						fetchFavoriteIpData(1, 12)
					} else if (filterTab == '3') {
						fetchRecentIpData(1, 12)
					} else {
						fetchFashIpData(1, 12, activeStyleTab)
					}
				}

				messageApi.success(successMessage);
			})
			.catch((err) => {
				messageApi.error(`${errorMessage}: ${err?.data?.msg || err?.msg}`);
			});
	};


	// 风格/动漫IP选择弹窗显影
	const [isModalOpen, setIsModalOpen] = useState(false)

	let commonStyleIPListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
		return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white"
			onClick={() => {
				if (styleOrIp == 'style') {
					setCurrentStyle(item)
				} else {
					setCurrentFashIP(item)
				}
				setIsModalOpen(false)
			}}>
			<div className="relative w-full h-[110px] rounded-lg overflow-hidden group">
				<img
					src={styleOrIp === 'style' ? `${item?.thumbnailImgUrl || item?.styleUrl || ''}` : `${item?.thumbnailImgUrl || item?.ipUrl || ''}`}
					className={`${(styleOrIp === 'style' ? currentStyle?.id : currentFashIP?.id) == item.id ? '!border-primary' : ''} w-full h-full rounded-lg hover:border-primary border-[2px]`}
					style={{ objectFit: 'cover' }}
					alt=""
				/>
				{/* 悬浮遮罩层 */}
				<div className="absolute inset-0 bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
					{/* 收藏 */}
					<div className='cursor-pointer absolute w-[20px] h-[20px] top-2  right-2'
						onClick={(e) => {
							e.stopPropagation(); // 阻止事件冒泡
							handleFollow(item);   // 传递item参数
						}}>
						{item.isFavorited ? <FollowActiveSvg className={'w-[16px]'} fill={'var(--primary-color)'} /> : <FollowSvg className={'w-[16px] svg-hover-white'} />}
					</div>
				</div>
			</div>
			<p style={{ textAlign: 'center' }} >{item.name}</p>
		</div>
	}) : <div className='w-full flex justify-center items-center pt-[100px]'><Empty /></div>


	const changeFilterTab = (key: string) => {
		setFilterTab(key)
		if (styleOrIp === 'style') {
			if (key == '2') {
				fetchFavoriteStyleData(1, 12)
			} else if (key == '3') {
				fetchRecentStyleData(1, 12)
			} else {
				fetchStyleData(1, 12, activeStyleTab)
			}
		} else {
			if (key == '2') {
				fetchFavoriteIpData(1, 12)
			} else if (key == '3') {
				fetchRecentIpData(1, 12)
			} else {
				fetchFashIpData(1, 12, activeStyleTab)
			}
		}
	}


	//上传弹窗状态管理
	const [uploadModalOpen, setUploadModalOpen] = useState(false)
	const [caozuo, setCaozuo] = useState(1) //1-导入表格 2-上传图片

	const [columnsUp, setColumnsUp] = useState([])
	const [loading, setLoading] = useState(false)



	const StyleIpPageChange = (pagenum: any) => {
		if (styleOrIp === 'style') {
			if (filterTab == '2') {
				fetchFavoriteStyleData(pagenum, 12)
			} else if (filterTab == '3') {
				fetchRecentStyleData(pagenum, 12)
			} else {
				fetchStyleData(pagenum, 12, activeStyleTab)
			}
		} else {
			if (filterTab == '2') {
				fetchFavoriteIpData(pagenum, 12)
			} else if (filterTab == '3') {
				fetchRecentIpData(pagenum, 12)
			} else {
				fetchFashIpData(pagenum, 12, activeStyleTab)
			}
		}
	}



	// 是否免抠图生成（默认否）
	const [isMattingFree, setIsMattingFree] = useState(false)
	const onSwitchChange = (checked: boolean) => {
		console.log(`switch to ${checked}`);
		setIsMattingFree(checked)
	};

	//生成图片比例
	const [imageScale, setImageScale] = useState('1:1')
	const [generateQuantity, setGenerateQuantity] = useState('1')
	const handleChange = (value: string) => {
		console.log(`selected ${value}`);
		setImageScale(value)
	};

	const onSearch = (params: FilterParams) => {
		const pageNum = 1;
		const pageSize = 10;
		fetchBatchListData(
			8,
			pageNum,
			pageSize,
			params.batchNumber ?? '',
			params.remark ?? '',
			params.userId ?? '',
			params.startTime,
			params.endTime
		);
	};

	return (
		<div className="h-full w-full p-[20px]">
			{contextHolder} {/* 这里确保 Modal 挂载 */}
			{messageContextHolder} {/* 这里确保 Message 挂载 */}
			<div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
				<Button type="primary" onClick={showModal}>
					新建文生图任务
				</Button>
				{/* 设置风格/IP弹窗 start */}
				<Modal width={800} footer={null} title={styleOrIp == 'style' ? '设置作品风格' : '设置动漫IP'} open={isModalOpen} centered onCancel={() => setIsModalOpen(false)}>
					<div className='h-[452px]  relative'>
						<div className="flex items-center gap-x-4">
							{
								filterTabs.map((item, index) => (
									<p className={`${filterTab == item.key ? 'bg-black text-white' : ''} w-[70px] h-[30px] rounded-full cursor-pointer bg-[#eee] `}
										style={{ lineHeight: '30px', textAlign: 'center' }} onClick={() => changeFilterTab(item.key)}
									>{item.label}</p>
								))
							}
						</div>
						{filterTab == '1' && (<Tabs defaultActiveKey="1" items={styleTabs} activeKey={activeStyleTab}
							onChange={(key: any) => {
								setActiveStyleTab(key);
								// 根据选中的分类ID查询数据
								if (styleOrIp == 'style') {
									fetchStyleData(1, 12, key);
								} else {
									fetchFashIpData(1, 12, key);

								}

							}} />)}

						<div className=' h-[340px]' >
							<div className='flex flex-wrap mt-[20px]'>
								{commonStyleIPListEL}
							</div>
							<label>
								<ul className='flex justify-center mt-[20px] w-full  absolute bottom-[8px] left-0'>
									{Array.from({ length: commonStyleTotal }, (_, index) => (
										<li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
											style={{ lineHeight: '18px' }} onClick={() => { StyleIpPageChange(index + 1) }}>{index + 1}</li>
									))}
									<li>{'共 ' + commonStyleTotal + ' 页'}</li>
								</ul>
							</label>
						</div>
					</div>
				</Modal>

				{/* 设置风格/IP弹窗 end */}
				{/* 新建文生图任务弹窗 start */}
				<Modal
					title="新建文生图任务"
					open={isModalVisible}
					onOk={handleOk}
					onCancel={handleCancel}
					width={660}
					centered
					okText="创建"
					confirmLoading={creatLoading}
				>
					<div className='flex justify-between items-center mt-8'>IP角色选择
						<Radio.Group style={{ width: '260px' }} block options={[
							{ label: '不选IP', value: '1' },
							{ label: 'IP选择', value: '2' },
						]} value={fashIpRadioValue} onChange={(e) => {
							setFashIpRadioValue(e.target.value)
							if (e.target.value == '1') {
								setCurrentFashIP({})
							} else {
								// 请求动漫IP分类列表
								fetchIpCateData()
							}
						}} />
					</div>
					{fashIpRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
						onClick={() => {
							setFilterTab('1')
							// 请求动漫IP分类列表
							fetchIpCateData()
							setIsModalOpen(true)
							setStyleOrIp('ip')
						}}>

						<img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentFashIP?.thumbnailImgUrl || currentFashIP?.ipUrl || ""} alt="" />
						<div className='w-[230px]'>
							<p className='text-[18px] mb-[12px]'>选中IP</p>
							<p>{currentFashIP?.name || '动漫IP'}</p>
						</div>
						<img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
					</div>}
					<div className='flex justify-between items-center mt-8'>风格选择
						<Radio.Group style={{ width: '260px' }} block options={[
							{ label: '不选风格', value: '1' },
							{ label: '风格选择', value: '2' },
						]} value={styleRadioValue} onChange={(e) => {
							setStyleRadioValue(e.target.value)
							if (e.target.value == '1') {
								setCurrentStyle({})
							} else {
								// 请求风格分类列表
								fetchStyleCateData()
							}
						}} />
					</div>
					{styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
						onClick={() => {
							setFilterTab('1')
							// 请求风格分类列表
							fetchStyleCateData()
							setIsModalOpen(true)
							setStyleOrIp('style')
						}}>
						<img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentStyle?.thumbnailImgUrl || currentStyle?.styleUrl || ""} alt="" />
						<div className='w-[230px]'>
							<p className='text-[18px] mb-[12px]'>选中风格</p>
							<p>{currentStyle?.name || '自定义风格'}</p>
						</div>
						<img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
					</div>}
					<div className="flex justify-between items-center  mt-4">
						<p className="w-[110px]">生成图片比例</p>
						<Select
							defaultValue='1:1'
							style={{ width: 260 }}
							onChange={handleChange}
							value={imageScale}
							options={[
								{ value: '1:1', label: '1 : 1' },
								{ value: '2:3', label: '2 : 3' },
								{ value: '3:2', label: '3 : 2' },
								{ value: '3:4', label: '3 : 4' },
								{ value: '4:3', label: '4 : 3' },
								{ value: '9:16', label: '9 : 16' },
								{ value: '16:9', label: '16 : 9' }
							]}
						/>
					</div>
					<div className="flex justify-between items-center mt-4">
						<p className="w-[110px]">图片生成数量</p>
						<Radio.Group
							size="large"
							block
							options={[
								{ label: '1', value: '1' },
								{ label: '2', value: '2' },
								{ label: '4', value: '4' },
							]}
							onChange={(e) => setGenerateQuantity(e.target.value)}
							value={generateQuantity}
							optionType="button"
							buttonStyle="solid"
							style={{ width: 260 }}
						/>
					</div>
					<div className='flex  justify-between items-center  text-normal mt-[20px] mb-[10px]'>
						<p>免抠图生成<Tooltip title="利用AI自动从图片中精准分离出前景对象并去除背景，可以省去用户手动抠图的操作。"><QuestionCircleOutlined className=' ml-[6px]' /></Tooltip></p>
						<Switch checked={isMattingFree} onChange={onSwitchChange} />
					</div>
					<div className="flex justify-between items-center mt-8">
						提示词列表
						<div className="w-[40%] flex justify-between">
							<Button
								style={{
									border:
										caozuo === 1 ? '1px dashed #33649f' : '1px dashed #d9d9d9',
									color: caozuo === 1 ? '#33649f' : '#000'
								}}
								type="dashed"
								onClick={() => {
									setUploadModalOpen(true)
									setCaozuo(1)
								}}
							>
								<PlusOutlined />
								导入Excel
							</Button>
							<Button
								style={{
									border:
										caozuo === 2 ? '1px dashed #33649f' : '1px dashed #d9d9d9',
									color: caozuo === 2 ? '#33649f' : '#000'
								}}
								type="dashed"
								onClick={() => {
									setUploadModalOpen(true)
									setCaozuo(2)
								}}
							>
								<PlusOutlined />
								图片转文字
							</Button>
						</div>
					</div>
					<div>
						<Upload.Dragger
							multiple
							accept="image/*"
							fileList={[]} // 设置为空数组隐藏自带列表
							onChange={handleUploadChange}
							beforeUpload={() => false} // 阻止自动上传
							className="upload-area"
							listType="picture"
							showUploadList={false} // 完全隐藏上传列表
							disabled={fileList.length >= 50} // 添加禁用状态
							style={{
								marginTop: '30px',
								display: caozuo === 2 ? 'block' : 'none'
							}}
							directory={true} // 允许选择或拖拽文件夹
						>
							<p className="ant-upload-drag-icon">
								<CloudUploadOutlined />
							</p>
							<p className="ant-upload-text">点击或拖拽文件到此处上传</p>
							<p className="ant-upload-hint">
								最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
							</p>
						</Upload.Dragger>
						<div
							className="mt-2 text-right"
							style={{ display: caozuo === 2 ? 'block' : 'none' }}
						>
							已选择图片张数:{' '}
							<span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
								{fileList.length}
							</span>{' '}
							/ 50
						</div>
						<div style={{ display: caozuo === 1 ? 'block' : 'none' }}>
							<div style={{ padding: 20 }}>
								<Upload.Dragger
									accept=".csv,.xls,.xlsx"
									beforeUpload={(file) => {
										setFileListCsv(file)
										return false
									}} // 阻止自动上传
									customRequest={() => { }}
									showUploadList={false}
									maxCount={1}
								>
									<Button icon={<UploadOutlined />} loading={loading}>
										上传表格文件 (CSV/XLS/XLSX)
										{"   "}{fileListCsv?.name}
									</Button>
								</Upload.Dragger>
								<div style={{ textAlign: 'center', marginTop: '20px' }}>
									<a
										href="https://image-task.xiaoaishop.com/prompt_demo.xlsx"
										download="template.xlsx"
									>
										<Button type="primary">下载Excel模板</Button>
									</a>
								</div>
							</div>
						</div>
					</div>
					{/* 上传导入弹窗 */}
				</Modal>
				{/* 新建文生图任务弹窗 end */}
				<div className="flex items-center gap-2">
					<FilterBar
						fields={['batchNumber','dateRange','remark','userId']}
						storageKey={QUERY_PARAMS_KEY}
						withTime={false}
						onSearch={onSearch}
					/>
				</div>
			</div>
			<div className='w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
				<Table
					columns={columns}
					dataSource={dataSource}
					className="mt-4"
					scroll={{ x: 966 }}
					loading={tableLoading}
					pagination={{
						...pagination,
						showTotal: (total: number) => `共 ${total} 条`,
						showSizeChanger: false
					}}
					onChange={handleTableChange}
				/>
			</div>
			<Modal
				title="添加备注信息"
				visible={openModal}
				onOk={editRemark}
				onCancel={() => setOpenModal(false)}
				okText="确定"
				cancelText="取消"
			>
				<Input placeholder="请输入备注" onChange={(e)=>setRemarkText(e.target.value)} value={remarkText} />
			</Modal>
		</div>
	)
};

export default BatchToolsTextToImg;





