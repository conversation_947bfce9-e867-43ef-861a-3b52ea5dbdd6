
import { useEffect, useState } from 'react';
import { Button, Checkbox, message, Spin } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getBatchDetail } from '@/api/task'
import { useLocation, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'
import { batchZipUrl } from '@/api/common'

export const UploadImageDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [messageApi, messageContextHolder] = message.useMessage()

    const [selectedImages, setSelectedImages] = useState<any[]>([]);

    const { batchId } = location.state || {};
    const [record, setRecord] = useState<any>();
    const [pageLoading, setPageLoading] = useState(false);

    const fetchDetail = () => {
        if (!batchId) return;
        setPageLoading(true)
        getBatchDetail(batchId).then((res: any) => {
            setRecord(res)
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }

    useEffect(() => {
        if (batchId) {
            fetchDetail()
        }
    }, [batchId]);

    const handleSelect = (task: any) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(task)
                ? prev.filter(item => item !== task)
                : [...prev, task];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedImages([]); // 清空已选图片
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };



    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.originalUrl}
                        oriImgUrl={image.originalUrl}
                        smallImgUrl={image.thumbnailUrl || image.originalUrl}
                        markImgUrl={image.originalUrl}
                        progress={image.progress}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    const [downloadLoading, setDownloadLoading] = useState(false);

    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : record ? (<>
                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}  >
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center  h-[60px] border-b-[1px] border-normal'>
                    <p className="mr-[20px]">批次: {record?.batchNumber}</p>
                    <p className="mr-[20px]">创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    <p className="mr-[20px]">图片数量：{record?.totalAmount}</p>
                    <p className="mr-[20px]">备注信息: {record?.remark || '-'}</p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="grid  p-[10px] grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 xxl:grid-cols-8 gap-0">
                        {record?.tasks?.length > 0 && record?.tasks.map((task: any, index: number) => (
                            <div key={index} className='aspect-square  p-2'>
                                <div className="w-full h-full flex  rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden">
                                    <Checkbox
                                        className="absolute top-4 left-4 z-10"
                                        style={{ transform: 'scale(1.25)' }}  // 放大1.5倍
                                        checked={selectedImages.includes(task)}
                                        onChange={() => handleSelect(task)}
                                        disabled={checkImageDisabled(task, 'uploadImage')}
                                    />
                                    {task.hasUploaded && <p className="absolute bottom-4  z-10"
                                        style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                    {getTaskImageComponent(task)}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* 底部操作栏 start */}
                    <BottomActionBar
                        visible={selectedImages.length > 0 || showBatchActions}
                        selectedCount={selectedImages.length}
                        isAllSelected={record?.tasks?.length > 0 && selectedImages.length === record?.tasks?.filter((task: any) => !checkImageDisabled(task, 'uploadImage')).length}
                        onToggleSelectAll={() => {
                            const enabledTasks = record?.tasks?.filter((task: any) => !checkImageDisabled(task, 'uploadImage')) || [];
                            if (record?.tasks?.length > 0 && selectedImages.length === enabledTasks.length) {
                                setSelectedImages([])
                            } else {
                                setSelectedImages([...enabledTasks])
                            }
                        }}
                        onCancelSelection={cancelSelection}
                        syncEnabled
                        syncExtraParams={{ batchId }}
                        selectedItems={selectedImages}
                        extractImageUrl={(task) => task.originalUrl}
                        onDownload={() => {
                            setDownloadLoading(true)
                            const imageUrls = selectedImages.map(task => task.originalUrl);
                            batchZipUrl({ imageUrls, type: 15 }).then((res: any) => {
                                if (res) {
                                    window.open(res, '_blank'); // 在新标签页打开下载链接
                                } else {
                                    message.error('获取下载链接失败');
                                }
                            }).catch((err: any) => {
                                message.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
                            }).finally(() => {
                                setDownloadLoading(false)
                            })
                        }}
                        downloadLoading={downloadLoading}
                        downloadDisabled={selectedImages.length === 0}
                        enableWorkflow={userInfo?.currentMode == 2}
                        onActionFinished={() => {
                            // 刷新积分或数据
                            userinfoService.refresh();
                            fetchDetail();
                            setSelectedImages([])
                        }}
                        actionDisabled={selectedImages.length === 0}
                    />
                    {/* 底部操作栏 end */}
                </div>
            </>) : null}
        </div >
    );
};

export default UploadImageDetail;





