package com.dataxai.web.controller.front;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.domain.*;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.service.ITTemplateInformationService;
import com.dataxai.service.ITTemplateInfromationDataService;
import com.dataxai.web.mapper.OrdinalImgResultMapper;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.utils.ExcelExportUtil;
import com.dataxai.web.vo.ProductExportVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;
import org.terracotta.utilities.io.Files;

/**
 * 半托数据商品模板Controller
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Api(tags = "半托数据模板")
@RestController
@RequestMapping("/system/information")
public class TTemplateInformationController extends BaseController {
    @Autowired
    private ITTemplateInformationService tTemplateInformationService;
    @Autowired
    private TUserMapper tUserMapper;
    @Autowired
    private ITTemplateInfromationDataService tTemplateInfromationDataService;
    @Autowired
    private OrdinalImgResultMapper ordinalImgResultMapper;
    @Autowired
    private AliYunFileService aliYunFileService;

    /**
     * 查询半托数据商品模板列表
     */
//    @PreAuthorize("@ss.hasPermi('system:information:list')")
    @GetMapping("/list")
    public TableDataInfo list(TTemplateInformation tTemplateInformation) {
        startPage();
        List<TTemplateInformation> list = tTemplateInformationService.selectTTemplateInformationList(tTemplateInformation);
        return getDataTable(list);
    }

    @GetMapping("/templatelist")
    public TableDataInfo templatelist(TTemplateInformation tTemplateInformation) {
        startPage();
        List<TTemplateInformation> list = tTemplateInformationService.selectTTemplateInformationTeamList(tTemplateInformation);
        return getDataTable(list);
    }

    @PostMapping("/uploadexcel")
    public AjaxResult uploadExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的Excel文件");

        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !(originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx"))) {
            return AjaxResult.error("只能上传Excel文件（.xls或.xlsx格式）");
        }
        try (InputStream inputStream = file.getInputStream()) {
            File tempFile = File.createTempFile("excel-", ".tmp");
            try (Workbook workbook = WorkbookFactory.create(inputStream)) {
                    // 验证是否为temu半托表格
                    org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(1);
                Sheet templateSheet = workbook.getSheet("模版");
                if (templateSheet == null) {
                    throw new IllegalArgumentException("模版Sheet不存在");
                }

                org.apache.poi.ss.usermodel.Row headerRow = templateSheet.getRow(0);
                    if (headerRow == null) {
                        return AjaxResult.error("表格标题行不存在");
                    }

                    // 验证关键列是否存在
                    org.apache.poi.ss.usermodel.Cell cell0 = headerRow.getCell(1, org.apache.poi.ss.usermodel.Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    org.apache.poi.ss.usermodel.Cell cell1 = headerRow.getCell(3, org.apache.poi.ss.usermodel.Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                    org.apache.poi.ss.usermodel.Cell cell2 = headerRow.getCell(4, org.apache.poi.ss.usermodel.Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);

                    if (cell0 == null || !"发货仓".equals(cell0.getStringCellValue()) ||
                        cell1 == null || !"运费模版".equals(cell1.getStringCellValue()) ||
                        cell2 == null || !"承诺发货时效".equals(cell2.getStringCellValue())) {
                        return AjaxResult.error("表格列格式不符合temu半托要求");
                    }

                    Files.deleteIfExists(tempFile.toPath());
//                Map<String,Object> map = aliYunFileService.excelUpload(file);
//                Object url = map.get("url");
                Map<String,Object> map = new HashMap<>();
                map.put("url", "https://image-task.xiaoaishop.com/excel/2025/08/06/a19dcd39_modified_男装商品上传模版.xlsx");
                map.get("url");
                String url = "https://image-task.xiaoaishop.com/excel/2025/08/06/a19dcd39_modified_男装商品上传模版.xlsx";
                Map<String, Object> stringObjectMap = tTemplateInformationService.excelAnalysis(url);
                return AjaxResult.success(stringObjectMap);
            }
        }catch (Exception e) {
            log.error("Excel文件验证异常：{}", e.getMessage(), e);
            return AjaxResult.error("文件格式验证失败：不是有效的Excel文件");
        }


    }


    /**
     * 导出半托数据商品模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:information:export')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TTemplateInformation tTemplateInformation) {
        List<TTemplateInformation> list = tTemplateInformationService.selectTTemplateInformationList(tTemplateInformation);
        ExcelUtil<TTemplateInformation> util = new ExcelUtil<TTemplateInformation>(TTemplateInformation.class);
        util.exportExcel(response, list, "半托数据商品模板数据");
    }

    /**
     * 获取半托数据商品模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:information:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(tTemplateInformationService.selectTTemplateInformationById(id));

    }
    @GetMapping("/export/products")
    public void exportProducts(
            @RequestParam List<Long> imageIds,     // 图片ID列表
            @RequestParam String type,           // 男装/女装
            @RequestParam String tableType,           // 表格格式 1：Temu美本 2：香港跨境 3：中国资料跨境店铺
            @RequestParam String color,            // 白色/黑色
            @RequestParam String pageType,
            @RequestParam Integer id,  // product-采集 risk-侵权 title-标题
            HttpServletResponse response) throws IOException {
        /*log.info("exportProducts - type: {}, tableType: {}, color: {},pageType:{}",
                type,
                tableType,
                color,
                pageType);*/
        String templatePath;
        String fileName;
        ArrayList<ProductExportVO> productExportVOS;
        List<ProductExportVO> dynamicData = Collections.emptyList();
        if ("product".equals(pageType)) {
            dynamicData = ordinalImgResultMapper.queryProducts(imageIds);
        } else if ("risk".equals(pageType)) {
            dynamicData = ordinalImgResultMapper.queryRiskImageById(imageIds);
        } else if ("title".equals(pageType)) {
            dynamicData = ordinalImgResultMapper.queryTitleDetailById(imageIds);
        } else {
            throw new ServiceException("缺少pageType参数！");
        }
        if (dynamicData.isEmpty()) {
            throw new ServiceException("未查询到数据！");
        }
        if ("2".equals(tableType)) {
            if ("男装".equals(type)) {
                templatePath = "/excel-template/product_template_hk_m.xlsx";
                productExportVOS = exportExcr(id, color, dynamicData);
            } else {
                templatePath = "/excel-template/product_template_hk_w.xlsx";
                productExportVOS = exportExcr(id,color, dynamicData);
            }
            fileName = "香港跨境店-" + type + "T恤商品模板_" + System.currentTimeMillis() + ".xlsx";
        } else if ("1".equals(tableType)) {
            if ("男装".equals(type)) {
                templatePath = "/excel-template/product_template_cn_m.xlsx";
                productExportVOS = exportExcr(id, color,dynamicData);
            } else {
                templatePath = "/excel-template/product_template_cn_w.xlsx";
                productExportVOS = exportExcr(id,color, dynamicData);
            }
            fileName = type + "T恤" + color + "导表-" + System.currentTimeMillis() + ".xlsx";
        } else {
            if ("男装".equals(type)) {
                templatePath = "/excel-template/product_template_cb_m.xlsx";
                productExportVOS = exportExcr(id,color, dynamicData);
            } else {
                templatePath = "/excel-template/product_template_cb_w.xlsx";
                productExportVOS = exportExcr(id ,color, dynamicData);
            }
            fileName = type + "中国资料跨境店铺" + color + "导表-" + System.currentTimeMillis() + ".xlsx";
        }
        /*log.info("manualExport入参 - templatePath: {}, productExportVOS: {}, fileName: {}",
                templatePath,
                productExportVOS,
                fileName);*/
        // 调用导出工具类（模板在resources目录下）
        ExcelExportUtil.manualExport(response, templatePath, productExportVOS, fileName);
    }

    public ArrayList<ProductExportVO> exportExcr( Integer id,String color, List<ProductExportVO> dynamicData) {
        ArrayList<ProductExportVO> productExportVOS = new ArrayList<>();
        String spuCode = generate();
        ArrayList<String> sizelist = new ArrayList<>();
        ArrayList<String> shoulderWidthlist = new ArrayList<>();
        ArrayList<String> bustCircumferencelist = new ArrayList<>();
        ArrayList<String> garmentLengthlist = new ArrayList<>();
        ArrayList<String> sleeveLengthCmlist = new ArrayList<>();
        ProductExportVO productExportVO = new ProductExportVO();
        ProductExportVO productExportVOgeneral = new ProductExportVO();
        TTemplateInformation tTemplateInformation = tTemplateInformationService.selectTTemplateInformationById(id);
        String templateId = tTemplateInformation.getTemplateId();
        TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
        tTemplateInfromationData.setTemplateId(templateId);
        List<TTemplateInfromationData> tTemplateInfromationDataOveralllist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        List<TTemplateInfromationData> filteredList = tTemplateInfromationDataOveralllist.stream().filter(data -> data.getColumnName() != null && !data.getColumnName().isEmpty()).filter(data -> data.getColumnValue() != null && !data.getColumnValue().isEmpty()).collect(Collectors.toList());
        for (TTemplateInfromationData templateInfromationData : filteredList) {
            if (templateInfromationData.getColumnName().equals("经营站点")) {
                productExportVO.setOperatingWebsite(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("发货仓")) {
                productExportVO.setWarehouseStock(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("类目")) {
                productExportVO.setCategory(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("运费模版")) {
                productExportVO.setFreightTemplate(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("承诺发货时效")) {
                productExportVO.setDeliveryTime(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("素材语言")) {
                productExportVO.setMaterialLanguage(templateInfromationData.getColumnValue());
            }
        }
        tTemplateInfromationData.setSign("base");
        List<TTemplateInfromationData> tTemplateInfromationDatabaselist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        tTemplateInfromationData.setSign("baseValues");
        List<TTemplateInfromationData> tTemplateInfromationDatabaseValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData  templateInfromationData : tTemplateInfromationDatabaselist) {
            for(TTemplateInfromationData   templateInfromationData1 : tTemplateInfromationDatabaseValuelist){
                if(templateInfromationData.getColumnId().equals(templateInfromationData1.getColumnId())){
                    if(templateInfromationData.getColumnName().equals("商品名称")){
                        productExportVO.setProductTitle(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("英文名称")){
                        productExportVO.setProductEnTitle(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("商品产地")){
                        productExportVO.setProductOrigin(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("产地省份")){
                        productExportVO.setProductionPlaceProvince(templateInfromationData1.getColumnValue());
                    }
                }
            }
        }
        tTemplateInfromationData.setSign("spu");
        List<TTemplateInfromationData> tTemplateInfromationDataspulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        tTemplateInfromationData.setSign("spuValues");
        List<TTemplateInfromationData> tTemplateInfromationDataspuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData  templateInfromationData : tTemplateInfromationDataspulist) {
            for (TTemplateInfromationData templateInfromationData1 : tTemplateInfromationDataspuValuelist) {
                if (templateInfromationData.getColumnId().equals(templateInfromationData1.getColumnId())) {
                    if (templateInfromationData.getColumnName().equals("材质")) {
                        productExportVO.setMaterial(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分1")) {
                        productExportVO.setIngredient1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分1成分比例")) {
                        productExportVO.setIngredient1Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分2")) {
                        productExportVO.setIngredient2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分2成分比例")) {
                        productExportVO.setIngredient2Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分3")) {
                        productExportVO.setIngredient3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分3成分比例")) {
                        productExportVO.setIngredient3Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("图案")) {
                        productExportVO.setPattern(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("细节")) {
                        productExportVO.setDetails(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("领型")) {
                        productExportVO.setCollarType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("风格")) {
                        productExportVO.setStyle(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("护理说明")) {
                        productExportVO.setCareInstructions(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料")) {
                        productExportVO.setFabric(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料弹性")) {
                        productExportVO.setFabricElasticity(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("适用人群")) {
                        productExportVO.setTargetAudience(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("季节")) {
                        productExportVO.setSeason(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("是否透明")) {
                        productExportVO.setIsIndividuallyPackaged(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("版型")) {
                        productExportVO.setSilhouette(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("织造方式")) {
                        productExportVO.setWeavingMethod(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("印花类型")) {
                        productExportVO.setPrintingType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料纹理1")) {
                        productExportVO.setFabricTexture1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重1（g/m²)")) {
                        productExportVO.setFabricWeight1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料纹理")) {
                        productExportVO.setLiningTexture(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料克重（g/m²)")) {
                        productExportVO.setLiningWeight(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料克重（g/m²)单位")) {
                        productExportVO.setLiningWeightUnit(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分1")) {
                        productExportVO.setLiningIngredient1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分1成分比例")) {
                        productExportVO.setLiningIngredient1Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分2")) {
                        productExportVO.setLiningIngredient2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分2成分比例")) {
                        productExportVO.setLiningIngredient2Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分3")) {
                        productExportVO.setLiningIngredient3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分3成分比例")) {
                        productExportVO.setLiningIngredient3Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分4")) {
                        productExportVO.setLiningIngredient4(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分4成分比例")) {
                        productExportVO.setLiningIngredient4Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分5")) {
                        productExportVO.setLiningIngredient5(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分5成分比例")) {
                        productExportVO.setLiningIngredient5Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("次要材质")) {
                        productExportVO.setSecondaryMaterial(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("袖型")) {
                        productExportVO.setSleeveType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("袖长")) {
                        productExportVO.setSleeveLength(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("长度")) {
                        productExportVO.setLength(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("场合")) {
                        productExportVO.setOccasion(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("场景")) {
                        productExportVO.setScene(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("廓形")) {
                        productExportVO.setOutline(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("门襟类型")) {
                        productExportVO.setPlacketType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("下摆形状")) {
                        productExportVO.setHemShape(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("腰带")) {
                        productExportVO.setWaistband(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("功能类型")) {
                        productExportVO.setFunctionType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("胸垫")) {
                        productExportVO.setChestPad(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("品牌名")) {
                        productExportVO.setBrandName(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("二次工艺")) {
                        productExportVO.setSecondaryProcess(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重")) {
                        productExportVO.setFabricWeight(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料纹理2")) {
                        productExportVO.setFabricTexture2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重2（g/m²)")) {
                        productExportVO.setFabricWeight2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重2（g/m²)单位")) {
                        productExportVO.setFabricWeight2Unit(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("款式来源")) {
                        productExportVO.setDesignSource(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿模特")) {
                        productExportVO.setFittingModel(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿尺码")) {
                        productExportVO.setFittingSize(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿感受")) {
                        productExportVO.setFittingExperience(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图1")) {
                        productExportVO.setCarouselImage1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图2")) {
                        productExportVO.setCarouselImage2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图3")) {
                        productExportVO.setCarouselImage3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图4")) {
                        productExportVO.setCarouselImage4(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图5")) {
                        productExportVO.setCarouselImage5(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图6")) {
                        productExportVO.setCarouselImage6(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图7")) {
                        productExportVO.setCarouselImage7(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图8")) {
                        productExportVO.setCarouselImage8(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图9")) {
                        productExportVO.setCarouselImage9(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图10")) {
                        productExportVO.setCarouselImage10(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("详情图文-英语")) {
                        productExportVO.setDetailDescriptionEn(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("主图视频")) {
                        productExportVO.setMainVideo(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("详情视频")) {
                        productExportVO.setDetailVideo(templateInfromationData1.getColumnValue());
                    }

                }
            }
            tTemplateInfromationData.setSign("sku");
            List<TTemplateInfromationData> tTemplateInfromationDataskulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
            tTemplateInfromationData.setSign("skuValues");
            List<TTemplateInfromationData> tTemplateInfromationDataskuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
            for(TTemplateInfromationData templateInfromationSkuData : tTemplateInfromationDataskulist){
                for (TTemplateInfromationData templateInfromationSkuValueData:tTemplateInfromationDataskuValuelist){
                    if (templateInfromationSkuData.getColumnId().equals(templateInfromationSkuValueData.getColumnId())) {
                        if (templateInfromationSkuData.getColumnName().equals("色值(主规格)")){
                            productExportVOgeneral.setColorValue(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("规格类型2")){
                            productExportVOgeneral.setSpecType2(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码组别")){
                            productExportVOgeneral.setSizeGroup(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码类型")){
                            productExportVOgeneral.setSizeType(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("申报价格-美国站")){
                            productExportVOgeneral.setDeclaredPriceUs(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("币种")){
                            productExportVOgeneral.setCurrency(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("发货仓1库存")){
                            productExportVOgeneral.setWarehouse1Stock(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("最长边（cm）")){
                            productExportVOgeneral.setLongestSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("次长边（cm）")){
                            productExportVOgeneral.setMiddleSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("最短边（cm）")){
                            productExportVOgeneral.setShortestSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("重量（g）")){
                            productExportVOgeneral.setWeightG(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图1")){
                            productExportVOgeneral.setCarouselImage1(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图2")){
                            productExportVOgeneral.setCarouselImage2(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图3")){
                            productExportVOgeneral.setCarouselImage3(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图4")){
                            productExportVOgeneral.setCarouselImage4(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图5")){
                            productExportVOgeneral.setCarouselImage5(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码")){
                            sizelist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("肩宽")){
                            shoulderWidthlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("胸围全围")){
                            bustCircumferencelist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("衣长")){
                            garmentLengthlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("袖长")){
                            sleeveLengthCmlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                    }
                }
            }
        }
        productExportVOS.add(productExportVO);
        for (int i = 0; i < sleeveLengthCmlist.size(); i++) {
            ProductExportVO sizeVO = new ProductExportVO();
            BeanUtils.copyProperties(productExportVO, sizeVO);
            // 设置尺码特有属性
            sizeVO.setSkuNumber(spuCode );
            sizeVO.setSize(sizelist.get(i));
            sizeVO.setShoulderWidth(shoulderWidthlist.get(i));
            sizeVO.setBustCircumference(bustCircumferencelist.get(i));
            sizeVO.setGarmentLength(garmentLengthlist.get(i));
            sizeVO.setSleeveLengthCm(sleeveLengthCmlist.get(i));

            productExportVOS.add(sizeVO);
        }

        return productExportVOS;
    }

    /**
     * 通过id导出自定义数据模板
     *
     * @param id
     * @return
     */
    @GetMapping("/exprot/{id}")
    public ArrayList<ProductExportVO> exportExcrl(@PathVariable("id") Integer id) {
        ArrayList<ProductExportVO> productExportVOS = new ArrayList<>();
        String spuCode = generate();
        ArrayList<String> sizelist = new ArrayList<>();
        ArrayList<String> shoulderWidthlist = new ArrayList<>();
        ArrayList<String> bustCircumferencelist = new ArrayList<>();
        ArrayList<String> garmentLengthlist = new ArrayList<>();
        ArrayList<String> sleeveLengthCmlist = new ArrayList<>();
        ProductExportVO productExportVO = new ProductExportVO();
        ProductExportVO productExportVOgeneral = new ProductExportVO();
        TTemplateInformation tTemplateInformation = tTemplateInformationService.selectTTemplateInformationById(id);
        String templateId = tTemplateInformation.getTemplateId();
        TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
        tTemplateInfromationData.setTemplateId(templateId);
        List<TTemplateInfromationData> tTemplateInfromationDataOveralllist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        List<TTemplateInfromationData> filteredList = tTemplateInfromationDataOveralllist.stream().filter(data -> data.getColumnName() != null && !data.getColumnName().isEmpty()).filter(data -> data.getColumnValue() != null && !data.getColumnValue().isEmpty()).collect(Collectors.toList());
        for (TTemplateInfromationData templateInfromationData : filteredList) {
            if (templateInfromationData.getColumnName().equals("经营站点")) {
                productExportVO.setOperatingWebsite(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("发货仓")) {
                productExportVO.setWarehouseStock(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("类目")) {
                productExportVO.setCategory(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("运费模版")) {
                productExportVO.setFreightTemplate(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("承诺发货时效")) {
                productExportVO.setDeliveryTime(templateInfromationData.getColumnValue());
            }
            if (templateInfromationData.getColumnName().equals("素材语言")) {
                productExportVO.setMaterialLanguage(templateInfromationData.getColumnValue());
            }
        }
        tTemplateInfromationData.setSign("base");
        List<TTemplateInfromationData> tTemplateInfromationDatabaselist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        tTemplateInfromationData.setSign("baseValues");
        List<TTemplateInfromationData> tTemplateInfromationDatabaseValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData  templateInfromationData : tTemplateInfromationDatabaselist) {
            for(TTemplateInfromationData   templateInfromationData1 : tTemplateInfromationDatabaseValuelist){
                if(templateInfromationData.getColumnId().equals(templateInfromationData1.getColumnId())){
                    if(templateInfromationData.getColumnName().equals("商品名称")){
                        productExportVO.setProductTitle(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("英文名称")){
                        productExportVO.setProductEnTitle(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("商品产地")){
                        productExportVO.setProductOrigin(templateInfromationData1.getColumnValue());
                    }
                    if(templateInfromationData.getColumnName().equals("产地省份")){
                        productExportVO.setProductionPlaceProvince(templateInfromationData1.getColumnValue());
                    }
                }
            }
        }
        tTemplateInfromationData.setSign("spu");
        List<TTemplateInfromationData> tTemplateInfromationDataspulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        tTemplateInfromationData.setSign("spuValues");
        List<TTemplateInfromationData> tTemplateInfromationDataspuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData  templateInfromationData : tTemplateInfromationDataspulist) {
            for (TTemplateInfromationData templateInfromationData1 : tTemplateInfromationDataspuValuelist) {
                if (templateInfromationData.getColumnId().equals(templateInfromationData1.getColumnId())) {
                    if (templateInfromationData.getColumnName().equals("材质")) {
                        productExportVO.setMaterial(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分1")) {
                        productExportVO.setIngredient1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分1成分比例")) {
                        productExportVO.setIngredient1Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分2")) {
                        productExportVO.setIngredient2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分2成分比例")) {
                        productExportVO.setIngredient2Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分3")) {
                        productExportVO.setIngredient3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("成分3成分比例")) {
                        productExportVO.setIngredient3Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("图案")) {
                        productExportVO.setPattern(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("细节")) {
                        productExportVO.setDetails(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("领型")) {
                        productExportVO.setCollarType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("风格")) {
                        productExportVO.setStyle(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("护理说明")) {
                        productExportVO.setCareInstructions(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料")) {
                        productExportVO.setFabric(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料弹性")) {
                        productExportVO.setFabricElasticity(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("适用人群")) {
                        productExportVO.setTargetAudience(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("季节")) {
                        productExportVO.setSeason(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("是否透明")) {
                        productExportVO.setIsIndividuallyPackaged(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("版型")) {
                        productExportVO.setSilhouette(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("织造方式")) {
                        productExportVO.setWeavingMethod(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("印花类型")) {
                        productExportVO.setPrintingType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料纹理1")) {
                        productExportVO.setFabricTexture1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重1（g/m²)")) {
                        productExportVO.setFabricWeight1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料纹理")) {
                        productExportVO.setLiningTexture(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料克重（g/m²)")) {
                        productExportVO.setLiningWeight(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里料克重（g/m²)单位")) {
                        productExportVO.setLiningWeightUnit(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分1")) {
                        productExportVO.setLiningIngredient1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分1成分比例")) {
                        productExportVO.setLiningIngredient1Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分2")) {
                        productExportVO.setLiningIngredient2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分2成分比例")) {
                        productExportVO.setLiningIngredient2Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分3")) {
                        productExportVO.setLiningIngredient3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分3成分比例")) {
                        productExportVO.setLiningIngredient3Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分4")) {
                        productExportVO.setLiningIngredient4(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分4成分比例")) {
                        productExportVO.setLiningIngredient4Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分5")) {
                        productExportVO.setLiningIngredient5(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("里衬成分5成分比例")) {
                        productExportVO.setLiningIngredient5Ratio(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("次要材质")) {
                        productExportVO.setSecondaryMaterial(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("袖型")) {
                        productExportVO.setSleeveType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("袖长")) {
                        productExportVO.setSleeveLength(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("长度")) {
                        productExportVO.setLength(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("场合")) {
                        productExportVO.setOccasion(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("场景")) {
                        productExportVO.setScene(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("廓形")) {
                        productExportVO.setOutline(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("门襟类型")) {
                        productExportVO.setPlacketType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("下摆形状")) {
                        productExportVO.setHemShape(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("腰带")) {
                        productExportVO.setWaistband(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("功能类型")) {
                        productExportVO.setFunctionType(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("胸垫")) {
                        productExportVO.setChestPad(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("品牌名")) {
                        productExportVO.setBrandName(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("二次工艺")) {
                        productExportVO.setSecondaryProcess(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重")) {
                        productExportVO.setFabricWeight(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料纹理2")) {
                        productExportVO.setFabricTexture2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重2（g/m²)")) {
                        productExportVO.setFabricWeight2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("面料克重2（g/m²)单位")) {
                        productExportVO.setFabricWeight2Unit(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("款式来源")) {
                        productExportVO.setDesignSource(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿模特")) {
                        productExportVO.setFittingModel(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿尺码")) {
                        productExportVO.setFittingSize(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("试穿感受")) {
                        productExportVO.setFittingExperience(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图1")) {
                        productExportVO.setCarouselImage1(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图2")) {
                        productExportVO.setCarouselImage2(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图3")) {
                        productExportVO.setCarouselImage3(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图4")) {
                        productExportVO.setCarouselImage4(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图5")) {
                        productExportVO.setCarouselImage5(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图6")) {
                        productExportVO.setCarouselImage6(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图7")) {
                        productExportVO.setCarouselImage7(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图8")) {
                        productExportVO.setCarouselImage8(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图9")) {
                        productExportVO.setCarouselImage9(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("商品轮播图10")) {
                        productExportVO.setCarouselImage10(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("详情图文-英语")) {
                        productExportVO.setDetailDescriptionEn(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("主图视频")) {
                        productExportVO.setMainVideo(templateInfromationData1.getColumnValue());
                    }
                    if (templateInfromationData.getColumnName().equals("详情视频")) {
                        productExportVO.setDetailVideo(templateInfromationData1.getColumnValue());
                    }

                }
            }
            tTemplateInfromationData.setSign("sku");
            List<TTemplateInfromationData> tTemplateInfromationDataskulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
            tTemplateInfromationData.setSign("skuValues");
            List<TTemplateInfromationData> tTemplateInfromationDataskuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
            for(TTemplateInfromationData templateInfromationSkuData : tTemplateInfromationDataskulist){
                for (TTemplateInfromationData templateInfromationSkuValueData:tTemplateInfromationDataskuValuelist){
                    if (templateInfromationSkuData.getColumnId().equals(templateInfromationSkuValueData.getColumnId())) {
                        if (templateInfromationSkuData.getColumnName().equals("色值(主规格)")){
                            productExportVOgeneral.setColorValue(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("规格类型2")){
                            productExportVOgeneral.setSpecType2(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码组别")){
                            productExportVOgeneral.setSizeGroup(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码类型")){
                            productExportVOgeneral.setSizeType(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("申报价格-美国站")){
                            productExportVOgeneral.setDeclaredPriceUs(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("币种")){
                            productExportVOgeneral.setCurrency(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("发货仓1库存")){
                            productExportVOgeneral.setWarehouse1Stock(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("最长边（cm）")){
                            productExportVOgeneral.setLongestSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("次长边（cm）")){
                            productExportVOgeneral.setMiddleSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("最短边（cm）")){
                            productExportVOgeneral.setShortestSideCm(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("重量（g）")){
                            productExportVOgeneral.setWeightG(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图1")){
                            productExportVOgeneral.setCarouselImage1(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图2")){
                            productExportVOgeneral.setCarouselImage2(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图3")){
                            productExportVOgeneral.setCarouselImage3(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图4")){
                            productExportVOgeneral.setCarouselImage4(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("商品轮播图5")){
                            productExportVOgeneral.setCarouselImage5(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("尺码")){
                            sizelist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("肩宽")){
                            shoulderWidthlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("胸围全围")){
                            bustCircumferencelist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("衣长")){
                            garmentLengthlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                        if (templateInfromationSkuData.getColumnName().equals("袖长")){
                            sleeveLengthCmlist.add(templateInfromationSkuValueData.getColumnValue());
                        }
                    }
                }
            }
        }
        productExportVOS.add(productExportVO);
        for (int i = 0; i < sleeveLengthCmlist.size(); i++) {
            ProductExportVO sizeVO = new ProductExportVO();
            BeanUtils.copyProperties(productExportVO, sizeVO);
            // 设置尺码特有属性
            sizeVO.setSkuNumber(spuCode );
            sizeVO.setSize(sizelist.get(i));
            sizeVO.setShoulderWidth(shoulderWidthlist.get(i));
            sizeVO.setBustCircumference(bustCircumferencelist.get(i));
            sizeVO.setGarmentLength(garmentLengthlist.get(i));
            sizeVO.setSleeveLengthCm(sleeveLengthCmlist.get(i));

            productExportVOS.add(sizeVO);
        }

        return productExportVOS;
    }


    @GetMapping(value = "/select/{id}")
    public AjaxResult selectinformation(@PathVariable("id") Integer id) {

        List<TTemplateInfromationDataVO> overallList = new ArrayList<>();
        List<TTemplateInfromationDataVO> baseList = new ArrayList<>();
        List<TTemplateInfromationDataVO> spuList = new ArrayList<>();
        List<TTemplateInfromationDataVO> skuList = new ArrayList<>();
        List<TTemplateInfromationDataVO> baseValuesList = new ArrayList<>();
        List<TTemplateInfromationDataVO> spuValuesList = new ArrayList<>();
        List<TTemplateInfromationDataVO> skuValuesList = new ArrayList<>();
        List<List<TTemplateInfromationDataVO>> skuValuesLists = new ArrayList<>();
        TTemplateInformation tTemplateInformation = tTemplateInformationService.selectTTemplateInformationById(id);
        String templateId = tTemplateInformation.getTemplateId();
        TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
        tTemplateInfromationData.setTemplateId(templateId);
        tTemplateInfromationData.setSign("overall");
        List<TTemplateInfromationData> tTemplateInfromationDataOveralllist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        TTemplateInformationVO tTemplateInformationVO = new TTemplateInformationVO();
        tTemplateInformationVO.setName(tTemplateInformation.getTemplateName());
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataOveralllist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setName(tTemplateInfromationDatas.getColumnName());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setRequired(Boolean.valueOf(tTemplateInfromationDatas.getColumnRequired()));
            overallList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("base");
        List<TTemplateInfromationData> tTemplateInfromationDatabaselist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDatabaselist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setName(tTemplateInfromationDatas.getColumnName());
            tTemplateInfromationDataVO.setHead(tTemplateInfromationDatas.getColumnHead());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
//            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setRequired(Boolean.valueOf(tTemplateInfromationDatas.getColumnRequired()));
            tTemplateInfromationDataVO.setRemark(tTemplateInfromationDatas.getRemark());
            baseList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("spu");
        List<TTemplateInfromationData> tTemplateInfromationDataSpulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataSpulist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setName(tTemplateInfromationDatas.getColumnName());
            tTemplateInfromationDataVO.setHead(tTemplateInfromationDatas.getColumnHead());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setOptions(tTemplateInfromationDatas.getOptions());
//            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setRequired(Boolean.valueOf(tTemplateInfromationDatas.getColumnRequired()));
            tTemplateInfromationDataVO.setRemark(tTemplateInfromationDatas.getRemark());
            spuList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("sku");
        List<TTemplateInfromationData> tTemplateInfromationDataSkulist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataSkulist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setName(tTemplateInfromationDatas.getColumnName());
            tTemplateInfromationDataVO.setHead(tTemplateInfromationDatas.getColumnHead());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setRequired(Boolean.valueOf(tTemplateInfromationDatas.getColumnRequired()));
            tTemplateInfromationDataVO.setRemark(tTemplateInfromationDatas.getRemark());
            skuList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("baseValues");
        List<TTemplateInfromationData> tTemplateInfromationDataBaseValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataBaseValuelist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
//            tTemplateInfromationDataVO.setName(tTemplateInfromationDatas.getColumnName());
//            tTemplateInfromationDataVO.setHead(tTemplateInfromationDatas.getColumnHead());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setNoSplit(tTemplateInfromationDatas.getNoSplit());
            baseValuesList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("spuValues");
        List<TTemplateInfromationData> tTemplateInfromationDataSpuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataSpuValuelist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setNoSplit(tTemplateInfromationDatas.getNoSplit());
            spuValuesList.add(tTemplateInfromationDataVO);
        }
        tTemplateInfromationData.setSign("skuValues");
        List<TTemplateInfromationData> tTemplateInfromationDataSkuValuelist = tTemplateInfromationDataService.selectTTemplateInfromationDataList(tTemplateInfromationData);
        for (TTemplateInfromationData tTemplateInfromationDatas : tTemplateInfromationDataSkuValuelist) {
            TTemplateInfromationDataVO tTemplateInfromationDataVO = new TTemplateInfromationDataVO();
            tTemplateInfromationDataVO.setIndex(tTemplateInfromationDatas.getIndexs());
            tTemplateInfromationDataVO.setColumn(tTemplateInfromationDatas.getColumnId());
            tTemplateInfromationDataVO.setType(tTemplateInfromationDatas.getColumnType());
            tTemplateInfromationDataVO.setValue(tTemplateInfromationDatas.getColumnValue());
            tTemplateInfromationDataVO.setNoSplit(tTemplateInfromationDatas.getNoSplit());
            skuValuesList.add(tTemplateInfromationDataVO);
        }
        tTemplateInformationVO.setOverall(overallList);
        tTemplateInformationVO.setBase(baseList);
        tTemplateInformationVO.setBaseValues(baseValuesList);
        tTemplateInformationVO.setSpu(spuList);
        tTemplateInformationVO.setSpuValues(spuValuesList);
        tTemplateInformationVO.setSku(skuList);
        skuValuesLists.add(skuValuesList);
        tTemplateInformationVO.setSkuValues(skuValuesLists);
        return success(tTemplateInformationVO);
    }

    /**
     * 新增半托数据商品模板
     */
//    @PreAuthorize("@ss.hasPermi('system:information:add')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTemplateInformation tTemplateInformation) {
        return toAjax(tTemplateInformationService.insertTTemplateInformation(tTemplateInformation));
    }

    @PostMapping("/added")
    public AjaxResult added(@RequestBody TTemplateInformationVO tTemplateInformationVO) {
        TTemplateInformation tTemplateInformation = new TTemplateInformation();

        String id = generate();
        String templateId = "t_" + id;
        tTemplateInformation.setTemplateId(templateId);
//        tTemplateInformation.setTeamId(1);
        tTemplateInformation.setCreateTime(DateUtils.getNowDate());
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getOverall()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("overall");
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnName(tTemplateInfromationDataVO.getName());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
            tTemplateInfromationData.setColumnRequired(String.valueOf(tTemplateInfromationDataVO.getRequired()));
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getBase()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("base");
            tTemplateInfromationData.setColumnName(tTemplateInfromationDataVO.getName());
            tTemplateInfromationData.setColumnHead(tTemplateInfromationDataVO.getHead());
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            tTemplateInfromationData.setColumnRequired(String.valueOf(tTemplateInfromationDataVO.getRequired()));
            tTemplateInfromationData.setRemark(tTemplateInfromationDataVO.getRemark());

//            tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSpu()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("spu");
            tTemplateInfromationData.setColumnName(tTemplateInfromationDataVO.getName());
            tTemplateInfromationData.setColumnHead(tTemplateInfromationDataVO.getHead());
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            if (tTemplateInfromationDataVO.getOptions().equals("")||tTemplateInfromationDataVO.getOptions()==null) {
                tTemplateInfromationData.setOptions(tTemplateInfromationDataVO.getOptions().toString());
            } else {
                tTemplateInfromationData.setOptions("");
            }
            tTemplateInfromationData.setColumnRequired(String.valueOf(tTemplateInfromationDataVO.getRequired()));
            tTemplateInfromationData.setRemark(tTemplateInfromationDataVO.getRemark());
//            tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSku()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("sku");
            tTemplateInfromationData.setColumnName(tTemplateInfromationDataVO.getName());
            tTemplateInfromationData.setColumnHead(tTemplateInfromationDataVO.getHead());
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            tTemplateInfromationData.setColumnRequired(String.valueOf(tTemplateInfromationDataVO.getRequired()));
            tTemplateInfromationData.setRemark(tTemplateInfromationDataVO.getRemark());
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getBaseValues()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("baseValues");
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            tTemplateInfromationData.setNoSplit(tTemplateInfromationDataVO.getNoSplit());
            tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInformationVO.getSpuValues()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            tTemplateInfromationData.setTemplateId(templateId);
            tTemplateInfromationData.setSign("spuValues");
            tTemplateInfromationData.setIndex(tTemplateInfromationDataVO.getIndex());
            tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
            tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
            tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
            tTemplateInfromationData.setNoSplit(tTemplateInfromationDataVO.getNoSplit());
            tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
        }
        for (List<TTemplateInfromationDataVO> tTemplateInfromationDataVOs : tTemplateInformationVO.getSkuValues()) {
            TTemplateInfromationData tTemplateInfromationData = new TTemplateInfromationData();
            for (TTemplateInfromationDataVO tTemplateInfromationDataVO : tTemplateInfromationDataVOs) {
                tTemplateInfromationData.setTemplateId(templateId);
                tTemplateInfromationData.setSign("skuValues");
                tTemplateInfromationData.setIndex(tTemplateInfromationDataVO.getIndex());
                tTemplateInfromationData.setColumnId(tTemplateInfromationDataVO.getColumn());
                tTemplateInfromationData.setColumnType(tTemplateInfromationDataVO.getType());
                tTemplateInfromationData.setColumnValue(tTemplateInfromationDataVO.getValue());
                tTemplateInfromationData.setNoSplit(tTemplateInfromationDataVO.getNoSplit());
                tTemplateInfromationDataService.insertTTemplateInfromationData(tTemplateInfromationData);
            }

        }


//        Long currentUserId = SecurityUtils.getUserId();
//        TUser user = tUserMapper.selectTUserById(currentUserId);
//        tTemplateInformation.setTeamId(user.getTeamId());
        tTemplateInformation.setTemplateName(tTemplateInformationVO.getName());

        return toAjax(tTemplateInformationService.insertTTemplateInformation(tTemplateInformation));

    }

    /**
     * 修改半托数据商品模板
     */
    @PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTemplateInformation tTemplateInformation) {
        return toAjax(tTemplateInformationService.updateTTemplateInformation(tTemplateInformation));
    }

    /**
     * 删除半托数据商品模板
     */
    @PreAuthorize("@ss.hasPermi('system:information:remove')")
    @Log(title = "半托数据商品模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(tTemplateInformationService.deleteTTemplateInformationByIds(ids));
    }

    public static String generate() {
        // 时间部分：12位（年月日时分秒）
        DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(timeFormat);

        // 随机数部分：4位
        int randomNum = ThreadLocalRandom.current().nextInt(0, 10000);
        String randomPart = String.format("%04d", randomNum);

        return timestamp + randomPart; // 组合为16位
    }
}
