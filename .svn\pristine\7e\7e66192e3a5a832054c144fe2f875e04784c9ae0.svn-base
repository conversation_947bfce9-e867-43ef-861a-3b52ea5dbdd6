
import { useEffect, useState } from 'react';
import { Button, Checkbox, message, Spin, Pagination } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { CopyOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getTitleExtractionTaskDetail } from '@/api/task'
import { batchZipUrl } from '@/api/common'
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs'
import { handleCopyText } from '@/utils/copyText'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const TitleExtractionDetail = () => {
    const location = useLocation();

    const [messageApi, messageContextHolder] = message.useMessage()

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [selectedIds, setSelectedIds] = useState<any[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    const { batchId } = location.state || {};

    const [record, setRecord] = useState<any>();
    const [pageLoading, setPageLoading] = useState(false);
    const [downloadLoading, setDownloadLoading] = useState(false);

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 200,
        total: 0,
    });
    const fetchDetail = (pageNum: any,) => {
        setPageLoading(true)
        getTitleExtractionTaskDetail({ id: batchId, pageNum, pageSize: 200 }).then((res: any) => {
            setRecord(res)
            // 更新分页信息
            setPagination(prev => ({
                ...prev,
                current: pageNum,
                total: res.total || 0,
            }))
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }
    useEffect(() => {
        fetchDetail(1)
    }, []);

    // 添加分页处理函数
    const handlePageChange = (page: number) => {

        fetchDetail(page)
    };

    const handleSelect = (task: any) => {

        const newSelectedIds = [...selectedIds];
        const newSelectedRows = [...selectedRows];
        const index = newSelectedIds.indexOf(task.id);

        if (index > -1) {
            // 如果已存在，则移除
            newSelectedIds.splice(index, 1);
            const rowIndex = newSelectedRows.findIndex(item => item.id === task.id);
            if (rowIndex > -1) {
                newSelectedRows.splice(rowIndex, 1);
            }
        } else {
            // 如果不存在，则添加
            newSelectedIds.push(task.id);
            newSelectedRows.push(task);
        }

        console.log('Updated selectedRows:', newSelectedRows);
        setSelectedIds(newSelectedIds);
        setSelectedRows(newSelectedRows);
        // 根据选择数量自动显示/隐藏操作栏
        setShowBatchActions(newSelectedRows.length > 0);
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedRows([]); // 清空已选图片
        setSelectedIds([]);
    };

    const cancelSelection = () => {
        setSelectedRows([]);
        setSelectedIds([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };

    // 下载图片功能
    const handleDownloadImgages = () => {
        setDownloadLoading(true);
        const imageUrls = selectedRows.map((row: any) => row.imageUrl).filter(Boolean);
        batchZipUrl({ imageUrls, type: 18 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false);
        });
    };


    // items 定义已移至 BottomActionBar 组件内部

    // 任务创建相关状态和函数已移至 BottomActionBar 组件内部

    // 任务相关状态已移至 BottomActionBar 组件内部

    // createTask 函数已移至 BottomActionBar 组件内部
    /*
    const createTask = async () => {
        const handleSuccess = () => {
            messageApi.success(`创建${taskInfo.typeName}成功`);
            // window.location.href = `/workspace/batchTools/${taskInfo.path}/index`; // 跳转到对应的任务列表页
            setTaskInfo({
                type: '',
                typeName: '',
                path: '',
                isModalOpen: false
            });
            userinfoService.refresh(); // 刷新用户积分
        };
        try {
            let params = {};
            if (taskInfo.type == '6') {
                params = {
                    styleId: radioValue == '1' ? '' : currentStyle?.styleId,
                    style: radioValue == '1' ? '无风格' : currentStyle?.style,
                    stylePrompt: radioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                    imageScale,
                }
            } else if (taskInfo.type == '8') {
                params = {
                    styleId: radioValue == '1' ? '' : currentStyle?.styleId,
                    style: radioValue == '1' ? '无风格' : currentStyle?.style,
                    stylePrompt: radioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                    isMattingFree: isMattingFree ? 1 : 0,
                    imageScale,
                }
            } else if (taskInfo.type == '9') {
                params = {
                    similarity,
                    imageScale,
                }
            } else if (taskInfo.type == '12') {
                params = {
                    magnification: generateQuantity,
                }
            }
            if (taskInfo.type == '17') {
                if (!isChecked) {
                    messageApi.error('请先勾选确认侵权风险过滤功能使用须知')
                    return
                }
            }
            if (taskInfo.type == '18') {
                params = {
                    taskType
                }
            }

            const imageUrls = selectedRows.map((img) => img?.imageUrl);

            setCreatLoading(true);

            if (taskInfo.type == '18') {
                const res = await createTitleExtractionTaskByImageUrls({
                    imageUrls,
                    ...params
                });
                if (res) {
                    handleSuccess();
                }
            } else {
                const res = await createByImageUrls({
                    imageUrls,
                    type: taskInfo.type,
                    ...params
                });
                if (response) handleSuccess();
            }

        } catch (err: any) {
            messageApi.error(`创建失败: ${err?.data?.msg || err?.msg}`)
        } finally {
            setCreatLoading(false)
        }
    };
    */

    // 任务配置相关状态和函数已移至 BottomActionBar 组件内部

    // 风格相关接口和状态已移至 BottomActionBar 组件内部

    // 风格相关函数已移至 BottomActionBar 组件内部

    // 下载功能已移至 BottomActionBar 组件内部


    // 同步功能已移至 BottomActionBar 组件内部

    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.id}
                >
                    <LikeImage
                        type={image.id}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={image.imageUrl || ''}
                        oriImgUrl={image.imageUrl || ''}
                        smallImgUrl={image.imageUrl || ''}
                        markImgUrl={image.imageUrl || ''}
                        progress={1}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={true}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    // 任务配置相关状态已移至 BottomActionBar 组件内部
    const handleCopy = (title: any) => {
        handleCopyText(title, '标题')
    };
    // 导出功能已移至 BottomActionBar 组件内部

    // 导出模板功能已移至 BottomActionBar 组件内部

    // 导出模板相关状态和配置已移至 BottomActionBar 组件内部

    // handleExportTemplate 函数已移至 BottomActionBar 组件内部

    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : (<>
                {/* 导出和导出模板弹窗已移至 BottomActionBar 组件内部 */}
                {/* 标签输入弹窗已移至 BottomActionBar 组件内部 */}
                {/* 创建任务弹窗已移至 BottomActionBar 组件内部 */}
                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}>
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center  h-[60px] border-b-[1px] border-normal'>
                    <p className='mr-[20px]'>批次: {record?.taskBatch}</p>
                    <p className='mr-[20px]'>创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    <p>总数：{record?.totalAmount}
                        <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.successAmount}</span>
                        {record?.failAmount > 0 && <span style={{ color: '#cf1322', marginLeft: '6px' }}>失败：{record?.failAmount}</span>}
                    </p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="grid  p-[10px] grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5  xxl:grid-cols-7 gap-4">
                        {record?.detailList.length > 0 && record?.detailList.map((task: any, index: number) => (
                            <div className='aspect-square  bg-[#f4f8fe]'>
                                <div key={index} className="w-full h-full flex  items-center justify-center  relative group overflow-hidden">
                                    <Checkbox
                                        className="absolute top-2 left-3 z-10"
                                        style={{ transform: 'scale(1.25)' }}  // 放大1.25倍
                                        checked={selectedIds.includes(task.id)}
                                        onChange={() => handleSelect(task)}
                                        disabled={checkImageDisabled(task, 'titleExtraction')}
                                    />
                                    {/* {task.hasUploaded && <p className="absolute bottom-4  z-10"
                                        style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>} */}
                                    {getTaskImageComponent(task)}
                                </div>
                                <p
                                    className='text-[13px] mt-3 mb-2  h-[42px] overflow-y-scroll scrollbar-container scrollbar-hide pl-3 pr-2'
                                >
                                    {task.temuProductTitle || ''}
                                    <CopyOutlined
                                        className={'cursor-pointer ml-2'}
                                        onClick={() => handleCopy(task.temuProductTitle || '')}
                                    />
                                </p>
                            </div>
                        ))}
                    </div>
                    {record?.detailList.length > 0 && <Pagination
                        align="center"
                        style={{ margin: '20px 0 120px' }}
                        current={pagination.current}
                        pageSize={pagination.pageSize}
                        total={pagination.total}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                    />}
                </div>

                {/* BottomActionBar 组件 */}
                <BottomActionBar
                    visible={selectedIds.length > 0 || showBatchActions}
                    selectedCount={selectedIds.length}
                    isAllSelected={record?.detailList?.length > 0 && selectedIds.length === record.detailList.filter((item: any) => !checkImageDisabled(item, 'titleExtraction')).length}
                    onToggleSelectAll={() => {
                        const enabledItems = record?.detailList.filter((item: any) => !checkImageDisabled(item, 'titleExtraction')) || [];
                        const enabledIds = enabledItems.map((item: any) => item.id);

                        if (enabledItems.length > 0 && selectedIds.length === enabledIds.length) {
                            // 取消全选
                            setSelectedIds([]);
                            setSelectedRows([]);
                        } else {
                            // 全选当前页
                            const currentPageIds = enabledIds || [];
                            const currentPageRows = enabledItems || [];
                            setSelectedIds([...new Set([...selectedIds, ...currentPageIds])]);
                            setSelectedRows([...selectedRows, ...currentPageRows.filter((row: any) => !selectedRows.some(r => r.id === row.id))]);
                        }
                    }}
                    onCancelSelection={cancelSelection}

                    // 同步功能
                    syncEnabled={true}
                    selectedItems={selectedRows}
                    extractImageUrl={(item: any) => {
                        const url = item?.imageUrl;
                        return url;
                    }}
                    syncExtraParams={{ type: 18 }}

                    // 下载功能 - 移除空的onDownload回调，让组件内部处理
                    downloadLoading={downloadLoading}
                    downloadDisabled={selectedIds.length === 0}
                    onDownload={handleDownloadImgages}

                    // 任务创建功能
                    enableWorkflow={userInfo?.currentMode == 2}

                    // 启用标题提取页专用功能
                    enableGatherExport={true}
                    enableGatherExportTemplate={userInfo?.currentMode == 2}
                    enableGatherDelete={false} // 标题提取页面不需要删除功能
                    extractGatherId={(item: any) => item.id}
                    extractGatherImageUrl={(item: any) => item.imageUrl}
                    extractGatherTitle={(item: any) => item.temuProductTitle || ''}

                    // 完成回调
                    onActionFinished={() => {
                        // 刷新积分和数据
                        userinfoService.refresh();
                        fetchDetail(pagination.current);
                        setSelectedIds([]);
                        setSelectedRows([]);
                    }}

                    actionDisabled={selectedIds.length === 0}
                />
            </>)}
        </div >
    );
};

export default TitleExtractionDetail;