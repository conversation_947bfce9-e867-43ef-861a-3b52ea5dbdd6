/**
 * @Author: xujing
 * @Date: 2025/3/3
 * @Description: ""
 */
import { useMemo, useEffect, useState, useContext } from 'react';
import { Segmented, Image, Upload, Tabs, Modal, Input, Button, Radio, Select, message, Tooltip, Pagination, Collapse } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { useMemoizedFn } from 'ahooks'
import { CloudUploadOutlined, CopyOutlined, FileImageOutlined, FormOutlined, DeleteOutlined, ExclamationCircleFilled, LoadingOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { useTaskService } from '@/common/services/task/taskContext'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import {
    getCommonStyleList, getCustomStyleList, addCustomStyleList, delCustomStyleList, createTask, executeTask, getTask, getHistoryList, getMyCollection, getNewestTaskResult,
    getHistoryImage, deleteReferenceFile, deleteTask, getAssociation, imageIntoText, taskExecAgain, taskEditAgain
} from '@/api/task'
import { uploadFileOssApi } from '@/api/common'
import { createSocketService } from '@/helper/services/socket/SocketService'
import { ESocketPushType } from '@/types/socket'
import type { CollapseProps } from 'antd'
import classNames from 'classnames'
import styles from './index.module.scss'
import { handleCopyText } from '@/utils/copyText'


export const Continuous = () => {

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()
    const taskService = useTaskService()
    const socketService = createSocketService({})

    useEffect(() => {
        getTaskInfo([6, 7])
    }, []);

    const [tab, setTab] = useState('文生图模式');
    const generateQuantityOptions: CheckboxGroupProps<string>['options'] = [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '4', value: '4' },
    ];

    const [generateQuantity, setGenerateQuantity] = useState('1')

    // 选择图片数量后改变消耗的积分数
    const handleQuantityChange = (e: any) => {
        console.log('Selected Quantity:', e.target.value);
        setGenerateQuantity(e.target.value)
    };

    const [imageScale, setImageScale] = useState('1:1')
    const handleChange = (value: string) => {
        console.log(`selected ${value}`);
        setImageScale(value)
    };

    const [imgToImgScale, setImgToImgScale] = useState('原图比例')
    const handleScaleChange = (value: string) => {
        console.log(`selected ${value}`);
        setImgToImgScale(value)

    };


    type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

    const getBase64 = (file: FileType): Promise<string> =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = (error) => reject(error);
        });
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [fileList, setFileList] = useState<any[]>([]);

    const handlePreview = async (file: any) => {
        if (!file.url && !file.preview) {
            file.preview = await getBase64(file.originFileObj as FileType);
        }

        setPreviewImage(file.url || (file.preview as string));
        setPreviewOpen(true);
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            <CloudUploadOutlined style={{ fontSize: '30px' }} />
            <div style={{ fontSize: '12px', marginTop: 4 }}>Upload</div>
        </button>
    );

    // 任务执行结果中图片的数据结构
    interface ITaskResultImage {
        imageId: string
        resImgUrl: string
        resSmallImgUrl: string
        markImgUrl: string
        follow: number
        zan: number
        progress: number
        queue: number
        taskOrdinalId: string
        taskId: string
        originalImgUrl: string
        ordinal: number
        type: string
        extra: string
        seed: number
    }

    interface taskOrdinalList {
        createTime: string;
        taskId: string;
        taskOrdinalId: string;
        // 返回图片列表
        ordinalImgResultList: ITaskResultImage[];
        status: string;
        originImgUrl: string;
        thumbnailImgUrl: string;
        prompt: string;
        shortCutDesc: string;
        taskParam: string
        type: string
        originalUrl: string
        ordinal: number;
        // 其他属性...
    }

    // 生成结果列表
    const [resultList, setResultList] = useState<HistoryItem[]>([]);
    // const [resultList, setResultList] = useState<taskOrdinalList[]>([]);

    const handleCopy = (id: string) => {
        handleCopyText(id, '任务 ID')
    };


    // 图片列表渲染
    const getTaskResultImageComponent = useMemoizedFn(
        (image: any, ordinalImgResultList: any[], index: number, status: any, waitTime: number) => {
            let previewImageList = ordinalImgResultList.map(
                (result) => result.resImgUrl
            )
            return (
                <div
                    className={
                        'aspect-square max-w-[310px] max-h-[310px] rounded-lg bg-[#EEF2FF] overflow-hidden'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        onFollow={() => {
                            taskService.refresh()
                        }}
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        isFollow={image.follow === 1}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.resImgUrl}
                        oriImgUrl={image.type == '7' ? image.originalImgUrl : image.resImgUrl}
                        smallImgUrl={image.resSmallImgUrl}
                        markImgUrl={image.markImgUrl}
                        waitTime={waitTime}
                        progress={image.progress}
                        previewImages={previewImageList}
                        index={index}
                        seed={image.seed}
                        delVisible={false}
                        likeVisible={true}
                        comparison={image.type == '7' ? true : false}
                        status={status}
                    />
                </div>
            )
        }
    )
    // 收藏图片列表渲染
    const getLikeResultImageComponent = useMemoizedFn(
        (image: any, index: number) => {
            return (
                <div
                    className={
                        'aspect-square max-w-[310px] max-h-[310px] mb-[20px] rounded-lg bg-[#EEF2FF] overflow-hidden'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        onFollow={() => {
                            taskService.refresh()
                        }}
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        isFollow={image.follow === 1}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.resImgUrl}
                        oriImgUrl={image.type == '7' ? image.originalImgUrl : image.resImgUrl}
                        smallImgUrl={image.resSmallImgUrl}
                        markImgUrl={image.resImgUrl}
                        progress={image.progress}
                        previewImages={[]}
                        index={index}
                        seed={image.seed}
                        delVisible={false}
                        likeVisible={true}
                        comparison={image.type == '7' ? true : false}
                    />
                </div>
            )
        }
    )
    // 传过的图片列表渲染
    const getUploadedImageComponent = useMemoizedFn(
        (image: any, index: number) => {
            return (
                <div
                    className={
                        'aspect-square max-w-[310px] max-h-[310px] mb-[20px] rounded-lg bg-[#EEF2FF] overflow-hidden'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        onDelete={() => {
                            fetchHistoryImage([6, 7], 1, 12);
                            setUploadedPage(1);
                        }}
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.originImgUrl}
                        oriImgUrl={image.originImgUrl}
                        smallImgUrl={image.thumbnailUrl || image.originImgUrl}
                        markImgUrl={image.originImgUrl}
                        progress={image.progress}
                        previewImages={[]}
                        index={index}
                        seed={-1}
                        delVisible={true}
                        likeVisible={false}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    interface TaskOrdinal {
        createTime: string;
        taskId: string;
        taskOrdinalId: string;
        ordinalImgResultList: ITaskResultImage[];
        status: string;
        waitTime: number;
        originImgUrl: string;
        thumbnailImgUrl: string;
        shortCutDesc: string;
        taskParam: string;
        type: string;
        originalUrl: string;
        ordinal: number;
        // 其他属性...
    }

    interface HistoryItem {
        taskOrdinalList: TaskOrdinal[];
    }
    interface HistoryListResponse {
        data: HistoryItem[];
        total: number;
        // 其他属性...
    }

    const [historyList, setHistoryList] = useState<HistoryItem[]>([]);
    const [historyTotal, setHistoryTotal] = useState(0);

    //获取历史记录列表
    const fetchHistoryList = async (types: any, pagenum: number, pageSize: number) => {
        setHistoryList([])
        try {
            const response = await getHistoryList(pagenum, pageSize, types) as HistoryListResponse;
            setHistoryList(response.data);
            setHistoryTotal(response.total)

        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };
    const [collectionList, setCollectionList] = useState<any[]>([]);
    const [collectionTotal, setCollectionTotal] = useState(0);
    //获取我的收藏列表
    const fetchMyCollectionList = (types: any, pagenum: number, pageSize: number) => {
        getMyCollection(pagenum, pageSize, types).then((value: any) => {
            setCollectionList(value.data)
            setCollectionTotal(value.total)
        })

    };
    const [historyImageList, setHistoryImageList] = useState<any[]>([]);
    const [uploadedTotal, setUploadedTotal] = useState(0);
    //获取上传过的图片列表
    const fetchHistoryImage = (types: any, pagenum: number, pageSize: number) => {
        getHistoryImage(pagenum, pageSize, types).then((value: any) => {
            setHistoryImageList(value.data)
            setUploadedTotal(value.total)
        })

    };
    const [activeKey, setActiveKey] = useState('1')

    const handleTabsChange = (key: string) => {
        setActiveKey(key);
        if (key == '1') {
            // getTaskInfo([6, 7])
        } else if (key == '2') {
            setHistoryPage(1);  // 重置页码
            fetchHistoryList([6, 7], 1, 10)
        } else if (key == '3') {
            setCollectionPage(1);
            fetchMyCollectionList([6, 7], 1, 12)
        } else {
            setUploadedPage(1);
            fetchHistoryImage([6, 7], 1, 12)
        }
    };
    // 再次编辑 
    const [againEditTaskId, setAgainEditTaskId] = useState('')

    const againEdit = useMemoizedFn((item: any) => {
        taskEditAgain(item.originImgUrl || '', item.taskOrdinalId || '', item.taskId || '', item.type || '',).then((res: any) => {
            setAgainEditTaskId(res.taskId || '')// 再次编辑接口生成的taskId
            setActiveKey('1')
            setResultList([])
            setTab(item.type == '6' ? '文生图模式' : '图生图模式')
            if (item.type == '6') {
                setValueInput(item.shortCutDesc)
                let id = JSON.parse(item.taskParam)?.styleId
                let filteredStyle = commonStyleList.find(item => item.styleId === id);
                if (id) {
                    setStyleActiveKey(['1'])
                    setCurrentStyle({
                        styleId: filteredStyle?.styleId || '',
                        id: '',
                        thumbnailImgUrl: filteredStyle?.thumbnailImgUrl || '',
                        originImgUrl: filteredStyle?.originImgUrl || '',
                        style: filteredStyle?.style || '',
                        stylePrompt: filteredStyle?.stylePrompt || ''
                    })
                } else {
                    setStyleActiveKey([])
                    setCurrentStyle(commonStyleList[0])
                }


            } else {
                const newFile = {
                    uid: '-1', // 可以自定义一个唯一标识符，这里简单用 -1 示例
                    name: 'img.png', // 文件名
                    status: 'done', // 表示已完成状态
                    url: item.originImgUrl, // 图片地址
                };
                setFileList([newFile]);
                setReferenceImgUrl(item.originImgUrl)
            }
            let taskParam = JSON.parse(item.taskParam)
            setGenerateQuantity((taskParam?.imageNumber).toString())
            if (item.type == '6') {
                setImageScale(taskParam?.imageScale)
            } else {
                setImgToImgScale(taskParam?.imageScale)
            }

        }).catch(err => {
            messageApi.error(`再次编辑失败：${err?.data?.msg}`)
        })

    })

    // 再次生成
    const againExec = useMemoizedFn((item: any) => {

        modal.confirm({
            centered: true,
            title: (
                <div className={'text-[18px] text-normal text-center'}>再次生成</div>
            ),
            content: (
                <div className={'text-normal text-center my-[10px]'}>
                    再次生成图片，需消耗
                    <span className={'text-[#F42929]'}> {JSON.parse(item.taskParam)?.imageNumber || '4'} </span>
                    积分，是否生成？
                </div>
            ),
            icon: null,
            okText: '生成',
            cancelText: '取消',
            onOk() {
                taskExecAgain(item.originImgUrl || '', item.taskId || '', item.taskOrdinalId || '', item.type || '',).then(res => {
                    messageApi.success('再次生成成功')
                    userinfoService.refresh() //刷新用户积分
                    // 获取任务详情
                    setActiveKey('1')
                    getTaskInfo([6, 7])
                }).catch(err => {
                    messageApi.error(`再次生成失败：${err?.data?.msg}`)
                })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    })
    // 删除任务
    const handleDelTask = (id: string, type: string) => {
        modal.confirm({
            centered: true,
            title: (
                <div className={'text-[18px] text-normal'}> 确认删除该任务？</div>
            ),
            content: null,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            onOk() {
                deleteTask([id]).then(res => {
                    messageApi.success('任务删除成功')
                    if (type == 'result') {
                        // 重新获取任务详情
                        getTaskInfo([6, 7])
                    } else {
                        // 重新请求历史记录列表
                        fetchHistoryList([6, 7], 1, 10)
                        setHistoryPage(1);  // 重置页码
                    }

                }).catch(err => {
                    messageApi.error(`任务删除失败：${err?.data?.msg}`)
                })


            },
            onCancel() {
                console.log('Cancel')
            }
        })

    };

    const [historyPage, setHistoryPage] = useState(1);
    const [collectionPage, setCollectionPage] = useState(1);
    const [uploadedPage, setUploadedPage] = useState(1);
    const handleHistoryPage = (page: number, pageSize: number) => {
        setHistoryPage(page);  // 更新页码状态
        fetchHistoryList([6, 7], page, pageSize)
    }
    const handleMyCollectionPage = (page: number, pageSize: number) => {
        setCollectionPage(page);
        fetchMyCollectionList([6, 7], page, pageSize)
    }
    const handleUploadedPage = (page: number, pageSize: number) => {
        setUploadedPage(page);
        fetchHistoryImage([6, 7], page, pageSize)
    }
    // 默认没有值的组件
    const emptyComponent = useMemo(() => {
        return (
            <div className={'flex justify-center items-center h-full'}>
                <img
                    src={require('@/asset/image/empty.png')}
                    className={'w-[282px]'}
                    alt=""
                />
            </div>
        )
    }, [])

    const tabItems = [
        {
            key: '1',
            label: '生成结果',
            children: (
                <div className='h-[calc(100vh-192px)]  flex-1 overflow-y-scroll scrollbar-container scrollbar-hide'>
                    {resultList.length > 0 ? resultList.map((task, inx) => (
                        <div key={inx}>
                            <div className='bg-[#eee] h-[auto] mb-[16px] border border-normal  rounded-lg' style={{ padding: '20px 10px 0 10px' }}>
                                {task.taskOrdinalList.map((item, index) => (
                                    <div key={index}>
                                        <div className='flex items-center'>第{item.ordinal}次执行 {item.type == '6' ? `/ ${JSON.parse(item.taskParam)?.style}` : ''} /
                                            {item.type == '6' &&
                                                <Tooltip title={item.shortCutDesc && item.shortCutDesc.length > 6 ? item.shortCutDesc : ''}>
                                                    <p
                                                        style={{
                                                            width: '100px', // 控制文本框宽度
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis',
                                                            display: 'inline-block',
                                                            marginLeft: '4px'
                                                        }}
                                                    >
                                                        {item.shortCutDesc}
                                                    </p>
                                                </Tooltip>
                                            }
                                            {item.type == '7' &&
                                                <img
                                                    className={'w-[32px] h-[32px] ml-[10px]'}
                                                    src={item.originImgUrl}
                                                />
                                            }
                                        </div>
                                        <div className='flex justify-between items-center mb-[20px]'>
                                            <p>{item.createTime}
                                                <span> / </span> 任务ID:{item.taskOrdinalId} <CopyOutlined className={'cursor-pointer'} onClick={() => handleCopy(item.taskOrdinalId)} /></p>
                                            <div>
                                                {index == 0 && <Button type="default" disabled={item.status == '3' || item.status == '4'} icon={<FormOutlined />} onClick={() => againEdit(item)}  >再次编辑 </Button>}
                                                {index == 0 && <Button type="default" disabled={item.status == '3' || item.status == '4'} className='ml-[6px]' icon={<FileImageOutlined />} onClick={() => againExec(item)}>再次生成 </Button>}
                                                {index == 0 && <Button type="default" className='ml-[6px]' onClick={() => handleDelTask(item.taskId, 'result')} icon={<DeleteOutlined />} >删除任务 </Button>}
                                            </div>
                                        </div>
                                        <div className={'flex flex-wrap'}>
                                            {item.ordinalImgResultList && item.ordinalImgResultList.map((image, index) => (
                                                <div
                                                    className={'max-w-[310px]  w-[25%] pr-[12px] pl-[12px] mb-[20px]'}
                                                    key={image.imageId}
                                                >
                                                    {getTaskResultImageComponent(image, item.ordinalImgResultList, index, item.status, item.waitTime ?? -1)}
                                                </div>
                                            ))}
                                        </div>
                                    </div>))}
                            </div>
                        </div>
                    )) : emptyComponent}
                </div>
            ),
        },
        {
            key: '2',
            label: '历史记录',
            children: (
                <div className='h-[calc(100vh-192px)]  flex-1 overflow-y-scroll scrollbar-container scrollbar-hide'>

                    {historyList.length > 0 ? historyList.map((history, inx) => (
                        <div key={inx}>

                            <div className='bg-[#eee] h-[auto] mb-[16px] border border-normal  rounded-lg' style={{ padding: '20px 10px 0 10px' }}>
                                {history.taskOrdinalList.map((item, index) => (
                                    <div key={index}>
                                        <div className='flex items-center'>第{item.ordinal}次执行  {item.type == '6' ? `/ ${JSON.parse(item.taskParam)?.style || ' '}` : ''} /
                                            {item.type == '6' &&
                                                <Tooltip title={item.shortCutDesc && item.shortCutDesc.length > 6 ? item.shortCutDesc : ''}>
                                                    <p
                                                        style={{
                                                            width: '100px', // 控制文本框宽度
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis',
                                                            display: 'inline-block',
                                                            marginLeft: '4px'
                                                        }}
                                                    >
                                                        {item.shortCutDesc}
                                                    </p>
                                                </Tooltip>

                                            }
                                            {item.type == '7' &&
                                                <img
                                                    className={'w-[32px] h-[32px] ml-[10px]'}
                                                    src={item.originImgUrl}
                                                />
                                            }
                                        </div>
                                        <div className='flex justify-between items-center mb-[20px]'>
                                            <p>{item.createTime}
                                                <span> / </span> 任务ID:{item.taskOrdinalId} <CopyOutlined className={'cursor-pointer'} onClick={() => handleCopy(item.taskOrdinalId)} /></p>
                                            <div>
                                                {index == 0 && <Button type="default" icon={<FormOutlined />} onClick={() => againEdit(item)}  >再次编辑 </Button>}
                                                {index == 0 && <Button type="default" className='ml-[6px]' icon={<FileImageOutlined />} onClick={() => againExec(item)}>再次生成 </Button>}
                                                {index == 0 && <Button type="default" className='ml-[6px]' onClick={() => handleDelTask(item.taskId, 'history')} icon={<DeleteOutlined />} >删除任务 </Button>}
                                            </div>
                                        </div>
                                        <div className={'flex flex-wrap'}>
                                            {item.ordinalImgResultList.map((image, imageIndex) => (
                                                <div
                                                    className={'max-w-[310px]  w-[25%] pr-[12px] pl-[12px] mb-[20px]'}
                                                    key={image.imageId}
                                                >
                                                    {getTaskResultImageComponent(image, item.ordinalImgResultList, imageIndex, '', -1)}
                                                </div>
                                            ))}
                                        </div>
                                    </div>))}
                            </div>
                        </div>
                    )) : emptyComponent}
                    {historyList.length > 0 && <Pagination align="center" current={historyPage} pageSize={10} onChange={handleHistoryPage} total={historyTotal} showSizeChanger={false} />}
                </div>
            ),
        },
        {
            key: '3',
            label: '我的收藏',
            children: (
                <div className='h-[calc(100vh-192px)]  flex-1 overflow-y-scroll scrollbar-container scrollbar-hide'>
                    {collectionList.length > 0 ? <div className={'flex flex-wrap'}>
                        {collectionList.map((image, index) => (
                            <div
                                className={'w-[25%] pr-[12px] pl-[12px]'}
                                key={image.imageId}
                            >
                                {getLikeResultImageComponent(image, 0)}
                            </div>
                        ))}
                    </div> : emptyComponent}
                    {collectionList.length > 0 && <Pagination align="center" current={collectionPage} pageSize={12} onChange={handleMyCollectionPage} total={collectionTotal} showSizeChanger={false} />}
                </div>
            ),
        },
        {
            key: '4',
            label: '传过的图',
            children: (
                <div className='h-[calc(100vh-192px)]  flex-1 overflow-y-scroll scrollbar-container scrollbar-hide'>
                    {historyImageList.length > 0 ? <div className={'flex flex-wrap'}>
                        {historyImageList.map((image, index) => (
                            <div
                                className={'w-[25%] pr-[12px] pl-[12px]'}
                                key={image.imageId}
                            >
                                {getUploadedImageComponent(image, index)}
                            </div>
                        ))}
                    </div> : emptyComponent}
                    {historyImageList.length > 0 && <Pagination align="center" current={uploadedPage} pageSize={12} onChange={handleUploadedPage} total={uploadedTotal} showSizeChanger={false} />}
                </div>
            ),
        },
    ];



    interface CustomStyle {
        styleId: string;
        id: string;
        originImgUrl: string;
        thumbnailImgUrl: string;
        stylePrompt: string;
        style: string;
        // 其他属性...
    }

    interface CustomStyleListResponse {
        data: CustomStyle[];
        total: number;
        default: any;
        // 其他属性...
    }
    // 参考风格列表
    const [commonStyleList, setCommonStyleList] = useState<CustomStyle[]>([]);
    // 无风格数据详情
    const [noStyleData, setNoStyleData] = useState<null | {
        stylePrompt: string
    }>(null)
    // 参考风格总页数
    const [commonStyleTotal, setCommonStyleTotal] = useState(0)
    // 参考风格当前页
    const [commonStylePage, setCommonStylePage] = useState(1)
    // 当前选中的风格
    const [currentStyle, setCurrentStyle] = useState<null | {
        styleId: string
        id: string
        originImgUrl: string
        thumbnailImgUrl: string
        style: string
        stylePrompt: string
    }>(null)
    //获取参考风格列表
    const fetchCommonStyleData = async (type: number, pagenum: number, pageSize: number) => {
        try {
            const response = await getCommonStyleList(type, pagenum, pageSize) as CustomStyleListResponse;
            setCommonStyleList(response.data);
            setNoStyleData(response.default || { stylePrompt: '' });

            if (response.data.length) {
                setCurrentStyle(response.data[0])
            } else {
                setCurrentStyle({
                    styleId: '',
                    id: '',
                    thumbnailImgUrl: '',
                    originImgUrl: '',
                    style: '',
                    stylePrompt: ''
                })
            }

            setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
            setCommonStylePage(pagenum);
        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };

    const commonStylePageChange = (pagenum: any) => {
        fetchCommonStyleData(1, pagenum, 12)
    }

    // 识图转文上传过的图片列表
    const [uploadImgList, setUploadImgList] = useState<CustomStyle[]>([]);
    // 识图转文上传过的图片总页数
    const [uploadImgTotal, setUploadImgTotal] = useState(0)
    // 识图转文上传过的图片当前页
    const [uploadImgPage, setUploadImgPage] = useState(1)

    // 当前选中的识图转文图片
    const [currentImg, setCurrentImg] = useState<null | {
        styleId: string
        id: string
        originImgUrl: string
        thumbnailImgUrl: string
        stylePrompt: string
    }>(null)

    //获取识图转文上传过的图片列表
    const fetchUploadImgData = async (type: number, pagenum: number, pageSize: number) => {
        try {
            const response = await getCustomStyleList(type, pagenum, pageSize) as CustomStyleListResponse;
            if (response.data) {
                setUploadImgList(response.data);
                setUploadImgTotal(Math.ceil(response.total / pageSize));// 总页数
                setUploadImgPage(pagenum);
            } else if (pagenum != 1) {
                fetchUploadImgData(2, 1, 11);
            }

        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };

    useEffect(() => {
        fetchCommonStyleData(1, 1, 12)
        fetchUploadImgData(2, 1, 11);
    }, [])  // 空依赖数组，表示仅在组件挂载时执行一次

    //上传识图转文的图片
    const handleUploadImg = ({ file }: { file: any }) => {

        addCustomStyleList(file, '2')
            .then((res) => {
                console.log("上传成功", res);
                fetchUploadImgData(2, uploadImgPage, 11);
            })
            .catch((err) => {
                messageApi.warning(err.data?.msg)
                console.error("上传失败", err);
            });

    }

    const handleDelete = (file: any, event: { stopPropagation: () => void }) => {
        event.stopPropagation();
        delCustomStyleList(file.id, file.imgUrl, file.thumbnailImgUrl)
            .then((res) => {
                console.log("删除成功", res);
                fetchUploadImgData(2, uploadImgPage, 11);
                if (file.id == currentStyle?.id) {
                    setCurrentStyle(commonStyleList[0])
                }
            })
            .catch((err) => {
                console.error("删除失败", err);
            });
    }
    const pageChange = (pagenum: any) => {
        fetchUploadImgData(2, pagenum, 11);
    }

    const selectUploadImg = (item: any) => {
        setCurrentImg(item)
    }

    const [isModalOpen, setIsModalOpen] = useState(false)


    // 点击参考风格反显
    const selectStyle = (item: any) => {

        setCurrentStyle(item)
        setIsModalOpen(false)
    }
    let commonStyleListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
        return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
            <img onClick={() => selectStyle(item)}
                src={item.originImgUrl || item.originImgUrl || ''}
                className={`${currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px] `}
                style={{ objectFit: 'cover' }}
                alt="" />
            <p style={{ textAlign: 'center' }} >{item.style}</p>
        </div>
    }) : null


    const [valueInput, setValueInput] = useState('')
    const [valueInputAssociate, setValueInputAssociate] = useState('')
    const [isAssociateModalOpen, setIsAssociateModalOpen] = useState(false)
    const [isConvertModalOpen, setIsConvertModalOpen] = useState(false)
    // 智能联想 
    const handleAssociation = () => {
        if (!valueInput.trim()) {
            messageApi.warning('想要生成的内容不能为空')
            return false
        }
        setValueInputAssociate('')
        setCurrentImg({
            styleId: '',
            id: '',
            originImgUrl: '',
            thumbnailImgUrl: '',
            stylePrompt: '',
        })
        setIsAssociateModalOpen(true)
        getAssociation(valueInput.trim(), '6').then((res: any) => {
            setValueInputAssociate(res || '')
        })
    }
    //使用生成出来的文字
    const handleUse = () => {
        setIsAssociateModalOpen(false)
        setValueInput(valueInputAssociate)
    }
    //识图转文
    const handleToText = () => {
        setIsConvertModalOpen(true)
        setCurrentImg({
            styleId: '',
            id: '',
            originImgUrl: '',
            thumbnailImgUrl: '',
            stylePrompt: '',
        })
    }
    // 识图转文 => 立即识别
    const handleConvert = () => {

        if (!currentImg?.originImgUrl) {
            messageApi.warning('请先选择识图转文的图片')
            return false
        }
        setValueInputAssociate('')
        setIsConvertModalOpen(false)
        setIsAssociateModalOpen(true)
        imageIntoText(currentImg.originImgUrl, '6').then((res: any) => {
            setValueInputAssociate(res || '')
        })
    }
    // 再次生成
    const handleAgainToText = () => {
        const url = currentImg?.originImgUrl || ''
        setValueInputAssociate('')
        if (url) {
            // 识图转文
            imageIntoText(url, '6').then((res: any) => {
                setValueInputAssociate(res || '')
            })
        } else {
            // 智能联想
            getAssociation(valueInput.trim(), '6').then((res: any) => {
                setValueInputAssociate(res || '')
            })

        }

    }
    // 识图转文上传图片按钮
    const convertUploadButton = (
        <button style={{ background: 'none', }} className='w-[106px] h-[106px]   hover:border-primary rounded-lg' type="button">
            <CloudUploadOutlined style={{ fontSize: '30px' }} />
            <div style={{ fontSize: '12px', marginTop: 4, width: '106px' }}>Upload</div>
        </button>
    );
    // 重置左侧编写信息
    const resetEditParams = () => {
        setValueInput('')
        setCurrentStyle(commonStyleList[0])
        setFileList([]);
        setReferenceImgUrl('')
        setGenerateQuantity('1')
        setImageScale('1:1')
        setImgToImgScale('原图比例')
        setStyleActiveKey([])

    }
    // 确认执行
    const handleExecute = () => {
        if (tab === '文生图模式' && !valueInput) {
            messageApi.warning('想要生成的内容不能为空');
            return false;
        }
        if (tab === '图生图模式' && !referenceImgUrl) {
            messageApi.warning('请上传参考图片');
            return false;
        }
        setActiveKey('1');
        let continuousType = tab === '文生图模式' ? 6 : 7;
        const executeTaskWrapper = (taskId: string) => {
            const params = tab === '文生图模式' ? {
                styleId: !styleActiveKey.length ? '' : currentStyle?.styleId,
                style: !styleActiveKey.length ? '无风格' : currentStyle?.style,
                stylePrompt: !styleActiveKey.length ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                imageNumber: Number(generateQuantity),
                imageScale: tab === '文生图模式' ? imageScale : imgToImgScale,
                usedIntegral: generateQuantity,
            } : {
                imageNumber: Number(generateQuantity),
                imageScale: tab === '文生图模式' ? imageScale : imgToImgScale,
                usedIntegral: generateQuantity,
            };
            const originUrl = tab === '文生图模式' ? currentStyle?.originImgUrl : referenceImgUrl;
            const prompt = tab === '文生图模式' ? valueInput : '';

            executeTask({
                taskId,
                type: continuousType,
                prompt,
                originImgUrl: tab === '文生图模式' && !styleActiveKey.length ? '' : originUrl,
                taskParam: JSON.stringify(params)
            }).then(() => {
                setAgainEditTaskId('');
                userinfoService.refresh(); // 刷新用户积分
                messageApi.success('开始执行');
                setActiveKey('1');
                // resetEditParams();
                getTaskInfo([6, 7])
            }).catch(err => {
                messageApi.error(`执行失败:${err?.data?.msg}`);
            });
        };

        if (againEditTaskId) {
            executeTaskWrapper(againEditTaskId);
        } else {
            const originalUrl = tab === '文生图模式' ? currentStyle?.originImgUrl || '' : referenceImgUrl;
            createTask({ originalUrl: tab === '文生图模式' && !styleActiveKey.length ? '' : originalUrl, type: continuousType }).then((res) => {
                if (res.taskId) {
                    executeTaskWrapper(res.taskId);
                }
            });
        }
    };

    const handleTaskStatusUpdate = (res: any, data: any) => {
        let arr = [...res];
        let progress = data.ordinalImgResultList[0].progress;
        let status = data.status;
        // 如果del_flag为1，直接返回不处理
        if (data?.del_flag === 1) {
            return;
        }
        for (let i = 0; i < arr.length; i++) {
            if (data.taskId === arr[i].taskId) {
                arr[i].status = status;
                arr[i].taskOrdinalList[0].status = status;
                console.log(status, progress, "状态 | 进度");
                if (status === 1 && progress === 1) {
                    arr[i].taskOrdinalList[0].ordinalImgResultList = data.ordinalImgResultList;
                }
            }
        }
        setResultList(arr);
    };

    const getTaskInfo = (types: any) => {
        getNewestTaskResult(types).then((res: any) => {
            if (res.length) {
                setResultList(res);
                socketService.on(
                    ESocketPushType.taskStatus,
                    (data) => handleTaskStatusUpdate(res, data)
                );
            } else {
                setResultList([]);
            }
        }).catch(err => {
            messageApi.error(`获取详情失败：${err?.msg}`);
        });
    };

    // 获取最新单条任务
    // const getTaskInfo = (types: any) => {
    //     getNewestTaskResult(types).then((res: any) => {
    //         if (res.length) {
    //             let result = res[0].taskOrdinalList
    //             setResultList(result);
    //             if (result[0].status != 1) {
    //                 socketService.on(
    //                     ESocketPushType.taskStatus,
    //                     (data) => {
    //                         let arr = result
    //                         if (data.taskId !== arr[0].taskId) return false;
    //                         let progress = data.ordinalImgResultList[0].progress;
    //                         let status = data.status;
    //                         if (arr.length > 0) {
    //                             arr[0].status = status;
    //                         }
    //                         console.log(status, progress, "状态 | 进度")
    //                         if (status == 1 && progress == 1) {
    //                             arr[0].ordinalImgResultList = data.ordinalImgResultList;

    //                         }
    //                         setResultList([...arr]);
    //                     }
    //                 )
    //             }
    //         } else {
    //             setResultList([]);
    //         }
    //     }).catch(err => {
    //         messageApi.error(`获取详情失败：${err?.msg}`)
    //     });
    // };


    const [referenceImgUrl, setReferenceImgUrl] = useState('')

    // 上传参考图
    const handleUploadChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
        const validFiles: UploadFile[] = [];

        setIsUploading(true); // 开始上传时启用加载状态

        const promises = newFileList.map((file) => {
            const isImage = /^image\//.test(file.type || '');
            const isLt10M = (file.size || 0) / 1024 / 1024 < 10;

            if (!isImage) {
                messageApi.error('请上传图片格式的文件');
                setIsUploading(false)
                return Promise.resolve(false);
            }
            if (!isLt10M) {
                messageApi.error('参考图片最大不能超过10m');
                setIsUploading(false)
                return Promise.resolve(false);
            }

            return new Promise<boolean>((resolve) => {
                const reader = new FileReader();
                const originFileObj = file.originFileObj as File;
                if (!originFileObj) {
                    messageApi.error('无法获取文件对象,请检查文件');
                    setIsUploading(false)
                    resolve(false);
                    return false;
                }
                reader.readAsArrayBuffer(originFileObj);
                reader.onload = async () => {
                    try {
                        const blob = new Blob([reader.result as ArrayBuffer]);
                        const img = await createImageBitmap(blob);
                        const { width, height } = img;
                        const isValidSize = width <= 4096 && height <= 4096;
                        if (!isValidSize) {
                            messageApi.error('参考图片尺寸最长边不超过4096');
                            setIsUploading(false)
                            return Promise.resolve(false);
                        } else {
                            resolve(true);
                        }
                    } catch (error) {
                        messageApi.error('图片加载失败，请检查图片格式');
                        setIsUploading(false)
                        resolve(false);
                    }
                };
                reader.onerror = () => {
                    messageApi.error('读取文件失败，请检查文件格式');
                    setIsUploading(false)
                    resolve(false);
                };
            });
        });

        Promise.all(promises).then((results) => {
            newFileList.forEach((file, index) => {
                if (results[index]) {
                    validFiles.push(file);
                }
            });
            if (validFiles.length > 0) {
                const file = validFiles[0].originFileObj as File;
                uploadFileOssApi(file).then(res => {
                    if (res.url) {
                        setFileList(validFiles);
                        setReferenceImgUrl(res.url)
                        messageApi.success('参考图片上传成功');
                    }
                }).catch(err => {
                    messageApi.error(`上传失败: ${err?.data?.msg}`);
                }).finally(() => setIsUploading(false)); // 无论成功失败都关闭加载状态;
            } else {
                setIsUploading(false)
            }
        });
    };
    const handleRemoveImg = async (file: any) => {
        const prefix = 'http://image-task.xiaoaishop.com/';
        const filePath = referenceImgUrl.replace(prefix, '');

        try {
            // 调用后端删除接口
            await deleteReferenceFile([filePath]);
            messageApi.success(`删除参考图片成功`);
            // 更新本地状态，移除图片列表中的图片
            setFileList((prevFileList) => prevFileList.filter(f => f.name !== file.name));
            setReferenceImgUrl('');
        } catch (error) {
            messageApi.error(`删除图片: ${file.name} 失败，请重试`);
        }
    };
    const [styleActiveKey, setStyleActiveKey] = useState<string[]>([]);
    const styleItem: CollapseProps['items'] = [
        {
            key: '1',
            label: (
                <div>
                    风格设置
                    <span className={'text-subtext text-[12px]'}>（可选）</span>
                </div>
            ),
            children: (
                <>
                    <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setIsModalOpen(true)}>
                        <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentStyle?.originImgUrl || currentStyle?.thumbnailImgUrl || ""} />
                        <div className='w-[230px]'>
                            <p className='text-[18px] mb-[12px]'>选中风格</p>
                            <p>{currentStyle ? currentStyle.style : ''}</p>
                        </div>
                        <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                    </div>
                </>
            )
        }
    ]

    const [isUploading, setIsUploading] = useState(false); // 文件上传中

    return (
        <div className='h-full flex'>
            {contextHolder}  {/* 这里确保 Modal 挂载 */}
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}

            <div className='w-[420px] border-r-[1px] border-normal h-[calc(100vh-90px)]  overflow-y-scroll scrollbar-container scrollbar-hide  p-[14px]'>
                <Segmented options={['文生图模式', '图生图模式']} block size="large" value={tab} onChange={(value) => setTab(value)} style={{ marginTop: '20px' }} />
                {tab === '文生图模式' && <div>
                    <div className='border border-normal mt-[20px] rounded-lg  hover:border-primary'>
                        <Input.TextArea placeholder="输入想要生成的内容，支持中英文，使用英文更准确。" onChange={(e) => setValueInput(e.target.value)} value={valueInput} rows={4} style={{
                            border: 'none', boxShadow: 'none', resize: 'none', margin: '10px 0',
                        }} />
                        <div className='w-[320px] ml-auto mr-auto flex justify-between items-center mb-[16px]' >
                            <Button type="primary" style={{ padding: '16px 26px' }} onClick={handleAssociation} >智能联想  <img className='w-[20px]' src={require('@/asset/icon/brain.png')} /></Button>
                            {/* 智能联想弹框  start */}
                            <Modal width={500} title="" open={isAssociateModalOpen} footer={null} onCancel={() => setIsAssociateModalOpen(false)}>
                                <div className='border border-normal mt-[20px] p-[6px] pb-[20px] rounded-lg relative  hover:border-primary'>
                                    <Input.TextArea placeholder="生成中..." onChange={(e) => setValueInputAssociate(e.target.value)} rows={5} value={valueInputAssociate} style={{
                                        border: 'none', boxShadow: 'none', resize: 'none', marginBottom: '10px'
                                    }} />
                                    <Button variant="link" color="primary" style={{ position: 'absolute', right: '0', bottom: '0' }} onClick={handleUse} > 使用 </Button>
                                </div>
                                <div style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: '16px' }}>
                                    <Button type="default" color="primary" onClick={handleAgainToText} > 再次生成</Button>
                                </div>
                            </Modal>
                            {/* 智能联想弹框 end */}
                            <Button type="default" style={{ padding: '16px 27px' }} onClick={handleToText} >识图转文  <img className='w-[18px]' src={require('@/asset/icon/saomiao.png')} /></Button>
                            <Modal width={800} title="上传过的图片" open={isConvertModalOpen} footer={null} onCancel={() => setIsConvertModalOpen(false)} >
                                <div className=' h-[360px] relative'  >
                                    <div className='w-[740px]  ml-auto mr-auto '>
                                        <p className='flex items-center'><img className='w-[18px] ml-[10px] mr-[10px]' src={require('@/asset/icon/tip.png')} /> 最多保存最新50张图片</p>
                                        <div className="flex flex-wrap mt-[20px]  ">
                                            {/* 手动放置 Upload 按钮，确保始终在最前面 */}
                                            <div className='mr-[10px]  ml-[10px] mt-[2px]'>
                                                <Upload
                                                    listType="picture-card"
                                                    showUploadList={false} // 关闭默认列表
                                                    onChange={handleUploadImg}
                                                    beforeUpload={() => false} // 阻止默认上传行为
                                                >
                                                    {convertUploadButton}
                                                </Upload>
                                            </div>
                                            {/* 手动渲染已上传的图片，确保在 Upload 右侧 */}
                                            {uploadImgList.map((file) => (
                                                <div key={file.styleId} onClick={() => selectUploadImg(file)} className="w-[106px] h-[106px] mr-[7px]  ml-[7px] mb-[24px] rounded-lg overflow-hidden cursor-pointer relative border-white ">
                                                    <img
                                                        src={file.thumbnailImgUrl || file.originImgUrl || ''} style={{ objectFit: 'cover' }}
                                                        className={`${currentImg?.id == file.id ? '!border-primary' : ''}  w-full h-full border-white rounded-lg  hover:border-primary border-[2px] `}
                                                        alt=""
                                                    />
                                                    <img className="w-[20px] absolute top-[6px] right-[6px] cursor-pointer hover:content-[url('@/asset/icon/del-active.png')]" onClick={(event) => handleDelete(file, event)} src={require('@/asset/icon/del.png')} alt="" />
                                                </div>
                                            ))}
                                        </div>
                                        < label >
                                            <ul className='flex justify-center mt-[20px] w-[40%]  absolute bottom-[20px] left-[30%] '>
                                                {Array.from({ length: uploadImgTotal }, (_, index) => (
                                                    <li className={`${uploadImgPage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
                                                        style={{ lineHeight: '18px' }} onClick={() => { pageChange(index + 1) }}>{index + 1}</li>
                                                ))}
                                                <li>{'共 ' + uploadImgTotal + ' 页'}</li>
                                            </ul>
                                        </ label>
                                    </div >
                                    <Button type="primary" style={{ position: 'absolute', bottom: '18px', right: '0' }} onClick={handleConvert} >立即识别</Button>
                                </div >
                            </Modal>
                        </div>
                    </div>
                    <Collapse
                        defaultActiveKey={styleActiveKey}
                        activeKey={styleActiveKey}
                        ghost
                        items={styleItem}
                        collapsible="header"
                        expandIconPosition="end"
                        className={classNames(styles['task-model-collapse'])}
                        onChange={(keys: any) => { setStyleActiveKey(keys) }}
                    />
                    {/* <p className='text-normal mt-[20px] '>风格设置</p>
                    <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setIsModalOpen(true)}>
                        <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentStyle ? currentStyle.thumbnailImgUrl : ""} />
                        <div className='w-[230px]'>
                            <p className='text-[18px] mb-[12px]'>参考风格</p>
                            <p>{currentStyle ? currentStyle.style : ''}</p>
                        </div>
                        <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                    </div> */}

                    <Modal width={800} title="" footer={null} open={isModalOpen} onCancel={() => setIsModalOpen(false)}>
                        <div >
                            <div className='flex flex-wrap mt-[20px] h-[340px]'>
                                {commonStyleListEL}
                            </div>
                            <label>
                                <ul className='flex justify-center  mt-[20px]'>
                                    {Array.from({ length: commonStyleTotal }, (_, index) => (
                                        <li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
                                            style={{ lineHeight: '18px' }} onClick={() => { commonStylePageChange(index + 1) }}>{index + 1}</li>
                                    ))}
                                    <li >{'共' + commonStyleTotal + '页'}</li>
                                </ul>
                            </label>
                        </div>
                    </Modal>
                    <p className='text-normal mt-[20px] mb-[10px]'>图片生成数量</p>
                    <Radio.Group
                        size="large"
                        block
                        options={generateQuantityOptions}
                        onChange={handleQuantityChange}
                        value={generateQuantity}
                        defaultValue="1"
                        optionType="button"
                        buttonStyle="solid"
                    />
                    <p className='text-normal mt-[20px] mb-[10px]'>生成图片比例</p>

                    <Select
                        defaultValue="1:1"
                        style={{ width: 260 }}
                        onChange={handleChange}
                        value={imageScale}
                        options={[
                            { value: '1:1', label: '1 : 1' },
                            { value: '2:3', label: '2 : 3' },
                            { value: '3:2', label: '3 : 2' },
                            { value: '3:4', label: '3 : 4' },
                            { value: '4:3', label: '4 : 3' },
                            { value: '9:16', label: '9 : 16' },
                            { value: '16:9', label: '16 : 9' },
                        ]}
                    />
                    <div className='text-center'>
                        <Button type="primary" className='w-[280px] mt-[100px] ' onClick={handleExecute} style={{ padding: '22px 0', fontSize: '17px' }}>确认执行（消耗 {generateQuantity} 积分）</Button>
                    </div>
                </div>}
                {
                    tab === '图生图模式' && <div className='main-edit pt-[20px]'>
                        <Upload.Dragger
                            listType="picture-card"
                            fileList={fileList}
                            onPreview={handlePreview}
                            onRemove={handleRemoveImg}
                            onChange={handleUploadChange}
                            beforeUpload={() => { return false }}
                            disabled={isUploading}
                            accept="image/*"
                            className="upload-area bg-[#f5f5f5]"
                        >
                            <div className='flex justify-between items-center'>
                                <div className='text-left'>
                                    <strong className='text-[18px]'>上传参考图</strong>
                                    <p className=' mt-[16px] w-[190px]'>10M以内, 最大4096*4096</p>
                                </div>

                                {fileList.length >= 1 ? null : (
                                    <div className="upload-button-wrapper">
                                        {isUploading ? (
                                            <div className="flex flex-col items-center">
                                                <LoadingOutlined />
                                                <p className="mt-2">
                                                    文件上传中
                                                </p>
                                            </div>
                                        ) : <div className='items-center gap-2 mr-[40px] '>
                                            <p><CloudUploadOutlined className='ant-upload-drag-icon text-[30px] mb-2' /></p>
                                            <p>upload</p>
                                        </div>}
                                    </div>
                                )}
                            </div>
                            {previewImage && (
                                <div onClick={(e) => e.stopPropagation()}>
                                    <Image
                                        wrapperStyle={{ display: 'none' }}
                                        preview={{
                                            visible: previewOpen,
                                            onVisibleChange: (visible) => setPreviewOpen(visible),
                                            afterOpenChange: (visible) => !visible && setPreviewImage(''),
                                        }}
                                        src={previewImage}

                                    />
                                </div>
                            )}
                        </Upload.Dragger>
                        {/* <div className='bg-[#eee] h-[128px] mt-[20px] border border-normal hover:border-primary rounded-lg flex justify-between items-center' style={{ padding: '12px 24px' }}>
                            <div>
                                <strong className='text-[18px]'>上传参考图</strong>
                                <p className=' mt-[16px]'>10M以内, 最大4096*4096</p>
                            </div>
                            <Upload
                                listType="picture-card"
                                fileList={fileList}
                                onPreview={handlePreview}
                                onRemove={handleRemoveImg}
                                onChange={handleUploadChange}
                                beforeUpload={() => { return false }}
                                disabled={isUploading}
                            >
                                {fileList.length >= 1 ? null : (
                                    <div className="upload-button-wrapper">
                                        {isUploading ? (
                                            <div className="flex flex-col items-center">
                                                <LoadingOutlined />
                                                <p className="mt-2">
                                                    文件上传中
                                                </p>
                                            </div>
                                        ) : uploadButton}
                                    </div>
                                )}
                            </Upload>
                            {previewImage && (
                                <Image
                                    wrapperStyle={{ display: 'none' }}
                                    preview={{
                                        visible: previewOpen,
                                        onVisibleChange: (visible) => setPreviewOpen(visible),
                                        afterOpenChange: (visible) => !visible && setPreviewImage(''),
                                    }}
                                    src={previewImage}
                                />
                            )}
                        </div> */}
                        <p className='text-normal mt-[20px] mb-[10px]'>图片生成数量</p>
                        <Radio.Group
                            size="large"
                            block
                            options={generateQuantityOptions}
                            onChange={handleQuantityChange}
                            value={generateQuantity}
                            defaultValue="1"
                            optionType="button"
                            buttonStyle="solid"
                        />
                        <p className='text-normal mt-[20px] mb-[10px]'>生成图片比例</p>

                        <Select
                            defaultValue="原图比例"
                            style={{ width: 260 }}
                            onChange={handleScaleChange}
                            value={imgToImgScale}
                            options={[
                                { value: '原图比例', label: '原图比例' },
                                { value: '1:1', label: '1 : 1' },
                                { value: '2:3', label: '2 : 3' },
                                { value: '3:2', label: '3 : 2' },
                                { value: '3:4', label: '3 : 4' },
                                { value: '4:3', label: '4 : 3' },
                                { value: '9:16', label: '9 : 16' },
                                { value: '16:9', label: '16 : 9' },
                            ]}
                        />
                        <div className='text-center'>
                            <Button type="primary" className='w-[280px] mt-[100px]' onClick={handleExecute} style={{ padding: '22px 0', fontSize: '17px' }}>确认执行（消耗 {generateQuantity} 积分）</Button>
                        </div>
                    </div>
                }
            </div >
            <div className='flex-1 flex flex-col  h-full p-[20px]' >
                <Tabs defaultActiveKey='1' activeKey={activeKey} onChange={handleTabsChange} items={tab === '文生图模式' ? tabItems.slice(0, 3) : tabItems} className="h-full" style={{ height: '100%', overflow: 'hidden' }} />
            </div>

        </div >
    );
};

export default Continuous;




