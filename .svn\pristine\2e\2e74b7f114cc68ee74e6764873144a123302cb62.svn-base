package com.dataxai.domain;

import com.dataxai.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 风险检测任务表 risk_detection_task
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RiskDetectionTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 任务批次（唯一，自动生成） */
    private String taskBatch;

    /** 总数量（该任务的图片总张数） */
    private Integer totalAmount;

    /** 完成数（该任务图片处理成功的张数） */
    private Integer successAmount;

    /** 失败数（该任务图片处理失败的张数） */
    private Integer failAmount;

    /** 任务状态（1：待执行，2：执行中，3：执行完成。默认为1） */
    private Integer status;

    /** 所属人ID (关联sys_user.user_id) */
    private Long ownerId;

    /** 团队ID (用于团队模式数据过滤) */
    private Long teamId;

    /** 关联工作流节点执行记录ID */
    private Long workflowNodeExecutionId;

    /** 查询开始时间 */
    private String startTime;
    /** 查询结束时间 */
    private String endTime;

    /** 任务备注 */
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private String remark;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setTaskBatch(String taskBatch)
    {
        this.taskBatch = taskBatch;
    }

    public String getTaskBatch()
    {
        return taskBatch;
    }

    public void setTotalAmount(Integer totalAmount)
    {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalAmount()
    {
        return totalAmount;
    }

    public void setSuccessAmount(Integer successAmount)
    {
        this.successAmount = successAmount;
    }

    public Integer getSuccessAmount()
    {
        return successAmount;
    }

    public void setFailAmount(Integer failAmount)
    {
        this.failAmount = failAmount;
    }

    public Integer getFailAmount()
    {
        return failAmount;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setOwnerId(Long ownerId)
    {
        this.ownerId = ownerId;
    }

    public Long getOwnerId()
    {
        return ownerId;
    }

    public void setTeamId(Long teamId)
    {
        this.teamId = teamId;
    }

    public Long getTeamId()
    {
        return teamId;
    }

    public void setWorkflowNodeExecutionId(Long workflowNodeExecutionId)
    {
        this.workflowNodeExecutionId = workflowNodeExecutionId;
    }

    public Long getWorkflowNodeExecutionId()
    {
        return workflowNodeExecutionId;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    public String getStartTime() {
        return startTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public String getEndTime() {
        return endTime;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskBatch", getTaskBatch())
            .append("totalAmount", getTotalAmount())
            .append("successAmount", getSuccessAmount())
            .append("failAmount", getFailAmount())
            .append("status", getStatus())
            .append("ownerId", getOwnerId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 