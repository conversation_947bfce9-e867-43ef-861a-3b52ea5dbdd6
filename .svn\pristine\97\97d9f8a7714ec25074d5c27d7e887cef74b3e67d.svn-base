---
description:
globs:
alwaysApply: false
---
### 数据库与 MyBatis

- **数据源配置**：
  - 主配置：[ruoyi-admin/src/main/resources/application.yml](mdc:ruoyi-admin/src/main/resources/application.yml)
  - 开发/本地覆盖：[ruoyi-admin/src/main/resources/application-dev.yml](mdc:ruoyi-admin/src/main/resources/application-dev.yml)、[ruoyi-admin/src/main/resources/application-local.yml](mdc:ruoyi-admin/src/main/resources/application-local.yml)

- **全局 MyBatis 配置**：
  - [ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml](mdc:ruoyi-admin/src/main/resources/mybatis/mybatis-config.xml)

- **Mapper XML 与包结构**：
  - XML 目录：`ruoyi-admin/src/main/resources/mapper/**`
  - 对应 Java Mapper 接口：`ruoyi-admin/src/main/java/com/dataxai/**/mapper/**`
  - 常见业务 Mapper：
    - 工作流：[ruoyi-admin/src/main/resources/mapper/workflow/](mdc:ruoyi-admin/src/main/resources/mapper/workflow)
    - 基础信息：[ruoyi-admin/src/main/resources/mapper/baseinfo/](mdc:ruoyi-admin/src/main/resources/mapper/baseinfo)

- **SQL 脚本**：
  - 位置：[sql/](mdc:sql)
  - 示例：
    - [sql/dev-ai-photo.sql](mdc:sql/dev-ai-photo.sql)
    - [sql/add_platform_api_fields.sql](mdc:sql/add_platform_api_fields.sql)

- **调试建议**：
  - 启用 MyBatis 日志或 SQL 打印，确认参数与结果映射是否一致
  - XML 中的 `namespace` 与接口全限定名一致；`id` 与方法名一致
  - 若实体新增字段，需同步更新表结构、实体类、Mapper 接口与 XML 映射
