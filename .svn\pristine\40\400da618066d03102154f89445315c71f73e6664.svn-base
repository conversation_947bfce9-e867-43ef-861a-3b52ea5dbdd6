
import { useEffect, useState, useMemo } from 'react';
import { Button, message, Checkbox, Spin } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import {
    getBatchDetail
} from '@/api/task'
import { batchZipUrl } from '@/api/common'
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const ClearDetail = () => {
    const location = useLocation();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [messageApi, messageContextHolder] = message.useMessage()

    const [selectedImages, setSelectedImages] = useState<any[]>([]);

    const { batchId } = location.state || {};

    const [record, setRecord] = useState<any>();
    const [pageLoading, setPageLoading] = useState(false);
    const fetchDetail = () => {
        setPageLoading(true)
        getBatchDetail(batchId).then((res: any) => {
            setRecord(res)
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }
    useEffect(() => {
        fetchDetail()
    }, []);

    const handleSelect = (task: any) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(task)
                ? prev.filter(item => item !== task)
                : [...prev, task];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });
    };

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedImages([]); // 清空已选图片
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };

    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        const imgUrls = selectedImages.map(task => task?.taskOrdinalList[0].ordinalImgResultList[0].resImgUrl);
        setDownloadLoading(true);
        batchZipUrl({ imageUrls: imgUrls, type: 12 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false); // 下载完成后重置加载状态
        })
    };

    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any, index: number, list, status: string) => {
            let originalImgUrlList = list?.map((task: any) =>
                task?.taskOrdinalList[0]?.ordinalImgResultList[0]?.originalImgUrl
            ) || []
            let previewImageList = list?.map((task: any) =>
                task?.taskOrdinalList[0]?.ordinalImgResultList[0]?.resImgUrl
            ) || []
            const isCheckedArray = record?.tasks.map((task: any) =>
                selectedImages.some(selectedTask =>
                    selectedTask.taskId === task.taskId &&
                    selectedTask.taskOrdinalId === task.taskOrdinalId
                )
            );
            // 处理勾选状态变化
            const handleCheckChange = (checked: boolean, changedIndex: number) => {
                const task = list?.[changedIndex];
                if (task) {
                    if (checked) {
                        setSelectedImages(prev => [...prev, task]);
                    } else {
                        setSelectedImages(prev => prev.filter(t =>
                            t.taskId !== task.taskId ||
                            t.taskOrdinalId !== task.taskOrdinalId
                        ));
                    }
                    setShowBatchActions(checked ? true : selectedImages.length > 1);
                }
            };
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.resImgUrl}
                        oriImgUrl={image.originalImgUrl}
                        oriImgUrlList={originalImgUrlList}
                        smallImgUrl={image.resSmallImgUrl}
                        markImgUrl={image.resWhiteImgUrl} // 白底图
                        progress={image.progress}
                        previewImages={previewImageList}
                        index={index}
                        seed={image.seed}
                        comparison={true}
                        canChecked={true}
                        isCheckedArray={isCheckedArray}
                        onCheckChange={handleCheckChange}
                        status={status}
                    />
                </div>
            )
        }
    )

    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : (<>
                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}  >
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center  h-[60px] border-b-[1px] border-normal'>
                    <p className='mr-[20px]'>批次: {record?.batchNumber}</p>
                    <p className='mr-[20px]'>创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    <p>总数：{record?.totalAmount}
                        <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.successAmount}</span>
                        {record?.failAmount > 0 && <span style={{ color: '#cf1322', marginLeft: '6px' }}>失败：{record?.failAmount}</span>}
                    </p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="grid  p-[10px] grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 xxl:grid-cols-8 gap-0">
                        {record?.tasks.length > 0 && record?.tasks.map((task: any, index: number) => (
                            <div className='aspect-square p-2'>
                                <div key={index} className="w-full h-full flex   rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden">
                                    <Checkbox
                                        className="absolute top-4 left-4 z-10"
                                        style={{ transform: 'scale(1.25)' }}  // 放大1.25倍
                                        checked={selectedImages.includes(task)}
                                        onChange={() => handleSelect(task)}
                                        disabled={checkImageDisabled(task, 'clear')}
                                    />
                                    {task?.taskOrdinalList[0].ordinalImgResultList[0].hasUploaded && <p className="absolute bottom-4  z-10"
                                        style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                    {getTaskImageComponent(task?.taskOrdinalList[0].ordinalImgResultList[0], index, record?.tasks, '')}
                                </div>
                            </div>
                        ))}
                    </div>
                    <BottomActionBar
                        visible={selectedImages.length > 0 || showBatchActions}
                        selectedCount={selectedImages.length}
                        isAllSelected={record?.tasks.length > 0 && selectedImages.length == record?.tasks.filter((task: any) => !checkImageDisabled(task, 'clear')).length}
                        onToggleSelectAll={() => {
                            const enabledTasks = record?.tasks.filter((task: any) => !checkImageDisabled(task, 'clear')) || [];
                            if (record?.tasks.length > 0 && selectedImages.length == enabledTasks.length) {
                                setSelectedImages([])
                            } else {
                                setSelectedImages([...enabledTasks])
                            }
                        }}
                        onCancelSelection={cancelSelection}
                        syncEnabled
                        selectedItems={selectedImages}
                        extractImageUrl={(task) => task?.taskOrdinalList?.[0]?.ordinalImgResultList?.[0]?.resImgUrl}
                        syncExtraParams={{}}
                        onDownload={handleDownloadImgages}
                        downloadLoading={downloadLoading}
                        downloadDisabled={selectedImages.length === 0}
                        enableWorkflow={userInfo?.currentMode == 2}
                        onActionFinished={() => {
                            // 刷新积分或数据
                            userinfoService.refresh();
                            fetchDetail();
                            setSelectedImages([])
                        }}
                        actionDisabled={selectedImages.length === 0}
                    />
                </div>
            </>)}
        </div >
    );
};

export default ClearDetail;





