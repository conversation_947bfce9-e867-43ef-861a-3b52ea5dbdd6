{"name": "ai-clothing-monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nx run ai-console:start:local", "start:local": "nx run ai-console:start:local", "start:dev": "nx run ai-console:start:dev", "dev": "nx run ai-console:start:fswy", "build:dev": "nx run ai-console:build:dev", "build:prod": "nx run ai-console:build:prod", "format": "prettier --write .", "lint": "eslint '{packages,projects}/*/{src,types}/**/*.{ts,tsx,js}'", "prepare": "husky install", "preinstall": "npx only-allow pnpm", "clean": "rimraf ./packages/*/{*.tsbuildinfo,.cache,build,dist,.hash,node_modules} node_modules ./projects/*/node_modules", "clean-install": "pnpm clean && pnpm i"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@rollup/plugin-commonjs": "^25.0.4", "@types/node": "^16.18.60", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "commitlint": "^17.7.1", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "nx": "18.1.2", "prettier": "^3.0.3", "rimraf": "^5.0.5", "rollup": "^3.29.3", "rollup-plugin-generate-package-json": "^3.2.0", "rollup-plugin-typescript2": "^0.35.0", "typescript": "^5.3.3"}, "pnpm": {"overrides": {"typescript": "^5.3.3"}}, "engines": {"npm": "only-allow-pnpm", "yarn": "only-allow-pnpm"}, "keywords": [], "author": "wuyang <<EMAIL>>", "license": "ISC", "repository": "http://git.vrsea.cn/data-x-fashion/data-x-fashion-front.git", "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab", "dependencies": {"react-image-crop": "latest"}}