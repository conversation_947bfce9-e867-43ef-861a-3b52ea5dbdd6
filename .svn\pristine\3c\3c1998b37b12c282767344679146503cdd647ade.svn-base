
import React from 'react'
import { createBrowserRouter, Navigate, RouteObject } from 'react-router-dom'
import { getLazy } from '@/helper/utils/get-lazy-component/getLazy'
import { BaseLayout } from '@/layout/base/Base'
import { DataXLayout } from '@/layout/data-x/DataXLayout'
import { BizPhotoLayout } from '@/layout/biz-photo/BizPhotoLayout'
import { DataXDocsLayout } from '@/layout/data-x/DataXDocsLayout'

// 登录页面
const Login = getLazy(() => import('../pages/login/Login'))
// 补充信息登录
const ContinueLogin = getLazy(() => import('../pages/login/ContinueLogin'))
// 教程列表页面
const DocsList = getLazy(() => import('../pages/docs/DocsList'))
// 教程详情页面
const DocsDetail = getLazy(() => import('../pages/docs/DocsDetail'))
// 平台配置
const PlatformConfig = getLazy(() => import('../pages/platformConfig/index'))
// 成员管理
const MemberManagement = getLazy(() => import('../pages/memberManagement/index'))
// 团队信息管理
const TeamInfo = getLazy(() => import('../pages/teamInfo/index'))
// 个人信息管理
const PersonalInfo = getLazy(() => import('../pages/personalInfo/index'))
// 积分明细
const IntegralDetail = getLazy(() => import('../pages/integralDetail/index'))
// 订单列表
const Order = getLazy(() => import('../pages/order/Order'))
// 会员中心
const Vip = getLazy(() => import('../pages/vip/Vip'))
// 素材创作
const Creation = getLazy(() => import('../pages/creation/index'))
// 素材编辑
const MaterialEdit = getLazy(() => import('../pages/materialEdit/index'))
// 批量化裂变工具
const BatchTools = getLazy(() => import('../pages/batchTools/index'))
// 团队工具
const TeamTools = getLazy(() => import('../pages/teamTools/index'))
const Result = getLazy(() => import('../pages/teamTools/result'))


// 下面是模块
// 收藏模块
const BizPhotoCollect = getLazy(
	() => import('@/business-component/biz-photo-collect/BizPhotoCollect')
)

// 任务详情模块
const TaskDetail = getLazy(
	() => import('@/business-component/task-detail/TaskDetail')
)

const bizChildrenRoutersConfig: RouteObject[] = [
	'mannequin',
	'real',
	'wig',
	'goods',
	'texture',
	'tabularasa'
].map((item) => {
	return {
		path: item,
		element: <BizPhotoLayout />,
		handle: {
			type: item
		},
		children: [
			{
				path: '',
				element: <Navigate to="collect" />
			},
			{
				path: ':taskId/task',
				handle: {
					type: item
				},
				element: <TaskDetail />
			},
			{
				path: 'collect',
				handle: {
					type: item
				},
				element: <BizPhotoCollect />
			},
		]
	} as RouteObject
})


export const routersConfig: RouteObject[] = [
	{
		path: '/',
		element: <BaseLayout />,
		children: [
			{
				path: '/',
				element: <Navigate to="/workspace/real" />
			},
			{
				id: 'login',
				path: 'login',
				element: (
					<div className={'w-screen h-screen'}>
						<Login />
					</div>
				),
				handle: {
					isFull: true,
					isAuth: false
				}
			},
			{
				id: 'login-continue',
				path: 'login-continue',
				element: (
					<div className={'w-screen h-screen'}>
						<ContinueLogin />
					</div>
				),
				handle: {
					isFull: true,
					isAuth: false
				}
			},
			{
				path: 'workspace',
				element: <DataXLayout />,
				children: bizChildrenRoutersConfig
			},
			{
				path: 'workspace/creation',
				element: <Creation />,
				children: [
					{
						path: '',
						element: <Creation />
					},
					{
						path: '/workspace/creation/continuous',
						element: <Creation />
					},
					{
						path: '/workspace/creation/textToImg',
						element: <Creation />
					},
					{
						path: '/workspace/creation/similar',
						element: <Creation />
					},

				]
			},
			{
				path: 'workspace/materialEdit',
				element: <MaterialEdit />,
				children: [
					{
						path: '',
						element: <MaterialEdit />
					},
					{
						path: '/workspace/materialEdit/cut',
						element: <MaterialEdit />
					},
					{
						path: '/workspace/materialEdit/blockingOut',
						element: <MaterialEdit />
					},
					{
						path: '/workspace/materialEdit/clear',
						element: <MaterialEdit />
					},
					{
						path: '/workspace/materialEdit/extract',
						element: <MaterialEdit />
					},
					{
						path: '/workspace/materialEdit/fourSided',
						element: <MaterialEdit />
					},

				]
			},
			{
				path: 'workspace/teamTools',
				element: <TeamTools />,
				children: [
					{
						path: '',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/myMaterial',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/workflow',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/workflowTemplate',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/workflowTemplateDetail',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/importTemplate',
						element: <TeamTools />
					},
					{
						path: '/workspace/teamTools/result/:type',
						element: <Result />
					},

				]
			},
			{
				path: 'workspace/batchTools',
				element: <BatchTools />,
				children: [
					// {
					// 	path: '',
					// 	element: <BatchTools />
					// },
					{
						path: '/workspace/batchTools/uploadImage/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/uploadImage/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/continuous/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/continuous/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/textToImg/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/textToImg/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/similar/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/similar/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/cut/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/cut/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/blockingOut/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/blockingOut/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/clear/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/clear/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/gather/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/filter/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/filter/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/titleExtraction/index',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/titleExtraction/detail',
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/extract/index', 
						element: <BatchTools />
					},
					{
						path: '/workspace/batchTools/extract/detail',
						element: <BatchTools />
					},
				]
			},
			{
				path: 'docs',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <DocsList />
					},
					{
						path: ':docsId',
						element: <DocsDetail />
					}
				]
			},
			{
				path: 'order',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <Order />
					}
				]
			},
			{
				path: 'platformConfig',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <PlatformConfig />
					}
				]
			},
			{
				path: 'memberManagement',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <MemberManagement />
					}
				]
			},
			{
				path: 'teamInfo',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <TeamInfo />
					}
				]
			},
			{
				path: 'personalInfo',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <PersonalInfo />
					}
				]
			},
			{
				path: 'integralDetail',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <IntegralDetail />
					}
				]
			},
			{
				path: 'vip',
				element: <DataXDocsLayout />,
				children: [
					{
						path: '',
						element: <Vip />
					}
				]
			},
			{
				path: '*',
				element: <Navigate to="/" />
			}
		]
	}
]

export const routers = createBrowserRouter(routersConfig)
