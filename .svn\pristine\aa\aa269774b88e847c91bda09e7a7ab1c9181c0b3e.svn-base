package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialStyleCategory;
import com.dataxai.web.mapper.MaterialStyleCategoryMapper;
import com.dataxai.web.mapper.MaterialStyleMapper;
import com.dataxai.web.service.MaterialStyleCategoryService;
import com.dataxai.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class MaterialStyleCategoryServiceImpl implements MaterialStyleCategoryService {

    @Autowired
    private MaterialStyleCategoryMapper categoryMapper;
    
    @Autowired
    private MaterialStyleMapper materialStyleMapper;

    @Override
    public List<MaterialStyleCategory> queryAll(String name, Integer taskType) {
        return categoryMapper.selectByCondition(name, taskType);
    }

    @Override
    public MaterialStyleCategory getById(Integer id) {
        return categoryMapper.selectById(id);
    }

    @Override
    public boolean addMaterialStyleCategory(MaterialStyleCategory category) {
        return categoryMapper.insert(category) > 0;
    }

    @Override
    public boolean updateMaterialStyleCategory(MaterialStyleCategory category) {
        return categoryMapper.update(category) > 0;
    }

    @Override
    public boolean deleteMaterialStyleCategory(Integer id) {
        // 检查分类是否被使用
        if (isCategoryInUse(id)) {
            throw new ServiceException("该分类正在被使用，无法删除");
        }
        return categoryMapper.deleteById(id) > 0;
    }

    @Override
    public List<MaterialStyleCategory> queryPage(Integer pageNum, Integer pageSize, String name, Integer taskType) {
        int offset = (pageNum - 1) * pageSize;
        return categoryMapper.selectPageByCondition(offset, pageSize, name, taskType);
    }

    @Override
    public int countByCondition(String name, Integer taskType) {
        return categoryMapper.countByCondition(name, taskType);
    }
    
    @Override
    public boolean isCategoryInUse(Integer categoryId) {
        // 检查是否有风格使用此分类
        int count = materialStyleMapper.countByCondition(null, null, categoryId, null);
        return count > 0;
    }
}