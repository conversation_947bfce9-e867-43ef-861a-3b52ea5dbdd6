package com.dataxai.web.batch;

import com.dataxai.web.domain.Batch;
import com.dataxai.web.dto.BatchDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 批次任务工厂管理器
 *
 * <p>管理不同类型的批次任务工厂，根据任务类型选择合适的工厂进行处理</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class BatchTaskFactoryManager {

    /**
     * 工厂缓存Map，key为任务类型，value为对应的工厂实例
     */
    private final Map<Long, BatchTaskFactory> factoryCache = new ConcurrentHashMap<>();

    /**
     * 所有批次任务工厂的列表
     */
    private final List<BatchTaskFactory> factories;

    /**
     * 构造函数，自动注入所有的批次任务工厂
     *
     * @param factories 所有批次任务工厂的列表
     */
    @Autowired
    public BatchTaskFactoryManager(List<BatchTaskFactory> factories) {
        this.factories = factories;
        initFactoryCache();
    }

    /**
     * 初始化工厂缓存
     */
    private void initFactoryCache() {
        for (BatchTaskFactory factory : factories) {
            Long[] supportedTypes = factory.getSupportedTaskTypes();
            if (supportedTypes != null) {
                for (Long taskType : supportedTypes) {
                    factoryCache.put(taskType, factory);
                    log.debug("注册批次任务工厂：{} 支持任务类型：{}",
                             factory.getClass().getSimpleName(), taskType);
                }
            }
        }
        log.info("批次任务工厂管理器初始化完成，共注册 {} 个工厂，支持 {} 种任务类型",
                factories.size(), factoryCache.size());
    }

    /**
     * 根据任务类型获取对应的工厂
     *
     * @param taskType 任务类型
     * @return 批次任务工厂实例
     * @throws IllegalArgumentException 如果找不到支持该任务类型的工厂
     */
    public BatchTaskFactory getFactory(Long taskType) {
        if (taskType == null) {
            throw new IllegalArgumentException("任务类型不能为空");
        }

        BatchTaskFactory factory = factoryCache.get(taskType);
        if (factory == null) {
            throw new IllegalArgumentException("未找到支持任务类型 " + taskType + " 的批次任务工厂");
        }

        return factory;
    }

    /**
     * 处理批次任务
     *
     * @param dto           批次DTO
     * @param batch         批次对象
     * @param fileBytesList 文件字节数组列表
     * @param tableBytes    表格字节数组
     * @param userId        用户ID
     * @param files         原始文件列表
     * @param pathList      路径列表
     * @throws IOException IO异常
     */
    public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                 Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

        if (dto == null || batch == null) {
            throw new IllegalArgumentException("批次DTO和批次对象不能为空");
        }

        Long taskType = dto.getType();
        if (taskType == null) {
            throw new IllegalArgumentException("任务类型不能为空");
        }
        dto.getTaskParam();
        System.out.println("任务类型：" + taskType);
        System.out.println("任务参数：" + dto.getTaskParam());
        System.out.println(dto);
        System.out.println("-----------------------------------------------------------");
        System.out.println("任务参数：" + dto.getImageNumber());
        System.out.println("------------------------------------------------------------------");
        System.out.println("------------------------------------------------------------------");


        try {
            // 获取对应的工厂
            BatchTaskFactory factory = getFactory(taskType);
            System.out.println("任务参数：" + dto.getImageNumber());

            // 处理批次任务
            factory.processBatchTask(dto, batch, fileBytesList, tableBytes, userId, files, pathList);


        } catch (Exception e) {
            log.error("批次任务处理失败 - 批次ID：{}，任务类型：{}，错误：{}",
                     batch.getBatchId(), taskType, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查是否支持指定的任务类型
     *
     * @param taskType 任务类型
     * @return true-支持，false-不支持
     */
    public boolean supportsTaskType(Long taskType) {
        return taskType != null && factoryCache.containsKey(taskType);
    }

    /**
     * 获取所有支持的任务类型
     *
     * @return 支持的任务类型列表
     */
    public List<Long> getSupportedTaskTypes() {
        return factoryCache.keySet().stream().sorted().collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取工厂统计信息
     *
     * @return 工厂统计信息
     */
    public String getFactoryStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("批次任务工厂统计信息:\n");
        sb.append("工厂总数: ").append(factories.size()).append("\n");
        sb.append("支持任务类型总数: ").append(factoryCache.size()).append("\n");
        sb.append("支持的任务类型: ").append(getSupportedTaskTypes()).append("\n");

        for (BatchTaskFactory factory : factories) {
            sb.append("- ").append(factory.getClass().getSimpleName())
              .append(": ").append(java.util.Arrays.asList(factory.getSupportedTaskTypes())).append("\n");
        }

        return sb.toString();
    }
}