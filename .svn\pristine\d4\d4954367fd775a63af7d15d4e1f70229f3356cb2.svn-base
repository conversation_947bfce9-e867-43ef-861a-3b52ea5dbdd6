
import { useEffect, useState, useMemo } from 'react';
import { Button, Checkbox, message, Pagination, Spin } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { CopyOutlined } from '@ant-design/icons';
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getOnePage } from '@/api/task'
import { handleCopyText } from '@/utils/copyText'
import { useLocation } from 'react-router-dom';
import './index.css';
import dayjs from 'dayjs';
import { batchZipUrl } from '@/api/common'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const ContinuousDetail = () => {
	const location = useLocation();

	const [userInfo] = useAtomMethod(userinfoService.userInfo)

	const { batchId } = location.state || {};

	const [record, setRecord] = useState<any>();
	const [detailTotal, setDetailTotal] = useState(0);
	const [pageLoading, setPageLoading] = useState(false);
	const fetchDetail = (pageNum: number, pageSize: number,) => {
		setPageLoading(true)
		getOnePage({ batchId, pageNum, pageSize }).then((res: any) => {
			setRecord(res)
			setDetailTotal(res.total)
		}).catch(err => {
			message.error(`请求批次详情失败：${err?.data?.msg}`)
		}).finally(() => {
			setPageLoading(false)
		})
	}
	useEffect(() => {
		fetchDetail(1, 50)
	}, []);

	const [style, setStyle] = useState('');

	const [messageApi, messageContextHolder] = message.useMessage()


	useEffect(() => {
		if (record?.data) {
			const listData = record.data.map((item: any) => {
				const results = item?.taskOrdinalList?.[0]?.ordinalImgResultList || [];
				const image = (results || []).slice(0, 4).map((res: any) => ({
					small: res?.resSmallImgUrl,
					taskImage: res?.resImgUrl,
					hasUploaded: res?.hasUploaded,
				})).filter((it: any) => it?.taskImage);
				return {
					image,
					keyword: item?.taskOrdinalList?.[0]?.shortCutDesc || ''
				}
			})
			setImageList(listData || [])
		}

		if (record?.data[0]) {
			setStyle(JSON.parse(record?.data[0].taskOrdinalList[0].taskParam).style)
		}
	}, [record]);
	const [selectedImages, setSelectedImages] = useState<string[]>([]);
	const [selectedItems, setSelectedItems] = useState<number[]>([]);

	const [imageList, setImageList] = useState([{ image: [], keyword: '', }]);

	const handleSelect = (imageUrl: string) => {
		setSelectedImages(prev => {
			const newSelected = prev.includes(imageUrl)
				? prev.filter(url => url !== imageUrl)
				: [...prev, imageUrl];
			// 根据选择数量自动显示/隐藏操作栏
			setShowBatchActions(newSelected.length > 0);
			return newSelected;
		});
		updateSelectedItemStatus();
	};

	const [showBatchActions, setShowBatchActions] = useState(false);

	const toggleBatchActions = () => {
		setShowBatchActions(!showBatchActions);
		if (!showBatchActions === false) {
			setSelectedImages([]); // 清空已选图片
		}
	};
	//实时更新选择状态
	useEffect(() => {
		setShowBatchActions(selectedImages.length > 0);
	}, [selectedImages]);

	const cancelSelection = () => {
		setSelectedImages([]);
		setShowBatchActions(false); // 取消选择时隐藏操作栏
	};
	const updateSelectedItemStatus = () => {
		imageList.forEach((item, index) => {
			const enabledImages = (item.image || []).filter((img: any) => !checkImageDisabled(img, 'continuous'))
			const allEnabledSelected = enabledImages.length > 0 && enabledImages.every((img: any) => selectedImages.includes(img.taskImage));
			if (allEnabledSelected && !selectedItems.includes(index)) {
				setSelectedItems([...selectedItems, index]);
			} else if (!allEnabledSelected && selectedItems.includes(index)) {
				setSelectedItems(selectedItems.filter(i => i !== index));
			}
		});
	};



	// 图片列表渲染
	const getTaskImageComponent = useMemoizedFn(
		(image: any) => {
			// 展平所有的image数组获取预览图片列表
			let previewImageList = imageList?.flatMap((item: any) =>
				item.image.map((img: any) => img.taskImage)
			) || [];
			// 计算当前图片在previewImageList中的索引
			const currentIndex = previewImageList.findIndex(url => url === image.taskImage);
			// 计算当前图片的选中状态
			const isCheckedArray = previewImageList?.map((url: string) =>
				selectedImages.includes(url)
			) || [];
			// 处理选中状态变化的回调函数
			const handleCheckChange = (checked: boolean, changedIndex: number) => {
				const imageUrl = previewImageList[changedIndex];
				if (imageUrl) {
					if (checked) {
						setSelectedImages(prev => [...prev, imageUrl]);
					} else {
						setSelectedImages(prev => prev.filter(url => url !== imageUrl));
					}
					setShowBatchActions(checked ? true : selectedImages.length > 0);
				}
			};
			return (
				<div
					className={
						'aspect-square w-full h-full'
					}
				>
					<LikeImage
						type={1}
						imageId={''}
						taskId={''}
						taskOrdinalId={''}
						imgUrl={image.taskImage}
						oriImgUrl={image.taskImage}
						smallImgUrl={image.small}
						markImgUrl={image.taskImage}
						progress={100}
						previewImages={previewImageList}
						index={currentIndex !== -1 ? currentIndex : 0} // 如果找不到则默认0
						seed={-1}
						delVisible={false}
						likeVisible={false}
						downloadVisible={false}
						comparison={false}
						canChecked={true}
						isCheckedArray={isCheckedArray}
						onCheckChange={handleCheckChange}
					/>
				</div>
			)
		}
	)
	const [downloadLoading, setDownloadLoading] = useState(false);
	// 下载图片
	const handleDownloadImgages = () => {
		setDownloadLoading(true);
		batchZipUrl({ imageUrls: selectedImages, type: 6 }).then((res: any) => {
			if (res) {
				window.open(res, '_blank'); // 在新标签页打开下载链接
			} else {
				messageApi.error('获取下载链接失败');
			}
		}).catch(err => {
			messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
		}).finally(() => {
			setDownloadLoading(false);
		});
	};

	const [detailPage, setDetailPage] = useState(1);
	const handleDetailPage = (page: number, pageSize: number) => {
		setDetailPage(page);  // 更新页码状态
		fetchDetail(page, pageSize)
	}

	//copy复制提示词
	const handleCopy = (keyword: string) => {
		handleCopyText(keyword, '提示词')
	};

	return (
		<div className="h-full w-full p-[20px]">
			{messageContextHolder}
			{pageLoading ? (
				<div className="flex justify-center items-center h-full">
					<Spin size="large" />
				</div>
			) : (<>
				<Button
					type="primary"
					style={{ display: 'block', marginLeft: 'auto' }}
					onClick={toggleBatchActions}
				>
					{showBatchActions ? '取消批量操作' : '批量操作'}
				</Button>
				<div className="w-full flex items-center justify-start h-[60px] border-b-[1px] border-normal">
					<p className="mr-[20px]">批次: {record?.batch?.batchNumber}</p>
					<p className="mr-[20px]">
						创建时间：{dayjs(record?.batch?.createTime).format('YYYY-MM-DD HH:mm:ss')}
					</p>
					<p className="mr-[20px]">风格：{style || '-'}</p>
					<p>
						总数：{record?.batch?.totalAmount}
						<span style={{ color: '#389e0d', marginLeft: '6px' }}>
							成功：{record?.batch?.successAmount}
						</span>
						{record?.batch?.failAmount > 0 && (
							<span style={{ color: '#CF1322', marginLeft: '6px' }}>
								失败：{record?.batch?.failAmount}
							</span>
						)}
					</p>
				</div>
				<div className="bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide">
					<div className="flex flex-wrap pt-[10px]">
						{imageList.map((item: any, index) => {
							return (
								<div className="w-[25%] pr-[12px] p-2 relative">
									{(() => {
										const enabledImages = (item.image || []).filter((img: any) => !checkImageDisabled(img, 'continuous'))
										const enabledTaskImages = enabledImages.map((img: any) => img.taskImage)
										const allEnabledChecked = enabledImages.length > 0 && enabledImages.every((img: any) => selectedImages.includes(img.taskImage))
										return (
											<Checkbox
												className="itemCheckbox"
												checked={allEnabledChecked}
												disabled={enabledImages.length === 0}
												onChange={(e) => {
													if (e.target.checked) {
														setSelectedImages((prev) => {
															const newArray = [...prev, ...enabledTaskImages]
															return Array.from(new Set(newArray))
														})
														setSelectedItems([...selectedItems, index])
													} else {
														setSelectedImages((prev) => prev.filter((img) => !enabledTaskImages.includes(img)))
														setSelectedItems(selectedItems.filter((i) => i !== index))
													}
												}}
											/>
										)
									})()}
									<div className="text-keyWord" title={item.keyword} style={{ cursor: 'pointer' }}>提示词：{item.keyword}</div>
									<CopyOutlined
										className="copy-icon"
										onClick={() => handleCopy(item.keyword)}
									/>
									<div
										className="w-full h-full flex  pt-[120px] rounded-lg items-center justify-center  relative group bg-[#d5d5d5] overflow-hidden"
										style={{ flexDirection: 'row', flexWrap: 'wrap', height: 'fit-content' }}
									>
										{item.image.map((imageUrl: any, index: any) => (
											<div className="max-w-[300px]   aspect-square  w-[50%] pr-[12px] p-2">
												<div
													key={index}
													className="w-full h-full flex min-h-[120px] rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden"
													style={{ flexDirection: 'row' }}
												>
													<Checkbox
														className="absolute top-4 left-4 z-10 "
														style={{ transform: 'scale(1.25)' }} // 放大1.5倍
														checked={selectedImages.includes(
															imageUrl.taskImage
														)}
														onChange={() => handleSelect(imageUrl.taskImage)}
														disabled={checkImageDisabled(imageUrl, 'continuous')}
													/>
													{imageUrl.hasUploaded && <p className="absolute bottom-4  z-10"
														style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
													{getTaskImageComponent(imageUrl)}
												</div>
											</div>
										))}
									</div>
								</div>
							)
						})}
					</div>
					{/* <Pagination align="center" style={{ margin: '20px 0 120px' }} current={detailPage} pageSize={50} onChange={handleDetailPage} total={detailTotal} showSizeChanger={false} /> */}
					<BottomActionBar
						visible={selectedImages.length > 0 || showBatchActions}
						selectedCount={selectedImages.length}
						isAllSelected={imageList.length > 0 && selectedImages.length === imageList.reduce((total: number, item: any) => total + item.image.filter((img: any) => !checkImageDisabled(img, 'continuous')).length, 0)}
						onToggleSelectAll={() => {
							const enabledImagesCount = imageList.reduce((total: number, item: any) => total + item.image.filter((img: any) => !checkImageDisabled(img, 'continuous')).length, 0);
							if (imageList.length > 0 && selectedImages.length === enabledImagesCount) {
								setSelectedImages([])
							} else {
								const allEnabledImages = imageList.reduce((acc: string[], item: any) => {
									return [...acc, ...item.image.filter((img: any) => !checkImageDisabled(img, 'continuous')).map((img: any) => img.taskImage)]
								}, [])
								setSelectedImages([...new Set(allEnabledImages)] as string[])
							}
						}}
						onCancelSelection={cancelSelection}
						syncEnabled
						selectedItems={selectedImages}
						extractImageUrl={(imageUrl) => imageUrl}
						syncExtraParams={{}}
						onDownload={handleDownloadImgages}
						downloadLoading={downloadLoading}
						downloadDisabled={selectedImages.length === 0}
						enableWorkflow={userInfo?.currentMode == 2}
						onActionFinished={() => {
							// 刷新积分或数据
							userinfoService.refresh();
							fetchDetail(detailPage, 50);
							setSelectedImages([])
						}}
						actionDisabled={selectedImages.length === 0}
					/>
				</div>
			</>)}
		</div>
	)
};

export default ContinuousDetail;





