/**
 * @Author: wuyang
 * @Date: 2024/1/18
 * @Description: ""
 */
import { DataXProgress } from '@/component/data-x-progress/DataXProgress'
import classNames from 'classnames'
import styles from './generateImage.module.scss'
import { ReactComponent as PreviewSvg } from '@/asset/svg/preview.svg'
import { ReactComponent as FollowSvg } from '@/asset/svg/follow.svg'
import { ReactComponent as FollowActiveSvg } from '@/asset/svg/follow-active.svg'
import { ReactComponent as DownloadSvg } from '@/asset/svg/download-wrap.svg'
import {
    GenerateImagePreview,
    IGenerateImagePreviewPropsImperativeHandle
} from '@/component/generate-image/GenerateImagePreview'
import {
    ComparisonImagePreview,
    IComparisonImagePreviewPropsImperativeHandle
} from '@/component/generate-image/ComparisonImagePreview'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Dropdown, message, Modal, Tooltip, Spin } from 'antd'
import { exec4kImage, followImage, likeImage } from '@/api/image'
import { EBizPhone } from '@/types/bizPhone'
import { downFile } from '@/utils/downFile'
import { useTaskListService } from '@/common/services/task-list/taskListContext'
import {
    FormOutlined,
    FileImageOutlined,
    FilePptOutlined,
    FileZipOutlined,
    DeleteOutlined,
    ScissorOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useMemoizedFn } from 'ahooks'
import { eventBusService } from '@/common/services/event-bus/eventBusService'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { AI_VIOLATION_FLAG } from '@/constant/index'
import { deleteHistoryImage } from '@/api/task'
import { divide } from 'lodash'
interface ILikeImageProps {
    // 任务id
    taskId: string
    taskOrdinalId: string
    type?: any
    // 任务序号 ID
    // 是否收藏
    isFollow?: boolean
    // 图片 id
    imageId: string
    // 执行进度 0 - 100
    progress?: number
    // 预计排队时间
    waitTime?: number
    // 结果图片大图
    imgUrl: string
    // 结果图片小图
    smallImgUrl: string
    // 模版图片
    markImgUrl: string
    // 原图
    oriImgUrl: string
    // 是否点赞
    isLike?: boolean
    // 是否点踩
    isUnLike?: boolean
    // 预览图片列表
    previewImages?: string[]
    // 原图片列表
    oriImgUrlList?: string[]
    // 当前图片索引
    index?: number
    // 关注回调
    onFollow?: () => void
    // 删除回调
    onDelete?: () => void
    // 裁剪回调
    onCut?: () => void
    // 点赞回调
    onLike?: () => void
    // 点踩回调
    onUnLike?: () => void
    seed: number,
    cutVisible?: boolean
    delVisible?: boolean
    downloadVisible?: boolean
    likeVisible?: boolean
    comparison?: boolean
    canChecked?: boolean
    demarcationPreview?: boolean
    downloadProps?: number
    status?: string
    isCheckedArray?: boolean[]
    onCheckChange?: (checked: boolean, index: number) => void

}
export const LikeImage = (props: ILikeImageProps) => {
    const {
        isFollow: defaultFollow = false,
        progress: defaultProgress = 1,
        waitTime,
        isLike: defaultIsLike = false,
        isUnLike: defaultIsUnLike = false,
        previewImages = [],
        oriImgUrlList = [],
        index = 0,
        imageId,
        imgUrl,
        smallImgUrl,
        oriImgUrl,
        markImgUrl,
        onFollow,
        onDelete,
        onCut,
        onLike,
        onUnLike,
        taskId,
        taskOrdinalId,
        type,
        seed,
        delVisible,
        cutVisible = false,
        downloadVisible = true,
        likeVisible,
        comparison = true,
        canChecked = false,
        demarcationPreview = false,
        downloadProps = 1,
        status,
        isCheckedArray = [],
        onCheckChange = () => { }
    } = props

    const navigate = useNavigate()
    const [canDownload4K] = useAtomMethod(userinfoService.canDownload4K)
    const taskList = useTaskListService()
    const progress = parseFloat(parseFloat(`${defaultProgress}`).toFixed(2))
    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, contextHolderMessage] = message.useMessage()
    const previewRef = useRef<IGenerateImagePreviewPropsImperativeHandle>(null)
    const ComparisonPreviewRef = useRef<IComparisonImagePreviewPropsImperativeHandle>(null)

    const containerRef = useRef<HTMLDivElement>(null)
    const [isLike, setIsLike] = useState(defaultIsLike)
    const [isUnLike, setIsUnLike] = useState(defaultIsUnLike)
    const [isFollow, setIsFollow] = useState(defaultFollow)

    useEffect(() => {
        setIsLike(defaultIsLike)
    }, [defaultIsLike])
    useEffect(() => {
        setIsUnLike(defaultIsUnLike)
    }, [defaultIsUnLike])
    useEffect(() => {
        setIsFollow(defaultFollow)
    }, [defaultFollow])


    // 收藏
    const handleFollow = useMemoizedFn(() => {
        const currentFollow = !isFollow
        setIsFollow(() => currentFollow)
        followImage({ imageId, follow: currentFollow })
            .then((res) => {
                onFollow?.()
            })
            .catch(() => { })
    })
    // 删除
    const handleDel = useMemoizedFn(() => {
        deleteHistoryImage(oriImgUrl, taskId, taskOrdinalId).then(res => {
            messageApi.success('删除图片成功')
            onDelete?.()
        }).catch(err => {
            messageApi.warning(`删除失败：${err?.data?.msg}`)
        })
    })
    // 裁剪
    const handleCut = useMemoizedFn(() => {
        onCut?.()
    })

    // 下载原图
    const handleDownOriImage = useMemoizedFn(() => {
        if (imgUrl) {
            const originalName = imgUrl.split('/').at(-1) || 'image';
            const today = new Date();
            const formattedDate = `${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}-${today.getFullYear()}`;

            let suffix = '';
            if (type == '11') {
                suffix = '_去背景' + formattedDate;
            } else if (type == 12) {
                suffix = '_变清晰' + formattedDate;
            }
            const filename = originalName.split('.')[0] + suffix + '.' + originalName.split('.')[1];
            downFile({
                url: imgUrl,
                filename: filename
            })
        } else {
            messageApi.warning('图片地址有误')
        }
    })
    // 下载白底图
    const handleDownWhiteImage = useMemoizedFn(() => {
        if (markImgUrl) {
            const originalName = markImgUrl.split('/').at(-1) || 'image';
            const today = new Date();
            const formattedDate = `${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}-${today.getFullYear()}`;

            let suffix = '';
            if (type == '11') {
                suffix = '_去背景' + formattedDate;
            }
            const filename = originalName.split('.')[0] + suffix + '.' + originalName.split('.')[1];
            downFile({
                url: markImgUrl,
                filename: filename
            })
        } else {
            messageApi.warning('图片地址有误')
        }
        // if (markImgUrl) {
        //     downFile({
        //         url: markImgUrl,
        //         filename: markImgUrl.split('/').at(-1)
        //     })
        // } else {
        //     messageApi.warning('图片地址有误')
        // }
    })
    // 下载 4K 图
    const handleDown4KImage = useMemoizedFn(() => {
        modal.confirm({
            centered: true,
            title: (
                <div className={'text-[18px] text-normal text-center'}>
                    下载 原图分辨率 图片
                </div>
            ),
            content: (
                <div className={'text-normal text-center my-[10px]'}>
                    下载 原图分辨率 图片，需消耗
                    <span className={'text-[#F42929]'}>1</span>
                    积分，是否下载？
                </div>
            ),
            icon: null,
            okText: '下载',
            cancelText: '取消',
            onOk() {
                exec4kImage({
                    taskId,
                    type,
                    originalUrl: imgUrl,
                    referedTaskOrdinalId: taskOrdinalId
                })
                    .then((res) => {
                        userinfoService.refresh()
                        const { resImgUrl = '', imageId = '' } = res
                        if (resImgUrl) {
                            messageApi.loading('正在下载 原图分辨率 图片，请您稍等')
                            console.log('www--------->resImgUrl--->', resImgUrl)
                            downFile({
                                url: resImgUrl,
                                filename: resImgUrl.split('/').at(-1)
                            }).finally(() => {
                                messageApi.destroy()
                            })
                        } else {
                            eventBusService.add4kImage(imageId)
                        }
                    })
                    .catch((err) => {
                        messageApi.warning(err?.data?.msg)
                    })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    })
    // 下载 PSD 图
    const handleDownPSDImage = useCallback(() => { }, [])
    const writeDownList = useMemo(() => {
        const downWriteItem = {
            key: '1',
            label: '下载白底图',
            icon: <FileImageOutlined style={{ fontSize: '16px' }} />,
            onClick: handleDownWhiteImage
        }
        const downTransparentItem = {
            key: '1',
            label: '下载透明图',
            icon: <FileImageOutlined style={{ fontSize: '16px' }} />,
            onClick: handleDownOriImage
        }
        return [downTransparentItem, downWriteItem]
    }, [
        handleDownOriImage,
        handleDownWhiteImage
    ])


    const downItemList = useMemo(() => {
        const psdItem = {
            key: '1',
            label: '下载 PSD',
            icon: <FilePptOutlined style={{ fontSize: '16px' }} />,
            onClick: handleDownPSDImage
        }
        const downBigItem = {
            key: '2',
            label: '图片下载',
            icon: <FileImageOutlined style={{ fontSize: '16px' }} />,
            onClick: handleDownOriImage
        }
        const down4kItem = {
            key: '3',
            label: '高清图片下载',
            icon: <FileZipOutlined style={{ fontSize: '16px' }} />,
            onClick: handleDown4KImage
        }
        return canDownload4K ? [downBigItem, down4kItem] : [downBigItem]
    }, [
        handleDown4KImage,
        handleDownOriImage,
        handleDownPSDImage,
        canDownload4K
    ])

    const violatingFlag = useMemo(() => {
        return (smallImgUrl || '').includes(AI_VIOLATION_FLAG)
    }, [smallImgUrl])
    // 排队或者执行中
    if (progress < 1) {
        return (
            <div
                className={
                    'w-full h-full flex justify-center items-center flex-col p-[10px]'
                }
            >
                {/* <div className={'w-[110px] h-[100px] '}>
                    <DataXProgress percent={progress * 100} />
                </div> */}
                {
                    status && status == '3' ? <div>
                        <div className={'w-[110px] h-[100px]  ml-[auto] mr-[auto]'} key="executing">
                            <DataXProgress loading={true} />
                        </div>
                        <div className={'ant text-subtext text-center mt-[10px]'}>
                            执行中
                        </div>
                    </div> : <div>
                        <div className={'w-[110px] h-[100px] ml-[auto] mr-[auto]'} key="queuing">
                            <DataXProgress percent={0} />
                        </div>
                        <div className={'text-subtext text-center mt-[10px]'}>
                            {
                                waitTime == undefined || waitTime == null
                                    ? '排队中'
                                    : waitTime == 0
                                        ? '排队中 预计1分钟以内'
                                        : waitTime > 0
                                            ? `排队中 预计${waitTime}分钟`
                                            : '排队中'
                            }
                        </div>
                    </div>
                }
            </div>
        )
    }
    return (
        <div
            className={classNames(
                'w-full h-full relative',
                styles.generateImageContainer
            )}
            ref={containerRef}
        >
            {contextHolderMessage}
            {contextHolder}
            <div
                className={classNames('w-full h-full flex justify-center items-center')}
            >
                <img
                    alt={''}
                    src={smallImgUrl}
                    className={`w-full h-full object-contain select-none`}
                // className={`max-w-full max-h-full select-none`}
                />
            </div>

            <div
                className={classNames(
                    'w-full h-full absolute top-0 left-0 bg-black/40 transition',
                    styles.generateImageContainerMask
                )}
            >
                <div
                    className={
                        'cursor-pointer w-full h-full absolute top-0 left-0 flex justify-center items-center'
                    }
                    onClick={() => {

                        demarcationPreview ? ComparisonPreviewRef.current?.showPreview({
                            oriImgUrl,
                            imgUrl
                        }) :
                            previewRef.current?.showPreview({
                                oriImgUrl,
                                markImgUrl,
                                type,
                                comparison,
                                canChecked,
                                index,
                                previewImages: previewImages?.length ? previewImages : [imgUrl],
                                oriImgUrlList: oriImgUrlList?.length ? oriImgUrlList : [],
                                isCheckedArray,
                                onCheckChange: (checked, idx) => onCheckChange(checked, idx)
                            })

                    }}
                >
                    <div
                        className={
                            'text-white text-[12px] flex justify-center items-center'
                        }
                    >
                        <PreviewSvg
                            className={'w-[16px] inline mr-[8px]'}
                            fill={'#ffffff'}
                        />
                        <span>预览</span>
                    </div>
                </div>
                {!violatingFlag && (
                    <>

                        {/* 裁剪 */}
                        {cutVisible && <div
                            className={
                                'cursor-pointer absolute w-[24px] h-[22px] top-4  right-4'
                            }
                            onClick={handleCut}
                        >

                            <Tooltip title='裁剪图片'>
                                <ScissorOutlined style={{ color: '#fff', fontSize: '17px' }} className={'svg-hover-white'} />
                            </Tooltip>
                        </div>}
                        {/* 删除 */}
                        {delVisible && <div
                            className={
                                'cursor-pointer absolute w-[22px] h-[22px] top-[6px]  right-[36px]'
                            }
                            onClick={handleDel}
                        >
                            <Tooltip title='删除图片'>
                                <img className="w-[20px]  hover:content-[url('@/asset/icon/del-active.png')]" src={require('@/asset/icon/del.png')} />
                            </Tooltip>
                            {/* <DeleteOutlined style={{ color: '#fff', fontSize: '17px' }} className={'svg-hover-white'} /> */}
                        </div>}
                        {/* 收藏 */}
                        {likeVisible && <div
                            className={
                                'cursor-pointer absolute w-[20px] h-[20px] top-[8px]  right-[36px]'
                            }
                            onClick={handleFollow}
                        >
                            {isFollow ? (
                                <Tooltip title='取消收藏'>
                                    <FollowActiveSvg
                                        className={'w-[16px]'}
                                        fill={'var(--primary-color)'}
                                    />
                                </Tooltip>
                            ) : (
                                <Tooltip title='收藏图片'>
                                    <FollowSvg className={'w-[16px] svg-hover-white'}></FollowSvg>
                                </Tooltip>
                            )}
                        </div>}
                        {/* 下载 */}

                        {(canDownload4K || downloadProps == 2) && downloadVisible && <Dropdown
                            menu={{
                                items: downloadProps == 2 ? writeDownList : downItemList
                            }}
                            placement="topRight"
                            arrow={{ pointAtCenter: false }}
                            overlayClassName={'w-500px'}
                            mouseLeaveDelay={0.4}
                            getPopupContainer={(parent) => {
                                return containerRef.current!
                            }}
                        >
                            <div
                                className={
                                    'cursor-pointer absolute w-[20px] h-[20px] top-[8px] right-[8px]'
                                }
                            >
                                <DownloadSvg className={'w-[16px] h-[16px] svg-hover-white'} />
                                {/* <p>此处渲染列表</p> */}
                            </div>
                        </Dropdown>}
                        {!canDownload4K && downloadProps != 2 && downloadVisible &&
                            <div
                                className={
                                    'cursor-pointer absolute w-[20px] h-[20px] top-[8px] right-[8px]'
                                }
                            >
                                <Tooltip title='下载图片'>
                                    <DownloadSvg onClick={handleDownOriImage} className={'w-[16px] h-[16px] svg-hover-white'} />
                                </Tooltip>
                                {/* <p>此处渲染列表</p> */}
                            </div>
                        }
                    </>
                )}
            </div>

            <GenerateImagePreview ref={previewRef} />
            <ComparisonImagePreview ref={ComparisonPreviewRef} />
        </div>
    )
}
