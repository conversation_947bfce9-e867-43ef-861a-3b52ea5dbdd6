package com.dataxai.web.service;

import com.dataxai.web.domain.CropExtractCategory;
import java.util.List;

public interface CropExtractCategoryService {
	List<CropExtractCategory> queryAll(String name, Integer status);

	CropExtractCategory getById(Integer id);

	boolean add(CropExtractCategory category);

	boolean update(CropExtractCategory category);

	boolean deleteById(Integer id);

	List<CropExtractCategory> queryPage(Integer pageNum, Integer pageSize, String name, Integer status);

	int countByCondition(String name, Integer status);
} 