package com.dataxai.service;

import java.io.IOException;
import java.util.List;
import com.dataxai.domain.RiskDetectionTaskDetail;

/**
 * 风险检测任务详情表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRiskDetectionTaskDetailService
{
    /**
     * 查询风险检测任务详情表
     *
     * @param id 风险检测任务详情表主键
     * @return 风险检测任务详情表
     */
    public RiskDetectionTaskDetail selectRiskDetectionTaskDetailById(Long id);

    /**
     * 查询风险检测任务详情表列表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 风险检测任务详情表集合
     */
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailList(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 根据任务ID查询详情列表
     *
     * @param taskId 任务ID
     * @return 风险检测任务详情表集合
     */
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailByTaskId(Long taskId);

    /**
     * 新增风险检测任务详情表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    public int insertRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 批量新增风险检测任务详情表
     *
     * @param riskDetectionTaskDetailList 风险检测任务详情表列表
     * @return 结果
     */
    public int insertRiskDetectionTaskDetailBatch(List<RiskDetectionTaskDetail> riskDetectionTaskDetailList) throws IOException;

    /**
     * 修改风险检测任务详情表
     *
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    public int updateRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 删除风险检测任务详情表
     *
     * @param id 风险检测任务详情表主键
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailById(Long id);

    /**
     * 批量删除风险检测任务详情表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailByIds(Long[] ids);

    /**
     * 根据任务ID删除详情
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailByTaskId(Long taskId);
}