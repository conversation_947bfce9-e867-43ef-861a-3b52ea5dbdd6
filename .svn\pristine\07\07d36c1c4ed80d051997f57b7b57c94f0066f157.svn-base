"use strict";(self.webpackChunkai_console=self.webpackChunkai_console||[]).push([[49],{57317:function(e,n,t){t.d(n,{A:function(){return r}});var o=t(46916);function r(e,n,t,r){var i=o.unstable_batchedUpdates?function(e){o.unstable_batchedUpdates(t,e)}:t;return null!==e&&void 0!==e&&e.addEventListener&&e.addEventListener(n,i,r),{remove:function(){null!==e&&void 0!==e&&e.removeEventListener&&e.removeEventListener(n,i,r)}}}},88850:function(e,n,t){t.d(n,{A:function(){return l}});var o=t(48524),r=t(9939),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M518.3 459a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V856c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V613.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 459z"}},{tag:"path",attrs:{d:"M811.4 366.7C765.6 245.9 648.9 160 512.2 160S258.8 245.8 213 366.6C127.3 389.1 64 467.2 64 560c0 110.5 89.5 200 199.9 200H304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4a245.6 245.6 0 0152.4-49.9c41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10C846.1 454.5 884 503.8 884 560c0 33.1-12.9 64.3-36.3 87.7a123.07 123.07 0 01-87.6 36.3H720c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1C870.5 760 960 670.5 960 560c0-92.7-63.1-170.7-148.6-193.3z"}}]},name:"cloud-upload",theme:"outlined"},a=t(93659),c=function(e,n){return r.createElement(a.A,(0,o.A)({},e,{ref:n,icon:i}))};var l=r.forwardRef(c)},93802:function(e,n,t){t.d(n,{A:function(){return Re}});var o=t(27569),r=t(9939),i=t(7278),a=t(76787),c=t.n(a),l=t(48524),s=t(68887),u=t(94423),f=t(62656),d=t(48281);function m(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var v=t(8076),p=t(85241),g=t(57317),h=t(99371),A=t(98684),w=t(84546),b=r.createContext(null),C=function(e){var n=e.visible,t=e.maskTransitionName,o=e.getContainer,i=e.prefixCls,a=e.rootClassName,l=e.icons,f=e.countRender,d=e.showSwitch,m=e.showProgress,v=e.current,p=e.transform,g=e.count,C=e.scale,x=e.minScale,y=e.maxScale,S=e.closeIcon,I=e.onActive,E=e.onClose,k=e.onZoomIn,N=e.onZoomOut,M=e.onRotateRight,R=e.onRotateLeft,z=e.onFlipX,O=e.onFlipY,T=e.onReset,L=e.toolbarRender,j=e.zIndex,P=e.image,Y=(0,r.useContext)(b),D=l.rotateLeft,X=l.rotateRight,H=l.zoomIn,Z=l.zoomOut,B=l.close,W=l.left,V=l.right,F=l.flipX,G=l.flipY,U="".concat(i,"-operations-operation");r.useEffect((function(){var e=function(e){e.keyCode===h.A.ESC&&E()};return n&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[n]);var _=function(e,n){e.preventDefault(),e.stopPropagation(),I(n)},Q=r.useCallback((function(e){var n=e.type,t=e.disabled,o=e.onClick,a=e.icon;return r.createElement("div",{key:n,className:c()(U,"".concat(i,"-operations-operation-").concat(n),(0,u.A)({},"".concat(i,"-operations-operation-disabled"),!!t)),onClick:o},a)}),[U,i]),J=d?Q({icon:W,onClick:function(e){return _(e,-1)},type:"prev",disabled:0===v}):void 0,q=d?Q({icon:V,onClick:function(e){return _(e,1)},type:"next",disabled:v===g-1}):void 0,K=Q({icon:G,onClick:O,type:"flipY"}),$=Q({icon:F,onClick:z,type:"flipX"}),ee=Q({icon:D,onClick:R,type:"rotateLeft"}),ne=Q({icon:X,onClick:M,type:"rotateRight"}),te=Q({icon:Z,onClick:N,type:"zoomOut",disabled:C<=x}),oe=Q({icon:H,onClick:k,type:"zoomIn",disabled:C===y}),re=r.createElement("div",{className:"".concat(i,"-operations")},K,$,ee,ne,te,oe);return r.createElement(w.Ay,{visible:n,motionName:t},(function(e){var n=e.className,t=e.style;return r.createElement(A.A,{open:!0,getContainer:null!==o&&void 0!==o?o:document.body},r.createElement("div",{className:c()("".concat(i,"-operations-wrapper"),n,a),style:(0,s.A)((0,s.A)({},t),{},{zIndex:j})},null===S?null:r.createElement("button",{className:"".concat(i,"-close"),onClick:E},S||B),d&&r.createElement(r.Fragment,null,r.createElement("div",{className:c()("".concat(i,"-switch-left"),(0,u.A)({},"".concat(i,"-switch-left-disabled"),0===v)),onClick:function(e){return _(e,-1)}},W),r.createElement("div",{className:c()("".concat(i,"-switch-right"),(0,u.A)({},"".concat(i,"-switch-right-disabled"),v===g-1)),onClick:function(e){return _(e,1)}},V)),r.createElement("div",{className:"".concat(i,"-footer")},m&&r.createElement("div",{className:"".concat(i,"-progress")},f?f(v+1,g):"".concat(v+1," / ").concat(g)),L?L(re,(0,s.A)((0,s.A)({icons:{prevIcon:J,nextIcon:q,flipYIcon:K,flipXIcon:$,rotateLeftIcon:ee,rotateRightIcon:ne,zoomOutIcon:te,zoomInIcon:oe},actions:{onActive:I,onFlipY:O,onFlipX:z,onRotateLeft:R,onRotateRight:M,onZoomOut:N,onZoomIn:k,onReset:T,onClose:E},transform:p},Y?{current:v,total:g}:{}),{},{image:P})):re)))}))},x=t(98121),y=t(88632),S={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};var I=t(17909);function E(e,n,t,o){var r=n+t,i=(t-o)/2;if(t>o){if(n>0)return(0,u.A)({},e,i);if(n<0&&r<o)return(0,u.A)({},e,-i)}else if(n<0||r>o)return(0,u.A)({},e,n<0?i:-i);return{}}function k(e,n,t,o){var r=m(),i=r.width,a=r.height,c=null;return e<=i&&n<=a?c={x:0,y:0}:(e>i||n>a)&&(c=(0,s.A)((0,s.A)({},E("x",t,e,i)),E("y",o,n,a))),c}function N(e){var n=e.src,t=e.isCustomPlaceholder,i=e.fallback,a=(0,r.useState)(t?"loading":"normal"),c=(0,o.A)(a,2),l=c[0],s=c[1],u=(0,r.useRef)(!1),f="error"===l;(0,r.useEffect)((function(){var e=!0;return function(e){return new Promise((function(n){var t=document.createElement("img");t.onerror=function(){return n(!1)},t.onload=function(){return n(!0)},t.src=e}))}(n).then((function(n){!n&&e&&s("error")})),function(){e=!1}}),[n]),(0,r.useEffect)((function(){t&&!u.current?s("loading"):f&&s("normal")}),[n]);var d=function(){s("normal")};return[function(e){u.current=!1,"loading"===l&&null!==e&&void 0!==e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(u.current=!0,d())},f&&i?{src:i}:{onLoad:d,src:n},l]}function M(e,n){var t=e.x-n.x,o=e.y-n.y;return Math.hypot(t,o)}function R(e,n,t,i,a,c,l){var u=a.rotate,f=a.scale,d=a.x,m=a.y,v=(0,r.useState)(!1),p=(0,o.A)(v,2),h=p[0],A=p[1],w=(0,r.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),b=function(e){w.current=(0,s.A)((0,s.A)({},w.current),e)};return(0,r.useEffect)((function(){var e;return t&&n&&(e=(0,g.A)(window,"touchmove",(function(e){return e.preventDefault()}),{passive:!1})),function(){var n;null===(n=e)||void 0===n||n.remove()}}),[t,n]),{isTouching:h,onTouchStart:function(e){if(n){e.stopPropagation(),A(!0);var t=e.touches,o=void 0===t?[]:t;o.length>1?b({point1:{x:o[0].clientX,y:o[0].clientY},point2:{x:o[1].clientX,y:o[1].clientY},eventType:"touchZoom"}):b({point1:{x:o[0].clientX-d,y:o[0].clientY-m},eventType:"move"})}},onTouchMove:function(e){var n=e.touches,t=void 0===n?[]:n,r=w.current,i=r.point1,a=r.point2,s=r.eventType;if(t.length>1&&"touchZoom"===s){var u={x:t[0].clientX,y:t[0].clientY},f={x:t[1].clientX,y:t[1].clientY},d=function(e,n,t,o){var r=M(e,t),i=M(n,o);if(0===r&&0===i)return[e.x,e.y];var a=r/(r+i);return[e.x+a*(n.x-e.x),e.y+a*(n.y-e.y)]}(i,a,u,f),m=(0,o.A)(d,2),v=m[0],p=m[1],g=M(u,f)/M(i,a);l(g,"touchZoom",v,p,!0),b({point1:u,point2:f,eventType:"touchZoom"})}else"move"===s&&(c({x:t[0].clientX-i.x,y:t[0].clientY-i.y},"move"),b({eventType:"move"}))},onTouchEnd:function(){if(t){if(h&&A(!1),b({eventType:"none"}),i>f)return c({x:0,y:0,scale:i},"touchZoom");var n=e.current.offsetWidth*f,o=e.current.offsetHeight*f,r=e.current.getBoundingClientRect(),a=r.left,l=r.top,d=u%180!==0,m=k(d?o:n,d?n:o,a,l);m&&c((0,s.A)({},m),"dragRebound")}}}}var z=["fallback","src","imgRef"],O=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],T=function(e){var n=e.fallback,t=e.src,i=e.imgRef,a=(0,d.A)(e,z),c=N({src:t,fallback:n}),s=(0,o.A)(c,2),u=s[0],f=s[1];return r.createElement("img",(0,l.A)({ref:function(e){i.current=e,u(e)}},a,f))},L=function(e){var n=e.prefixCls,t=e.src,i=e.alt,a=e.imageInfo,f=e.fallback,v=e.movable,A=void 0===v||v,w=e.onClose,E=e.visible,N=e.icons,M=void 0===N?{}:N,z=e.rootClassName,L=e.closeIcon,j=e.getContainer,P=e.current,Y=void 0===P?0:P,D=e.count,X=void 0===D?1:D,H=e.countRender,Z=e.scaleStep,B=void 0===Z?.5:Z,W=e.minScale,V=void 0===W?1:W,F=e.maxScale,G=void 0===F?50:F,U=e.transitionName,_=void 0===U?"zoom":U,Q=e.maskTransitionName,J=void 0===Q?"fade":Q,q=e.imageRender,K=e.imgCommonProps,$=e.toolbarRender,ee=e.onTransform,ne=e.onChange,te=(0,d.A)(e,O),oe=(0,r.useRef)(),re=(0,r.useContext)(b),ie=re&&X>1,ae=re&&X>=1,ce=(0,r.useState)(!0),le=(0,o.A)(ce,2),se=le[0],ue=le[1],fe=function(e,n,t,i){var a=(0,r.useRef)(null),c=(0,r.useRef)([]),l=(0,r.useState)(S),u=(0,o.A)(l,2),f=u[0],d=u[1],v=function(e,n){null===a.current&&(c.current=[],a.current=(0,y.A)((function(){d((function(e){var t=e;return c.current.forEach((function(e){t=(0,s.A)((0,s.A)({},t),e)})),a.current=null,null===i||void 0===i||i({transform:t,action:n}),t}))}))),c.current.push((0,s.A)((0,s.A)({},f),e))};return{transform:f,resetTransform:function(e){d(S),(0,x.A)(S,f)||null===i||void 0===i||i({transform:S,action:e})},updateTransform:v,dispatchZoomChange:function(o,r,i,a,c){var l=e.current,s=l.width,u=l.height,d=l.offsetWidth,p=l.offsetHeight,g=l.offsetLeft,h=l.offsetTop,A=o,w=f.scale*o;w>t?(w=t,A=t/f.scale):w<n&&(A=(w=c?w:n)/f.scale);var b=null!==i&&void 0!==i?i:innerWidth/2,C=null!==a&&void 0!==a?a:innerHeight/2,x=A-1,y=x*s*.5,S=x*u*.5,I=x*(b-f.x-g),E=x*(C-f.y-h),k=f.x-(I-y),N=f.y-(E-S);if(o<1&&1===w){var M=d*w,R=p*w,z=m(),O=z.width,T=z.height;M<=O&&R<=T&&(k=0,N=0)}v({x:k,y:N,scale:w},r)}}}(oe,V,G,ee),de=fe.transform,me=fe.resetTransform,ve=fe.updateTransform,pe=fe.dispatchZoomChange,ge=function(e,n,t,i,a,c,l){var u=a.rotate,f=a.scale,d=a.x,m=a.y,v=(0,r.useState)(!1),p=(0,o.A)(v,2),h=p[0],A=p[1],w=(0,r.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),b=function(e){t&&h&&c({x:e.pageX-w.current.diffX,y:e.pageY-w.current.diffY},"move")},C=function(){if(t&&h){A(!1);var n=w.current,o=n.transformX,r=n.transformY;if(d===o||m===r)return;var i=e.current.offsetWidth*f,a=e.current.offsetHeight*f,l=e.current.getBoundingClientRect(),v=l.left,p=l.top,g=u%180!==0,b=k(g?a:i,g?i:a,v,p);b&&c((0,s.A)({},b),"dragRebound")}};return(0,r.useEffect)((function(){var e,t,o,r;if(n){o=(0,g.A)(window,"mouseup",C,!1),r=(0,g.A)(window,"mousemove",b,!1);try{window.top!==window.self&&(e=(0,g.A)(window.top,"mouseup",C,!1),t=(0,g.A)(window.top,"mousemove",b,!1))}catch(i){(0,I.$e)(!1,"[rc-image] ".concat(i))}}return function(){var n,i,a,c;null===(n=o)||void 0===n||n.remove(),null===(i=r)||void 0===i||i.remove(),null===(a=e)||void 0===a||a.remove(),null===(c=t)||void 0===c||c.remove()}}),[t,h,d,m,u,n]),{isMoving:h,onMouseDown:function(e){n&&0===e.button&&(e.preventDefault(),e.stopPropagation(),w.current={diffX:e.pageX-d,diffY:e.pageY-m,transformX:d,transformY:m},A(!0))},onMouseMove:b,onMouseUp:C,onWheel:function(e){if(t&&0!=e.deltaY){var n=Math.abs(e.deltaY/100),o=1+Math.min(n,1)*i;e.deltaY>0&&(o=1/o),l(o,"wheel",e.clientX,e.clientY)}}}}(oe,A,E,B,de,ve,pe),he=ge.isMoving,Ae=ge.onMouseDown,we=ge.onWheel,be=R(oe,A,E,V,de,ve,pe),Ce=be.isTouching,xe=be.onTouchStart,ye=be.onTouchMove,Se=be.onTouchEnd,Ie=de.rotate,Ee=de.scale,ke=c()((0,u.A)({},"".concat(n,"-moving"),he));(0,r.useEffect)((function(){se||ue(!0)}),[se]);var Ne=function(e){var n=Y+e;!Number.isInteger(n)||n<0||n>X-1||(ue(!1),me(e<0?"prev":"next"),null===ne||void 0===ne||ne(n,Y))},Me=function(e){E&&ie&&(e.keyCode===h.A.LEFT?Ne(-1):e.keyCode===h.A.RIGHT&&Ne(1))};(0,r.useEffect)((function(){var e=(0,g.A)(window,"keydown",Me,!1);return function(){e.remove()}}),[E,ie,Y]);var Re=r.createElement(T,(0,l.A)({},K,{width:e.width,height:e.height,imgRef:oe,className:"".concat(n,"-img"),alt:i,style:{transform:"translate3d(".concat(de.x,"px, ").concat(de.y,"px, 0) scale3d(").concat(de.flipX?"-":"").concat(Ee,", ").concat(de.flipY?"-":"").concat(Ee,", 1) rotate(").concat(Ie,"deg)"),transitionDuration:(!se||Ce)&&"0s"},fallback:f,src:t,onWheel:we,onMouseDown:Ae,onDoubleClick:function(e){E&&(1!==Ee?ve({x:0,y:0,scale:1},"doubleClick"):pe(1+B,"doubleClick",e.clientX,e.clientY))},onTouchStart:xe,onTouchMove:ye,onTouchEnd:Se,onTouchCancel:Se})),ze=(0,s.A)({url:t,alt:i},a);return r.createElement(r.Fragment,null,r.createElement(p.A,(0,l.A)({transitionName:_,maskTransitionName:J,closable:!1,keyboard:!0,prefixCls:n,onClose:w,visible:E,classNames:{wrapper:ke},rootClassName:z,getContainer:j},te,{afterClose:function(){me("close")}}),r.createElement("div",{className:"".concat(n,"-img-wrapper")},q?q(Re,(0,s.A)({transform:de,image:ze},re?{current:Y}:{})):Re)),r.createElement(C,{visible:E,transform:de,maskTransitionName:J,closeIcon:L,getContainer:j,prefixCls:n,rootClassName:z,icons:M,countRender:H,showSwitch:ie,showProgress:ae,current:Y,count:X,scale:Ee,minScale:V,maxScale:G,toolbarRender:$,onActive:Ne,onZoomIn:function(){pe(1+B,"zoomIn")},onZoomOut:function(){pe(1/(1+B),"zoomOut")},onRotateRight:function(){ve({rotate:Ie+90},"rotateRight")},onRotateLeft:function(){ve({rotate:Ie-90},"rotateLeft")},onFlipX:function(){ve({flipX:!de.flipX},"flipX")},onFlipY:function(){ve({flipY:!de.flipY},"flipY")},onClose:w,onReset:function(){me("reset")},zIndex:void 0!==te.zIndex?te.zIndex+1:void 0,image:ze}))},j=t(45726),P=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"];var Y=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],D=["src"],X=function(e){var n,t=e.previewPrefixCls,i=void 0===t?"rc-image-preview":t,a=e.children,c=e.icons,m=void 0===c?{}:c,p=e.items,g=e.preview,h=e.fallback,A="object"===(0,f.A)(g)?g:{},w=A.visible,C=A.onVisibleChange,x=A.getContainer,y=A.current,S=A.movable,I=A.minScale,E=A.maxScale,k=A.countRender,N=A.closeIcon,M=A.onChange,R=A.onTransform,z=A.toolbarRender,O=A.imageRender,T=(0,d.A)(A,Y),X=function(e){var n=r.useState({}),t=(0,o.A)(n,2),i=t[0],a=t[1],c=r.useCallback((function(e,n){return a((function(t){return(0,s.A)((0,s.A)({},t),{},(0,u.A)({},e,n))})),function(){a((function(n){var t=(0,s.A)({},n);return delete t[e],t}))}}),[]);return[r.useMemo((function(){return e?e.map((function(e){if("string"===typeof e)return{data:{src:e}};var n={};return Object.keys(e).forEach((function(t){["src"].concat((0,j.A)(P)).includes(t)&&(n[t]=e[t])})),{data:n}})):Object.keys(i).reduce((function(e,n){var t=i[n],o=t.canPreview,r=t.data;return o&&e.push({data:r,id:n}),e}),[])}),[e,i]),c,!!e]}(p),H=(0,o.A)(X,3),Z=H[0],B=H[1],W=H[2],V=(0,v.A)(0,{value:y}),F=(0,o.A)(V,2),G=F[0],U=F[1],_=(0,r.useState)(!1),Q=(0,o.A)(_,2),J=Q[0],q=Q[1],K=(null===(n=Z[G])||void 0===n?void 0:n.data)||{},$=K.src,ee=(0,d.A)(K,D),ne=(0,v.A)(!!w,{value:w,onChange:function(e,n){null===C||void 0===C||C(e,n,G)}}),te=(0,o.A)(ne,2),oe=te[0],re=te[1],ie=(0,r.useState)(null),ae=(0,o.A)(ie,2),ce=ae[0],le=ae[1],se=r.useCallback((function(e,n,t,o){var r=W?Z.findIndex((function(e){return e.data.src===n})):Z.findIndex((function(n){return n.id===e}));U(r<0?0:r),re(!0),le({x:t,y:o}),q(!0)}),[Z,W]);r.useEffect((function(){oe?J||U(0):q(!1)}),[oe]);var ue=r.useMemo((function(){return{register:B,onPreview:se}}),[B,se]);return r.createElement(b.Provider,{value:ue},a,r.createElement(L,(0,l.A)({"aria-hidden":!oe,movable:S,visible:oe,prefixCls:i,closeIcon:N,onClose:function(){re(!1),le(null)},mousePosition:ce,imgCommonProps:ee,src:$,fallback:h,icons:m,minScale:I,maxScale:E,getContainer:x,current:G,count:Z.length,countRender:k,onTransform:R,toolbarRender:z,imageRender:O,onChange:function(e,n){U(e),null===M||void 0===M||M(e,n)}},T)))},H=0;var Z=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],B=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],W=function(e){var n=e.src,t=e.alt,i=e.onPreviewClose,a=e.prefixCls,m=void 0===a?"rc-image":a,p=e.previewPrefixCls,g=void 0===p?"".concat(m,"-preview"):p,h=e.placeholder,A=e.fallback,w=e.width,C=e.height,x=e.style,y=e.preview,S=void 0===y||y,I=e.className,E=e.onClick,k=e.onError,M=e.wrapperClassName,R=e.wrapperStyle,z=e.rootClassName,O=(0,d.A)(e,Z),T=h&&!0!==h,j="object"===(0,f.A)(S)?S:{},Y=j.src,D=j.visible,X=void 0===D?void 0:D,W=j.onVisibleChange,V=void 0===W?i:W,F=j.getContainer,G=void 0===F?void 0:F,U=j.mask,_=j.maskClassName,Q=j.movable,J=j.icons,q=j.scaleStep,K=j.minScale,$=j.maxScale,ee=j.imageRender,ne=j.toolbarRender,te=(0,d.A)(j,B),oe=null!==Y&&void 0!==Y?Y:n,re=(0,v.A)(!!X,{value:X,onChange:V}),ie=(0,o.A)(re,2),ae=ie[0],ce=ie[1],le=N({src:n,isCustomPlaceholder:T,fallback:A}),se=(0,o.A)(le,3),ue=se[0],fe=se[1],de=se[2],me=(0,r.useState)(null),ve=(0,o.A)(me,2),pe=ve[0],ge=ve[1],he=(0,r.useContext)(b),Ae=!!S,we=c()(m,M,z,(0,u.A)({},"".concat(m,"-error"),"error"===de)),be=(0,r.useMemo)((function(){var n={};return P.forEach((function(t){void 0!==e[t]&&(n[t]=e[t])})),n}),P.map((function(n){return e[n]}))),Ce=function(e,n){var t=r.useState((function(){return String(H+=1)})),i=(0,o.A)(t,1)[0],a=r.useContext(b),c={data:n,canPreview:e};return r.useEffect((function(){if(a)return a.register(i,c)}),[]),r.useEffect((function(){a&&a.register(i,c)}),[e,n]),i}(Ae,(0,r.useMemo)((function(){return(0,s.A)((0,s.A)({},be),{},{src:oe})}),[oe,be]));return r.createElement(r.Fragment,null,r.createElement("div",(0,l.A)({},O,{className:we,onClick:Ae?function(e){var n=function(e){var n=e.getBoundingClientRect(),t=document.documentElement;return{left:n.left+(window.pageXOffset||t.scrollLeft)-(t.clientLeft||document.body.clientLeft||0),top:n.top+(window.pageYOffset||t.scrollTop)-(t.clientTop||document.body.clientTop||0)}}(e.target),t=n.left,o=n.top;he?he.onPreview(Ce,oe,t,o):(ge({x:t,y:o}),ce(!0)),null===E||void 0===E||E(e)}:E,style:(0,s.A)({width:w,height:C},R)}),r.createElement("img",(0,l.A)({},be,{className:c()("".concat(m,"-img"),(0,u.A)({},"".concat(m,"-img-placeholder"),!0===h),I),style:(0,s.A)({height:C},x),ref:ue},fe,{width:w,height:C,onError:k})),"loading"===de&&r.createElement("div",{"aria-hidden":"true",className:"".concat(m,"-placeholder")},h),U&&Ae&&r.createElement("div",{className:c()("".concat(m,"-mask"),_),style:{display:"none"===(null===x||void 0===x?void 0:x.display)?"none":void 0}},U)),!he&&Ae&&r.createElement(L,(0,l.A)({"aria-hidden":!ae,visible:ae,prefixCls:g,onClose:function(){ce(!1),ge(null)},mousePosition:pe,src:oe,alt:t,imageInfo:{width:w,height:C},fallback:A,getContainer:G,icons:J,movable:Q,scaleStep:q,minScale:K,maxScale:$,rootClassName:z,imageRender:ee,imgCommonProps:be,toolbarRender:ne},te)))};W.PreviewGroup=X;var V=W,F=t(41539),G=t(55643),U=t(45847),_=t(72790),Q=t(48739),J=t(51551),q=t(96471),K=t(41610),$={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},ee=t(93659),ne=function(e,n){return r.createElement(ee.A,(0,l.A)({},e,{ref:n,icon:$}))};var te=r.forwardRef(ne),oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},re=function(e,n){return r.createElement(ee.A,(0,l.A)({},e,{ref:n,icon:oe}))};var ie=r.forwardRef(re),ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},ce=function(e,n){return r.createElement(ee.A,(0,l.A)({},e,{ref:n,icon:ae}))};var le=r.forwardRef(ce),se=t(13695),ue=t(86465),fe=t(54942),de=t(37471),me=t(96583),ve=t(3425),pe=t(70421),ge=t(94296),he=t(80894),Ae=t(47749),we=function(e){return{position:e||"absolute",inset:0}},be=function(e){var n=e.previewCls,t=e.modalMaskBg,o=e.paddingSM,r=e.marginXL,i=e.margin,a=e.paddingLG,c=e.previewOperationColorDisabled,l=e.previewOperationHoverColor,s=e.motionDurationSlow,f=e.iconCls,d=e.colorTextLightSolid,m=new de.Y(t).setA(.1),v=m.clone().setA(.2);return(0,u.A)((0,u.A)((0,u.A)((0,u.A)({},"".concat(n,"-footer"),{position:"fixed",bottom:r,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"}),"".concat(n,"-progress"),{marginBottom:i}),"".concat(n,"-close"),(0,u.A)({position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:d,backgroundColor:m.toRgbString(),borderRadius:"50%",padding:o,outline:0,border:0,cursor:"pointer",transition:"all ".concat(s),"&:hover":{backgroundColor:v.toRgbString()}},"& > ".concat(f),{fontSize:e.previewOperationSize})),"".concat(n,"-operations"),{display:"flex",alignItems:"center",padding:"0 ".concat((0,fe.zA)(a)),backgroundColor:m.toRgbString(),borderRadius:100,"&-operation":(0,u.A)((0,u.A)((0,u.A)((0,u.A)({marginInlineStart:o,padding:o,cursor:"pointer",transition:"all ".concat(s),userSelect:"none"},"&:not(".concat(n,"-operations-operation-disabled):hover > ").concat(f),{color:l}),"&-disabled",{color:c,cursor:"not-allowed"}),"&:first-of-type",{marginInlineStart:0}),"& > ".concat(f),{fontSize:e.previewOperationSize})})},Ce=function(e){var n=e.modalMaskBg,t=e.iconCls,o=e.previewOperationColorDisabled,r=e.previewCls,i=e.zIndexPopup,a=e.motionDurationSlow,c=new de.Y(n).setA(.1),l=c.clone().setA(.2);return(0,u.A)((0,u.A)((0,u.A)({},"".concat(r,"-switch-left, ").concat(r,"-switch-right"),(0,u.A)({position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:c.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:"all ".concat(a),userSelect:"none","&:hover":{background:l.toRgbString()},"&-disabled":{"&, &:hover":(0,u.A)({color:o,background:"transparent",cursor:"not-allowed"},"> ".concat(t),{cursor:"not-allowed"})}},"> ".concat(t),{fontSize:e.previewOperationSize})),"".concat(r,"-switch-left"),{insetInlineStart:e.marginSM}),"".concat(r,"-switch-right"),{insetInlineEnd:e.marginSM})},xe=function(e){var n=e.motionEaseOut,t=e.previewCls,o=e.motionDurationSlow,r=e.componentCls;return[(0,u.A)({},"".concat(r,"-preview-root"),(0,u.A)((0,u.A)((0,u.A)((0,u.A)({},t,{height:"100%",textAlign:"center",pointerEvents:"none"}),"".concat(t,"-body"),Object.assign(Object.assign({},we()),{overflow:"hidden"})),"".concat(t,"-img"),{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:"transform ".concat(o," ").concat(n," 0s"),userSelect:"none","&-wrapper":Object.assign(Object.assign({},we()),{transition:"transform ".concat(o," ").concat(n," 0s"),display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})}),"".concat(t,"-moving"),(0,u.A)({},"".concat(t,"-preview-img"),{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}))),(0,u.A)({},"".concat(r,"-preview-root"),(0,u.A)({},"".concat(t,"-wrap"),{zIndex:e.zIndexPopup})),(0,u.A)((0,u.A)({},"".concat(r,"-preview-operations-wrapper"),{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()}),"&",[be(e),Ce(e)])]},ye=function(e){var n=e.componentCls;return(0,u.A)({},n,(0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)({position:"relative",display:"inline-block"},"".concat(n,"-img"),{width:"100%",height:"auto",verticalAlign:"middle"}),"".concat(n,"-img-placeholder"),{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"}),"".concat(n,"-mask"),Object.assign({},function(e){var n=e.iconCls,t=e.motionDurationSlow,o=e.paddingXXS,r=e.marginXXS,i=e.prefixCls,a=e.colorTextLightSolid;return(0,u.A)({position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:a,background:new de.Y("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:"opacity ".concat(t)},".".concat(i,"-mask-info"),Object.assign(Object.assign({},ve.L9),(0,u.A)({padding:"0 ".concat((0,fe.zA)(o))},n,{marginInlineEnd:r,svg:{verticalAlign:"baseline"}})))}(e))),"".concat(n,"-mask:hover"),{opacity:1}),"".concat(n,"-placeholder"),Object.assign({},we())))},Se=function(e){var n=e.previewCls;return(0,u.A)((0,u.A)({},"".concat(n,"-root"),(0,pe.aB)(e,"zoom")),"&",(0,ge.p9)(e,!0))},Ie=(0,he.OF)("Image",(function(e){var n="".concat(e.componentCls,"-preview"),t=(0,Ae.oX)(e,{previewCls:n,modalMaskBg:new de.Y("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[ye(t),xe(t),(0,me.Dk)((0,Ae.oX)(t,{componentCls:n})),Se(t)]}),(function(e){return{zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new de.Y(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new de.Y(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new de.Y(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon}})),Ee=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]])}return t},ke={rotateLeft:r.createElement(te,null),rotateRight:r.createElement(ie,null),zoomIn:r.createElement(se.A,null),zoomOut:r.createElement(ue.A,null),close:r.createElement(J.A,null),left:r.createElement(q.A,null),right:r.createElement(K.A,null),flipX:r.createElement(le,null),flipY:r.createElement(le,{rotate:90})},Ne=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]])}return t},Me=function(e){var n=e.prefixCls,t=e.preview,a=e.className,l=e.rootClassName,s=e.style,u=Ne(e,["prefixCls","preview","className","rootClassName","style"]),f=(0,U.TP)("image"),d=f.getPrefixCls,m=f.getPopupContainer,v=f.className,p=f.style,g=f.preview,h=(0,Q.A)("Image"),A=(0,o.A)(h,1)[0],w=d("image",n),b=d(),C=(0,_.A)(w),x=Ie(w,C),y=(0,o.A)(x,3),S=y[0],I=y[1],E=y[2],k=c()(l,I,E,C),N=c()(a,I,v),M=(0,F.YK)("ImagePreview","object"===typeof t?t.zIndex:void 0),R=(0,o.A)(M,1)[0],z=r.useMemo((function(){if(!1===t)return t;var e="object"===typeof t?t:{},n=e.getContainer,o=e.closeIcon,a=e.rootClassName,l=Ne(e,["getContainer","closeIcon","rootClassName"]);return Object.assign(Object.assign({mask:r.createElement("div",{className:"".concat(w,"-mask-info")},r.createElement(i.A,null),null===A||void 0===A?void 0:A.preview),icons:ke},l),{rootClassName:c()(k,a),getContainer:null!==n&&void 0!==n?n:m,transitionName:(0,G.b)(b,"zoom",e.transitionName),maskTransitionName:(0,G.b)(b,"fade",e.maskTransitionName),zIndex:R,closeIcon:null!==o&&void 0!==o?o:null===g||void 0===g?void 0:g.closeIcon})}),[t,A,null===g||void 0===g?void 0:g.closeIcon]),O=Object.assign(Object.assign({},p),s);return S(r.createElement(V,Object.assign({prefixCls:w,preview:z,rootClassName:k,className:N,style:O},u)))};Me.PreviewGroup=function(e){var n=e.previewPrefixCls,t=e.preview,i=Ee(e,["previewPrefixCls","preview"]),a=r.useContext(U.QO).getPrefixCls,l=a("image",n),s="".concat(l,"-preview"),u=a(),f=(0,_.A)(l),d=Ie(l,f),m=(0,o.A)(d,3),v=m[0],p=m[1],g=m[2],h=(0,F.YK)("ImagePreview","object"===typeof t?t.zIndex:void 0),A=(0,o.A)(h,1)[0],w=r.useMemo((function(){var e;if(!1===t)return t;var n="object"===typeof t?t:{},o=c()(p,g,f,null!==(e=n.rootClassName)&&void 0!==e?e:"");return Object.assign(Object.assign({},n),{transitionName:(0,G.b)(u,"zoom",n.transitionName),maskTransitionName:(0,G.b)(u,"fade",n.maskTransitionName),rootClassName:o,zIndex:A})}),[t]);return v(r.createElement(V.PreviewGroup,Object.assign({preview:w,previewPrefixCls:s,icons:ke},i)))};var Re=Me}}]);
//# sourceMappingURL=49.edb60a69.chunk.js.map