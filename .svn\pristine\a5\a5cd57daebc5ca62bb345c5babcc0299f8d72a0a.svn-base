/**
 * @Author: xujing
 * @Date: 2025/7/23
 * @Description: ""
 */
import { useEffect, useRef, useState, MouseEvent, } from 'react';
import { Table, Tag, Tabs, Upload, Modal, Button, Input, message, DatePicker, Select, Empty, Dropdown } from 'antd';
import { CloudUploadOutlined, RightOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getWorkflowList, getWorkflowTemplateList, getTemplateGroupList, createWorkflowByFiles } from '@/api/task'
import { useMemoizedFn } from 'ahooks'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import './../integralDetail/index.css'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { getTeamMembers } from '@/api/task'
import { getUserInfo } from '@/api/common'
const { Option } = Select;

export const Workflow = () => {
    const navigate = useNavigate()

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()

    const [userInfo] = useAtomMethod(userinfoService.userInfo)
	const [userId, setUserId] = useState<string>('');
	const [selectedTeam, setSelectedTeam] = useState<Array <any> | null>([]);

    const allNodeList = [
        {
            taskType: 8,
            label: '文生图'
        },
        {
            taskType: 9,
            label: '相似图裂变'
        },
        {
            taskType: 6,
            label: '平铺图-文生图'
        },
        {
            taskType: 7,
            label: '平铺图-图生图'
        },
        {
            taskType: 14,
            label: '图案裁剪'
        },
        {
            taskType: 11,
            label: '图片去背景'
        },
        {
            taskType: 12,
            label: '图片变清晰'
        },
        {
            taskType: 52,
            label: '印花图提取'
        },
        {
            taskType: 17,
            label: '侵权风险过滤'
        },
        {
            taskType: 18,
            label: '标题提取'
        },
    ]

    const [workflowName, setWorkflowName] = useState('') // 批次号
    const [dateRange, setDateRange] = useState<any>(null) // 日期范围
    const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

    const [taskStatus, setTaskStatus] = useState('') // 任务状态
    const handleTaskStatusChange = (value: string) => {
        console.log(`selected ${value}`);
        setTaskStatus(value)
    };

    const [isModalVisible, setIsModalVisible] = useState(false)
    const [fileList, setFileList] = useState<UploadFile[]>([])

    // 表格数据
    const [dataSource, setDataSource] = useState([])
    // 表格loading
    const [tableLoading, setTableLoading] = useState(false)
    // 分页配置
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    })
	//获取团队id
	useEffect(() => { 
		getUserInfo().then(res => { 

			getTeamMembers({ teamId: res.teamId }).then((res:any) => { 
				
				setSelectedTeam(res.data)
			});
		});
	}, []);
    //获取工作流批次列表
    const fetchBatchListData = async (
        pageNum: number,
        pageSize: number,
        workflowName: string,
        status: string,
        userId: string,
        // startTime: string,
        // endTime: string
    ) => {
        setDataSource([])
        setTableLoading(true)
        try {
            const response: any = await getWorkflowList({
                pageNum,
                pageSize,
                workflowName,
                status,
                userId
                // startTime,
                // endTime
            })
            if (response.data) {
                console.log(response.data, '获取工作流批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
                setIsFirstLoad(false)
            }
        } catch (error) {
            console.error('获取数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }



    const [workflowTemplateList, setWorkflowTemplateList] = useState<any[]>([])
    //获取工作流模板列表
    const fetchWorkflowTemplateList = (
        pageNum: number,
        pageSize: number,
        groupId: any,
    ) => {
        getWorkflowTemplateList({
            pageNum,
            pageSize,
            groupId: groupId == '0' ? '' : groupId,
        }).then((res: any) => {
            if (res.data) {
                setWorkflowTemplateList(res.data)
            }
        }).catch(err => {
            messageApi.error(`获取工作流模板列表失败:${err?.msg || err?.data?.msg || ''}`)
        })
    }
    const [groups, setGroups] = useState<any[]>([]);

    //获取分组列表
    const fetchGroupListData = (groupName: string) => {
        getTemplateGroupList({ pageNum: 1, pageSize: 99, groupName }).then((res: any) => {
            setGroups([{ id: 0, groupName: "全部" }, ...res.data]);
        }).catch(err => {
            messageApi.error(`获取分组列表失败:${err?.msg || err?.data?.msg || ''}`)
        })
    }

    useEffect(() => {
        if (userInfo?.currentMode == 2) {
            fetchGroupListData('')
            handleSearch(1, 10)
        }
    }, [userInfo?.currentMode]);

    const [activeTab, setActiveTab] = useState({ id: 0, groupName: "全部" });

    // 查询条件存储key
    const QUERY_PARAMS_KEY = 'workflow_query_params';

    //是否首次加载
    const [isFirstLoad, setIsFirstLoad] = useState(true)
    // Refs 声明（必须放在组件顶层）
    const timerRef = useRef<NodeJS.Timeout>()
    const fetchRef = useRef<typeof refreshBatchListData>()
    const paginationRef = useRef(pagination)
    const isFirstLoadRef = useRef(isFirstLoad)
    // 同步最新状态到 ref
    useEffect(() => {
        paginationRef.current = pagination
        isFirstLoadRef.current = isFirstLoad
    })
    useEffect(() => {
        // 更新函数引用
        fetchRef.current = refreshBatchListData
        const tick = () => {
            if (!isFirstLoadRef.current) {
                console.log('定时刷新')
                fetchRef.current?.(paginationRef.current.current, 10)
            }
        }

        // 清除旧定时器
        if (timerRef.current) clearInterval(timerRef.current)
        // 启动新定时器
        timerRef.current = setInterval(tick, 30000)
        // 清理函数
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current)
            }
        }
    }, [isFirstLoad]) // 依赖项
    //刷新工作流批次列表
    const refreshBatchListData = async (
        pageNum: number,
        pageSize: number
    ) => {
        try {
            const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
            let queryParams = {
                workflowName: '',
                status: '',
                // stringDateRange: [] as string[]
            };

            if (savedParams) {
                queryParams = JSON.parse(savedParams);
            }

            // let startTime = '';
            // let endTime = '';
            // if (queryParams.stringDateRange.length > 0) {
            //     [startTime, endTime] = queryParams.stringDateRange;
            //     startTime = startTime ? `${startTime} 00:00:00` : '';
            //     endTime = endTime ? `${endTime} 23:59:59` : '';
            // }
            const response: any = await getWorkflowList({
                pageNum,
                pageSize,
                workflowName: queryParams.workflowName,
                status: queryParams.status,
                // startTime,
                // endTime
            })
            if (response.data) {
                console.log(response, '刷新图片任务批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
            }
        } catch (error) {
            console.error('刷新数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }
    // 查询处理函数
    const handleSearch = (pageNum: number, pageSize: number) => {
        // 存储查询条件到sessionStorage
        const queryParams = {
            workflowName,
            status: taskStatus,
            userId: userId,
            // stringDateRange
        };
        sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
        // if (stringDateRange.length > 0) {
        //     let [startTime, endTime] = stringDateRange
        //     startTime = startTime ? `${startTime} 00:00:00` : ''
        //     endTime = endTime ? `${endTime} 23:59:59` : ''
        //     fetchBatchListData(pageNum, pageSize, workflowName, startTime, endTime)
        // } else {
        //     fetchBatchListData(pageNum, pageSize, workflowName, '', '')
        // }
        fetchBatchListData(pageNum, pageSize, workflowName, taskStatus,userId)
    }

    // 表格列配置
    const columns = [
        {
            title: '任务批次',
            dataIndex: 'workflowName',
            key: 'workflowName',
            width: 180,
        },
        {
            title: '任务进度',
            dataIndex: 'nodeExecutionList',
            key: 'nodeExecutionList',
            render: (totalAmount: number, record: any) => (    // 执行状态：0-待执行  1-执行中  2-已完成  3-执行失败
                <div className="flex flex-wrap gap-1">
                    {record?.nodeExecutionList.map((item: any, inx: number) => (
                        <div key={inx} className="flex items-center">
                            <span className={`border  pl-2 pr-2 rounded-[4px]  ${item.status != 2 ? 'text-[#999] cursor-not-allowed border-[#999]' : 'text-primary cursor-pointer border-primary'}`}
                                onClick={() => { if (item.status === 2) goDetail(item, record); }}>
                                {item?.taskType ? allNodeList.find(n => n.taskType === item.taskType)?.label : '未知'}
                            </span>
                            {inx < record?.nodeExecutionList?.length - 1 && (<RightOutlined className='text-[9px] text-gray-500 ml-1' />)}
                        </div>
                    ))}
                </div>
            )
        },

        {
            title: '任务状态',
            dataIndex: 'status',
            key: 'status',
            width: 180,
            render: (status: string, record: any) => {
                let color = ''
                if (status == '0') {
                    color = 'orange'
                } else if (status == '1') {
                    color = 'blue'
                } else if (status == '2') {
                    color = 'green'
                } else if (status == '3') {
                    color = 'red'
                } else {
                    color = 'default'
                }
                return ( // 执行状态：0-待执行  1-执行中  2-已完成  3-执行失败 4-已取消'，
                    <Tag color={color}>
                        {status == '0' ? '待执行'
                            : status == '1' ? '执行中'
                                : status == '2' ? '已完成'
                                    : status == '3' ? '执行失败'
                                        : status == '4' ? '已取消'
                                            : ''}
                    </Tag>
                )
            }
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 220,
            render: (createTime: string) => (
                <p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 180,
            render: (_: any, record: any) => (
                <div>
                    {(record.status == 2) && (
                        <Button
                            type="link"
                            disabled={record.successAmount == 0}
                            style={{
                                color: record.successAmount == 0 ? '#bfbfbf' : '#32649f'
                            }}
                            onClick={() => goDetail(record.nodeExecutionList[record.nodeExecutionList.length - 1], record)}
                            size="small"
                        >
                            查看详情
                        </Button>
                    )}
                </div>
            )
        }
    ]

    // 查看详情
    const goDetail = (item: any, record: any) => {
        const typeMap: Record<number, string> = {
            8: 'textToImg',    // 文生图
            9: 'similar',      // 相似图裂变
            6: 'continuous',     // 平铺图-文生图
            7: 'continuous',      // 平铺图-图生图
            14: 'cut',         // 图案裁剪
            11: 'blockingOut',     // 图片去背景
            12: 'clear',      // 图片变清晰
            52: 'extract',      // 印花图提取
            17: 'filter',       // 侵权风险过滤
            18: 'titleExtraction'       // 标题提取
        };

        const detailType = typeMap[item.taskType] || 'unknown';

        navigate(`/workspace/teamTools/result/${detailType}?batchId=${item.batchId}`, {
            state: {
                batchId: item.batchId,
                nodeExecutionList: record?.nodeExecutionList,
                taskType: item.taskType,
                id: item.id
            },
            replace: true
        });
    }
    const showCreateWorkflow = () => {
        setIsModalVisible(true)
        setFileList([]) // 清空图片列表
        setCurrentTemplate({})
        setActiveTab(groups[0])
        fetchWorkflowTemplateList(1, 99, '')
    }
    const [creatLoading, setCreatLoading] = useState(false)

    // 创建任务
    const handleOk = async () => {
        try {
            if (fileList.length === 0) {
                messageApi.error('请先上传图片')
                return
            }
            if (!currentTemplate?.id) {
                messageApi.error('请先选择模板')
                return
            }
            setCreatLoading(true)
            // const files = fileList.map((file) => file.originFileObj as File)
            const files = fileList.map((file) => file.originFileObj)

            createWorkflowByFiles({ files, templateId: currentTemplate?.id }).then((data) => {
                messageApi.success('工作流创建成功')
                setIsModalVisible(false)
                setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
                handleSearch(1, 10) // 刷新批次列表数据
                userinfoService.refresh() // 刷新用户积分
            }).catch((err) => {
                messageApi.error(`创建失败: ${err?.msg || err?.data?.msg || ''}`)
            }).finally(() => {
                setCreatLoading(false)
            })
        } catch (err) {
            messageApi.error('创建失败')
            setCreatLoading(false)
        }
    }

    const handleCancel = () => {
        setIsModalVisible(false)
    }

    let prevFileList: any[] = []
    // 图片上传
    const handleUploadChange = ({
        fileList: newFileList
    }: {
        fileList: any[]
    }) => {
        // 比较新旧文件列表，如果相同则不处理
        if (
            newFileList.length === prevFileList.length &&
            newFileList.every((file, index) => file.uid === prevFileList[index].uid)
        ) {
            return
        }

        prevFileList = newFileList
        const validFiles: UploadFile[] = []

        const promises = newFileList.map((file) => {
            // 支持通过扩展名判断文件类型
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            const fileName = file.name || '';
            const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
            const isImage = /^image\//.test(file.type || '') || imageExtensions.includes(fileExtension);

            if (!isImage) {
                messageApi.error('请检查文件类型')
                return Promise.resolve(false)
            }

            return new Promise<boolean>((resolve) => {
                const reader = new FileReader()
                const originFileObj = file.originFileObj
                if (!originFileObj) {
                    messageApi.error('无法获取文件对象,请检查文件')
                    resolve(false)
                    return
                }
                reader.readAsArrayBuffer(originFileObj)
                reader.onload = async () => {
                    try {
                        const blob = new Blob([reader.result as ArrayBuffer])
                        const img = await createImageBitmap(blob)
                        const { width, height } = img
                        const isValidSize = width <= 4096 && height <= 4096
                        if (!isValidSize) {
                            messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过')
                            resolve(false)
                        } else {
                            resolve(true)
                        }
                    } catch (error) {
                        messageApi.error('图片加载失败，请检查图片格式')
                        resolve(false)
                    }
                }
                reader.onerror = () => {
                    messageApi.error('读取文件失败，请检查文件格式')
                    resolve(false)
                }
            })
        })

        Promise.all(promises).then((results) => {
            newFileList.forEach((file, index) => {
                if (results[index]) {
                    validFiles.push(file)
                }
            })

            const totalFiles = fileList.length + validFiles.length
            if (totalFiles > 50) {
                messageApi.error('最多只能上传50张图片')
                return
            }

            if (validFiles.length > 0) {
                messageApi.success(`成功上传 ${validFiles.length} 张图片`);
                setFileList((prev) => [...prev, ...validFiles])
            }
        })
    }

    // 在组件中添加分页变化处理函数
    const handleTableChange = (pagination: any) => {
        setPagination({
            ...pagination,
            current: pagination.current,
            pageSize: pagination.pageSize
        })
        handleSearch(pagination.current, pagination.pageSize)
    }


    const [materialVisible, setMaterialVisible] = useState(false);
    const [selectedImages, setSelectedImages] = useState<any[]>([]);
    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.originalUrl}
                        oriImgUrl={image.originalUrl}
                        smallImgUrl={image.thumbnailUrl || image.originalUrl}
                        markImgUrl={image.originalUrl}
                        progress={image.progress}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    const handleSelect = (task: any) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(task)
                ? prev.filter(item => item !== task)
                : [...prev, task];
            return newSelected;
        });
    };

    const selectMaterialOk = () => {
        if (selectedImages.length === 0) {
            messageApi.error('请选择图片')
            return
        }
    }
    const tabItems = [
        { id: '1', label: '按素材选取' },
        { id: '2', label: '按任务批次选取' }
    ];
    const [materialActiveTab, setMaterialActiveTab] = useState('1');

    const goCreat = () => {
        setIsModalVisible(false)
        sessionStorage.setItem('workflowParams', JSON.stringify({ from: 'workflow' }));
        window.location.href = '/workspace/teamTools/workflowTemplate'
    }

    const [currentTemplate, setCurrentTemplate] = useState<any>({});

    // 手动触发文件选择对话框
    const handleManualUpload = (isFolder: boolean) => {
        const inputNode = document.createElement('input');
        inputNode.type = 'file';
        inputNode.multiple = true;
        inputNode.style.display = 'none';

        if (isFolder) {
            inputNode.setAttribute('webkitdirectory', '');
            inputNode.setAttribute('directory', '');
        }
        inputNode.addEventListener('change', (e) => {
            const files = Array.from((e.target as HTMLInputElement).files || []);
            handleUploadChange({
                fileList: files.map(file => ({
                    uid: Math.random().toString(36).substr(2, 9),
                    name: file.name,
                    status: 'done',
                    originFileObj: file
                }))
            });
        });

        document.body.appendChild(inputNode);
        inputNode.click();
        document.body.removeChild(inputNode);
    };


    return (
        <div className="h-full w-full p-[20px]">
            {contextHolder} {/* 这里确保 Modal 挂载 */}
            {messageContextHolder} {/* 这里确保 Message 挂载 */}
            {/* 新建工作流任务弹窗 start */}
            <Modal
                title="新建工作流"
                open={isModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                width={600}
                centered
                okText="添加"
                confirmLoading={creatLoading}
            >
                <p className='text-gray-500 font-bold mt-5'>添加素材</p>
                <Upload.Dragger
                    multiple
                    accept="image/*"
                    fileList={[]} // 设置为空数组隐藏自带列表
                    onChange={handleUploadChange}
                    beforeUpload={() => false} // 阻止自动上传
                    className="upload-area"
                    listType="picture"
                    showUploadList={false} // 完全隐藏上传列表
                    disabled={fileList.length >= 50} // 添加禁用状态
                    style={{ marginTop: '30px' }}
                    directory={true} // true开启上传文件夹
                    openFileDialogOnClick={false} // 禁用默认点击上传

                >
                    <p className="ant-upload-drag-icon">
                        {/* <CloudUploadOutlined /> */}
                        <Dropdown
                            menu={{
                                items: [
                                    { key: 'file', label: '选择文件', onClick: () => handleManualUpload(false) },
                                    { key: 'folder', label: '选择文件夹', onClick: () => handleManualUpload(true) },
                                ]
                            }}
                            placement="bottomLeft"
                            trigger={['hover']}
                            disabled={fileList.length >= 50}
                        >
                            <Button onClick={(e) => e.stopPropagation()} disabled={fileList.length >= 50}>上传图片</Button>
                        </Dropdown>
                    </p>

                    <p className="ant-upload-text">拖拽文件到此处上传</p>
                    <p className="ant-upload-hint">
                        最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
                    </p>
                </Upload.Dragger>
                {/* <div className='flex items-center  justify-between mt-2 '>
                        <Button size='large' style={{ width: '266px', color: '#32649f', borderColor: '#32649f' }}>上传文件夹</Button>
                        <Button size='large' style={{ width: '266px', color: '#32649f', borderColor: '#32649f' }}
                            onClick={() => {
                                setMaterialVisible(true)
                                setSelectedImages([])
                            }}
                        >从我的素材选取</Button>
                    </div> */}
                <div className='text-right mt-2'>
                    已选择图片张数:{' '}
                    <span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
                        {fileList.length}
                    </span>{' '}
                    / 50
                </div>

                <div className='flex justify-between items-center  mt-5 mb-6'>
                    <p className='text-gray-500 font-bold'>选择流程</p>
                    <p className='text-[#F06A34] cursor-pointer' onClick={goCreat}>去创建工作流</p>
                </div>
                <Tabs
                    activeKey={activeTab?.id?.toString()}
                    onChange={(key: any) => {
                        const selectedGroup = groups.find(g => g.id.toString() === key);
                        setActiveTab(selectedGroup);
                        // 根据选中的分组ID查询数据
                        fetchWorkflowTemplateList(1, 99, key);
                    }}
                    items={groups.map((group, index) => ({
                        label: group?.groupName || '',
                        key: group?.id?.toString(),
                        children: (
                            <>
                                {workflowTemplateList.length > 0 ? (
                                    <div className='bg-[#f5f5f5] p-2 flex flex-wrap gap-2 h-[320px] overflow-y-scroll scrollbar-container scrollbar-hide '>
                                        {workflowTemplateList.map((item, index) => (
                                            <div key={index} className={`bg-white w-full  p-4 rounded-lg border  ${currentTemplate?.id && currentTemplate?.id == item?.id ? 'border-[#F06A34]' : 'border-white'}`}
                                                style={{ height: 'fit-content' }}
                                                onClick={() => setCurrentTemplate(item)}>
                                                <p className='text-gray-500 '>{item?.templateName}</p>
                                                <div className="flex flex-wrap gap-1 min-h-[64px] pt-1">
                                                    {item?.nodeList.map((node: any, inx: number) => (
                                                        <div key={inx} className="flex items-center">
                                                            <span className='border border-primary text-primary pl-2 pr-2 rounded-[4px]'>
                                                                {node?.taskType ? allNodeList.find(n => n.taskType === node.taskType)?.label : '未知'}
                                                            </span>
                                                            {inx < item?.nodeList.length - 1 && (<RightOutlined className='text-[9px] text-gray-500 ml-1' />)}
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>))}
                                    </div>) : <div className='h-[320px] flex justify-center items-center'>
                                    <Empty description="暂无模板，快去新增吧～" />
                                </div>}
                            </>
                        ),
                    }))}
                />
            </Modal>
            {/* 新建工作流任务弹窗 end */}
            {/* 我的素材选取弹窗 start */}
            {/* <Modal
                open={materialVisible}
                onOk={selectMaterialOk}
                onCancel={() => setMaterialVisible(false)}
                width={900}
                centered
                okText="选取"
            >
                <p className='mb-2'>
                    {tabItems.map((item) => (
                        <span
                            className={`mr-4 inline-block pb-1 cursor-pointer ${materialActiveTab === item.id ? 'text-primary border-b-2 border-primary' : ''
                                }`}
                            key={item.id} onClick={() => setMaterialActiveTab(item.id)}
                        >
                            {item.label}
                        </span>
                    ))}
                </p>
                <div>
                    <Checkbox
                        checked={selectedImages.length == record?.tasks.length}
                        onChange={(e) => {
                            if (e.target.checked) {
                                setSelectedImages([...record?.tasks]);
                            } else {
                                setSelectedImages([]);
                            }
                        }}
                    >
                        全选
                    </Checkbox>
                </div>
                <div className=' w-full  mt-[10px] h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="grid  grid-cols-8 gap-0">
                        {record?.tasks.length > 0 && record?.tasks.map((task: any, index: number) => (
                            <div className='aspect-square p-1'>
                                <div key={index} className="w-full h-full flex  rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden">
                                    <Checkbox
                                        className="absolute top-2 left-2 z-10"
                                        checked={selectedImages.includes(task)}
                                        onChange={() => handleSelect(task)}
                                    />
                                    {getTaskImageComponent(task)}
                                </div>
                            </div>
                        ))}
                    </div>

                </div>
            </Modal> */}
            {/* 我的素材选取弹窗弹窗 end */}
            <div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
                <Button type="primary" onClick={showCreateWorkflow}>
                    新建工作流
                </Button>
                <div className="flex items-center gap-2 ]">
                    <Input
                        style={{ width: '150px' }}
                        value={workflowName}
                        onChange={(e) => setWorkflowName(e.target.value)}
                        placeholder="输入批次号"
                    />
                    {/* <DatePicker.RangePicker
                        style={{ width: '250px' }}
                        value={dateRange}
                        onChange={(dates, dateStrings) => {
                            // dates 是 Moment 对象数组，包含完整时间信息
                            // dateStrings 是格式化后的字符串数组
                            console.log('日期范围:', dates, dateStrings)
                            setDateRange(dates)
                            setStringDateRange(dateStrings)
                        }}
                        format="YYYY-MM-DD"
                        disabledDate={(current) => {
                            return current && current > dayjs().endOf('day')
                        }}
                    /> */}
                    <Select
                        defaultValue=""
                        style={{ width: 120 }}
                        onChange={handleTaskStatusChange}
                        value={taskStatus}
                        options={[
                            { value: '', label: '全部状态' },
                            { value: '0', label: '待执行' },
                            { value: '1', label: '执行中' },
                            { value: '2', label: '已完成' },
                            { value: '3', label: '执行失败' },
                            { value: '4', label: '已取消' },
                        ]}
                    />
                    
                    <Select
                        placeholder="请选择要查看的账号"
                        style={{ width: 200 }}
                        value={userId || undefined}
                        onChange={(value) => setUserId(value)}
                        allowClear
                        showSearch
                    >
                        {selectedTeam && selectedTeam.map((item: any) => (
                            <Option key={item.phonenumber} value={item.userId}>
                                {item.nickname===""?item.phonenumber:item.nickname}
                            </Option>
                        ))}
                    </Select>
                    <Button
                        type="primary"
                        onClick={() => {
                            setPagination(prev => ({ ...prev, current: 1 }));
                            handleSearch(1, 10)
                        }}
                        loading={false}
                    >
                        查询
                    </Button>
                </div>
            </div >
            <div className="w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide">
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    className="mt-4"
                    scroll={{ x: 966 }}
                    loading={tableLoading}
                    pagination={{
                        ...pagination,
                        showTotal: (total: number) => `共 ${total} 条`,
                        showSizeChanger: false
                    }}
                    onChange={handleTableChange}
                />
            </div>
        </div >
    )
};

export default Workflow;




