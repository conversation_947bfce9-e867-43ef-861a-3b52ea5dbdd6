import { message } from 'antd';


export const handleCopyText = async (id: string, tip: string) => {
    if ('clipboard' in navigator) {
        try {
            await navigator.clipboard.writeText(id);
            message.success(`${tip} 复制成功`);
        } catch (error) {
            // 如果 Clipboard API 失败，尝试使用 execCommand
            fallbackCopyTextToClipboard(id, tip);
        }
    } else {
        fallbackCopyTextToClipboard(id, tip);
    }
};

const fallbackCopyTextToClipboard = (id: string, tip: string) => {
    const textArea = document.createElement('textarea');
    textArea.value = id;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            message.success(`${tip} 复制成功`);
        } else {
            message.error('复制失败，请稍后重试');
        }
    } catch (err) {
        message.error('复制失败，请稍后重试');
    }
    document.body.removeChild(textArea);
};