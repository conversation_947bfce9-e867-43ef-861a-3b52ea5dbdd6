package com.dataxai.web.service.export.support;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.dataxai.domain.*;
import com.dataxai.service.*;
import com.dataxai.web.dto.ExcelDTO;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import java.io.*;
import java.lang.reflect.Type;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * Excel 数据填充服务：
 * - 负责基于模板的两种导出模式的具体填充
 * - 提供下载、打开、清理、颜色解析等通用辅助
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelFillService {

    private final ITExcelCustomTemplateService tExcelCustomTemplateService;
    private final ITExcelCustomValueService iTExcelCustomValueService;
    private final IProductInfoService iProductInfoService;
    private final IRiskDetectionTaskDetailService iRiskDetectionTaskDetailService;
    private final ITitleExtractionTaskDetailService iTitleExtractionTaskDetailService;

    /**
     * 中文模板（templateType == 1）填充入口。
     * 1) 根据请求中的模板ID拉取模板明细数据
     * 2) 第3/4行分别视为主/子类目头，先写入无 index 的固定行数据
     * 3) 再按 index 分组写入可扩展行（SKU、图片等）
     * 4) 收尾清理空行
     */
    public void fillForTemplateMode(TExcelCustomTemplateDTO dto, Workbook wb, Sheet sheet) throws Exception {

        int currentRow = 5; // 从第6行开始写
        if (dto.getTExcelCustomTemplateList() == null) return;

        for (TExcelCustomTemplateDTO t : dto.getTExcelCustomTemplateList()) {
            if (t == null || t.getId() == null) continue;

            TExcelCustomTemplate tpl = tExcelCustomTemplateService.selectTExcelCustomTemplateById(t.getId());
            if (tpl == null) continue;

            String tplId = tpl.getTExcelCustomTemplateId();
            List<TExcelCustomTemplateVO> dataList = iTExcelCustomValueService.selectTExcelCustomValueListSign(tplId);
            if (dataList == null || dataList.isEmpty()) continue;

            // 生成 SPU 编码
            String spuCode = genCode();
            List<TExcelCustomTemplateVO> sizeList = dataList.stream().filter(x -> "尺码".equals(x.getChildCategory())).collect(Collectors.toList());
            // 步骤 1: 写入无 index 的固定行数据 (SPU 行)
            t.setTemplateType(dto.getTemplateType());
            writeNonIndexedData(sheet, dataList, currentRow, t, spuCode, sizeList);

            // 步骤 2: 写入有 index 的数据行 (SKU 行)

            try {
                sizeList.sort(Comparator.comparing(TExcelCustomTemplateVO::getIndexs));
            } catch (Exception ignore) {
            }
            int rowsAdded = writeIndexedForTemplateMode(sheet, dataList, currentRow + 1, spuCode, sizeList, t);

            // 步骤 3: 在 SPU 和 SKU 行写完后，回填需要跨行的特殊列
            fillSpecialColumns(sheet, dataList, currentRow, currentRow + rowsAdded, spuCode, t);

            // 更新下一个产品块的起始行号
            currentRow += (1 + rowsAdded);
        }
        clearExtraEmptyRows(sheet, currentRow);
    }

    /**
     * 英文/通用模板 (templateType != 1) 填充入口。
     * 1) 循环处理请求中的每个子模板
     * 2) 按 index 分组将数据写入对应行
     * 3) 收尾清理空行
     */
    public void fillForSimpleMode(TExcelCustomTemplateDTO dto, Workbook wb, Sheet sheet) throws Exception {
        int currentRow = 6; // 英文模板从第7行
        if (dto.getTExcelCustomTemplateList() == null) return;
        for (TExcelCustomTemplateDTO t : dto.getTExcelCustomTemplateList()) {
            if (t == null || t.getId() == null) continue;
            TExcelCustomTemplate tpl = tExcelCustomTemplateService.selectTExcelCustomTemplateById(t.getId());
            if (tpl == null) continue;
            String tplId = tpl.getTExcelCustomTemplateId();
            List<TExcelCustomTemplateVO> dataList = iTExcelCustomValueService.selectTExcelCustomValueListSign(tplId);
            String code = genCode();
            t.setTemplateType(dto.getTemplateType());
            int rowsAdded = writeIndexedForTk(sheet, dataList, currentRow, code, t);
            currentRow += rowsAdded;
        }
        clearExtraEmptyRows(sheet, currentRow);
    }

    /**
     * 生成 SPU/SKU 等编码，格式: yyMMddHHmmss + 4位随机数
     */
    private String genCode() {

        DateTimeFormatter timeFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(timeFormat);
        int randomNum = ThreadLocalRandom.current().nextInt(0, 10000);
        String randomPart = String.format("%04d", randomNum);
        return timestamp + randomPart;
    }

    /**
     * 将无 index 的数据写入固定行。
     * - 通过匹配第3行的主分类和第4行的子分类来定位列，支持合并单元格。
     */
    private void writeNonIndexedData(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int rowNum, TExcelCustomTemplateDTO req, String spuCode, List<TExcelCustomTemplateVO> sizeList) {
        if (sheet == null || dataList == null || dataList.isEmpty()) return;
        Row mainCategoryRow = sheet.getRow(2);
        Row childCategoryRow = sheet.getRow(3);
        int idx = 0;
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
        for (TExcelCustomTemplateVO vo : dataList) {
            if (vo.getIndex() != null) continue;
            String mainCategory = vo.getMainCategory();
            String childCategory = vo.getChildCategory();
            String value = vo.getColumnValue();
            String type = vo.getType();
            if (mainCategoryRow == null || childCategoryRow == null) continue;
            int firstCol = -1, lastCol = -1;
            for (CellRangeAddress region : mergedRegions) {
                if (region.containsRow(2)) {
                    int fc = region.getFirstColumn();
                    Row topRow = sheet.getRow(region.getFirstRow());
                    Cell topCell = (topRow != null ? topRow.getCell(fc) : null);
                    String v = getValue(topCell);
                    if (Objects.equals(mainCategory, v)) {
                        firstCol = region.getFirstColumn();
                        lastCol = region.getLastColumn();
                        break;
                    }
                }
            }
            if (firstCol == -1) {
                for (int c = 0; c < mainCategoryRow.getLastCellNum(); c++) {
                    if (Objects.equals(mainCategory, getValue(mainCategoryRow.getCell(c)))) {
                        firstCol = c;
                        lastCol = c;
                        break;
                    }
                }
            }
            if (firstCol == -1) continue;
            for (int c = firstCol; c <= lastCol; c++) {
                String childVal = getValue(childCategoryRow.getCell(c));
                if (headerMatches(childCategory, childVal)) {
                    Row targetRow = sheet.getRow(rowNum);
                    if (targetRow == null) targetRow = sheet.createRow(rowNum);
                    Cell cell = targetRow.getCell(c);
                    if (cell == null) cell = targetRow.createCell(c);
                    ExcelDTO excelDTO = processJsonConfig(type, value, req, spuCode, sizeList, idx);
                    cell.setCellValue(excelDTO.getValue());
                    break;
                }
            }
        }
    }

    /**
     * 定义子分类头的柔性匹配规则，例如允许“面料”匹配“面料弹性”。
     */
    private boolean headerMatches(String wanted, String header) {

        if (wanted == null || header == null) return false;
        if (wanted.equals(header)) return true;
        if ("面料".equals(wanted)) return "面料".equals(header) || "面料弹性".equals(header);
        return false;
    }

    /**
     * 安全地获取单元格的字符串值，处理了多种单元格类型。
     */
    private String getValue(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                switch (cell.getCachedFormulaResultType()) {
                    case STRING:
                        return cell.getStringCellValue().trim();
                    case NUMERIC:
                        return String.valueOf((long) cell.getNumericCellValue());
                    default:
                        return null;
                }
            default:
                return null;
        }
    }

    /**
     * 为中文模板写入按 index 分组的数据行。
     * - 处理 SKU货号、色值、商品轮播图等特殊列的填充逻辑。
     *
     * @return 添加的总行数
     */
    private int writeIndexedForTemplateMode(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int startRowNum, String code, List<TExcelCustomTemplateVO> sizeList, TExcelCustomTemplateDTO req) {
        int startBase = startRowNum - 1;
        Map<Long, List<TExcelCustomTemplateVO>> groups = dataList.stream().filter(d -> d.getIndex() != null).collect(Collectors.groupingBy(TExcelCustomTemplateVO::getIndex));
        if (groups.isEmpty()) return 0;
        Row mainRow = sheet.getRow(2);
        Row childRow = sheet.getRow(3);
        List<CellRangeAddress> merged = sheet.getMergedRegions();
        int maxAdded = 0;
        int sizeIdx = 0;
        String colorValue = resolveColorValue(req, dataList);
        String[] whiteUrlList = {"https://a1.x914.com/xjmhjh/i/2025/06/23/beimianbai.jpg", "https://a1.x914.com/xjmhjh/i/2025/06/23/caizhibai.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/chefengbai.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/xiukoubai.png"};
        String[] blackUrlList = {"https://a1.x914.com/xjmhjh/i/2025/06/23/Beimian-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/chefeng-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/mianliao-1.png", "https://a1.x914.com/xjmhjh/i/2025/06/23/xiukou-1.png"};
        for (Map.Entry<Long, List<TExcelCustomTemplateVO>> e : groups.entrySet()) {
            int target = startBase + e.getKey().intValue();
            Row row = sheet.getRow(target);
            if (row == null) row = sheet.createRow(target);
            maxAdded = Math.max(maxAdded, target - startBase);
            for (TExcelCustomTemplateVO d : e.getValue()) {
                String main = d.getMainCategory();
                String child = d.getChildCategory();
                String val = d.getColumnValue();
                for (int i = 0; i < mainRow.getLastCellNum(); i++) {
                    Cell mc = mainRow.getCell(i);
                    if (mc == null) continue;
                    String mainCell = getValue(mc);
                    if (!Objects.equals(main, mainCell)) continue;
                    boolean mergedCell = false;
                    int first = i, last = i;
                    for (CellRangeAddress r : merged) {
                        if (r.containsRow(mc.getRowIndex()) && r.containsColumn(i)) {
                            mergedCell = true;
                            first = r.getFirstColumn();
                            last = r.getLastColumn();
                            break;
                        }
                    }
                    for (int c = first; c <= last; c++) {
                        String childCell = getValue(childRow.getCell(c));
                        if (!Objects.equals(child, childCell)) continue;
                        Cell cell = row.getCell(c);
                        if (cell == null) cell = row.createCell(c);
                        ExcelDTO excelDTO = processJsonConfig(d.getType(), val, req, code, sizeList, sizeIdx);
                        sizeIdx = excelDTO.getSizeIdx();
                        cell.setCellValue(excelDTO.getValue());
//                        if ("SKU货号".equals(child)) {
//                            String sizeVal = (sizeIdx < sizeList.size() ? sizeList.get(sizeIdx).getColumnValue() : "");
//                            cell.setCellValue(code + "-" + sizeVal);
//                            sizeIdx++;
//                        } else if ("色值(主规格)".equals(child)) {
//                            cell.setCellValue(colorValue);
//                        } else if ("商品轮播图1".equals(child)) {
//                            cell.setCellValue(req.getImageUrl());
//                        } else if ("商品轮播图2".equals(child) || "商品轮播图3".equals(child) || "商品轮播图4".equals(child) || "商品轮播图5".equals(child)) {
//                            if (Objects.equals(req.getDoesmatch(), 1)) {
//                                int urlIdx = Math.min(3, c % 4);
//                                cell.setCellValue("白色".equals(colorValue) ? whiteUrlList[urlIdx] : blackUrlList[urlIdx]);
//                            } else {
//                                cell.setCellValue(val);
//                            }
//                        } else {
//                            cell.setCellValue(val);
//                        }
                    }
                }
            }
        }
        return maxAdded;
    }

    /**
     * /**
     * 填充模板模式下的特殊列，这些列的值通常跨越多行或需要动态生成。
     * <p>
     * 此方法处理 "商品层级" (spu/sku), "SPU货号", "商品名称", "英文名称" 等列。
     * 它会遍历指定行范围内的每一行，并根据预设规则填充这些特殊列的值。
     *
     * @param sheet    当前操作的 Excel 工作表。
     * @param dataList 包含所有模板数据的列表，用于查找特殊列的模板值。
     * @param startRow 需要填充的起始行号（包含）。
     * @param endRow   需要填充的结束行号（包含）。
     * @param spuCode  为 "SPU货号" 列生成的唯一编码。
     * @param req      当前的子请求 DTO，用于获取 "商品名称" 等信息。
     */
    private void fillSpecialColumns(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int startRow, int endRow, String spuCode, TExcelCustomTemplateDTO req) {
        if (sheet == null) return;
        Row childCategoryRow = sheet.getRow(3); // 子分类行
        if (childCategoryRow == null) return;

        // 定义需要特殊处理的列名
        Set<String> specialColumns = new HashSet<>(Arrays.asList(
                "商品层级", "SPU货号", "商品名称", "英文名称", "商品产地", "产地省份"
        ));

        // 获取所有需要填充的特殊列数据 (这些是无index的，作为模板)
        List<TExcelCustomTemplateVO> specialDataTemplates = dataList.stream()
                .filter(item -> item.getIndex() == null && specialColumns.contains(item.getChildCategory()))
                .collect(Collectors.toList());

        if (specialDataTemplates.isEmpty()) {
            log.warn("没有找到需要特殊处理的列的模板数据");
            return;
        }

        // 遍历需要填充的每一行
        for (int rowNum = startRow; rowNum <= endRow; rowNum++) {
            Row targetRow = sheet.getRow(rowNum);
            if (targetRow == null) {
                targetRow = sheet.createRow(rowNum);
            }

            // 遍历每个特殊列的模板数据
            for (TExcelCustomTemplateVO data : specialDataTemplates) {
                String childCategory = data.getChildCategory();
                String columnValue = data.getColumnValue(); // 默认值

                // 根据列名和行号决定最终值
                switch (childCategory) {
                    case "商品名称":
                    case "英文名称":
                        columnValue = req.getEnname();
                        break;
                    case "商品层级":
                        columnValue = (rowNum == startRow) ? "spu" : "sku";
                        break;
                    case "SPU货号":
                        columnValue = spuCode;
                        break;
                    case "商品产地":
                    case "产地省份":
                        // 保持默认值
                        break;
                }

                // 遍历子分类行，查找匹配的列并写入值
                for (int col = 0; col < childCategoryRow.getLastCellNum(); col++) {
                    Cell childCell = childCategoryRow.getCell(col);
                    String cellValue = getValue(childCell);
                    if (childCategory.equals(cellValue)) {
                        Cell targetCell = targetRow.getCell(col, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        targetCell.setCellValue(columnValue);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 为英文/TK 模板写入按 index 分组的数据行。
     * - 只处理 SKU货号 和其他通用值的写入。
     *
     * @return 添加的总行数
     */
    private int writeIndexedForTk(Sheet sheet, List<TExcelCustomTemplateVO> dataList, int startRowNum, String code, TExcelCustomTemplateDTO req) {
        int startBase = startRowNum - 1;
        Map<Long, List<TExcelCustomTemplateVO>> groups = dataList.stream().filter(d -> d.getIndex() != null).collect(Collectors.groupingBy(TExcelCustomTemplateVO::getIndex));
        if (groups.isEmpty()) return 0;
        Row nameRow = sheet.getRow(2);
        int maxAdded = 0;
        for (Map.Entry<Long, List<TExcelCustomTemplateVO>> e : groups.entrySet()) {
            int target = startBase + e.getKey().intValue();
            Row row = sheet.getRow(target);
            if (row == null) row = sheet.createRow(target);
            maxAdded = Math.max(maxAdded, target - startBase);
            for (TExcelCustomTemplateVO d : e.getValue()) {
                String child = d.getChildCategory();
                String val = d.getColumnValue();
                String type = d.getType();
                for (int i = 0; i < nameRow.getLastCellNum(); i++) {
                    String head = getValue(nameRow.getCell(i));
                    if (!Objects.equals(child, head)) continue;
                    Cell cell = row.getCell(i);
                    if (cell == null) cell = row.createCell(i);
                    ExcelDTO excelDTO = processJsonConfig(type, val, req, code, null, 0);
                    cell.setCellValue(excelDTO.getValue());
                    // 根据列名填充特定的动态值
//                    switch (child) {
//                        case "Seller SKU":
//                        case "SKU货号":
//                            cell.setCellValue(code);
//                            break;
//                        case "商品主图":
//                        case "主要销售变体图片 1":
//                            cell.setCellValue(req.getImageUrl());
//                            break;
//                        case "商品名称":
//                            cell.setCellValue(req.getEnname());
//                            break;
//                        default:
//                            cell.setCellValue(val);
//                            break;
//                    }
                }
            }
        }
        return maxAdded;
    }

    /**
     * 解析并决定应填充的颜色值。
     * - 默认从模板数据中获取“色值(主规格)”。
     * - 当 doesmatch=1 时，根据 exportType 从业务表读取 imageColor 覆盖。
     */
    private String resolveColorValue(TExcelCustomTemplateDTO req, List<TExcelCustomTemplateVO> dataList) {
        // 默认从数据项中取“色值(主规格)”
        String colorValue = dataList.stream()
                .filter(v -> "色值(主规格)".equals(v.getChildCategory()))
                .map(TExcelCustomTemplateVO::getColumnValue)
                .findFirst()
                .orElse("");
        // 仅在 doesmatch == 1 时，才尝试根据 exportType 从业务表覆盖颜色
        if (!Objects.equals(req.getDoesmatch(), 1)) {
            return colorValue == null ? "" : colorValue;
        }
        if (Objects.equals(req.getExportType(), 1)) {
            ProductInfo p = iProductInfoService.selectProductInfoById(req.getDataId());
            if (p != null && p.getImageColor() != null && !p.getImageColor().isEmpty()) colorValue = p.getImageColor();
        } else if (Objects.equals(req.getExportType(), 2)) {
            RiskDetectionTaskDetail r = iRiskDetectionTaskDetailService.selectRiskDetectionTaskDetailById(req.getDataId());
            if (r != null && Objects.equals(r.getType(), 2)) {
                ProductInfo p = iProductInfoService.selectProductInfoById(r.getTypeId());
                if (p != null && p.getImageColor() != null && !p.getImageColor().isEmpty())
                    colorValue = p.getImageColor();
            }
        } else if (Objects.equals(req.getExportType(), 3)) {
            TitleExtractionTaskDetail t = iTitleExtractionTaskDetailService.selectTitleExtractionTaskDetailById(req.getDataId());
            if (t != null && Objects.equals(t.getType(), 2)) {
                ProductInfo p = iProductInfoService.selectProductInfoById(t.getTypeId());
                if (p != null && p.getImageColor() != null && !p.getImageColor().isEmpty())
                    colorValue = p.getImageColor();
            }
        }
        return colorValue == null ? "" : colorValue;
    }


    /**
     * 下载模板文件并校验是否为有效的 Excel（.xlsx/.xls）。
     * - 支持 HTTP 跳转、超时配置
     * - 下载后尝试用 Apache POI 打开，验证文件格式，失败则抛出明确异常
     */
    public File downloadFileFromUrl(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setInstanceFollowRedirects(true);
        conn.setConnectTimeout(10000);
        conn.setReadTimeout(20000);
        conn.setRequestMethod("GET");
        int code = conn.getResponseCode();
        if (code >= 300 && code < 400) {
            String loc = conn.getHeaderField("Location");
            if (loc != null && !loc.isEmpty()) {
                conn.disconnect();
                url = new URL(loc);
                conn = (HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(10000);
                conn.setReadTimeout(20000);
            }
        }
        // 始终以 .xlsx 后缀保存临时文件，便于排查
        File tempFile = File.createTempFile("excel-", ".xlsx");
        try (InputStream is = conn.getInputStream(); FileOutputStream fos = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        } finally {
            try {
                conn.disconnect();
            } catch (Exception ignore) {
            }
        }
        // 轻量校验：尝试用 POI 打开，失败则说明不是有效的 Excel
        try (InputStream in = new FileInputStream(tempFile)) {
            Workbook wb = WorkbookFactory.create(in);
            wb.close();
        } catch (Exception e) {
            tempFile.delete();
            throw new IOException("下载的模板不是有效的Excel文件，请检查链接是否指向 .xlsx/.xls", e);
        }
        return tempFile;
    }

    /**
     * 从文件创建 Workbook 实例
     */
    public Workbook createWorkbook(File file) throws IOException {
        try (InputStream inputStream = new FileInputStream(file)) {
            return WorkbookFactory.create(inputStream);
        }
    }

    /**
     * 清空 Sheet 中从 startRow 开始的所有行内容。
     */
    public void clearSheetData(Sheet sheet, int startRow) {
        if (sheet == null) return;
        int lastRowNum = sheet.getLastRowNum();
        if (startRow > lastRowNum) return;
        for (int i = lastRowNum; i >= startRow; i--) {
            Row row = sheet.getRow(i);
            if (row != null) sheet.removeRow(row);
        }
    }

    /**
     * 清空 Sheet 中从 startRow 开始的所有行内容。
     */
    private void clearExtraEmptyRows(Sheet sheet, int startRow) {
        int lastRowNum = sheet.getLastRowNum();
        for (int i = lastRowNum; i > startRow; i--) {
            Row row = sheet.getRow(i);
            boolean isEmpty = true;
            if (row != null) {
                for (int j = row.getFirstCellNum(); j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() != CellType.BLANK &&
                            !(cell.getCellType() == CellType.STRING && (cell.getStringCellValue() == null || cell.getStringCellValue().isEmpty()))) {
                        isEmpty = false;
                        break;
                    }
                }
            }
            if (isEmpty) {
                sheet.removeRow(row);
            }
        }
    }

    /**
     * 根据传入的 DTO（取其子项的 id）从数据库加载模板并返回 excelUrl
     */
    public String resolveExcelUrlFromDto(TExcelCustomTemplateDTO dto) {
        if (dto == null || dto.getTExcelCustomTemplateList() == null) return null;
        for (TExcelCustomTemplateDTO it : dto.getTExcelCustomTemplateList()) {
            if (it == null || it.getId() == null) continue;
            try {
                TExcelCustomTemplate tpl = tExcelCustomTemplateService.selectTExcelCustomTemplateById(it.getId());
                if (tpl != null && tpl.getExcelUrl() != null && !tpl.getExcelUrl().isEmpty()) return tpl.getExcelUrl();
            } catch (Exception ignore) {
            }
        }
        return null;
    }
    /**
     * 通用列值解析器。根据一个JSON配置字符串动态生成单元格的值。
     *
     * @param jsonConfig        描述如何生成值的JSON数组字符串。示例: [{"type":"fixed","value":"SKU-"}, {"type":"spu"}]
     * @param dynamicValues     一个存放当前行动态数据的Map，键是列名，值是数据值。
     * @param spuCode           当前产品的SPU货号。
     * @param skuCode           当前行的SKU货号。
     * @param mainImageUrl      当前产品的主图URL。
     * @return 根据配置生成的最终字符串值。
     */
//    public String resolveColumnValue(String jsonConfig, Map<String, String> dynamicValues, String spuCode, String skuCode, TExcelCustomTemplateDTO req) {
//        if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
//            return "";
//        }
//
//        StringBuilder finalValue = new StringBuilder();
//        try {
//            cn.hutool.json.JSONArray configArray = cn.hutool.json.JSONUtil.parseArray(jsonConfig);
//            // 调用辅助方法处理配置数组
//            processJsonConfig(finalValue, configArray, dynamicValues, spuCode, skuCode, req);
//        } catch (Exception e) {
//            log.error("解析列配置JSON失败: {}", jsonConfig, e);
//            return ""; // 出错时返回空字符串，避免影响导出
//        }
//
//        return finalValue.toString();
//    }

    /**
     * 递归处理JSON配置数组的辅助方法。
     */
    private ExcelDTO processJsonConfig(String type, String value, TExcelCustomTemplateDTO req, String spuCode, List<TExcelCustomTemplateVO> sizeList, int sizeIdx) {
//    private void processJsonConfig(StringBuilder finalValue, cn.hutool.json.JSONArray configArray, Map<String, String> dynamicValues, String spuCode, String skuCode, TExcelCustomTemplateDTO req) {
//        for (Object configObject : configArray) {
//            if (!(configObject instanceof cn.hutool.json.JSONObject)) continue;
//
//            cn.hutool.json.JSONObject config = (cn.hutool.json.JSONObject) configObject;
//            String type = config.getStr("type");
        ExcelDTO excelDTO = new ExcelDTO();
        String values = "";
        switch (type) {
            case "fixed":
                values = value;
                break;
            case "dynamics":
                String dynamicKey = value;
                switch (dynamicKey) {
                    case "images": // 结果图
                    case "images-1": // 结果图
                    case "original": // 原图
                    case "printing": // 印花图
                        values = req.getImageUrl();
                        break;
                    case "title": // 原标题
                    case "cnTitle": // 中文标题
                        values = req.getEnname();
                        break;
                    case "enTitle": // 英文标题
                    case "name": // 名称
                        values = req.getEnname();
                        break;
                    default:
                        // 如果没有匹配任何关键字，默认使用图片URL
                        values = req.getImageUrl();
                        break;
                }
                break;
            case "multiple":
                Gson gson = new Gson();
                Type listType = new TypeToken<List<ExcelDTO>>() {}.getType();
                List<ExcelDTO> configList = gson.fromJson(value, listType);
                List<String> collectedValues = new ArrayList<>();
                // 遍历嵌套的配置并解析出值
                for (ExcelDTO config : configList) {
                    String resolvedValue = "";
                    switch (config.getType()) {
                        case "fixed":
                            resolvedValue = config.getValue();
                            break;
                        case "dynamics":
                            String dynamicKeyvalue = config.getValue();
                            switch (dynamicKeyvalue) {
                                case "images": // 结果图
                                case "original": // 原图
                                case "printing": // 印花图
                                    resolvedValue = req.getImageUrl();
                                    break;
                                case "title": // 原标题
                                case "cnTitle": // 中文标题
                                    resolvedValue = req.getCnname();
                                    break;
                                case "enTitle": // 英文标题
                                case "name": // 名称
                                    resolvedValue = req.getEnname();
                                    break;
                                default:
                                    // 如果没有匹配任何关键字，默认使用图片URL
                                    resolvedValue = req.getImageUrl();
                                    break;
                            }
                            break;
                    }
                    collectedValues.add(resolvedValue != null ? resolvedValue : "");
                }
                // 使用换行符拼接所有的值
                values = String.join("\n", collectedValues);
//                JSONArray subArray = value;
//                if (subArray != null) {
//                    // 递归处理嵌套的数组
//                    processJsonConfig(finalValue, subArray, dynamicValues, spuCode, skuCode, req);
//                }

                break;
            case "spu":
                values = spuCode;
                break;
            case "sku":
                if (req.getTemplateType() == 1) {
                    String sizeVal = (sizeIdx < sizeList.size() ? sizeList.get(sizeIdx).getColumnValue() : "");
                    values = spuCode + "-" + sizeVal;
                    sizeIdx++;
                } else {
                    values = spuCode;
                }

                break;
            case "upload":
                values = value;
                break;
            case "text":
                values = value;
                break;
            case "select":
                values = value;
                break;
            default:
                log.warn("发现未知的列值类型: {}", type);
                break;
        }

        excelDTO.setValue(values);
        excelDTO.setSizeIdx(sizeIdx);
//        }
        return excelDTO;
    }

    /**
     * 封装了对字符串的再次解析和处理，用于 dynamics 类型。
     */
//    private void resolveAndProcess(StringBuilder finalValue, String jsonConfig, Map<String, String> dynamicValues, String spuCode, String skuCode, TExcelCustomTemplateDTO req) {
//        if (jsonConfig != null && !jsonConfig.trim().isEmpty()) {
//            try {
//                if (JSONUtil.isTypeJSONArray(jsonConfig.trim())) {
//                    JSONArray configArray = JSONUtil.parseArray(jsonConfig);
//                    processJsonConfig(finalValue, configArray, dynamicValues, spuCode, skuCode, req);
//                } else {
//                    // 如果不是JSON数组，则作为普通字符串处理
//                    finalValue.append(jsonConfig);
//                }
//            } catch (Exception e) {
//                // 解析失败，很可能它本身就是一个普通字符串，直接追加
//                finalValue.append(jsonConfig);
//            }
//        }
//    }


}

