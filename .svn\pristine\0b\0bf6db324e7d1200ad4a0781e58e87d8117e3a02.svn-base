package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialIpHistory;
import com.dataxai.web.domain.MaterialStyleHistory;
import com.dataxai.web.mapper.MaterialIpHistoryMapper;
import com.dataxai.web.mapper.MaterialStyleHistoryMapper;
import com.dataxai.web.service.MaterialHistoryService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class MaterialHistoryServiceImpl implements MaterialHistoryService {

    @Autowired
    private MaterialIpHistoryMapper materialIpHistoryMapper;
    
    @Autowired
    private MaterialStyleHistoryMapper materialStyleHistoryMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void recordIpUsage(Integer materialIpId, Integer userId, String taskId) {
        if (materialIpId == null || userId == null || taskId == null) {
            log.warn("记录IP素材使用历史时参数不完整: materialIpId={}, userId={}, taskId={}",
                    materialIpId, userId, taskId);
            return;
        }

        try {
            MaterialIpHistory history = new MaterialIpHistory();
            history.setMaterialIpId(materialIpId);
            history.setUserId(userId);
            history.setTaskId(taskId);
            history.setCreateTime(new Date());

            materialIpHistoryMapper.insert(history);
            log.info("记录IP素材使用历史成功: materialIpId={}, userId={}, taskId={}",
                    materialIpId, userId, taskId);
        } catch (Exception e) {
            log.error("记录IP素材使用历史失败: materialIpId={}, userId={}, taskId={}",
                    materialIpId, userId, taskId, e);
        }
    }

    @Override
    public void recordStyleUsage(Integer materialStyleId, Integer userId, String taskId) {
        if (materialStyleId == null || userId == null || taskId == null) {
            log.warn("记录风格素材使用历史时参数不完整: materialStyleId={}, userId={}, taskId={}", 
                    materialStyleId, userId, taskId);
            return;
        }
        
        try {
            MaterialStyleHistory history = new MaterialStyleHistory();
            history.setMaterialStyleId(materialStyleId);
            history.setUserId(userId);
            history.setTaskId(taskId);
            history.setCreateTime(new Date());
            
            materialStyleHistoryMapper.insert(history);
            log.info("记录风格素材使用历史成功: materialStyleId={}, userId={}, taskId={}", 
                    materialStyleId, userId, taskId);
        } catch (Exception e) {
            log.error("记录风格素材使用历史失败: materialStyleId={}, userId={}, taskId={}", 
                    materialStyleId, userId, taskId, e);
        }
    }

    @Override
    public void recordMaterialUsageFromTaskParam(String taskParam, Integer userId, String taskId) {
        if (taskParam == null || taskParam.trim().isEmpty() || userId == null || taskId == null) {
            log.debug("任务参数为空或用户ID/任务ID为空，跳过素材使用历史记录");
            return;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(taskParam);

            // 解析IP素材ID
            JsonNode materialIpIdNode = jsonNode.get("materialIpId");
            if (materialIpIdNode != null && !materialIpIdNode.isNull()) {
                Integer materialIpId = materialIpIdNode.asInt();
                recordIpUsage(materialIpId, userId, taskId);
            }

            // 解析风格素材ID
            JsonNode materialStyleIdNode = jsonNode.get("materialStyleId");
            if (materialStyleIdNode != null && !materialStyleIdNode.isNull()) {
                Integer materialStyleId = materialStyleIdNode.asInt();
                recordStyleUsage(materialStyleId, userId, taskId);
            }

            // 兼容旧的字段名
            JsonNode ipIdNode = jsonNode.get("ipId");
            if (ipIdNode != null && !ipIdNode.isNull()) {
                Integer materialIpId = ipIdNode.asInt();
                recordIpUsage(materialIpId, userId, taskId);
            }

            JsonNode styleIdNode = jsonNode.get("styleId");
            if (styleIdNode != null && !styleIdNode.isNull()) {
                Integer materialStyleId = styleIdNode.asInt();
                recordStyleUsage(materialStyleId, userId, taskId);
            }

        } catch (Exception e) {
            log.error("解析任务参数并记录素材使用历史失败: taskParam={}, userId={}, taskId={}",
                    taskParam, userId, taskId, e);
        }
    }
    @Override
    public void cleanOldRecords() {
        try {
            int deletedIpRecords = materialIpHistoryMapper.deleteOldRecords();
            int deletedStyleRecords = materialStyleHistoryMapper.deleteOldRecords();
            
            log.info("清理历史记录完成: 删除IP历史记录{}条, 删除风格历史记录{}条", 
                    deletedIpRecords, deletedStyleRecords);
        } catch (Exception e) {
            log.error("清理历史记录失败", e);
        }
    }
}
