package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialIp;
import com.dataxai.web.domain.MaterialStyle;
import com.dataxai.web.domain.MaterialStyleCategory;
import com.dataxai.web.domain.MaterialIpCategory;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.service.MaterialIpService;
import com.dataxai.web.service.MaterialResolverService;
import com.dataxai.web.service.MaterialStyleService;
import com.dataxai.web.service.MaterialStyleCategoryService;
import com.dataxai.web.service.MaterialIpCategoryService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.dataxai.web.service.CropExtractCategoryService;
import com.dataxai.web.domain.CropExtractCategory;

@Slf4j
@Service
public class MaterialResolverServiceImpl implements MaterialResolverService {

    @Autowired
    private MaterialStyleService materialStyleService;
    
    @Autowired
    private MaterialIpService materialIpService;

    @Autowired
    private MaterialStyleCategoryService materialStyleCategoryService;

    @Autowired
    private MaterialIpCategoryService materialIpCategoryService;

    @Autowired(required = false)
    private CropExtractCategoryService cropExtractCategoryService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public MaterialStyle getStyleById(Integer materialStyleId) {
        if (materialStyleId == null) {
            return null;
        }

        try {
            if (materialStyleService == null) {
                log.error("materialStyleService注入失败");
                return null;
            }

            MaterialStyle result = materialStyleService.getById(materialStyleId);
            if (result == null) {
                log.warn("风格素材不存在: materialStyleId={}", materialStyleId);
            }
            return result;

        } catch (Exception e) {
            log.error("获取风格素材失败: materialStyleId={}", materialStyleId, e);
            return null; // 返回null而不是抛出异常
        }
    }

    @Override
    public MaterialIp getIpById(Integer materialIpId) {
        if (materialIpId == null) {
            return null;
        }

        try {
            if (materialIpService == null) {
                log.error("materialIpService注入失败");
                return null;
            }

            MaterialIp result = materialIpService.getById(materialIpId);
            if (result == null) {
                log.warn("IP素材不存在: materialIpId={}", materialIpId);
            }
            return result;

        } catch (Exception e) {
            log.error("获取IP素材失败: materialIpId={}", materialIpId, e);
            return null; // 返回null而不是抛出异常
        }
    }

    @Override
    public String resolveStylePrompt(String taskParam) {
        if (!StringUtils.hasText(taskParam)) {
            return null;
        }

        try {
            // 直接解析JSON，不反序列化为DTO
            JsonNode paramNode = objectMapper.readTree(taskParam);

            // 优先使用taskParam中已有的stylePrompt（由buildFullTaskParam构建）
            JsonNode stylePromptNode = paramNode.get("stylePrompt");
            if (stylePromptNode != null && !stylePromptNode.isNull()) {
                String stylePrompt = stylePromptNode.asText();
                if (StringUtils.hasText(stylePrompt)) {
                    return stylePrompt;
                }
            }

            // 如果taskParam中没有stylePrompt，则通过materialStyleId查询
            JsonNode materialStyleIdNode = paramNode.get("materialStyleId");
            if (materialStyleIdNode != null && !materialStyleIdNode.isNull()) {
                Integer materialStyleId = materialStyleIdNode.asInt();
                MaterialStyle materialStyle = getStyleById(materialStyleId);
                if (materialStyle != null) {
                    return materialStyle.getStylePrompt();
                }
            }

            return null;

        } catch (Exception e) {
            log.error("解析风格提示词失败: taskParam={}", taskParam, e);
            return null;
        }
    }

    @Override
    public String resolveIpPrompt(String taskParam) {
        if (!StringUtils.hasText(taskParam)) {
            return null;
        }

        try {
            // 直接解析JSON，不反序列化为DTO
            JsonNode paramNode = objectMapper.readTree(taskParam);

            // 优先使用taskParam中已有的fashIpPrompt（由buildFullTaskParam构建）
            JsonNode fashIpPromptNode = paramNode.get("fashIpPrompt");
            if (fashIpPromptNode != null && !fashIpPromptNode.isNull()) {
                String fashIpPrompt = fashIpPromptNode.asText();
                if (StringUtils.hasText(fashIpPrompt)) {
                    return fashIpPrompt;
                }
            }

            // 如果taskParam中没有fashIpPrompt，则通过materialIpId查询
            JsonNode materialIpIdNode = paramNode.get("materialIpId");
            if (materialIpIdNode != null && !materialIpIdNode.isNull()) {
                Integer materialIpId = materialIpIdNode.asInt();
                MaterialIp materialIp = getIpById(materialIpId);
                if (materialIp != null) {
                    return materialIp.getFashIpPrompt();
                }
            }

            return null;

        } catch (Exception e) {
            log.error("解析IP提示词失败: taskParam={}", taskParam, e);
            return null;
        }
    }

    @Override
    public String buildFullPrompt(String originalPrompt, String taskParam) {
        StringBuilder fullPrompt = new StringBuilder();
        
        // 添加原始提示词
        if (StringUtils.hasText(originalPrompt)) {
            fullPrompt.append(originalPrompt);
        }
        
        // 添加风格提示词
        String stylePrompt = resolveStylePrompt(taskParam);
        if (StringUtils.hasText(stylePrompt)) {
            if (fullPrompt.length() > 0) {
                fullPrompt.append(", ");
            }
            fullPrompt.append(stylePrompt);
        }
        
        // 添加IP提示词
        String ipPrompt = resolveIpPrompt(taskParam);
        if (StringUtils.hasText(ipPrompt)) {
            if (fullPrompt.length() > 0) {
                fullPrompt.append(", ");
            }
            fullPrompt.append(ipPrompt);
        }
        
        String result = fullPrompt.toString();
        log.info("构建完整提示词: 原始='{}', 风格='{}', IP='{}', 结果='{}'", 
                originalPrompt, stylePrompt, ipPrompt, result);
        
        return result;
    }

    @Override
    public String buildFullTaskParam(String originalTaskParam) {
        if (!StringUtils.hasText(originalTaskParam)) {
            return originalTaskParam;
        }

        try {
            ObjectNode paramNode = (ObjectNode) objectMapper.readTree(originalTaskParam);
            
            // 处理通用风格素材（materialStyleId）
            JsonNode materialStyleIdNode = paramNode.get("materialStyleId");
            if (materialStyleIdNode != null && !materialStyleIdNode.isNull()) {
                Integer materialStyleId = materialStyleIdNode.asInt();
                MaterialStyle materialStyle = getStyleById(materialStyleId);
                
                if (materialStyle != null) {
                    // 添加风格相关信息
                    paramNode.put("style", materialStyle.getName() != null ? materialStyle.getName() : "");
                    paramNode.put("stylePrompt", materialStyle.getStylePrompt() != null ? materialStyle.getStylePrompt() : "");
                    paramNode.put("styleUrl", materialStyle.getStyleUrl() != null ? materialStyle.getStyleUrl() : "");
                    
                    // 获取风格分类信息
                    if (materialStyle.getMaterialStyleCategoryId() != null) {
                        try {
                            MaterialStyleCategory styleCategory = materialStyleCategoryService.getById(materialStyle.getMaterialStyleCategoryId());
                            if (styleCategory != null) {
                                paramNode.put("styleCate", styleCategory.getName() != null ? styleCategory.getName() : "");
                                log.info("添加风格分类信息: categoryId={}, categoryName={}", 
                                    materialStyle.getMaterialStyleCategoryId(), styleCategory.getName());
                            } else {
                                paramNode.put("styleCate", "");
                                log.warn("风格分类不存在: categoryId={}", materialStyle.getMaterialStyleCategoryId());
                            }
                        } catch (Exception e) {
                            log.error("获取风格分类失败: categoryId={}", materialStyle.getMaterialStyleCategoryId(), e);
                            paramNode.put("styleCate", "");
                        }
                    } else {
                        paramNode.put("styleCate", "");
                        log.info("风格素材没有分类ID");
                    }
                    
                    log.info("添加风格素材信息: styleId={}, name={}", materialStyleId, materialStyle.getName());
                }
            }
            
            // 处理IP素材
            JsonNode materialIpIdNode = paramNode.get("materialIpId");
            if (materialIpIdNode != null && !materialIpIdNode.isNull()) {
                Integer materialIpId = materialIpIdNode.asInt();
                MaterialIp materialIp = getIpById(materialIpId);
                
                if (materialIp != null) {
                    // 添加IP相关信息
                    paramNode.put("fashIp", materialIp.getName() != null ? materialIp.getName() : "");
                    paramNode.put("fashIpPrompt", materialIp.getFashIpPrompt() != null ? materialIp.getFashIpPrompt() : "");
                    paramNode.put("ipUrl", materialIp.getIpUrl() != null ? materialIp.getIpUrl() : "");

                    // 获取IP分类信息
                    if (materialIp.getMaterialIpCategoryId() != null) {
                        try {
                            MaterialIpCategory ipCategory = materialIpCategoryService.getById(materialIp.getMaterialIpCategoryId());
                            if (ipCategory != null) {
                                paramNode.put("fashIpCate", ipCategory.getName() != null ? ipCategory.getName() : "");
                                log.info("添加IP分类信息: categoryId={}, categoryName={}", 
                                    materialIp.getMaterialIpCategoryId(), ipCategory.getName());
                            } else {
                                paramNode.put("fashIpCate", "");
                                log.warn("IP分类不存在: categoryId={}", materialIp.getMaterialIpCategoryId());
                            }
                        } catch (Exception e) {
                            log.error("获取IP分类失败: categoryId={}", materialIp.getMaterialIpCategoryId(), e);
                            paramNode.put("fashIpCate", "");
                        }
                    } else {
                        paramNode.put("fashIpCate", "");
                        log.info("IP素材没有分类ID");
                    }

                    log.info("添加IP素材信息: ipId={}, name={}, ipUrl={}", materialIpId, materialIp.getName(), materialIp.getIpUrl());
                }
            }

            // 兼容旧格式：如果没有新的风格ID，但有旧的风格信息，保持不变
            JsonNode _msNode = paramNode.get("materialStyleId");
            if ((_msNode == null || _msNode.isNull())) {
                JsonNode stylePromptNode = paramNode.get("stylePrompt");
                JsonNode styleNode = paramNode.get("style");
                if (stylePromptNode != null || styleNode != null) {
                    log.info("使用旧格式的风格信息");
                }
            }
            
            // 处理印花图提取品类（cropCategoryId -> cropCategoryName）
            JsonNode cropCategoryIdNode = paramNode.get("cropCategoryId");
            if (cropCategoryIdNode != null && !cropCategoryIdNode.isNull()) {
                Integer cropCategoryId = cropCategoryIdNode.asInt();
                try {
                    if (cropExtractCategoryService != null) {
                        CropExtractCategory cate = cropExtractCategoryService.getById(cropCategoryId);
                        if (cate != null) {
                            paramNode.put("cropCategoryName", cate.getName() != null ? cate.getName() : "");
                            log.info("添加印花图提取品类: id={}, name={}", cropCategoryId, cate.getName());
                        } else {
                            paramNode.put("cropCategoryName", "");
                            log.warn("印花图提取品类不存在: id={}", cropCategoryId);
                        }
                    } else {
                        log.error("cropExtractCategoryService注入失败，无法回填cropCategoryName");
                    }
                } catch (Exception e) {
                    log.error("获取印花图提取品类失败: id={}", cropCategoryId, e);
                    paramNode.put("cropCategoryName", "");
                }
            }
            
            String result = objectMapper.writeValueAsString(paramNode);
            log.info("构建完整任务参数完成: 原始长度={}, 结果长度={}", originalTaskParam.length(), result.length());
            
            return result;
            
        } catch (Exception e) {
            log.error("构建完整任务参数失败: taskParam={}", originalTaskParam, e);
            return originalTaskParam; // 返回原始参数，不影响主流程
        }
    }
}
