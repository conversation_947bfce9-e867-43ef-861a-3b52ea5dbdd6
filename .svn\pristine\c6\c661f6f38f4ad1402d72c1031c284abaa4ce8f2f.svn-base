package com.dataxai.web.batch.impl;

import cn.hutool.json.JSONObject;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.AbstractBatchTaskFactory;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.domain.FileAdapter;
import com.dataxai.web.utils.SnowFlakeUtils;
import com.dataxai.web.utils.ThumbnailUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量上传任务工厂实现
 *
 * <p>处理批量上传图片任务（type=15）</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class BatchUploadTaskFactory extends AbstractBatchTaskFactory {

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{(long) Constants.TASK_TYPE_BATCH_UPLOAD};
    }

    @Override
    public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

        log.info("开始处理批量上传任务，批次ID：{}", batch.getBatchId());
        System.out.println("任务开始");
        String taskParam = dto.getTaskParam();
        JSONObject jsonObj = new JSONObject(taskParam);
        int imageNumber = jsonObj.getInt("imageNumber");
        System.out.println(imageNumber);
        dto.setImageNumber((long) imageNumber);
        System.out.println();
        System.out.println("----------------------------------------------");

        List<Task> taskList = new ArrayList<>();

        for (byte[] fileBytes : fileBytesList) {
            // 上传原图
            String imgUrl = aliYunFileService.upload(new ByteArrayResource(fileBytes));

            // 生成缩略图并转换为MultipartFile
            File thumbnailFile = ThumbnailUtils.generateThumbnail(fileBytes, 300);
            MultipartFile thumbnailMultipartFile = new FileAdapter(thumbnailFile);

            // 上传缩略图
            String thumbnailUrl = aliYunFileService.uploadALiYun(thumbnailMultipartFile);

            // 清理临时文件
            thumbnailFile.delete();

            // 创建任务对象
            Task task = createBatchUploadTask(dto, userId, batch, imgUrl, thumbnailUrl);
            taskList.add(task);

            // 更新批次统计
            updateBatchStatistics(batch);
        }

        // 批量插入任务
        if (!taskList.isEmpty()) {
            taskMapper.insertBatchTask(taskList);
        }

        log.info("批量上传任务处理完成，批次ID：{}，任务数量：{}", batch.getBatchId(), taskList.size());
    }

    @Override
    public List<Task> createTasks(BatchDTO dto, Batch batch, Long userId, String taskParam) {
        // 批量上传任务不需要单独创建任务列表，在processBatchTask中直接处理
        return new ArrayList<>();
    }

    @Override
    public List<TaskOrdinal> createTaskOrdinals(BatchDTO dto, Batch batch, List<Task> taskList,
                                               Long userId, String taskParam) {
        // 批量上传任务不需要创建子任务
        return new ArrayList<>();
    }

    /**
     * 创建批量上传任务
     *
     * @param dto 批次DTO
     * @param userId 用户ID
     * @param batch 批次对象
     * @param imgUrl 图片URL
     * @param thumbnailUrl 缩略图URL
     * @return 任务对象
     */
    private Task createBatchUploadTask(BatchDTO dto, Long userId, Batch batch, String imgUrl, String thumbnailUrl) {
        Task task = new Task();
        task.setBatchId(batch.getBatchId());
        task.setTaskId(SnowFlakeUtils.nextIdStr());
        task.setStatus(Constants.TASK_STATUS_SUCCESS);
        task.setType(dto.getType());
        task.setOriginalUrl(CommonUtils.subCosPrefix(imgUrl));
        task.setThumbnailUrl(CommonUtils.subCosPrefix(thumbnailUrl));
        task.setUserId(userId);
        task.setCreateTime(batch.getCreateTime());
        task.setUpdateTime(batch.getCreateTime());
        task.setDelFlag(0);
        task.setDescStatus(0);
        return task;
    }

    /**
     * 更新批次统计信息
     *
     * @param batch 批次对象
     */
    private void updateBatchStatistics(Batch batch) {
        Batch batchDb = batchMapper.selectById(batch.getBatchId());
        if (batchDb.getSuccessAmount() == null) {
            batchDb.setSuccessAmount(1);
        } else {
            batchDb.setSuccessAmount(batchDb.getSuccessAmount() + 1);
        }

        if (batchDb.getTotalAmount().equals(batchDb.getSuccessAmount())) {
            batchDb.setStatus(Constants.TASK_STATUS_SUCCESS);
        } else {
            batchDb.setStatus(Constants.TASK_STATUS_EXECUTING);
        }

        batchMapper.updateBat(batchDb);
    }
}