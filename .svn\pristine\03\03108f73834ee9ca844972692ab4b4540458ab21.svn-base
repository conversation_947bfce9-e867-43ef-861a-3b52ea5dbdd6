package com.dataxai.web.service.export;


import com.dataxai.domain.TExcelCustomTemplateDTO;
import com.dataxai.web.service.export.strategy.ExportStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Excel 导出服务，作为导出策略的门面(Facade)。
 * <p>
 * 此类通过 Spring 的依赖注入收集所有 {@link ExportStrategy} 的实现。
 * 当调用 {@code export} 方法时，它会根据传入的 {@code templateType} 动态地
 * 查找并委托给合适的策略实例来处理导出请求。
 *
 * @see ExportStrategy
 * @see com.dataxai.web.service.export.strategy.BaseExportStrategy
 */
@Service
@RequiredArgsConstructor
public class TExcelExportService {

    private final List<ExportStrategy> strategies;

    /**
     * 执行导出操作。
     * <p>
     * 此方法作为公开 API，接收控制层的请求。它从 DTO 中提取模板类型，
     * 然后在已注册的策略列表中查找第一个支持该类型的策略，并调用其
     * {@code export} 方法。
     *
     * @param dto      数据传输对象，包含导出所需的全部信息。
     * @param response HTTP 响应对象，用于将生成的 Excel 文件写入客户端。
     * @throws IllegalArgumentException 如果没有找到支持指定 {@code templateType} 的策略。
     */
    public void export(TExcelCustomTemplateDTO dto, HttpServletResponse response) throws Exception {
        // 从 DTO 中获取模板类型作为策略选择的依据
        final Long type = dto.getTemplateType();

        // 使用 Stream API 在策略列表中过滤，找到第一个匹配的策略
        strategies.stream()
                .filter(s -> s.supports(type))
                .findFirst()
                // 如果未找到，抛出异常，提供清晰的错误信息
                .orElseThrow(() -> new IllegalArgumentException("Unsupported templateType: " + type))
                // 如果找到，调用其 export 方法执行导出
                .export(dto, response);
    }
}

