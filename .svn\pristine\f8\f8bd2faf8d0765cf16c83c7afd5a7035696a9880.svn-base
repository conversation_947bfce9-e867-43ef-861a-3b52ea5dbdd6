package com.dataxai.web.controller.ImageController;

import com.dataxai.web.domain.MaterialStyleCategory;
import com.dataxai.web.service.MaterialStyleCategoryService;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/material/style/category")
@Api(tags = "素材风格分类管理")
public class MaterialStyleCategoryController {

    @Autowired
    private MaterialStyleCategoryService categoryService;

    @GetMapping("/page")
    @ApiOperation("分页查询素材风格分类")
    public R<PageResult<MaterialStyleCategory>> queryPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer taskType) {

        List<MaterialStyleCategory> list = categoryService.queryPage(pageNum, pageSize, name, taskType);
        int total = categoryService.countByCondition(name, taskType);

        PageResult<MaterialStyleCategory> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);

        return R.ok(pageResult);
    }

    @PostMapping
    @ApiOperation("新增素材风格分类")
    public R<Boolean> addMaterialStyleCategory(@RequestBody MaterialStyleCategory category) {
        boolean result = categoryService.addMaterialStyleCategory(category);
        return result ? R.ok(true) : R.fail("新增失败");
    }

    @PutMapping
    @ApiOperation("修改素材风格分类")
    public R<Boolean> updateMaterialStyleCategory(@RequestBody MaterialStyleCategory category) {
        boolean result = categoryService.updateMaterialStyleCategory(category);
        return result ? R.ok(true) : R.fail("修改失败");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除素材风格分类")
    public R<Boolean> deleteMaterialStyleCategory(@PathVariable Integer id) {
        try {
            boolean result = categoryService.deleteMaterialStyleCategory(id);
            return result ? R.ok(true) : R.fail("删除失败");
        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取素材风格分类详情")
    public R<MaterialStyleCategory> getById(@PathVariable Integer id) {
        MaterialStyleCategory category = categoryService.getById(id);
        return category != null ? R.ok(category) : R.fail("数据不存在");
    }
}