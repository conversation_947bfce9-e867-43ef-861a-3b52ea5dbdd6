package com.dataxai.web.service;

import com.alibaba.fastjson2.JSON;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.domain.model.LoginUser;
import com.dataxai.common.core.domain.model.User;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.framework.web.service.TokenService;
import com.dataxai.web.Constants.CommonStatusEnum;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.Constants.IdentityConstant;
import com.dataxai.web.core.config.ConstantAliYunSmsPropertiesUtils;
import com.dataxai.web.domain.NumberCodeResponse;
import com.dataxai.web.domain.TokenResponse;
import com.dataxai.web.utils.MapBuilder;
import com.dataxai.web.utils.redis.RedisPrefixUtils;
import com.google.gson.Gson;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @version 1.0
 * @Author:xg
 */
@Slf4j
@Service
public class VerificationCodeService {

    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    IFrontUserService userService;

    @Autowired
    private TokenService tokenService;

    public R<NumberCodeResponse> getVerificationCode(String phone) {
        if(StringUtils.isEmpty(phone)){
            return R.fail("短信验证码为空");
        }
        int numberCode = getNumberCode(6);
        boolean isProd = Constants.ACTIVE_PROD.equals(active);
        try {
            sendSms(phone,numberCode);
        }catch (ServiceException e){
            throw new ServiceException(e.getMessage(),e.getCode());
        } catch (Exception e) {
            log.error("短信发送异常", e);
            throw new ServiceException("短信发送失败");
        }

        System.out.println("用户端页面收到的验证码： "+numberCode);
        String redisKey = RedisPrefixUtils.generateRedisVerificationCodeKey(phone,IdentityConstant.FRONT_USER_IDENTITY);
        int timeout = 10;
        stringRedisTemplate.opsForValue().set(redisKey,numberCode+"",timeout, TimeUnit.MINUTES);
        NumberCodeResponse numberCodeResponse = new NumberCodeResponse();
        if(!isProd){
            numberCodeResponse.setNumberCode(numberCode);
        }
        // 通过短信服务商，将对应的验证码发送到手机上。阿里短信服务，腾讯短信通，华信，容联
        return R.ok(null);
    }

    private void sendSms(String phone, int numberCode) throws Exception{
        // 限流，限制1分钟内不能重复发送短信
        String verifyTimeStr = stringRedisTemplate.opsForValue().get(Constants.SMS_PER + phone);
        if (com.dataxai.common.utils.StringUtils.isNotEmpty(verifyTimeStr)){
            Long verifyTime = Long.valueOf(verifyTimeStr);
            if (System.currentTimeMillis() - verifyTime < Constants.SMS_MIN_INTERVAL_MINUTE){
                throw new ServiceException("1分钟内不能重复发送短信",Constants.SMS_MIN_INTERVAL_MINUTE_ERROR_CODE);
            }
        }

        String sign = ConstantAliYunSmsPropertiesUtils.aliYunSms_sign;
        String templateCode = ConstantAliYunSmsPropertiesUtils.aliYunSms_code;
        String region = ConstantAliYunSmsPropertiesUtils.aliYunSms_region;
        String ak = ConstantAliYunSmsPropertiesUtils.aliYunSms_ak;
        String sk = ConstantAliYunSmsPropertiesUtils.aliYunSms_sk;

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(ak)
                .accessKeySecret(sk)
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                )
                .build();

        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                .phoneNumbers(phone)
                .signName(sign)
                .templateCode(templateCode)
                .templateParam(JSON.toJSONString(MapBuilder.newBuilder()
                        .put("code", numberCode)
                        .build()))
                .build();
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        SendSmsResponse resp = response.get();
        System.out.println(new Gson().toJson(resp));
        client.close();
        stringRedisTemplate.opsForValue().set(Constants.SMS_PER + phone, String.valueOf(System.currentTimeMillis()), 1, TimeUnit.MINUTES);
    }

    public String verificationCodeCheck(String frontUserPhone,String verificationCode,String openId,Integer type) throws NoSuchAlgorithmException {
        //从redis，根据手机号，获取到验证码
        if(type == 1){
            String redisKey = RedisPrefixUtils.generateRedisVerificationCodeKey(frontUserPhone,IdentityConstant.FRONT_USER_IDENTITY);
            String redisCode = stringRedisTemplate.opsForValue().get(redisKey);
            System.out.println("redisCode:   "+redisCode);
            //校验验证码
            if(!verificationCode.equals(redisCode)){
                throw new ServiceException(CommonStatusEnum.VERIFICATION_CODE_ERROR.getMessage());
            }
        }else if(type == 2){
            //根据手机号查询用户
            User user = userService.selectOneByPhonePassword(frontUserPhone);
            if(user != null){
                //老用户 判断密码  密码哈希
                StringBuilder hexString = passwordHash(verificationCode);
                if(! user.getPassword().equals( hexString.toString())){
                    throw new ServiceException("用户密码错误");
                }
            }else {
                throw new ServiceException("用户不存在 请验证码登录");
            }
        }
        // 判断原来是否有用户，并进行相应的处理
        userService.loginOrRegisterByPhone(frontUserPhone);
        User user = userService.getUserByPhone(frontUserPhone);
        if(null == user){
            throw new ServiceException("用户不存在");
        }
        if(user.getEnable() == Constants.ENABLE_1){
            throw new ServiceException(Constants.ENABLE_1_Str);
        }
        // 生成token
        userService.refreshLoginTime(user.getUserId());
        LoginUser loginUser = new LoginUser(user);
        System.out.println(loginUser.getAppUser().toString());
        String token = tokenService.createToken(loginUser);
        return token;

    }

    /**
     * 密码哈希
     * @param verificationCode
     * @return
     * @throws NoSuchAlgorithmException
     */
    private StringBuilder passwordHash(String verificationCode) throws NoSuchAlgorithmException {
        StringBuilder hexString = new StringBuilder();
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] encodedhash = digest.digest(verificationCode.getBytes());

        for (byte b : encodedhash) {
            String hex = Integer.toHexString(0xff & b);
            if(hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString;

    }

    private int getNumberCode(int size){
        return generateRandomCode(size);
    }

    private int generateRandomCode(int size){
        double random = (Math.random()*9+1)*Math.pow(10,size-1);
        System.out.println(random);
        return (int) random;
    }
}
