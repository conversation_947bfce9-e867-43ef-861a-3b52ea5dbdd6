package com.dataxai.web.controller.ImageController;

import com.dataxai.common.core.domain.R;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.web.dto.MaterialIpWithFavoriteDTO;
import com.dataxai.web.dto.MaterialStyleWithFavoriteDTO;
import com.dataxai.web.service.MaterialWithFavoriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 前端素材展示控制器
 * 提供带收藏状态的素材查询接口
 */
@RestController
@RequestMapping("/front/material")
@Api(tags = "前端素材展示")
public class MaterialFrontController {

    @Autowired
    private MaterialWithFavoriteService materialWithFavoriteService;

    /**
     * 获取用户ID的工具方法（用于可选登录的接口）
     * @param userId 传入的用户ID
     * @return 用户ID，如果无法获取则返回null
     */
    private Integer getUserIdOptional(Integer userId) {
        if (userId != null) {
            return userId;
        }

        try {
            Long userIdLong = SecurityUtils.getUserId();
            return userIdLong.intValue();
        } catch (Exception e) {
            // 用户未登录，返回null，表示不显示收藏状态
            return null;
        }
    }

    /**
     * 获取用户ID的工具方法（用于必须登录的接口）
     * @param userId 传入的用户ID
     * @return 用户ID，如果传入为null则从登录信息获取
     * @throws RuntimeException 如果无法获取用户ID
     */
    private Integer getUserIdRequired(Integer userId) {
        if (userId != null) {
            return userId;
        }

        try {
            Long userIdLong = SecurityUtils.getUserId();
            return userIdLong.intValue();
        } catch (Exception e) {
            throw new RuntimeException("用户未登录或用户ID获取失败");
        }
    }

    @GetMapping("/ip/page")
    @ApiOperation("分页查询IP素材（带收藏状态）- 支持未登录用户")
    public R<PageResult<MaterialIpWithFavoriteDTO>> queryIpPageWithFavorite(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取，未登录时收藏状态为false）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "IP名称") @RequestParam(required = false) String name,
            @ApiParam(value = "状态(0-禁用,1-启用)") @RequestParam(required = false) Integer status,
            @ApiParam(value = "分类ID") @RequestParam(required = false) Integer categoryId) {

        // 获取用户ID，如果未登录则为null（表示所有素材收藏状态为false）
        userId = getUserIdOptional(userId);

        PageResult<MaterialIpWithFavoriteDTO> result = materialWithFavoriteService.queryIpPageWithFavorite(
                userId, pageNum, pageSize, name, status, categoryId);
        return R.ok(result);
    }

    @GetMapping("/style/page")
    @ApiOperation("分页查询风格素材（带收藏状态）- 支持未登录用户")
    public R<PageResult<MaterialStyleWithFavoriteDTO>> queryStylePageWithFavorite(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取，未登录时收藏状态为false）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "风格名称") @RequestParam(required = false) String name,
            @ApiParam(value = "状态(0-禁用,1-启用)") @RequestParam(required = false) Integer status,
            @ApiParam(value = "分类ID") @RequestParam(required = false) Integer categoryId,
            @ApiParam(value = "任务类型：6-平铺图文生图，8-文生图") @RequestParam(required = false) Integer taskType) {

        // 获取用户ID，如果未登录则为null（表示所有素材收藏状态为false）
        userId = getUserIdOptional(userId);

        PageResult<MaterialStyleWithFavoriteDTO> result = materialWithFavoriteService.queryStylePageWithFavorite(
                userId, pageNum, pageSize, name, status, categoryId, taskType);
        return R.ok(result);
    }

    @GetMapping("/ip/favorites")
    @ApiOperation("获取用户收藏的IP素材列表")
    public R<PageResult<MaterialIpWithFavoriteDTO>> getUserFavoriteIps(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            userId = getUserIdRequired(userId);
        } catch (RuntimeException e) {
            return R.fail(e.getMessage());
        }

        PageResult<MaterialIpWithFavoriteDTO> result = materialWithFavoriteService.getUserFavoriteIps(
                userId, pageNum, pageSize);
        return R.ok(result);
    }

    @GetMapping("/style/favorites")
    @ApiOperation("获取用户收藏的风格素材列表")
    public R<PageResult<MaterialStyleWithFavoriteDTO>> getUserFavoriteStyles(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "任务类型：6-平铺图文生图，8-文生图") @RequestParam(required = false) Integer taskType) {

        try {
            userId = getUserIdRequired(userId);
        } catch (RuntimeException e) {
            return R.fail(e.getMessage());
        }

        PageResult<MaterialStyleWithFavoriteDTO> result = materialWithFavoriteService.getUserFavoriteStyles(
                userId, pageNum, pageSize, taskType);
        return R.ok(result);
    }

    // ========== 最近使用相关接口 ==========

    @GetMapping("/ip/recent")
    @ApiOperation("获取用户最近使用的IP素材列表（7天内）")
    public R<PageResult<MaterialIpWithFavoriteDTO>> getUserRecentIps(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            userId = getUserIdRequired(userId);
        } catch (RuntimeException e) {
            return R.fail(e.getMessage());
        }

        PageResult<MaterialIpWithFavoriteDTO> result = materialWithFavoriteService.getUserRecentIps(
                userId, pageNum, pageSize);
        return R.ok(result);
    }

    @GetMapping("/style/recent")
    @ApiOperation("获取用户最近使用的风格素材列表（7天内）")
    public R<PageResult<MaterialStyleWithFavoriteDTO>> getUserRecentStyles(
            @ApiParam(value = "用户ID（可选，不传则从登录信息获取）") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "任务类型：6-平铺图文生图，8-文生图") @RequestParam(required = false) Integer taskType) {

        try {
            userId = getUserIdRequired(userId);
        } catch (RuntimeException e) {
            return R.fail(e.getMessage());
        }

        PageResult<MaterialStyleWithFavoriteDTO> result = materialWithFavoriteService.getUserRecentStyles(
                userId, pageNum, pageSize, taskType);
        return R.ok(result);
    }
}
