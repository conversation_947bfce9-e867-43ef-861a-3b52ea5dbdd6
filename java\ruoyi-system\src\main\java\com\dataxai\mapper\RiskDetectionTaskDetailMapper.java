package com.dataxai.mapper;

import java.util.List;
import com.dataxai.domain.RiskDetectionTaskDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 风险检测任务详情表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RiskDetectionTaskDetailMapper 
{
    /**
     * 查询风险检测任务详情表
     * 
     * @param id 风险检测任务详情表主键
     * @return 风险检测任务详情表
     */
    public RiskDetectionTaskDetail selectRiskDetectionTaskDetailById(Long id);

    /**
     * 查询风险检测任务详情表列表
     * 
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 风险检测任务详情表集合
     */
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailList(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 根据任务ID查询详情列表
     * 
     * @param taskId 任务ID
     * @return 风险检测任务详情表集合
     */
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailByTaskId(Long taskId);

    /**
     * 根据任务ID和多个风险等级查询详情列表
     * 
     * @param taskId 任务ID
     * @param riskLevels 风险等级列表
     * @return 风险检测任务详情表集合
     */
    public List<RiskDetectionTaskDetail> selectRiskDetectionTaskDetailByTaskIdAndRiskLevels(@Param("taskId") Long taskId, @Param("riskLevels") List<String> riskLevels);

    /**
     * 新增风险检测任务详情表
     * 
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    public int insertRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 修改风险检测任务详情表
     * 
     * @param riskDetectionTaskDetail 风险检测任务详情表
     * @return 结果
     */
    public int updateRiskDetectionTaskDetail(RiskDetectionTaskDetail riskDetectionTaskDetail);

    /**
     * 删除风险检测任务详情表
     * 
     * @param id 风险检测任务详情表主键
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailById(Long id);

    /**
     * 批量删除风险检测任务详情表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailByIds(Long[] ids);

    /**
     * 根据任务ID删除详情
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public int deleteRiskDetectionTaskDetailByTaskId(Long taskId);

    /**
     * 批量更新风险检测任务详情的has_uploaded字段
     * 
     * @param imageUrls 图片URL列表
     * @param hasUploaded 是否已上传
     * @return 结果
     */
    public int batchUpdateHasUploaded(@Param("imageUrls") List<String> imageUrls, @Param("hasUploaded") boolean hasUploaded);

    int updateRiskDetectionTaskDetailAbandoned(RiskDetectionTaskDetail riskDetectionTaskDetail);
}