package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 风格素材使用历史表
 * @TableName t_material_style_history
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialStyleHistory {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "历史记录ID")
    private Integer id;

    /**
     * 风格素材ID
     */
    @ApiModelProperty(value = "风格素材ID")
    private Integer materialStyleId;

    /**
     * 任务类型：6-平铺图文生图，8-文生图
     */
    @ApiModelProperty(value = "任务类型：6-平铺图文生图，8-文生图")
    private Integer taskType;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
