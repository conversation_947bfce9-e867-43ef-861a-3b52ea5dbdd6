package com.dataxai.web.mapper;

import com.dataxai.web.domain.CropExtractCategory;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface CropExtractCategoryMapper {
	List<CropExtractCategory> selectByCondition(@Param("name") String name,
	                                            @Param("status") Integer status);

	CropExtractCategory selectById(@Param("id") Integer id);

	int insert(CropExtractCategory category);

	int update(CropExtractCategory category);

	int deleteById(@Param("id") Integer id);

	int countByCondition(@Param("name") String name,
	                     @Param("status") Integer status);

	List<CropExtractCategory> selectPageByCondition(@Param("offset") int offset,
	                                                @Param("pageSize") int pageSize,
	                                                @Param("name") String name,
	                                                @Param("status") Integer status);
} 