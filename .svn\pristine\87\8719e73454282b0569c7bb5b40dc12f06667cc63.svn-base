---
description:
globs:
alwaysApply: false
---
### 构建与运行

#### 后端（Java, Maven）
- 清理/打包/运行脚本（Windows）：
  - 清理：[bin/clean.bat](mdc:bin/clean.bat)
  - 打包：[bin/package.bat](mdc:bin/package.bat)
  - 运行：[bin/run.bat](mdc:bin/run.bat)
- 典型 Maven 命令：
  - `mvn -T 1C -DskipTests clean package`
  - 单模块运行：在 `ruoyi-admin` 目录使用 `mvn spring-boot:run -Dspring-boot.run.profiles=dev`
- 配置切换：在 [ruoyi-admin/src/main/resources/application.yml](mdc:ruoyi-admin/src/main/resources/application.yml) 中使用 `spring.profiles.active`

#### 前端管理端（ruoyi-ui/admin）
- 入口目录：`ruoyi-ui/admin`
- 脚本：
  - 运行开发环境：[ruoyi-ui/admin/bin/run-web.bat](mdc:ruoyi-ui/admin/bin/run-web.bat)
  - 打包：[ruoyi-ui/admin/bin/build.bat](mdc:ruoyi-ui/admin/bin/build.bat)
  - 包管理器：`pnpm`（参考 [ruoyi-ui/admin/pnpm-lock.yaml](mdc:ruoyi-ui/admin/pnpm-lock.yaml)）
- 常用命令（在 `ruoyi-ui/admin` 执行）：
  - `pnpm install`
  - `pnpm run dev` 或 `pnpm run serve`
  - `pnpm run build`

#### 日志与排错
- 日志目录：[logs/](mdc:logs)（按日期、级别分类）
- 常见配置项：端口、数据源、Redis、MQ 等在 `application-*.yml` 中
- 若前后端联调，确保跨域或网关配置正确，参见后端 `spring` 与 `security` 配置段落
