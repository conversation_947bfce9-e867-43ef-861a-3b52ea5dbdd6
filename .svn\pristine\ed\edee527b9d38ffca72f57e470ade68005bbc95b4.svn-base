import React, { useEffect, useMemo, useState } from 'react';
import { Button, DatePicker, Input , Select} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { getUserInfo } from '@/api/common'
import { getTeamMembers } from '@/api/task'
import { get } from 'lodash';
const { Option } = Select;

type RangeValue = [Dayjs | null, Dayjs | null] | null;

export type FilterField = 'batchNumber' | 'remark' | 'dateRange' | 'imageColor' | 'userId' | 'taskStatus';

export interface FilterParams {
	batchNumber?: string;
	remark?: string;// 备注筛选（可选）
	// 原始字符串日期（YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss，按 withTime 决定）
	stringDateRange: string[];
	imageColor?: string; // 颜色筛选（可选）
	userId?: string; // 账号号筛选（可选）
	taskStatus?: string; // 任务状态筛选（可选）
	// 处理后的时间（带 00:00:00 / 23:59:59 或精确到秒，按 withTime 决定）
	startTime: string;
	endTime: string;
	type: any; // 类型筛选（可选）

}

export interface FilterBarProps {
	fields?: FilterField[];                 // 需要展示的字段
	storageKey?: string;                    // sessionStorage key，用于记住查询条件
	withTime?: boolean;                     // 日期范围是否带时分秒
	defaults?: Partial<Pick<FilterParams, 'batchNumber' | 'remark' | 'stringDateRange' | 'imageColor' | 'userId' | 'taskStatus'>>; // 初始默认值
	onSearch: (params: FilterParams) => void; // 点击查询时回调
	onReset?: (params: FilterParams) => void;  // 点击重置时回调（可选）
	className?: string;                     // 外层样式
	type?: any; // 类型筛选（可选）
}

/**
 * 通用筛选条（批次号/备注/日期范围）
 * - 内部管理状态并同步到 sessionStorage（可选）
 * - 输出 stringDateRange 与标准化的 startTime/endTime
 */
const FilterBar: React.FC<FilterBarProps> = ({
	fields = ['batchNumber', 'dateRange'],
	storageKey,
	withTime = false,
	defaults,
	onSearch,
	onReset,
	className,
	type
}) => {
	// 本地状态
	const [batchNumber, setBatchNumber] = useState<string>(defaults?.batchNumber ?? '');
	const [remark, setRemark] = useState<string>(defaults?.remark ?? '');
	const [imageColor, setImageColor] = useState<string>(defaults?.imageColor ?? '');
	const [userId, setUserId] = useState<string>('');
	const [taskStatus, setTaskStatus] = useState<string>(defaults?.taskStatus ?? '');
	const [queryType, setQueryType] = useState<string>(type??'');
	const [dateRange, setDateRange] = useState<RangeValue>(null);
	const [stringDateRange, setStringDateRange] = useState<string[]>(defaults?.stringDateRange ?? []);
	const [teamId, setTeamId] = useState<any>(null);
	const [selectedTeam, setSelectedTeam] = useState<Array <any> | null>([]);
	
	// 初始化：从 sessionStorage 回填
	useEffect(() => {
		if (!storageKey) return;
		try {
			const saved = sessionStorage.getItem(storageKey);
			if (saved) {
				const parsed = JSON.parse(saved) as {
					batchNumber?: string;
					remark?: string;
					imageColor?: string;
					userId?: string;
					taskStatus?: string;
					stringDateRange?: string[];
				};
				setBatchNumber(parsed.batchNumber ?? '');
				setRemark(parsed.remark ?? '');
				setImageColor(parsed.imageColor ?? '');
				setUserId(parsed.userId ?? '');
				setTaskStatus(parsed.taskStatus ?? '');
				const sdr = parsed.stringDateRange ?? [];
				setStringDateRange(sdr);
				if (sdr.length === 2) {
					setDateRange([
						withTime ? dayjs(sdr[0], 'YYYY-MM-DD HH:mm:ss') : dayjs(sdr[0], 'YYYY-MM-DD'),
						withTime ? dayjs(sdr[1], 'YYYY-MM-DD HH:mm:ss') : dayjs(sdr[1], 'YYYY-MM-DD'),
					]);
				}
			}
		} catch {
			// ignore
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [storageKey]);
	//获取团队id
	useEffect(() => { 
		getUserInfo().then(res => { 
			setTeamId(res.teamId)

			getTeamMembers({ teamId: res.teamId }).then((res:any) => { 
				
				setSelectedTeam(res.data)
			});
		});
	}, []);

	// 统一生成 startTime / endTime
	const computedTime = useMemo(() => {
		let startTime = '';
		let endTime = '';
		if (stringDateRange.length === 2) {
			if (withTime) {
				startTime = stringDateRange[0] ?? '';
				endTime = stringDateRange[1] ?? '';
			} else {
				const s = stringDateRange[0] ? `${stringDateRange[0]} 00:00:00` : '';
				const e = stringDateRange[1] ? `${stringDateRange[1]} 23:59:59` : '';
				startTime = s; endTime = e;
			}
		}
		return { startTime, endTime };
	}, [stringDateRange, withTime]);

	// 写入 sessionStorage
	const persist = (next: { batchNumber: string; remark: string; imageColor: string; userId: string; taskStatus: string; stringDateRange: string[] }) => {
		if (!storageKey) return;
		try {
			sessionStorage.setItem(storageKey, JSON.stringify(next));
		} catch {
			// ignore
		}
	};

	// 查询
	const handleSearch = () => {
		
		const next = {
			batchNumber,
			remark,
			imageColor,
			userId,
			taskStatus,
			stringDateRange
		};
		persist(next);
		onSearch({
			...next,
			startTime: computedTime.startTime,
			endTime: computedTime.endTime,
			type: queryType,
		});
	};

	// 重置
	const handleReset = () => {
		setBatchNumber('');
		setRemark('');
		setImageColor('');
		setUserId('');
		setTaskStatus('');
		setDateRange(null);
		setStringDateRange([]);
		const next = { batchNumber: '', remark: '', imageColor: '', userId: '', taskStatus: '', stringDateRange: [] as string[] };
		persist(next);
		const params: FilterParams = {
			type: queryType,
			...next,
			startTime: '',
			endTime: ''
		};
		onReset ? onReset(params) : onSearch(params);
	};

	return (
		<div className={className ?? 'flex items-center flex-wrap gap-2'}>
			{fields.includes('batchNumber') && (
				<Input
					placeholder="批次号"
					style={{ width: 200 }}
					value={batchNumber}
					onChange={(e) => setBatchNumber(e.target.value)}
					allowClear
				/>
			)}
			{fields.includes('remark') && (
				<Input
					placeholder="备注信息查询"
					style={{ width: 200 }}
					value={remark}
					onChange={(e) => setRemark(e.target.value)}
					allowClear
				/>
			)}
			{fields.includes('imageColor') && (
				<Input
					placeholder="图片颜色"
					style={{ width: 200 }}
					value={imageColor}
					onChange={(e) => setImageColor(e.target.value)}
					allowClear
				/>
			)}
			{fields.includes('userId') && (
				<Select
					placeholder="请选择要查看的账号"
					style={{ width: 200 }}
					value={userId || undefined}
					onChange={(value) => setUserId(value)}
					allowClear
					showSearch
				>
					{selectedTeam && selectedTeam.map((item: any) => (
						<Option key={item.phonenumber} value={item.userId}>
							{item.nickname===""?item.phonenumber:item.nickname}
						</Option>
					))}
				</Select>
			)}
			{fields.includes('taskStatus') && (
				<Select
					placeholder="任务状态"
					style={{ width: 140 }}
					value={taskStatus}
					onChange={(value) => setTaskStatus(value)}
					allowClear
				>
					<Option value="">全部状态</Option>
					<Option value="1">排队中</Option>
					<Option value="2">执行中</Option>
					<Option value="3">已完成</Option>
				</Select>
			)}
			{fields.includes('dateRange') && (
				<DatePicker.RangePicker
					style={{ width: withTime ? 320 : 260 }}
					value={dateRange as any}
					onChange={(dates, dateStrings) => {
						setDateRange(dates as RangeValue);
						setStringDateRange(dateStrings as string[]);
					}}
					format={withTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'}
					{...(withTime ? { showTime: { format: 'HH:mm:ss' } } : {})}
					disabledDate={(current) => current && current > dayjs().endOf('day')}
				/>
			)}
			<Button type="primary" onClick={handleSearch}>查询</Button>
			<Button onClick={handleReset}>重置</Button>
		</div>
	);
};

export default FilterBar;