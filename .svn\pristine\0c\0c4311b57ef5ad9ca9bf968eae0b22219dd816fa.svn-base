package com.dataxai.web.task.queue.socket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.corundumstudio.socketio.AckRequest;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.OnConnect;
import com.corundumstudio.socketio.annotation.OnDisconnect;
import com.corundumstudio.socketio.annotation.OnEvent;
import com.dataxai.web.domain.PushDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Socket.IO服务器处理器
 * 优化后的Socket.IO连接和消息处理
 */
@Slf4j
@Component
public class SocketIOServerHandler {

    @Autowired
    private ClientCache clientCache;

    @Autowired
    private SocketIOServer socketIOServer;

    @Autowired
private SocketIOMonitor monitor;

@Autowired
private SocketIOExceptionHandler exceptionHandler;

    // 配置参数
    private static final int MAX_RETRY_ATTEMPTS = 3;

    /**
     * 建立连接
     */
    @OnConnect
    public void onConnect(SocketIOClient client) {
        String userId = null;
        UUID sessionId = null;

        try {
            userId = client.getHandshakeData().getSingleUrlParam("userId");
            sessionId = client.getSessionId();

            // 参数验证 - 改进验证逻辑
            if (!StringUtils.hasText(userId)) {
                // 尝试从其他来源获取userId
                userId = extractUserIdFromClient(client);

                if (!StringUtils.hasText(userId)) {
                    // log.warn("连接建立失败：无法获取userId - sessionId: {}", sessionId);
                    client.disconnect();
                    return;
                }
            }

            // 验证userId格式
            if (!isValidUserId(userId)) {
                log.warn("连接建立失败：userId格式无效 - userId: {}, sessionId: {}", userId, sessionId);
                client.disconnect();
                return;
            }

            // 保存用户连接信息（ClientCache内部会调用monitor.recordConnection）
            clientCache.saveClient(userId, sessionId, client);

            // 发送连接成功消息
            sendConnectionSuccessMessage(client, userId);

            log.debug("Socket.IO连接建立成功 - 用户: {}, 会话: {}", userId, sessionId);

        } catch (Exception e) {
            log.error("连接建立异常 - 用户: {}, 会话: {}", userId, sessionId, e);
            exceptionHandler.handleConnectionException(e, userId != null ? userId : "unknown", "onConnect");
            try {
                client.disconnect();
            } catch (Exception disconnectException) {
                log.error("断开异常连接失败", disconnectException);
            }
        }
    }

    /**
     * 关闭连接
     */
    @OnDisconnect
    public void onDisconnect(SocketIOClient client) {
        String userId = null;
        UUID sessionId = null;

        try {
            userId = client.getHandshakeData().getSingleUrlParam("userId");
            sessionId = client.getSessionId();

            if (StringUtils.hasText(userId)) {
                // ClientCache内部会调用monitor.recordDisconnection
                clientCache.deleteSessionClientByUserId(userId, sessionId);
                log.debug("Socket.IO连接断开 - 用户: {}, 会话: {}", userId, sessionId);
            } else {
                // log.warn("连接断开：userId为空 - sessionId: {}", sessionId);
                // 即使userId为空，也要尝试清理可能的缓存
                try {
                    // 从ClientCache中查找并清理这个sessionId的连接
                    clientCache.deleteSessionBySessionId(sessionId);
                } catch (Exception cleanupException) {
                    log.debug("清理空userId连接失败 - sessionId: {}", sessionId, cleanupException);
                }
            }

        } catch (Exception e) {
            log.error("连接断开处理异常 - 用户: {}, 会话: {}", userId, sessionId, e);
            exceptionHandler.handleConnectionException(e, userId != null ? userId : "unknown", "onDisconnect");
        }
    }

    /**
     * 处理用户房间查询事件
     */
    @OnEvent("getUserRooms")
    public void getUserRooms(SocketIOClient client, String data, AckRequest ackRequest) {
        try {
            String userId = client.getHandshakeData().getSingleUrlParam("userId");
            Set<String> allRooms = client.getAllRooms();

            log.debug("用户房间查询 - 用户: {}, 消息: {}, 房间数: {}", userId, data, allRooms.size());

            if (ackRequest.isAckRequested()) {
                JSONObject response = new JSONObject();
                response.put("rooms", allRooms);
                response.put("message", "查询成功");
                ackRequest.sendAckData(response);
            }

        } catch (Exception e) {
            log.error("处理用户房间查询异常", e);
            exceptionHandler.handleMessageException(e, "unknown", "getUserRooms");

            if (ackRequest.isAckRequested()) {
                ackRequest.sendAckData("error", "查询失败");
            }
        }
    }

    /**
     * 点对点消息发送
     */
    public boolean sendMessageOne(String userId, String content) {
        return sendMessageOne(userId, content, "message");
    }

    /**
     * 点对点消息发送（指定事件类型）
     */
    public boolean sendMessageOne(String userId, String content, String eventType) {

        if (!StringUtils.hasText(userId) || !StringUtils.hasText(content)) {
            log.warn("=== 消息发送失败：参数无效 ===");
            log.warn("userId: {}, content长度: {}", userId, content != null ? content.length() : 0);
            return false;
        }

        HashMap<UUID, SocketIOClient> userClients = clientCache.getUserClient(userId);

        if (userClients == null || userClients.isEmpty()) {
            log.warn("=== 用户无活跃连接 ===");
            log.warn("用户: {}, 连接数: {}", userId, userClients != null ? userClients.size() : 0);
            return false;
        }

        int successCount = 0;

        for (UUID sessionId : userClients.keySet()) {
            if (sendMessageToSession(sessionId, content, eventType, userId)) {
                successCount++;
            } else {
                log.warn("会话 {} 消息发送失败", sessionId);
            }
        }

        // 记录监控信息
        monitor.recordMessageSent(userId, eventType, content.length());

        boolean success = successCount > 0;

        return success;
    }

    /**
     * 向指定会话发送消息
     */
    private boolean sendMessageToSession(UUID sessionId, String content, String eventType, String userId) {

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {

            try {
                SocketIOClient client = socketIOServer.getClient(sessionId);

                if (client == null || !client.isChannelOpen()) {
                    log.warn("=== 客户端连接无效 ===");
                    log.warn("会话: {}, 用户: {}, 客户端: {}, 通道状态: {}",
                        sessionId, userId, client, client != null ? client.isChannelOpen() : "null");
                    return false;
                }

                // 使用异步发送，避免阻塞
                CompletableFuture.runAsync(() -> {
                    try {
                        client.sendEvent(eventType, content);
                    } catch (Exception e) {
                        log.error("=== 异步消息发送异常 ===");
                        log.error("会话: {}, 用户: {}, 异常详情: {}", sessionId, userId, e.getMessage(), e);
                        exceptionHandler.handleMessageException(e, userId, eventType);
                    }
                });
                return true;
            } catch (Exception e) {
                log.warn("=== 消息发送失败，第{}次尝试 ===", attempt);
                log.warn("会话: {}, 用户: {}, 异常: {}", sessionId, userId, e.getMessage());

                if (attempt == MAX_RETRY_ATTEMPTS) {
                    log.error("=== 达到最大重试次数，发送失败 ===");
                    exceptionHandler.handleMessageException(e, userId, eventType);
                    return false;
                }

                // 重试前等待
                try {
                    log.info("等待 {}ms 后重试", 100 * attempt);
                    Thread.sleep(100 * attempt);
                } catch (InterruptedException ie) {
                    log.warn("重试等待被中断");
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }

        log.error("=== sendMessageToSession 失败 ===");
        return false;
    }

    /**
     * 广播消息给所有用户
     */
    public void broadcastMessage(String content, String eventType) {
        if (!StringUtils.hasText(content)) {
            log.warn("广播消息失败：内容为空");
            return;
        }

        java.util.Set<String> allUserIds = clientCache.getAllUserIds();
        int successCount = 0;

        for (String userId : allUserIds) {
            if (sendMessageOne(userId, content, eventType)) {
                successCount++;
            }
        }

        log.info("广播消息完成 - 总用户数: {}, 成功发送: {}, 事件类型: {}",
                allUserIds.size(), successCount, eventType);
    }

    /**
     * 发送连接成功消息
     */
    private void sendConnectionSuccessMessage(SocketIOClient client, String userId) {
        try {
            // 验证userId是否为有效的数字
            if (!StringUtils.hasText(userId) || "undefined".equals(userId) || "null".equals(userId)) {
                log.warn("发送连接成功消息失败：无效的userId - {}", userId);
                return;
            }

            // 尝试解析userId为Long类型
            Long userIdLong;
            try {
                userIdLong = Long.valueOf(userId);
            } catch (NumberFormatException e) {
                log.warn("发送连接成功消息失败：userId不是有效的数字 - {}", userId);
                return;
            }

            PushDTO<JSONObject> pushDTO = new PushDTO<>();
            pushDTO.setUserId(userIdLong);
            pushDTO.setPushType(0); // 连接成功类型

            JSONObject message = new JSONObject();
            message.put("status", "connected");
            message.put("timestamp", System.currentTimeMillis());
            pushDTO.setMessage(message);

            String response = JSON.toJSONString(pushDTO);
            client.sendEvent("connection", response);

            log.debug("发送连接成功消息 - 用户: {}", userId);

        } catch (Exception e) {
            log.error("发送连接成功消息异常 - 用户: {}", userId, e);
        }
    }

    /**
     * 检查用户连接状态
     */
    public boolean isUserConnected(String userId) {
        try {
            boolean hasConnection = clientCache.hasActiveConnection(userId);

            // 获取更详细的连接信息
            HashMap<UUID, SocketIOClient> userClients = clientCache.getUserClient(userId);

            if (userClients != null && !userClients.isEmpty()) {

            } else {
                log.warn("用户 {} 没有活跃连接", userId);
            }

            return hasConnection;
        } catch (Exception e) {
            log.error("=== 检查用户连接状态异常 ===");
            log.error("用户ID: {}, 异常详情: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取用户连接数
     */
    public int getUserConnectionCount(String userId) {
        return clientCache.getUserActiveConnectionCount(userId);
    }

    /**
     * 强制断开用户连接
     */
    public void forceDisconnectUser(String userId) {
        try {
            HashMap<UUID, SocketIOClient> userClients = clientCache.getUserClient(userId);
            if (userClients != null) {
                for (SocketIOClient client : userClients.values()) {
                    if (client != null && client.isChannelOpen()) {
                        client.disconnect();
                    }
                }
            }
            clientCache.deleteUserCacheByUserId(userId);
            log.info("强制断开用户连接 - 用户: {}", userId);

        } catch (Exception e) {
            log.error("强制断开用户连接异常 - 用户: {}", userId, e);
            exceptionHandler.handleConnectionException(e, userId, "forceDisconnect");
        }
    }

    /**
     * 从客户端提取用户ID
     * 尝试从多个来源获取userId
     */
    private String extractUserIdFromClient(SocketIOClient client) {
        try {
            // 1. 尝试从URL参数获取
            String userId = client.getHandshakeData().getSingleUrlParam("userId");
            if (StringUtils.hasText(userId)) {
                return userId;
            }

            // 2. 尝试从查询参数获取
            userId = client.getHandshakeData().getSingleUrlParam("user_id");
            if (StringUtils.hasText(userId)) {
                return userId;
            }

            // 3. 尝试从token参数获取并解析
            String token = client.getHandshakeData().getSingleUrlParam("token");
            if (StringUtils.hasText(token)) {
                userId = extractUserIdFromToken(token);
                if (StringUtils.hasText(userId)) {
                    return userId;
                }
            }

            // 4. 尝试从Authorization头获取
            String authHeader = client.getHandshakeData().getSingleUrlParam("authorization");
            if (StringUtils.hasText(authHeader)) {
                userId = extractUserIdFromAuthHeader(authHeader);
                if (StringUtils.hasText(userId)) {
                    return userId;
                }
            }

            return null;
        } catch (Exception e) {
            log.debug("提取userId失败", e);
            return null;
        }
    }

    /**
     * 验证用户ID格式
     */
    private boolean isValidUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return false;
        }

        // 明确拒绝无效值
        if ("undefined".equals(userId) || "null".equals(userId) || "NaN".equals(userId)) {
            return false;
        }

        try {
            // 检查是否为数字格式
            Long.parseLong(userId);
            return true;
        } catch (NumberFormatException e) {
            // 如果不是数字，检查其他格式
            return userId.matches("^[a-zA-Z0-9_-]+$") && userId.length() <= 50;
        }
    }

    /**
     * 从token中提取用户ID
     */
    private String extractUserIdFromToken(String token) {
        try {
            // 这里可以添加JWT token解析逻辑
            // 暂时返回null，需要根据实际的token格式实现
            return null;
        } catch (Exception e) {
            log.debug("从token提取userId失败", e);
            return null;
        }
    }

    /**
     * 从Authorization头提取用户ID
     */
    private String extractUserIdFromAuthHeader(String authHeader) {
        try {
            // 这里可以添加Authorization头解析逻辑
            // 暂时返回null，需要根据实际的认证格式实现
            return null;
        } catch (Exception e) {
            log.debug("从Authorization头提取userId失败", e);
            return null;
        }
    }
}