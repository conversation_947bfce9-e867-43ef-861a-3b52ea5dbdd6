package com.dataxai.web.service;

import com.dataxai.web.domain.MaterialStyleCategory;
import java.util.List;

public interface MaterialStyleCategoryService {
    List<MaterialStyleCategory> queryAll(String name, Integer taskType);

    MaterialStyleCategory getById(Integer id);

    boolean addMaterialStyleCategory(MaterialStyleCategory category);

    boolean updateMaterialStyleCategory(MaterialStyleCategory category);

    boolean deleteMaterialStyleCategory(Integer id);

    // 分页查询
    List<MaterialStyleCategory> queryPage(Integer pageNum, Integer pageSize, String name, Integer taskType);

    // 获取总数
    int countByCondition(String name, Integer taskType);
    
    // 检查分类是否被使用
    boolean isCategoryInUse(Integer categoryId);
}