package com.dataxai.web.gtask.scheduler;

import com.dataxai.domain.RiskDetectionTaskDetail;
import com.dataxai.web.gtask.processor.GTaskProcessor;
import com.dataxai.web.gtask.processor.RiskDetectionGTaskProcessor;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.gtask.config.GTaskTokenPoolsProps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 风险检测GTask调度器
 *
 * <AUTHOR>
 * @date 2025-01-26

 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "gtask.schedulers", name = "risk-detection.enabled", havingValue = "true", matchIfMissing = false)
public class RiskDetectionGTaskScheduler extends AbstractGTaskScheduler<RiskDetectionTaskDetail> {

    @Autowired
    private RiskDetectionGTaskProcessor riskDetectionProcessor;
    @Autowired
    private GTaskTokenPoolsProps tokenPoolsProps;
    @PostConstruct
    public void init() {
        initScheduler();
        TaskLogUtils.RiskFilter.info("风险检测GTask调度器初始化完成");
    }

    @Override
    protected GTaskProcessor<RiskDetectionTaskDetail> createTaskProcessor() {
        return riskDetectionProcessor;
    }

    /**
     * 每30秒执行一次风险检测任务调度
     * 可通过配置文件 gtask.intervals.risk_detection 调整执行间隔
     */
    @Scheduled(fixedRate = 30000)
    public void scheduleRiskDetectionTasks() {
        // 如果配置了 token-pools 的 risk_detection，则停用旧调度执行，交由令牌分发调度器处理
        if (tokenPoolsProps.get("risk_detection") != null) {
            return;
        }
        if (!gTaskConfig.isEnabled()) {
            return;
        }
        long startTime = System.currentTimeMillis();
        try {
            TaskLogUtils.RiskFilter.info("开始执行风险检测任务调度");
            executeSchedule();
            long duration = System.currentTimeMillis() - startTime;
            TaskLogUtils.RiskFilter.info("风险检测任务调度执行完成，耗时: {}ms", duration);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            TaskLogUtils.RiskFilter.error("风险检测任务调度执行失败，耗时: {}ms", duration, e);
        }
    }

    /**
     * 手动触发风险检测任务调度
     * 提供给管理接口调用
     */
    public void manualTrigger() {
        TaskLogUtils.RiskFilter.info("手动触发风险检测任务调度");
        scheduleRiskDetectionTasks();
    }

    /**
     * 获取调度器状态
     */
    public SchedulerStatus getSchedulerStatus() {
        String taskType = taskProcessor.getTaskType();
        SchedulerStatus status = new SchedulerStatus();
        status.setTaskType(taskType);
        status.setTaskDescription(taskProcessor.getTaskDescription());
        status.setEnabled(gTaskConfig.isEnabled());
        status.setExecuting(isExecuting); // 使用AbstractGTaskScheduler的执行标志
        status.setConcurrency(gTaskConfig.getConcurrency(taskType));
        status.setBatchSize(gTaskConfig.getBatchSize(taskType));
        status.setInterval(gTaskConfig.getInterval(taskType));
        status.setTimeout(gTaskConfig.getTimeout(taskType));
        // 暂时不提供统计信息，可以后续添加
        status.setStatistics(null);
        return status;
    }

    @PreDestroy
    public void destroy() {
        TaskLogUtils.RiskFilter.info("风险检测GTask调度器正在关闭...");
        shutdown();
        TaskLogUtils.RiskFilter.info("风险检测GTask调度器已关闭");
    }

    /**
     * 调度器状态信息
     */
    public static class SchedulerStatus {
        private String taskType;
        private String taskDescription;
        private boolean enabled;
        private boolean executing;
        private int concurrency;
        private int batchSize;
        private long interval;
        private long timeout;
        private Object statistics; // 简化为Object类型，暂时不提供统计信息

        // Getters and Setters
        public String getTaskType() { return taskType; }
        public void setTaskType(String taskType) { this.taskType = taskType; }
        public String getTaskDescription() { return taskDescription; }
        public void setTaskDescription(String taskDescription) { this.taskDescription = taskDescription; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public boolean isExecuting() { return executing; }
        public void setExecuting(boolean executing) { this.executing = executing; }
        public int getConcurrency() { return concurrency; }
        public void setConcurrency(int concurrency) { this.concurrency = concurrency; }
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        public long getInterval() { return interval; }
        public void setInterval(long interval) { this.interval = interval; }
        public long getTimeout() { return timeout; }
        public void setTimeout(long timeout) { this.timeout = timeout; }
        public Object getStatistics() { return statistics; }
        public void setStatistics(Object statistics) { this.statistics = statistics; }
    }
}
