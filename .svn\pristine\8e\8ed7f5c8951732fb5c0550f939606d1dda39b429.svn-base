package com.dataxai.web.service.export.strategy;
/**
 * 针对英文或其他通用模板（templateType != 1）的具体导出策略实现。
 *
 * @see BaseExportStrategy
 */
import com.dataxai.domain.TExcelCustomTemplateDTO;
import com.dataxai.web.service.export.support.ExcelFillService;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SimpleModeStrategy extends BaseExportStrategy {

    /**
     * 通过构造函数注入依赖。
     * @param excelFillService 提供填充逻辑的服务类。
     */
    @Autowired
    public SimpleModeStrategy(ExcelFillService excelFillService) {
        super(excelFillService);
    }

    /**
     * {@inheritDoc}
     * <p>
     * 此策略支持所有 {@code templateType} 不为 1 的情况，包括 null。
     */
    @Override
    public boolean supports(Long templateType) {
        return templateType == null || !Long.valueOf(1L).equals(templateType);
    }

    /**
     * {@inheritDoc}
     * <p>
     * 将具体的填充逻辑委托给 {@link ExcelFillService#fillForSimpleMode}。
     */
    @Override
    protected void doFill(TExcelCustomTemplateDTO dto, Workbook workbook, Sheet sheet) throws Exception {
        excelFillService.fillForSimpleMode(dto, workbook, sheet);
    }
}
