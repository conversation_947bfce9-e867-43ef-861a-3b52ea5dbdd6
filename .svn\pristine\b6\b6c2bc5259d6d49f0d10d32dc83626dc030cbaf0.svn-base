package com.dataxai.service.impl;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.file.FileUploadUtils;
import com.dataxai.common.config.RuoYiConfig;
import com.dataxai.workflow.engine.WorkflowEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.dataxai.mapper.WorkflowMapper;
import com.dataxai.mapper.WorkflowMaterialMapper;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.dataxai.mapper.WorkflowTemplateMapper;
import com.dataxai.domain.Workflow;
import com.dataxai.domain.WorkflowMaterial;
import com.dataxai.domain.WorkflowNodeExecution;
import com.dataxai.domain.WorkflowTemplate;
import com.dataxai.domain.WorkflowTemplateNode;

import com.dataxai.domain.dto.WorkflowNodeTaskDTO;
import com.dataxai.domain.dto.TaskSummaryDTO;
import com.dataxai.domain.dto.TaskStatisticsDTO;

import com.dataxai.service.IWorkflowService;
import com.dataxai.service.IWorkflowBatchService;

/**
 * 工作流表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class WorkflowServiceImpl implements IWorkflowService
{
    @Autowired
    private WorkflowMapper workflowMapper;

    @Autowired
    private WorkflowMaterialMapper workflowMaterialMapper;

    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;

    @Autowired
    private WorkflowTemplateMapper workflowTemplateMapper;

    @Autowired
    private WorkflowEngine workflowEngine;



    @Autowired
    private IWorkflowBatchService workflowBatchService;

    /**
     * 查询工作流表
     *
     * @param id 工作流表主键
     * @return 工作流表
     */
    @Override
    public Workflow selectWorkflowById(Long id)
    {
        return workflowMapper.selectWorkflowById(id);
    }

    /**
     * 查询工作流表列表
     *
     * @param workflow 工作流表
     * @return 工作流表
     */
    @Override
    public List<Workflow> selectWorkflowList(Workflow workflow)
    {
        return workflowMapper.selectWorkflowList(workflow);
    }

    /**
     * 查询工作流详情（包含素材和节点任务信息）
     *
     * @param id 工作流表主键
     * @return 工作流详情DTO
     */
    @Override
    public Object selectWorkflowWithDetails(Long id)
    {
        // 查询工作流基本信息
        Workflow workflow = workflowMapper.selectWorkflowById(id);
        if (workflow == null) {
            return null;
        }

        // 查询工作流素材
        List<WorkflowMaterial> materialList = workflowMaterialMapper.selectByWorkflowId(id);
        workflow.setMaterialList(materialList);

        // 查询节点执行记录
        List<WorkflowNodeExecution> nodeExecutionList = workflowNodeExecutionMapper.selectByWorkflowId(id);

        // 为每个节点查询关联的任务信息
        List<WorkflowNodeTaskDTO> nodeTaskList = new ArrayList<>();
        for (WorkflowNodeExecution nodeExecution : nodeExecutionList) {
            WorkflowNodeTaskDTO nodeTaskDTO = new WorkflowNodeTaskDTO();

            // 复制节点基本信息
            nodeTaskDTO.setNodeExecutionId(nodeExecution.getId());
            nodeTaskDTO.setWorkflowId(nodeExecution.getWorkflowId());
            nodeTaskDTO.setNodeOrder(nodeExecution.getNodeOrder());
            nodeTaskDTO.setTaskType(nodeExecution.getTaskType());
            nodeTaskDTO.setTaskTypeDesc(getTaskTypeDescription(nodeExecution.getTaskType()));
            nodeTaskDTO.setExecutionParams(nodeExecution.getExecutionParams());
            nodeTaskDTO.setStatus(nodeExecution.getStatus());
            nodeTaskDTO.setStatusDesc(getNodeStatusDescription(nodeExecution.getStatus()));
            nodeTaskDTO.setErrorMessage(nodeExecution.getErrorMessage());
            nodeTaskDTO.setCreateTime(nodeExecution.getCreateTime());
            nodeTaskDTO.setUpdateTime(nodeExecution.getUpdateTime());

            // 查询关联的任务列表
            List<TaskSummaryDTO> taskList = getNodeTasks(nodeExecution);
            nodeTaskDTO.setTaskList(taskList);

            // 计算任务统计信息
            TaskStatisticsDTO statistics = calculateTaskStatistics(taskList);
            nodeTaskDTO.setTaskStatistics(statistics);

            nodeTaskList.add(nodeTaskDTO);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("workflow", workflow);
        result.put("nodeTaskList", nodeTaskList);

        return result;
    }

    /**
     * 新增工作流表
     *
     * @param workflow 工作流表
     * @return 结果
     */
    @Override
    public int insertWorkflow(Workflow workflow)
    {
        workflow.setCreateTime(DateUtils.getNowDate());
        return workflowMapper.insertWorkflow(workflow);
    }

    /**
     * 创建工作流（包含素材上传和节点初始化）
     *
     * @param workflow 工作流基本信息
     * @param materialFiles 素材文件列表
     * @return 结果
     */
    @Override
    @Transactional
    public int createWorkflow(Workflow workflow, List<MultipartFile> materialFiles)
    {
        try {
            // 获取模板信息
            WorkflowTemplate template = workflowTemplateMapper.selectWorkflowTemplateWithNodes(workflow.getTemplateId());
            if (template == null) {
                throw new RuntimeException("工作流模板不存在");
            }

            if (template.getNodeList() == null || template.getNodeList().isEmpty()) {
                throw new RuntimeException("工作流模板未配置节点");
            }

            // 设置工作流基本信息
            workflow.setTemplateName(template.getTemplateName());
            workflow.setTotalNodes(template.getNodeList().size());
            workflow.setCurrentNodeOrder(1);
            workflow.setStatus(0); // 待执行
            workflow.setCreateTime(DateUtils.getNowDate());

            // 保存工作流
            int result = workflowMapper.insertWorkflow(workflow);
            if (result <= 0) {
                throw new RuntimeException("创建工作流失败");
            }

            // 上传并保存素材
            if (materialFiles != null && !materialFiles.isEmpty()) {
                List<WorkflowMaterial> materialList = new ArrayList<>();
                Date now = DateUtils.getNowDate();

                for (int i = 0; i < materialFiles.size(); i++) {
                    MultipartFile file = materialFiles.get(i);
                    if (!file.isEmpty()) {
                        // 上传文件
                        String filePath = FileUploadUtils.upload(RuoYiConfig.getUploadPath(), file);

                        WorkflowMaterial material = new WorkflowMaterial();
                        material.setWorkflowId(workflow.getId());
                        material.setMaterialUrl(filePath);
                        material.setSortOrder(i + 1);
                        material.setCreateTime(now);

                        materialList.add(material);
                    }
                }

                if (!materialList.isEmpty()) {
                    workflowMaterialMapper.insertWorkflowMaterialBatch(materialList);
                }
            }

            // 初始化节点执行记录
            List<WorkflowNodeExecution> nodeExecutions = new ArrayList<>();
            Date now = DateUtils.getNowDate();

            for (WorkflowTemplateNode templateNode : template.getNodeList()) {
                WorkflowNodeExecution nodeExecution = new WorkflowNodeExecution();
                nodeExecution.setWorkflowId(workflow.getId());
                nodeExecution.setNodeOrder(templateNode.getNodeOrder());
                nodeExecution.setTaskType(templateNode.getTaskType());
                nodeExecution.setExecutionParams(templateNode.getNodeParams());
                nodeExecution.setStatus(0); // 待执行
                nodeExecution.setCreateTime(now);
                nodeExecution.setUpdateTime(now);

                nodeExecutions.add(nodeExecution);
            }

            // 逐个插入节点执行记录，确保获取到正确的ID
            for (WorkflowNodeExecution nodeExecution : nodeExecutions) {
                workflowNodeExecutionMapper.insertWorkflowNodeExecution(nodeExecution);
            }

            // 为第一个节点创建批次
            if (!nodeExecutions.isEmpty()) {
                WorkflowNodeExecution firstNode = nodeExecutions.get(0);
                try {
                    String batchId = workflowBatchService.createFirstNodeBatch(firstNode, workflow.getId());
                    if (batchId != null) {
                        // 更新第一个节点的批次ID
                        firstNode.setBatchId(Long.valueOf(batchId));
                        workflowNodeExecutionMapper.updateWorkflowNodeExecution(firstNode);
                    }
                } catch (Exception e) {
                    // 记录错误但不影响工作流创建
                    System.err.println("为第一个节点创建批次失败: " + e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            throw new RuntimeException("创建工作流失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建工作流（包含素材URL和节点初始化）
     *
     * @param workflow 工作流基本信息
     * @param materialUrls 素材URL列表
     * @return 结果
     */
    @Override
    @Transactional
    public int createWorkflowByUrls(Workflow workflow, List<String> materialUrls)
    {
        try {
            // 获取模板信息
            WorkflowTemplate template = workflowTemplateMapper.selectWorkflowTemplateWithNodes(workflow.getTemplateId());
            if (template == null) {
                throw new RuntimeException("工作流模板不存在");
            }

            if (template.getNodeList() == null || template.getNodeList().isEmpty()) {
                throw new RuntimeException("工作流模板未配置节点");
            }

            // 设置工作流基本信息
            workflow.setTemplateName(template.getTemplateName());
            workflow.setTotalNodes(template.getNodeList().size());
            workflow.setCurrentNodeOrder(1);
            workflow.setStatus(0); // 待执行
            workflow.setCreateTime(DateUtils.getNowDate());

            // 保存工作流
            int result = workflowMapper.insertWorkflow(workflow);
            if (result <= 0) {
                throw new RuntimeException("创建工作流失败");
            }

            // 保存素材URL
            if (materialUrls != null && !materialUrls.isEmpty()) {
                List<WorkflowMaterial> materialList = new ArrayList<>();
                Date now = DateUtils.getNowDate();

                for (int i = 0; i < materialUrls.size(); i++) {
                    String materialUrl = materialUrls.get(i);
                    if (materialUrl != null && !materialUrl.trim().isEmpty()) {
                        WorkflowMaterial material = new WorkflowMaterial();
                        material.setWorkflowId(workflow.getId());
                        material.setMaterialUrl(materialUrl.trim());
                        material.setSortOrder(i + 1);
                        material.setCreateTime(now);

                        materialList.add(material);
                    }
                }

                if (!materialList.isEmpty()) {
                    workflowMaterialMapper.insertWorkflowMaterialBatch(materialList);
                }
            }

            // 初始化节点执行记录
            List<WorkflowNodeExecution> nodeExecutions = new ArrayList<>();
            Date now = DateUtils.getNowDate();

            for (WorkflowTemplateNode templateNode : template.getNodeList()) {
                WorkflowNodeExecution nodeExecution = new WorkflowNodeExecution();
                nodeExecution.setWorkflowId(workflow.getId());
                nodeExecution.setNodeOrder(templateNode.getNodeOrder());
                nodeExecution.setTaskType(templateNode.getTaskType());
                nodeExecution.setExecutionParams(templateNode.getNodeParams());
                nodeExecution.setStatus(0); // 待执行
                nodeExecution.setCreateTime(now);
                nodeExecution.setUpdateTime(now);

                nodeExecutions.add(nodeExecution);
            }

            // 逐个插入节点执行记录，确保获取到正确的ID
            for (WorkflowNodeExecution nodeExecution : nodeExecutions) {
                workflowNodeExecutionMapper.insertWorkflowNodeExecution(nodeExecution);
            }

            // 为第一个节点创建批次
            if (!nodeExecutions.isEmpty()) {
                WorkflowNodeExecution firstNode = nodeExecutions.get(0);
                try {
                    String batchId = workflowBatchService.createFirstNodeBatch(firstNode, workflow.getId());
                    // 不在节点上记录批次ID（表中无batch_id列）
                } catch (Exception e) {
                    // 记录错误但不影响工作流创建
                    System.err.println("为第一个节点创建批次失败: " + e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            throw new RuntimeException("创建工作流失败：" + e.getMessage(), e);
        }
    }

    /**
     * 修改工作流表
     *
     * @param workflow 工作流表
     * @return 结果
     */
    @Override
    public int updateWorkflow(Workflow workflow)
    {
        workflow.setUpdateTime(DateUtils.getNowDate());
        return workflowMapper.updateWorkflow(workflow);
    }

    /**
     * 批量删除工作流表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @Override
    public int deleteWorkflowByIds(Long[] ids)
    {
        return workflowMapper.deleteWorkflowByIds(ids);
    }

    /**
     * 删除工作流表信息
     *
     * @param id 工作流表主键
     * @return 结果
     */
    @Override
    public int deleteWorkflowById(Long id)
    {
        return workflowMapper.deleteWorkflowById(id);
    }



    /**
     * 取消工作流执行
     *
     * @param workflowId 工作流ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean cancelWorkflow(Long workflowId)
    {
        return workflowEngine.cancelWorkflow(workflowId);
    }



    /**
     * 获取工作流执行进度
     *
     * @param workflowId 工作流ID
     * @return 进度信息
     */
    @Override
    public Object getWorkflowProgress(Long workflowId)
    {
        Workflow workflow = workflowMapper.selectWorkflowById(workflowId);
        if (workflow == null) {
            return null;
        }

        // 查询节点执行记录
        List<WorkflowNodeExecution> nodeExecutionList = workflowNodeExecutionMapper.selectByWorkflowId(workflowId);
        workflow.setNodeExecutionList(nodeExecutionList);

        Map<String, Object> progress = new HashMap<>();
        progress.put("workflowId", workflowId);
        progress.put("workflowName", workflow.getWorkflowName());
        progress.put("status", workflow.getStatus());
        progress.put("currentNodeOrder", workflow.getCurrentNodeOrder());
        progress.put("totalNodes", workflow.getTotalNodes());

        if (workflow.getNodeExecutionList() != null) {
            // 计算节点级别的进度
            long completedNodes = workflow.getNodeExecutionList().stream()
                    .mapToLong(node -> node.getStatus() == 2 ? 1 : 0)
                    .sum();
            progress.put("completedNodes", completedNodes);
            progress.put("progressPercentage", (double) completedNodes / workflow.getTotalNodes() * 100);

            // 为每个节点添加任务详情
            List<Map<String, Object>> nodeProgressList = new ArrayList<>();
            for (WorkflowNodeExecution nodeExecution : workflow.getNodeExecutionList()) {
                Map<String, Object> nodeProgress = new HashMap<>();
                nodeProgress.put("nodeExecutionId", nodeExecution.getId());
                nodeProgress.put("nodeOrder", nodeExecution.getNodeOrder());
                nodeProgress.put("taskType", nodeExecution.getTaskType());
                nodeProgress.put("taskTypeDesc", getTaskTypeDescription(nodeExecution.getTaskType()));
                nodeProgress.put("status", nodeExecution.getStatus());
                nodeProgress.put("statusDesc", getNodeStatusDescription(nodeExecution.getStatus()));
                nodeProgress.put("errorMessage", nodeExecution.getErrorMessage());
                nodeProgress.put("createTime", nodeExecution.getCreateTime());
                nodeProgress.put("updateTime", nodeExecution.getUpdateTime());

                // 查询节点关联的任务进度
                List<TaskSummaryDTO> taskList = getNodeTasks(nodeExecution);
                TaskStatisticsDTO taskStats = calculateTaskStatistics(taskList);

                nodeProgress.put("taskCount", taskStats.getTotalTasks());
                nodeProgress.put("taskStatistics", taskStats);
                nodeProgress.put("taskList", taskList);

                nodeProgressList.add(nodeProgress);
            }

            progress.put("nodeProgressList", nodeProgressList);
        }

        return progress;
    }

    /**
     * 获取任务类型描述
     */
    private String getTaskTypeDescription(Integer taskType) {
        if (taskType == null) return "未知";
        switch (taskType) {
            case 6: return "真人图";
            case 7: return "人台图";
            case 8: return "商品图";
            case 9: return "配饰图";
            case 11: return "图片去背景";
            case 12: return "相似图裂变";
            case 14: return "平铺图图生图";
            case 17: return "侵权风险过滤";
            case 18: return "标题提取";
            default: return "未知类型(" + taskType + ")";
        }
    }

    /**
     * 获取节点状态描述
     */
    private String getNodeStatusDescription(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待执行";
            case 1: return "执行中";
            case 2: return "已完成";
            case 3: return "执行失败";
            default: return "未知状态(" + status + ")";
        }
    }

    /**
     * 获取节点关联的任务列表
     */
    private List<TaskSummaryDTO> getNodeTasks(WorkflowNodeExecution nodeExecution) {
        List<TaskSummaryDTO> taskList = new ArrayList<>();

        try {
            Integer taskType = nodeExecution.getTaskType();
            Long nodeExecutionId = nodeExecution.getId();

            if (taskType != null && nodeExecutionId != null) {
                // 根据任务类型查询不同的任务表
                // 现在可以直接通过 workflow_node_execution_id 查询任务

                if (taskType == 17) {
                    // 风险检测任务 - 通过工作流节点执行记录ID查询
                    // 注意：具体的任务查询逻辑在对应的Service层实现
                    System.out.println("风险检测任务查询，节点ID: " + nodeExecutionId);
                } else if (taskType == 18) {
                    // 标题提取任务 - 通过工作流节点执行记录ID查询
                    // 注意：具体的任务查询逻辑在对应的Service层实现
                    System.out.println("标题提取任务查询，节点ID: " + nodeExecutionId);
                }
                // 注意：t_task表的查询需要在Controller层处理，因为存在跨模块依赖
            }
        } catch (Exception e) {
            // 记录日志但不抛出异常，确保其他节点能正常查询
            System.err.println("查询节点任务失败: " + e.getMessage());
        }

        return taskList;
    }

    /**
     * 计算任务统计信息
     */
    private TaskStatisticsDTO calculateTaskStatistics(List<TaskSummaryDTO> taskList) {
        TaskStatisticsDTO statistics = new TaskStatisticsDTO();

        if (taskList == null || taskList.isEmpty()) {
            statistics.setTotalTasks(0);
            statistics.setPendingTasks(0);
            statistics.setRunningTasks(0);
            statistics.setCompletedTasks(0);
            statistics.setFailedTasks(0);
            statistics.setProgressPercentage(0.0);
            return statistics;
        }

        int total = taskList.size();
        int pending = 0;
        int running = 0;
        int completed = 0;
        int failed = 0;

        for (TaskSummaryDTO task : taskList) {
            Integer status = task.getStatus();
            if (status == null || status == 1) {
                pending++;
            } else if (status == 2) {
                running++;
            } else if (status == 3) {
                completed++;
            } else {
                failed++;
            }
        }

        statistics.setTotalTasks(total);
        statistics.setPendingTasks(pending);
        statistics.setRunningTasks(running);
        statistics.setCompletedTasks(completed);
        statistics.setFailedTasks(failed);

        // 计算进度百分比
        double progress = total > 0 ? (double) completed / total * 100 : 0.0;
        statistics.setProgressPercentage(Math.round(progress * 100.0) / 100.0);

        return statistics;
    }


}