<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.AdminTaskMapper">

    <resultMap type="AdminTask" id="AdminTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="type"    column="type"    />

        <result property="currentQueue"    column="current_queue"    />
        <result property="nextQueue"    column="next_queue"    />
        <result property="userId"    column="user_id"    />
        <result property="referedTaskId"    column="refered_task_id"    />
        <result property="status"    column="status"    />
        <result property="segData"    column="seg_data"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="originalUrl"    column="original_url"    />

        <result property="fourK"    column="four_k"    />
    </resultMap>




    <sql id="selectAdminTaskVo">
<!--        select task_id, task_name, type, user_id, refered_task_id, status, seg_data, execute_time, create_time, update_time, del_flag, original_url, proceing, four_k from t_task-->
        select task_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url, four_k from t_task
    </sql>

    <select id="selectAdminTaskList" parameterType="AdminTask" resultMap="AdminTaskResult">
        <include refid="selectAdminTaskVo"/>
        <where>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="referedTaskId != null  and referedTaskId != ''"> and refered_task_id = #{referedTaskId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="fourK != null "> and four_k = #{fourK}</if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>




    <select id="selectAdminTaskByTaskId" parameterType="String" resultMap="AdminTaskResult">
        <include refid="selectAdminTaskVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertAdminTask" parameterType="AdminTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into t_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null">task_name,</if>
            <if test="type != null">type,</if>
            <if test="userId != null">user_id,</if>
            <if test="referedTaskId != null">refered_task_id,</if>
            <if test="status != null">status,</if>
            <if test="segData != null">seg_data,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="originalUrl != null">original_url,</if>
            <if test="fourK != null">four_k,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null">#{taskName},</if>
            <if test="type != null">#{type},</if>
            <if test="userId != null">#{userId},</if>
            <if test="referedTaskId != null">#{referedTaskId},</if>
            <if test="status != null">#{status},</if>
            <if test="segData != null">#{segData},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="originalUrl != null">#{originalUrl},</if>
            <if test="fourK != null">#{fourK},</if>
         </trim>
    </insert>

    <update id="updateAdminTask" parameterType="AdminTask">
        update t_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="referedTaskId != null">refered_task_id = #{referedTaskId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="segData != null">seg_data = #{segData},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="originalUrl != null">original_url = #{originalUrl},</if>
            <if test="fourK != null">four_k = #{fourK},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteAdminTaskByTaskId" parameterType="String">
        delete from t_task where task_id = #{taskId}
    </delete>

    <delete id="deleteAdminTaskByTaskIds" parameterType="String">
        delete from t_task where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>