package com.dataxai.web.service;

import com.dataxai.web.controller.ImageController.PageResult;
import com.dataxai.web.domain.MaterialStyleUser;

public interface MaterialStyleUserService {
    boolean addFavorite(MaterialStyleUser record);
    boolean removeFavorite(Integer id);
    boolean removeFavoriteByUserAndStyle(Integer userId, Integer styleId, Integer taskType);
    PageResult<MaterialStyleUser> getUserFavorites(Integer userId, Integer pageNum, Integer pageSize, Integer taskType);
    boolean isFavorited(Integer userId, Integer styleId, Integer taskType);
}