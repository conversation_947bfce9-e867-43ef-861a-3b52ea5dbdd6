package com.dataxai.web.service.impl;

import cn.hutool.http.HttpUtil;
import com.dataxai.common.core.redis.RedisCache;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.domain.ProductInfo;
import com.dataxai.service.IProductInfoService;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.service.ProductImageUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * 产品图片上传服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
@Slf4j
public class ProductImageUploadServiceImpl implements ProductImageUploadService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private AliYunFileService aliYunFileService;

    private static final String IMAGE_UPLOAD_QUEUE = "product:image:upload:queue";
    private static final String IMAGE_UPLOAD_PROCESSING = "product:image:upload:processing:";

    // 添加关闭标志
    private volatile boolean isShutdown = false;

    /**
     * 优雅关闭方法
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始优雅关闭ProductImageUploadService...");
        isShutdown = true;
        log.info("ProductImageUploadService优雅关闭完成");
    }

    @Override
    public void addImageUploadTask(ProductInfo productInfo) {
        if (StringUtils.isNotEmpty(productInfo.getOriginalImageUrl())) {
            // 验证URL格式
            if (!isValidImageUrl(productInfo.getOriginalImageUrl())) {
                log.info("无效的图片URL，跳过上传任务，产品ID: {}, URL: {}",
                    productInfo.getId(), productInfo.getOriginalImageUrl());
                // 直接更新状态为失败
                updateProductStatus(productInfo.getId(), 3);
                return;
            }

            // 将任务添加到Redis队列
            String taskData = String.format("%d|%s", productInfo.getId(), productInfo.getOriginalImageUrl());
            redisCache.redisTemplate.opsForList().leftPush(IMAGE_UPLOAD_QUEUE, taskData);

            log.info("产品图片上传任务已添加到队列，产品ID: {}, 原始图片URL: {}",
                productInfo.getId(), productInfo.getOriginalImageUrl());
        }
    }

    @Override
    @Async
    public void processImageUpload(Long productId, String originalImageUrl) {
        String processingKey = IMAGE_UPLOAD_PROCESSING + productId;

        try {
            // 设置处理中状态，防止重复处理
            redisCache.setCacheObject(processingKey, "processing", 30, TimeUnit.MINUTES);

            log.info("开始处理产品图片上传，产品ID: {}, 原始图片URL: {}", productId, originalImageUrl);

            // 下载图片
            byte[] imageData = downloadImage(originalImageUrl);
            if (imageData == null) {
                updateProductStatus(productId, 3); // 同步图片失败
                log.error("下载图片失败，产品ID: {}", productId);
                return;
            }

            // 上传到阿里云
            String aliYunUrl = uploadToAliYun(imageData);
            if (StringUtils.isEmpty(aliYunUrl)) {
                updateProductStatus(productId, 3); // 同步图片失败
                log.error("上传图片到阿里云失败，产品ID: {}", productId);
                return;
            }

            // 更新产品信息
            ProductInfo updateProduct = new ProductInfo();
            updateProduct.setId(productId);
            updateProduct.setProductImageUrl(aliYunUrl);
            updateProduct.setStatus(2); // 已同步图片

            int result = productInfoService.updateProductInfo(updateProduct);
            if (result > 0) {
                log.info("产品图片上传成功，产品ID: {}, 阿里云URL: {}", productId, aliYunUrl);
            } else {
                log.error("更新产品信息失败，产品ID: {}", productId);
            }

        } catch (Exception e) {
            log.error("处理产品图片上传异常，产品ID: {}", productId, e);
            updateProductStatus(productId, 3); // 同步图片失败
        } finally {
            // 清除处理中状态
            redisCache.deleteObject(processingKey);
        }
    }

    /**
     * 验证图片URL是否有效
     */
    private boolean isValidImageUrl(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl)) {
            return false;
        }

        // 预检查：URL是否包含协议
        if (!imageUrl.contains("://")) {
            log.debug("URL缺少协议: {}", imageUrl);
            return false;
        }

        // 预检查：URL是否以http或https开头
        if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
            log.debug("URL协议不支持: {}", imageUrl);
            return false;
        }

        try {
            // 检查URL格式
            java.net.URL url = new java.net.URL(imageUrl);
            String protocol = url.getProtocol();
            String host = url.getHost();

            // 只允许http和https协议
            if (!"http".equals(protocol) && !"https".equals(protocol)) {
                log.debug("不支持的协议: {}", protocol);
                return false;
            }

            // 检查主机名是否为空或无效
            if (StringUtils.isEmpty(host) || host.length() < 3) {
                log.debug("无效的主机名: {}", host);
                return false;
            }

            // 检查是否是本地地址或无效地址
            if (host.equals("localhost") || host.equals("127.0.0.1") ||
                host.equals("0.0.0.0") || host.matches("^[a-zA-Z]+$")) {
                log.debug("无效的主机地址: {}", host);
                return false;
            }

            return true;
        } catch (java.net.MalformedURLException e) {
            log.debug("URL格式错误: {}", imageUrl);
            return false;
        } catch (Exception e) {
            log.debug("URL验证异常: {}", imageUrl);
            return false;
        }
    }

    /**
     * 下载图片
     */
    private byte[] downloadImage(String imageUrl) {
        try {
            // 再次验证URL（双重保险）
            if (!isValidImageUrl(imageUrl)) {
                log.error("下载前URL验证失败: {}", imageUrl);
                return null;
            }

            return HttpUtil.downloadBytes(imageUrl);
        } catch (Exception e) {
            log.error("下载图片失败，URL: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 上传图片到阿里云
     */
    private String uploadToAliYun(byte[] imageData) {
        try {
            ByteArrayResource resource = new ByteArrayResource(imageData);
            return aliYunFileService.upload(resource);
        } catch (Exception e) {
            log.error("上传图片到阿里云失败", e);
            return null;
        }
    }

    /**
     * 更新产品状态
     */
    private void updateProductStatus(Long productId, Integer status) {
        try {
            ProductInfo updateProduct = new ProductInfo();
            updateProduct.setId(productId);
            updateProduct.setStatus(status);
            productInfoService.updateProductInfo(updateProduct);
        } catch (Exception e) {
            log.error("更新产品状态失败，产品ID: {}, 状态: {}", productId, status, e);
        }
    }

    /**
     * 启动队列消费者
     */
    @Async
    public void startQueueConsumer() {
        while (!isShutdown) {
            try {
                // 从队列中获取任务，使用较短的阻塞时间
                Object task = redisCache.redisTemplate.opsForList().rightPop(IMAGE_UPLOAD_QUEUE, 2, TimeUnit.SECONDS);
                if (task != null) {
                    String taskStr = task.toString();
                    String[] parts = taskStr.split("\\|");
                    if (parts.length == 2) {
                        Long productId = Long.parseLong(parts[0]);
                        String originalImageUrl = parts[1];

                        // 再次验证URL（防止队列中的无效URL）
                        if (!isValidImageUrl(originalImageUrl)) {
                            log.info("队列中发现无效URL，跳过处理，产品ID: {}, URL: {}", productId, originalImageUrl);
                            updateProductStatus(productId, 3);
                            continue;
                        }

                        // 检查是否正在处理中
                        String processingKey = IMAGE_UPLOAD_PROCESSING + productId;
                        if (!redisCache.hasKey(processingKey)) {
                            processImageUpload(productId, originalImageUrl);
                        }
                    } else {
                        log.warn("队列任务格式错误: {}", taskStr);
                    }
                }
            } catch (org.springframework.data.redis.RedisSystemException e) {
                // 处理Redis系统异常，包括命令中断
                if (e.getCause() instanceof io.lettuce.core.RedisCommandInterruptedException) {
                    if (isShutdown) {
                        log.info("Redis命令被中断，应用正在关闭，退出消费者循环");
                        break;
                    } else {
                        log.info("Redis命令被中断，但应用未关闭，继续运行");
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                } else {
                    log.error("Redis系统异常", e);
                    try {
                        Thread.sleep(2000); // 异常时休眠2秒
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                if (!isShutdown) {
                    log.error("队列消费者异常", e);
                } else {
                    log.info("产品图片上传队列消费者在关闭过程中发生异常: {}", e.getMessage());
                }
                try {
                    Thread.sleep(2000); // 异常后休眠2秒
                } catch (InterruptedException ie) {
                    log.info("产品图片上传队列消费者被中断，正在关闭...");
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("产品图片上传队列消费者已停止");
    }
}