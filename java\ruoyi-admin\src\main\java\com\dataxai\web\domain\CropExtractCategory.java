package com.dataxai.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 印花图提取品类表
 * @TableName t_crop_extract_category
 */
@Data
@TableName("t_crop_extract_category")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CropExtractCategory {
	/** 主键ID */
	@ApiModelProperty(value = "品类ID")
	private Integer id;

	/** 品类名称 */
	@ApiModelProperty(value = "品类名称")
	private String name;

	/** 排序（越小越靠前） */
	@ApiModelProperty(value = "排序（越小越靠前）")
	private Integer sortOrder;

	/** 状态(1-启用,0-禁用)，可选 */
	@ApiModelProperty(value = "状态(1-启用,0-禁用)")
	private Integer status;
} 