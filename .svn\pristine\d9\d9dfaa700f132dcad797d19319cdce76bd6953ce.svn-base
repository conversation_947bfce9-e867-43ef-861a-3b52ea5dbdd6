package com.dataxai.web.controller.ImageController;

import com.dataxai.web.domain.MaterialIpCategory;
import com.dataxai.web.service.MaterialIpCategoryService;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/material/ip/category")
@Api(tags = "IP分类管理")
public class MaterialIpCategoryController {

    @Autowired
    private MaterialIpCategoryService categoryService;

    @GetMapping("/page")
    @ApiOperation("分页查询IP分类")
    public R<PageResult<MaterialIpCategory>> queryPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name) {

        List<MaterialIpCategory> list = categoryService.queryPage(pageNum, pageSize, name);
        int total = categoryService.countByCondition(name);

        PageResult<MaterialIpCategory> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);

        return R.ok(pageResult);
    }

    @PostMapping
    @ApiOperation("新增IP分类")
    public R<Boolean> addMaterialIpCategory(@RequestBody MaterialIpCategory category) {
        boolean result = categoryService.addMaterialIpCategory(category);
        return result ? R.ok(true) : R.fail("新增失败");
    }

    @PutMapping
    @ApiOperation("修改IP分类")
    public R<Boolean> updateMaterialIpCategory(@RequestBody MaterialIpCategory category) {
        boolean result = categoryService.updateMaterialIpCategory(category);
        return result ? R.ok(true) : R.fail("修改失败");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除IP分类")
    public R<Boolean> deleteMaterialIpCategory(@PathVariable Integer id) {
        try {
            boolean result = categoryService.deleteMaterialIpCategory(id);
            return result ? R.ok(true) : R.fail("删除失败");
        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取IP分类详情")
    public R<MaterialIpCategory> getById(@PathVariable Integer id) {
        MaterialIpCategory category = categoryService.getById(id);
        return category != null ? R.ok(category) : R.fail("数据不存在");
    }
}