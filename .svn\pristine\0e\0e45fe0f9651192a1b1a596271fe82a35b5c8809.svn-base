package com.dataxai.web.service.platform;

import java.util.List;
import java.util.Map;

/**
 * 第三方平台API调用服务接口
 * 提供统一的平台API调用接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IPlatformApiService
{
    /**
     * 推送图片到指定平台
     *
     * @param platformCode 平台代码
     * @param userId 用户ID
     * @param images 图片列表
     * @param categoryName 分类名称
     * @param tags 标签
     * @param total 总数
     * @return 推送结果
     */
    Map<String, Object> pushImages(String platformCode, Long userId, List<String> images,
                                   String categoryName, String tags, Integer total);

    /**
     * 推送图片到指定平台（带参数）
     *
     * @param platformCode 平台代码
     * @param userId 用户ID
     * @param images 图片列表
     * @param phone 手机号
     * @param name 名称
     * @param tags 标签
     * @param total 总数
     * @return 推送结果
     */
    Map<String, Object> pushImagesWithParams(String platformCode, Long userId, List<String> images,
                                             String phone, String name, String tags, Integer total);

    /**
     * 检查平台是否支持
     *
     * @param platformCode 平台代码
     * @return 是否支持
     */
    boolean isPlatformSupported(String platformCode);

    /**
     * 获取支持的平台列表
     *
     * @return 支持的平台列表
     */
    List<String> getSupportedPlatforms();
} 