package com.dataxai.service;

import java.util.List;
import com.dataxai.domain.TitleExtractionTaskDetail;

/**
 * 标题提取任务详情表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface ITitleExtractionTaskDetailService
{
    /**
     * 查询标题提取任务详情表
     *
     * @param id 标题提取任务详情表主键
     * @return 标题提取任务详情表
     */
    public TitleExtractionTaskDetail selectTitleExtractionTaskDetailById(Long id);

    /**
     * 查询标题提取任务详情表列表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 标题提取任务详情表集合
     */
    public List<TitleExtractionTaskDetail> selectTitleExtractionTaskDetailList(TitleExtractionTaskDetail titleExtractionTaskDetail);

    /**
     * 根据任务ID查询详情列表
     *
     * @param taskId 任务ID
     * @return 标题提取任务详情表集合
     */
    public List<TitleExtractionTaskDetail> selectTitleExtractionTaskDetailByTaskId(Long taskId);

    /**
     * 新增标题提取任务详情表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 结果
     */
    public int insertTitleExtractionTaskDetail(TitleExtractionTaskDetail titleExtractionTaskDetail);

    /**
     * 批量新增标题提取任务详情表
     *
     * @param titleExtractionTaskDetailList 标题提取任务详情表列表
     * @return 结果
     */
    public int insertTitleExtractionTaskDetailBatch(List<TitleExtractionTaskDetail> titleExtractionTaskDetailList);

    /**
     * 修改标题提取任务详情表
     *
     * @param titleExtractionTaskDetail 标题提取任务详情表
     * @return 结果
     */
    public int updateTitleExtractionTaskDetail(TitleExtractionTaskDetail titleExtractionTaskDetail);

    /**
     * 批量删除标题提取任务详情表
     *
     * @param ids 需要删除的标题提取任务详情表主键集合
     * @return 结果
     */
    public int deleteTitleExtractionTaskDetailByIds(Long[] ids);

    /**
     * 删除标题提取任务详情表信息
     *
     * @param id 标题提取任务详情表主键
     * @return 结果
     */
    public int deleteTitleExtractionTaskDetailById(Long id);
}