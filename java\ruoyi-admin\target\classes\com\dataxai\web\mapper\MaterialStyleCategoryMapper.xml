<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.MaterialStyleCategoryMapper">

    <resultMap id="BaseResultMap" type="com.dataxai.web.domain.MaterialStyleCategory">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="sort_order" property="sortOrder" />
        <result column="task_type" property="taskType" />
    </resultMap>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT * FROM t_material_style_category
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
        </where>
        ORDER BY sort_order ASC, id DESC
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM t_material_style_category WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_material_style_category (name, sort_order, task_type)
        VALUES (#{name}, #{sortOrder}, #{taskType})
    </insert>

    <update id="update">
        UPDATE t_material_style_category
        SET name = #{name},
            sort_order = #{sortOrder},
            task_type = #{taskType}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM t_material_style_category WHERE id = #{id}
    </delete>

    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM t_material_style_category
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
        </where>
    </select>

    <select id="selectPageByCondition" resultMap="BaseResultMap">
        SELECT * FROM t_material_style_category
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
        </where>
        ORDER BY sort_order ASC, id DESC
        LIMIT #{offset}, #{pageSize}
    </select>
</mapper>