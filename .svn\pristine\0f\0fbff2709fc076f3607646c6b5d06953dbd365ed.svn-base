import request from '@/utils/request'

// 获取风格配置列表
export function getStyleList(query) {
	return request({
		url: '/material/style/page',
		method: 'get',
		params: query
	})
}
// 新增风格配置
export function addStyle(data) {
	return request({
		url: '/material/style',
		method: 'post',
		data: data
	})
}
// 修改风格配置
export function updateStyle(data) {
	return request({
		url: '/material/style',
		method: 'put',
		data: data
	})
}
// 删除风格配置
export function delStyle(id) {
	return request({
		url: '/material/style/' + id,
		method: 'delete'
	})
}
// 查询风格详请
export function getStyle(id) {
	return request({
		url: '/material/style/' + id,
		method: 'get'
	})
}

// 获取风格分类列表
export function getStyleCategoryList(query) {
	return request({
		url: '/material/style/category/page',
		method: 'get',
		params: query
	})
}
// 新增风格分类
export function addStyleCategory(data) {
	return request({
		url: '/material/style/category',
		method: 'post',
		data: data
	})
}

// 修改风格分类
export function updateStyleCategory(data) {
	return request({
		url: '/material/style/category',
		method: 'put',
		data: data
	})
}
// 删除风格分类
export function delStyleCategory(id) {
	return request({
		url: '/material/style/category/' + id,
		method: 'delete'
	})
}
// 查询风格分类详请
export function getStyleCategory(id) {
	return request({
		url: '/material/style/category/' + id,
		method: 'get'
	})
}

// 获取动漫IP配置列表
export function getFashIpList(query) {
	return request({
		url: '/material/ip/page',
		method: 'get',
		params: query
	})
}
// 新增动漫IP配置
export function addFashIp(data) {
	return request({
		url: '/material/ip',
		method: 'post',
		data: data
	})
}

// 修改动漫IP配置
export function updateFashIp(data) {
	return request({
		url: '/material/ip',
		method: 'put',
		data: data
	})
}
// 删除动漫IP配置
export function delFashIp(id) {
	return request({
		url: '/material/ip/' + id,
		method: 'delete'
	})
}
// 查询动漫IP详请
export function getFashIp(id) {
	return request({
		url: '/material/ip/' + id,
		method: 'get'
	})
}

// 获取动漫IP分类列表
export function getIpCategoryList(query) {
	return request({
		url: '/material/ip/category/page',
		method: 'get',
		params: query
	})
}
// 新增风格分类
export function addIpCategory(data) {
	return request({
		url: '/material/ip/category',
		method: 'post',
		data: data
	})
}

// 修改动漫IP分类
export function updateIpCategory(data) {
	return request({
		url: '/material/ip/category',
		method: 'put',
		data: data
	})
}

// 删除动漫IP分类
export function delIpCategory(id) {
	return request({
		url: '/material/ip/category/' + id,
		method: 'delete'
	})
}
// 查询动漫IP分类详请
export function getIpCategory(id) {
	return request({
		url: '/material/ip/category/' + id,
		method: 'get'
	})
}

// 印花图品类配置
// 分页查询
export function getPrintPatternCategoryList(query) {
	return request({
		// url: '/crop/extract/category',
		url: '/cropExtractCategory/page',
		method: 'get',
		params: query
	})
}
// 新增
export function addPrintPatternCategory(data) {
	return request({
		url: 'cropExtractCategory',
		method: 'post',
		data: data
	})
}

// 修改
export function updatePrintPatternCategory(data) {
	return request({
		url: 'cropExtractCategory',
		method: 'put',
		data: data
	})
}
// 删除
export function delPrintPatternCategory(id) {
	return request({
		url: 'cropExtractCategory/' + id,
		method: 'delete'
	})
}
// 详情
export function getPrintPatternCategoryDetail(id) {
	console.log('🧊🧊🧊 id', id)

	return request({
		url: 'cropExtractCategory/' + id,
		method: 'get'
	})
}
