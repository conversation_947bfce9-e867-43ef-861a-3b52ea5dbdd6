package com.dataxai.domain;

import com.dataxai.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 风险检测任务详情表 risk_detection_task_detail
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
public class RiskDetectionTaskDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    private Long[] ids;

    /** 关联风险检测任务表ID */
    private Long taskId;

    /**
     * 产品标题
     */
    private String productTitle;

    /** 图片链接 */
    private String imageUrl;
    /** 缩放图片链接 */
    private String scaleImageUrl;

    /** 来源类型（1-来源为t_ordinal_img_result，2-来源为product_info，3-来源为直接上传的图片） */
    private Integer type;

    /** 关联ID（如果type为1，则保存t_ordinal_img_result的image_id，如果type为2，则保存product_info的id，如果type为3，则为空） */
    private Long typeId;

    /** 风险等级（高、中、低） */
    private String riskLevel;

    /** 包含元素 (存储JSON或其他格式) */
    private String elements;

    /** 建议 */
    private String suggestion;

    /** 处理状态（1：待处理，2：已处理，默认1） */
    private Integer processStatus;

    /** 图片状态（0：正常使用，1：废弃，默认0） */
    private Integer imageStatus;

    /** 所属人ID (关联sys_user.user_id) */
    private Long ownerId;

    /** 是否上传过设计器 0：未上传，1：已上传 */
    private Boolean hasUploaded;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }

    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }

    public void setTypeId(Long typeId)
    {
        this.typeId = typeId;
    }

    public Long getTypeId()
    {
        return typeId;
    }

    public void setRiskLevel(String riskLevel)
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel()
    {
        return riskLevel;
    }

    public void setElements(String elements)
    {
        this.elements = elements;
    }

    public String getElements()
    {
        return elements;
    }

    public void setSuggestion(String suggestion)
    {
        this.suggestion = suggestion;
    }

    public String getSuggestion()
    {
        return suggestion;
    }

    public void setProcessStatus(Integer processStatus)
    {
        this.processStatus = processStatus;
    }

    public Integer getProcessStatus()
    {
        return processStatus;
    }

    public void setOwnerId(Long ownerId)
    {
        this.ownerId = ownerId;
    }

    public Long getOwnerId()
    {
        return ownerId;
    }

    public void setHasUploaded(Boolean hasUploaded)
    {
        this.hasUploaded = hasUploaded;
    }

    public Boolean getHasUploaded()
    {
        return hasUploaded;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskId", getTaskId())
            .append("imageUrl", getImageUrl())
            .append("type", getType())
            .append("typeId", getTypeId())
            .append("riskLevel", getRiskLevel())
            .append("elements", getElements())
            .append("suggestion", getSuggestion())
            .append("processStatus", getProcessStatus())
            .append("ownerId", getOwnerId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}