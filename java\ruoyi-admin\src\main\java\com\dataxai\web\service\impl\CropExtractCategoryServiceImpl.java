package com.dataxai.web.service.impl;

import com.dataxai.web.domain.CropExtractCategory;
import com.dataxai.web.mapper.CropExtractCategoryMapper;
import com.dataxai.web.service.CropExtractCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CropExtractCategoryServiceImpl implements CropExtractCategoryService {
	@Autowired
	private CropExtractCategoryMapper categoryMapper;

	@Override
	public List<CropExtractCategory> queryAll(String name, Integer status) {
		return categoryMapper.selectByCondition(name, status);
	}

	@Override
	public CropExtractCategory getById(Integer id) {
		return categoryMapper.selectById(id);
	}

	@Override
	public boolean add(CropExtractCategory category) {
		return categoryMapper.insert(category) > 0;
	}

	@Override
	public boolean update(CropExtractCategory category) {
		return categoryMapper.update(category) > 0;
	}

	@Override
	public boolean deleteById(Integer id) {
		return categoryMapper.deleteById(id) > 0;
	}

	@Override
	public List<CropExtractCategory> queryPage(Integer pageNum, Integer pageSize, String name, Integer status) {
		int offset = (pageNum - 1) * pageSize;
		return categoryMapper.selectPageByCondition(offset, pageSize, name, status);
	}

	@Override
	public int countByCondition(String name, Integer status) {
		return categoryMapper.countByCondition(name, status);
	}
} 