<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.ProductInfoMapper">

    <resultMap type="com.dataxai.domain.ProductInfo" id="ProductInfoResult">
        <result property="id" column="id"/>
        <result property="productTitle" column="product_title"/>
        <result property="productPrice" column="product_price"/>
        <result property="productImageUrl" column="product_image_url"/>
        <result property="originalImageUrl" column="original_image_url"/>
        <result property="scaleImageUrl" column="scale_image_url"/>
        <result property="imageColor" column="image_color"/>
        <result property="sourcePlatform" column="source_platform"/>
        <result property="ownerId" column="owner_id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="teamId" column="team_id"/>
        <result property="commentNum" column="comment_num"/>
        <result property="starLevel" column="star_level"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="productLink" column="product_link"/>
        <result property="hasUploaded" column="has_uploaded"/>
        <result property="batch" column="batch"/>
        <result property="teamId" column="team_id"/>
        <result property="infringementMark" column="infringement_mark"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectProductInfoVo">
        select id,
               product_title,
               product_price,
               product_image_url,
               original_image_url,
               scale_image_url,
               image_color,
               source_platform,
               owner_id,
               owner_name,
               team_id,
               comment_num,
               star_level,
               create_time,
               update_time,
               status,
               product_link,
               has_uploaded,
               batch,
               team_id,
               infringement_mark,
               remark
        from product_info
    </sql>

    <select id="selectProductInfoList" parameterType="com.dataxai.domain.ProductInfo" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        <where>
            <if test="startTime != null and startTime != ''">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and create_time &lt;= #{endTime}
            </if>
            <if test="imageColor != null  and imageColor != ''">and image_color = #{imageColor}</if>
            <if test="sourcePlatform != null  and sourcePlatform != ''">and source_platform = #{sourcePlatform}</if>
            <if test="teamId != null and ownerId == null">
                <!-- 团队模式：直接通过team_id过滤 -->
                and team_id = #{teamId}
            </if>
            <if test="teamId !=null and  ownerId != null">
                and owner_id = #{ownerId} and team_id = #{teamId}
            </if>
            <if test="ownerId != null and (teamId == null or teamId==0)">
                <!-- 个人模式：通过owner_id过滤，且team_id为0 -->
                and owner_id = #{ownerId}
                -- AND team_id = 0
            </if>
            <if test="status != null ">and status = #{status}</if>
            <!-- infringement_mark 特殊处理 - 暂时注释掉，因为数据库表中没有此字段 -->
            <if test="infringementMark == null">
                <!-- 查询所有数据 -->
            </if>
            <if test="infringementMark == 0">
                AND (infringement_mark = 0 OR infringement_mark IS NULL)
            </if>
            <if test="infringementMark == 1">
                AND infringement_mark = 1
            </if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>

        </where>
        order by create_time desc
    </select>

    <select id="selectProductInfoById" parameterType="Long" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductInfo" parameterType="com.dataxai.domain.ProductInfo" useGeneratedKeys="true" keyProperty="id">
        insert into product_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">product_title,</if>
            <if test="productPrice != null and productPrice != ''">product_price,</if>
            <if test="productImageUrl != null and productImageUrl != ''">product_image_url,</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">original_image_url,</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url,</if>
            <if test="imageColor != null and imageColor != ''">image_color,</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">source_platform,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="ownerName != null and ownerName != ''">owner_name,</if>
            <if test="commentNum != null">comment_num,</if>
            <if test="starLevel != null">star_level,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="productLink != null and productLink != ''">product_link,</if>
            <if test="hasUploaded != null">has_uploaded,</if>
            <if test="batch != null and batch != ''">batch,</if>
            <if test="teamId != null and teamId != ''">team_id,</if>
            <if test="infringementMark != null and infringementMark != ''">infringement_mark,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">#{productTitle},</if>
            <if test="productPrice != null and productPrice != ''">#{productPrice},</if>
            <if test="productImageUrl != null and productImageUrl != ''">#{productImageUrl},</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">#{originalImageUrl},</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">#{scaleImageUrl},</if>
            <if test="imageColor != null and imageColor != ''">#{imageColor},</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">#{sourcePlatform},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="ownerName != null and ownerName != ''">#{ownerName},</if>
            <if test="commentNum != null">#{commentNum},</if>
            <if test="starLevel != null">#{starLevel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="productLink != null and productLink != ''">#{productLink},</if>
            <if test="hasUploaded != null">#{hasUploaded},</if>
            <if test="batch != null and batch != ''">#{batch},</if>
            <if test="teamId != null and teamId != ''">#{teamId},</if>
            <if test="infringementMark != null and infringementMark != ''">#{infringementMark},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProductInfo" parameterType="ProductInfo">
        update product_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">product_title = #{productTitle},</if>
            <if test="productPrice != null and productPrice != ''">product_price = #{productPrice},</if>
            <if test="productImageUrl != null and productImageUrl != ''">product_image_url = #{productImageUrl},</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">original_image_url = #{originalImageUrl},
            </if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url = #{scaleImageUrl},</if>
            <if test="imageColor != null and imageColor != ''">image_color = #{imageColor},</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">source_platform = #{sourcePlatform},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="ownerName != null and ownerName != ''">owner_name = #{ownerName},</if>
            <if test="commentNum != null">comment_num = #{commentNum},</if>
            <if test="starLevel != null">star_level = #{starLevel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="productLink != null and productLink != ''">product_link = #{productLink},</if>
            <if test="hasUploaded != null">has_uploaded = #{hasUploaded},</if>
            <if test="batch != null and batch != ''">batch = #{batch},</if>
            <if test="teamId != null and teamId != ''">team_id = #{teamId},</if>
            <if test="infringementMark != null and infringementMark != ''">infringement_mark = #{infringementMark},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductInfoById" parameterType="Long">
        delete
        from product_info
        where id = #{id}
    </delete>

    <delete id="deleteProductInfoByIds" parameterType="String">
        delete from product_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdateHasUploaded">
        update product_info
        set has_uploaded = #{hasUploaded}
        where product_image_url in
        <foreach item="imageUrl" collection="imageUrls" open="(" separator="," close=")">
            #{imageUrl}
        </foreach>
    </update>

    <select id="selectProductInfoByBatch" parameterType="String" resultMap="ProductInfoResult">
        select id,
               product_title,
               product_price,
               product_image_url,
               original_image_url,
               scaleImageUrl,
               imageColor,
               source_platform,
               owner_id,
               owner_name,
               comment_num,
               star_level,
               create_time,
               update_time,
               status,
               product_link,
               has_uploaded,
               batch,
               team_id,
               infringement_mark
        from product_info
        where batch = #{batch}
    </select>

    <select id="countProductInfoByBatch" parameterType="String" resultType="int">
        select count(1)
        from product_info
        where batch = #{batch}
    </select>
    <select id="selectProductInfoImageColorIsNullList" parameterType="com.dataxai.domain.ProductInfo" resultMap="ProductInfoResult">
        select * from product_info where image_color is null  limit 1000
    </select>
</mapper>