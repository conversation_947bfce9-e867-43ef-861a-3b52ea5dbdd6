

import { useEffect, useState, useRef } from 'react';
import { Modal, Button, Checkbox, message, Spin } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getBatchDetail, updateBatchImage } from '@/api/task'
import { useLocation, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { batchZipUrl } from '@/api/common'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const CutDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [messageApi, messageContextHolder] = message.useMessage()

    const [selectedImages, setSelectedImages] = useState<any[]>([]);
    const [showBatchActions, setShowBatchActions] = useState(false);

    const { batchId } = location.state || {};

    const [record, setRecord] = useState<any>();
    const [pageLoading, setPageLoading] = useState(false);
    const fetchDetail = () => {
        setPageLoading(true)
        getBatchDetail(batchId).then((res: any) => {
            setRecord(res)
        }).catch(err => {
            message.error(`请求批次详情失败：${err?.data?.msg}`)
        }).finally(() => {
            setPageLoading(false)
        })
    }
    useEffect(() => {
        if (batchId) {
            fetchDetail()
        }
    }, [batchId]);

    const handleSelect = (task: any) => {
        setSelectedImages(prev => {
            const newSelected = prev.includes(task)
                ? prev.filter(item => item !== task)
                : [...prev, task];
            // 根据选择数量自动显示/隐藏操作栏
            setShowBatchActions(newSelected.length > 0);
            return newSelected;
        });
    };

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedImages([]); // 清空已选图片
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };

    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        const imgUrls = selectedImages.map(task =>
            task?.taskOrdinalList?.[0]?.ordinalImgResultList?.[0]?.resImgUrl
        ).filter(Boolean); // 过滤掉undefined的值

        if (imgUrls.length === 0) {
            messageApi.error('没有可下载的图片');
            return;
        }

        setDownloadLoading(true);
        batchZipUrl({ imageUrls: imgUrls, type: 14 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false);
        })
    };

    const [croppedImage, setCroppedImage] = useState<any>(); // 当前被裁剪的图片信息
    const [cutModalOpen, setCutModalOpen] = useState(false) // 裁剪弹窗显隐

    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any, index: number, status: string) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.imageId}
                >
                    <LikeImage
                        onCut={() => {
                            setCroppedImage(image);
                            setCutModalOpen(true);
                        }}
                        type={image.type}
                        imageId={image.imageId}
                        taskId={image.taskId}
                        taskOrdinalId={image.taskOrdinalId}
                        imgUrl={image.resImgUrl}
                        oriImgUrl={image.originalImgUrl}
                        smallImgUrl={image.resSmallImgUrl}
                        markImgUrl={image.markImgUrl}
                        progress={image.progress}
                        previewImages={[]}
                        index={0}
                        seed={image.seed}
                        delVisible={false}
                        cutVisible={true}
                        likeVisible={false}
                        downloadVisible={false}
                        comparison={true}
                        status={status}
                    />
                </div>
            )
        }
    )

    const [crop, setCrop] = useState<Crop>();
    const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
    const imgRef = useRef<HTMLImageElement>(null);
    const [isCropped, setIsCropped] = useState(false);

    const drawImageToCanvas = (
        image: HTMLImageElement,
        crop: PixelCrop,
    ): HTMLCanvasElement => {
        const canvas = document.createElement('canvas');
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        canvas.width = crop.width;
        canvas.height = crop.height;

        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('No 2d context');
        }

        ctx.drawImage(
            image,
            crop.x * scaleX,
            crop.y * scaleY,
            crop.width * scaleX,
            crop.height * scaleY,
            0,
            0,
            crop.width,
            crop.height,
        );

        return canvas;
    };

    const handleCutOk = async () => {
        if (!completedCrop || !imgRef.current) {
            messageApi.error('请先裁剪图片');
            return;
        }

        try {
            const canvas = drawImageToCanvas(imgRef.current, completedCrop);
            canvas.toBlob(async (blob) => {
                if (blob) {
                    const formData = new FormData();
                    formData.append('file', blob, 'cropped-image.png');
                    formData.append('batchId', batchId);
                    formData.append('imageId', croppedImage.imageId);

                    await updateBatchImage(formData);
                    messageApi.success('裁剪成功');
                    setCutModalOpen(false);
                    setCrop(undefined);
                    setCompletedCrop(undefined);
                    setIsCropped(false);
                    fetchDetail();
                }
            }, 'image/png');
        } catch (error) {
            messageApi.error('裁剪失败');
        }
    };

    return (
        <div className='h-full w-full p-[20px]'>
            {messageContextHolder}
            {pageLoading ? (
                <div className="flex justify-center items-center h-full">
                    <Spin size="large" />
                </div>
            ) : record ? (<>
                {/* 创建裁剪图片弹窗 start */}
                <Modal
                    title="裁剪图片"
                    open={cutModalOpen}
                    onCancel={() => {
                        setCutModalOpen(false);
                        setCrop(undefined);
                        setCompletedCrop(undefined);
                        setIsCropped(false);
                    }}
                    footer={null}
                    width={800}
                    centered
                >
                    <div className='p-[20px]'>
                        <div className='w-full h-full flex flex-col items-center justify-center'>
                            <ReactCrop
                                crop={crop}
                                onChange={(_, percentCrop) => setCrop(percentCrop)}
                                onComplete={(c) => setCompletedCrop(c)}
                                aspect={undefined}
                            >
                                <img
                                    ref={imgRef}
                                    src={croppedImage?.originalImgUrl}
                                    className="w-full h-full  object-contain "
                                    style={{ maxHeight: 'calc(90vh - 168px)' }}
                                    alt="裁剪图片"
                                    onLoad={(e) => {
                                        const img = e.currentTarget;
                                        const width = img.width / 2;
                                        const height = img.height / 2;
                                        const x = img.width / 4;
                                        const y = img.height / 4;
                                        setCrop({
                                            unit: 'px',
                                            width,
                                            height,
                                            x,
                                            y
                                        });
                                    }}
                                />
                            </ReactCrop>
                        </div>
                        <Button className='w-[calc(100%-40px)] mt-[6px] ml-[20px]' onClick={handleCutOk} style={{ height: '44px', fontSize: '18px' }}>完成</Button>

                    </div>
                </Modal>
                {/* 创建裁剪图片弹窗 end */}

                <Button type="primary" style={{ display: 'block', marginLeft: 'auto' }} onClick={toggleBatchActions}  >
                    {showBatchActions ? '取消批量操作' : '批量操作'}
                </Button>
                <div className='w-full flex items-center  h-[60px] border-b-[1px] border-normal'>
                    <p className='mr-[20px]'>批次: {record?.batchNumber}</p>
                    <p className='mr-[20px]'>创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    <p>总数：{record?.totalAmount}
                        <span style={{ color: '#389e0d', marginLeft: '6px' }}>成功：{record?.successAmount}</span>
                        {record?.failAmount > 0 && <span style={{ color: '#cf1322', marginLeft: '6px' }}>失败：{record?.failAmount}</span>}
                    </p>
                </div>
                <div className='bg-[#eee] w-full  mt-[20px] border border-normal  rounded-lg h-[calc(100vh-242px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                    <div className="grid  p-[10px] grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 xxl:grid-cols-8 gap-0">
                        {record?.tasks?.length > 0 && record?.tasks.map((task: any, index: number) => {
                            const taskOrdinal = task?.taskOrdinalList?.[0];
                            const ordinalImg = taskOrdinal?.ordinalImgResultList?.[0];

                            if (!taskOrdinal || !ordinalImg) {
                                return null; // 跳过无效的任务
                            }

                            return (
                                <div key={index} className='aspect-square  p-2'>
                                    <div className="w-full h-full flex   rounded-lg items-center justify-center  relative group bg-[#eef2ff] overflow-hidden">
                                        <Checkbox
                                            className="absolute top-4 left-4 z-10"
                                            style={{ transform: 'scale(1.25)' }}  // 放大1.5倍
                                            checked={selectedImages.includes(task)}
                                            onChange={() => handleSelect(task)}
                                            disabled={checkImageDisabled(task, 'cut')}
                                        />
                                        {ordinalImg.hasUploaded && <p className="absolute bottom-4  z-10"
                                            style={{ background: 'rgba(0,0,0,0.4)', color: '#fff', fontSize: '12px', padding: '0 4px', borderRadius: '4px;' }}>已上传设计器</p>}
                                        {getTaskImageComponent(ordinalImg, 0, '')}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                    {/* 底部操作栏 start */}
                    <BottomActionBar
                        visible={selectedImages.length > 0 || showBatchActions}
                        selectedCount={selectedImages.length}
                        isAllSelected={record?.tasks?.length > 0 && selectedImages.length == record?.tasks?.filter((task: any) => !checkImageDisabled(task, 'cut')).length}
                        onToggleSelectAll={() => {
                            const enabledTasks = record?.tasks?.filter((task: any) => !checkImageDisabled(task, 'cut')) || [];
                            if (record?.tasks?.length > 0 && selectedImages.length == enabledTasks.length) {
                                setSelectedImages([])
                            } else {
                                setSelectedImages([...enabledTasks])
                            }
                        }}
                        onCancelSelection={cancelSelection}
                        syncEnabled
                        selectedItems={selectedImages}
                        extractImageUrl={(task) => {
                            const taskOrdinal = task?.taskOrdinalList?.[0];
                            const ordinalImg = taskOrdinal?.ordinalImgResultList?.[0];
                            return ordinalImg?.resImgUrl;
                        }}
                        syncExtraParams={{}}
                        onDownload={handleDownloadImgages}
                        downloadLoading={downloadLoading}
                        downloadDisabled={selectedImages.length === 0}
                        enableWorkflow={userInfo?.currentMode == 2}
                        onActionFinished={() => {
                            // 刷新积分或数据
                            userinfoService.refresh();
                            fetchDetail();
                            setSelectedImages([])
                        }}
                        actionDisabled={selectedImages.length === 0}
                    />
                    {/* 底部操作栏 end */}
                </div>
            </>) : null}
        </div >
    );
};

export default CutDetail;






















