@import './variable.scss';
@import './preflight.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 滚动条容器的样式 */
.scrollbar-container {
	overflow-y: scroll; /* 添加纵向滚动条 */
	scrollbar-width: thin; /* 定义滚动条宽度 */
	scrollbar-color: rgba(207, 207, 207, 0.5) transparent; /* 定义滚动条颜色 */
}

/* 定义滚动条轨道的样式 */
.scrollbar-container::-webkit-scrollbar-track {
	background-color: transparent; /* 设置轨道背景为透明 */
}

/* 定义滚动条滑块的样式 */
.scrollbar-container::-webkit-scrollbar-thumb {
	background-color: rgba(207, 207, 207, 0.5); /* 设置滑块颜色 */
	border-radius: 10px; /* 设置滑块圆角 */
}

/* 鼠标悬浮在滚动条上的样式 */
.scrollbar-container::-webkit-scrollbar-thumb:hover {
	background-color: rgba(207, 207, 207, 0.8); /* 设置滑块悬浮时的颜色 */
}

.svg-hover path {
	fill: var(--sub-text-color);
}
.svg-hover-white path {
	fill: white;
}
.svg-hover:hover path,
.svg-hover-white:hover path {
	fill: var(--primary-color);
}
.svg-primary path {
	fill: var(--primary-color);
}
/* Affix继承字体 */
/* .ant-affix {
	font-family: initial;
	font-size: initial;
} */
.icon-hover {
	color: white;
}
.icon-hover:hover {
	color: var(--primary-color);
}

.icon-hover:hover .svg-hover path,
.icon-hover:hover .svg-hover-white path {
	fill: var(--primary-color);
}

/* 旋转动画*/
.loading-rotate {
	animation: rotateLoading 2s linear infinite;
	transform: translateZ(0);
	image-rendering: optimizeSpeed;
	image-rendering: -moz-crisp-edges;
	image-rendering: -webkit-optimize-contrast;
}
@keyframes rotateLoading {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/*textarea 取消选中*/
.textarea-noFocus:focus {
	outline: none !important;
	border: none !important;
	box-shadow: none !important;
}

.textarea-noFocus:focus-within {
	outline: none !important;
	border: none !important;
	box-shadow: none !important;
}

.spinStyle .css-dev-only-do-not-override-10htqj8.ant-spin-fullscreen {
	width: calc(100vw - 410px);
	height: 85vh;
	left: unset;
	top: unset;
}
.ant::after {
	content: "";
	animation: dots 2s infinite steps(3, start);
  }
   
  @keyframes dots {
	33.33% {
	  content: " .";
	}
	66.67% {
	  content: " . .";
	}
	100% {
	  content: " . . .";
	}
  }  
  .mask{
	width: calc(100vw - 410px);
	height: 100vh;
	background: rgba(0,0,0,0.6);
	position: fixed;
	top: 0;
	left: 410px;
	z-index: 99;
	display: flex;
	align-items: center;
	justify-content: center
  }

  /* .wensheng .ant-upload.ant-upload-select {
    border: none !important;
    background: transparent !important; 
    width: auto !important; 
    height: 120px !important; 
} */
  
  .ReactCrop__child-wrapper {
	position: relative;
	width: 100%!important;
	height: 100% !important;
	display: inline-block !important;
  }
  .cut-con .ReactCrop__child-wrapper {
	position: relative;
	width: 100%!important;
	height: fit-content !important;
	display: block !important;
  }
  .ant-image-css-var{
	width: 100%!important;
	height: 100% !important;
	object-fit: contain!important;
  }
  .ant-image-img{
	height: 100% !important;
  }
  .ant-image-preview-root .ant-image-preview-img {
   background-image: 
	linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%),
	linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%);
	background-size: 20px 20px;
	background-position: 0 0, 10px 10px;
	background-color: #fff;

}

.cut-slider  .ant-slider-mark-text{
	color: #333!important;
	margin-left: 10px;
}
.previewImg .ant-checkbox-inner{
	border: 1px solid #32649f!important;
}

.gather-table .ant-table-body{
	scrollbar-width: thin;
}
.gather-table .ant-table-cell:nth-child(1) {
  vertical-align: top; 
}
.main-edit .upload-area .ant-upload{
	min-height: 128px ;
	width: 100%;
}
.main-edit .upload-area{
	position: relative;
}
.main-edit .upload-area .ant-upload-list-picture-card{
	position: absolute;
	top: 14px;
	right: 20px;
}
.tag-box .ant-tag{ 
	text-wrap: auto;
}