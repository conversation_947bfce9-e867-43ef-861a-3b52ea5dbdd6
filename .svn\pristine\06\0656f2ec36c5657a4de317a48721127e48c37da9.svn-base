package com.dataxai.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.Constants.CommonStatusEnum;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.BatchTaskFactoryManager;
import com.dataxai.web.domain.*;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.PlatformBatchRequestDTO;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.dto.ZIPDTO;
import com.dataxai.web.mapper.*;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.service.BatchService;
import com.dataxai.web.service.MaterialResolverService;
import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.web.task.core.TaskScoreService;
import com.dataxai.web.task.core.common.TaskTypeUtils;
import com.dataxai.web.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_batch(任务批次表)】的数据库操作Service实现
 * @createDate 2025-04-10 09:45:16
 */
@Service
@Slf4j
public class BatchServiceImpl implements BatchService {

    @Autowired
    private TaskScoreService taskScoreService;

    @Autowired
    private AliYunFileService aliYunFileService;

    @Autowired
    private MaterialResolverService materialResolverService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private OrdinalImgResultMapper ordinalImgResultMapper;

    @Autowired
    private TaskOrdinalMapper taskOrdinalMapper;

    @Autowired
    private UserTeamInfoService userTeamInfoService;

    @Autowired
    @Lazy
    private BatchTaskFactoryManager batchTaskFactoryManager;

    @Autowired
    private RiskDetectionImageUploadService riskDetectionImageUploadService;

    // 移除mydesgin配置依赖，使用新的平台API服务
    @Value("${python.gpu_read_pictures}")
    private String readPictures;

    // 常量定义
    private static final String VALIDATION_FLAG = "flag";
    private static final String ERROR_NO_IMAGE_DATA = "图片信息为空：未提供imgUrl参数且未上传文件";
    private static final String ERROR_TABLE_VALIDATION = "Excel表格验证失败: ";
    private static final String ERROR_FILE_PROCESS = "文件处理失败: ";
    private static final String ERROR_BATCH_CREATE = "批次创建失败";

    /**
     * 新增批次信息
     *
     * @param dto
     * @return
     */
    @Override
    public Batch add(BatchDTO dto, List<MultipartFile> files, MultipartFile table, List<Path> pathList) throws IOException {
        //验证积分够不够扣
        Integer amount = checkIntegral(dto, table, files, pathList);
        //新增批次信息
        Batch batch = insertBatchInfo(dto, amount);
        return batch;
    }

    /**
     * 创建批次（完整流程）- 包含数据处理、创建批次记录，不包含异步任务处理
     */
    @Override
    public BatchCreationResult createBatchWithFullProcess(MultipartFile table, Long type, List<String> imgUrls, String remark, String taskParam, List<MultipartFile> files, Long userId) {
        // 根据任务类型记录日志
        int taskType = type.intValue();
        TaskLogUtils.info(taskType, "开始创建{}批次任务，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId);

        try {
            // 1. 参数验证
            validateAddParameters(type, imgUrls, files, table);

            // 2. 处理请求数据
            BatchCreationContext context = processRequestData(table, type, imgUrls, files, remark, taskParam, userId);

            // 3. 创建批次记录
            Batch batch = createBatchRecord(context);
            Long imageNumber = 4L;

            TaskLogUtils.info(taskType, "{}批次任务创建成功，批次ID: {}, 用户ID: {}",
                    TaskLogUtils.getTaskTypeName(taskType), batch.getBatchId(), userId);

            // 4. 返回批次创建结果

            return new BatchCreationResult(
                    batch,
                    context.getBatchDTO(),
                    context.getFilesResult().getFileBytesList(),
                    context.getTableResult().getTableBytes(),
                    context.getOriginalFiles(),
                    context.getFilesResult().getPathList()
            );

        } catch (ServiceException e) {
            log.error("批次创建业务异常 - 类型: {}, 错误: {}", type, e.getMessage());
            TaskLogUtils.error(taskType, "{}批次任务创建业务异常，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId, e);
            throw e;
        } catch (Exception e) {
            log.error("批次创建失败 - 类型: {}, 错误: {}", type, e.getMessage(), e);
            TaskLogUtils.error(taskType, "{}批次任务创建失败，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId, e);
            throw new ServiceException(ERROR_BATCH_CREATE + ": " + e.getMessage());
        }
    }

    @Override
    public BatchCreationResult createBatchWithFullProcesscope(MultipartFile table, Long type, List<String> imgUrls, String remark, String taskParam, List<MultipartFile> files, Long userId) {
        // 添加参数调试日志
        log.info("Service层接收参数 - type: {}, taskParam: {}, remark: {}, userId: {}", type, taskParam, remark, userId);
        
        // 根据任务类型记录日志
        int taskType = type.intValue();
        TaskLogUtils.info(taskType, "开始创建{}批次任务，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId);

        try {
            // 1. 参数验证
            validateAddParameters(type, imgUrls, files, table);

            // 2. 处理请求数据
            BatchCreationContext context = processRequestData(table, type, imgUrls, files, remark, taskParam, userId);

            // 3. 创建批次记录
            Batch batch = createBatchRecord(context);

            TaskLogUtils.info(taskType, "{}批次任务创建成功，批次ID: {}, 用户ID: {}",
                    TaskLogUtils.getTaskTypeName(taskType), batch.getBatchId(), userId);

            // 4. 返回批次创建结果
            return new BatchCreationResult(
                    batch,
                    context.getBatchDTO(),
                    context.getFilesResult().getFileBytesList(),
                    context.getTableResult().getTableBytes(),
                    context.getOriginalFiles(),
                    context.getFilesResult().getPathList()
            );

        } catch (ServiceException e) {
            log.error("批次创建业务异常 - 类型: {}, 错误: {}", type, e.getMessage());
            TaskLogUtils.error(taskType, "{}批次任务创建业务异常，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId, e);
            throw e;
        } catch (Exception e) {
            log.error("批次创建失败 - 类型: {}, 错误: {}", type, e.getMessage(), e);
            TaskLogUtils.error(taskType, "{}批次任务创建失败，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), userId, e);
            throw new ServiceException(ERROR_BATCH_CREATE + ": " + e.getMessage());
        }
    }

    /**
     * 平台API创建批次（基于图片URL）
     */
    @Override
    public BatchCreationResult createPlatformBatch(PlatformBatchRequestDTO requestDTO, Long userId) {
        // 根据任务类型记录日志
        int taskType = requestDTO.getType().intValue();
        TaskLogUtils.info(taskType, "平台API创建{}批次任务，用户ID: {}",
                TaskLogUtils.getTaskTypeName(taskType), userId);

        try {
            // 参数验证
            if (requestDTO.getType() == null) {
                throw new ServiceException("批次类型不能为空");
            }

            // 对于非批量上传类型，需要验证图片数据源
            if (!requestDTO.getType().equals(Long.valueOf(Constants.TASK_TYPE_BATCH_UPLOAD))) {
                if (CollectionUtil.isEmpty(requestDTO.getImageUrls())) {
                    throw new ServiceException("请提供图片URL列表");
                }
            }

            // 调用完整流程方法
            BatchCreationResult result = createBatchWithFullProcess(
                    null,  // table
                    requestDTO.getType(),
                    requestDTO.getImageUrls(),
                    null,  // remark
                    requestDTO.getTaskParam(),
                    null,  // files
                    userId
            );

            TaskLogUtils.info(taskType, "平台API{}批次任务创建成功，批次ID: {}, 用户ID: {}",
                    TaskLogUtils.getTaskTypeName(taskType), result.getBatch().getBatchId(), userId);

            return result;

        } catch (ServiceException e) {
            log.error("平台API批次创建业务异常 - 类型: {}, 错误: {}", requestDTO.getType(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("平台API批次创建失败 - 类型: {}, 错误: {}", requestDTO.getType(), e.getMessage(), e);
            throw new ServiceException("批次创建失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求参数
     */
    private void validateAddParameters(Long type, List<String> imgUrls, List<MultipartFile> files, MultipartFile table) {
        if (type == null) {
            throw new ServiceException("批次类型不能为空");
        }
        // 放开素材校验：所有类型均可不提供图片URL/文件/表格
        log.debug("参数验证通过 - 类型: {}", type);
    }

    /**
     * 处理请求数据
     */
    private BatchCreationContext processRequestData(MultipartFile table, Long type, List<String> imgUrls,
                                                    List<MultipartFile> files, String remark, String taskParam, Long userId) {
        // 添加参数调试日志
        log.info("processRequestData接收参数 - type: {}, taskParam: {}, remark: {}, userId: {}", type, taskParam, remark, userId);
        
        try {
            // 处理Excel表格
            ProcessedTableResult tableResult = processAndValidateTable(table);

            // 将imgUrls数组转换为逗号分隔的字符串（用于保持兼容性）
            String imgUrl = CollectionUtil.isNotEmpty(imgUrls) ? String.join(",", imgUrls) : null;

            // 处理图片文件
            ProcessedFilesResult filesResult = processFiles(type, imgUrl, files, tableResult.getTableBytes());

            // 构建批次DTO
            BatchDTO batchDTO = buildBatchDTO(type, remark, filesResult.getImgUrl(), taskParam);
            batchDTO.setUserId(userId);

            return new BatchCreationContext(tableResult, filesResult, batchDTO, userId, files);

        } catch (IOException e) {
            log.error("数据处理失败: {}", e.getMessage(), e);
            throw new ServiceException(ERROR_FILE_PROCESS + e.getMessage());
        }
    }

    /**
     * 创建批次记录
     */
    private Batch createBatchRecord(BatchCreationContext context) {
        try {
            Batch batch = add(
                    context.getBatchDTO(),
                    context.getOriginalFiles(),
                    context.getTableResult().getProcessedTable(),
                    context.getFilesResult().getPathList()
            );

            return batch;

        } catch (Exception e) {
            log.error("批次记录创建失败: {}", e.getMessage(), e);
            throw new ServiceException("批次记录创建失败: " + e.getMessage());
        }
    }


    /**
     * 处理和验证Excel表格
     */
    private ProcessedTableResult processAndValidateTable(MultipartFile table) throws IOException {
        if (table == null || table.isEmpty()) {
            log.debug("未上传表格文件");
            return new ProcessedTableResult(null, null);
        }

        try {
            // 读取表格字节数组
            byte[] tableBytes = table.getBytes();

            // 验证Excel数据
            validateExcelTable(table, tableBytes);

            // 创建处理后的表格对象
            MultipartFile processedTable = new CustomMultipartFile(
                    table.getName(),
                    table.getOriginalFilename(),
                    table.getContentType(),
                    tableBytes
            );

            return new ProcessedTableResult(tableBytes, processedTable);

        } catch (IOException e) {
            log.error("Excel表格处理失败 - 文件: {}, 错误: {}", table.getOriginalFilename(), e.getMessage());
            throw new ServiceException(ERROR_FILE_PROCESS + e.getMessage());
        }
    }

    /**
     * 验证Excel表格
     */
    private void validateExcelTable(MultipartFile table, byte[] tableBytes) throws IOException {
        // 创建缓存的表格副本用于验证
        MultipartFile cachedTable = new CustomMultipartFile(
                table.getName(),
                table.getOriginalFilename(),
                table.getContentType(),
                tableBytes
        );

        // 验证Excel数据
        Map<String, Object> validationResult = aliYunFileService.validateUploadTable(cachedTable);
        if (!(boolean) validationResult.get(VALIDATION_FLAG)) {
            String errorMessage = validationResult.get("message").toString();
            log.warn("Excel表格验证失败 - 文件: {}, 错误: {}", table.getOriginalFilename(), errorMessage);
            throw new ServiceException(ERROR_TABLE_VALIDATION + errorMessage);
        }
    }

    /**
     * 处理图片文件
     */
    private ProcessedFilesResult processFiles(Long type, String imgUrl, List<MultipartFile> files, byte[] tableBytes) throws IOException {
        // 批量上传类型（type=15）的特殊处理
        if (type.equals(Long.valueOf(Constants.TASK_TYPE_BATCH_UPLOAD))) {
            return processBatchUploadFiles(files);
        }

        // 其他类型的处理
        return processOtherTypeFiles(imgUrl, files, tableBytes);
    }

    /**
     * 处理批量上传类型的文件（type=15）
     */
    private ProcessedFilesResult processBatchUploadFiles(List<MultipartFile> files) {
        if (CollectionUtil.isEmpty(files)) {
            return new ProcessedFilesResult(null, null, new ArrayList<>());
        }

        try {
            // 将文件转换为字节数组
            List<byte[]> fileBytesList = files.stream()
                    .map(this::convertFileToBytes)
                    .collect(Collectors.toList());

            return new ProcessedFilesResult(null, fileBytesList, new ArrayList<>());

        } catch (Exception e) {
            throw new ServiceException("批量上传文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 转换文件为字节数组
     */
    private byte[] convertFileToBytes(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            log.error("文件读取失败 - 文件名: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件读取失败: " + file.getOriginalFilename(), e);
        }
    }

    /**
     * 处理其他类型的文件
     */
    private ProcessedFilesResult processOtherTypeFiles(String imgUrl, List<MultipartFile> files, byte[] tableBytes) throws IOException {
        // 如果没有表格且没有图片URL，需要从文件中生成
        if (tableBytes == null && StringUtils.isEmpty(imgUrl)) {
            return processFilesAsImageUrl(files);
        }

        // 如果有图片URL，处理外部URL上传到阿里云
        if (StringUtils.isNotEmpty(imgUrl)) {
            if (areAllHttpUrls(imgUrl)) {
                // 外部HTTP URL，需要下载并上传到阿里云
                return processExternalImageUrls(imgUrl);
            } else if (CommonUtils.isUrlMatchOosPrefix(imgUrl)) {
                // 已经是阿里云OSS URL，直接使用
                return new ProcessedFilesResult(imgUrl, null, new ArrayList<>());
            } else {
                return processFilesAsImageUrl(files);
            }
        }

        // 如果有表格数据，检查表格中的图片URL
        if (tableBytes != null) {
            String tableImgUrl = extractImageUrlFromTable(tableBytes);
            if (StringUtils.isNotEmpty(tableImgUrl)) {
                if (areAllHttpUrls(tableImgUrl)) {
                    // 外部HTTP URL，需要下载并上传到阿里云
                    return processExternalImageUrls(tableImgUrl);
                } else if (CommonUtils.isUrlMatchOosPrefix(tableImgUrl)) {
                    // 已经是阿里云OSS URL，直接使用
                    return new ProcessedFilesResult(tableImgUrl, null, new ArrayList<>());
                } else {
                    return processFilesAsImageUrl(files);
                }
            }
        }

        return new ProcessedFilesResult(imgUrl, null, new ArrayList<>());
    }

    /**
     * 处理文件并生成图片URL
     */
    private ProcessedFilesResult processFilesAsImageUrl(List<MultipartFile> files) throws IOException {
        if (CollectionUtil.isEmpty(files)) {
            throw new ServiceException(ERROR_NO_IMAGE_DATA);
        }

        List<Path> pathList = saveFilesToTempDir(files);

        String generatedImgUrl = pathList.stream()
                .map(Path::toString)
                .collect(Collectors.joining(","));

        return new ProcessedFilesResult(generatedImgUrl, null, pathList);
    }

    /**
     * 处理外部图片URL，下载并上传到阿里云
     */
    private ProcessedFilesResult processExternalImageUrls(String imgUrl) {
        try {
            log.info("开始处理外部图片URL: {}", imgUrl);

            // 解析多个URL
            List<String> imageUrlList = parseImgUrlList(imgUrl);

            // 使用RiskDetectionImageUploadService处理外部URL
            List<String> processedUrls = riskDetectionImageUploadService.processImageUrls(imageUrlList);

            if (CollectionUtil.isEmpty(processedUrls)) {
                throw new ServiceException("外部图片URL处理失败，无法上传到阿里云");
            }

            // 将处理后的URL重新组合为逗号分隔的字符串
            String processedImgUrl = String.join(",", processedUrls);

            log.info("外部图片URL处理完成: {} -> {}", imgUrl, processedImgUrl);

            return new ProcessedFilesResult(processedImgUrl, null, new ArrayList<>());

        } catch (Exception e) {
            log.error("处理外部图片URL失败: {}", imgUrl, e);
            throw new ServiceException("外部图片URL处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析图片URL列表
     */
    private List<String> parseImgUrlList(String imgUrl) {
        List<String> imgUrlList = new ArrayList<>();
        if (StringUtils.isNotEmpty(imgUrl)) {
            String[] split = imgUrl.split(",");
            imgUrlList = Arrays.asList(split);
        }
        return imgUrlList;
    }

    /**
     * 从表格中提取图片URL
     */
    private String extractImageUrlFromTable(byte[] tableBytes) {
        try {
            List<PromptDTO> promptList = PromptDTO.parseTableBytes(tableBytes);
            if (CollectionUtil.isEmpty(promptList)) {
                return null;
            }

            // 检查第一列是否包含图片URL
            for (PromptDTO prompt : promptList) {
                String promptText = prompt.getPrompt();
                if (StringUtils.isNotEmpty(promptText) &&
                        (promptText.startsWith("http://") || promptText.startsWith("https://"))) {
                    log.info("从表格中提取到图片URL: {}", promptText);
                    return promptText;
                }
            }

            // 检查第二列是否包含图片URL
            for (PromptDTO prompt : promptList) {
                String resultText = prompt.getResult();
                if (StringUtils.isNotEmpty(resultText) &&
                        (resultText.startsWith("http://") || resultText.startsWith("https://"))) {
                    log.info("从表格第二列中提取到图片URL: {}", resultText);
                    return resultText;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("从表格中提取图片URL失败", e);
            return null;
        }
    }

    /**
     * 判断是否为 http(s) URL（支持逗号分隔的多个URL）
     */
    private boolean areAllHttpUrls(String imgUrl) {
        if (StringUtils.isEmpty(imgUrl)) {
            return false;
        }
        String[] urls = imgUrl.split(",");
        for (String url : urls) {
            String u = url == null ? null : url.trim();
            if (StringUtils.isEmpty(u)) {
                return false;
            }
            if (!(u.startsWith("http://") || u.startsWith("https://"))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 构建批次DTO对象
     */
    private BatchDTO buildBatchDTO(Long type, String remark, String imgUrl, String taskParam) {
        // 添加参数调试日志
        log.info("buildBatchDTO接收参数 - type: {}, taskParam: {}, remark: {}, imgUrl: {}", type, taskParam, remark, imgUrl);
        
        BatchDTO dto = new BatchDTO();
        dto.setType(type);
        dto.setRemark(remark);
        dto.setImgUrl(imgUrl);

        // 处理任务参数，添加完整的素材信息
        String fullTaskParam = taskParam;
        if (StringUtils.isNotEmpty(taskParam)) {
            try {
                // 使用素材解析服务构建包含完整素材信息的任务参数
                fullTaskParam = materialResolverService.buildFullTaskParam(taskParam);
                log.info("批次任务参数已更新，包含完整素材信息");
            } catch (Exception e) {
                log.error("构建完整任务参数失败，使用原始参数: {}", e.getMessage(), e);
                fullTaskParam = taskParam; // 失败时使用原始参数
            }
        }

        dto.setTaskParam(fullTaskParam);

        log.debug("构建BatchDTO完成 - 类型: {}, 图片URL: {}", type, imgUrl);
        return dto;
    }

    /**
     * 保存文件到临时目录
     */
    private List<Path> saveFilesToTempDir(List<MultipartFile> files) throws IOException {
        List<Path> savedPaths = new ArrayList<>();

        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                log.warn("跳过空文件: {}", file.getOriginalFilename());
                continue;
            }

            Path savedPath = saveFileToTempDir(file);
            savedPaths.add(savedPath);
        }

        return savedPaths;
    }

    /**
     * 保存单个文件到临时目录
     */
    private Path saveFileToTempDir(MultipartFile file) throws IOException {
        // 生成唯一文件名
        String fileName = generateUniqueFileName(file.getOriginalFilename());
        Path targetPath = Paths.get(FileUtil.TEMP_DIR_PATH, fileName);

        // 确保目录存在
        ensureDirectoryExists(targetPath.getParent());

        // 保存文件
        try (InputStream in = file.getInputStream()) {
            Files.copy(in, targetPath, StandardCopyOption.REPLACE_EXISTING);
            return targetPath;
        } catch (IOException e) {
            log.error("文件保存失败: {}", targetPath, e);
            throw new IOException("文件保存失败: " + targetPath, e);
        }
    }

    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String fileExtension = originalFilename.lastIndexOf(".") > 0 ?
                originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
        return UUID.randomUUID().toString() + "-" + originalFilename + fileExtension;
    }

    /**
     * 批次创建上下文类
     */
    private static class BatchCreationContext {
        private final ProcessedTableResult tableResult;
        private final ProcessedFilesResult filesResult;
        private final BatchDTO batchDTO;
        private final Long userId;
        private final List<MultipartFile> originalFiles;

        public BatchCreationContext(ProcessedTableResult tableResult, ProcessedFilesResult filesResult,
                                    BatchDTO batchDTO, Long userId, List<MultipartFile> originalFiles) {
            this.tableResult = tableResult;
            this.filesResult = filesResult;
            this.batchDTO = batchDTO;
            this.userId = userId;
            this.originalFiles = originalFiles;
        }

        public ProcessedTableResult getTableResult() {
            return tableResult;
        }

        public ProcessedFilesResult getFilesResult() {
            return filesResult;
        }

        public BatchDTO getBatchDTO() {
            return batchDTO;
        }

        public Long getUserId() {
            return userId;
        }

        public List<MultipartFile> getOriginalFiles() {
            return originalFiles;
        }
    }

    /**
     * 表格处理结果类
     */
    private static class ProcessedTableResult {
        private final byte[] tableBytes;
        private final MultipartFile processedTable;

        public ProcessedTableResult(byte[] tableBytes, MultipartFile processedTable) {
            this.tableBytes = tableBytes;
            this.processedTable = processedTable;
        }

        public byte[] getTableBytes() {
            return tableBytes;
        }

        public MultipartFile getProcessedTable() {
            return processedTable;
        }
    }

    /**
     * 文件处理结果类
     */
    private static class ProcessedFilesResult {
        private final String imgUrl;
        private final List<byte[]> fileBytesList;
        private final List<Path> pathList;

        public ProcessedFilesResult(String imgUrl, List<byte[]> fileBytesList, List<Path> pathList) {
            this.imgUrl = imgUrl;
            this.fileBytesList = fileBytesList;
            this.pathList = pathList;
        }

        public String getImgUrl() {
            return imgUrl;
        }

        public List<byte[]> getFileBytesList() {
            return fileBytesList;
        }

        public List<Path> getPathList() {
            return pathList;
        }
    }

    public List<Task> getTaskInfo(List<Task> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            list = list.stream().map(
                    map -> {
                        Task taskNew = new Task();
                        BeanUtils.copyProperties(map, taskNew);
                        taskNew.setOriginalUrl(CommonUtils.addCosPrefix(taskNew.getOriginalUrl()));
                        taskNew.setThumbnailUrl(CommonUtils.addCosPrefix(taskNew.getThumbnailUrl()));
                        return taskNew;
                    }).collect(Collectors.toList());
            for (Task taskDB : list) {
                TaskOrdinal taskOrdinal = new TaskOrdinal();
                taskOrdinal.setDelFlag(0L);
                taskOrdinal.setTaskId(taskDB.getTaskId());

                List<TaskOrdinal> taskOrdinals = taskOrdinalMapper.selectTaskOrdinalList(taskOrdinal);
//                List<TaskOrdinal> taskOrdinals = taskOrdinalMapper.selectTaskOrdinalListInfo(taskOrdinal);
                if (CollectionUtil.isNotEmpty(taskOrdinals)) {
                    for (TaskOrdinal ordinal : taskOrdinals) {
                        OrdinalImgResult ordinalImgResult = new OrdinalImgResult();
                        ordinalImgResult.setTaskOrdinalId(ordinal.getTaskOrdinalId());
                        ordinalImgResult.setTaskId(ordinal.getTaskId());
                        ordinalImgResult.setDelFlag(0l);
                        ordinal.setOriginImgUrl(CommonUtils.addCosPrefix(ordinal.getOriginImgUrl()));
                        List<OrdinalImgResult> ordinalImgResults = ordinalImgResultMapper.selectOrdinalImgResultList(ordinalImgResult);
                        for (OrdinalImgResult y : ordinalImgResults) {
                            y.setOriginalImgUrl(CommonUtils.addCosPrefix(y.getOriginalImgUrl()));
                            y.setResImgUrl(CommonUtils.addCosPrefix(y.getResImgUrl()));
                            y.setResSmallImgUrl(CommonUtils.addSizeHalf(y.getResSmallImgUrl()));
                            y.setResWhiteImgUrl(CommonUtils.addCosPrefix(y.getResWhiteImgUrl()));
                        }
                        ordinal.setOrdinalImgResultList(ordinalImgResults);
                    }

                    taskDB.setTaskOrdinalList(taskOrdinals);
                }
            }
        }
        return list;
    }

    @Override
    public Batch getOne(String batchId) {
        Batch batch = batchMapper.selectById(batchId);
        if (batch != null) {
            Task task = new Task();
            task.setBatchId(batchId);
            task.setDelFlag(0);
            task.setStatus(Constants.TASK_STATUS_SUCCESS);
            List<Task> list = taskMapper.selectTaskList(task);
            list = getTaskInfo(list);
            batch.setTasks(list);
        }
        return batch;
    }

    @Override
    public Integer logicDeleteTaskByTaskIds(Long userId, String batchId) {
        Batch batchDB = batchMapper.selectById(batchId);
        if (batchDB.getStatus() == Constants.TASK_STATUS_SUCCESS) {
            throw new ServiceException("批次任务已结束 无法操作");
        }
        Batch batch = new Batch();
        batch.setDelFlag(1);
        batch.setBatchId(batchId);
        Integer count = batchMapper.updateBat(batch);
        Task task = new Task();
        task.setBatchId(batchId);
        List<Task> tasks = taskMapper.selectTaskList(task);
        List<String> taskIds = tasks.stream().map(map -> map.getTaskId()).collect(Collectors.toList());
        String[] array = taskIds.toArray(new String[0]);
        if (CollectionUtil.isNotEmpty(taskIds)) {
            taskMapper.logicDeleteTaskOrdinalByTaskIds(userId, array);
            taskMapper.logicDeleteTaskByTaskIds(userId, array);
        }
        return count;
    }

    /**
     * 终止任务
     *
     * @param userId
     * @param batchId
     * @return
     */
    @Override
    public Integer stop(Long userId, String batchId) {
        Batch batchDB = batchMapper.selectById(batchId);
        if (batchDB.getStatus() == Constants.TASK_STATUS_SUCCESS) {
            throw new ServiceException("批次任务已结束 无法操作");
        }
        Batch batch = new Batch();
        batch.setStatus(Constants.TASK_STATUS_STOP);
        batch.setBatchId(batchId);
        Integer count = batchMapper.updateBat(batch);

        // 仅终止排队中的任务（状态=4），正在执行(3)与已完成(1)不处理
        Task queryTask = new Task();
        queryTask.setBatchId(batchId);
        queryTask.setDelFlag(0);
        List<Task> allTasks = taskMapper.selectTaskList(queryTask);

        if (CollectionUtil.isNotEmpty(allTasks)) {
            for (Task task : allTasks) {
                // 只更新排队中的任务
                if (task.getStatus() == Constants.TASK_STATUS_PARKING) {
                    Task updateTask = new Task();
                    updateTask.setTaskId(task.getTaskId());
                    updateTask.setStatus(Constants.TASK_STATUS_STOP);
                    taskMapper.updateTask(updateTask);
                    taskMapper.updateTaskStatus(updateTask);

                    // 更新对应的任务执行次数状态
                    TaskOrdinal taskOrdinal = new TaskOrdinal();
                    taskOrdinal.setTaskId(task.getTaskId());
                    taskOrdinal.setStatus(Constants.TASK_STATUS_STOP);
                    taskOrdinalMapper.updateTaskOrdinalStatus(taskOrdinal);
                }
            }
        }

        // 清理批次相关的临时资源
        cleanupBatchTempResources(batchId);

        return count;
    }

    /**
     * 清理批次临时资源
     *
     * @param batchId 批次ID
     */
    private void cleanupBatchTempResources(String batchId) {
        try {
            log.info("开始清理终止批次 {} 的临时资源", batchId);

            // 清理与批次相关的临时文件夹
            int cleanedCount = FileUtil.cleanupBatchTempFolders(batchId);

            // 清理空的临时文件夹
            int emptyFolderCount = FileUtil.cleanupEmptyTempFolders();

            log.info("终止批次 {} 临时资源清理完成，清理批次相关文件夹 {} 个，清理空文件夹 {} 个",
                    batchId, cleanedCount, emptyFolderCount);

        } catch (Exception e) {
            log.error("清理终止批次 {} 临时资源时发生异常: {}", batchId, e.getMessage(), e);
        }
    }

    @Override
    public Integer updateImage(MultipartFile file, String imageId) throws IOException {
        OrdinalImgResult ordinalImgResult = ordinalImgResultMapper.selectOrdinalImgResultByImageId(imageId);
        if (ordinalImgResult == null) {
            throw new ServiceException("图片结果不存在");
        }
        String resImgUrl = aliYunFileService.uploadALiYun(file);
        ordinalImgResult.setResImgUrl(CommonUtils.subCosPrefix(resImgUrl));
        ordinalImgResult.setResSmallImgUrl(CommonUtils.subCosPrefix(resImgUrl + Constants.OSS_URL_HALF));
        ordinalImgResult.setUpdateTime(new Date());
        Integer count = ordinalImgResultMapper.updateOrdinalImgResult(ordinalImgResult);
        return count;
    }

    /**
     * 将图片同步到设计器
     */
    @Override
    public String synchronization(ZIPDTO dto) throws Exception {
        // 处理新的ZIPDTO格式，将List<String>转换为逗号分隔的字符串
        String imageUrls;
        if (dto.getImageUrls() != null && !dto.getImageUrls().isEmpty()) {
            imageUrls = String.join(",", dto.getImageUrls());
        } else {
            throw new ServiceException("图片地址为空");
        }

        String phone = SecurityUtils.getLoginUser().getAppUser().getPhone();
        System.out.println("登录人的手机号" + phone);
        if (StringUtils.isNotEmpty(phone)) {
            String[] split = imageUrls.split(",");
            String subUrl = CommonUtils.subCosPrefix(split[0]);

            // 如果指定了type，直接使用指定的type
            String typeToString = null;
            if (dto.getType() != null) {
                if ("16".equals(dto.getType())) {
                    typeToString = "产品信息";
                } else if ("17".equals(dto.getType())) {
                    typeToString = "风险检测详情";
                } else {
                    // 其他类型，先去结果表查数据res_img_url/res_white_img_url，没有的话就去主任务表查original_url
                    OrdinalImgResult imgResult = ordinalImgResultMapper.selectTypeByUrl(subUrl);
                    if (imgResult != null) {
                        typeToString = TaskTypeUtils.getTaskTypeName(imgResult.getType());
                    } else {
                        if (null != dto.getBatchId()) {
                            Task task = taskMapper.selectTypeByUrlAndBatchId(subUrl, dto.getBatchId());
                            if (task != null) {
                                typeToString = TaskTypeUtils.getTaskTypeName(task.getType());
                            } else {
                                // 如果查询不到任务信息，使用默认类型名称
                                typeToString = "未知类型";
                            }
                        } else {
                            Task task = taskMapper.selectTypeByUrl(subUrl);
                            if (task != null) {
                                typeToString = TaskTypeUtils.getTaskTypeName(task.getType());
                            } else {
                                // 如果查询不到任务信息，使用默认类型名称
                                typeToString = "未知类型";
                            }
                        }
                    }
                }
            } else {
                // 没有指定type，使用原来的逻辑
                OrdinalImgResult imgResult = ordinalImgResultMapper.selectTypeByUrl(subUrl);
                if (imgResult != null) {
                    typeToString = TaskTypeUtils.getTaskTypeName(imgResult.getType());
                } else {
                    if (null != dto.getBatchId()) {
                        Task task = taskMapper.selectTypeByUrlAndBatchId(subUrl, dto.getBatchId());
                        if (task != null) {
                            typeToString = TaskTypeUtils.getTaskTypeName(task.getType());
                        } else {
                            // 如果查询不到任务信息，使用默认类型名称
                            typeToString = "未知类型";
                        }
                    } else {
                        Task task = taskMapper.selectTypeByUrl(subUrl);
                        if (task != null) {
                            typeToString = TaskTypeUtils.getTaskTypeName(task.getType());
                        } else {
                            // 如果查询不到任务信息，使用默认类型名称
                            typeToString = "未知类型";
                        }
                    }
                }
            }
            // 调用设计器接口 获得请求
            String currentTime = String.valueOf(System.currentTimeMillis());
            // 构造请求体数据
            Map<String, Object> badyData = new HashMap<>();
            badyData.put("phone", phone);
            badyData.put("images", imageUrls);
            badyData.put("name", typeToString);
            // 添加tags字段，如果前端传入了tags则使用，否则使用空字符串
            String tags = dto.getTags() != null ? dto.getTags() : "";
            badyData.put("tags", tags);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("time", currentTime);
            requestBody.put("data", badyData);
            // 注意：此方法已废弃，现在使用PlatformApiService
            // 为了保持向后兼容，这里返回成功信息
            log.warn("synchronization方法已废弃，请使用新的平台API服务");
            return "此方法已废弃，请使用新的平台API服务";
        } else {
            throw new ServiceException("用户手机号不存在");
        }
    }

    @Override
    public Map<String, Object> fixBatchStatistics(String batchId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询批次信息
            Batch batch = batchMapper.selectById(batchId);
            if (batch == null) {
                result.put("success", false);
                result.put("message", "批次不存在");
                return result;
            }

            // 查询该批次下的所有任务
            Task taskQuery = new Task();
            taskQuery.setBatchId(batchId);
            List<Task> tasks = taskMapper.selectTaskList(taskQuery);

            if (tasks == null || tasks.isEmpty()) {
                result.put("success", false);
                result.put("message", "批次下没有任务");
                return result;
            }

            // 统计实际的成功和失败数量
            int actualSuccessCount = 0;
            int actualFailCount = 0;
            int terminatedCount = 0;

            for (Task task : tasks) {
                if (task.getStatus() != null) {
                    if (task.getStatus() == Constants.TASK_STATUS_SUCCESS) {
                        actualSuccessCount++;
                    } else if (task.getStatus() == Constants.TASK_STATUS_FAILED) {
                        actualFailCount++;
                    } else if (task.getStatus() == Constants.TASK_STATUS_STOP) {
                        terminatedCount++;
                    }
                }
            }

            // 更新批次统计数据
            Batch updateBatch = new Batch();
            updateBatch.setBatchId(batchId);
            updateBatch.setSuccessAmount(actualSuccessCount);
            updateBatch.setFailAmount(actualFailCount);
            updateBatch.setUpdateTime(new Date());

            // 如果批次已终止或已删除，保持终止状态；否则根据完成情况设置状态
            if (batch.getStatus() != Constants.TASK_STATUS_STOP && batch.getDelFlag() != 1) {
                if (actualSuccessCount + actualFailCount >= batch.getTotalAmount()) {
                    updateBatch.setStatus(Constants.TASK_STATUS_SUCCESS);
                } else {
                    updateBatch.setStatus(Constants.TASK_STATUS_EXECUTING);
                }
            }

            batchMapper.updateBat(updateBatch);

            result.put("success", true);
            result.put("message", "统计数据修复成功");
            result.put("batchId", batchId);
            result.put("originalSuccess", batch.getSuccessAmount());
            result.put("originalFail", batch.getFailAmount());
            result.put("fixedSuccess", actualSuccessCount);
            result.put("fixedFail", actualFailCount);
            result.put("terminatedCount", terminatedCount);
            result.put("totalTasks", tasks.size());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 为工作流创建批次（不扣除积分）
     */
    @Override
    public Batch createWorkflowBatch(BatchDTO dto, Long userId, Long teamId) {
        try {
            Batch batch = new Batch();
            BeanUtils.copyProperties(dto, batch);
            Date date = new Date();
            batch.setCreateTime(date);
            batch.setUpdateTime(date);

            // 使用传入的用户ID
            batch.setUserId(userId);

            // 设置团队信息
            if (teamId != null && teamId > 0) {
                batch.setTeamId(teamId);
            } else {
                batch.setTeamId(0L); // 个人模式
            }

            // 获取时间戳（毫秒级）
            String timestamp = String.valueOf(System.currentTimeMillis());
            batch.setBatchNumber(userId.toString() + timestamp);

            // 计算素材数量
            Integer amount = 0;
            if (dto.getImgUrl() != null && !dto.getImgUrl().trim().isEmpty()) {
                amount = dto.getImgUrl().split(",").length;
            }
            batch.setTotalAmount(amount);

            batch.setDelFlag(0);
            batch.setStatus(Constants.TASK_STATUS_PARKING);
            String batchId = SnowFlakeUtils.nextIdStr();
            batch.setBatchId(batchId);

            batchMapper.insertBat(batch);

            log.info("工作流批次创建成功，批次ID: {}, 用户ID: {}, 团队ID: {}, 素材数量: {}",
                    batchId, userId, teamId, amount);

            return batch;

        } catch (Exception e) {
            log.error("工作流批次创建失败，用户ID: {}, 团队ID: {}", userId, teamId, e);
            throw new RuntimeException("工作流批次创建失败: " + e.getMessage());
        }
    }

    private Batch insertBatchInfo(BatchDTO dto, Integer amount) {
        Batch batch = new Batch();
        BeanUtils.copyProperties(dto, batch);
        Date date = new Date();
        batch.setCreateTime(date);
        batch.setUpdateTime(date);
        Long userId = dto.getUserId();
        if (userId == null) {
            userId = SecurityUtils.getUserId();
        }

        // 获取用户的团队信息并设置team_id
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(userId);
        if (userTeamInfo != null && userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：设置团队ID
            batch.setTeamId(userTeamInfo.getTeamId());
        } else {
            // 个人模式：设置为0表示个人批次
            batch.setTeamId(0L);
        }

        // 获取时间戳（毫秒级）
        String timestamp = String.valueOf(System.currentTimeMillis());
        batch.setUserId(userId);
        batch.setBatchNumber(userId.toString() + timestamp);
        batch.setTotalAmount(amount);
        batch.setDelFlag(0);
        batch.setStatus(Constants.TASK_STATUS_PARKING);
        String batchId = SnowFlakeUtils.nextIdStr();
        batch.setBatchId(batchId);
        batchMapper.insertBat(batch);
        return batch;

    }

    /**
     * 验证积分够不够扣
     *
     * @param dto
     * @param table
     */
    private Integer checkIntegral(BatchDTO dto, MultipartFile table, List<MultipartFile> files, List<Path> pathList) throws JsonProcessingException {
        // 计算本次任务应消耗的素材数量 amount 以及积分 integral
        // 说明：
        //  - 除批量上传类型外，均按不同任务类型与入参来源(imgUrl/表格/files)计算扣减积分
        //  - 批量上传（TASK_TYPE_BATCH_UPLOAD）每张图片按 1 分计
        //  - 文生图/平铺-文生图 按 4 分/张计
        //  - 相似图裂变 按 4 分/张计
        //  - 去背景/变清晰/自动裁剪 默认 1 分/张；变清晰在放大倍率为 4 时按 2 分/张计
        if (dto == null || dto.getType() == null) {
            throw new ServiceException("批次类型不能为空");
        }
        int typeInt = dto.getType().intValue();
        // 支持进行积分扣减计算的任务类型
        List<Integer> supportedTypes = Arrays.asList(
                Constants.TASK_TYPE_BATCH_UPLOAD,
                Constants.TASK_TYPE_TEXT_TO_IMAGE,
                Constants.TASK_TYPE_FOUR_TEXT,
                Constants.TASK_TYPE_REMOVE_BG,
                Constants.TASK_TYPE_ENHANCE,
                Constants.TASK_TYPE_AUTO_CROP_TEXTURE,
                Constants.TASK_TYPE_SIMILAR_SPLIT,
                Constants.TASK_TYPE_CROP_EXTRACT
        );
        if (!supportedTypes.contains(typeInt)) {
            throw new ServiceException("任务类型不符");
        }

        Integer amount = 0;
        Long integral = 0L;

        // 非批量上传类型：需要根据类型与数据来源计算
        if (dto.getType() != Constants.TASK_TYPE_BATCH_UPLOAD) {
            // 必须提供图片 URL 的类型：去背景/变清晰/自动裁剪/相似图裂变/印花图提取
            if ((dto.getType() == Constants.TASK_TYPE_REMOVE_BG
                    || dto.getType() == Constants.TASK_TYPE_ENHANCE
                    || dto.getType() == Constants.TASK_TYPE_AUTO_CROP_TEXTURE
                    || dto.getType() == Constants.TASK_TYPE_SIMILAR_SPLIT
                    || dto.getType() == Constants.TASK_TYPE_CROP_EXTRACT)
                    && StringUtils.isEmpty(dto.getImgUrl())) {
                throw new ServiceException("图片地址不能为空");
            } else if ((dto.getType() == Constants.TASK_TYPE_REMOVE_BG
                    || dto.getType() == Constants.TASK_TYPE_ENHANCE
                    || dto.getType() == Constants.TASK_TYPE_AUTO_CROP_TEXTURE
                    || dto.getType() == Constants.TASK_TYPE_SIMILAR_SPLIT
                    || dto.getType() == Constants.TASK_TYPE_CROP_EXTRACT)
                    && StringUtils.isNotEmpty(dto.getImgUrl())) {
                // 使用 imgUrl 计算素材数量，若 pathList 有值优先以 pathList.size() 为准
                String[] split = dto.getImgUrl().split(",");
                int slength = (pathList != null && !pathList.isEmpty()) ? pathList.size() : split.length;
                amount = slength;
                // 默认每张 1 分
                integral = (long) slength;
                // 相似图裂变：每张 4 分
                if (dto.getType() == Constants.TASK_TYPE_SIMILAR_SPLIT) {
                    integral = (long) slength * 4;
                }
                // 变清晰：放大倍率为 4 时，每张 2 分
                if (dto.getType() == Constants.TASK_TYPE_ENHANCE) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    OrdinalParamDTO param = new OrdinalParamDTO();
                    if (StringUtils.isNotEmpty(dto.getTaskParam())) {
                        param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
                    }
                    if (param.getMagnification() != null && param.getMagnification() == 4) {
                        integral = (long) slength * 2;
                    }
                }
            }

            // 文生图/平铺-文生图：按 4 分/张（imgUrl 场景）
            if ((dto.getType() == Constants.TASK_TYPE_TEXT_TO_IMAGE || dto.getType() == Constants.TASK_TYPE_FOUR_TEXT)
                    && StringUtils.isNotEmpty(dto.getImgUrl())) {
                String[] split = dto.getImgUrl().split(",");
                int slength = (pathList != null && !pathList.isEmpty()) ? pathList.size() : split.length;
                amount = slength;
                integral = (long) slength * 4;
            }

            // 文生图/平铺-文生图：按 4 分/张（表格场景）
            if ((dto.getType() == Constants.TASK_TYPE_TEXT_TO_IMAGE || dto.getType() == Constants.TASK_TYPE_FOUR_TEXT) && table != null) {
                List<PromptDTO> promptDTO = aliYunFileService.parseTable(table);
                amount = promptDTO.size();
                integral = (long) promptDTO.size() * 4;
            }
        } else {
            // 批量上传：每张 1 分
            amount = files.size();
            integral = (long) files.size();
        }

        // 执行积分扣减前打印扣减信息
        Long currentUserId = dto.getUserId() != null ? dto.getUserId() : SecurityUtils.getUserId();
        log.info("积分扣减明细 -> 用户ID: {}, 任务类型: {}({}), 素材数量: {}, 扣减积分: {}",
                currentUserId,
                dto.getType(),
                TaskTypeUtils.getTaskTypeName(dto.getType().intValue()),
                amount,
                integral);

        boolean deductioned = taskScoreService.deductionPoints(currentUserId, integral, Constants.SCORE_TYPE_4, dto.getType().intValue());
        if (!deductioned) {
            throw new ServiceException(CommonStatusEnum.SCORE_ERROR.getMessage(), CommonStatusEnum.SCORE_ERROR.getCode());
        }
        return amount;
    }


    /**
     * 更新批次信息
     *
     * @param batch 批次对象
     * @return 更新结果
     */
    @Override
    public Integer updateBat(Batch batch) {
        if (batch == null || batch.getBatchId() == null) {
            throw new ServiceException("批次信息不能为空");
        }

        try {
            // 设置更新时间
            batch.setUpdateTime(new Date());
            Integer result = batchMapper.updateBat(batch);
            log.info("更新批次信息成功，批次ID: {}", batch.getBatchId());
            return result;
        } catch (Exception e) {
            log.error("更新批次信息失败，批次ID: {}", batch.getBatchId(), e);
            throw new ServiceException("更新批次信息失败: " + e.getMessage());
        }
    }

    @Override
    @Async("taskExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processAsyncTasks(BatchCreationResult result, Long userId) {
        System.out.println("工厂方法处理开始");
        System.out.println( result);
        System.out.println("----------------------------------------------------------------------");
        try {
            // 调用工厂管理器处理批次任务
            batchTaskFactoryManager.processBatchTask(
                    result.getBatchDTO(),
                    result.getBatch(),
                    result.getFileBytesList(),
                    result.getTableBytes(),
                    userId,
                    result.getOriginalFiles(),
                    result.getPathList() == null ? new ArrayList<>() : result.getPathList()
            );
        } catch (Exception e) {
            log.error("批次任务异步处理失败 - 批次ID：{}，任务类型：{}，错误：{}",
                    result.getBatch().getBatchId(), result.getBatchDTO().getType(), e.getMessage(), e);
        } finally {
            try {
                // 清理临时资源
                FileUtil.cleanupBatchTempFolders(result.getBatch().getBatchId());
                FileUtil.cleanupEmptyTempFolders();
            } catch (Exception ex) {
                log.warn("清理批次临时资源失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 判断是否为文生图家族（8 文生图 / 6 平铺文生图 / 7 平铺图生图）
     */
    private boolean isTextToImageType(Long taskType) {
        if (taskType == null) return false;
        int t = taskType.intValue();
        return t == Constants.TASK_TYPE_TEXT_TO_IMAGE
                || t == Constants.TASK_TYPE_FOUR_TEXT
                || t == Constants.TASK_TYPE_FOUR_IMAGE;
    }
}




