package com.dataxai.web.Constants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 系统常量定义类
 * @version 1.0
 * @Author: xg
 * @Date: 2024-01-02 10:15
 * @Description: 定义系统中使用的各种常量，包括认证、文件存储、任务状态、API地址等
 */
public class Constants {

    // ==================== 认证相关常量 ====================
    /** 访问令牌字段名 */
    public static final String ACCESS_TOKEN = "access_token";
    /** 授权头字段名 */
    public static final String Authorization = "Authorization";

    // ==================== 环境标识常量 ====================
    /** 开发环境标识 */
    public static final String ACTIVE_DEV = "dev";
    /** 生产环境标识 */
    public static final String ACTIVE_PROD = "prod";
    /** 本地环境标识 */
    public static final String ACTIVE_LOCAL = "local";

    // ==================== 文件存储相关常量 ====================
    /**
     * 文件存储服务地址前缀
     * 原腾讯云地址: http://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/
     * 阿里云内网地址: http://ai-photo-task-1303206685.oss-cn-huhehaote-internal.aliyuncs.com/
     * 阿里云公网地址: http://ai-photo-task-1303206685.oss-cn-huhehaote.aliyuncs.com/
     */
    public static final String OOS_URL_PREFIX = "https://image-task.xiaoaishop.com/";

    /** 阿里云图片缩略图处理参数 - 宽高300px */
    public static final String OOS_URL_THUMBNAIL = "?x-oss-process=image/resize,w_300";
    /** 阿里云图片缩略图处理参数 - 原尺寸的一半 */
    public static final String OSS_URL_HALF = "?x-oss-process=image/resize,p_50";

    // ==================== ID生成器常量 ====================
    /** 任务ID生成器Redis键 */
    public static final String ID_GENERATOR_TASK = "id:generator:task";
    /** 用户ID生成器Redis键 */
    public static final String ID_GENERATOR_USER = "id:generator:user";

    // ==================== 用户属性相关常量 ====================
    /** 性别字段中文名 */
    public static final String GENDER_STR_ZH = "性别";
    /** 年龄字段中文名 */
    public static final String AGE_STR_ZH = "年龄";
    /** 肤色字段中文名 */
    public static final String SKIN_STR_ZH = "肤色";

    // ==================== 任务状态相关常量 ====================
    /** 任务执行成功状态值 */


    /**
     * 任务执行状态常量
     * 0-编辑中，1-成功，2-失败，3-执行中，4-排队中，5-准备中，6-终止
     */
    public static final int TASK_STATUS_EDIT = 0;        // 编辑中
    public static final int TASK_STATUS_SUCCESS = 1;     // 成功
    public static final int TASK_STATUS_FAILED = 2;      // 失败
    public static final int TASK_STATUS_EXECUTING = 3;   // 执行中
    public static final int TASK_STATUS_PARKING = 4;     // 排队中
    public static final int TASK_STATUS_PREPARING = 5;   // 准备中
    public static final int TASK_STATUS_STOP = 6;        // 终止

    // ==================== GPU服务API地址常量 ====================
    /** 开发环境GPU分割任务执行地址 */
    public static final String GPU_EXECUTE_SEG_TASK_URL_DEV = "http://*************:8003/seg/";
    /** 生产环境GPU分割任务执行地址 */
    public static final String GPU_EXECUTE_SEG_TASK_URL_PROD = "http://**************:8003/seg/";

    /** 开发环境GPU任务执行地址 */
    public static final String GPU_EXECUTE_TASK_URL_DEV = "http://*************:8003/interface/";
    /** 生产环境GPU任务执行地址 */
    public static final String GPU_EXECUTE_TASK_URL_PROD = "http://**************:8003/interface/";

    /** 开发环境GPU 4K任务执行地址 */
    public static final String GPU_EXECUTE_4k_TASK_URL_DEV = "http://*************:8003/Enhance/";
    /** 生产环境GPU 4K任务执行地址 */
    public static final String GPU_EXECUTE_4k_TASK_URL_PROD = "http://**************:8003/Enhance/";

    /** 开发环境GPU图片缩放地址 */
    public static final String GPU_RESIZE_URL_DEV = "http://*************:8004/resize";
    /** 生产环境GPU图片缩放地址 */
    public static final String GPU_RESIZE_URL_PROD = "http://**************:8004/resize";

    /** 开发环境GPU图片描述生成地址 */
    public static final String GPU_CAPTION_URL_DEV = "http://*************:8003/caption/";
    /** 生产环境GPU图片描述生成地址 */
    public static final String GPU_CAPTION_URL_PROD = "http://**************:8003/caption/";

    // ==================== 素材创作相关API地址 ====================
    /** 开发环境素材创作/编辑地址 */
    public static final String GPU_EXECUTE_NEW_TASK_DEV = "http://*************:8004/pod_Resource/";
    /** 生产环境素材创作/编辑地址 */
    public static final String GPU_EXECUTE_NEW_TASK_PROD = "http://**************:8004/pod_Resource/";

    /** 开发环境批量素材创作/编辑地址 */
    public static final String GPU_EXECUTE_BATCH_TASK_DEV = "http://*************:8005/pod_Resource/";
    /** 生产环境批量素材创作/编辑地址 */
    public static final String GPU_EXECUTE_BATCH_TASK_PROD = "http://**************:8005/pod_Resource/";

    // ==================== AI智能服务API地址 ====================
    /** 开发环境智能联想服务地址 */
    public static final String GPU_ASSOCIATION_DEV = "http://*************:8004/pod_ai_t2t/";
    /** 生产环境智能联想服务地址 */
    public static final String GPU_ASSOCIATION_PROD = "http://**************:8004/pod_ai_t2t/";

    /** 开发环境识图转文服务地址 */
    public static final String GPU_READ_PICTURES_DEV = "http://*************:8004/pod_ai_i2t/";
    /** 生产环境识图转文服务地址 */
    public static final String GPU_READ_PICTURES_PROD = "http://**************:8004/pod_ai_i2t/";

    // ==================== Socket.IO推送相关常量 ====================
    /** 开发环境Socket.IO推送地址 */
    public static final String SOCKETIO_PUSH_URL_DEV = "http://localhost:8080/push2";
    /** 本地环境Socket.IO推送地址 */
    public static final String SOCKETIO_PUSH_URL_LOCAL = "http://localhost:8090/push2";
    /** 生产环境Socket.IO推送地址 */
    public static final String SOCKETIO_PUSH_URL_PROD = "http://localhost:8080/push2";

    /** 推送事件类型字段名 */
    public static final String PUSH_EVENT_TYPE_KEY = "pushType";
    /** 推送消息字段名 */
    public static final String PUSH_EVENT_TYPE_MESSAGE_KEY = "message";
    /** 任务状态推送事件类型 */
    public static final String PUSH_EVENT_TYPE_TASK_STATUS = "task_status";

    /**
     * 推送事件类型常量
     * 0-seg分割，1-task_status任务状态，2-关注微信公众号推送，3-pay支付，4-4k推送，5-心跳，6-推送描述
     */
    public static final Integer PUSH_EVENT_TYPE_SEG = 0;           // 分割
    public static final Integer PUSH_EVENT_TYPE_STATUS = 1;        // 任务状态
    public static final Integer PUSH_EVENT_TYPE_GZH = 2;           // 关注微信公众号推送
    public static final Integer PUSH_EVENT_TYPE_PAY = 3;           // 支付
    public static final Integer PUSH_EVENT_TYPE_STATUS_4K = 4;     // 4K推送
    public static final Integer PUSH_EVENT_TYPE_HEARTBEAT = 5;     // 心跳
    public static final Integer IMAGE_CHANGE_TEXT = 6;             // 图片转文字推送

    // ==================== 任务类型常量 ====================
    /**
     * 任务类型常量
     * 0-真人图，1-人台图，5-白板图，6-平铺图，
     * 8-文生图，9-相似图裂变，10-图片裁剪，
     * 11-图片去背景，12-图片变清晰，15-批量上传图片，
     * 16-产品采集，17-侵权风险过滤
     */
    public static final int TASK_TYPE_REAL_PERSON = 0;          // 真人图
    public static final int TASK_TYPE_MANNEQUIN = 1;            // 人台图
    public static final int TASK_TYPE_WHITEBOARD = 5;           // 白板图
    public static final int TASK_TYPE_FOUR_TEXT = 6;            // 平铺图 - 文生图
    public static final int TASK_TYPE_FOUR_IMAGE = 7;           // 平铺图 - 图生图
    public static final int TASK_TYPE_TEXT_TO_IMAGE = 8;        // 文生图
    public static final int TASK_TYPE_SIMILAR_SPLIT = 9;        // 相似图裂变
    public static final int TASK_TYPE_CROP_TEXTURE = 10;        // 图片裁剪
    public static final int TASK_TYPE_REMOVE_BG = 11;           // 图片去背景
    public static final int TASK_TYPE_ENHANCE = 12;             // 图片变清晰
    public static final int TASK_TYPE_CREATIVE_EXTRACT = 13;    // 创意图提取
    public static final int TASK_TYPE_AUTO_CROP_TEXTURE = 14;   // 图片自动裁剪
    public static final int TASK_TYPE_BATCH_UPLOAD = 15;        // 批量上传图片
    public static final int TASK_TYPE_PRODUCT_COLLECTION = 16;  // 产品采集
    public static final int TASK_TYPE_RISK_FILTER = 17;         // 侵权风险过滤
    public static final int TASK_TYPE_TITLE_EXTRACT = 18;       // 标题提取
    public static final int TASK_TYPE_CROP_EXTRACT = 52;        // 印花图提取

    // ==================== 队列类型常量 ====================
    /** 素材创作队列 */
    public static final String QUEUE_MATERIAL = "material";
    /** AI商拍队列 */
    public static final String QUEUE_AISNAP = "aiSnap";
    /** 蒙版队列 */
    public static final String QUEUE_MASK = "mask";
    /** 图片缩放队列 */
    public static final String QUEUE_RESIZE = "resize";
    /** 识图转文队列 */
    public static final String QUEUE_AIOCR = "aiOcr";
    /** 智能联想队列 */
    public static final String QUEUE_ASSOCIATE = "associate";
    /** 图片上传队列 */
    public static final String QUEUE_UPLOAD_IMAGE = "uploadImage";
    /** 风险检测队列 */
    public static final String QUEUE_RISK = "risk";
    /** 标题提取队列 */
    public static final String QUEUE_TITLE_EXTRACT = "titleExtract";

    // ==================== 短信服务相关常量 ====================
    /** 发送短信最小间隔时间（毫秒） */
    public static final Long SMS_MIN_INTERVAL_MINUTE = 60000L;
    /** 短信手机号Redis存储前缀 */
    public static final String SMS_PER = "sms:phone:";
    /** 短信发送间隔过短错误码 */
    public static final Integer SMS_MIN_INTERVAL_MINUTE_ERROR_CODE = 10000;

    // ==================== 订单相关常量 ====================
    /**
     * 订单类型常量
     * 0-加油包，1-套餐
     */
    public static final long ORDER_TYPE_TARIFF_PACKAGE = 0;    // 套餐
    public static final long ORDER_TYPE_REFUELING_BAG = 1;     // 加油包

    /** 订单未支付状态 */
    public static final String ORDER_STATUS_NOTPAY = "NOTPAY";

    // ==================== 任务描述类型常量 ====================
    /**
     * 任务描述类型常量
     * 0-快捷描述，1-预设模块，2-高级描述
     */
    public static final String DES_TYPE_0 = "0";    // 快捷描述
    public static final String DES_TYPE_1 = "1";    // 预设模块
    public static final String DES_TYPE_2 = "2";    // 高级描述

    // ==================== 积分相关常量 ====================
    /** 普通任务消耗积分 */
    public static final Long TASK_COMMON_SCORE = 4L;
    /** 4K任务消耗积分 */
    public static final Long TASK_COMMON_4k_SCORE = 1L;

    /**
     * 积分类型常量
     * 1-购买套餐，2-购买加油包，3-积分过期，4-任务消耗，5-再次生成，6-下载4k图，7-后台增加
     */
    public static final Long SCORE_TYPE_1 = 1L;     // 购买套餐
    public static final Long SCORE_TYPE_2 = 2L;     // 购买加油包
    public static final Long SCORE_TYPE_3 = 3L;     // 积分过期
    public static final Long SCORE_TYPE_4 = 4L;     // 任务消耗
    public static final Long SCORE_TYPE_5 = 5L;     // 再次生成
    public static final Long SCORE_TYPE_6 = 6L;     // 下载4k图
    public static final Long SCORE_TYPE_7 = 7L;     // 后台增加

    // ==================== 用户绑定相关常量 ====================
    /**
     * 绑定失败原因常量
     * 0-手机号已存在，1-绑定失败，微信已绑定其他用户
     */
    public static final Integer BIND_FAIL_PHONE_EXISTS = 0;        // 手机号已存在
    public static final Integer BIND_FAIL_WX_OPENID_EXISTS = 1;    // 微信已绑定其他用户

    // ==================== 用户状态相关常量 ====================
    /**
     * 用户启用状态常量
     * 0-未禁用，1-已禁用
     */
    public static final Integer ENABLE_1 = 1;       // 已禁用
    public static final Integer ENABLE_0 = 0;       // 未禁用
    /** 账号禁用提示信息 */
    public static final String ENABLE_1_Str = "账号出现异常，请联系客服人员！";

    // ==================== 系统配置相关常量 ====================
    /** 公司名称配置键 */
    public static final String COMPANY_NAME_KEY = "公司名称";
    /** 套餐结束时间Redis键 */
    public static final String KEY_TARIFF_END_TIME = "key_tariff_end_time";
    /** 套餐未过期积分Redis键 */
    public static final String KEY_TARIFF_UNEXPIRE_SCORE = "key_tariff_unexpire_score";
    /** 积分转余额配置键 */
    public static final String KEY_SCORETOLEFTMONEY = "scoretoleftmoney";
    /** 时长配置键 */
    public static final String KEY_DURATION = "duration";

    // ==================== HTTP请求相关常量 ====================
    /** HTTP请求超时时间（30秒） */
    public static final Integer HTTP_TIME_OUT = 30000;
    /** HTTP请求超时时间（60秒） */
    public static final Integer HTTP_TIME_OUT_60S = 60000;

    // ==================== 任务相关常量 ====================
    /** 任务名称前缀 */
    public static final String TASKNAME_PREFIX = "XAI";

    // ==================== 心跳相关常量 ====================
    /** 心跳请求标识 */
    public static final String PING = "ping";
    /** 心跳响应标识 */
    public static final String PONG = "pong";

    // ==================== 错误信息常量 ====================
    /** 图片上传失败错误信息 */
    public static final String PHOTO_ERROR = "图片上传失败";
    /** 图片尺寸错误信息 */
    public static final String PHOTO_MAXSIZE_ERROR = "调整的图片尺寸不能小于等于0 或大于1600";
    /** 图片违规错误信息 */
    public static final String IMG_VIOLATION = "图片违规,请更换图片";
    /** 支付失败提示信息 */
    public static final String PAY_FAIL_TIPS = "无法购买，剩余积分价值大于套餐价格";

    // ==================== 状态码常量 ====================
    /** 成功状态码字符串 */
    public static final String STR_200 = "200";
    /** 成功响应字符串 */
    public static final String SUCCESS_STR = "Success";
}
