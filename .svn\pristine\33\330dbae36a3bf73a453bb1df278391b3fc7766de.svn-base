package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.AdminTask;

/**
 * 任务Service接口
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IAdminTaskService
{
    /**
     * 查询任务
     *
     * @param taskId 任务主键
     * @return 任务
     */
    public AdminTask selectAdminTaskByTaskId(String taskId);

    /**
     * 查询任务列表
     *
     * @param adminTask 任务
     * @return 任务集合
     */
    public List<AdminTask> selectAdminTaskList(AdminTask adminTask);



    /**
     * 新增任务
     *
     * @param adminTask 任务
     * @return 结果
     */
    public int insertAdminTask(AdminTask adminTask);

    /**
     * 修改任务
     *
     * @param adminTask 任务
     * @return 结果
     */
    public int updateAdminTask(AdminTask adminTask);

    /**
     * 批量删除任务
     *
     * @param taskIds 需要删除的任务主键集合
     * @return 结果
     */
    public int deleteAdminTaskByTaskIds(String[] taskIds);

    /**
     * 删除任务信息
     *
     * @param taskId 任务主键
     * @return 结果
     */
    public int deleteAdminTaskByTaskId(String taskId);
}
