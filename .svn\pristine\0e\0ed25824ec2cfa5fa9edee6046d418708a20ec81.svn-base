package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * IP素材表
 * @TableName t_material_ip
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialIp {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "IP ID")
    private Integer id;

    /**
     * IP名称
     */
    @ApiModelProperty(value = "IP名称")
    private String name;

    /**
     * 原始图片地址
     */
    @ApiModelProperty(value = "IP图片URL")
    private String ipUrl;

    /**
     * 缩略图片地址
     */
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailImgUrl;

    /**
     * IP分类ID
     */
    @ApiModelProperty(value = "IP分类ID")
//    @JsonProperty("t_material_ip_category_id") // 与前端的命名保持一致
    private Integer materialIpCategoryId;

    /**
     * 用户输入的IP
     */
    @ApiModelProperty(value = "用户输入IP")
    private String fashIp;

    /**
     * IP提示词
     */
    @ApiModelProperty(value = "IP提示词")
    private String fashIpPrompt;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

    /**
     * 排序字段（越小越靠前）
     */
    @ApiModelProperty(value = "排序字段（越小越靠前）")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String categoryName; // 分类名称
}