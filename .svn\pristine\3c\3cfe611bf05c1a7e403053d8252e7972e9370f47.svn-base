package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialStyleUser;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface MaterialStyleUserMapper {
    int insert(MaterialStyleUser record);
    int deleteById(@Param("id") Integer id);
    int deleteByUserAndStyle(@Param("userId") Integer userId, 
                            @Param("styleId") Integer styleId,
                            @Param("taskType") Integer taskType);
    List<MaterialStyleUser> selectByUser(@Param("userId") Integer userId,
                                       @Param("offset") int offset,
                                       @Param("pageSize") int pageSize,
                                       @Param("taskType") Integer taskType);
    int countByUser(@Param("userId") Integer userId,
                    @Param("taskType") Integer taskType);
    int checkFavoriteExists(@Param("userId") Integer userId, 
                           @Param("styleId") Integer styleId,
                           @Param("taskType") Integer taskType);
    
    /**
     * 获取用户收藏的风格映射 (styleId -> favoriteId)
     */
    List<Map<String, Object>> selectUserFavoriteMap(@Param("userId") Integer userId, 
                                                   @Param("styleIds") List<Integer> styleIds,
                                                   @Param("taskType") Integer taskType);

    /**
     * 根据用户ID和风格ID查询收藏记录
     */
    MaterialStyleUser selectByUserAndStyle(@Param("userId") Integer userId, 
                                          @Param("styleId") Integer styleId,
                                          @Param("taskType") Integer taskType);
}
