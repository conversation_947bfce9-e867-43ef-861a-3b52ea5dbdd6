/**
 * @Author: wuy<PERSON>
 * @Date: 2024/1/20
 * @Description: ""
 */
import {
	AccountBookOutlined,
	StarFilled,
	ThunderboltTwoTone,
	CalendarOutlined
} from '@ant-design/icons'
import { VipCurrentBox } from '@/pages/vip/VipCurrentBox'
import { Button } from 'antd'
import { VipPackageBox } from '@/pages/vip/VipPackageBox'
import { openPayModal } from '@/business-component/pay-modal/PayModal'
import { useEffect, useMemo, useRef, useState } from 'react'
import { getVipListCode } from '@/api/vip'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { getPayListByRefueling } from '@/api/vip'
import dayjs from 'dayjs'
import '@/pages/vip/style.scss'

export const Vip = () => {
	const [userInfo] = useAtomMethod(userinfoService.userInfo)
	const [businessInfo] = useAtomMethod(userinfoService.businessInfo)
	// 付费列表展示
	const [payList, setPayList] = useState<
		Array<{
			score: number
			money: number
			refuelingBagId: string
		}>
	>([])
	const scoreList = userInfo?.userPackageAndBagVOs || [
		{ refuelingBagEndTime: '', refuelingBagScore: 0, type: 1 }
	]

	// 购买套餐ref
	const packageRef = useRef<HTMLDivElement>(null)

	// vip 套餐列表
	const [vipList, setVipList] = useState<
		{
			isDefault: number
			tariffPackageId: string
			name: string
			describe: string
			money: number
			equityDescriptionList: Array<{
				content: string
				contains: number
			}>
		}[]
	>([])

	const currentVip = useMemo(() => {
		return vipList.find((item) => item.tariffPackageId === userInfo?.packageId)
	}, [userInfo, vipList])
	const [supportCurrentList, setSupportCurrentList] = useState<string[]>([])
	const [notSupportCurrentList, setNotSupportCurrentList] = useState<string[]>(
		[]
	)

	useEffect(() => {
		const notSupportList: string[] = []
		const supportList: string[] = []
		userInfo?.equityDescriptionList?.forEach((item) => {
			if (item.contains) {
				supportList.push(item.content)
			} else {
				notSupportList.push(item.content)
			}
		})
		// currentVip?.equityDescriptionList.forEach((item) => {
		// 	if (item.contains) {
		// 		supportList.push(item.content)
		// 	} else {
		// 		notSupportList.push(item.content)
		// 	}
		// })
		setSupportCurrentList(supportList)
		setNotSupportCurrentList(notSupportList)
	}, [userInfo])

	useEffect(() => {
		getVipListCode().then((res) => {
			setVipList(res)
		})
	}, [])
	useEffect(() => {
		// 请求加油包套餐
		getPayListByRefueling().then((res) => {
			setPayList(
				res.data && res.data.length > 0
					? res.data
					: [
						{
							money: 59.9,
							score: 300,
							refuelingBagId: ''
						}
					]
			)
		})
	}, [])

	const tariffEndTimeStr = useMemo(() => {
		return userInfo?.tariffEndTime
			? `${dayjs(userInfo.tariffEndTime).format('YYYY/MM/DD')}止`
			: '-'
	}, [userInfo?.tariffEndTime])

	const toFixedNoRounding = (num1: number, num2: number) => {
		const num = num1 / num2
		// 将数字转换为字符串
		const numStr = num.toString()
		// 分割整数部分和小数部分
		var parts = numStr.split('.')
		// 如果小数部分长度大于2，则截取前两位
		if (parts.length > 1 && parts[1].length > 2) {
			return numStr.slice(0, numStr.indexOf('.') + 3)
		}
		// 如果小数部分不足两位，补充0补齐
		if (parts.length === 1 || parts[1].length === 0) {
			return numStr + '.00'
		}
		// 如果小数部分只有一位，补充一个0
		if (parts[1].length === 1) {
			return numStr + '0'
		}
		// 如果小数部分正好是两位，直接返回
		return numStr
	}

	return (
		<div className={'w-full mx-auto'}>
			<div className={'w-full rounded-lg'}>
				<div className="total-box pt-[100px] pb-[100px] text-center">
					<p className="text-[40px]">我的算力</p>
					<p className="text-[30px] text-primary m-[6px] ">
						<ThunderboltTwoTone twoToneColor="#32649f" />
						<span> {userInfo?.residueScore}</span>
					</p>
					<p className="desc mt-[20px]">
						"AI算力"（AI computing
						power）通用于进行人工智能（AI）任务和应用的计算能力。
					</p>
					<p className="desc">
						AI算力点赋予用户在WeShop唯象工具中进行生图、渲染、下载以及参与社区等操作的能力。
					</p>
					<p className="text-primary mt-[12px]">
						<ThunderboltTwoTone twoToneColor="#32649f" />
						1算力点 ≈ 高速生成1张图
					</p>
					<div className="flex justify-center items-center">
						{scoreList.length > 0 ? (
							scoreList.map((item) => {
								return (
									<div className="flex out-box  mt-[40px] ml-[10px]  mr-[10px] ">
										<div className="detail-box">
											<p className="from">
												{item.type === 1
													? '每月免费赠送积分'
													: item.type === 2
														? '加油包购买积分'
														: '定向免费赠送积分'}
											</p>
											<p className="score">
												{item.refuelingBagScore || 0}
												<span className="text-[14px] font-normal "> 积分</span>
											</p>
											<p className="text-[14px] mt-[14px]">
												约可高速生成 {item.refuelingBagScore || 0} 张图
											</p>
											<p className="text-[14px] mt-[28px]">
												于{' '}
												{dayjs(item.refuelingBagEndTime).format('YYYY-MM-DD')}
												过期
											</p>
										</div>
									</div>
								)
							})
						) : (
							<>
								<div className="flex out-box  mt-[40px] ml-[10px]  mr-[10px] ">
									<div className="detail-box">
										<p className="from">每月免费赠送积分</p>
										<p className="score">
											0<span className="text-[12px] font-normal ">积分</span>
										</p>
										<p className="text-[12px] mt-[10px]">约可高速生成 0 张图</p>
										<p className="text-[12px] mt-[24px]">于 ---- 过期</p>
									</div>
								</div>
							</>
						)}
					</div>
				</div>
			</div>
			<div className={'w-full bg-white text-center pt-[100px] pb-[100px]'}>
				<p className="text-[50px]">感谢您的支持，请选择适合您的加油包计划</p>
				<div className="advantage">
					<span>️️⚡️高速GPU算力池</span>
					<div className="line"></div>
					<span>🎉任务不限执行次数</span>
					<div className="line"></div>
					<span>🔥所有AI素材可商用</span>
					<div className="line"></div>
					<span>👑高清图片下载</span>
				</div>
				<div className="flex  justify-center items-center mt-[50px]">
					<div className="free-box mr-[20px]">
						<div className="top">
							<p>
								<CalendarOutlined className=" mr-[6px]" />
								免费套餐
							</p>
						</div>
						<div className="detail">
							<div className="flex flex-wrap detail-con">
								<div className="flex text-[18px] items-baseline w-[100%]">
									<h1
										className="text-[40px]"
										style={{ fontWeight: '700', lineHeight: '48px' }}
									>
										¥0
									</h1>
									<span> /</span>
									<span>月</span>
								</div>
								<p className="text-[12px] mb-[20px]">购买后，您将获得</p>
								<div className="text-[14px]">
									<div className="desc-item">
										<span className="dot"></span>
										<span>可获得100免费积分</span>
									</div>
									<div className="desc-item">
										<span className="dot"></span>
										<span>每月持续赠送积分</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					{payList.length > 0 && <div
						className="free-box"
						style={{ display: 'ruby', width: `${payList.length * 292}px` }}
					>
						<div className="top">
							<p>
								<AccountBookOutlined className=" mr-[6px]" />
								加油包
							</p>
						</div>
						{payList.map((item, index) => {
							return (
								<div
									className="detail  w-[291px]"
									style={{ borderRight: '1px solid #E5EBF3' }}
								>
									<div className="flex flex-wrap detail-con">
										<div className="flex text-[18px] items-baseline w-[100%]">
											<h1
												className="text-[40px]"
												style={{ fontWeight: '700', lineHeight: '48px' }}
											>
												¥{item.money}
											</h1>
										</div>
										<p className="text-[12px] mb-[20px]">购买后，您将获得</p>
										<div className="text-[14px]">
											<div className="desc-item">
												<span className="dot"></span>
												<span>
													可获得{item.score}积分算力（约可生成{item.score}
													张图片）
												</span>
											</div>
											<div className="desc-item">
												<span className="dot"></span>
												<span>积分算力点有效期1年</span>
											</div>
											<div className="desc-item">
												<span className="dot"></span>
												<span>
													生成一张图片价格约
													{toFixedNoRounding(
														item.money,
														item.score
													)}元
													{/* {(item.money / item.score).toPrecision(2)}元 */}
												</span>
											</div>
										</div>
										<div
											className="btn"
											onClick={() => {
												openPayModal({
													payType: 0,
													refuelingBagId: item.refuelingBagId
												})
											}}
										>
											购买（立即生效）
										</div>
									</div>
								</div>
							)
						})}
					</div>}
				</div>
			</div>
			{/* <div
				className={'w-full bg-white py-[20px]'}
				style={{ borderTop: '1px solid #E5EBF3' }}
			> */}
			{/* 		<div className={'pb-[60px]'}>
					<div className={'mt-[40px] text-center'}>
						<span>
							<StarFilled className={'text-primary text-[26px]'} />
						</span>
						<span className={'text-[30px] mx-[12px] relative'}>
							会员中心
							<span
								className={
									'absolute w-[100px] h-[4px] bottom-[-14px] left-[10px] bg-primary'
								}
							></span>
						</span>
						<span>
							<StarFilled className={'text-primary text-[26px]'} />
						</span>
					</div>
					<div className={'mt-[60px] flex justify-around'}>
						<VipCurrentBox title={'当前套餐'} type={1}>
							<div className={'mt-[36px]'}>
								<div className={'flex justify-between text-normal mt-[20px]'}>
									<span>我的套餐</span>
									<span>{currentVip?.name}</span>
								</div>
								<div className={'flex justify-between text-normal mt-[20px]'}>
									<span>价格</span>
									<span>￥{currentVip?.money}/月</span>
								</div>
								<div className={'flex justify-between text-normal mt-[20px]'}>
									<span>有效时间</span>
									<span>{tariffEndTimeStr}</span>
								</div>
								<div className={'mt-[60px]'}>
									<Button
										type={'primary'}
										size={'large'}
										className={'w-full'}
										onClick={() => {
											const packageDom = packageRef.current
											if (!packageDom) return

											const { top } = packageDom.getBoundingClientRect()
											const scrollY = window.scrollY
											// console.log('www--------->', top, scrollY)
											window.scrollTo({
												top: top + scrollY - 60,
												behavior: 'smooth'
											})
										}}
									>
										升级套餐
									</Button>
								</div>
							</div>
						</VipCurrentBox>
						<VipCurrentBox title={'我的积分'} type={2}>
							<div className={'mt-[0px]'}>
								<div className={'flex text-normal items-baseline'}>
									<span className={'text-primary text-[40px] font-bold'}>
										{userInfo?.residueScore}
									</span>
									<span className={'text-[18px]'}>/积分</span>
								</div>
								{userInfo?.expirationTime ? (
									<>
										<div className={'flex text-normal mt-[20px]'}>
											<span>
												有
												<span className={'text-[#F53A3A] font-bold'}>
													{userInfo?.residualIntegral || 0}
												</span>
												积分（
												{userInfo?.packageOrRefueligBag === 1
													? '套餐'
													: '加油包'}
												）
											</span>
										</div>
										<div className={'flex text-normal mt-[20px]'}>
											<span>
												于{dayjs(userInfo.expirationTime).format('YYYY/MM/DD')}
												过期
											</span>
										</div>
									</>
								) : (
									<div className="h-[84px]"></div>
								)}
								<div className={'mt-[60px]'}>
									<Button
										type={'primary'}
										size={'large'}
										className={'w-full select-none'}
										onClick={() => {
											openPayModal({
												payType: 0
											})
										}}
									>
										购买加油包
									</Button>
								</div>
							</div>
						</VipCurrentBox>
						<VipCurrentBox title={'服务信息'} type={3}>
							<div>
								{supportCurrentList.map((item) => {
									return (
										<div className={'mt-[20px] flex items-center'} key={item}>
											<span>
												<img
													src={require('@/asset/image/vip-support.png')}
													alt=""
													className={'w-[20px] h-[20px]'}
												/>
											</span>
											<span className={'text-normal ml-[12px]'}>{item}</span>
										</div>
									)
								})}

								{notSupportCurrentList.map((item) => {
									return (
										<div className={'mt-[20px] flex items-center'} key={item}>
											<span>
												<img
													src={require('@/asset/image/vip-not-support.png')}
													alt=""
													className={'w-[20px] h-[20px]'}
												/>
											</span>
											<span className={'text-normal ml-[12px]'}>{item}</span>
										</div>
									)
								})}
							</div>
						</VipCurrentBox>
					</div>
				</div> */}
			{/* <div className={'pb-[60px] bg-[#F2F8FF]'} ref={packageRef}>
					<div className={'text-primary text-[28px] pt-[30px] text-center'}>
						感谢您的支持, 请选择购买适合您的套餐
					</div>

					<div
						className={
							'flex justify-center text-primary text-[14px] mt-[20px] items-center'
						}
					>
						<span>
							<AccountBookOutlined className={'mr-[10px]'} />
							1个积分=生成1张图片
						</span>
						<span className={'ml-[60px]'}>
							<AccountBookOutlined className={'mr-[10px]'} />
							1个积分=下载1张原图分辨率图片
						</span>
					</div>
					<div className={' flex justify-around mt-[40px]'}>
						{vipList.map((vip) => {
							const notSupportList: string[] = []
							const supportList: string[] = []
							vip.equityDescriptionList.forEach((item) => {
								if (item.contains) {
									supportList.push(item.content)
								} else {
									notSupportList.push(item.content)
								}
							})
							return (
								<VipPackageBox
									packageId={vip.tariffPackageId}
									key={vip.tariffPackageId}
									title={vip.name}
									isDefaultPackage={vip.isDefault === 1}
									active={vip.tariffPackageId === userInfo?.packageId}
									desc={vip.describe}
									isRecommend={false}
									isSupport={true}
									money={vip.money}
									notSupportList={notSupportList}
									supportList={supportList}
								/>
							)
						})}
					</div>
				</div> */}
			{/* <div className={'py-[40px]  text-center '}>
					<div className="text-[40px]">联系我们</div>
					<p style={{ fontSize: '18px', color: '#97a0b4' }}>
						如果您在使用我们产品时需要帮助或合作
					</p>
					<p style={{ fontSize: '18px', color: '#97a0b4' }}>
						请扫码添加我们的微信
					</p>
					<div className={'w-[80%] ml-[10%] mt-[30px] text-normal leading-8'}>
						<div
							dangerouslySetInnerHTML={{
								__html: businessInfo?.customVipDesc || '<div />'
							}}
						></div>
					</div>
				</div> */}
			{/* </div> */}
		</div>
	)
}
export default Vip
