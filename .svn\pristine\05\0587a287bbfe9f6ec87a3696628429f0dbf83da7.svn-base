import { TextareaSlot } from '@/component/textarea-slot'
import { ReconciliationOutlined } from '@ant-design/icons'
import { useCallback, useEffect, useRef, useState } from 'react'
import { SpellBookModal } from '@/business-component/spell-book/SpellBookModal'
interface ITextareaViewProps {
	value: string
	onChange: (val: string) => void
	placeholder: string
	isRefresh?: boolean // 非textread组件调用时，需要刷新组件
	modalKey?: string | number
	advanced?: boolean
}

export const TextareaView = (props: ITextareaViewProps) => {
	const {
		value,
		onChange,
		placeholder,
		isRefresh = false,
		modalKey = '1',
		advanced,
	} = props
	const SpellBookModalRef = useRef<any>()
	const [spellBooKVals, setSpellBooKVals] = useState<string>(value)

	const openModal = useCallback(() => {
		if (!SpellBookModalRef.current) return
		SpellBookModalRef.current.openModal()
		SpellBookModalRef.current.reverseDisplayTags(spellBooKVals)
	}, [spellBooKVals])

	const spellBookClear = () => {
		if (!SpellBookModalRef.current) return
		SpellBookModalRef.current.clear()
	}

	const matchWord = (
		originStr: string,
		selectTags: string[],
		spellAllVaCh: string[]
	) => {
		//将符号统一
		originStr = originStr.replace(/，/g, ',')
		const strArr = originStr.split(',')
		const noSelectTag = spellAllVaCh.filter((v) => {
			return !selectTags.includes(v)
		})
		const reg = new RegExp(`${noSelectTag.join('|,')}`)
		originStr = originStr.replace(reg, '')
		const _b = selectTags.filter((v) => {
			return !strArr.includes(v)
		})
		originStr += _b.length ? ',' + _b.join(',') + ',' : ''
		originStr = originStr.replace(/^,/, '')
		const endStr = originStr[originStr.length - 1]
		if (![',', '，', '.', '。'].includes(endStr) && originStr) {
			originStr += ','
		}
		return originStr
	}

	useEffect(() => {
		if (!spellBooKVals) {
			spellBookClear()
		}
	}, [spellBooKVals])

	useEffect(() => {
		onChange(spellBooKVals)
	}, [onChange, spellBooKVals])

	useEffect(() => {
		if (isRefresh) {
			spellBookClear()
			setSpellBooKVals(value)
		}
	}, [isRefresh, setSpellBooKVals, value])

	const handleClick = (params: {
		selectTags: string[]
		spellAllVaCh: string[]
	}) => {
		setSpellBooKVals((val) => {
			return matchWord(val, params.selectTags, params.spellAllVaCh)
		})
	}

	return (
		<>
			<TextareaSlot
				rows={8}
				placeholder={placeholder}
				count={{
					max: 500,
					show: true
				}}
				allowClear
				value = {spellBooKVals}
				onChange = {(event) => {
					setSpellBooKVals(event.target.value)
				}}
			>
				{ props.modalKey !== 'reverse' ? 
					<span
						onClick={openModal}
						className="text-[#acacac] text-[12px] hover:text-primary cursor-pointer ml-[2px]"
					>
						<ReconciliationOutlined className="mr-[2px]" />
						咒语词典
					</span> : null 
				}
			</TextareaSlot>
			<SpellBookModal
				ref={SpellBookModalRef}
				modalKey={modalKey}
				submit={handleClick}
			/>
		</>
	)
}
