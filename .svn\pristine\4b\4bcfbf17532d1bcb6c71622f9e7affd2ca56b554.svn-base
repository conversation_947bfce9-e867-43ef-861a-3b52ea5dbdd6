/**
 * @Author: wuyang
 * @Date: 2024/1/18
 * @Description: ""
 */
import {
	forwardRef,
	useCallback,
	useEffect,
	useImperativeHandle,
	useMemo,
	useRef,
	useState
} from 'react'
import { Modal, Checkbox } from 'antd'
import {
	CloseCircleFilled,
	LeftCircleFilled,
	RightCircleFilled
} from '@ant-design/icons'
import { EBizPhone } from '@/types/bizPhone'
import { AI_VIOLATION_FLAG } from '@/constant'

interface IGenerateImagePreviewProps { }
export interface IGenerateImagePreviewPropsImperativeHandle {
	showPreview: ({
		oriImgUrl,
		markImgUrl,
		previewImages,
		oriImgUrlList,
		comparison,
		canChecked,
		type,
		index,
		isCheckedArray,
		onCheckChange
	}: {
		oriImgUrl: string
		markImgUrl: string
		comparison: boolean
		canChecked: boolean
		type: EBizPhone
		previewImages: string[]
		oriImgUrlList: string[]
		index: number
		isCheckedArray?: boolean[]
		onCheckChange?: (checked: boolean, index: number) => void
	}) => void
}

export const GenerateImagePreview = forwardRef<
	IGenerateImagePreviewPropsImperativeHandle,
	IGenerateImagePreviewProps
>((props, ref) => {
	const [isModalOpen, setIsModalOpen] = useState(false)
	const [index, setIndex] = useState(0)
	const oriRef = useRef('')
	const [comparison, setComparison] = useState(true)
	const [canChecked, setCanChecked] = useState(false)
	const [isCheckedArray, setIsCheckedArray] = useState<boolean[]>([]);
	const [onCheckChange, setOnCheckChange] = useState<(checked: boolean, index: number) => void>(() => { });
	const [images, setImages] = useState<string[]>([])
	const [oriImgUrls, setOriImgUrls] = useState<string[]>([])
	const [shadowImageInfo, setShadowImageInfo] = useState<{
		width: number
		height: number
	}>({ width: 800, height: 600 }) // 设置默认尺寸

	const showModal = () => {
		setIsModalOpen(true)
	}

	const handleCancel = () => {
		setIsModalOpen(false)
	}
	// 上一张图片
	const handlePrev = useCallback(() => {
		if (index === 0) {
			setIndex(images.length - 1)
		} else {
			setIndex(index - 1)
		}
	}, [images.length, index])

	// 下一张图片
	const handleNext = useCallback(() => {
		if (index === images.length - 1) {
			setIndex(0)
		} else {
			setIndex(index + 1)
		}
	}, [images.length, index])

	const handleKeydown = useCallback(
		(event: any) => {
			if (event.key === 'Escape') {
				handleCancel()
			} else if (event.key === 'ArrowRight') {
				handleNext()
			} else if (event.key === 'ArrowLeft') {
				handlePrev()
			}
		},
		[handleNext, handlePrev]
	)

	// 绑定键盘事件
	useEffect(() => {
		if (isModalOpen) {
			document.body.addEventListener('keydown', handleKeydown)
		} else {
			document.body.removeEventListener('keydown', handleKeydown)
		}
		return () => {
			document.body.removeEventListener('keydown', handleKeydown)
		}
	}, [handleKeydown, isModalOpen])

	const handleImageLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
		const targetImage = event.target as HTMLImageElement
		const { naturalWidth, naturalHeight } = targetImage

		const containerWidth = window.innerWidth * 0.4
		const containerHeight = window.innerHeight * 0.8

		let width = naturalWidth
		let height = naturalHeight

		if (naturalWidth > containerWidth || naturalHeight > containerHeight) {
			const widthRatio = containerWidth / naturalWidth
			const heightRatio = containerHeight / naturalHeight
			const ratio = Math.min(widthRatio, heightRatio)

			width = naturalWidth * ratio
			height = naturalHeight * ratio
		}

		setShadowImageInfo({ width, height })
		targetImage.style.width = `${width}px`
		targetImage.style.height = `${height}px`
	}, [])

	useImperativeHandle(ref, () => {
		return {
			showPreview: ({ oriImgUrl, previewImages, oriImgUrlList, index, type, markImgUrl, comparison, canChecked, isCheckedArray, onCheckChange = () => { } }) => {
				const images = previewImages?.length ? previewImages : [oriImgUrl]
				const oriImgUrls = oriImgUrlList?.length ? oriImgUrlList : []
				setIndex(index)
				oriRef.current = type === EBizPhone.goods ? markImgUrl : oriImgUrl
				setComparison(comparison)
				setCanChecked(canChecked)
				setImages(images)
				setOriImgUrls(oriImgUrls)
				setIsCheckedArray(isCheckedArray || []);
				setOnCheckChange(() => onCheckChange);
				showModal()
			}
		}
	})

	const violatingFlag = useMemo(() => {
		return images[index]?.includes(AI_VIOLATION_FLAG)
	}, [images, index])

	const getErrorPng = () => {
		const errorW = 600,
			errorH = 600
		const ratio = shadowImageInfo.width / shadowImageInfo.height

		if (shadowImageInfo.width >= 600) {
			return {
				width: errorW,
				height: errorH
			}
		} else {
			return {
				width: errorW * ratio,
				height: errorH * ratio
			}
		}
	}

	return (
		<div>
			<Modal
				title="图片预览"
				open={isModalOpen}
				getContainer={() => document.body}
				destroyOnClose={true}
				maskClosable={false}
				width={'100vw'}
				style={{
					maxWidth: '100%',
					top: 0,
					padding: 0,
					margin: 0,
					height: '100vh',
					pointerEvents: 'all'
				}}
				modalRender={() => {
					return (
						<div className={'relative'} onClick={handleCancel} >
							<div
								className={
									'absolute top-[20px] right-[30px] cursor-pointer z-10'
								}
								onClick={handleCancel}
							>
								<CloseCircleFilled
									className={' rounded-full text-[40px] text-white'}
								/>
							</div>
							{
								comparison ? <div
									className={
										'relative h-screen w-screen flex justify-center items-center'
									}
								>
									<div className={'flex relative w-full h-full'}>
										<div
											className={
												'absolute top-0 left-0 w-1/2 h-full flex justify-end items-center'
											}
										>
											<div className={'w-[80%] h-[80%] relative'}>
												{oriImgUrls.length > 1 ? <img
													src={oriImgUrls[index]}
													alt=""
													className={
														'select-none max-h-full max-w-full absolute right-0 top-1/2 translate-y-[-50%]'
													}
													onLoad={handleImageLoad}
													style={{
														background: '#cccccc',
														backgroundImage: `linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0)`,
														backgroundPosition: '0 0, 30px 30px',
														backgroundSize: '20px 20px'
													}}
												/> : <img
													src={oriRef.current || ''}
													alt=""
													className={
														'select-none max-h-full max-w-full absolute right-0 top-1/2 translate-y-[-50%]'
													}
													onLoad={handleImageLoad}
													style={{
														background: '#cccccc',
														backgroundImage: `linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0)`,
														backgroundPosition: '0 0, 30px 30px',
														backgroundSize: '20px 20px'
													}}
												/>}
											</div>
										</div>
										<div
											className={
												'absolute top-0 left-1/2 w-1/2 h-full flex justify-start items-center'
											}
										>
											<div className={'w-[80%] h-[80%] relative'}>
												{violatingFlag ? (
													<div
														className={
															'max-w-full max-h-full select-none absolute left-0 top-1/2 translate-y-[-50%] bg-[rgba(0,0,0,.1)] flex items-center justify-center'
														}
														style={{
															width: shadowImageInfo.width,
															height: shadowImageInfo.height
														}}
													>
														<img
															src={
																'https://image-task.xiaoaishop.com/Web_Violating_regulations_picture/Violating_regulations.png'
															}
															alt=""
															className={'select-none'}
															style={getErrorPng()}
														/>
													</div>
												) : (
													// <img
													// 	src={images[index]}
													// 	alt=""
													// 	className={
													// 		'max-w-full max-h-full select-none absolute left-0 top-1/2 translate-y-[-50%]'
													// 	}
													// 	style={{
													// 		width: shadowImageInfo.width,
													// 		height: shadowImageInfo.height,
													// 		background: '#cccccc',
													// 		backgroundImage: `linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0)`,
													// 		backgroundPosition: '0 0, 30px 30px',
													// 		backgroundSize: '20px 20px'
													// 	}}
													// />
													<div className="relative previewImg" style={{
														width: shadowImageInfo.width,
														height: shadowImageInfo.height,
														position: 'absolute',
														left: 0,
														top: '50%',
														transform: 'translateY(-50%)'
													}}>
														{canChecked && <Checkbox
															className="absolute top-3 right-4 z-10"
															style={{ transform: 'scale(1.5)' }}
															checked={isCheckedArray[index] ?? false}
															onChange={(e) => {
																const newChecked = e.target.checked;
																const updatedArray = [...isCheckedArray];
																updatedArray[index] = newChecked;
																setIsCheckedArray(updatedArray);
																onCheckChange?.(newChecked, index);
															}}
														/>}

														<img
															src={images[index]}
															alt=""
															className={
																'max-w-full max-h-full select-none'
															}
															style={{
																width: '100%',
																height: '100%',
																background: '#cccccc',
																backgroundImage: `linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0)`,
																backgroundPosition: '0 0, 30px 30px',
																backgroundSize: '20px 20px'
															}}
														/>

														{/* 水印图片 - 位于两张对比图底部中间位置 */}
														<div
															className="absolute bottom-0 left-0 transform -translate-x-1/2 w-[200px] pointer-events-none"
															style={{ zIndex: 5 }}
														>
															<img
																src={require('@/asset/image/watermark.png')}
																alt=""
																className="select-none w-full"
															/>
														</div>
													</div>
												)}
											</div>
										</div>
									</div>
								</div> : <div
									className={
										'relative h-screen w-screen flex justify-center items-center'
									}
								>
									<div className={'flex relative w-full h-full'}>
										<div
											className={
												'absolute top-0 left-0 w-full h-full flex justify-center items-center'
											}
										>
											<div className={'w-[80%] h-[80%] relative'}>
												<div className="relative" style={{
													width: shadowImageInfo.width,
													height: shadowImageInfo.height,
													position: 'absolute',
													left: '50%',
													top: '50%',
													transform: 'translate(-50%, -50%)'
												}}>
													{canChecked && <Checkbox
														className="absolute top-3 right-4 z-10"
														style={{ transform: 'scale(1.5)' }}
														checked={isCheckedArray[index] ?? false}
														onChange={(e) => {
															const newChecked = e.target.checked;
															const updatedArray = [...isCheckedArray];
															updatedArray[index] = newChecked;
															setIsCheckedArray(updatedArray);
															onCheckChange?.(newChecked, index);
														}}
													/>}
													<img
														src={images.length > 1 ? images[index] : oriRef.current}
														alt=""
														className={
															'select-none max-h-full max-w-full absolute top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%]'
														}
														onLoad={handleImageLoad}
														style={{
															background: '#cccccc',
															backgroundImage: `linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0)`,
															backgroundPosition: '0 0, 30px 30px',
															backgroundSize: '20px 20px'
														}}
													/>

													{/* 水印图片 - 相对于当前图片定位 */}
													<div
														className="absolute bottom-0 left-1/2 transform -translate-x-1/2  w-[200px] pointer-events-none"
														style={{ zIndex: 5 }}
													>
														<img
															src={require('@/asset/image/watermark.png')}
															alt=""
															className="select-none w-full"
														/>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							}
							{images?.length > 1 && (
								<div
									className={
										'absolute w-full flex justify-between items-center px-[60px] box-border'
									}
									style={{
										top: '50%'
									}}
								>
									<div className={'cursor-pointer'}>
										<LeftCircleFilled
											onClick={(e) => {
												e.stopPropagation(); // 阻止事件冒泡
												handlePrev();
											}}
											className={
												' rounded-full text-[40px] text-white hover:text-primary'
											}
										/>
									</div>
									<div className={'cursor-pointer'}>
										<RightCircleFilled
											onClick={(e) => {
												e.stopPropagation(); // 阻止事件冒泡
												handleNext();
											}}
											className={
												' rounded-full text-[40px] text-white hover:text-primary'
											}
										/>
									</div>
								</div>
							)}
						</div>
					)
				}}
			></Modal>
		</div>
	)
})