package com.dataxai.domain;

import com.dataxai.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品信息表 product_info
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class ProductInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品标题 */
    @Size(max = 1000, message = "产品标题长度不能超过1000个字符")
    private String productTitle;

    /** 产品价格（带符号的字符串，如"¥99.99"、"$199.99"） */
    private String productPrice;

    /** 产品图片地址 */
    private String productImageUrl;

    /** 产品原始图片地址 */
    private String originalImageUrl;
    /**
     * 缩略图地址
     */
    private String scaleImageUrl;
    /** 图片颜色 */
    private String imageColor;

    /** 来源平台 */
    private String sourcePlatform;

    /** 所属人ID (关联sys_user.user_id) */
    private Long ownerId;

    /** 所属人用户名 (关联sys_user.user_name) */
    private String ownerName;

    /** 团队ID (用于团队模式数据过滤) */
    private Long teamId;

    /** 评论数 */
    private Integer commentNum;

    /** 星级 (浮点数，例如4.5、3.8) */
    private Float starLevel;

    /** 状态（1- 待同步图片，2-已同步图片，3-同步图片失败） */
    private Integer status;

    /** 查询开始时间 */
    private String startTime;
    /** 查询结束时间 */
    private String endTime;

    /** 产品链接 */
    private String productLink;

    /** 是否上传过设计器 0：未上传，1：已上传 */
    private Boolean hasUploaded;

    /** 批次号（用于标识批量导入的批次） */
    private String batch;

    private Integer infringementMark;

    /** 任务备注 */
    private String remark;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setProductTitle(String productTitle)
    {
        this.productTitle = productTitle;
    }

    public String getProductTitle()
    {
        return productTitle;
    }
    public void setProductPrice(String productPrice)
    {
        this.productPrice = productPrice;
    }

    public String getProductPrice()
    {
        return productPrice;
    }
    public void setProductImageUrl(String productImageUrl)
    {
        this.productImageUrl = productImageUrl;
    }

    public String getProductImageUrl()
    {
        return productImageUrl;
    }
    public void setOriginalImageUrl(String originalImageUrl)
    {
        this.originalImageUrl = originalImageUrl;
    }

    public String getOriginalImageUrl()
    {
        return originalImageUrl;
    }
    public void setSourcePlatform(String sourcePlatform)
    {
        this.sourcePlatform = sourcePlatform;
    }

    public String getSourcePlatform()
    {
        return sourcePlatform;
    }
    public void setOwnerId(Long ownerId)
    {
        this.ownerId = ownerId;
    }

    public Long getOwnerId()
    {
        return ownerId;
    }
    public void setOwnerName(String ownerName)
    {
        this.ownerName = ownerName;
    }

    public String getOwnerName()
    {
        return ownerName;
    }
    public void setCommentNum(Integer commentNum)
    {
        this.commentNum = commentNum;
    }

    public Integer getCommentNum()
    {
        return commentNum;
    }

    public void setTeamId(Long teamId)
    {
        this.teamId = teamId;
    }

    public Long getTeamId()
    {
        return teamId;
    }

    public void setStarLevel(Float starLevel)
    {
        this.starLevel = starLevel;
    }

    public Float getStarLevel()
    {
        return starLevel;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    public String getStartTime() {
        return startTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    public String getEndTime() {
        return endTime;
    }

    public void setProductLink(String productLink) {
        this.productLink = productLink;
    }
    public String getProductLink() {
        return productLink;
    }

    public void setHasUploaded(Boolean hasUploaded) {
        this.hasUploaded = hasUploaded;
    }
    public Boolean getHasUploaded() {
        return hasUploaded;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }
    public String getBatch() {
        return batch;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productTitle", getProductTitle())
            .append("productPrice", getProductPrice())
            .append("productImageUrl", getProductImageUrl())
            .append("originalImageUrl", getOriginalImageUrl())
            .append("sourcePlatform", getSourcePlatform())
            .append("ownerId", getOwnerId())
            .append("ownerName", getOwnerName())
            .append("commentNum", getCommentNum())
            .append("starLevel", getStarLevel())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("status", getStatus())
            .append("productLink", getProductLink())
            .append("batch", getBatch())
            .toString();
    }
}
