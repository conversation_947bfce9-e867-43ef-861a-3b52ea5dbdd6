package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialIpCategory;
import com.dataxai.web.mapper.MaterialIpCategoryMapper;
import com.dataxai.web.mapper.MaterialIpMapper;
import com.dataxai.web.service.MaterialIpCategoryService;
import com.dataxai.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class MaterialIpCategoryServiceImpl implements MaterialIpCategoryService {

    @Autowired
    private MaterialIpCategoryMapper categoryMapper;
    
    @Autowired
    private MaterialIpMapper materialIpMapper;

    @Override
    public List<MaterialIpCategory> queryAll(String name) {
        return categoryMapper.selectByCondition(name);
    }

    @Override
    public MaterialIpCategory getById(Integer id) {
        return categoryMapper.selectById(id);
    }

    @Override
    public boolean addMaterialIpCategory(MaterialIpCategory category) {
        return categoryMapper.insert(category) > 0;
    }

    @Override
    public boolean updateMaterialIpCategory(MaterialIpCategory category) {
        return categoryMapper.update(category) > 0;
    }

    @Override
    public boolean deleteMaterialIpCategory(Integer id) {
        // 检查分类是否被使用
        if (isCategoryInUse(id)) {
            throw new ServiceException("该分类正在被使用，无法删除");
        }
        return categoryMapper.deleteById(id) > 0;
    }

    @Override
    public List<MaterialIpCategory> queryPage(Integer pageNum, Integer pageSize, String name) {
        int offset = (pageNum - 1) * pageSize;
        return categoryMapper.selectPageByCondition(offset, pageSize, name);
    }

    @Override
    public int countByCondition(String name) {
        return categoryMapper.countByCondition(name);
    }
    
    @Override
    public boolean isCategoryInUse(Integer categoryId) {
        // 检查是否有IP素材使用此分类
        int count = materialIpMapper.countByCondition(null, null, categoryId);
        return count > 0;
    }
}