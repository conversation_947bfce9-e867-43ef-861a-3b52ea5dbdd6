package com.dataxai.web.controller.ImageController;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.web.domain.CropExtractCategory;
import com.dataxai.web.service.CropExtractCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 印花图品类管理
 * 
 * <AUTHOR>
 */
@Api(tags = "印花图品类管理")
@RestController
@RequestMapping({"/cropExtractCategory", "/textToImgStyle/cropExtractCategory"})
public class CropExtractCategoryController extends BaseController {

    @Autowired
    private CropExtractCategoryService cropExtractCategoryService;

    /**
     * 查询印花图品类列表
     */
    @ApiOperation("查询印花图品类列表")
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:list')")
    public R<List<CropExtractCategory>> list(
            @ApiParam("品类名称") @RequestParam(required = false) String name,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        List<CropExtractCategory> list = cropExtractCategoryService.queryAll(name, status);
        return R.ok(list);
    }

    /**
     * 分页查询印花图品类列表
     */
    @ApiOperation("分页查询印花图品类列表")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:list')")
    public R<PageResult<CropExtractCategory>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("品类名称") @RequestParam(required = false) String name,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        List<CropExtractCategory> list = cropExtractCategoryService.queryPage(pageNum, pageSize, name, status);
        int total = cropExtractCategoryService.countByCondition(name, status);

        PageResult<CropExtractCategory> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);

        return R.ok(pageResult);
    }

    /**
     * 根据ID获取印花图品类详情
     */
    @ApiOperation("根据ID获取印花图品类详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:query')")
    public R<CropExtractCategory> getById(@ApiParam("品类ID") @PathVariable Integer id) {
        CropExtractCategory category = cropExtractCategoryService.getById(id);
        return category != null ? R.ok(category) : R.fail("品类不存在");
    }

    /**
     * 新增印花图品类
     */
    @ApiOperation("新增印花图品类")
    @Log(title = "印花图品类管理", businessType = BusinessType.INSERT)
    @PostMapping
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:add')")
    public R<Boolean> add(@RequestBody CropExtractCategory category) {
        if (StringUtils.isEmpty(category.getName())) {
            return R.fail("品类名称不能为空");
        }
        
        boolean success = cropExtractCategoryService.add(category);
        return success ? R.ok(true) : R.fail("新增失败");
    }

    /**
     * 修改印花图品类
     */
    @ApiOperation("修改印花图品类")
    @Log(title = "印花图品类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:edit')")
    public R<Boolean> update(@RequestBody CropExtractCategory category) {
        if (category.getId() == null) {
            return R.fail("品类ID不能为空");
        }
        if (StringUtils.isEmpty(category.getName())) {
            return R.fail("品类名称不能为空");
        }
        
        boolean success = cropExtractCategoryService.update(category);
        return success ? R.ok(true) : R.fail("修改失败");
    }

    /**
     * 删除印花图品类
     */
    @ApiOperation("删除印花图品类")
    @Log(title = "印花图品类管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @PreAuthorize("@ss.hasPermi('cropExtractCategory:remove')")
    public R<Boolean> remove(@ApiParam("品类ID") @PathVariable Integer id) {
        // 使用物理删除
        boolean success = cropExtractCategoryService.deleteById(id);
        return success ? R.ok(true) : R.fail("删除失败");
    }
} 