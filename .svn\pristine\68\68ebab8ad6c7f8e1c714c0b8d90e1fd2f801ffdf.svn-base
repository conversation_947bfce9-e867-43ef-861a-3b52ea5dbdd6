
import { useRef, useEffect, useState, useContext } from 'react';
import { Table, Tag, Progress, Image, Upload, Modal, Button, Radio, Input, message, DatePicker, Pagination } from 'antd';
import { CloudUploadOutlined, CopyOutlined, DeleteOutlined, ExclamationCircleFilled, LoadingOutlined, SearchOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getBatchList, addBatch, deleteBatch } from '@/api/task'
import { batchZipUrl } from '@/api/common'
import { batchDownloadImages } from '@/utils/downFile'
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import FilterBar, { FilterParams } from '@/component/filter';

export const UploadImage = () => {
    const navigate = useNavigate();

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()


    const [batchNumber, setBatchNumber] = useState('') // 批次号
    const [remark, setRemark] = useState('') // 备注信息
    const [dateRange, setDateRange] = useState<any>(null) // 日期范围
    const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [batchRemark, setBatchRemark] = useState('');
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    // 表格数据
    const [dataSource, setDataSource] = useState([]);
    // 表格loading
    const [tableLoading, setTableLoading] = useState(false);

    //获取图片上传批次列表
    const fetchBatchListData = async (type: number, pageNum: number, pageSize: number, remark: string, batchNumber: string, userId: string, startTime: string, endTime: string,) => {
        setDataSource([])
        setTableLoading(true)
        try {
            const response: any = await getBatchList({ type, pageNum, pageSize, batchNumber, remark, userId, startTime, endTime, });
            if (response.data) {
                console.log(response, '获取图片上传批次列表')
                setDataSource(response.data)
                setPagination(prev => ({
                    ...prev,
                    total: response.total,
                }));
                setIsFirstLoad(false);
            }

        } catch (error) {
            console.error('获取数据时出错：', error);
        } finally {
            setTableLoading(false)
        }
    };
    useEffect(() => {
        handleSearch(1, 10)
    }, []);
    //分页信息
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    // 查询条件存储key
    const QUERY_PARAMS_KEY = 'upload_image_query_params';

    //是否首次加载
    const [isFirstLoad, setIsFirstLoad] = useState(true)
    // Refs 声明（必须放在组件顶层）
    const timerRef = useRef<NodeJS.Timeout>()
    const fetchRef = useRef<typeof refreshBatchListData>()
    const paginationRef = useRef(pagination)
    const isFirstLoadRef = useRef(isFirstLoad)
    // 同步最新状态到 ref
    useEffect(() => {
        paginationRef.current = pagination
        isFirstLoadRef.current = isFirstLoad
    })
    useEffect(() => {
        // 更新函数引用
        fetchRef.current = refreshBatchListData

        const tick = () => {
            if (!isFirstLoadRef.current) {
                console.log('定时刷新')
                fetchRef.current?.(15, paginationRef.current.current, 10)
            }
        }

        // 清除旧定时器
        if (timerRef.current) clearInterval(timerRef.current)
        // 启动新定时器
        timerRef.current = setInterval(tick, 5000)
        // 清理函数
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current)
            }
        }
    }, [isFirstLoad]) // 依赖项

    //刷新图片上传批次列表
    const refreshBatchListData = async (
        type: number,
        pageNum: number,
        pageSize: number
    ) => {
        try {
            const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
            let queryParams = {
                remark: '',
                batchNumber: '',
                userId: '',
                stringDateRange: [] as string[]
            };

            if (savedParams) {
                queryParams = JSON.parse(savedParams);
            }

            let startTime = '';
            let endTime = '';
            if (queryParams.stringDateRange.length > 0) {
                [startTime, endTime] = queryParams.stringDateRange;
                startTime = startTime ? `${startTime} 00:00:00` : '';
                endTime = endTime ? `${endTime} 23:59:59` : '';
            }
            const response: any = await getBatchList({
                type,
                pageNum,
                pageSize,
                batchNumber: queryParams.batchNumber,
                remark: queryParams.remark,
                userId: queryParams.userId,
                startTime,
                endTime
            })
            if (response.data) {
                console.log(response, '刷新图片任务批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
            }
        } catch (error) {
            console.error('刷新数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }

    // 查询处理函数
    const handleSearch = (pageNum: number, pageSize: number) => {
        // 存储查询条件到sessionStorage
        const queryParams = {
            remark,
            batchNumber,
            userId: '',
            stringDateRange
        };
        sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
        if (stringDateRange.length > 0) {
            let [startTime, endTime] = stringDateRange;
            startTime = startTime ? `${startTime} 00:00:00` : '';
            endTime = endTime ? `${endTime} 23:59:59` : '';
            fetchBatchListData(15, pageNum, pageSize, remark, batchNumber, queryParams.userId, startTime, endTime)
        } else {
            fetchBatchListData(15, pageNum, pageSize, remark, batchNumber, queryParams.userId, '', '')
        }
    }

    // 表格列配置
    const columns = [
        {
            title: '上传批次',
            dataIndex: 'batchNumber',
            key: 'batchNumber',
            ellipsis: true,
            align: 'center' as const,
        },
        {
            title: '图片数量',
            dataIndex: 'totalAmount',
            key: 'totalAmount',
            width: 140,
            align: 'center' as const,
            render: (totalAmount: number) => (
                <p>总数：{totalAmount}</p>
            ),
        },
        {
            title: '任务进度',
            dataIndex: 'successAmount',
            key: 'successAmount',
            align: 'center' as const,
            render: (successAmount: number, record: any) => (
                <Progress percent={Math.round((successAmount + record.failAmount) / record.totalAmount * 100)} />
            )
        },
        {
            title: '备注信息',
            dataIndex: 'remark',
            key: 'remark',
            ellipsis: true,
            align: 'center' as const,
        },

        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            align: 'center' as const,
            render: (createTime: string) => (
                <p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            ),
        },
        {
            title: '操作',
            key: 'action',
            align: 'center' as const,
            render: (_: any, record: any) => (
                <div className='flex justify-center  gap-2'>
                    {record.status == 1 && <Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
                        onClick={() => goDetail(record)} size="small">查看详情</Button>}
                    {record.status == 1 && <Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
                        onClick={() => handleDownloadImgages(record.tasks, record.batchId)}
                        size="small"
                        loading={downloadLoading === record.batchId}>下载</Button>}
                    {record.status == 4 && <Button type="link" size="small" style={{ color: '#cf1322' }} onClick={() => handleDelBatch(record.batchId)} >删除</Button>}
                </div>
            ),
        },
    ];
    // 删除批次
    const handleDelBatch = (id: string) => {
        modal.confirm({
            centered: true,
            title: (
                <div className={'text-[18px] text-normal'}> 确认删除该批次？</div>
            ),
            content: null,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            onOk() {
                deleteBatch(id).then(res => {
                    message.success('批次删除成功')
                    setPagination(prev => ({ ...prev, current: 1 }));
                    handleSearch(1, 10)
                }).catch(err => {
                    message.error(`批次删除失败：${err?.data?.msg}`)
                    setPagination(prev => ({ ...prev, current: 1 }));
                    handleSearch(1, 10)
                })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    };

    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = (tasks: any[], batchId: any) => {
        const imgUrls = tasks.map(task => task.originalUrl);
        setDownloadLoading(batchId);
        batchZipUrl({ imageUrls: imgUrls, type: 15 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false);
        })
        // batchDownloadImages(imgUrls);
    };

    // 查看详情
    const goDetail = (record: any) => {
        navigate('/workspace/batchTools/uploadImage/detail', {
            state: { batchId: record.batchId }
        });
    };
    const showModal = () => {
        setIsModalVisible(true);
        setFileList([]); // 清空图片列表
        setBatchRemark(''); // 清空备注信息
    };

    const [creatLoading, setCreatLoading] = useState(false)

    // 创建任务
    const handleOk = async () => {
        try {
            if (fileList.length === 0) {
                messageApi.error('请先上传图片');
                return;
            }
            setCreatLoading(true)
            const files = fileList.map(file => file.originFileObj as File);
            const response = await addBatch({ files, remark: batchRemark, type: 15 });
            if (response) {
                messageApi.success('创建成功');
                setIsModalVisible(false);
                setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
                handleSearch(1, 10); // 刷新批次列表数据
                userinfoService.refresh(); // 刷新用户积分
            }
        } catch (err: any) {
            messageApi.error(`创建失败: ${err?.data?.msg || err?.msg}`)
        } finally {
            setCreatLoading(false)
        }
    };

    // 创建任务 - 分块上传版本
    // const handleOk = async () => {
    //     try {
    //         if (fileList.length === 0) {
    //             messageApi.error('请先上传图片');
    //             return;
    //         }
    //         setCreatLoading(true);

    //         // 分块大小设置为20MB
    //         const CHUNK_SIZE = 20 * 1024 * 1024;
    //         const files = fileList.map(file => file.originFileObj as File);

    //         // 分块上传函数
    //         const uploadChunks = async (file: File) => {
    //             const fileSize = file.size;
    //             const chunks = Math.ceil(fileSize / CHUNK_SIZE);

    //             // 添加日志检查分块计算
    //             console.log(`文件大小: ${fileSize} bytes (${(fileSize / (1024 * 1024)).toFixed(2)}MB), 分块数: ${chunks}`);

    //             const uploadPromises = [];

    //             for (let i = 0; i < chunks; i++) {
    //                 const start = i * CHUNK_SIZE;
    //                 const end = Math.min(fileSize, start + CHUNK_SIZE);
    //                 const chunk = file.slice(start, end);

    //                 uploadPromises.push(
    //                     addBatch({
    //                         file: chunk,
    //                         fileName: file.name,
    //                         chunkIndex: i,
    //                         totalChunks: chunks,
    //                         remark: batchRemark,
    //                         type: 15
    //                     })
    //                 );
    //             }

    //             // 等待当前文件的所有分块上传完成
    //             await Promise.all(uploadPromises);
    //         };

    //         // 并行上传所有文件的分块
    //         await Promise.all(files.map(file => uploadChunks(file)));

    //         messageApi.success('创建成功');
    //         setIsModalVisible(false);
    //         setPagination(prev => ({ ...prev, current: 1 }));
    //         handleSearch(1, 10);
    //         userinfoService.refresh();

    //     } catch (err: any) {
    //         messageApi.error(`创建失败: ${err?.data?.msg || err?.msg}`);
    //     } finally {
    //         setCreatLoading(false);
    //     }
    // };

    const handleCancel = () => {
        setIsModalVisible(false);
    };

    let prevFileList: any[] = [];
    // 图片上传
    const handleUploadChange = ({ fileList: newFileList }: { fileList: any[] }) => {
        // 比较新旧文件列表，如果相同则不处理
        if (newFileList.length === prevFileList.length &&
            newFileList.every((file, index) => file.uid === prevFileList[index].uid)) {
            return;
        }

        prevFileList = newFileList;
        const validFiles: UploadFile[] = [];

        const promises = newFileList.map((file) => {
            const isImage = /^image\//.test(file.type || '');

            if (!isImage) {
                messageApi.error('请检查文件类型');
                return Promise.resolve(false);
            }

            return new Promise<boolean>((resolve) => {
                const reader = new FileReader();
                const originFileObj = file.originFileObj;
                if (!originFileObj) {
                    messageApi.error('无法获取文件对象,请检查文件');
                    resolve(false);
                    return;
                }
                reader.readAsArrayBuffer(originFileObj);
                reader.onload = async () => {
                    try {
                        const blob = new Blob([reader.result as ArrayBuffer]);
                        const img = await createImageBitmap(blob);
                        const { width, height } = img;
                        const isValidSize = width <= 4096 && height <= 4096;
                        if (!isValidSize) {
                            messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过');
                            resolve(false);
                        } else {
                            resolve(true);
                        }
                    } catch (error) {
                        messageApi.error('图片加载失败，请检查图片格式');
                        resolve(false);
                    }
                };
                reader.onerror = () => {
                    messageApi.error('读取文件失败，请检查文件格式');
                    resolve(false);
                };
            });
        });

        Promise.all(promises).then((results) => {
            newFileList.forEach((file, index) => {
                if (results[index]) {
                    validFiles.push(file);
                }
            });

            const totalFiles = fileList.length + validFiles.length;
            if (totalFiles > 50) {
                messageApi.error('最多只能上传50张图片');
                return;
            }

            if (validFiles.length > 0) {
                messageApi.success(`成功上传 ${validFiles.length} 张图片`);
                setFileList(prev => [...prev, ...validFiles]);
            }
        });
    };


    // 在组件中添加分页变化处理函数
    const handleTableChange = (pagination: any) => {
        setPagination({
            ...pagination,
            current: pagination.current,
            pageSize: pagination.pageSize
        });
        handleSearch(pagination.current, pagination.pageSize);
    };

    const onSearch = (params: FilterParams) => {
        const pageNum = 1;
        const pageSize = 10;
        fetchBatchListData(
            15,
            pageNum,
            pageSize,
            params.remark ?? '',
            params.batchNumber ?? '',
            params.userId ?? '',
            params.startTime,
            params.endTime
        );
    };
    return (
        <div className='h-full w-full p-[20px]'>
            {contextHolder}  {/* 这里确保 Modal 挂载 */}
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            <div className='w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal'>
                <Button type="primary" onClick={showModal} >新建批量图片上传任务</Button>
                {/* 新建批量图片上传任务弹窗 start */}
                <Modal
                    title="新建批量图片上传任务"
                    open={isModalVisible}
                    onOk={handleOk}
                    onCancel={handleCancel}
                    width={660}
                    centered
                    okText="创建"
                    confirmLoading={creatLoading}
                >
                    <Upload.Dragger
                        multiple
                        accept="image/*"
                        fileList={[]} // 设置为空数组隐藏自带列表
                        onChange={handleUploadChange}
                        beforeUpload={() => false} // 阻止自动上传
                        className="upload-area"
                        listType="picture"
                        showUploadList={false} // 完全隐藏上传列表
                        disabled={fileList.length >= 50} // 添加禁用状态
                        style={{ marginTop: '30px' }}
                        directory={true} // 允许选择或拖拽文件夹
                    >
                        <p className="ant-upload-drag-icon">
                            <CloudUploadOutlined />
                        </p>
                        <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                        <p className="ant-upload-hint">
                            最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
                        </p>
                    </Upload.Dragger>
                    {/* <div style={{ maxHeight: '30vh', overflowY: 'auto' }}>
                        <Upload
                            fileList={fileList}
                            listType="picture"
                            showUploadList={{
                                showPreviewIcon: false,
                                showRemoveIcon: true
                            }}
                            onRemove={(file) => {
                                setFileList(fileList.filter(f => f.uid !== file.uid));
                                return false;
                            }}
                        />
                    </div> */}
                    <div className="mt-2 text-right">
                        已选择图片张数: <span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
                            {fileList.length}
                        </span> / 50
                    </div>
                    <div className="flex items-center mt-[20px] mb-[30px]">
                        <p className='min-w-[180px]'>图片批次备注信息（选填）：</p>
                        <Input
                            value={batchRemark}
                            onChange={(e) => setBatchRemark(e.target.value)}
                            placeholder="请输入图片批次备注信息"
                            showCount maxLength={20}
                            style={{ flex: 1 }}
                        />
                    </div>
                </Modal>
                {/* 新建批量图片上传任务弹窗 end */}
                <div className='flex items-center gap-2 ]'>
                    <FilterBar
                        fields={['batchNumber','dateRange','remark','userId']}
                        storageKey={QUERY_PARAMS_KEY}
                        withTime={false}
                        onSearch={onSearch}
                    />
                </div>
            </div>
            <div className='w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    className='mt-4'
                    scroll={{ x: 966 }}
                    loading={tableLoading}
                    pagination={{
                        ...pagination,
                        showTotal: (total: number) => `共 ${total} 条`,
                        showSizeChanger: false
                    }}
                    onChange={handleTableChange}
                />
            </div>

        </div >
    );
};

export default UploadImage;





