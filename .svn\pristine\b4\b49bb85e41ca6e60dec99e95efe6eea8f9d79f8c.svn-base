<mapper namespace="com.dataxai.web.mapper.MaterialIpUserMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_material_ip_user (material_ip_id, user_id)
        VALUES (#{materialIpId}, #{userId})
    </insert>

    <delete id="deleteById">
        DELETE FROM t_material_ip_user WHERE id = #{id}
    </delete>

    <delete id="deleteByUserAndIp">
        DELETE FROM t_material_ip_user
        WHERE user_id = #{userId} AND material_ip_id = #{ipId}
    </delete>

    <!-- 新增：根据IP ID删除所有收藏记录 -->
    <delete id="deleteByIpId">
        DELETE FROM t_material_ip_user WHERE material_ip_id = #{ipId}
    </delete>

    <select id="selectByUser" resultType="MaterialIpUser">
        SELECT * FROM t_material_ip_user
        WHERE user_id = #{userId}
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countByUser" resultType="int">
        SELECT COUNT(*) FROM t_material_ip_user
        WHERE user_id = #{userId}
    </select>
</mapper>