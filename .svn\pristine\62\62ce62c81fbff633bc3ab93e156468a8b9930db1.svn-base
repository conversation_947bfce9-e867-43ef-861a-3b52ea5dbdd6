package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.domain.ProductInfo;
import com.dataxai.domain.TUser;
import com.dataxai.domain.TeamUser;
import com.dataxai.service.IProductInfoService;
import com.dataxai.service.ITeamUserService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 平台API-产品信息接口
 */
@Api(tags = "平台API产品信息接口")
@RestController
@RequestMapping("/platform/product/info")
public class PlatformProductInfoController extends BaseController {

    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;

    /**
     * 查询产品信息列表（分页）
     */
    @ApiOperation("查询产品信息列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(
            HttpServletRequest request,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime,
            @ApiParam("来源平台") @RequestParam(required = false) String sourcePlatform,
            @ApiParam("侵权标注") @RequestParam(required = false) Integer infringementMark
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }

            // 构造查询对象
            ProductInfo query = new ProductInfo();

            // 根据用户模式设置数据过滤条件
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(user.getUserId());
                if (teamUser != null && teamUser.getIsAdmin() == true) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                } else {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(user.getUserId());
                }
            } else {
                // 个人模式：通过owner_id过滤，且team_id为0
                query.setOwnerId(user.getUserId());
                query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
            }

            query.setStartTime(startTime);
            query.setEndTime(endTime);
            query.setSourcePlatform(sourcePlatform);
            query.setInfringementMark(infringementMark);

            startPage();
            List<ProductInfo> list = productInfoService.selectProductInfoList(query);

            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", new PageInfo<>(list).getTotal());
            pageData.put("data", list);
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除产品信息
     */
    @ApiOperation("删除产品信息")
    @Log(title = "平台API-删除产品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<String> remove(HttpServletRequest request, @ApiParam("产品ID") @PathVariable Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            ProductInfo productInfo = productInfoService.selectProductInfoById(id);
            if (productInfo == null) {
                return R.fail("产品信息不存在");
            }
            if (!Objects.equals(productInfo.getOwnerId(), user.getUserId())) {
                return R.fail("无权限删除此产品信息");
            }
            int rows = productInfoService.deleteProductInfoById(id);
            return rows > 0 ? R.ok("删除成功") : R.fail("删除失败");
        } catch (Exception e) {
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除产品信息（与前台接口保持一致的请求方式与参数）
     */
    @ApiOperation("批量删除产品信息")
    @Log(title = "平台API-批量删除产品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public R<String> removeBatch(HttpServletRequest request, @PathVariable Long[] ids) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (ids == null || ids.length == 0) {
                return R.fail("参数不能为空");
            }
            // 权限校验
            for (Long id : ids) {
                ProductInfo productInfo = productInfoService.selectProductInfoById(id);
                if (productInfo == null) {
                    return R.fail("产品信息不存在，ID: " + id);
                }
                if (!Objects.equals(productInfo.getOwnerId(), user.getUserId())) {
                    return R.fail("无权限删除产品信息，ID: " + id);
                }
            }
            int rows = productInfoService.deleteProductInfoByIds(ids);
            return rows > 0 ? R.ok("批量删除成功") : R.fail("批量删除失败");
        } catch (Exception e) {
            return R.fail("批量删除失败: " + e.getMessage());
        }
    }
}