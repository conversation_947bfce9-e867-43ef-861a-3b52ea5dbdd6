/**
 * @Author: xujing
 * @Date: 2025/3/3
 * @Description: ""
 */
import { useMemo, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { DataXHeader } from '@/business-component/data-x-header/DataXHeader';
import { Cut } from '@/pages/materialEdit/cut'
import { Extract } from '@/pages/materialEdit/extract'
import { BlockingOut } from '@/pages/materialEdit/blockingOut'
import { Clear } from '@/pages/materialEdit/clear'
import { FourSided } from '@/pages/materialEdit/fourSided'

export const MaterialEdit = () => {
    const navigate = useNavigate();

    useEffect(() => {
        // You can handle side effects here if needed.
    }, []);

    const navs = useMemo(() => {
        return [
            {
                title: '图案裁剪',
                id: 10,
                path: 'cut'
            },
            {
                title: '图片去背景',
                id: 11,
                path: 'blockingOut'
            },
            {
                title: '图片变清晰',
                id: 12,
                path: 'clear'
            },
            {
                title: '印花图提取',
                id: 13,
                path: 'extract'
            },
            {
                title: '四方循环图',
                id: 14,
                path: 'fourSided'
            },
        ];
    }, []);


    const location = useLocation()

    // 初始化 activeNav 状态
    const [activeNav, setActiveNav] = useState(() => {
        const currentPath = location.pathname;
        if (
            currentPath === '/workspace/materialEdit' ||
            currentPath === '/workspace/materialEdit/cut'
        ) {
            return navs.find(nav => nav.path === 'cut');
        }
        if (currentPath.includes('extract')) {
            return navs.find(nav => nav.path === 'extract');  // 匹配type=52
        }
        return navs.find(nav => currentPath.includes(nav.path));
    });

    const handleNavClick = (nav: any) => {
        setActiveNav(nav); // Set active navigation
        navigate(`/workspace/materialEdit/${nav.path}`); // Update the route
    };

    return (
        <div className="bg-normal flex flex-col min-h-[100vh] h-screen overflow-hidden">
            <DataXHeader />
            <div className={'p-[14px] h-full box-border'}>
                <div className={'bg-white rounded-xl flex overflow-hidden h-full'}>
                    <div className={'w-[76px] border-r-[1px] border-normal min-w-[76px]'}>
                        <div className={'text-center'}>
                            {navs.map((nav) => (
                                <div
                                    className={'mt-[14px] flex flex-col items-center cursor-pointer'}
                                    key={nav.id}
                                    onClick={() => handleNavClick(nav)} // Handle click event
                                >
                                    <img
                                        className={'w-[48px] h-[48px] rounded-full overflow-hidden'}
                                        src={
                                            activeNav?.id === nav.id
                                                ? require(`@/asset/icon/${nav.path}-active.webp`)
                                                : require(`@/asset/icon/${nav.path}.webp`)
                                        }
                                        alt={nav.title}
                                    />
                                    <div className={'text-normal mt-[6px] text-[12px]'}>
                                        {nav.title}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="flex-1">
                        {/* The content corresponding to the active navigation */}
                        {activeNav?.id === 10 && <Cut />}
                        {activeNav?.id === 11 && <BlockingOut />}
                        {activeNav?.id === 12 && <Clear />}
                        {activeNav?.id === 13 && <Extract />}
                        {activeNav?.id === 14 && <FourSided />}

                    </div>
                </div>
            </div>
        </div>
    );
};

export default MaterialEdit;




