package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialIpUser;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface MaterialIpUserMapper {
    int insert(MaterialIpUser record);
    int deleteById(@Param("id") Integer id);
    int deleteByUserAndIp(@Param("userId") Integer userId, @Param("ipId") Integer ipId);
    List<MaterialIpUser> selectByUser(@Param("userId") Integer userId,
                                    @Param("offset") int offset,
                                    @Param("pageSize") int pageSize);
    int countByUser(@Param("userId") Integer userId);
    int checkFavoriteExists(@Param("userId") Integer userId, @Param("ipId") Integer ipId);
    
    /**
     * 获取用户收藏的IP映射 (ipId -> favoriteId)
     */
    List<Map<String, Object>> selectUserFavoriteMap(@Param("userId") Integer userId, @Param("ipIds") List<Integer> ipIds);

    /**
     * 根据用户ID和IP ID查询收藏记录
     */
    MaterialIpUser selectByUserAndIp(@Param("userId") Integer userId, @Param("ipId") Integer ipId);

    /**
     * 根据IP ID删除所有收藏
     */
    int deleteByIpId(@Param("ipId") Integer ipId);
}
