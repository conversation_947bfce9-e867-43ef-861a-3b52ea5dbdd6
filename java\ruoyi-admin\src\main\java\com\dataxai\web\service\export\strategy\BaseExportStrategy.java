package com.dataxai.web.service.export.strategy;
/**
 * 导出策略的抽象基类，采用了模板方法模式。
 * <p>
 * 此类定义了一个标准的导出工作流程，包括下载模板、创建工作簿、
 * 清理数据、填充数据以及将结果写入 HTTP 响应。
 * 具体的数据填充逻辑由抽象方法 {@link #doFill(TExcelCustomTemplateDTO, Workbook, Sheet)}
 * 延迟到子类中实现。
 *
 * @see ExportStrategy
 * @see com.dataxai.web.service.export.support.ExcelFillService
 */

import com.dataxai.domain.TExcelCustomTemplateDTO;
import com.dataxai.web.service.export.support.ExcelFillService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RequiredArgsConstructor
public abstract class BaseExportStrategy implements ExportStrategy {
    /**
     * {@inheritDoc}
     * <p>
     * 此实现定义了导出操作的核心骨架。它依次执行以下步骤：
     * <ol>
     *     <li>解析 DTO 获取并下载模板文件。</li>
     *     <li>创建 Workbook 并找到目标 Sheet。</li>
     *     <li>清空 Sheet 中的旧数据。</li>
     *     <li>调用抽象方法 {@code doFill} 由子类完成具体的数据填充。</li>
     *     <li>将填充后的 Workbook 写入临时文件。</li>
     *     <li>设置 HTTP 响应头，将临时文件以文件流形式发送给客户端。</li>
     * </ol>
     * 所有临时文件会在 finally 块中被安全删除。
     */
    protected final ExcelFillService excelFillService;

    /**
     * {@inheritDoc}
     * <p>
     * 此实现定义了导出操作的核心骨架。它依次执行以下步骤：
     * <ol>
     *     <li>解析 DTO 获取并下载模板文件。</li>
     *     <li>创建 Workbook 并找到目标 Sheet。</li>
     *     <li>清空 Sheet 中的旧数据。</li>
     *     <li>调用抽象方法 {@code doFill} 由子类完成具体的数据填充。</li>
     *     <li>将填充后的 Workbook 写入临时文件。</li>
     *     <li>设置 HTTP 响应头，将临时文件以文件流形式发送给客户端。</li>
     * </ol>
     * 所有临时文件会在 finally 块中被安全删除。
     */
    @Override
    public void export(TExcelCustomTemplateDTO dto, HttpServletResponse response) throws Exception {
        File tempFile = null;
        File outputFile = null;
        try {
            // 步骤 1: 获取URL并下载模板
            String excelUrl = excelFillService.resolveExcelUrlFromDto(dto);
            if (excelUrl == null || excelUrl.isEmpty()) throw new IllegalArgumentException("未提供 Excel 模板下载地址");
            tempFile = excelFillService.downloadFileFromUrl(excelUrl);

            // 步骤 2: 创建工作簿并定位Sheet
            Workbook workbook = excelFillService.createWorkbook(tempFile);
            Sheet sheet = workbook.getSheet(resolveSheetName(dto));
            if (sheet == null) throw new IllegalStateException("未找到工作表: " + resolveSheetName(dto));

            // 步骤 3: 清空旧数据
            excelFillService.clearSheetData(sheet, getStartRow(dto));

            // 步骤 4: 子类实现填充
            doFill(dto, workbook, sheet);

            // 步骤 5: 写入临时文件
            outputFile = File.createTempFile("export-", ".xlsx");
            try (OutputStream fos = new FileOutputStream(outputFile)) { workbook.write(fos); }

            // 步骤 6: 设置响应头并发送文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode(buildFileName(dto), StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + fileName);
            try (InputStream is = new FileInputStream(outputFile); OutputStream os = response.getOutputStream()) {
                byte[] buf = new byte[8192]; int n; while((n=is.read(buf))!=-1){ os.write(buf,0,n);} os.flush();
            }
        } catch (Exception e) {
            // 封装异常，方便上层捕获
            throw new RuntimeException("导出过程中发生错误", e);
        } finally {
            // 确保临时文件被删除
            if (tempFile != null && tempFile.exists()) tempFile.delete();
            if (outputFile != null && outputFile.exists()) outputFile.delete();
        }
    }

    /**
     * 抽象方法，由子类实现具体的数据填充逻辑。
     * @param dto      包含导出数据的请求体。
     * @param workbook Apache POI 工作簿对象。
     * @param sheet    当前操作的工作表。
     * @throws Exception 如果填充过程中发生错误。
     */
    protected abstract void doFill(TExcelCustomTemplateDTO dto, Workbook workbook, Sheet sheet) throws Exception;

    /**
     * 根据模板类型解析应该使用的 Sheet 名称。
     * @param dto 请求体。
     * @return Sheet 名称。
     */
    protected String resolveSheetName(TExcelCustomTemplateDTO dto) { return dto.getTemplateType()!=null && dto.getTemplateType()==1 ? "模版" : "Template"; }

    /**
     * 根据模板类型获取数据填充的起始行号。
     * @param dto 请求体。
     * @return 起始行号（0-基础）。
     */
    protected int getStartRow(TExcelCustomTemplateDTO dto) { return dto.getTemplateType()!=null && dto.getTemplateType()==1 ? 5 : 6; }

    /**
     * 构建下载时使用的文件名。
     * @param dto 请求体。
     * @return 包含时间戳的文件名。
     */
    protected String buildFileName(TExcelCustomTemplateDTO dto){ return "updated_template_" + System.currentTimeMillis() + ".xlsx"; }



}

