/**
 * @Author: xujing
 * @Date: 2025/7/29
 * @Description: ""
 */
import React from 'react';
import { useEffect, useState } from 'react';
import { Slider, Select, Modal, Button, Dropdown, Radio, Checkbox, message, Tooltip, Switch, Input, Tabs, Empty } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { LeftOutlined, RightOutlined, WarningOutlined, PlusCircleOutlined, EditOutlined, MoreOutlined, QuestionCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import {
    getWorkflowTemplateDetails, getCommonStyleList, addWorkflowTemplate, editTemplateInfo, editWorkflowTemplate,
    getTextToImgStyleList, getTextToImgStyleCategoryList, getFavoriteStyleList, getRecentStyleList, addStyleFavorite, delStyleFavorite,
    getFashIpList, getFashIp<PERSON>ategoryList, getFavoriteIpList, getRecentIpList, addIpFavorite, delIpFavorite, getPrintingCategoryList
} from '@/api/task'
import arrowImg from '../../asset/icon/arrow.svg'
import '../integralDetail/index.css';
import { ReactComponent as FollowSvg } from '@/asset/svg/follow.svg'
import { ReactComponent as FollowActiveSvg } from '@/asset/svg/follow-active.svg'


export const WorkflowTemplateDetail = () => {
    const navigate = useNavigate();

    const location = useLocation();
    const { formTemplateName, groupId, type, templateId } = location.state || {};

    const [styleOrIp, setStyleOrIp] = useState('style');


    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()

    const baseItems = [
        {
            key: '8',
            label: '文生图'
        },
        {
            key: '9',
            label: '相似图裂变'
        },
        {
            key: '6',
            label: '平铺图-文生图'
        },
        {
            key: '7',
            label: '平铺图-图生图'
        },
        {
            key: '14',
            label: '图案裁剪'
        },
        {
            key: '11',
            label: '图片去背景'
        },
        {
            key: '12',
            label: '图片变清晰'
        },
        {
            key: '52',
            label: '印花图提取'
        },
        {
            key: '17',
            label: '侵权风险过滤'
        },
        {
            key: '18',
            label: '标题提取'
        },
    ]
    // ].map(item => ({
    //     ...item,
    //     disabled: hasTitleExtraction || (item.label !== '相似图裂变' && disabledItems.includes(item.label))
    // }));


    // 然后定义 itemList，它依赖于 items
    const [itemList, setItemList] = useState<any[]>([]);

    const [editingName, setEditingName] = useState(formTemplateName || '');
    const [isEditing, setIsEditing] = useState(false);

    const hasTitleExtraction = itemList.some((item: any) => item.label === '标题提取');
    const disabledItems = itemList.map((item: any) => item.label);

    // 提取公共的空样式对象
    const EMPTY_STYLE = {
        styleId: '',
        id: '',
        thumbnailImgUrl: '',
        originImgUrl: '',
        style: '',
        stylePrompt: ''
    };
    // 提取公共的空文生图风格对象
    const TEXTTOIMG_STYLE = {
        id: '',
        name: '',
        styleUrl: '',
        stylePrompt: ''
    };
    // 提取公共的空动漫IP对象
    const FASHIP_STYLE = {
        id: '',
        name: '',
        ipUrl: '',
        fashIpPrompt: ''
    };


    const [templateInfo, setTemplateInfo] = useState<any>({});
    const fetchTemplateDetail = () => {
        getWorkflowTemplateDetails({ templateId }).then((res: any) => {
            setTemplateInfo(res);
            setEditingName(res?.templateName);

            const nodeList = res?.nodeList?.map((node: any, index: number) => {
                return {
                    key: node.taskType.toString(),
                    label: baseItems.find(item => item.key === node.taskType.toString())?.label || '',
                    id: node.id,
                    nodeParams: typeof node.nodeParams === 'string' ? JSON.parse(node.nodeParams) : node.nodeParams || {},
                    index: index // 添加唯一index标识
                };
            }) || [];

            setItemList(nodeList);

            // 对每个节点进行数据反显处理
            nodeList.forEach((item: any) => {
                if (['6', '7'].includes(item.key)) {
                    // 平铺图节点：调用 updateItemAndList
                    updateItemAndList(item, allContinuousStyleList);
                } else if (item.key === '8') {
                    // 文生图节点：调用 textToImgUpdataItemAndList
                    textToImgUpdataItemAndList(item);
                }
            });
        })
    }

    const [currentItem, setCurrentItem] = useState<any>(null);

    useEffect(() => {
        fetchAllCommonStyleData(2);
        fetchAllCommonStyleData(1);
        if (type == 'edit') {
            fetchTemplateDetail()
        }
    }, [type])

    const items = baseItems.map(item => ({
        ...item,
        disabled: hasTitleExtraction || (item.label !== '相似图裂变' && disabledItems.includes(item.label))
    }));



    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.slice(0, 20); // 限制20字符
        setEditingName(value);
    };

    const addItem = (item: any) => {
        // setItemList(prevItems => [...prevItems, item]);
        // setCurrentItem(item);
        let defaultParams = {};

        // 根据任务类型设置不同的默认参数
        switch (item.key) {
            case '6': // 平铺图-文生图
                defaultParams = {
                    styleRadioValue: '1',
                    imageScale: '1:1',
                    imageNumber: '1', // 图片生成数量
                    currentStyle: EMPTY_STYLE
                };
                break;
            case '7': // 平铺图-图生图
                defaultParams = {
                    styleRadioValue: '1',
                    imageScale: '1:1',
                    imageNumber: '1', // 图片生成数量
                    currentStyle: EMPTY_STYLE
                };
                break;
            case '8': // 文生图
                defaultParams = {
                    styleRadioValue: '1',
                    fashIpRadioValue: '1',
                    isMattingFree: 0,
                    imageScale: '1:1',
                    imageNumber: '1', // 图片生成数量
                    currentStyle: TEXTTOIMG_STYLE,
                    currentFashIP: FASHIP_STYLE,


                };
                break;
            case '9': // 相似图裂变
                defaultParams = {
                    similarity: 0.5,
                    imageScale: '原图比例',
                    onlyFissionPattern: 0,
                    imageNumber: '1', // 图片生成数量
                };
                break;
            case '12': // 图片变清晰
                defaultParams = {
                    magnification: '1'
                };
                break;
            case '52': // 印花图提取
                defaultParams = {
                    imageNumber: '1' // 图片生成数量
                };
                break;
            case '17': // 侵权风险过滤
                defaultParams = {
                    isChecked: true
                };
                break;
            default:
                defaultParams = {};
        }
        if (item.key == '8') {
            fetchCommonStyleData(2, 1, 12); // 文生图(type=8)用2，平铺图生成(type=6)用1

        } else if (item.key == '6' || item.key == '7') {
            fetchCommonStyleData(1, 1, 12);
        }
        setItemList(prevItems => {
            const newIndex = prevItems.length; // 计算新项的index（当前数组长度即新索引）
            const newItem = {
                ...item,
                nodeParams: defaultParams,
                index: newIndex // 为新项添加index
            };
            setCurrentItem(newItem); // 直接使用newItem设置currentItem，避免访问外部未定义的prevItems
            return [...prevItems, newItem];
        });
    }

    // 配置参数  start
    const generateQuantityOptions: CheckboxGroupProps<string>['options'] = [
        { label: '1X', value: '1' },
        { label: '2X', value: '2' },
        { label: '4X', value: '4' },
    ];

    // 放大倍数
    const [generateQuantity, setGenerateQuantity] = useState('1')
    const handleQuantityChange = (e: any) => {
        // setGenerateQuantity(e.target.value)
        const newValue = e.target.value;
        if (currentItem) {
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, magnification: newValue } } : item
                )
            );
            // 更新currentItem
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, magnification: newValue } }));
        }
    };

    // 图片生成数量变化处理
    const handleImageNumberChange = (e: any) => {
        const newValue = e.target.value;
        if (currentItem) {
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, imageNumber: newValue } } : item
                )
            );
            // 更新currentItem
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, imageNumber: newValue } }));
        }
    };
    //原图相似度
    const [similarity, setSimilarity] = useState(0.5);
    const onSimilarityChange = (value: number) => {
        // setSimilarity(value)
        if (currentItem) {
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, similarity: value } } : item
                )
            );
            // 更新currentItem
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, similarity: value } }));
        }
    };
    //生成图片比例
    const [imageScale, setImageScale] = useState(currentItem?.key == '9' ? '原图比例' : '1:1')
    const handleChange = (value: string) => {
        if (currentItem) {
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, imageScale: value } } : item
                )
            );
            // 更新currentItem
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, imageScale: value } }));

        }
    };

    interface CustomStyle {
        styleId: string;
        id: string;
        originImgUrl: string;
        thumbnailImgUrl: string;
        stylePrompt: string;
        style: string;
        // 其他属性...
    }

    interface CustomStyleListResponse {
        data: CustomStyle[];
        total: number;
        default: any;
        // 其他属性...
    }

    // 参考风格列表
    const [commonStyleList, setCommonStyleList] = useState<CustomStyle[]>([]);
    // 无风格数据详情
    const [noStyleData, setNoStyleData] = useState<null | {
        stylePrompt: string
    }>(null)
    // 参考风格总页数
    const [commonStyleTotal, setCommonStyleTotal] = useState(0)
    // 参考风格当前页
    const [commonStylePage, setCommonStylePage] = useState(1)


    //获取参考风格列表
    const fetchCommonStyleData = async (type: number, pagenum: number, pageSize: number) => {
        try {
            const response = await getCommonStyleList(type, pagenum, pageSize) as CustomStyleListResponse;
            setCommonStyleList(response.data);
            setNoStyleData(response.default || { stylePrompt: '' });


            setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
            setCommonStylePage(pagenum);
        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };
    // 文生图所有参考风格列表(不分页版)
    const [allTextToImgStyleList, setAllTextToImgStyleList] = useState<any[]>([]);
    // 平铺图所有参考风格列表(不分页版)
    const [allContinuousStyleList, setAllContinuousStyleList] = useState<any[]>([]);
    //获取参考风格列表(不分页版)

    const fetchAllCommonStyleData = async (type: number) => {
        try {
            const response = await getCommonStyleList(type, 1, 999) as CustomStyleListResponse;
            if (type == 2) {
                setAllTextToImgStyleList(response.data);
            } else {
                setAllContinuousStyleList(response.data);
            }
        } catch (error) {
            console.error('获取数据时出错：', error);
        }
    };
    // 平铺图参考风格分页
    const commonStylePageChange = (pagenum: any) => {
        const styleType = currentItem?.key === '8' ? 2 : 1; // 文生图(type=8)用2，平铺图生成(type=6)用1
        fetchCommonStyleData(styleType, pagenum, 12)
    }
    // 文生图风格/动漫IP配置切换分页
    const StyleIpPageChange = (pagenum: any) => {
        if (styleOrIp === 'style') {
            if (filterTab == '2') {
                fetchFavoriteStyleData(pagenum, 12)
            } else if (filterTab == '3') {
                fetchRecentStyleData(pagenum, 12)
            } else {
                fetchStyleData(pagenum, 12, activeStyleTab)
            }
        } else {
            if (filterTab == '2') {
                fetchFavoriteIpData(pagenum, 12)
            } else if (filterTab == '3') {
                fetchRecentIpData(pagenum, 12)
            } else {
                fetchFashIpData(pagenum, 12, activeStyleTab)
            }
        }
    }


    //获取风格配置列表
    const fetchStyleData = async (pageNum: number, pageSize: number, categoryId: any) => {
        try {
            const response: any = await getTextToImgStyleList({ pageNum, pageSize, categoryId });
            setCommonStyleList(response.list);

            setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
            setCommonStylePage(pageNum);

            // 如果当前项没有具体的风格对象，且列表不为空，自动选择第一个并更新到当前项
            if (currentItem && (!currentItem?.nodeParams?.currentStyle?.id || currentItem?.nodeParams?.currentStyle?.id === '') && response.list?.length > 0) {
                const firstStyle = response.list[0];

                // 同时更新到当前项，并设置 styleRadioValue 为 '2'
                const currentIndex = currentItem.index;
                const newNodeParams = { ...currentItem.nodeParams, styleRadioValue: '2', currentStyle: firstStyle };
                const updatedCurrentItem = { ...currentItem, nodeParams: newNodeParams };
                setCurrentItem(updatedCurrentItem);
                setItemList(prevList =>
                    prevList.map(item =>
                        item.index === currentIndex ? updatedCurrentItem : item
                    )
                );
            }
        } catch (err: any) {
            messageApi.error(`获取风格配置列表失败: ${err?.data?.msg || err?.msg}`);
        }
    };
    //获取收藏风格列表
    const fetchFavoriteStyleData = (pageNum: number, pageSize: number) => {
        getFavoriteStyleList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list);
            setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
            setCommonStylePage(pageNum);

        })

    };
    //获取用户最近7天使用风格列表
    const fetchRecentStyleData = (pageNum: number, pageSize: number) => {

        getRecentStyleList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list);
            setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
            setCommonStylePage(pageNum);

        })
    };


    const [styleTabs, setStyleTabs] = useState<any[]>([]);
    const [activeStyleTab, setActiveStyleTab] = useState('');

    // 获取风格分类列表
    const fetchStyleCateData = () => {
        getTextToImgStyleCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
            const tabs = res?.list?.map((item: any) => ({
                key: item.id,
                label: item.name,
            })) || [];
            setStyleTabs(tabs);
            setActiveStyleTab(tabs[0]?.key)
            // 请求风格配置列表
            fetchStyleData(1, 12, tabs[0]?.key);
        });
    }

    const [filterTab, setFilterTab] = useState('1');
    const filterTabs = [
        {
            key: '1',
            label: '所有',
        },
        {
            key: '2',
            label: '收藏',
        },
        {
            key: '3',
            label: '最近',
        },
    ];

    //获取动漫IP列表
    const fetchFashIpData = async (pageNum: number, pageSize: number, categoryId: any) => {
        try {
            const response: any = await getFashIpList({ pageNum, pageSize, categoryId });
            setCommonStyleList(response.list);
            setCommonStyleTotal(Math.ceil(response.total / pageSize));// 总页数
            setCommonStylePage(pageNum);
            // 如果当前项没有具体的IP对象，且列表不为空，自动选择第一个并更新到当前项
            if (currentItem && (!currentItem?.nodeParams?.currentFashIP?.id || currentItem?.nodeParams?.currentFashIP?.id === '') && response.list?.length > 0) {
                const firstIp = response.list[0];

                // 同时更新到当前项，并设置 fashIpRadioValue 为 '2'
                const currentIndex = currentItem.index;
                const newNodeParams = { ...currentItem.nodeParams, fashIpRadioValue: '2', currentFashIP: firstIp };
                const updatedCurrentItem = { ...currentItem, nodeParams: newNodeParams };

                setCurrentItem(updatedCurrentItem);
                setItemList(prevList =>
                    prevList.map(item =>
                        item.index === currentIndex ? updatedCurrentItem : item
                    )
                );
            }
        } catch (err: any) {
            messageApi.error(`获取动漫IP列表失败: ${err?.data?.msg || err?.msg}`);
        }
    };
    //获取收藏动漫IP列表
    const fetchFavoriteIpData = (pageNum: number, pageSize: number) => {
        getFavoriteIpList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list);
            setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
            setCommonStylePage(pageNum);
        })

    };
    //获取用户最近7天使用动漫IP列表
    const fetchRecentIpData = (pageNum: number, pageSize: number) => {
        getRecentIpList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list);
            setCommonStyleTotal(Math.ceil(res.total / pageSize));// 总页数
            setCommonStylePage(pageNum);
        })
    };

    // 获取动漫IP分类列表
    const fetchIpCateData = () => {
        getFashIpCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
            const tabs = res?.list?.map((item: any) => ({
                key: item.id,
                label: item.name,
            })) || [];
            setStyleTabs(tabs);
            setActiveStyleTab(tabs[0]?.key)
            // 请求动漫IP配置列表
            fetchFashIpData(1, 12, tabs[0]?.key);
        });
    }

    // 收藏/取消收藏风格或IP
    const handleFollow = (item: any) => {
        const isFavorited = item.isFavorited;
        const isStyle = styleOrIp === 'style';

        // 根据类型和收藏状态确定要调用的API函数和提示信息
        const apiCall = isStyle
            ? (isFavorited ? () => delStyleFavorite(item.favoriteId) : () => addStyleFavorite({ materialStyleId: item.id }))
            : (isFavorited ? () => delIpFavorite(item.favoriteId) : () => addIpFavorite({ materialIpId: item.id }));

        const successMessage = isStyle
            ? (isFavorited ? '取消收藏风格成功' : '风格收藏成功')
            : (isFavorited ? '取消收藏IP成功' : 'IP收藏成功');

        const errorMessage = isStyle
            ? (isFavorited ? '取消收藏风格失败' : '风格收藏失败')
            : (isFavorited ? '取消收藏IP失败' : 'IP收藏失败');

        apiCall()
            .then(() => {
                if (styleOrIp === 'style') {
                    if (filterTab == '2') {
                        fetchFavoriteStyleData(1, 12)
                    } else if (filterTab == '3') {
                        fetchRecentStyleData(1, 12)
                    } else {
                        fetchStyleData(1, 12, activeStyleTab)
                    }
                } else {
                    if (filterTab == '2') {
                        fetchFavoriteIpData(1, 12)
                    } else if (filterTab == '3') {
                        fetchRecentIpData(1, 12)
                    } else {
                        fetchFashIpData(1, 12, activeStyleTab)
                    }
                }
                messageApi.success(successMessage);
            })
            .catch((err) => {
                messageApi.error(`${errorMessage}: ${err?.data?.msg || err?.msg}`);
            });
    };
    // 参考风格选择弹窗显影
    const [styleModalOpen, setStyleModalOpen] = useState(false)

    // 点击参考风格反显
    const selectStyle = (item: any) => {
        if (currentItem) {
            const newNodeParams = { ...currentItem.nodeParams, styleRadioValue: '2', currentStyle: item };
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: newNodeParams } : item
                )
            );
            // 更新currentItem
            setCurrentItem({ ...currentItem, nodeParams: newNodeParams });
        }
        setStyleModalOpen(false)
    }

    let commonStyleListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
        return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
            <img onClick={() => selectStyle(item)}
                src={item.originImgUrl || item.thumbnailImgUrl || ''}
                className={`${currentItem?.nodeParams?.currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px] `}
                style={{ objectFit: 'cover' }}
                alt="" />
            <p style={{ textAlign: 'center' }} >{item.style}</p>
        </div>
    }) : null

    // 风格/动漫IP选择弹窗显影
    const [isModalOpen, setIsModalOpen] = useState(false)

    let commonStyleIPListEL = commonStyleList?.length > 0 ? commonStyleList.map((item: any) => {
        return <div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white"
            onClick={() => {
                if (styleOrIp == 'style') {

                    // 同时更新到当前项
                    if (currentItem) {
                        const currentIndex = currentItem.index;
                        const newNodeParams = { ...currentItem.nodeParams, currentStyle: item };
                        const updatedCurrentItem = { ...currentItem, nodeParams: newNodeParams };

                        setCurrentItem(updatedCurrentItem);
                        setItemList(prevList =>
                            prevList.map(listItem =>
                                listItem.index === currentIndex ? updatedCurrentItem : listItem
                            )
                        );
                    }

                } else {
                    // 同时更新到当前项
                    if (currentItem) {
                        const currentIndex = currentItem.index;
                        const newNodeParams = { ...currentItem.nodeParams, currentFashIP: item };
                        const updatedCurrentItem = { ...currentItem, nodeParams: newNodeParams };

                        setCurrentItem(updatedCurrentItem);
                        setItemList(prevList =>
                            prevList.map(listItem =>
                                listItem.index === currentIndex ? updatedCurrentItem : listItem
                            )
                        );
                    }
                }
                setIsModalOpen(false);
            }}>
            <div className="relative w-full h-[110px] rounded-lg overflow-hidden group">
                <img
                    src={styleOrIp === 'style' ? `${item?.thumbnailImgUrl || item?.styleUrl || ''}` : `${item?.thumbnailImgUrl || item?.ipUrl || ''}`}
                    className={`${(styleOrIp === 'style' ? currentItem?.nodeParams?.currentStyle?.id : currentItem?.nodeParams?.currentFashIP?.id) == item.id ? '!border-primary' : ''} w-full h-full rounded-lg hover:border-primary border-[2px]`}
                    style={{ objectFit: 'cover' }}
                    alt=""
                />
                {/* 悬浮遮罩层 */}
                <div className="absolute inset-0 bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    {/* 收藏 */}
                    <div className='cursor-pointer absolute w-[20px] h-[20px] top-2  right-2'
                        onClick={(e) => {
                            e.stopPropagation(); // 阻止事件冒泡
                            handleFollow(item);   // 传递item参数
                        }}>
                        {item.isFavorited ? <FollowActiveSvg className={'w-[16px]'} fill={'var(--primary-color)'} /> : <FollowSvg className={'w-[16px] svg-hover-white'} />}
                    </div>
                </div>
            </div>
            <p style={{ textAlign: 'center' }} >{item.name}</p>
        </div>
    }) : <div className='w-full flex justify-center items-center pt-[100px]'><Empty /></div>


    const changeFilterTab = (key: string) => {
        setFilterTab(key)
        if (styleOrIp === 'style') {
            if (key == '2') {
                fetchFavoriteStyleData(1, 12)
            } else if (key == '3') {
                fetchRecentStyleData(1, 12)
            } else {
                fetchStyleData(1, 12, activeStyleTab)
            }
        } else {
            if (key == '2') {
                fetchFavoriteIpData(1, 12)
            } else if (key == '3') {
                fetchRecentIpData(1, 12)
            } else {
                fetchFashIpData(1, 12, activeStyleTab)
            }
        }
    }

    // 是否选择风格
    const [styleRadioValue, setStyleRadioValue] = useState('1');
    // 是否选择动漫IP
    const [fashIpRadioValue, setFashIpRadioValue] = useState('1');

    const handleRadioChange = (e: any) => {

        const newValue = e.target.value;
        if (currentItem) {
            const newNodeParams = newValue == '1'
                ? { ...currentItem.nodeParams, styleRadioValue: newValue, currentStyle: EMPTY_STYLE }
                : { ...currentItem.nodeParams, styleRadioValue: newValue, currentStyle: commonStyleList[0] };
            // 更新itemList
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: newNodeParams } : item
                )
            );
            // 更新currentItem
            setCurrentItem({ ...currentItem, nodeParams: newNodeParams });
        }

    }

    // 修改是否免抠图生成（默认否）
    const onSwitchChange = (checked: boolean) => {
        const value = checked ? 1 : 0;
        if (currentItem) {
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, isMattingFree: value } } : item
                )
            );
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, isMattingFree: value } }));
        }
    };
    // 修改是否仅裂变图案 （默认否）
    const onOnlyFissionPatternChange = (checked: boolean) => {
        const value = checked ? 1 : 0;
        if (currentItem) {
            setItemList(prevList =>
                prevList.map(item =>
                    item.index === currentItem.index ? { ...item, nodeParams: { ...item.nodeParams, onlyFissionPattern: value } } : item
                )
            );
            setCurrentItem((prev: any) => ({ ...prev, nodeParams: { ...prev.nodeParams, onlyFissionPattern: value } }));
        }
    };

    const [isGuideModalVisible, setIsGuideModalVisible] = useState(false)
    // 是否勾选
    const [isChecked, setIsChecked] = useState(true)
    // 同意侵权风险过滤功能使用须知
    const handleAgree = () => {
        setIsChecked(true)
        setIsGuideModalVisible(false)
    }

    // 印花图品类
    // 印花品类选择相关状态
    // const [categoryOptions, setCategoryOptions] = useState<any[]>([]);
    // const [categoryLoading, setCategoryLoading] = useState(false);
    // const [cropCategoryId, setCropCategoryId] = useState<number | undefined>(undefined);

    // 获取印花图品类
    // const fetchPrintingPatternCategory = async () => {
    //     getPrintingCategoryList({}).then((res: any) => {
    //         setCategoryOptions(res || []);
    //     });
    // }

    // 配置参数  end

    const saveTemplate = () => {
        if (!editingName) return messageApi.warning('请输入工作流模板名称');
        if (itemList?.length === 0) return messageApi.warning('请添加工作流模板');

        const hasInfringementTask = itemList.some(item => item.key === '17');
        if (hasInfringementTask && !isChecked) {
            messageApi.warning('侵权风险过滤任务，需要调整参数');
            return;
        }
        const list = itemList.map((item: any, index: number) => {
            const { styleRadioValue, fashIpRadioValue, isChecked, currentStyle, currentFashIP, ...restParams } = item.nodeParams;

            return {
                taskType: Number(item.key),
                nodeOrder: index + 1,
                // 文生图任务才添加materialStyleId属性
                ...(item.key == '8' && styleRadioValue ? {
                    materialStyleId: styleRadioValue === '1' ? '' : currentStyle?.id,
                } : {}),
                // 添加IP相关属性（如果fashIpRadioValue存在）
                ...(item.key == '8' && fashIpRadioValue ? {
                    materialIpId: fashIpRadioValue === '1' ? '' : currentFashIP?.id,
                } : {}),
                // // 印花图任务才添加品类属性
                // ...(item.key == '52' && cropCategoryId ? {
                //     cropCategoryId: cropCategoryId,
                // } : {}),
                nodeParams: JSON.stringify({
                    ...restParams,
                    // 仅当风格radioValue存在且任务不是文生图时添加风格相关属性
                    ...(item.key != '8' && styleRadioValue ? {
                        styleId: styleRadioValue === '1' ? '' : (currentStyle?.styleId || currentStyle?.id),
                        style: styleRadioValue === '1' ? '无风格' : (currentStyle?.style || currentStyle?.name),
                        stylePrompt: styleRadioValue === '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt
                    } : {})
                }),
            }
        })


        if (type == 'add') {

            addWorkflowTemplate({
                templateName: editingName,
                groupId: typeof groupId === 'number' ? groupId : '',
                nodeList: list
            }).then(res => {
                messageApi.success('新建工作流模板成功');
                navigate('/workspace/teamTools/workflowTemplate');
            }).catch(err => {
                messageApi.error(`新建工作流模板失败 ${err?.msg || err?.data?.msg || ''}`);
            })
        } else {
            editWorkflowTemplate({
                id: templateInfo?.id,
                nodes: list,
            }).then(res => {
                messageApi.success('编辑工作流模板成功');
                navigate('/workspace/teamTools/workflowTemplate');
            }).catch(err => {
                messageApi.error(`编辑工作流模板失败 ${err?.msg || err?.data?.msg || ''}`);
            })
        }
    };

    const changeTemplateInfo = () => {
        setIsEditing(false)
        if (type === 'edit' && templateInfo?.templateName == editingName) return;
        editTemplateInfo({
            id: templateInfo?.id,
            templateName: editingName,
            groupId: typeof groupId === 'number' ? groupId : '',
        }).then(res => {
            messageApi.success('修改工作流模板名称成功');
        }).catch(err => {
            messageApi.error(`修改工作流模板名称失败 ${err?.msg || err?.data?.msg || ''}`);
        })
    }

    // 提取更新列表和当前项的公共函数
    const updateItemAndList = (item: any, styleList: any[]) => {
        const hasStyleId = item.nodeParams?.styleId;
        const styleRadioValue = hasStyleId ? '2' : '1';
        const currentStyle = hasStyleId
            ? styleList.find(i => i.styleId === item.nodeParams.styleId) || EMPTY_STYLE
            : EMPTY_STYLE;

        // 更新列表项
        setItemList(prevList => prevList.map(pre =>
            pre.index === item.index
                ? { ...pre, nodeParams: { ...pre.nodeParams, styleRadioValue, currentStyle } }
                : pre
        ));

        // 只有当前项存在且是同一个项时才更新当前项
        setCurrentItem((prev: any) => {
            if (prev && prev.index === item.index) {
                return {
                    ...prev,
                    nodeParams: { ...prev.nodeParams, styleRadioValue, currentStyle }
                };
            }
            return prev;
        });
    };
    // 文生图提取更新列表和当前项的公共函数
    const textToImgUpdataItemAndList = (item: any) => {
        if (!item?.nodeParams) return;

        const nodeParams = typeof item.nodeParams === 'string' ? JSON.parse(item.nodeParams) : item.nodeParams;


        // 处理风格反显
        const hasStyleId = nodeParams.materialStyleId && nodeParams.materialStyleId !== '';
        const styleRadioValue = hasStyleId ? '2' : '1';
        const currentStyle = hasStyleId
            ? {
                id: nodeParams.materialStyleId,
                name: nodeParams.style,
                styleUrl: nodeParams.styleUrl,
                thumbnailImgUrl: nodeParams.thumbnailImgUrl,
                stylePrompt: nodeParams.stylePrompt
            }
            : TEXTTOIMG_STYLE;

        // 处理IP反显
        const hasIpId = nodeParams.materialIpId && nodeParams.materialIpId !== '';
        const fashIpRadioValue = hasIpId ? '2' : '1';
        const currentFashIP = hasIpId
            ? {
                id: nodeParams.materialIpId,
                name: nodeParams.fashIp,
                ipUrl: nodeParams.ipUrl,
                thumbnailImgUrl: nodeParams.thumbnailImgUrl,
                fashIpPrompt: nodeParams.fashIpPrompt
            }
            : FASHIP_STYLE;

        // 更新列表项
        setItemList(prevList => prevList.map(pre =>
            pre.index === item.index
                ? { ...pre, nodeParams: { ...pre.nodeParams, styleRadioValue, currentStyle, fashIpRadioValue, currentFashIP } }
                : pre
        ));

        // 只有当前项存在且是同一个项时才更新当前项
        setCurrentItem((prev: any) => {
            if (prev && prev.index === item.index) {
                return {
                    ...prev,
                    nodeParams: { ...prev.nodeParams, styleRadioValue, currentStyle, fashIpRadioValue, currentFashIP }
                };
            }
            return prev;
        });


    };

    return (
        <div className='h-full w-full bg-[#f5f8fc]'>
            {contextHolder}  {/* 这里确保 Modal 挂载 */}
            {messageContextHolder}  {/* 这里确保 Message 挂载 */}
            {/* 侵权风险过滤功能使用须知弹窗 start */}
            <Modal
                title="侵权风险检测功能使用须知"
                open={isGuideModalVisible}
                onOk={handleAgree}
                onCancel={() => setIsGuideModalVisible(false)}
                width={660}
                centered
                okText="我已知晓，同意"
            >
                <div className='border-[1px] border-normal p-[16px] border-[#999] bg-[#fafafa] mt-4 mb-4' style={{ borderRadius: '8px' }}>
                    <p className='mb-4'>在您使用本网站提供的侵权风险过滤服务前，请您仔细阅读本声明的所有条款，您一旦开始使用该服务，即表明您无条件地接受本免责声明，您应遵守本声明和相关法律的规定。</p>
                    <p className='mb-4'>1. 我们提供的侵权风险过滤服务（包括但不限于文字、图片等内容的侵权风险分析）均基于自动化算法及公开数据，结果仅供参考，不构成任何形式的法律意见或专业建议。</p>
                    <p className='mb-4'>2. 本服务无法保证结果的绝对准确性、完整性或时效性，可能存在漏判、误判或因法律法规变化导致的偏差。</p>
                    <p className='mb-4'>3. 您需自行判断过滤检测结果的适用性，并承担因依赖该结果而产生的全部风险。对于您依据本服务做出的任何行为（如内容发布、下架、商业决策等），我们不承担法律责任。</p>
                    <p className='mb-4'>4. 如您不同意本声明内容，应立即停止使用侵权风险过滤服务。继续使用视为接受全部条款。</p>
                    <p className='mb-4'>生成页面建议提示内容：检测结果仅供参考，不构成任何形式的法律意见或专业建议。您应做出独立的判断，本网站对您依据本服务做出的决策不承担责任。</p>
                </div>
            </Modal>
            {/* 侵权风险过滤功能使用须知弹窗 end */}
            <div className='flex items-center justify-between h-[48px] border-b-[1px] pl-4 pr-4 border-b-[#d8d8d8]'>
                <p className='cursor-pointer' onClick={() => navigate('/workspace/teamTools/workflowTemplate')}><LeftOutlined style={{ marginRight: 10 }} />{type == 'add' ? '新建' : '编辑'}工作流模板</p>
                <div className="flex items-center">
                    {isEditing ? (
                        <Input
                            value={editingName}
                            onChange={handleNameChange}
                            onBlur={changeTemplateInfo}
                            maxLength={20}
                            showCount
                            autoFocus
                        />
                    ) : (
                        <span className='text-ellipsis overflow-hidden'>{editingName}</span>
                    )}
                    {
                        type == 'edit' && <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => setIsEditing(true)}
                            className="ml-1 mr-1"
                        />
                    }
                </div>
                <Button type='primary' onClick={saveTemplate}>保存模板</Button>
            </div>
            <div className='h-[calc(100vh-142px)] overflow-y-scroll scrollbar-container scrollbar-hide '>
                <div className='flex justify-between pt-5 pb-5'>
                    <div className='flex items-center justify-center flex-col flex-1'>
                        <p className='text-white text-center bg-[#08AA6B] w-[60px] h-[32px] rounded-full pt-1'>开始</p>
                        <img src={arrowImg} alt="" />
                        <div className='w-[160px] h-[84px] bg-white rounded-lg text-center text-gray-500' style={{ cursor: 'not-allowed', lineHeight: '84px' }}>添加素材</div>
                        {itemList.map((item: any, inx: number) => (
                            <>
                                <img src={arrowImg} alt="" />
                                <div className={`w-[160px] h-[84px] bg-white rounded-lg p-3 border-[1px] cursor-pointer ${currentItem?.index == item.index ? 'border-[#EA0000]' : 'border-white'}`}
                                    onClick={() => {
                                        if (['6', '7'].includes(item.key)) {
                                            fetchCommonStyleData(1, 1, 12);
                                        }
                                        setCurrentItem(item)

                                    }}>
                                    <div className='flex justify-between'>
                                        {item.label}
                                        <div onClick={(e) => e.stopPropagation()}>
                                            <Dropdown
                                                menu={{
                                                    items: [
                                                        {
                                                            key: 'delete', label: '删除', onClick: () => {
                                                                if (currentItem && currentItem.index === item.index) {
                                                                    setCurrentItem(null);
                                                                }
                                                                // 删除后重新赋值index
                                                                setItemList(prev => {
                                                                    const newList = prev.filter((i) => i.index !== item.index);
                                                                    // 遍历新数组，将每个item的index设为当前索引
                                                                    return newList.map((item, newIndex) => ({ ...item, index: newIndex }));
                                                                });
                                                            }
                                                        }
                                                    ]
                                                }}
                                                placement="bottomLeft"
                                                trigger={['hover']}
                                            >
                                                <div className="group relative">
                                                    <MoreOutlined
                                                        className='opacity-0 group-hover:opacity-100 group-[.ant-dropdown-open]:opacity-100 transition-opacity p-[2px] bg-[#F5F5F5]'
                                                        style={{ transform: 'rotate(90deg)' }}
                                                    />
                                                </div>
                                            </Dropdown>
                                        </div>
                                    </div>
                                    <p className={`text-[12px] text-[#EA0000] flex items-center ${item.key == '17' && !isChecked ? 'opacity-100' : 'opacity-0'}`}><WarningOutlined style={{ marginRight: 2 }} />需要调整参数</p>
                                    {item.key != '11' && item.key != '14' && item.key != '18' && <p className='text-[12px] '>配置参数<RightOutlined /></p>}
                                </div >
                            </>
                        ))}
                        <Dropdown
                            menu={{
                                items,
                                onClick: ({ key }) => {
                                    if (key == 'header' || key == 'footer') {
                                        return
                                    }
                                    const selectedItem = items.find(item => item.key === key);
                                    if (selectedItem) {
                                        addItem(selectedItem);
                                    }
                                }
                            }}
                            placement="bottomLeft"
                            dropdownRender={(menu) => (
                                <div className="bg-white rounded-lg node-drop">
                                    <div className="pl-4 pt-3 font-bold cursor-pointer">新增功能节点</div>
                                    {React.cloneElement(menu as React.ReactElement<any>)}
                                    <div className="pl-4 pb-3 pr-2 text-xs cursor-pointer">
                                        <WarningOutlined /> 置灰节点在当前排序不可用
                                    </div>
                                </div>
                            )}
                        >
                            <PlusCircleOutlined style={{ color: '#d16c44', margin: '2px auto' }} />
                        </Dropdown>
                    </div>
                    {currentItem?.key && currentItem?.key != '11' && currentItem?.key != '14' && currentItem?.key != '18' &&
                        <div className='w-[500px] bg-white sticky  rounded-lg top-5 mr-4 flex  flex-col items-center justify-center' style={{ height: 'fit-content', padding: '18px 0 30px' }}>
                            <div className='flex items-center w-full justify-between pl-6 pr-6 text-[18px]' >
                                <p className='font-bold '>配置{currentItem?.label}参数</p>
                                <CloseOutlined onClick={() => setCurrentItem(null)} />
                            </div>
                            {(currentItem?.key === '8' || currentItem?.key === '6' || currentItem?.key === '7') &&
                                <div>
                                    {(currentItem?.key === '6' || currentItem?.key === '7') && <>
                                        <div className='flex justify-between items-center mt-8'>风格选择
                                            <Radio.Group style={{ width: '260px' }} block options={[
                                                { label: '不选风格', value: '1' },
                                                { label: '风格选择', value: '2' },
                                            ]} value={currentItem?.nodeParams?.styleRadioValue} onChange={(e) => handleRadioChange(e)} />
                                        </div>
                                        {currentItem?.nodeParams?.styleRadioValue == '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setStyleModalOpen(true)}>
                                            <img className='w-[80px] h-[80px] mr-[10px]' style={{ objectFit: 'cover' }} src={currentItem?.nodeParams?.currentStyle?.originImgUrl || currentItem?.nodeParams?.currentStyle?.thumbnailImgUrl || ""} />
                                            <div className='w-[200px]'>
                                                <p className='text-[18px] mb-[12px]'>选中风格</p>
                                                <p>{currentItem?.nodeParams?.currentStyle ? currentItem?.nodeParams?.currentStyle.style : ''}</p>
                                            </div>
                                            <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                                        </div>}
                                    </>}
                                    {currentItem?.key === '8' && <>
                                        <div className='flex justify-between items-center mt-8'>IP角色选择
                                            <Radio.Group style={{ width: '260px' }} block options={[
                                                { label: '不选IP', value: '1' },
                                                { label: 'IP选择', value: '2' },
                                            ]} value={currentItem?.nodeParams?.fashIpRadioValue || '1'} onChange={(e) => {
                                                const newValue = e.target.value;
                                                const newNodeParams = newValue == '1'
                                                    ? { ...currentItem.nodeParams, fashIpRadioValue: newValue, currentFashIP: FASHIP_STYLE }
                                                    : { ...currentItem.nodeParams, fashIpRadioValue: newValue };

                                                // 更新itemList
                                                setItemList(prevList =>
                                                    prevList.map(item =>
                                                        item.index === currentItem.index ? { ...item, nodeParams: newNodeParams } : item
                                                    )
                                                );
                                                // 更新currentItem
                                                setCurrentItem({ ...currentItem, nodeParams: newNodeParams });

                                                if (newValue === '2') {
                                                    // 请求动漫IP分类列表
                                                    fetchIpCateData();
                                                }
                                            }} />
                                        </div>
                                        {currentItem?.nodeParams?.fashIpRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
                                            onClick={() => {
                                                setFilterTab('1')
                                                // 请求动漫IP分类列表
                                                fetchIpCateData()
                                                setIsModalOpen(true)
                                                setStyleOrIp('ip')
                                            }}>

                                            <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentItem?.nodeParams?.currentFashIP?.thumbnailImgUrl || currentItem?.nodeParams?.currentFashIP?.ipUrl || ""} alt="" />
                                            <div className='w-[230px]'>
                                                <p className='text-[18px] mb-[12px]'>选中IP</p>
                                                <p>{currentItem?.nodeParams?.currentFashIP ? currentItem?.nodeParams?.currentFashIP.name : ''}</p>
                                            </div>
                                            <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
                                        </div>}
                                        <div className='flex justify-between items-center mt-8'>风格选择
                                            <Radio.Group style={{ width: '260px' }} block options={[
                                                { label: '不选风格', value: '1' },
                                                { label: '风格选择', value: '2' },
                                            ]} value={currentItem?.nodeParams?.styleRadioValue || '1'} onChange={(e) => {
                                                const newValue = e.target.value;
                                                const newNodeParams = newValue == '1'
                                                    ? { ...currentItem.nodeParams, styleRadioValue: newValue, currentStyle: TEXTTOIMG_STYLE }
                                                    : { ...currentItem.nodeParams, styleRadioValue: newValue };

                                                // 更新itemList
                                                setItemList(prevList =>
                                                    prevList.map(item =>
                                                        item.index === currentItem.index ? { ...item, nodeParams: newNodeParams } : item
                                                    )
                                                );
                                                // 更新currentItem
                                                setCurrentItem({ ...currentItem, nodeParams: newNodeParams });

                                                if (newValue === '2') {
                                                    // 请求风格分类列表
                                                    fetchStyleCateData();
                                                }
                                            }} />
                                        </div>
                                        {currentItem?.nodeParams?.styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]'
                                            onClick={() => {
                                                setFilterTab('1')
                                                // 请求风格分类列表
                                                fetchStyleCateData()
                                                setIsModalOpen(true)
                                                setStyleOrIp('style')
                                            }}>
                                            <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentItem?.nodeParams?.currentStyle?.thumbnailImgUrl || currentItem?.nodeParams?.currentStyle?.styleUrl || ""} alt="" />
                                            <div className='w-[230px]'>
                                                <p className='text-[18px] mb-[12px]'>选中风格</p>
                                                <p>{currentItem?.nodeParams?.currentStyle ? currentItem?.nodeParams?.currentStyle.name : ''}</p>
                                            </div>
                                            <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} alt="" />
                                        </div>}

                                        <div className='flex  justify-between items-center  text-normal mt-6 mb-3'>
                                            <p>免抠图生成<Tooltip title="利用AI自动从图片中精准分离出前景对象并去除背景,可以省去用户手动抠图的操作。"><QuestionCircleOutlined className=' ml-[6px]' /></Tooltip></p>
                                            <Switch checked={currentItem?.nodeParams?.isMattingFree === 1} onChange={onSwitchChange} />
                                        </div>
                                    </>}
                                    <div className='flex justify-between items-center  mt-6'>
                                        <p className='w-[100px]'>生成图片比例</p>
                                        <Select
                                            defaultValue="1:1"
                                            style={{ width: 280 }}
                                            onChange={handleChange}
                                            value={currentItem?.nodeParams?.imageScale}
                                            options={[
                                                { value: '1:1', label: '1 : 1' },
                                                { value: '2:3', label: '2 : 3' },
                                                { value: '3:2', label: '3 : 2' },
                                                { value: '3:4', label: '3 : 4' },
                                                { value: '4:3', label: '4 : 3' },
                                                { value: '9:16', label: '9 : 16' },
                                                { value: '16:9', label: '16 : 9' },
                                            ]}
                                        />
                                    </div>
                                    {/* 参考风格弹窗 start */}
                                    <Modal width={800} title="" footer={null} open={styleModalOpen} onCancel={() => setStyleModalOpen(false)}>
                                        <div >
                                            <div className='flex flex-wrap mt-[20px] h-[340px]'>
                                                {commonStyleListEL}
                                            </div>
                                            <label>
                                                <ul className='flex justify-center  mt-[20px]'>
                                                    {Array.from({ length: commonStyleTotal }, (_, index) => (
                                                        <li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
                                                            style={{ lineHeight: '18px' }} onClick={() => { commonStylePageChange(index + 1) }}>{index + 1}</li>
                                                    ))}
                                                    <li >{'共' + commonStyleTotal + '页'}</li>
                                                </ul>
                                            </label>
                                        </div>
                                    </Modal>
                                    {/* 参考风格弹窗 end */}
                                    {/* 设置风格/IP弹窗 start */}
                                    <Modal width={800} footer={null} title={styleOrIp == 'style' ? '设置作品风格' : '设置动漫IP'} open={isModalOpen} centered onCancel={() => setIsModalOpen(false)}>
                                        <div className='h-[452px]  relative'>
                                            <div className="flex items-center gap-x-4">
                                                {
                                                    filterTabs.map((item, index) => (
                                                        <p className={`${filterTab == item.key ? 'bg-black text-white' : ''} w-[70px] h-[30px] rounded-full cursor-pointer bg-[#eee] `}
                                                            style={{ lineHeight: '30px', textAlign: 'center' }} onClick={() => changeFilterTab(item.key)}
                                                        >{item.label}</p>
                                                    ))
                                                }
                                            </div>
                                            {filterTab == '1' && (<Tabs defaultActiveKey="1" items={styleTabs} activeKey={activeStyleTab}
                                                onChange={(key: any) => {
                                                    setActiveStyleTab(key);
                                                    // 根据选中的分类ID查询数据
                                                    if (styleOrIp == 'style') {
                                                        fetchStyleData(1, 12, key);
                                                    } else {
                                                        fetchFashIpData(1, 12, key);

                                                    }

                                                }} />)}

                                            <div className=' h-[340px]' >
                                                <div className='flex flex-wrap mt-[20px]'>
                                                    {commonStyleIPListEL}
                                                </div>
                                                <label>
                                                    <ul className='flex justify-center mt-[20px] w-full  absolute bottom-[8px] left-0'>
                                                        {Array.from({ length: commonStyleTotal }, (_, index) => (
                                                            <li className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
                                                                style={{ lineHeight: '18px' }} onClick={() => { StyleIpPageChange(index + 1) }}>{index + 1}</li>
                                                        ))}
                                                        <li>{'共 ' + commonStyleTotal + ' 页'}</li>
                                                    </ul>
                                                </label>
                                            </div>
                                        </div>
                                    </Modal>
                                    {/* 设置风格/IP弹窗 end */}
                                </div>
                            }
                            {currentItem?.key === '9' &&
                                <div>
                                    <div className='flex justify-between items-center mt-4'>
                                        <p className='w-[110px]'>原图相似度</p>
                                        <span className='mr-[10px] '>低</span>
                                        <Slider
                                            styles={{
                                                rail: {
                                                    background: '#ddd'
                                                },
                                                track: {
                                                    background: 'var(--primary-color)'
                                                },
                                                handle: {
                                                    background: 'var(--primary-color)'
                                                },
                                            }}
                                            className='w-[220px] '
                                            min={0.3}
                                            max={0.8}
                                            step={0.05}
                                            value={currentItem?.nodeParams?.similarity}
                                            onChange={onSimilarityChange}
                                            tooltip={{ open: false }}
                                        />
                                        <span className='ml-[10px] '>高</span>
                                    </div>
                                    <div className='flex justify-between items-center  mt-6'>
                                        <p className='w-[100px]'>生成图片比例</p>
                                        <Select
                                            defaultValue="原图比例"
                                            style={{ width: 280 }}
                                            onChange={handleChange}
                                            value={currentItem?.nodeParams?.imageScale}
                                            options={[
                                                { value: '原图比例', label: '原图比例' },
                                                { value: '1:1', label: '1 : 1' },
                                                { value: '2:3', label: '2 : 3' },
                                                { value: '3:2', label: '3 : 2' },
                                                { value: '3:4', label: '3 : 4' },
                                                { value: '4:3', label: '4 : 3' },
                                                { value: '9:16', label: '9 : 16' },
                                                { value: '16:9', label: '16 : 9' },
                                            ]}
                                        />
                                    </div>
                                    <div className='flex justify-between items-center  mt-6'>
                                        <p>仅裂变图案</p>
                                        <Switch checked={currentItem?.nodeParams?.onlyFissionPattern === 1} onChange={onOnlyFissionPatternChange} />
                                    </div>

                                </div>
                            }
                            {(currentItem?.key === '6' || currentItem?.key === '7' || currentItem?.key === '8' || currentItem?.key === '9' || currentItem?.key === '52') &&
                                <div className='flex justify-between items-center  mt-6'>
                                    <p className='w-[100px]'>图片生成数量</p>
                                    <Radio.Group
                                        size="large"
                                        block
                                        options={[
                                            { label: '1', value: '1' },
                                            { label: '2', value: '2' },
                                            { label: '4', value: '4' },
                                        ]}
                                        onChange={handleImageNumberChange}
                                        value={currentItem?.nodeParams?.imageNumber}
                                        optionType="button"
                                        buttonStyle="solid"
                                        style={{ width: 280 }}
                                    />
                                </div>
                            }
                            {/* {currentItem?.key === '52' &&
                                <div>
                                    <div className='flex justify-between items-center  mt-6'>
                                        <p className='w-[100px]'>选择品类</p>
                                        <Select
                                            style={{ width: 280 }}
                                            showSearch
                                            allowClear
                                            placeholder="请选择品类"
                                            value={cropCategoryId}
                                            onChange={(v) => setCropCategoryId(v)}
                                            optionLabelProp="label"
                                            filterOption={(input, option) =>
                                                (typeof option?.label === 'string' ? option.label.toLowerCase() : '').includes(input.toLowerCase())
                                            }
                                            options={categoryOptions.map(item => ({
                                                label: item.name,
                                                value: item.id
                                            }))}
                                            loading={categoryLoading}
                                            onFocus={() => {
                                                if (categoryOptions.length === 0) {
                                                    fetchPrintingPatternCategory();
                                                }
                                            }}
                                        />
                                    </div>
                                </div>
                            } */}
                            {currentItem?.key === '12' &&
                                <div>
                                    <p className='w-[360px] flex items-center justify-between text-normal mt-[26px] '>放大倍数
                                        <Radio.Group
                                            size="large"
                                            block
                                            options={generateQuantityOptions}
                                            onChange={handleQuantityChange}
                                            defaultValue="1"
                                            value={currentItem?.nodeParams?.magnification}
                                            optionType="button"
                                            buttonStyle="solid"
                                            className='w-[280px]'
                                        />
                                    </p>
                                </div>
                            }
                            {currentItem?.key === '17' &&
                                <div className="w-full flex items-center pl-8 mt-6">
                                    <Checkbox
                                        checked={isChecked}
                                        onChange={(e) => {
                                            if (isChecked) {
                                                setIsChecked(false);
                                            } else {
                                                setIsGuideModalVisible(true);
                                            }
                                        }}
                                    />
                                    <span className='ml-[10px] cursor-pointer' onClick={() => {
                                        if (isChecked) {
                                            setIsChecked(false);
                                        } else {
                                            setIsGuideModalVisible(true);
                                        }
                                    }}>侵权风险过滤功能使用须知</span>
                                </div>
                            }
                        </div>
                    }


                </div>
            </div>
            {/* </div> */}
        </div >
    );
}


export default WorkflowTemplateDetail;