package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialStyle;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface MaterialStyleMapper {
    List<MaterialStyle> selectByCondition(@Param("name") String name,
                                          @Param("status") Integer status,
                                          @Param("categoryId") Integer categoryId,
                                          @Param("taskType") Integer taskType);

    MaterialStyle selectById(@Param("id") Integer id);

    int insert(MaterialStyle materialStyle);

    int update(MaterialStyle materialStyle);

    int deleteById(@Param("id") Integer id);

    // 用于分页查询的总数
    int countByCondition(@Param("name") String name,
                         @Param("status") Integer status,
                         @Param("categoryId") Integer categoryId,
                         @Param("taskType") Integer taskType);

    // 分页查询
    List<MaterialStyle> selectPageByCondition(@Param("offset") int offset,
                                              @Param("pageSize") int pageSize,
                                              @Param("name") String name,
                                              @Param("status") Integer status,
                                              @Param("categoryId") Integer categoryId,
                                              @Param("taskType") Integer taskType);
}