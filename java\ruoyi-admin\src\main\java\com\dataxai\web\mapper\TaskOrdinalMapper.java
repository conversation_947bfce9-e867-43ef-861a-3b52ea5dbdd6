package com.dataxai.web.mapper;

import java.util.List;

import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.domain.OrdinalImageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务次数Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-14
 */
@Mapper
public interface TaskOrdinalMapper
{
    /**
     * 查询任务次数
     *
     * @param taskOrdinalId 任务次数主键
     * @return 任务次数
     */
    public TaskOrdinal selectTaskOrdinalByTaskOrdinalId(String taskOrdinalId);

    /**
     * 查询任务次数列表
     *
     * @param taskOrdinal 任务次数
     * @return 任务次数集合
     */
    public List<TaskOrdinal> selectTaskOrdinalList(TaskOrdinal taskOrdinal);

    /**
     * 新增任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    public int insertTaskOrdinal(TaskOrdinal taskOrdinal);

    /**
     * 修改任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    public int updateTaskOrdinal(TaskOrdinal taskOrdinal);

    /**
     * 删除任务次数
     *
     * @param taskOrdinalId 任务次数主键
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskOrdinalId(String taskOrdinalId);

    /**
     * 批量删除任务次数
     *
     * @param taskOrdinalIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskOrdinalIds(String[] taskOrdinalIds);

    /**
     * 批量删除每次任务执行后生成的图片
     *
     * @param taskOrdinalIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrdinalImgResultByTaskOrdinalIds(String[] taskOrdinalIds);

    /**
     * 批量新增每次任务执行后生成的图片
     *
     * @param ordinalImgResultList 每次任务执行后生成的图片列表
     * @return 结果
     */
    public int batchOrdinalImgResult(List<OrdinalImgResult> ordinalImgResultList);


    /**
     * 通过任务次数主键删除每次任务执行后生成的图片信息
     *
     * @param taskOrdinalId 任务次数ID
     * @return 结果
     */
    public int deleteOrdinalImgResultByTaskOrdinalId(String taskOrdinalId);

    //查询用户传过的图
    List<OrdinalImageVO> getHistoryImage(@Param("types") List<Long> types, @Param("userId") Long userId);

    // 截取部分原图图片地址
    Integer updateOrdinalUrlSub(@Param("originImgUrl") String originImgUrl,@Param("taskOrdinalId")String taskOrdinalId);

    List<TaskOrdinal> selectByTaskIds(@Param("taskIds") String[] taskIds, @Param("userId") Long userId);

    void updateTaskOrdinalStatus(TaskOrdinal taskOrdinal);


    List<TaskOrdinal> getTaskOrdinalByPush(@Param("taskIds") String[] taskIds);
    List<TaskOrdinal> getTaskOrdinalByTask(@Param("taskIds") String[] taskIds);

    int delTaskPush(String[] pushIds);

    List<TaskOrdinal> selectTaskOrdinalListInfo(TaskOrdinal taskOrdinal);
}
