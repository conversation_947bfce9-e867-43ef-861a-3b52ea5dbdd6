package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.domain.*;
import com.dataxai.domain.dto.RiskDetectionTaskDetailDTO;
import com.dataxai.mapper.RiskDetectionTaskDetailMapper;
import com.dataxai.service.IProductInfoService;
import com.dataxai.service.IRiskDetectionTaskDetailService;
import com.dataxai.service.IRiskDetectionTaskService;
import com.dataxai.service.ITeamUserService;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.service.IOrdinalImgResultService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 平台API-风险检测任务接口
 */
@Api(tags = "平台API风险检测任务接口")
@RestController
@RequestMapping("/platform/risk/detection/task")
public class PlatformRiskDetectionTaskController extends BaseController<RiskDetectionTask> {

    @Autowired
    private IRiskDetectionTaskService riskDetectionTaskService;
    @Autowired
    private IRiskDetectionTaskDetailService riskDetectionTaskDetailService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IOrdinalImgResultService ordinalImgResultService;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;
    @Autowired
    private RiskDetectionTaskDetailMapper riskDetectionTaskDetailMapper;

    /**
     * 列表
     */
    @ApiOperation("查询风险检测任务列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(
            HttpServletRequest request,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "batchNumber", required = false) String batchNumber
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) return R.fail("用户未授权");

            // 构造查询对象
            RiskDetectionTask query = new RiskDetectionTask();

            // 根据用户模式设置数据过滤条件
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(user.getUserId());
                if (teamUser != null && teamUser.getIsAdmin() == true) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                } else {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(user.getUserId());
                }
            } else {
                // 个人模式：通过owner_id过滤，且team_id为0
                query.setOwnerId(user.getUserId());
                query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
            }

            query.setStartTime(startTime);
            query.setEndTime(endTime);
            query.setStatus(status);
            query.setTaskBatch(batchNumber);

            startPage();
            List<RiskDetectionTask> list = riskDetectionTaskService.selectRiskDetectionTaskList(query);
            PageInfo<RiskDetectionTask> pageInfo = new PageInfo<>(list);
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", pageInfo.getTotal());
            pageData.put("data", pageInfo.getList());
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 详情
     */
    @ApiOperation("获取风险检测任务详情，支持分页查询")
    @GetMapping("/{id}")
    public R<RiskDetectionTaskDetailDTO> getInfo(HttpServletRequest request,
                                  @PathVariable Long id,
                                  @RequestParam(value = "riskLevel", required = false) String riskLevel) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        RiskDetectionTask riskDetectionTask = riskDetectionTaskService.selectRiskDetectionTaskById(id);
        if (riskDetectionTask == null) return R.fail("风险检测任务不存在");

        // 根据用户模式设置权限验证
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
        if (!userTeamInfo.isTeamMode() || userTeamInfo.getTeamId() == null) {
            // 个人模式：必须是任务拥有者
            if (!Objects.equals(riskDetectionTask.getOwnerId(), user.getUserId())) {
                return R.fail("无权限查看此风险检测任务");
            }
        } else {
            // 团队模式：检查是否属于同一团队
            if (!Objects.equals(riskDetectionTask.getTeamId(), userTeamInfo.getTeamId())) {
                return R.fail("无权限查看此风险检测任务");
            }
        }

        // 查询任务详情列表，支持风险等级筛选和分页
        List<RiskDetectionTaskDetail> detailList;
        if (riskLevel != null && !riskLevel.trim().isEmpty()) {
            // 支持多个风险等级（用逗号分隔），同时支持分页
            String[] riskLevels = riskLevel.trim().split(",");
            List<String> levelList = new ArrayList<>();

            for (String level : riskLevels) {
                String trimmedLevel = level.trim();
                if (!trimmedLevel.isEmpty()) {
                    levelList.add(trimmedLevel);
                }
            }

            // 使用分页查询
            startPage();
            detailList = riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailByTaskIdAndRiskLevels(id, levelList);
        } else {
            startPage();
            detailList = riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailByTaskId(id);
        }

        int processedCount = 0;
        int unprocessedCount = 0;
        for (RiskDetectionTaskDetail detail : detailList) {
            if (detail.getProcessStatus() != null && detail.getProcessStatus() == 2) processedCount++; else unprocessedCount++;
        }

        RiskDetectionTaskDetailDTO detailDTO = new RiskDetectionTaskDetailDTO();
        detailDTO.setId(riskDetectionTask.getId());
        detailDTO.setTaskBatch(riskDetectionTask.getTaskBatch());
        detailDTO.setTotalAmount(riskDetectionTask.getTotalAmount());
        detailDTO.setSuccessAmount(riskDetectionTask.getSuccessAmount());
        detailDTO.setFailAmount(riskDetectionTask.getFailAmount());
        detailDTO.setStatus(riskDetectionTask.getStatus());
        detailDTO.setOwnerId(riskDetectionTask.getOwnerId());
        detailDTO.setCreateTime(riskDetectionTask.getCreateTime());
        detailDTO.setUpdateTime(riskDetectionTask.getUpdateTime());
        detailDTO.setDetailList(detailList);
        detailDTO.setDetailCount(detailList.size());
        detailDTO.setProcessedCount(processedCount);
        detailDTO.setUnprocessedCount(unprocessedCount);

        return R.ok(detailDTO);
    }

    /**
     * 通过图片链接创建
     */
    @ApiOperation("通过图片链接创建风险检测任务")
    @Log(title = "平台API-创建风险检测任务(图片链接)", businessType = BusinessType.INSERT)
    @PostMapping("/createByImageUrls")
    public R<Map<String, Object>> createByImageUrls(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        Object urlsObj = requestData.get("imageUrls");
        List<String> imageUrls = new ArrayList<>();
        if (urlsObj instanceof List) {
            for (Object item : (List<?>) urlsObj) {
                if (item != null && !item.toString().trim().isEmpty()) {
                    imageUrls.add(item.toString().trim());
                }
            }
        }
        if (imageUrls.isEmpty()) return R.fail("图片链接列表不能为空");
        try {
            RiskDetectionTask task = new RiskDetectionTask();
            task.setOwnerId(user.getUserId());
            task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1);
            int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
            if (taskResult <= 0) return R.fail("创建风险检测任务失败");

            List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrl);
                detail.setType(3);
                detail.setTypeId(null);
                detail.setProcessStatus(1);
                detail.setOwnerId(user.getUserId());
                detail.setImageStatus(0);
                detailList.add(detail);
            }
            int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("successDetails", detailResult);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("创建风险检测任务失败：" + e.getMessage());
        }
    }

    /**
     * 通过产品ID创建
     */
    @ApiOperation("通过产品信息ID创建风险检测任务")
    @Log(title = "平台API-创建风险检测任务(产品ID)", businessType = BusinessType.INSERT)
    @PostMapping("/createByProductIds")
    public R<Map<String, Object>> createByProductIds(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        Object productListObj = requestData.get("productList");
        if (!(productListObj instanceof List)) return R.fail("产品信息参数列表不能为空");
        List<Map<String, Object>> productList = (List<Map<String, Object>>) productListObj;
        if (productList.isEmpty()) return R.fail("产品信息参数列表不能为空");
        try {
            List<String> imageUrls = new ArrayList<>();
            List<Long> validIds = new ArrayList<>();
            List<Integer> typeList = new ArrayList<>();
            List<Long> typeIdList = new ArrayList<>();
            Integer typeid = 0;
            for (Map<String, Object> item : productList) {
                Object idObj = item.get("id");
                Object typeObj = item.get("type");
                if (idObj == null || typeObj == null) continue;
                Long id = Long.valueOf(idObj.toString());
                Integer type = Integer.valueOf(typeObj.toString());
                typeid = type;
                String imageUrl = null;
                if (type == 1) {
                    OrdinalImgResult ordinalImgResult = ordinalImgResultService.selectOrdinalImgResultByImageId(id.toString());
                    if (ordinalImgResult != null && ordinalImgResult.getUserId().equals(user.getUserId())) {
                        String resImgUrl = ordinalImgResult.getResImgUrl();
                        if (resImgUrl != null && !resImgUrl.isEmpty()) {
                            imageUrl = com.dataxai.web.utils.CommonUtils.addCosPrefix(resImgUrl);
                        }
                    }
                } else if (type == 2) {
                    ProductInfo productInfo = productInfoService.selectProductInfoById(id);
                    if (productInfo != null && Objects.equals(productInfo.getOwnerId(), user.getUserId())) {
                        imageUrl = productInfo.getProductImageUrl();
                    }
                }
                if (imageUrl != null && !imageUrl.isEmpty()) {
                    imageUrls.add(imageUrl);
                    validIds.add(id);
                    typeList.add(type);
                    typeIdList.add(id);
                }
            }
            if (imageUrls.isEmpty()) return R.fail("没有找到有效的图片");

            RiskDetectionTask task = new RiskDetectionTask();
            task.setOwnerId(user.getUserId());
            task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1);
            int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
            if (taskResult <= 0) return R.fail("创建风险检测任务失败");

            List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
            List<ProductInfo> productInfoUpdateList = new ArrayList<>();
            for (int i = 0; i < imageUrls.size(); i++) {
                RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                ProductInfo productInfoEntity = new ProductInfo();
                if (typeid == 2) {
                    ProductInfo productInfo = productInfoService.selectProductInfoById(validIds.get(i));
                    if (productInfo != null) {
                        detail.setProductTitle(productInfo.getProductTitle());
                        detail.setTypeId(productInfo.getId());
                        productInfoEntity.setId(validIds.get(i));
                        productInfoEntity.setInfringementMark(1);
                    }
                } else {
                    detail.setTypeId(typeIdList.get(i));
                }
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrls.get(i));
                detail.setType(typeList.get(i));
                detail.setProcessStatus(1);
                detail.setOwnerId(user.getUserId());
                detailList.add(detail);
                detail.setImageStatus(0);
                productInfoUpdateList.add(productInfoEntity);
            }
            int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);
            if (typeid == 2) {
                productInfoService.updateProductInfoList(productInfoUpdateList);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("validIds", validIds.size());
            result.put("successDetails", detailResult);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("创建风险检测任务失败：" + e.getMessage());
        }
    }

    /** 删除任务 */
    @ApiOperation("删除风险检测任务")
    @Log(title = "平台API-删除风险检测任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<String> remove(HttpServletRequest request, @PathVariable Long id) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");
        RiskDetectionTask task = riskDetectionTaskService.selectRiskDetectionTaskById(id);
        if (task == null) return R.fail("风险检测任务不存在");
        if (!Objects.equals(task.getOwnerId(), user.getUserId())) return R.fail("无权限删除此风险检测任务");
        int rows = riskDetectionTaskService.deleteRiskDetectionTaskById(id);
        return rows > 0 ? R.ok("删除成功") : R.fail("删除失败");
    }

    /** 批量删除任务 */
    @ApiOperation("批量删除风险检测任务")
    @Log(title = "平台API-批量删除风险检测任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public R<String> removeBatch(HttpServletRequest request, @PathVariable Long[] ids) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");
        if (ids == null || ids.length == 0) return R.fail("参数不能为空");
        for (Long id : ids) {
            RiskDetectionTask task = riskDetectionTaskService.selectRiskDetectionTaskById(id);
            if (task == null) return R.fail("风险检测任务不存在，ID: " + id);
            if (!Objects.equals(task.getOwnerId(), user.getUserId())) return R.fail("无权限删除风险检测任务，ID: " + id);
        }
        int rows = riskDetectionTaskService.deleteRiskDetectionTaskByIds(ids);
        return rows > 0 ? R.ok("批量删除成功") : R.fail("批量删除失败");
    }

    /** 批量删除详情 */
    @ApiOperation("批量删除风险检测任务详情")
    @Log(title = "平台API-批量删除风险检测任务详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/detail/batch/{ids}")
    public R<String> removeDetailBatch(HttpServletRequest request, @PathVariable Long[] ids) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");
        if (ids == null || ids.length == 0) return R.fail("参数不能为空");
        for (Long id : ids) {
            RiskDetectionTaskDetail detail = riskDetectionTaskDetailService.selectRiskDetectionTaskDetailById(id);
            if (detail == null) return R.fail("风险检测任务详情不存在，ID: " + id);
            if (!Objects.equals(detail.getOwnerId(), user.getUserId())) return R.fail("无权限删除风险检测任务详情，ID: " + id);
        }
        int rows = riskDetectionTaskDetailService.deleteRiskDetectionTaskDetailByIds(ids);
        return rows > 0 ? R.ok("批量删除成功") : R.fail("批量删除失败");
    }




}