package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.domain.TUser;
import com.dataxai.domain.TeamUser;
import com.dataxai.domain.WorkflowTemplateGroup;
import com.dataxai.service.ITeamUserService;
import com.dataxai.service.IWorkflowTemplateGroupService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 平台API-工作流模板分组接口
 */
@Api(tags = "平台API工作流模板分组接口")
@RestController
@RequestMapping("/platform/workflow/templateGroup")
public class PlatformWorkflowTemplateGroupController extends BaseController {

    @Autowired
    private IWorkflowTemplateGroupService workflowTemplateGroupService;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;

    /**
     * 查询工作流模板分组列表（分页）
     */
    @ApiOperation("查询工作流模板分组列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(
            HttpServletRequest request,
            @ApiParam("分组名称") @RequestParam(required = false) String groupName
    ) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }

            WorkflowTemplateGroup query = new WorkflowTemplateGroup();
            query.setGroupName(groupName);

            // 根据用户模式设置数据过滤条件
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：管理员可查看团队全部；普通成员仅查看自己创建
                TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(user.getUserId());
                query.setTeamId(userTeamInfo.getTeamId());
                if (teamUser == null || !Boolean.TRUE.equals(teamUser.getIsAdmin())) {
                    query.setUserId(user.getUserId());
                }
            } else {
                // 个人模式：teamId=0，仅查看自己创建
                query.setTeamId(0L);
                query.setUserId(user.getUserId());
            }

            startPage();
            List<WorkflowTemplateGroup> list = workflowTemplateGroupService.selectWorkflowTemplateGroupList(query);

            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", new PageInfo<>(list).getTotal());
            pageData.put("data", list);
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增工作流模板分组
     */
    @ApiOperation("新增工作流模板分组")
    @Log(title = "平台API-新增工作流模板分组", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(HttpServletRequest request, @RequestBody WorkflowTemplateGroup workflowTemplateGroup) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplateGroup == null || workflowTemplateGroup.getGroupName() == null || workflowTemplateGroup.getGroupName().trim().isEmpty()) {
                return R.fail("分组名称不能为空");
            }

            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
            Long resolvedTeamId = (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) ? userTeamInfo.getTeamId() : 0L;

            // 设置归属
            workflowTemplateGroup.setUserId(user.getUserId());
            workflowTemplateGroup.setTeamId(resolvedTeamId);

            // 名称唯一性校验（按团队维度）
            boolean unique = workflowTemplateGroupService.checkGroupNameUnique(
                    resolvedTeamId,
                    workflowTemplateGroup.getGroupName(),
                    null
            );
            if (!unique) {
                return R.fail("分组名称在当前团队中已存在");
            }

            int rows = workflowTemplateGroupService.insertWorkflowTemplateGroup(workflowTemplateGroup);
            return rows > 0 ? R.ok("新增成功") : R.fail("新增失败");
        } catch (Exception e) {
            return R.fail("新增失败: " + e.getMessage());
        }
    }

    /**
     * 修改工作流模板分组
     */
    @ApiOperation("修改工作流模板分组")
    @Log(title = "平台API-修改工作流模板分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<String> edit(HttpServletRequest request, @RequestBody WorkflowTemplateGroup workflowTemplateGroup) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            if (workflowTemplateGroup == null || workflowTemplateGroup.getId() == null) {
                return R.fail("参数不完整");
            }
            WorkflowTemplateGroup existing = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplateGroup.getId());
            if (existing == null) {
                return R.fail("工作流模板分组不存在");
            }
            // 权限校验：仅创建者可编辑（与平台产品逻辑一致）
            if (!Objects.equals(existing.getUserId(), user.getUserId())) {
                return R.fail("无权限修改此分组");
            }

            Long teamId = existing.getTeamId() == null ? 0L : existing.getTeamId();
            // 名称唯一性（排除自己）
            boolean unique = workflowTemplateGroupService.checkGroupNameUnique(
                    teamId,
                    workflowTemplateGroup.getGroupName(),
                    workflowTemplateGroup.getId()
            );
            if (!unique) {
                return R.fail("分组名称在当前团队中已存在");
            }

            // 固定归属，不允许通过请求更改
            workflowTemplateGroup.setTeamId(teamId);
            workflowTemplateGroup.setUserId(existing.getUserId());

            int rows = workflowTemplateGroupService.updateWorkflowTemplateGroup(workflowTemplateGroup);
            return rows > 0 ? R.ok("修改成功") : R.fail("修改失败");
        } catch (Exception e) {
            return R.fail("修改失败: " + e.getMessage());
        }
    }

    /**
     * 删除工作流模板分组
     */
    @ApiOperation("删除工作流模板分组")
    @Log(title = "平台API-删除工作流模板分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<String> remove(HttpServletRequest request, @ApiParam("分组ID") @PathVariable Long id) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) {
                return R.fail("用户未授权");
            }
            WorkflowTemplateGroup existing = workflowTemplateGroupService.selectWorkflowTemplateGroupById(id);
            if (existing == null) {
                return R.fail("工作流模板分组不存在");
            }
            if (!Objects.equals(existing.getUserId(), user.getUserId())) {
                return R.fail("无权限删除此分组");
            }
            // 业务校验：分组下不能有模板
            if (!workflowTemplateGroupService.checkCanDelete(id)) {
                return R.fail("分组下还有模板，无法删除");
            }

            int rows = workflowTemplateGroupService.deleteWorkflowTemplateGroupById(id);
            return rows > 0 ? R.ok("删除成功") : R.fail("删除失败");
        } catch (Exception e) {
            return R.fail("删除失败: " + e.getMessage());
        }
    }
}