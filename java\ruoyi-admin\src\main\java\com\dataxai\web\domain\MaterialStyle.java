package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 素材风格表
 * @TableName t_material_style
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialStyle {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "风格ID")
    private Integer id;

    /**
     * 风格名称
     */
    @ApiModelProperty(value = "风格名称")
    private String name;

    /**
     * 风格图片地址
     */
    @ApiModelProperty(value = "风格图片URL")
    private String styleUrl;

    /**
     * 缩略图片地址
     */
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailImgUrl;

    /**
     * 风格分类ID
     */
    @ApiModelProperty(value = "风格分类ID")
//    @JsonProperty("t_material_style_category_id") // 与前端的命名保持一致
    private Integer MaterialStyleCategoryId;

    /**
     * 任务类型：6-平铺图文生图，8-文生图
     */
    @ApiModelProperty(value = "任务类型：6-平铺图文生图，8-文生图")
    private Integer taskType;

    /**
     * 风格提示词
     */
    @ApiModelProperty(value = "风格提示词")
    private String stylePrompt;

    /**
     * 用户输入的风格
     */
    @ApiModelProperty(value = "用户输入风格")
    private String style;

    /**
     * 状态(0-禁用,1-启用)
     */
    @ApiModelProperty(value = "状态(0-禁用,1-启用)")
    private Integer status;

    /**
     * 排序字段（越小越靠前）
     */
    @ApiModelProperty(value = "排序字段（越小越靠前）")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String categoryName; // 分类名称
}