{"version": 3, "file": "static/css/774.0e686d38.chunk.css", "mappings": "AACE,8FACE,UAGA,oGACE,UCLL,+DACC,uBAED,oDACC,0BCLF,sCACE,kBACA,6CAKE,yBAGA,mBAPA,YAKA,WACA,SALA,kBACA,SACA,UAKA,UAEF,4CAKE,yBAGA,mBAPA,YAKA,WACA,SALA,kBACA,SACA,UAKA,UCtBJ,oCACC,0BAGE,4EACC,oBAED,iFACC,oBACA,0BCTJ,WAUI,0BAJA,kBACA,iCACA,kCALA,YAEA,cAIA,kBAPA,UAQA,CAEJ,iBAII,cAHA,eAKA,mBADA,UAJA,CAOJ,iBACI,0BAEJ,gBAII,SAGA,cADA,eAJA,eACA,iBAEA,aAJA,UAMA,CAEJ,sBACI,wBAKJ,uBAFI,gCAIA,CAFJ,cAEI,4CAGJ,iBAII,mBADA,sBAEA,kBAGA,4DACA,eAPA,YAIA,iBACA,gBANA,UAQA,CAEJ,2BAKI,eAAc,CAHd,SADA,eAGA,YADA,UAEe,CAEnB,WASI,mBARA,qBAAqB,CAIrB,iBAAiB,CACjB,cAAc,CAJd,oBAAoB,CACpB,eAAe,CAIf,eAAe,CACf,eAAe,CAJf,gBAKA,CCtEJ,sCACE,kBACA,6CAKE,yBAGA,mBAPA,YAKA,WACA,SALA,kBACA,SACA,UAKA,UAEF,4CAKE,yBAGA,mBAPA,YAKA,WACA,SALA,kBACA,SACA,UAKA,UCpBJ,yCAKE,SAJA,kBAGA,SAEA,iBAHA,qFADA,SAIA,CAIF,0CAGE,SAFA,kBAGA,QACA,sEAHA,2DAGA,CACA,iDAGE,6BAEA,YAEA,MAAK,CANL,kBAKA,MAFA,WAFA,SAKA,CAIJ,2CAOE,gBACA,0JACQ,CAER,kCACA,0BARA,YAEA,OAJA,kBAGA,MAFA,WAFA,SAWA", "sources": ["component/generate-image/generateImage.module.scss", "business-component/spell-book/spellbookmodal.module.scss", "business-component/task-detail/task-editor/task-settings/task-settings-component/mannequin-scene/taskDescPreset.module.scss", "business-component/task-detail/task-editor/task-settings/task-settings-component/model/component/Feature.module.scss", "business-component/task-detail/task-editor/task-settings/task-settings-component/face-feature-custom/component/TaskFaceFeatureCustom.scss", "business-component/task-detail/task-editor/task-desc-preset/taskDescPreset.module.scss", "component/article-image/article-image.module.scss"], "sourcesContent": [".generateImageContainer {\r\n  .generateImageContainerMask {\r\n    opacity: 0;\r\n  }\r\n  &:hover {\r\n    .generateImageContainerMask {\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n", ".spellBook-tabs {\r\n\t:global(.ant-tabs-nav-operations) {\r\n\t\tdisplay: none !important;\r\n\t}\r\n\t:global(.ant-tabs-tab) {\r\n\t\tfont-weight: 500 !important;\r\n\t}\r\n}\r\n", ".sceneGoodsList {\r\n  position: relative;\r\n  &::before {\r\n    content: ' ';\r\n    position: absolute;\r\n    top: -2px;\r\n    width: 80%;\r\n    border: solid 2px  #EEF2FF;\r\n    height: 2px;\r\n    left: 10%;\r\n    border-radius: 60px;\r\n    z-index: 1;\r\n  }\r\n  &::after {\r\n    content: ' ';\r\n    position: absolute;\r\n    top: -6px;\r\n    width: 60%;\r\n    border: solid 2px  #F2F7FE;\r\n    height: 2px;\r\n    left: 20%;\r\n    border-radius: 60px;\r\n    z-index: 1;\r\n  }\r\n}\r\n", ".task-model-collapse {\r\n\tmargin-top: 20px !important;\r\n\t:global {\r\n\t\t.ant-collapse-item {\r\n\t\t\t.ant-collapse-header {\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t}\r\n\t\t\t.ant-collapse-content-box {\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t\tpadding-block: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", ".uphistory {\r\n    margin-top: 2%;\r\n    width: 100%;\r\n    height: auto;\r\n    border: 1px dashed #E5E7EB;\r\n    margin: 10px 0;\r\n    border-radius: 5px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    text-align: center;\r\n    border: #1677ff 1px dashed;\r\n}\r\n.uphistory label{\r\n    font-size: 18px;\r\n}\r\n.uphistory label{\r\n    display: block;\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n}\r\n.uphistory:hover {\r\n    border: 1px dashed #1677ff;\r\n}\r\n.uphistory .del{\r\n    width: auto;\r\n    font-size: 14px;\r\n    line-height: 14px;\r\n    border: 0;\r\n    padding: auto;\r\n    cursor: pointer;\r\n    color: #cf2317;\r\n}\r\n.uphistory .del:hover{\r\n    color: #1677ff !important;\r\n}\r\n.imgArea{\r\n    display: flex;\r\n}\r\n.uphistory ul {\r\n    display: flex;\r\n    justify-content: center;\r\n\r\n}\r\n.uphistory ul li{\r\n    width: 20px;\r\n    height: 20px;\r\n    border:  #ccc 1px solid;\r\n    background: #e5e5e5;\r\n    border-radius: 2px;\r\n    line-height: 20px;\r\n    margin: auto 5px;\r\n    box-shadow: #fff 0 0 0 2px;\r\n    cursor: pointer;\r\n}\r\n.uphistory ul .total-title{\r\n    font-size: 18px;\r\n    border: 0;\r\n    width: auto;\r\n    height: auto;\r\n    background:#fff;\r\n}\r\n.his-label{\r\n    border:1px solid #ccc;\r\n    display:inline-block;\r\n    margin-left:76%;\r\n    text-align:right;\r\n    border-radius:5px;\r\n    cursor:pointer;\r\n    margin-top:10px;\r\n    padding:2px 3px;\r\n    background: #f8ebeb;\r\n}\r\n", ".sceneGoodsList {\r\n  position: relative;\r\n  &::before {\r\n    content: ' ';\r\n    position: absolute;\r\n    top: -2px;\r\n    width: 80%;\r\n    border: solid 2px  #EEF2FF;\r\n    height: 2px;\r\n    left: 10%;\r\n    border-radius: 60px;\r\n    z-index: 1;\r\n  }\r\n  &::after {\r\n    content: ' ';\r\n    position: absolute;\r\n    top: -6px;\r\n    width: 60%;\r\n    border: solid 2px  #F2F7FE;\r\n    height: 2px;\r\n    left: 20%;\r\n    border-radius: 60px;\r\n    z-index: 1;\r\n  }\r\n}\r\n", "\r\n// 拖拽区域，视窗大小\r\n.articleImagePanel {\r\n  position: absolute;\r\n  z-index: 1;\r\n  user-select: none;\r\n  top: 80px;\r\n  left: 50%;\r\n  translate: -50%  0;\r\n}\r\n\r\n// 拖拽区域\r\n.articleImageCanvas {\r\n  position: absolute;\r\n  transform-origin: left top;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n  & canvas {\r\n    position: absolute;\r\n    z-index: 2;\r\n    background-color: transparent;\r\n    width: 100%;\r\n    height: 100%;\r\n    top: 0;\r\n    left: 0;\r\n  }\r\n}\r\n\r\n.articleImageCanvasBg{\r\n  z-index: 1;\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  background: #cccccc;\r\n  background-image:\r\n          linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0),\r\n          linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0);\r\n  background-position: 0 0, 30px 30px;\r\n  background-size: 20px 20px;\r\n}\r\n"], "names": [], "sourceRoot": ""}