package com.dataxai.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 素材风格分类表
 * @TableName t_material_style_category
 */
@Data
@TableName("t_material_style_category")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialStyleCategory {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "分类ID")
    private Integer id;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    /**
     * 排序字段（越小越靠前）
     */
    @ApiModelProperty(value = "排序字段（越小越靠前）")
    private Integer sortOrder;

    /**
     * 任务类型：6-平铺图文生图，8-文生图
     */
    @ApiModelProperty(value = "任务类型：6-平铺图文生图，8-文生图")
    private Integer taskType;
}