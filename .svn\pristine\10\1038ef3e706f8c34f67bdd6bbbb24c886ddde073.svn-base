package com.dataxai.web.task.core.ordinal.base;

import com.dataxai.web.Constants.Constants;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.TaskOrdinalDTO;
import com.dataxai.web.domain.WebCharacterModel;
import com.dataxai.web.domain.WebScenRealHuman;
import com.dataxai.web.task.core.ordinal.AbstractAICommercialShootingTaskOrdinalFactory;
import com.dataxai.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 真人图任务序号工厂实现类
 *
 * <p>处理真人图任务（任务类型：0）的参数校验和逻辑处理</p>
 *
 * <AUTHOR>
 * @date 2024-01-08
 * @version 1.0
 */
@Component
@Slf4j
public class RealPersonTaskOrdinalFactory extends AbstractAICommercialShootingTaskOrdinalFactory {

    @Override
    protected boolean isSupportedTaskType(Long taskType) {
        return Constants.TASK_TYPE_REAL_PERSON == taskType;
    }

    /** 参数校验 */
    @Override
    protected void validateSpecificParameters(TaskOrdinalDTO taskOrdinalDTO) {
        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "开始校验真人图任务特定参数，任务ID: {}", taskOrdinalDTO.getTaskId());

        // 真人图特有的参数校验逻辑可以在这里添加
        // 目前所有通用校验逻辑已经迁移到基类中

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图任务特定参数校验通过，任务ID: {}", taskOrdinalDTO.getTaskId());
    }

    /**
     * 客户端请求的参数处理
     */
    @Override
    protected void processSpecificLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) throws Exception {
        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "开始处理真人图任务参数，任务ID: {}", taskOrdinalDTO.getTaskId());

        // 设置真人图特有标识
        taskOrdinal.setRealPerson(1); // 1表示真人图

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图任务特定逻辑处理完成，任务ID: {}", taskOrdinalDTO.getTaskId());
    }

    /**
     * 真人图任务需要更详细的进度推送
     * 重写此方法以启用进度推送（即使是批量任务）
     */
    @Override
    protected boolean shouldPushProgress(TaskOrdinal taskOrdinal) {
        // 真人图任务总是推送进度，即使是批量任务
        return true;
    }

    /**
     * 真人图任务在成功后需要特殊的结果推送逻辑
     * 只有当结果数量达到预期时才推送
     */
    @Override
    protected boolean shouldPushResultAfterSuccess(TaskOrdinal taskOrdinal, List<OrdinalImgResult> results) {
        if (results == null || results.isEmpty()) {
            TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图任务结果为空，不推送结果");
            return false;
        }

        // 真人图任务要求至少有1个有效结果
        long validResults = results.stream()
                .filter(result -> result != null && result.getResImgUrl() != null)
                .count();

        if (validResults < 1) {
            TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图任务有效结果不足，不推送结果。有效结果数: {}", validResults);
            return false;
        }

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图任务有效结果满足条件，推送结果。有效结果数: {}", validResults);
        return true;
    }

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{(long) Constants.TASK_TYPE_REAL_PERSON};
    }

    /**
     * 执行任务的特有逻辑
     */
    @Override
    protected void processSpecificExecution(TaskOrdinal taskOrdinal) throws Exception {
        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "处理真人图特定执行逻辑，任务ID: {}", taskOrdinal.getTaskId());

        // 真人图特有的执行逻辑
        // 1. 验证模特信息是否有效
        if (taskOrdinal.getModelCharacterId() != null) {
            WebCharacterModel characterModel = webCharacterModelService.selectWebCharacterModelById(taskOrdinal.getModelCharacterId());
            if (characterModel == null) {
                throw new ServiceException("模特信息不存在，ID：" + taskOrdinal.getModelCharacterId(), 400);
            }
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "验证模特信息成功，模特ID: {}，提示词: {}", characterModel.getId(), characterModel.getPrompt());
        }

        // 2. 验证场景信息是否有效
        if (taskOrdinal.getSceneId() != null) {
            WebScenRealHuman sceneInfo = webScenRealHumanService.selectWebScenRealHumanById(taskOrdinal.getSceneId());
            if (sceneInfo == null) {
                throw new ServiceException("场景信息不存在，ID：" + taskOrdinal.getSceneId(), 400);
            }
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "验证场景信息成功，场景ID: {}，提示词: {}", sceneInfo.getId(), sceneInfo.getPrompt());
        }

        // 3. 处理可选模特特征
        if (StringUtils.isNotEmpty(taskOrdinal.getOptionalCharacterIds())) {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "处理可选模特特征，特征IDs: {}", taskOrdinal.getOptionalCharacterIds());
            // 这里可以添加具体的可选特征处理逻辑
        }

        // 4. 设置真人图特有的队列类型
        // 真人图使用特定的队列处理

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_REAL_PERSON, "真人图特定执行逻辑处理完成，任务ID: {}", taskOrdinal.getTaskId());
    }

}