package com.dataxai.web.controller.monitor;

import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.web.task.ConsumerManager;
import com.dataxai.web.task.TaskQueueService;
import com.dataxai.web.task.TaskProcessorConfig;
import com.dataxai.web.task.ThreadPoolManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 队列监控控制器
 * 提供队列状态监控和统计信息
 */
@Slf4j
@RestController
@RequestMapping("/monitor/queue")
@Api(tags = "队列监控接口")
public class QueueMonitorController extends BaseController {

    @Autowired
    private TaskQueueService taskQueueService;

    @Autowired
    private TaskProcessorConfig taskProcessorConfig;

    @Autowired
    private ConsumerManager consumerManager;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    /**
     * 获取队列统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getQueueStatistics() {
        try {
            Map<String, Object> statistics = taskQueueService.getQueueStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return AjaxResult.error("获取队列统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列配置信息
     */
    @GetMapping("/config")
    public AjaxResult getQueueConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("queueConfigs", taskProcessorConfig.getAllQueueConfigs());
            return AjaxResult.success(config);
        } catch (Exception e) {
            log.error("获取队列配置信息失败", e);
            return AjaxResult.error("获取队列配置信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列状态概览
     */
    @GetMapping("/overview")
    public AjaxResult getQueueOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // 获取队列统计信息
            Map<String, Object> statistics = taskQueueService.getQueueStatistics();

            // 计算总体统计
            long totalHighPriority = 0;
            long totalNormalPriority = 0;

            @SuppressWarnings("unchecked")
            Map<String, Long> highPrioritySizes = (Map<String, Long>) statistics.get("highPrioritySizes");
            @SuppressWarnings("unchecked")
            Map<String, Long> normalPrioritySizes = (Map<String, Long>) statistics.get("normalPrioritySizes");

            if (highPrioritySizes != null) {
                totalHighPriority = highPrioritySizes.values().stream().mapToLong(Long::longValue).sum();
            }

            if (normalPrioritySizes != null) {
                totalNormalPriority = normalPrioritySizes.values().stream().mapToLong(Long::longValue).sum();
            }

            overview.put("totalHighPriorityTasks", totalHighPriority);
            overview.put("totalNormalPriorityTasks", totalNormalPriority);
            overview.put("totalTasks", totalHighPriority + totalNormalPriority);
            overview.put("queueStatistics", statistics);
            overview.put("queueConfigs", taskProcessorConfig.getAllQueueConfigs());

            return AjaxResult.success(overview);
        } catch (Exception e) {
            log.error("获取队列概览失败", e);
            return AjaxResult.error("获取队列概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列优先级信息
     */
    @GetMapping("/priority")
    public AjaxResult getQueuePriority() {
        try {
            Map<String, Object> priorityInfo = new HashMap<>();

            // 高速队列优先级（1-6）
            Map<String, Integer> highPriorityQueues = new HashMap<>();
            highPriorityQueues.put("material:high", 1);
            highPriorityQueues.put("aiSnap:high", 2);
            highPriorityQueues.put("mask:high", 3);
            highPriorityQueues.put("aiOcr:high", 4);
            highPriorityQueues.put("associate:high", 5);
            highPriorityQueues.put("risk:high", 6);

            // 普通队列优先级（11-17）
            Map<String, Integer> normalPriorityQueues = new HashMap<>();
            normalPriorityQueues.put("material:normal", 11);
            normalPriorityQueues.put("aiSnap:normal", 12);
            normalPriorityQueues.put("mask:normal", 13);
            normalPriorityQueues.put("aiOcr:normal", 14);
            normalPriorityQueues.put("associate:normal", 15);
            normalPriorityQueues.put("risk:normal", 16);
            normalPriorityQueues.put("batch:normal", 17);

            priorityInfo.put("highPriorityQueues", highPriorityQueues);
            priorityInfo.put("normalPriorityQueues", normalPriorityQueues);
            priorityInfo.put("description", "数字越小优先级越高，高速队列优先级永远高于普通队列");

            return AjaxResult.success(priorityInfo);
        } catch (Exception e) {
            log.error("获取队列优先级信息失败", e);
            return AjaxResult.error("获取队列优先级信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取消费者状态
     */
    @GetMapping("/consumer/status")
    @ApiOperation("获取消费者状态")
    public AjaxResult getConsumerStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("isShutdown", consumerManager.isShutdown());
            status.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("获取消费者状态失败", e);
            return AjaxResult.error("获取消费者状态失败");
        }
    }

    /**
     * 获取线程池状态
     */
    @GetMapping("/threadpool/status")
    @ApiOperation("获取线程池状态")
    public AjaxResult getThreadPoolStatus() {
        try {
            Map<String, Object> status = threadPoolManager.getThreadPoolStatistics();
            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("获取线程池状态失败", e);
            return AjaxResult.error("获取线程池状态失败");
        }
    }

    /**
     * 检查系统健康状态
     */
    @GetMapping("/health")
    @ApiOperation("检查系统健康状态")
    public AjaxResult checkHealth() {
        try {
            Map<String, Object> health = new HashMap<>();

            // 检查消费者状态
            health.put("consumerShutdown", consumerManager.isShutdown());

            // 检查线程池状态
            Map<String, Object> threadPoolStats = threadPoolManager.getThreadPoolStatistics();
            health.put("threadPoolStats", threadPoolStats);

            // 检查队列状态
            Map<String, Object> queueStats = taskQueueService.getQueueStatistics();
            health.put("queueStats", queueStats);

            // 整体健康状态
            boolean isHealthy = !consumerManager.isShutdown();
            health.put("healthy", isHealthy);
            health.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success(health);
        } catch (Exception e) {
            log.error("检查系统健康状态失败", e);
            return AjaxResult.error("检查系统健康状态失败");
        }
    }

    /**
     * 手动触发队列处理（仅用于测试）
     */
    @PostMapping("/test/process")
    @ApiOperation("手动触发队列处理（测试用）")
    public AjaxResult testProcessQueue() {
        try {

            // 获取队列统计信息
            Map<String, Object> beforeStats = taskQueueService.getQueueStatistics();
            log.info("处理前队列状态: {}", beforeStats);

            // 等待一段时间
            Thread.sleep(5000);

            // 再次获取队列统计信息
            Map<String, Object> afterStats = taskQueueService.getQueueStatistics();
            log.info("处理后队列状态: {}", afterStats);

            Map<String, Object> result = new HashMap<>();
            result.put("beforeStats", beforeStats);
            result.put("afterStats", afterStats);
            result.put("timestamp", System.currentTimeMillis());

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("手动触发队列处理失败", e);
            return AjaxResult.error("手动触发队列处理失败");
        }
    }
}