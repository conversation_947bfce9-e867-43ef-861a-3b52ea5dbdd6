package com.dataxai.domain;

import com.dataxai.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 团队用户关系表 t_team_user
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
public class TeamUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 团队ID */
    private Long teamId;

    /** 用户ID */
    private Long userId;

    /** 在团队中的昵称 */
    @Size(min = 0, max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /** 职业 */
    @Size(min = 0, max = 50, message = "职业长度不能超过50个字符")
    private String profession;

    /** 是否团队管理员 */
    private Boolean isAdmin;


    private Long isAdmins;

    /** 加入时间 */
    private Date joinTime;

    /** 用户名（查询时使用，不存储） */
    private String userName;

    /** 手机号（查询时使用，不存储） */
    private String phonenumber;

    /** 团队名称（查询时使用，不存储） */
    private String teamName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTeamId(Long teamId) 
    {
        this.teamId = teamId;
    }

    public Long getTeamId() 
    {
        return teamId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }

    public void setProfession(String profession) 
    {
        this.profession = profession;
    }

    public String getProfession() 
    {
        return profession;
    }

    public void setIsAdmin(Boolean isAdmin) 
    {
        this.isAdmin = isAdmin;
    }

    public Boolean getIsAdmin() 
    {
        return isAdmin;
    }

    public void setJoinTime(Date joinTime) 
    {
        this.joinTime = joinTime;
    }

    public Date getJoinTime() 
    {
        return joinTime;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setPhonenumber(String phonenumber) 
    {
        this.phonenumber = phonenumber;
    }

    public String getPhonenumber() 
    {
        return phonenumber;
    }

    public void setTeamName(String teamName) 
    {
        this.teamName = teamName;
    }

    public String getTeamName() 
    {
        return teamName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teamId", getTeamId())
            .append("userId", getUserId())
            .append("nickname", getNickname())
            .append("profession", getProfession())
            .append("isAdmin", getIsAdmin())
            .append("joinTime", getJoinTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 