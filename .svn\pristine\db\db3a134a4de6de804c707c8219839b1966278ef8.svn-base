package com.dataxai.web.service.impl;

import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.common.utils.StringUtils;
import cn.hutool.http.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.ByteArrayResource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.ArrayList;
import java.util.regex.Pattern;

/**
 * 风险检测图片上传服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
@Slf4j
public class RiskDetectionImageUploadServiceImpl implements RiskDetectionImageUploadService
{

    @Autowired
    private AliYunFileService aliYunFileService;

    @Override
    public String uploadImage(MultipartFile file) {
        try {
            log.info("开始上传单个图片文件: {}", file.getOriginalFilename());

            // 使用AliYunFileService上传到阿里云
            String url = aliYunFileService.uploadALiYun(file);

            if (StringUtils.isEmpty(url)) {
                log.error("图片上传失败，返回URL为空: {}", file.getOriginalFilename());
                return null;
            }

            // 验证图片内容（可选，根据业务需求决定是否启用）
            try {
                boolean isValid = aliYunFileService.authenticateUrl(url);
                if (!isValid) {
                    log.warn("图片内容验证失败: {}", url);
                    // 根据业务需求决定是否返回null或继续使用
                }
            } catch (Exception e) {
                log.warn("图片内容验证异常，继续使用: {}", e.getMessage());
            }

            log.info("图片上传成功: {} -> {}", file.getOriginalFilename(), url);
            return url;

        } catch (Exception e) {
            log.error("图片上传异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<String> uploadImages(List<MultipartFile> files) {
        List<String> imageUrls = new ArrayList<>();

        log.info("开始批量上传图片，文件数量: {}", files.size());

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            log.info("正在上传第 {} 个文件: {}", i + 1, file.getOriginalFilename());

            String imageUrl = uploadImage(file);
            System.out.println("imageUrl========================" +imageUrl);
            if (imageUrl != null) {
                imageUrl = CommonUtils.subCosPrefix(imageUrl);
                imageUrls.add(imageUrl);
                log.info("第 {} 个文件上传成功: {}", i + 1, imageUrl);
            } else {
                log.error("第 {} 个文件上传失败: {}", i + 1, file.getOriginalFilename());
            }
        }

        log.info("批量上传完成，成功上传 {} 个文件，失败 {} 个文件",
                imageUrls.size(), files.size() - imageUrls.size());

        return imageUrls;
    }

    @Override
    public List<String>
    uploadRiskImages(List<MultipartFile> files) {
        List<String> imageUrls = new ArrayList<>();

        log.info("开始批量上传图片，文件数量: {}", files.size());

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            log.info("正在上传第 {} 个文件: {}", i + 1, file.getOriginalFilename());

            String imageUrl = uploadImage(file);
            if (imageUrl != null) {

                imageUrls.add(imageUrl);
                log.info("第 {} 个文件上传成功: {}", i + 1, imageUrl);
            } else {
                log.error("第 {} 个文件上传失败: {}", i + 1, file.getOriginalFilename());
            }
        }

        log.info("批量上传完成，成功上传 {} 个文件，失败 {} 个文件",
                imageUrls.size(), files.size() - imageUrls.size());

        return imageUrls;
    }

    @Override
    public List<String> processImageUrls(List<String> imageUrls) {
        List<String> processedUrls = new ArrayList<>();

        log.info("开始处理图片URL列表，URL数量: {}", imageUrls.size());

        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            log.info("正在处理第 {} 个URL: {}", i + 1, imageUrl);

            String processedUrl = processImageUrl(imageUrl);
            if (processedUrl != null) {
                processedUrl = CommonUtils.subCosPrefix(processedUrl);
                processedUrls.add(processedUrl);
                log.info("第 {} 个URL处理成功: {}", i + 1, processedUrl);
            } else {
                log.error("第 {} 个URL处理失败: {}", i + 1, imageUrl);
            }
        }

        log.info("URL处理完成，成功处理 {} 个URL，失败 {} 个URL",
                processedUrls.size(), imageUrls.size() - processedUrls.size());

        return processedUrls;
    }

    /**
     * 处理单个图片URL（下载并重新上传到阿里云）
     *
     * @param imageUrl 图片URL
     * @return 处理后的阿里云图片地址
     */
    private String processImageUrl(String imageUrl) {
        try {
            log.info("开始处理单个图片URL: {}", imageUrl);

            // 验证URL格式
            if (!isValidImageUrl(imageUrl)) {
                log.error("无效的图片URL: {}", imageUrl);
                return null;
            }

            // 下载图片
            byte[] imageData = downloadImage(imageUrl);
            if (imageData == null) {
                log.error("下载图片失败，URL: {}", imageUrl);
                return null;
            }

            // 上传到阿里云
            String aliYunUrl = uploadToAliYun(imageData);
            if (StringUtils.isEmpty(aliYunUrl)) {
                log.error("上传图片到阿里云失败，URL: {}", imageUrl);
                return null;
            }

            log.info("图片URL处理成功: {} -> {}", imageUrl, aliYunUrl);
            return aliYunUrl;

        } catch (Exception e) {
            log.error("处理图片URL异常: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 验证图片URL格式
     */
    private boolean isValidImageUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        // 基本URL格式验证
        String urlPattern = "^https?://.*\\.(jpg|jpeg|png|gif|bmp|webp)$";
        return Pattern.compile(urlPattern, Pattern.CASE_INSENSITIVE).matcher(url).matches();
    }

    /**
     * 下载图片
     */
    private byte[] downloadImage(String imageUrl) {
        try {
            if (!isValidImageUrl(imageUrl)) {
                log.error("下载前URL验证失败: {}", imageUrl);
                return null;
            }

            return HttpUtil.downloadBytes(imageUrl);
        } catch (Exception e) {
            log.error("下载图片失败，URL: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 上传图片到阿里云
     */
    private String uploadToAliYun(byte[] imageData) {
        try {
            ByteArrayResource resource = new ByteArrayResource(imageData);
            return aliYunFileService.upload(resource);
        } catch (Exception e) {
            log.error("上传图片到阿里云失败", e);
            return null;
        }
    }
}