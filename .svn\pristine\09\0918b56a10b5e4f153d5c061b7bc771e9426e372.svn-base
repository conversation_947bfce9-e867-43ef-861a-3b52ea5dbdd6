package com.dataxai.web.controller.front;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.TeamUser;
import com.dataxai.domain.TitleExtractionTask;
import com.dataxai.domain.TitleExtractionTaskDetail;
import com.dataxai.domain.ProductInfo;
import com.dataxai.domain.dto.TitleExtractionTaskDetailDTO;
import com.dataxai.mapper.TitleExtractionTaskDetailMapper;
import com.dataxai.service.ITeamUserService;
import com.dataxai.service.ITitleExtractionTaskService;
import com.dataxai.service.ITitleExtractionTaskDetailService;
import com.dataxai.service.IProductInfoService;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.service.IOrdinalImgResultService;
import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.web.task.core.TaskScoreService;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 标题提取任务表Controller
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "标题提取任务")
@RestController
@RequestMapping("/title/extraction/task")
public class TitleExtractionTaskController extends BaseController
{
    @Autowired
    private ITitleExtractionTaskService titleExtractionTaskService;

    @Autowired
    private ITitleExtractionTaskDetailService titleExtractionTaskDetailService;

    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private IOrdinalImgResultService ordinalImgResultService;

    @Autowired
    private RiskDetectionImageUploadService riskDetectionImageUploadService;

    @Autowired
    private TaskScoreService taskScoreService;

    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;
    @Autowired
    private TitleExtractionTaskDetailMapper titleExtractionTaskDetailMapper;

    /**
     * 查询标题提取任务表列表
     */
    @ApiOperation("查询标题提取任务列表")
    @GetMapping("/list")
    public AjaxResult list(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "batchNumber", required = false) String batchNumber,
            @RequestParam(value = "userId", required = false)Long userId,
            @RequestParam(value = "remark", required = false) String remark

    )
    {
        // 构造查询对象
        TitleExtractionTask query = new TitleExtractionTask();

        // 根据用户模式设置数据过滤条件
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            Long LoginUserId = SecurityUtils.getUserId();

            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(LoginUserId);
            if (teamUser.getIsAdmin() == true){
                Long selectUserId = userId;
                if (selectUserId != null ) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(selectUserId);
                }else{
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                }
            }else{
                query.setTeamId(userTeamInfo.getTeamId());
                query.setOwnerId(LoginUserId);
            }
        } else {
            // 个人模式：通过owner_id过滤，且team_id为0
            query.setOwnerId(getUserId());
            query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
        }

        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setStatus(status);
        query.setTaskBatch(batchNumber);
        query.setRemark(remark);
        startPage();
        List<TitleExtractionTask> list = titleExtractionTaskService.selectTitleExtractionTaskList(query);

        // 组装自定义分页结构
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new com.github.pagehelper.PageInfo<>(list).getTotal());
        pageData.put("data", list);
        PageInfo<TitleExtractionTask> pageInfo = new PageInfo<>(list);
        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 获取标题提取任务基本信息（包含备注）
     */
    @ApiOperation("获取标题提取任务基本信息")
    @GetMapping(value = "/basic/{id}")
    public AjaxResult getBasicInfo(@PathVariable("id") Long id) {
        TitleExtractionTask titleExtractionTask = titleExtractionTaskService.selectTitleExtractionTaskById(id);
        if (titleExtractionTask == null) {
            return AjaxResult.error("标题提取任务不存在");
        }

        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (!userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() == null){
            if (!titleExtractionTask.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限查看此标题提取任务");
            }
        }

        return AjaxResult.success(titleExtractionTask);
    }

    /**
     * 获取标题提取任务表详细信息
     */
    @ApiOperation("获取标题提取任务详情，支持分页查询")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id,
                              @RequestParam(value = "processStatus", required = false) String processStatus)
    {
        TitleExtractionTask titleExtractionTask = titleExtractionTaskService.selectTitleExtractionTaskById(id);
        if (titleExtractionTask == null) {
            return AjaxResult.error("标题提取任务不存在");
        }
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();

        if (!userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() == null){
            if (!titleExtractionTask.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限查看此标题提取任务");
            }
        }

        // 查询所有详情，支持分页
        startPage();
        List<TitleExtractionTaskDetail> detailList = titleExtractionTaskDetailMapper.selectTitleExtractionTaskDetailByTaskId(id);
        PageInfo<TitleExtractionTaskDetail> pageInfo = new PageInfo<>(detailList);
        // 组装详细信息DTO
        TitleExtractionTaskDetailDTO detailDTO = new TitleExtractionTaskDetailDTO();
        // 复制任务基本信息
        detailDTO.setId(titleExtractionTask.getId());
        detailDTO.setTaskBatch(titleExtractionTask.getTaskBatch());
        detailDTO.setTotalAmount(titleExtractionTask.getTotalAmount());
        detailDTO.setSuccessAmount(titleExtractionTask.getSuccessAmount());
        detailDTO.setFailAmount(titleExtractionTask.getFailAmount());
        detailDTO.setStatus(titleExtractionTask.getStatus());
        detailDTO.setOwnerId(titleExtractionTask.getOwnerId());
        detailDTO.setCreateTime(titleExtractionTask.getCreateTime());
        detailDTO.setUpdateTime(titleExtractionTask.getUpdateTime());
        detailDTO.setRemark(titleExtractionTask.getRemark());
        detailDTO.setTotal(Long.valueOf(pageInfo.getTotal()).intValue());
        detailDTO.setPageNum(pageInfo.getPageNum());
        detailDTO.setPageSize(pageInfo.getPageSize());
        detailDTO.setPages(Long.valueOf(pageInfo.getPages()).intValue());
        detailDTO.setDetailList(detailList);

        return AjaxResult.success(detailDTO);
    }

    /**
     * 通过图片文件创建标题提取任务
     */
    @ApiOperation("通过图片文件创建标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByImages")
    public AjaxResult createByImages(@RequestParam(value = "files", required = false) List<MultipartFile> files,
                                   @RequestParam(value = "files[]", required = false) List<MultipartFile> filesArray,
                                   @RequestParam(value = "type", required = false, defaultValue = "1") Integer type)
    {
        // 优先使用files，如果为空则使用files[]
        List<MultipartFile> fileList = files;
        if (fileList == null || fileList.isEmpty()) {
            fileList = filesArray;
        }

        if (fileList == null || fileList.isEmpty()) {
            return AjaxResult.error("图片文件不能为空");
        }

        try {
            logger.info("开始通过图片文件创建标题提取任务，文件数量: {}", fileList.size());
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "开始创建标题提取任务，文件数量: {}, 用户ID: {}",
                fileList.size(), getUserId());

            // 1. 扣除积分（每个详情扣1分）
            Long totalPoints = (long) fileList.size();
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, Constants.TASK_TYPE_TITLE_EXTRACT);
            if (!deductioned) {
                TaskLogUtils.warn(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "积分不足，无法创建标题提取任务，用户ID: {}", getUserId());
                return AjaxResult.error("积分不足，无法创建标题提取任务");
            }

            // 2. 上传图片到阿里云
            logger.info("开始上传图片到阿里云...");
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "开始上传图片到阿里云，文件数量: {}", fileList.size());
            List<String> imageUrls = riskDetectionImageUploadService.uploadRiskImages(fileList);
            logger.info("图片上传完成，成功上传 {} 个文件", imageUrls.size());
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "图片上传完成，成功上传 {} 个文件", imageUrls.size());

            if (imageUrls.isEmpty()) {
                logger.error("没有图片上传成功，无法创建标题提取任务");
                TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "没有图片上传成功，无法创建标题提取任务，用户ID: {}", getUserId());
                return AjaxResult.error("图片上传失败，无法创建标题提取任务");
            }

            // 3. 创建标题提取任务
            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(getUserId());
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            task.setType(type); // 设置配置类型
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());
            }
            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) {
                return AjaxResult.error("创建标题提取任务失败");
            }

            // 4. 创建标题提取任务详情
            List<TitleExtractionTaskDetail> detailList = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrl);
                detail.setType(3); // 3-来源为直接上传的图片
                detail.setTypeId(null); // 直接上传的图片，type_id为空
                detail.setProcessStatus(1); // 待处理
                detail.setOwnerId(getUserId());
                detail.setHasUploaded(false); // 设置默认值
                detailList.add(detail);
            }

            int detailResult = titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(detailList);

            if (detailResult <= 0) {
                logger.error("批量插入标题提取任务详情失败");
                return AjaxResult.error("创建标题提取任务失败：数据插入异常");
            }

            // 5. 添加标题提取任务到通用队列
            int successQueueCount = 0;
            for (TitleExtractionTaskDetail detail : detailList) {
                if (detail.getId() == null) {
                    logger.warn("任务详情ID为空，跳过入队，图片URL: {}", detail.getImageUrl());
                    continue;
                }
                try {
                    successQueueCount++;
                } catch (Exception e) {
                    logger.error("添加标题提取任务到队列失败，任务详情ID: {}, 图片URL: {}", detail.getId(), detail.getImageUrl(), e);
                    // 将失败的任务详情标记为处理失败
                    markTaskDetailAsFailed(detail.getId());
                }
            }

            logger.info("标题提取任务入队完成，成功入队: {}, 总数: {}", successQueueCount, detailList.size());
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "标题提取任务入队完成，成功入队: {}, 总数: {}, 任务ID: {}",
                successQueueCount, detailList.size(), task.getId());

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("successDetails", detailResult);

            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "标题提取任务创建成功，任务ID: {}, 用户ID: {}",
                task.getId(), getUserId());

            return AjaxResult.success("创建标题提取任务成功", result);

        } catch (Exception e) {
            logger.error("通过图片文件创建标题提取任务失败", e);
            TaskLogUtils.error(TaskLogUtils.TASK_TYPE_TITLE_EXTRACT, "标题提取任务创建失败，用户ID: {}", getUserId(), e);
            return AjaxResult.error("创建标题提取任务失败：" + e.getMessage());
        }
    }

    /**
     * 通过图片链接创建标题提取任务
     */
    @ApiOperation("通过图片链接创建标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByImageUrls")
    public AjaxResult createByImageUrls(@RequestBody Map<String, Object> requestData)
    {
        List<String> imageUrls = new ArrayList<>();
        Integer type = 1; // 默认值

        // 获取type参数
        if (requestData.containsKey("type")) {
            Object typeObj = requestData.get("type");
            if (typeObj != null) {
                type = Integer.valueOf(typeObj.toString());
            }
        }

        // 获取imageUrls参数
        Object urlsObj = requestData.get("imageUrls");
        if (urlsObj instanceof List) {
            List<?> list = (List<?>) urlsObj;
            for (Object item : list) {
                if (item instanceof String) {
                    imageUrls.add((String) item);
                } else if (item != null) {
                    imageUrls.add(item.toString());
                }
            }
        }

        if (imageUrls.isEmpty()) {
            return AjaxResult.error("图片链接列表不能为空");
        }

        try {
            logger.info("开始通过图片链接创建标题提取任务，链接数量: {}", imageUrls.size());

            // 过滤有效的图片链接
            List<String> validImageUrls = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    validImageUrls.add(imageUrl.trim());
                }
            }

            if (validImageUrls.isEmpty()) {
                logger.error("没有有效的图片链接，无法创建标题提取任务");
                return AjaxResult.error("没有有效的图片链接，无法创建标题提取任务");
            }

            // 扣除积分（每个详情扣1分）
            Long totalPoints = (long) validImageUrls.size();
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, Constants.TASK_TYPE_TITLE_EXTRACT);
            if (!deductioned) {
                return AjaxResult.error("积分不足，无法创建标题提取任务");
            }

            // 创建标题提取任务
            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(getUserId());
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(validImageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            task.setType(type); // 设置配置类型
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());

            }
            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) {
                return AjaxResult.error("创建标题提取任务失败");
            }

            // 创建标题提取任务详情
            List<TitleExtractionTaskDetail> detailList = new ArrayList<>();
            for (String imageUrl : validImageUrls) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrl);
                detail.setType(3); // 3-来源为直接上传的图片
                detail.setTypeId(null); // 直接上传的图片，type_id为空
                detail.setProcessStatus(1); // 待处理
                detail.setOwnerId(getUserId());
                detail.setHasUploaded(false); // 设置默认值
                detailList.add(detail);
            }

            int detailResult = titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(detailList);

            if (detailResult <= 0) {
                logger.error("批量插入标题提取任务详情失败（URL方式）");
                return AjaxResult.error("创建标题提取任务失败：数据插入异常");
            }

            // 添加标题提取任务到通用队列
            int successQueueCount = 0;
            for (TitleExtractionTaskDetail detail : detailList) {
                if (detail.getId() == null) {
                    logger.warn("任务详情ID为空，跳过入队，图片URL: {}", detail.getImageUrl());
                    continue;
                }
                try {
                    successQueueCount++;
                } catch (Exception e) {
                    logger.error("添加标题提取任务到队列失败，任务详情ID: {}, 图片URL: {}", detail.getId(), detail.getImageUrl(), e);
                    // 将失败的任务详情标记为处理失败
                    markTaskDetailAsFailed(detail.getId());
                }
            }

            logger.info("标题提取任务入队完成（URL方式），成功入队: {}, 总数: {}", successQueueCount, detailList.size());

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", validImageUrls.size());
            result.put("successDetails", detailResult);

            return AjaxResult.success("创建标题提取任务成功", result);

        } catch (Exception e) {
            logger.error("通过图片链接创建标题提取任务失败", e);
            return AjaxResult.error("创建标题提取任务失败：" + e.getMessage());
        }
    }

    /**
     * 通过产品信息ID创建标题提取任务
     */
    @ApiOperation("通过产品信息ID创建标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByProductIds")
    public AjaxResult createByProductIds(@RequestBody Map<String, Object> requestData)
    {
        // 获取任务配置type参数
        Integer taskType = 1; // 默认值
        if (requestData.containsKey("type")) {
            Object typeObj = requestData.get("type");
            if (typeObj != null) {
                taskType = Integer.valueOf(typeObj.toString());
            }
        }

        // 获取productList参数
        Object productListObj = requestData.get("productList");
        if (!(productListObj instanceof List)) {
            return AjaxResult.error("产品信息参数列表不能为空");
        }

        List<Map<String, Object>> productList = (List<Map<String, Object>>) productListObj;
        if (productList == null || productList.isEmpty()) {
            return AjaxResult.error("产品信息参数列表不能为空");
        }

        try {
            List<String> imageUrls = new ArrayList<>();
            List<Long> validIds = new ArrayList<>();
            List<Integer> typeList = new ArrayList<>();
            List<Long> typeIdList = new ArrayList<>();

            // 1. 根据type不同，查询不同的表获取图片地址
            for (Map<String, Object> item : productList) {
                Object idObj = item.get("id");
                Object typeObj = item.get("type");

                if (idObj == null || typeObj == null) {
                    continue;
                }

                Long id = Long.valueOf(idObj.toString());
                Integer type = Integer.valueOf(typeObj.toString());

                String imageUrl = null;
                if (type == 1) {
                    // 从t_ordinal_img_result表查询
                    OrdinalImgResult ordinalImgResult = ordinalImgResultService.selectOrdinalImgResultByImageId(id.toString());
                    if (ordinalImgResult != null && ordinalImgResult.getUserId().equals(getUserId())) {
                        String resImgUrl = ordinalImgResult.getResImgUrl();
                        if (resImgUrl != null && !resImgUrl.isEmpty()) {
                            // 使用CommonUtils.addCosPrefix添加域名前缀
                            imageUrl = CommonUtils.addCosPrefix(resImgUrl);
                        }
                    }
                } else if (type == 2) {
                    // 从product_info表查询
                    ProductInfo productInfo = productInfoService.selectProductInfoById(id);
                    if (productInfo != null ) {
                        imageUrl = productInfo.getProductImageUrl();
                    }
                }

                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    imageUrls.add(imageUrl);
                    validIds.add(id);
                    typeList.add(type);
                    typeIdList.add(id);
                }
            }

            if (imageUrls.isEmpty()) {
                return AjaxResult.error("没有找到有效的图片地址");
            }

            Long totalPoints = (long) imageUrls.size();
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, Constants.TASK_TYPE_TITLE_EXTRACT);
            if (!deductioned) {
                return AjaxResult.error("积分不足，无法创建标题提取任务");
            }

            // 2. 创建标题提取任务
            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(getUserId());
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            task.setType(taskType); // 设置配置类型
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());
            }

            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) {
                return AjaxResult.error("创建标题提取任务失败");
            }

            // 3. 创建标题提取任务详情
            List<TitleExtractionTaskDetail> detailList = new ArrayList<>();
            for (int i = 0; i < imageUrls.size(); i++) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrls.get(i));
                detail.setType(typeList.get(i));
                detail.setTypeId(typeIdList.get(i));
                detail.setProcessStatus(1); // 待处理
                detail.setOwnerId(getUserId());
                detail.setHasUploaded(false); // 设置默认值
                detailList.add(detail);
            }

            int detailResult = titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(detailList);

            if (detailResult <= 0) {
                logger.error("批量插入标题提取任务详情失败（产品ID方式）");
                return AjaxResult.error("创建标题提取任务失败：数据插入异常");
            }

            // 添加标题提取任务到通用队列
            int successQueueCount = 0;
            for (TitleExtractionTaskDetail detail : detailList) {
                if (detail.getId() == null) {
                    logger.warn("任务详情ID为空，跳过入队，图片URL: {}", detail.getImageUrl());
                    continue;
                }
                try {
                    successQueueCount++;
                } catch (Exception e) {
                    logger.error("添加标题提取任务到队列失败，任务详情ID: {}, 图片URL: {}", detail.getId(), detail.getImageUrl(), e);
                    // 将失败的任务详情标记为处理失败
                    markTaskDetailAsFailed(detail.getId());
                }
            }

            logger.info("标题提取任务入队完成（产品ID方式），成功入队: {}, 总数: {}", successQueueCount, detailList.size());

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("validIds", validIds.size());
            result.put("successDetails", detailResult);

            return AjaxResult.success("创建标题提取任务成功", result);

        } catch (Exception e) {
            logger.error("通过产品信息ID创建标题提取任务失败", e);
            return AjaxResult.error("创建标题提取任务失败：" + e.getMessage());
        }
    }

    /**
     * 新增标题提取任务表
     */
    @ApiOperation("新增标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TitleExtractionTask titleExtractionTask)
    {
        // 设置用户ID
        titleExtractionTask.setOwnerId(getUserId());
        // 生成任务批次号
        titleExtractionTask.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
        // 设置初始状态
        titleExtractionTask.setStatus(1);
        // 设置初始数量
        if (titleExtractionTask.getTotalAmount() == null) {
            titleExtractionTask.setTotalAmount(0);
        }
        if (titleExtractionTask.getSuccessAmount() == null) {
            titleExtractionTask.setSuccessAmount(0);
        }
        if (titleExtractionTask.getFailAmount() == null) {
            titleExtractionTask.setFailAmount(0);
        }

        return toAjax(titleExtractionTaskService.insertTitleExtractionTask(titleExtractionTask));
    }

    /**
     * 修改标题提取任务表
     */
    @ApiOperation("修改标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TitleExtractionTask titleExtractionTask)
    {
        // 验证任务是否属于当前用户
        TitleExtractionTask existingTask = titleExtractionTaskService.selectTitleExtractionTaskById(titleExtractionTask.getId());
        if (existingTask == null) {
            return AjaxResult.error("标题提取任务不存在");
        }
        if (!existingTask.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限修改此标题提取任务");
        }

        // 设置用户ID（确保不被篡改）
        titleExtractionTask.setOwnerId(getUserId());

        return toAjax(titleExtractionTaskService.updateTitleExtractionTask(titleExtractionTask));
    }

    /**
     * 删除标题提取任务表
     */
    @ApiOperation("删除标题提取任务")
    @Log(title = "标题提取任务表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 验证所有任务都属于当前用户
        for (Long id : ids) {
            TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(id);
            if (task == null) {
                return AjaxResult.error("标题提取任务不存在，ID: " + id);
            }
            if (!task.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限删除此标题提取任务，ID: " + id);
            }
        }

        return toAjax(titleExtractionTaskService.deleteTitleExtractionTaskByIds(ids));
    }

    /**
     * 将任务详情标记为处理失败
     */
    private void markTaskDetailAsFailed(Long taskDetailId) {
        try {
            if (taskDetailId == null) {
                return;
            }
            TitleExtractionTaskDetail updateDetail = new TitleExtractionTaskDetail();
            updateDetail.setId(taskDetailId);
            updateDetail.setProcessStatus(2); // 已处理（但是失败）
            titleExtractionTaskDetailService.updateTitleExtractionTaskDetail(updateDetail);

            // 获取任务详情以更新主任务统计
            TitleExtractionTaskDetail detail = titleExtractionTaskDetailService.selectTitleExtractionTaskDetailById(taskDetailId);
            if (detail != null && detail.getTaskId() != null) {
                updateTaskStatisticsOnFailure(detail.getTaskId());
            }
        } catch (Exception e) {
            logger.error("标记任务详情失败异常，任务详情ID: {}", taskDetailId, e);
        }
    }

    /**
     * 更新任务统计（失败情况）
     */
    private void updateTaskStatisticsOnFailure(Long taskId) {
        try {
            if (taskId == null) {
                return;
            }

            // 查询当前任务的所有详情
            java.util.List<TitleExtractionTaskDetail> detailList = titleExtractionTaskDetailService.selectTitleExtractionTaskDetailByTaskId(taskId);

            int successCount = 0;
            int failCount = 0;
            for (TitleExtractionTaskDetail detail : detailList) {
                if (detail.getProcessStatus() != null && detail.getProcessStatus() == 2) {
                    // 需要根据实际结果判断成功还是失败，这里简化为失败
                    if (detail.getTemuProductTitle() != null && !detail.getTemuProductTitle().trim().isEmpty()) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                }
            }

            // 更新主任务统计
            TitleExtractionTask updateTask = new TitleExtractionTask();
            updateTask.setId(taskId);
            updateTask.setSuccessAmount(successCount);
            updateTask.setFailAmount(failCount);

            // 判断任务状态
            if (successCount + failCount >= detailList.size()) {
                updateTask.setStatus(3); // 执行完成
            } else {
                updateTask.setStatus(2); // 执行中
            }

            titleExtractionTaskService.updateTitleExtractionTask(updateTask);

        } catch (Exception e) {
            logger.error("更新任务统计失败，任务ID: {}", taskId, e);
        }
    }
}