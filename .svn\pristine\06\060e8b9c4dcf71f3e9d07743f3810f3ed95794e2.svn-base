/**
 * @Author: xujing
 * @Date: 2025/7/23
 * @Description: ""
 */
import { useMemo, useEffect, useState, useContext } from 'react';
import { Dropdown, Tabs, Modal, Input, Button, Select, message, Empty } from 'antd';
import { RightOutlined, MoreOutlined, FolderOpenOutlined } from '@ant-design/icons';
import { getWorkflowTemplateList, getTemplateGroupList, addTemplateGroup, editTemplateGroup, delTemplateGroup, copyWorkflowTemplate, moveWorkflowTemplate, delWorkflowTemplate } from '@/api/task'
import { useNavigate } from 'react-router-dom';
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getTeamMembers } from '@/api/task'
import { getUserInfo } from '@/api/common'
const { Option } = Select;


export const WorkflowTemplate = () => {
    const navigate = useNavigate();

    const [modal, contextHolder] = Modal.useModal();
    const [messageApi, messageContextHolder] = message.useMessage();

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [templateName, setTemplateName] = useState('');
    const [groups, setGroups] = useState<any[]>([]);
    const [activeTab, setActiveTab] = useState({ id: 0, groupName: "全部" });
    const [modalVisible, setModalVisible] = useState(false);
    const [groupName, setGroupName] = useState('');
    const [editingGroupId, setEditingGroupId] = useState('');
	const [userId, setUserId] = useState<string>('');
	const [selectedTeam, setSelectedTeam] = useState<Array <any> | null>([]);

    const allNodeList = [
        {
            taskType: 8,
            label: '文生图'
        },
        {
            taskType: 9,
            label: '相似图裂变'
        },
        {
            taskType: 6,
            label: '平铺图-文生图'
        },
        {
            taskType: 7,
            label: '平铺图-图生图'
        },
        {
            taskType: 14,
            label: '图案裁剪'
        },
        {
            taskType: 11,
            label: '图片去背景'
        },
        {
            taskType: 12,
            label: '图片变清晰'
        },
        {
            taskType: 52,
            label: '印花图提取'
        },
        {
            taskType: 17,
            label: '侵权风险过滤'
        },
        {
            taskType: 18,
            label: '标题提取'
        },
    ]

    // 工作流模板列表
    const [workflowTemplateList, setWorkflowTemplateList] = useState<any[]>([])

	//获取团队id
	useEffect(() => { 
		getUserInfo().then(res => { 

			getTeamMembers({ teamId: res.teamId }).then((res:any) => { 
				
				setSelectedTeam(res.data)
			});
		});
	}, []);
    //获取工作流模板列表
    const fetchWorkflowTemplateList = (
        pageNum: number,
        pageSize: number,
        groupId: any,
        templateName: string,
        userId: string,
    ) => {
        getWorkflowTemplateList({
            pageNum,
            pageSize,
            groupId: groupId == '0' ? '' : groupId,
            templateName,
            userId
        }).then((res: any) => {
            if (res.data) {
                setWorkflowTemplateList(res.data)
            }
        }).catch(err => {
            messageApi.error(`获取工作流模板列表失败:${err?.msg || err?.data?.msg || ''}`)
        })

    }
    //获取分组列表
    const fetchGroupListData = (groupName: string) => {
        getTemplateGroupList({ pageNum: 1, pageSize: 99, groupName }).then((res: any) => {
            setGroups([{ id: 0, groupName: "全部" }, ...res.data]);
        }).catch(err => {
            messageApi.error(`获取分组列表失败:${err?.msg || err?.data?.msg || ''}`)
        })
    }

    useEffect(() => {
        if (userInfo?.currentMode == 2) {
            fetchGroupListData('')
            handleSearch(1, 999)
        }
    }, [userInfo?.currentMode]);
    // 查询处理函数
    const handleSearch = (pageNum: number, pageSize: number) => {
        fetchWorkflowTemplateList(pageNum, pageSize, activeTab?.id, templateName,userId)
    }


    useEffect(() => {
        const params: any = sessionStorage.getItem('workflowParams');
        const parsedParams = JSON.parse(params);
        if (parsedParams?.from == 'workflow') {
            setCreateTemplateModal(true)
            setFormTemplateName('')
            setSelectedGroup({})
            // 使用完清掉
            sessionStorage.removeItem('workflowParams');
        }

    }, []);

    // 新建/编辑分组
    const handleGroupAction = () => {
        const name = groupName.trim();
        if (!name) return messageApi.warning('请输入分组名称');

        if (editingGroupId) {
            editTemplateGroup({ id: editingGroupId, groupName: name }).then(res => {
                messageApi.success('分组重命名成功')
                fetchGroupListData('') // 添加成功后刷新分组列表
            }).catch(err => {
                messageApi.error(`分组重命名失败:${err?.msg || err?.data?.msg || ''}`)
            })
        } else {
            addTemplateGroup({ groupName: name }).then(res => {
                messageApi.success('分组创建成功')
                fetchGroupListData('') // 添加成功后刷新分组列表
            }).catch(err => {
                messageApi.error(`分组创建失败:${err?.msg || err?.data?.msg || ''}`)
            })
        }

        setModalVisible(false);
        setGroupName('');
        setEditingGroupId('');
    };

    const handleDelete = (group: any) => {
        modal.confirm({
            title: '确认删除分组',
            content: `确定要删除分组 "${group?.groupName}" 吗？`,
            centered: true,
            onOk: () => {
                delTemplateGroup({ id: group?.id }).then(res => {
                    messageApi.success('分组删除成功')
                    fetchGroupListData('') // 添加成功后刷新分组列表
                    if (group?.id == activeTab?.id) {
                        setActiveTab(groups[0])
                    }
                }).catch(err => {
                    messageApi.error(`分组删除失败:${err?.msg || err?.data?.msg || ''}`)
                })
            }
        });
    };

    const tabItems = groups.map((group, index) => ({
        label: (
            <div className='flex items-center group' >
                {group?.groupName}
                <div onClick={(e) => e.stopPropagation()}>
                    <Dropdown
                        menu={{
                            items: [
                                {
                                    key: 'rename', label: '重命名', onClick: () => {
                                        setGroupName(group?.groupName);
                                        setEditingGroupId(group?.id);
                                        setModalVisible(true);
                                    }
                                },
                                { key: 'delete', label: '删除', onClick: () => handleDelete(group) }
                            ]
                        }}
                        placement="bottomLeft"
                    >
                        {group?.id != 0 && (
                            <div className="relative ml-[2px] h-[20px] w-[10px] " >
                                {/* <div className="relative ml-[2px] h-[20px] w-[20px]" > */}
                                {/* <p className='absolute text-gray-500 top-[-1px] right-0 opacity-100 group-hover:opacity-0 transition-opacity'>0</p> */}
                                <MoreOutlined className='absolute top-[3px] left-0 opacity-0 group-hover:opacity-100 transition-opacity' />
                            </div>
                        )}
                    </Dropdown>
                </div>
            </div>
        ),
        key: group?.id?.toString()
    }));
    // 编辑工作流模板
    const editTemplate = (item: any) => {
        navigate('/workspace/teamTools/workflowTemplateDetail', {
            state: { templateId: item?.id, type: 'edit' }
        });
    };
    // 删除工作流模板
    const delTemplate = (item: any) => {
        modal.confirm({
            title: '提示',
            content: `确定要删除该工作流模板 "${item?.templateName}" 吗？`,
            centered: true,
            onOk: () => {
                delWorkflowTemplate({ id: item?.id }).then(res => {
                    messageApi.success('删除成功')
                    handleSearch(1, 999,)
                }).catch(err => {
                    messageApi.error(`删除失败:${err?.msg || err?.data?.msg || ''}`)
                })
            }
        });
    };
    // 复制工作流模板
    const copyTemplate = (item: any) => {
        copyWorkflowTemplate({
            id: item?.id,
            //  newTemplateName: `${item?.templateName}-副本`
        }).then(res => {
            messageApi.success('模板复制成功')
            handleSearch(1, 999,)
        }).catch(err => {
            messageApi.error(`模板复制失败:${err?.msg || err?.data?.msg || ''}`)
        })

    };
    const [currentItem, setCurrentItem] = useState<any>({})
    // 移动工作流模板
    const moveTemplate = (item: any) => {
        if (groups.length <= 1) return messageApi.warning('请先创建分组')
        setMoveTemplateModal(true);
        setSelectedGroup({})
        setCurrentItem(item)
    };

    const [createTemplateModal, setCreateTemplateModal] = useState(false); // 新建工作流模板弹窗
    const [formTemplateName, setFormTemplateName] = useState(''); // 新建模板名称
    const [selectedGroup, setSelectedGroup] = useState<any>({}); // 选择的分组

    const handleCreateTemplate = () => {
        if (!formTemplateName) return messageApi.warning('请输入工作流模板名称');

        setCreateTemplateModal(false);
        navigate('/workspace/teamTools/workflowTemplateDetail', {
            state: { formTemplateName, groupId: selectedGroup || '', type: 'add' }
        });
    }

    const [moveTemplateModal, setMoveTemplateModal] = useState(false); // 移动到分组弹窗

    return (
        <div className='h-full w-full p-[20px]'>
            {contextHolder}
            {messageContextHolder}
            {/* 新建分组/重命名分组弹窗  start */}
            <Modal
                title={editingGroupId ? "重命名分组" : "新建分组"}
                open={modalVisible}
                onOk={handleGroupAction}
                onCancel={() => setModalVisible(false)}
                centered
            >
                <Input
                    placeholder="请输入分组名称"
                    value={groupName}
                    onChange={e => setGroupName(e.target.value)}
                />
            </Modal>
            {/* 新建分组/重命名分组弹窗  end */}
            {/* 新建工作流模板弹窗  start */}
            <Modal
                title='新建工作流模板'
                open={createTemplateModal}
                onOk={handleCreateTemplate}
                onCancel={() => {
                    setCreateTemplateModal(false);
                }}
                centered
            >
                <p className='text-[#333] mb-3'>工作流模板名称<span className='text-[red] ml-1'>*</span></p>
                <Input
                    placeholder="请输入工作流模板名称"
                    value={formTemplateName}
                    onChange={e => setFormTemplateName(e.target.value)}
                />
                <p className='text-[#333] mb-3 mt-3'>模板分组</p>
                <Select
                    options={groups.filter(group => group.id != 0).map(group => ({
                        label: group?.groupName,
                        value: group?.id
                    }))}
                    placeholder="请选择分组"
                    allowClear
                    value={selectedGroup}
                    onChange={setSelectedGroup}
                    style={{ width: '100%' }}
                />
            </Modal>
            {/* 新建工作流模板弹窗  end */}
            {/* 移动到分组弹窗  start */}
            <Modal
                title='移动到分组'
                open={moveTemplateModal}
                onOk={() => { }}
                onCancel={() => {
                    setMoveTemplateModal(false);
                }}
                footer={
                    <div className='flex justify-between items-center'>
                        <Button
                            type="link"
                            onClick={() => {
                                setModalVisible(true)
                                setGroupName('')
                                setEditingGroupId('')
                            }}
                            style={{ color: '#F06A34', height: '32px', padding: '0px', gap: '2px' }}
                        >
                            <span className='text-[22px] mb-[5px]'>+</span>创建分组
                        </Button>
                        <div>
                            <Button style={{ marginRight: 8 }}
                                onClick={() => { setMoveTemplateModal(false) }}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (currentItem?.groupId && selectedGroup?.id == currentItem?.groupId) return messageApi.warning('所选项目已在此分组里')
                                    if (!selectedGroup?.id) return messageApi.warning('请选择分组')
                                    moveWorkflowTemplate({ id: currentItem?.id, newGroupId: selectedGroup?.id }).then(res => {
                                        setMoveTemplateModal(false)
                                        messageApi.success('移动分组成功')
                                        handleSearch(1, 999,)
                                    }).catch(err => {
                                        messageApi.error(`移动分组失败:${err?.msg || err?.data?.msg || ''}`)
                                    })
                                }}
                            >
                                确定
                            </Button>
                        </div>
                    </div>
                }

                centered
            >
                <div className='border border-[#D8D8D8] rounded-lg mt-4 p-[2px] h-[220px] relative overflow-hidden'>
                    <div className={`h-[220px]  overflow-y-scroll scrollbar-container scrollbar-hide ${currentItem?.groupId && selectedGroup?.id == currentItem?.groupId ? 'pb-10' : 'pb-2'}`}>
                        <p className='font-bold ml-4 mt-3'>全部</p>
                        {groups.filter(group => group.id != 0).map((item: any, index) => (
                            <div key={index}
                                className={`pl-10 h-[30px] rounded-md flex items-center cursor-pointer ${selectedGroup?.id === item?.id ? 'bg-gray-100 text-[#F06A34]' : 'hover:text-[#F06A34]'}`}
                                onClick={() => setSelectedGroup(item)}
                            >
                                <FolderOpenOutlined style={{ marginRight: 6 }} />{item?.groupName}
                            </div>
                        ))}
                    </div>
                    {currentItem?.groupId && selectedGroup?.id == currentItem?.groupId && <div className='text-center text-[12px] absolute h-[30px] pt-1 rounded-md w-[456px] bg-[#F06A34] bottom-1 text-[#fff]'>所选项目已在此分组里</div>}

                </div>
            </Modal>
            {/* 移动到分组弹窗  end */}
            <div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
                <Button type="primary" onClick={() => {
                    setCreateTemplateModal(true)
                    setFormTemplateName('')
                    setSelectedGroup({})
                }}>
                    新建工作流模板
                </Button>
                <div className="flex items-center gap-2">
                    <Input
                        style={{ width: '150px' }}
                        value={templateName}
                        onChange={(e) => setTemplateName(e.target.value)}
                        placeholder="模板名称"
                    />
                    <Select
                        placeholder="请选择要查看的账号"
                        style={{ width: 200 }}
                        value={userId || undefined}
                        onChange={(value) => setUserId(value)}
                        allowClear
                        showSearch
                    >
                        {selectedTeam && selectedTeam.map((item: any) => (
                            <Option key={item.phonenumber} value={item.userId}>
                                {item.nickname===""?item.phonenumber:item.nickname}
                            </Option>
                        ))}
                    </Select>
                    <Button
                        type="primary"
                        onClick={() => { handleSearch(1, 999) }}
                    >
                        查询
                    </Button>
                </div>
            </div>
            <div className='w-full'>
                <div className='flex'>
                    <Tabs
                        activeKey={activeTab?.id?.toString()}
                        onChange={(key: any) => {
                            const selectedGroup = groups.find(g => g.id.toString() === key);
                            setActiveTab(selectedGroup);
                            // 根据选中的分组ID查询数据
                            fetchWorkflowTemplateList(1, 999, key, templateName,userId);
                        }}
                        items={tabItems}
                        style={{ maxWidth: 'calc(100vw - 258px)' }}
                    />
                    <Button
                        type="link"
                        onClick={() => {
                            setModalVisible(true)
                            setGroupName('')
                            setEditingGroupId('')
                        }}
                        style={{ color: '#F06A34', height: '32px', padding: '0px', gap: '2px', margin: '7px 0 0 10px' }}
                    >
                        <span className='text-[22px] mb-[5px]'>+</span>创建分组
                    </Button>
                </div>
                {workflowTemplateList.length > 0 ? <div className='grid  grid-cols-1  md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xxl:grid-cols-5 xxxl:grid-cols-6 gap-4'>
                    {workflowTemplateList.map((item, index) => (
                        <div key={index} className='p-4 rounded-lg border group' >
                            <div className='flex justify-between items-center  mb-1'>
                                <p className='text-gray-500'>{item?.templateName}</p>
                                <Dropdown
                                    menu={{
                                        items: [
                                            { key: 'edit', label: '编辑', onClick: () => editTemplate(item) },
                                            { key: 'delete', label: '删除', onClick: () => delTemplate(item) },
                                            { key: 'copy', label: '复制', onClick: () => copyTemplate(item) },
                                            { key: 'move', label: '移动到', onClick: () => moveTemplate(item) },
                                        ]
                                    }}
                                    placement="bottomLeft"
                                    trigger={['hover']}
                                >
                                    <div className="group relative">
                                        <MoreOutlined
                                            className='opacity-0 group-hover:opacity-100 group-[.ant-dropdown-open]:opacity-100 transition-opacity p-[2px] bg-[#F5F5F5]'
                                            style={{ transform: 'rotate(90deg)' }}
                                        />
                                    </div>
                                </Dropdown>
                            </div>
                            <div className="flex flex-wrap gap-1 min-h-[64px] pt-1">
                                {item?.nodeList.map((node: any, inx: number) => (
                                    <div key={index} className="flex items-center">
                                        <span className='border border-primary text-primary pl-2 pr-2 rounded-[4px]'>
                                            {node?.taskType ? allNodeList.find(n => n.taskType === node.taskType)?.label : '未知'}
                                        </span>
                                        {inx < item?.nodeList.length - 1 && (<RightOutlined className='text-[9px] text-gray-500 ml-1' />)}
                                    </div>
                                ))}
                            </div>
                        </div>))}
                </div> : <Empty className='mt-10' description="暂无模板，快去新增吧～" />}

            </div>
        </div>

    );
};

export default WorkflowTemplate;




