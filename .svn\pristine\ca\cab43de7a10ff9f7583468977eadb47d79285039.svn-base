package com.dataxai.web.controller.front;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.domain.WorkflowTemplateNode;
import com.dataxai.service.IWorkflowTemplateNodeService;
import com.dataxai.service.IWorkflowTemplateService;
import com.dataxai.domain.TUser;
import com.dataxai.domain.WorkflowTemplate;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.poi.ExcelUtil;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作流模板节点表Controller
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@RestController
@RequestMapping("/workflow/templateNode")
public class WorkflowTemplateNodeController extends BaseController
{
    @Autowired
    private IWorkflowTemplateNodeService workflowTemplateNodeService;

    @Autowired
    private IWorkflowTemplateService workflowTemplateService;

    @Autowired
    private TUserMapper tUserMapper;

    /**
     * 获取当前用户信息（包含团队ID和模式）
     */
    private TUser getCurrentUserInfo() {
        Long userId = WorkflowSecurityUtils.getCurrentUserId();
        return tUserMapper.selectTUserById(userId);
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId() {
        TUser currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());

        Long teamId = currentUser.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 查询工作流模板节点表列表
     */
    @GetMapping("/list")
    public AjaxResult list(WorkflowTemplateNode workflowTemplateNode)
    {
        // 验证团队模式
        validateAndGetTeamId();

        startPage();
        List<WorkflowTemplateNode> list = workflowTemplateNodeService.selectWorkflowTemplateNodeList(workflowTemplateNode);

        // 组装自定义分页结构
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("data", list);

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 根据模板ID查询节点列表
     */
    @GetMapping("/listByTemplateId/{templateId}")
    public AjaxResult listByTemplateId(@PathVariable("templateId") Long templateId)
    {
        // 检查模板是否存在
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(templateId);
        if (template == null) {
            return error("指定的工作流模板不存在");
        }

        // 验证团队模式和权限
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                template.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        List<WorkflowTemplateNode> list = workflowTemplateNodeService.selectByTemplateId(templateId);

        // 组装自定义分页结构（虽然不分页，但保持结构一致）
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", list.size());
        pageData.put("data", list);

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 导出工作流模板节点表列表
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkflowTemplateNode workflowTemplateNode)
    {
        List<WorkflowTemplateNode> list = workflowTemplateNodeService.selectWorkflowTemplateNodeList(workflowTemplateNode);
        ExcelUtil<WorkflowTemplateNode> util = new ExcelUtil<WorkflowTemplateNode>(WorkflowTemplateNode.class);
        util.exportExcel(response, list, "工作流模板节点表数据");
    }

    /**
     * 获取工作流模板节点表详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(workflowTemplateNodeService.selectWorkflowTemplateNodeById(id));
    }

    /**
     * 新增工作流模板节点表
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WorkflowTemplateNode workflowTemplateNode)
    {
        // 检查模板是否存在
        if (workflowTemplateNode.getTemplateId() != null) {
            if (workflowTemplateService.selectWorkflowTemplateById(workflowTemplateNode.getTemplateId()) == null) {
                return error("指定的工作流模板不存在");
            }
        }

        return toAjax(workflowTemplateNodeService.insertWorkflowTemplateNode(workflowTemplateNode));
    }

    /**
     * 批量新增工作流模板节点
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult addBatch(@RequestBody List<WorkflowTemplateNode> nodeList)
    {
        if (nodeList == null || nodeList.isEmpty()) {
            return error("节点列表不能为空");
        }

        // 检查所有节点的模板是否存在
        for (WorkflowTemplateNode node : nodeList) {
            if (node.getTemplateId() != null) {
                if (workflowTemplateService.selectWorkflowTemplateById(node.getTemplateId()) == null) {
                    return error("节点中指定的工作流模板不存在：" + node.getTemplateId());
                }
            }
        }

        return toAjax(workflowTemplateNodeService.insertWorkflowTemplateNodeBatch(nodeList));
    }

    /**
     * 修改工作流模板节点表
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WorkflowTemplateNode workflowTemplateNode)
    {
        return toAjax(workflowTemplateNodeService.updateWorkflowTemplateNode(workflowTemplateNode));
    }

    /**
     * 删除工作流模板节点表
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(workflowTemplateNodeService.deleteWorkflowTemplateNodeByIds(ids));
    }

    /**
     * 根据模板ID删除所有节点
     */
    @Log(title = "工作流模板节点表", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/{templateId}")
    public AjaxResult removeByTemplateId(@PathVariable("templateId") Long templateId)
    {
        return toAjax(workflowTemplateNodeService.deleteWorkflowTemplateNodeByTemplateId(templateId));
    }

    /**
     * 统计模板中指定任务类型的节点数量
     */
    @GetMapping("/count/{templateId}/{taskType}")
    public AjaxResult countByTemplateIdAndTaskType(@PathVariable("templateId") Long templateId,
                                                   @PathVariable("taskType") Integer taskType)
    {
        int count = workflowTemplateNodeService.countByTemplateIdAndTaskType(templateId, taskType);
        return success(count);
    }
}