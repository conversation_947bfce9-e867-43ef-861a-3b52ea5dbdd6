package com.dataxai.web.batch.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.AbstractBatchTaskFactory;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.utils.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Autowired;
import com.dataxai.web.service.MaterialHistoryService;
import com.dataxai.web.service.MaterialResolverService;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 平铺图任务工厂实现
 *
 * <p>处理平铺图任务（type=6和type=7）</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class FourTiledTaskFactory extends AbstractBatchTaskFactory {

    @Autowired
    private MaterialHistoryService materialHistoryService;

    @Autowired
    private MaterialResolverService materialResolverService;

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{
            (long) Constants.TASK_TYPE_FOUR_TEXT,
            (long) Constants.TASK_TYPE_FOUR_IMAGE
        };
    }

    @Override
    public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

        if (!CollectionUtil.isEmpty(pathList)) {
            processPathList(pathList, dto);
        }
        String originalTaskParam = dto.getTaskParam();
        String fullTaskParam = originalTaskParam;
        try {
            if (materialResolverService != null && originalTaskParam != null && !originalTaskParam.trim().isEmpty()) {
                fullTaskParam = materialResolverService.buildFullTaskParam(originalTaskParam);
            }
        } catch (Exception e) {
            log.warn("构建fullTaskParam失败，使用原始参数: {}", e.getMessage());
        }

        // 兜底：确保有图片数量，避免后续NPE
        if (dto.getImageNumber() == null) {
            try {
                if (originalTaskParam != null && !originalTaskParam.trim().isEmpty()) {
                    JSONObject obj = new JSONObject(originalTaskParam);
                    if (obj.containsKey("imageNumber")) {
                        dto.setImageNumber(Long.valueOf(obj.getInt("imageNumber")));
                    } else {
                        dto.setImageNumber(4L);
                    }
                } else {
                    dto.setImageNumber(4L);
                }
            } catch (Exception ex) {
                dto.setImageNumber(4L);
            }
        }

        String taskParam = buildTaskParam(dto, dto.getType());

        List<Task> taskList = new ArrayList<>();
        List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getTaskParam())) {
                param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
            }

            if (dto.getType() == Constants.TASK_TYPE_FOUR_TEXT && tableBytes != null) {
                List<PromptDTO> promptDTOS = PromptDTO.parseTableBytes(tableBytes);
                for (PromptDTO promptDTO : promptDTOS) {
                    Task task = createBaseTask(dto, userId, param, batch);
                    taskList.add(task);

                    TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
                                                                  task.getTaskId(), param.getOriginImgUrl(), batch);
                    taskOrdinal.setShortCutDesc(promptDTO.getPrompt());
                    taskOrdinalList.add(taskOrdinal);

                    if (fullTaskParam != null && !fullTaskParam.trim().isEmpty()) {
                        try {
                            Integer uid = userId != null ? userId.intValue() : null;
                            materialHistoryService.recordMaterialUsageFromTaskParam(
                                    fullTaskParam, uid, task.getTaskId(), Constants.TASK_TYPE_FOUR_TEXT);
                        } catch (Exception e) {
                            log.warn("记录平铺图文生图素材历史失败, taskId={}: {}", task.getTaskId(), e.getMessage());
                        }
                    } else {
                        log.info("平铺图文生图记录历史跳过：taskParam为空");
                    }
                }
            }
            else if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getImgUrl())) {
                List<String> imgUrlList = parseImgUrlList(dto.getImgUrl());
                for (String url : imgUrlList) {
                    Task task = createBaseTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
                    taskList.add(task);

                    TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
                                                                  task.getTaskId(), url, batch);
                    taskOrdinalList.add(taskOrdinal);

                    if (dto.getType() == Constants.TASK_TYPE_FOUR_TEXT) {
                        if (fullTaskParam != null && !fullTaskParam.trim().isEmpty()) {
                            try {
                                Integer uid = userId != null ? userId.intValue() : null;
                                materialHistoryService.recordMaterialUsageFromTaskParam(
                                        fullTaskParam, uid, task.getTaskId(), Constants.TASK_TYPE_FOUR_TEXT);
                            } catch (Exception e) {
                                log.warn("记录平铺图文生图素材历史失败, taskId={}: {}", task.getTaskId(), e.getMessage());
                            }
                        } else {
                            log.info("平铺图文生图记录历史跳过：taskParam为空");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("创建平铺图任务失败", e);
            throw new RuntimeException("创建平铺图任务失败", e);
        }

        insertTasks(taskList);
        processTaskOrdinals(taskOrdinalList);

    }

    @Override
    public List<Task> createTasks(BatchDTO dto, Batch batch, Long userId, String taskParam) {
        return new ArrayList<>();
    }

    @Override
    public List<TaskOrdinal> createTaskOrdinals(BatchDTO dto, Batch batch, List<Task> taskList,
                                               Long userId, String taskParam) {
        return new ArrayList<>();
    }
}