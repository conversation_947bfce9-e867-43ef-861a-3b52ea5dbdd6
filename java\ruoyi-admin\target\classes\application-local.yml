# 项目相关配置
ruoyi:
    # 名称
    name: AiPhoto
    # 版本
    version: 3.8.7
    # 版权年份
    copyrightYear: 2023
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: D:/ruoyi/uploadPath
    # 获取ip地址开关
    addressEnabled: false
    # 验证码类型 math 数字计算 char 字符验证
    captchaType: math

# 开发环境配置
server:
    # 服务器的HTTP端口，默认为8080
    port: 8090
    servlet:
        # 应用的访问路径
        context-path:
    tomcat:
        # tomcat的URI编码
        uri-encoding: UTF-8
        # 连接数满后的排队数，默认为100
        accept-count: 1000
        threads:
            # tomcat最大线程数，默认为200
            max: 800
            # Tomcat启动初始化的线程数，默认值10
            min-spare: 100
        # 服务器在任何给定时间接受和处理的最大连接数。一旦达到限制，操作系统仍然可以接受基于“acceptCount”属性的连接。
        max-connections: 58192
#        不作限制
        max-http-form-post-size: -1
# 数据源配置
spring:
    application:
        # 应用名称
        name: ai-photo
    # 资源信息
    messages:
        # 国际化资源文件路径
        basename: i18n/messages
        multipart:
            # 单个文件大小
            max-file-size: 10MB
            # 设置总上传的文件大小
#            max-request-size: 20MB
    # 服务模块
    devtools:
        restart:
            # 热部署开关
            enabled: false
    # redis 配置
    redis:
        #    测试服务器：本地用 公网************ 线上用 内网*************
        host: localhost
        # 端口，默认为6379
        port: 16379
        # 数据库索引
        #    database: 3
        database: 0
        # 密码
#        password:
        password: root
        # 连接超时时间
        timeout: 30s
        # 连接超时时间（毫秒）
        connect-timeout: 30000
        # 读取超时时间（毫秒）
        read-timeout: 30000
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 5
                # 连接池中的最大空闲连接
                max-idle: 20
                # 连接池的最大数据库连接数
                max-active: 50
                # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: 5000ms
                # 连接空闲时间（毫秒）
                time-between-eviction-runs: 30000
            # 关闭超时时间（毫秒）
            shutdown-timeout: 500ms
            pubsub-subscriber-enabled: true
            # 连接工厂配置
            cluster:
                refresh:
                    adaptive: true
                    period: 30s
            # 客户端配置
            client-name: ai-photo-client
        listener:
            keyspace-events: Ex  # 设置为'Ex'以监听键过期事件
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: **********************************************************************************************************************************************************************************
                username: root
                password: root

            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    servlet:
        # 应用的访问路径
        context-path: /

mybatis-plus:
    global-config:
        #    work-id: ${random.int(1,31)}
        #    datacenter-id: ${random.int(1,31)}
        work-id: 1
        datacenter-id: 3

# Swagger配置
swagger:
    # 是否开启swagger
    enabled: true
    #  # 请求前缀
#    pathMapping: /prod-api
    pathMapping:

#腾讯云api秘钥id
tencent:
    yun:
        secretid: AKIDDFHGJOLB0MlryJWUI1vtp4MqT7naPwLl
        secretkey: X9sDM1lO2yx1TnAg5ZBnQ9nLsWEys5DI
        region: ap-guangzhou
        Bucket: ai-photo-task-1303206685

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 43200

# 阿里云内网 发测试环境用这个 http://oss-cn-huhehaote-internal.aliyuncs.com
#阿里云公网  本地测试用 http://oss-cn-huhehaote.aliyuncs.com
aliyun:
    endpoint: http://oss-cn-huhehaote.aliyuncs.com
    accessKeyId: LTAI5tMV98xMHPV9QbmLRu1R
    accessKeySecret: ******************************
    bucketName: ai-photo-task-1303206685
    region: cn-huhehaote
    green_endpoint: green-cip.cn-beijing.aliyuncs.com
    sms:
        sign: 铺江湖
        code: SMS_205585453
        region: cn-beijing
        ak: LTAI4G2NDkPbAhT8hpEEavXo
        sk: ******************************

# 微信开放平台 appid
wx:
    open:
        app_id: wxf380a931b228a6bc
        # 微信开放平台 appsecret
        app_secret: 3a29e49bf9e28ea825eaaa3426d2a81e
        # 微信开放平台重定向url，即扫码登录后回调的后端api，中间的ip地址是内网穿透的
        redirect_url:  http://79b5ed0a.r10.cpolar.top/wechat/callback

python:
    #    调用python 的识图转文接口
    gpu_read_pictures: http://*************:8004/pod_ai_i2t/

platform:
  api:
    forgediy-url: https://design.styleforgediy.com/blade-product/AIShop/setMaterial
    dingzhi-url: https://design.the2016.com/api/xiaoaishop/api/image_task/store

# GTask统一任务配置 - 本地开发环境
gtask:
  # 是否启用GTask任务调度
  enabled: true

  # 默认并发数
  default-concurrency: 2

  # 任务执行间隔配置（毫秒）
  intervals:
    risk_detection: 60000      # 风险检测任务，60秒（本地环境降低频率）
    title_extraction: 60000    # 标题提取任务，60秒
    product_collection: 60000  # 产品采集任务，60秒
    product_color: 60000      # 产品颜色任务，60秒

  # 任务并发数配置
  concurrency:
    risk_detection: 5          # 风险检测任务并发数（本地环境降低并发）
    title_extraction: 5        # 标题提取任务并发数
    product_collection: 2      # 产品采集任务并发数
    product_color: 2           # 产品颜色任务并发数

  # 任务批处理大小配置
  batch-size:
    risk_detection: 50         # 风险检测任务批处理大小（本地环境降低批量）
    title_extraction: 50       # 标题提取任务批处理大小
    product_collection: 50     # 产品采集任务批处理大小
    product_color: 50          # 产品颜色任务批处理大小

  # 任务超时配置（毫秒）
  timeout:
    risk_detection: 300000     # 风险检测任务超时时间，5分钟
    title_extraction: 300000   # 标题提取任务超时时间，5分钟
    product_collection: 180000 # 产品采集任务超时时间，3分钟
    product_color: 180000      # 产品颜色任务超时时间，3分钟