package com.dataxai.web.task.core.ordinal.material;

import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.TaskOrdinalDTO;
import com.dataxai.web.task.core.ordinal.AbstractMaterialTaskOrdinalFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.dataxai.web.service.MaterialHistoryService;
import com.dataxai.web.service.MaterialResolverService;

/**
 * 平铺图任务序号工厂实现类
 *
 * <p>处理平铺图任务（任务类型：6）的参数校验和逻辑处理</p>
 *
 * <AUTHOR>
 * @date 2024-01-08
 * @version 1.0
 */
@Component
@Slf4j
public class FourTextTaskOrdinalFactory extends AbstractMaterialTaskOrdinalFactory {

    @Autowired
    private MaterialHistoryService materialHistoryService;

    @Autowired
    private MaterialResolverService materialResolverService;

    @Override
    public void validateParameters(TaskOrdinalDTO taskOrdinalDTO) {
        if (taskOrdinalDTO == null) {
            throw new ServiceException("任务参数不能为空", 500);
        }

        // 校验任务类型
        Long taskType = taskOrdinalDTO.getType();
        if (taskType == null) {
            throw new ServiceException("任务类型不能为空", 500);
        }

        if (Constants.TASK_TYPE_FOUR_TEXT != taskType) {
            throw new ServiceException("不支持的任务类型：" + taskType + "，当前工厂仅支持平铺图任务", 500);
        }

        // 平铺图特定校验
        validateFourTextSpecificParameters(taskOrdinalDTO);

        // 修复功能仅限人台图使用
        if (taskOrdinalDTO.getRepair() != null && 1 == taskOrdinalDTO.getRepair()) {
            throw new ServiceException("仅限人台图使用", 500);
        }

        log.info("平铺图任务参数校验通过");
    }

    @Override
    public void processLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) throws Exception {
        if (taskOrdinalDTO == null || taskOrdinal == null) {
            throw new ServiceException("任务参数或实体不能为空", 500);
        }

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_FOUR_TEXT, "开始处理平铺图任务逻辑，任务ID: {}", taskOrdinalDTO.getTaskId());

        // 设置任务基本信息
        taskOrdinal.setShortCutDesc(taskOrdinalDTO.getPrompt());

        // 处理平铺图特定逻辑
        processFourTextSpecificLogic(taskOrdinalDTO, taskOrdinal);

        // 记录素材使用历史（与文生图一致，但taskType=6）
        try {
            String taskId = taskOrdinalDTO.getTaskId();
            if (StringUtils.isNotEmpty(taskId)) {
                Long userIdLong = taskOrdinal.getUserId();
                Integer userId = userIdLong != null ? userIdLong.intValue() : null;

                String fullTaskParam = taskOrdinalDTO.getTaskParam();
                if (materialResolverService != null && StringUtils.isNotEmpty(fullTaskParam)) {
                    try {
                        fullTaskParam = materialResolverService.buildFullTaskParam(fullTaskParam);
                    } catch (Exception e) {
                        log.warn("构建fullTaskParam失败，使用原始参数: {}", e.getMessage());
                    }
                }

                if (StringUtils.isNotEmpty(fullTaskParam)) {
                    materialHistoryService.recordMaterialUsageFromTaskParam(
                        fullTaskParam,
                        userId,
                        taskId,
                        Constants.TASK_TYPE_FOUR_TEXT
                    );
                    log.info("平铺图文生图素材历史记录成功，任务ID: {}", taskId);
                } else {
                    log.info("平铺图文生图素材历史记录跳过，taskParam为空，任务ID: {}", taskId);
                }
            } else {
                log.warn("任务ID为空，跳过平铺图文生图素材历史记录");
            }
        } catch (Exception e) {
            log.error("记录平铺图文生图素材历史失败，任务ID: {}", taskOrdinalDTO.getTaskId(), e);
        }

        TaskLogUtils.info(TaskLogUtils.TASK_TYPE_FOUR_TEXT, "平铺图任务逻辑处理完成，任务ID: {}", taskOrdinal.getTaskId());
    }

    /**
     * 校验平铺图特定参数
     */
    private void validateFourTextSpecificParameters(TaskOrdinalDTO taskOrdinalDTO) {
        // 校验描述信息
        validateDescription(taskOrdinalDTO);
    }

    /**
     * 处理平铺图特定逻辑
     */
    private void processFourTextSpecificLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) {
        // 平铺图特定的处理逻辑
        TaskLogUtils.debug(TaskLogUtils.TASK_TYPE_FOUR_TEXT, "处理平铺图特定逻辑");
    }

    @Override
    protected boolean isSupportedTaskType(Long taskType) {
        return Constants.TASK_TYPE_FOUR_TEXT == taskType;
    }

    @Override
    protected void validateSpecificParameters(TaskOrdinalDTO taskOrdinalDTO) {
        validateFourTextSpecificParameters(taskOrdinalDTO);
    }

    @Override
    protected void processSpecificLogic(TaskOrdinalDTO taskOrdinalDTO, TaskOrdinal taskOrdinal) throws Exception {
        processFourTextSpecificLogic(taskOrdinalDTO, taskOrdinal);
    }

    @Override
    protected void processSpecificExecution(TaskOrdinal taskOrdinal) throws Exception {
        // 平铺图特定的执行逻辑
        log.info("处理平铺图特定执行逻辑，任务ID：{}", taskOrdinal.getTaskId());
    }

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{(long) Constants.TASK_TYPE_FOUR_TEXT};
    }

        /**
     * 判断是否需要AIOCR预处理
     * 平铺图文生图任务如果描述为空则需要预处理
     */
    @Override
    protected boolean needsAiocrPreprocess(TaskOrdinal taskOrdinal) {
        // 检查是否有原始图片且描述为空
        boolean hasOriginImage = StringUtils.isNotEmpty(taskOrdinal.getOriginImgUrl());
        boolean hasEmptyDescription = StringUtils.isEmpty(taskOrdinal.getShortCutDesc());

        boolean needAiocr = hasOriginImage && hasEmptyDescription;

        if (needAiocr) {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_FOUR_TEXT, "平铺图任务需要AIOCR预处理 - TaskOrdinalId: {}, 有原始图片: {}, 描述为空: {}",
                     taskOrdinal.getTaskOrdinalId(), hasOriginImage, hasEmptyDescription);
        } else {
            TaskLogUtils.info(TaskLogUtils.TASK_TYPE_FOUR_TEXT, "平铺图任务不需要AIOCR预处理 - TaskOrdinalId: {}, 有原始图片: {}, 描述为空: {}",
                     taskOrdinal.getTaskOrdinalId(), hasOriginImage, hasEmptyDescription);
        }

        return needAiocr;
    }

}