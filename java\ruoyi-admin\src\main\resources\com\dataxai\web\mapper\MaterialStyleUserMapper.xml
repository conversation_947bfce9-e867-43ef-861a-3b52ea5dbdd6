<mapper namespace="com.dataxai.web.mapper.MaterialStyleUserMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_material_style_user (material_style_id, user_id)
        VALUES (#{materialStyleId}, #{userId})
    </insert>

    <delete id="deleteById">
        DELETE FROM t_material_style_user WHERE id = #{id}
    </delete>

    <delete id="deleteByUserAndStyle">
        DELETE FROM t_material_style_user
        WHERE user_id = #{userId} AND material_style_id = #{styleId}
    </delete>

    <!-- 新增：根据风格ID删除所有收藏记录 -->
    <delete id="deleteByStyleId">
        DELETE FROM t_material_style_user WHERE material_style_id = #{styleId}
    </delete>

    <select id="selectByUser" resultType="MaterialStyleUser">
        SELECT * FROM t_material_style_user
        WHERE user_id = #{userId}
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countByUser" resultType="int">
        SELECT COUNT(*) FROM t_material_style_user
        WHERE user_id = #{userId}
    </select>
</mapper>