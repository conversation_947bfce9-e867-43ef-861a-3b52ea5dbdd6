import { useEffect, useRef, useState } from 'react'
import {
	Table,
	Tag,
	Progress,
	Upload,
	Modal,
	Button,
	Radio,
	Input,
	message,
	DatePicker,
	Pagination
} from 'antd'
import type { CheckboxGroupProps } from 'antd/es/checkbox'
import { CloudUploadOutlined, ExclamationCircleFilled } from '@ant-design/icons'
import type { UploadFile, UploadProps } from 'antd'
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getBatchList, addBatch, deleteBatch, stopBatch ,updateTaskRemark,getBatchRemark} from '@/api/task'
import { batchDownloadImages } from '@/utils/downFile'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { batchUploadOss, batchZipUrl } from '@/api/common'
import { set } from 'lodash'
import FilterBar, { FilterParams } from '@/component/filter';

export const BatchToolsCut = () => {
	const navigate = useNavigate()

	const [modal, contextHolder] = Modal.useModal()
	const [messageApi, messageContextHolder] = message.useMessage()

	const [batchNumber, setBatchNumber] = useState('') // 批次号
	const [dateRange, setDateRange] = useState<any>(null) // 日期范围
	const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

	const [isModalVisible, setIsModalVisible] = useState(false)
	const [fileList, setFileList] = useState<UploadFile[]>([])

	// 表格数据
	const [dataSource, setDataSource] = useState([])
	// 表格loading
	const [tableLoading, setTableLoading] = useState(false)
	// 分页配置
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0
	})
    const [openModal, setOpenModal] = useState<any>(false)//remark modal type
    const [remarkText, setRemarkText] = useState('')
    const [remarkId, setRemarkId] = useState(null)//修改备注需要的id
    //提交备注信息
    const editRemark = ()=>{
        updateTaskRemark({remark:remarkText,id:remarkId,taskType: 'batch'}).then(res=>{ 
            setOpenModal(false);
        })
    }
	//获取图案裁剪批次列表
	const fetchBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number,
		batchNumber: string,
		remark: string,
		userId: string,
		startTime: string,
		endTime: string
	) => {
		setDataSource([])
		setTableLoading(true)
		try {
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber,
				remark,
				userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response.data, '获取图案裁剪批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
				setIsFirstLoad(false)
			}
		} catch (error) {
			console.error('获取数据时出错：', error)
		} finally {
			setTableLoading(false)
		}
	}
	useEffect(() => {
		handleSearch(1, 10)
	}, [])
	// 查询条件存储key
	const QUERY_PARAMS_KEY = 'upload_image_query_params';
	//是否首次加载
	const [isFirstLoad, setIsFirstLoad] = useState(true)
	// Refs 声明（必须放在组件顶层）
	const timerRef = useRef<NodeJS.Timeout>()
	const fetchRef = useRef<typeof refreshBatchListData>()
	const paginationRef = useRef(pagination)
	const isFirstLoadRef = useRef(isFirstLoad)
	// 同步最新状态到 ref
	useEffect(() => {
		paginationRef.current = pagination
		isFirstLoadRef.current = isFirstLoad
	})
	useEffect(() => {
		// 更新函数引用
		fetchRef.current = refreshBatchListData

		const tick = () => {
			if (!isFirstLoadRef.current) {
				console.log('定时刷新')
				fetchRef.current?.(14, paginationRef.current.current, 10)
			}
		}

		// 清除旧定时器
		if (timerRef.current) clearInterval(timerRef.current)
		// 启动新定时器
		timerRef.current = setInterval(tick, 5000)
		// 清理函数
		return () => {
			if (timerRef.current) {
				clearInterval(timerRef.current)
			}
		}
	}, [isFirstLoad]) // 依赖项

	//刷新图案裁剪批次列表
	const refreshBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number
	) => {
		try {
			const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
			let queryParams = {
				batchNumber: '',
				remark: '',
				userId: '',
				stringDateRange: [] as string[]
			};

			if (savedParams) {
				queryParams = JSON.parse(savedParams);
			}

			let startTime = '';
			let endTime = '';
			if (queryParams.stringDateRange.length > 0) {
				[startTime, endTime] = queryParams.stringDateRange;
				startTime = startTime ? `${startTime} 00:00:00` : '';
				endTime = endTime ? `${endTime} 23:59:59` : '';
			}
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber: queryParams.batchNumber,
				remark: queryParams.remark,
				userId: queryParams.userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response, '刷新图片任务批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
			}
		} catch (error) {
			console.error('刷新数据时出错：', error)
		} finally {
			setTableLoading(false)
		}
	}
	// 查询处理函数
	const handleSearch = (pageNum: number, pageSize: number) => {
		// 存储查询条件到sessionStorage
		const queryParams = {
			batchNumber,
			remark: '',
			userId: '',
			stringDateRange
		};
		sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
		if (stringDateRange.length > 0) {
			let [startTime, endTime] = stringDateRange
			startTime = startTime ? `${startTime} 00:00:00` : ''
			endTime = endTime ? `${endTime} 23:59:59` : ''
			fetchBatchListData(14, pageNum, pageSize, batchNumber, queryParams.remark, queryParams.userId, startTime, endTime)
		} else {
			fetchBatchListData(14, pageNum, pageSize, batchNumber, queryParams.remark, queryParams.userId, '', '')
		}
	}

	const onSearch = (params: FilterParams) => {
		const pageNum = 1;
		const pageSize = 10;
		fetchBatchListData(
			14,
			pageNum,
			pageSize,
			params.batchNumber ?? '',
			params.remark ?? '',
			params.userId ?? '',
			params.startTime,
			params.endTime
		);
	};

	// 表格列配置
	const columns = [
		{
			title: '任务批次',
			dataIndex: 'batchNumber',
			key: 'batchNumber',
			ellipsis: true,
			align: 'center' as const,
			render: (taskBatch: string, record: any) => (
				<>
					<p>{taskBatch}</p>
					<p>{record.remark}</p>
				</>
			)
		},
		{
			title: '任务数量',
			dataIndex: 'totalAmount',
			key: 'totalAmount',
			width: 140,
			align: 'center' as const,
			render: (totalAmount: number, record: any) => (
				<>
					<p>总数：{totalAmount}</p>
					<p>成功：{record.successAmount}</p>
					{(record.status == 1 || record.status == 3 || record.status == 6) && record.failAmount > 0 && (
						<p style={{ color: '#cf1322' }}>失败：{record.failAmount}</p>
					)}
				</>
			)
		},
		{
			title: '任务进度',
			dataIndex: 'successAmount',
			key: 'successAmount',
			align: 'center' as const,
			render: (successAmount: number, record: any) => (
				<Progress
					percent={
						(record.status == 1 || record.status == 3 || record.status == 6)
							? Math.round(((successAmount + record.failAmount) / record.totalAmount) * 100)
							: 0
					}
					strokeColor={record.status == '6' ? '#cf1322' : undefined}
				/>
			)
		},
		{
			title: '任务状态',
			dataIndex: 'status',
			key: 'status',
			width: 200,
			align: 'center' as const,
			render: (status: string, record: any) => {
				let color = ''
				if (status == '1') {
					color = 'green'
				} else if (status == '3') {
					color = 'blue'
				} else if (status == '4') {
					color = 'orange'
				} else {
					color = 'red'
				}
				return (
					<Tag color={color}>
						{status == '1'
							? '已完成'
							: status == '3'
								? '执行中'
								: status == '4' ? (record.waitTime == 0 ? '排队中 预估1分钟以内' : record.waitTime > 0 ? `排队中 预估(${record.waitTime}分钟)` : '排队中')
									: status == '5'
										? '准备中'
										: '手动终止'}
					</Tag>
				)
			}
		},
		{
			title: '创建时间',
			dataIndex: 'createTime',
			key: 'createTime',
			align: 'center' as const,
			render: (createTime: string) => (
				<p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
			)
		},
		{
			title: '操作',
			key: 'action',
			align: 'center' as const,
			render: (_: any, record: any) => (
				<div className="flex justify-center  gap-2">
					{(
						<Button
							type="link"
							disabled={record.successAmount == 0}
							style={{
								color: record.successAmount == 0 ? '#bfbfbf' : '#32649f'
							}}
							onClick={() => goDetail(record)}
							size="small"
						>
							查看详情
						</Button>
					)}
					{(
						<Button
							type="link"
							disabled={record.successAmount == 0}
							style={{
								color: record.successAmount == 0 ? '#bfbfbf' : '#32649f'
							}}
							onClick={() => handleDownloadImgages(record.imgResults, record.batchId)}
							size="small"
							loading={downloadLoading === record.batchId}
						>
							下载
						</Button>
					)}
					{record.status == 4 && (
						<Button
							type="link"
							size="small"
							style={{ color: '#cf1322' }}
							onClick={() => handleDelBatch(record.batchId)}
						>
							删除
						</Button>
					)}
					{record.status == 3 && (
						<Button
							type="link"
							size="small"
							style={{ color: '#cf1322' }}
							onClick={() => handleStopBatch(record.batchId)}
						>
							手动终止
						</Button>
					)}
                    {<Button type="link" style={{  color: '#32649f' }} onClick={() => {
                        //record.id 任务id
                        getBatchRemark(record.batchId ).then((res: any) => {
                            console.log(res);
                                setRemarkText(res.remark)
                            })
                            //console.log(record);
                            setRemarkId(record.batchId);
                            setOpenModal(!openModal);
                                
                        }}
                        size="small"
                        >
                            备注
                    </Button>}
				</div>
			)
		}
	]
	// 手动终止批次
	const handleStopBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: (
				<div className={'text-[18px] text-normal'}> 确认手动终止该批次？</div>
			),
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				stopBatch(id)
					.then((res) => {
						message.success('该批次手动终止成功')
						setPagination(prev => ({ ...prev, current: 1 }));
						handleSearch(1, 10)
					})
					.catch((err) => {
						message.error(`该批次手动终止失败：${err?.data?.msg}`)
						setPagination(prev => ({ ...prev, current: 1 }));
						handleSearch(1, 10)
					})
			},
			onCancel() {
				console.log('Cancel')
			}
		})
	}
	// 删除批次
	const handleDelBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: <div className={'text-[18px] text-normal'}> 确认删除该批次？</div>,
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				deleteBatch(id)
					.then((res) => {
						message.success('批次删除成功')
						setPagination(prev => ({ ...prev, current: 1 }));
						handleSearch(1, 10)
					})
					.catch((err) => {
						message.error(`批次删除失败：${err?.data?.msg}`)
						setPagination(prev => ({ ...prev, current: 1 }));
						handleSearch(1, 10)
					})
			},
			onCancel() {
				console.log('Cancel')
			}
		})
	}

	const [downloadLoading, setDownloadLoading] = useState(false);
	// 下载图片
	const handleDownloadImgages = (imgResults: any[], batchId: any) => {
		const imgUrls = imgResults.map((img) => img?.resImgUrl)
		setDownloadLoading(batchId);
		batchZipUrl({ imageUrls: imgUrls, type: 14 }).then((res: any) => {
			if (res) {
				window.open(res, '_blank'); // 在新标签页打开下载链接
			} else {
				messageApi.error('获取下载链接失败');
			}
		}).catch(err => {
			messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
		}).finally(() => {
			setDownloadLoading(false);
		})
		// batchDownloadImages(imgUrls);
	}

	// 查看详情
	const goDetail = (record: any) => {
		navigate('/workspace/batchTools/cut/detail', {
			state: { batchId: record.batchId }
		})
	}
	const showModal = () => {
		setIsModalVisible(true)
		setFileList([]) // 清空图片列表
	}
	const [creatLoading, setCreatLoading] = useState(false)
	// 创建任务
	const handleOk = async () => {
		try {
			if (fileList.length === 0) {
				messageApi.error('请先上传图片')
				return
			}
			setCreatLoading(true)

			const files = fileList.map((file) => file.originFileObj as File)

			addBatch({ files, type: 14 })
				.then((data) => {
					messageApi.success('图案裁剪任务新建成功')
					setIsModalVisible(false)
					setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
					handleSearch(1, 10) // 刷新批次列表数据
					userinfoService.refresh() // 刷新用户积分
				})
				.catch((err) => {
					messageApi.error(`创建失败: ${err?.data?.msg}`)
				})
				.finally(() => {
					setCreatLoading(false)
				})

			// batchUploadOss(files).then((res) => {
			// 	if (res.url) {
			// 		// 图片上传成功后，创建任务
			// 		addBatch({ imgUrl: res.url, type: 14 })
			// 			.then((data) => {
			// 				messageApi.success('图案裁剪任务新建成功')
			// 				setIsModalVisible(false)
			// 				setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
			// 				handleSearch(1, 10) // 刷新批次列表数据
			// 				userinfoService.refresh() // 刷新用户积分
			// 			})
			// 			.catch((err) => {
			// 				messageApi.error(`创建失败: ${err?.data?.msg}`)
			// 			})
			// 			.finally(() => {
			// 				setCreatLoading(false)
			// 			})
			// 	}
			// }).catch((err) => {
			// 	messageApi.error(`图片上传失败: ${err?.data?.msg}`)
			// 	setCreatLoading(false)
			// 	setFileList([]) // 清空图片列表
			// })
		} catch (err) {
			messageApi.error('创建失败')
			setCreatLoading(false)
		}
	}

	const handleCancel = () => {
		setIsModalVisible(false)
	}

	let prevFileList: any[] = []
	// 图片上传
	const handleUploadChange = ({
		fileList: newFileList
	}: {
		fileList: any[]
	}) => {
		// 比较新旧文件列表，如果相同则不处理
		if (
			newFileList.length === prevFileList.length &&
			newFileList.every((file, index) => file.uid === prevFileList[index].uid)
		) {
			return
		}

		prevFileList = newFileList
		const validFiles: UploadFile[] = []

		const promises = newFileList.map((file) => {
			const isImage = /^image\//.test(file.type || '')

			if (!isImage) {
				messageApi.error('请检查文件类型')
				return Promise.resolve(false)
			}

			return new Promise<boolean>((resolve) => {
				const reader = new FileReader()
				const originFileObj = file.originFileObj
				if (!originFileObj) {
					messageApi.error('无法获取文件对象,请检查文件')
					resolve(false)
					return
				}
				reader.readAsArrayBuffer(originFileObj)
				reader.onload = async () => {
					try {
						const blob = new Blob([reader.result as ArrayBuffer])
						const img = await createImageBitmap(blob)
						const { width, height } = img
						const isValidSize = width <= 4096 && height <= 4096
						if (!isValidSize) {
							messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过')
							resolve(false)
						} else {
							resolve(true)
						}
					} catch (error) {
						messageApi.error('图片加载失败，请检查图片格式')
						resolve(false)
					}
				}
				reader.onerror = () => {
					messageApi.error('读取文件失败，请检查文件格式')
					resolve(false)
				}
			})
		})

		Promise.all(promises).then((results) => {
			newFileList.forEach((file, index) => {
				if (results[index]) {
					validFiles.push(file)
				}
			})

			const totalFiles = fileList.length + validFiles.length
			if (totalFiles > 50) {
				messageApi.error('最多只能上传50张图片')
				return
			}

			if (validFiles.length > 0) {
				messageApi.success(`成功上传 ${validFiles.length} 张图片`);
				setFileList((prev) => [...prev, ...validFiles])
			}
		})
	}

	// 在组件中添加分页变化处理函数
	const handleTableChange = (pagination: any) => {
		setPagination({
			...pagination,
			current: pagination.current,
			pageSize: pagination.pageSize
		})
		handleSearch(pagination.current, pagination.pageSize)
	}
	return (
		<div className="h-full w-full p-[20px]">
			{contextHolder} {/* 这里确保 Modal 挂载 */}
			{messageContextHolder} {/* 这里确保 Message 挂载 */}
			<div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
				<Button type="primary" onClick={showModal}>
					新建图案裁剪任务
				</Button>
				{/* 新建图案裁剪任务弹窗 start */}
				<Modal
					title="新建图案裁剪任务"
					open={isModalVisible}
					onOk={handleOk}
					onCancel={handleCancel}
					width={660}
					centered
					okText="创建"
					confirmLoading={creatLoading}
				>
					<Upload.Dragger
						multiple
						accept="image/*"
						fileList={[]} // 设置为空数组隐藏自带列表
						onChange={handleUploadChange}
						beforeUpload={() => false} // 阻止自动上传
						className="upload-area"
						listType="picture"
						showUploadList={false} // 完全隐藏上传列表
						disabled={fileList.length >= 50} // 添加禁用状态
						style={{ marginTop: '30px' }}
						directory={true} // 允许选择或拖拽文件夹
					>
						<p className="ant-upload-drag-icon">
							<CloudUploadOutlined />
						</p>
						<p className="ant-upload-text">点击或拖拽文件到此处上传</p>
						<p className="ant-upload-hint">
							最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
						</p>
					</Upload.Dragger>
					{/* <div style={{ maxHeight: '40vh', overflowY: 'auto' }}>
                        <Upload
                            fileList={fileList}
                            listType="picture"
                            showUploadList={{
                                showPreviewIcon: false,
                                showRemoveIcon: true
                            }}
                            onRemove={(file) => {
                                setFileList(fileList.filter(f => f.uid !== file.uid));
                                return false;
                            }}
                        />
                    </div> */}
					<div className="mt-2 text-right">
						已选择图片张数:{' '}
						<span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
							{fileList.length}
						</span>{' '}
						/ 50
					</div>
				</Modal>
				{/* 新建图案裁剪任务弹窗 end */}
				<div className="flex items-center gap-2 ]">
					<FilterBar
						fields={['batchNumber','dateRange','remark','userId']}
						storageKey={QUERY_PARAMS_KEY}
						withTime={false}
						onSearch={onSearch}
					/>
				</div>
			</div>
			<div className="w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide">
				<Table
					columns={columns}
					dataSource={dataSource}
					className="mt-4"
					scroll={{ x: 966 }}
					loading={tableLoading}
					pagination={{
						...pagination,
						showTotal: (total: number) => `共 ${total} 条`,
						showSizeChanger: false
					}}
					onChange={handleTableChange}
				/>
			</div>
			<Modal
				title="添加备注信息"
				visible={openModal}
				onOk={editRemark}
				onCancel={() => setOpenModal(false)}
				okText="确定"
				cancelText="取消"
			>
				<Input placeholder="请输入备注" onChange={(e)=>setRemarkText(e.target.value)} value={remarkText} />
			</Modal>
		</div>
	)
}

export default BatchToolsCut
