package com.dataxai.web.gtask.scheduler;

import com.dataxai.domain.ProductInfo;
import com.dataxai.web.gtask.manager.GTaskManager;
import com.dataxai.web.gtask.processor.GTaskProcessor;
import com.dataxai.web.gtask.processor.ProductColorGTaskProcessor;
import com.dataxai.common.utils.TaskLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 产品颜色识别 GTask 调度器
 *
 * 作用：
 * - 以固定频率（@Scheduled）触发一轮“产品颜色识别”任务调度
 * - 统一控制并发度/批量/超时（委托给 AbstractGTaskScheduler 和 GTaskConfig）
 * - 防止重入（通过 GTaskManager 的 isTaskExecuting 标志位与父类 isExecuting 双层保护）
 *
 * 主要流程：
 * 1) @PostConstruct init() → initScheduler()：创建处理器、初始化线程池（并发度来自 gtask 配置）
 * 2) scheduleProductColorTasks()：每次触发时检查开关与重入，然后调用父类 executeSchedule()
 * 3) AbstractGTaskScheduler.executeSchedule()：
 *    - 调用处理器 queryPendingTasks(batchSize) 拉取待处理数据
 *    - 根据并发度切分任务列表并发执行 processBatchTasks / processSingleTask
 *    - 在超时时间内等待结束
 * 4) @PreDestroy destroy()：优雅关闭线程池
 *
 * 配置对应关系（可参考 application-*.yml 的 gtask 段）：
 * - gtask.concurrency.product_color → 线程池并发数
 * - gtask.batch-size.product_color → 每轮处理的最大条数
 * - gtask.timeout.product_color → 等待本轮任务完成的超时时间（毫秒）
 * - gtask.enabled → 总开关，关闭后调度器不再执行
 *
 * 状态/接口：
 * - getSchedulerStatus()：便于对外提供监控（任务描述、并发、批量、间隔、超时、是否执行中、统计等）
 */
@Slf4j
@Component
public class ProductColorGTaskScheduler extends AbstractGTaskScheduler<ProductInfo> {

    @Autowired
    private ProductColorGTaskProcessor colorProcessor;

    @Autowired
    private GTaskManager gTaskManager;

    @PostConstruct
    public void init() {
        // 应用启动时清理一次所有任务锁，防止因异常停机导致的残留锁
        gTaskManager.clearAllExecutionStatus();
        initScheduler();
        TaskLogUtils.ProductColorCollection.info("产品颜色识别GTask调度器初始化完成");
    }

    /**
     * 提供本调度器对应的处理器实例
     * 由父类在 initScheduler() 时调用，用于组装任务流水线
     */
    @Override
    protected GTaskProcessor<ProductInfo> createTaskProcessor() {
        return colorProcessor;
    }

    /**
     * 定时触发一次颜色识别任务
     *
     * 步骤：
     * 1) 读取总开关（gtask.enabled），关闭则直接返回
     * 2) 通过 GTaskManager 的标志位防止重入（同一 taskType 在执行中则跳过）
     * 3) 委托给父类 executeSchedule() 完成查询→切分→并发→等待
     * 4) 记录结果与耗时，最终清理执行标志位
     */
    // 每60秒（1分钟）执行一次；如需走配置，请参考其它 Scheduler 的实现
    @Scheduled(fixedRate = 60000)
    public void scheduleProductColorTasks() {
        TaskLogUtils.ProductColorCollection.debug("--- 颜色识别调度周期开始 ---");

        if (!gTaskConfig.isEnabled()) {
            TaskLogUtils.ProductColorCollection.warn("调度周期取消: GTask 总开关已关闭。");
            return;
        }

        String taskType = taskProcessor.getTaskType();
        long threadId = Thread.currentThread().getId();

        TaskLogUtils.ProductColorCollection.info("尝试获取任务锁 (Thread: {})...", threadId);
        if (gTaskManager.isTaskExecuting(taskType)) {
            TaskLogUtils.ProductColorCollection.warn("获取任务锁失败: 任务已在执行中，本次调度跳过。 (Thread: {})", threadId);
            return;
        }

        long start = System.currentTimeMillis();
        gTaskManager.setTaskExecuting(taskType, true);
        TaskLogUtils.ProductColorCollection.info("成功获取任务锁，开始执行。 (Thread: {})", threadId);

        try {
            executeSchedule();
            long cost = System.currentTimeMillis() - start;
            TaskLogUtils.ProductColorCollection.info("任务执行完成，耗时: {}ms (Thread: {})", cost, threadId);
            gTaskManager.recordTaskResult(taskType, 0, 0, cost);
        } catch (Exception e) {
            long cost = System.currentTimeMillis() - start;
            TaskLogUtils.ProductColorCollection.error("任务执行异常，耗时: {}ms (Thread: {})", cost, threadId, e);
            gTaskManager.recordTaskResult(taskType, 0, 1, cost);
        } finally {
            gTaskManager.setTaskExecuting(taskType, false);
            TaskLogUtils.ProductColorCollection.info("任务锁已释放。 (Thread: {})", threadId);
            TaskLogUtils.ProductColorCollection.debug("--- 颜色识别调度周期结束 ---");
        }
    }

    @PreDestroy
    public void destroy() {
        TaskLogUtils.ProductColorCollection.info("产品颜色识别GTask调度器正在关闭...");
        shutdown();
        TaskLogUtils.ProductColorCollection.info("产品颜色识别GTask调度器已关闭");
    }
}