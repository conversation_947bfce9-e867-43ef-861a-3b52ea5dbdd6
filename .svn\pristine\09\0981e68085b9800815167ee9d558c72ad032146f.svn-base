package com.dataxai.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 数据半托模板数据信息对象 t_template_infromation_data
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
public class TTemplateInfromationData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 关联模板信息表 */
    @Excel(name = "关联模板信息表")
    private String templateId;

    /** 列id */
    @Excel(name = "列id")
    private Long columnId;

    /** 列名称 */
    @Excel(name = "列名称")
    private String columnName;

    /** 列类型 */
    @Excel(name = "列类型")
    private String columnType;

    /** 列数据值 */
    @Excel(name = "列数据值")
    private String columnValue;

    /** 是否必填 */
    @Excel(name = "是否必填")
    private String columnRequired;

    /** 列头部 */
    @Excel(name = "列头部")
    private String columnHead;

    /** 选择值 */
    @Excel(name = "选择值")
    private String options;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String noSplit;

    /** 索引下标 */
    @Excel(name = "索引下标")
    private Long indexs;
    @Excel(name = "索引")
    private String sign;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTemplateId(String templateId) 
    {
        this.templateId = templateId;
    }




    public String getColumnName() 
    {
        return columnName;
    }
    public void setColumnType(String columnType) 
    {
        this.columnType = columnType;
    }

    public String getColumnType() 
    {
        return columnType;
    }
    public void setColumnValue(String columnValue) 
    {
        this.columnValue = columnValue;
    }

    public String getColumnValue() 
    {
        return columnValue;
    }
    public void setColumnRequired(String columnRequired) 
    {
        this.columnRequired = columnRequired;
    }



    public void setColumnHead(String columnHead) 
    {
        this.columnHead = columnHead;
    }

    public String getColumnHead() 
    {
        return columnHead;
    }
    public void setOptions(String options) 
    {
        this.options = options;
    }

    public String getOptions() 
    {
        return options;
    }
    public void setNoSplit(String noSplit) 
    {
        this.noSplit = noSplit;
    }

    public String getNoSplit() 
    {
        return noSplit;
    }
    public void setIndex(Long index)
    {
        this.indexs = indexs;
    }

    public Long getIndexs()
    {
        return indexs;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("templateId", getTemplateId())
            .append("columnId", getColumnId())
            .append("columnName", getColumnName())
            .append("columnType", getColumnType())
            .append("columnValue", getColumnValue())
            .append("columnRequired", getColumnRequired())
            .append("columnHead", getColumnHead())
            .append("remark", getRemark())
            .append("options", getOptions())
            .append("noSplit", getNoSplit())
            .append("indexs", getIndexs())
            .toString();
    }
}
