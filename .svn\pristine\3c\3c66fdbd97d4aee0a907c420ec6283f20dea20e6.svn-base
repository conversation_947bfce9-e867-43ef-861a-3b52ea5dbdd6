<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.TitleExtractionTaskDetailMapper">
    
    <resultMap type="com.dataxai.domain.TitleExtractionTaskDetail" id="TitleExtractionTaskDetailResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="scaleImageUrl"    column="scale_image_url"    />
        <result property="type"    column="type"    />
        <result property="typeId"    column="type_id"    />
        <result property="productTitle"    column="product_title"    />
        <result property="temuProductTitle"    column="temu_product_title"    />
        <result property="productDescription"    column="product_description"    />
        <result property="metaDescription"    column="meta_description"    />
        <result property="processStatus"    column="process_status"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="hasUploaded"    column="has_uploaded"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTitleExtractionTaskDetailVo">
        select id, task_id, image_url,scale_image_url, type, type_id, product_title, temu_product_title, product_description, meta_description, process_status, owner_id, has_uploaded, create_time, update_time from title_extraction_task_detail
    </sql>

    <select id="selectTitleExtractionTaskDetailList" parameterType="TitleExtractionTaskDetail" resultMap="TitleExtractionTaskDetailResult">
        <include refid="selectTitleExtractionTaskDetailVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="processStatus != null "> and process_status = #{processStatus}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
        </where>
        order by id asc
    </select>

    <select id="selectTitleExtractionTaskDetailByTaskId" parameterType="Long" resultMap="TitleExtractionTaskDetailResult">
        <include refid="selectTitleExtractionTaskDetailVo"/>
        where task_id = #{taskId}
--         order by create_time asc
    </select>
    
    <select id="selectTitleExtractionTaskDetailById" parameterType="Long" resultMap="TitleExtractionTaskDetailResult">
        <include refid="selectTitleExtractionTaskDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTitleExtractionTaskDetail" parameterType="TitleExtractionTaskDetail" useGeneratedKeys="true" keyProperty="id">
        insert into title_extraction_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url,</if>
            <if test="type != null">type,</if>
            <if test="typeId != null">type_id,</if>
            <if test="productTitle != null">product_title,</if>
            <if test="temuProductTitle != null">temu_product_title,</if>
            <if test="productDescription != null">product_description,</if>
            <if test="metaDescription != null">meta_description,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="hasUploaded != null">has_uploaded,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">#{scaleImageUrl},</if>
            <if test="type != null">#{type},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="productTitle != null">#{productTitle},</if>
            <if test="temuProductTitle != null">#{temuProductTitle},</if>
            <if test="productDescription != null">#{productDescription},</if>
            <if test="metaDescription != null">#{metaDescription},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="hasUploaded != null">#{hasUploaded},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertTitleExtractionTaskDetailBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into title_extraction_task_detail (
            task_id, image_url, scale_image_url,type, type_id, product_title, temu_product_title,
            product_description, meta_description, process_status, owner_id, has_uploaded, create_time
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.taskId}, #{item.imageUrl},#{item.scaleImageUrl} #{item.type}, #{item.typeId}, #{item.productTitle},
            #{item.temuProductTitle}, #{item.productDescription}, #{item.metaDescription}, 
            #{item.processStatus}, #{item.ownerId}, #{item.hasUploaded}, #{item.createTime}
        )
        </foreach>
    </insert>

    <update id="updateTitleExtractionTaskDetail" parameterType="TitleExtractionTaskDetail">
        update title_extraction_task_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url = #{scaleImageUrl},</if>
            <if test="type != null">type = #{type},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="productTitle != null">product_title = #{productTitle},</if>
            <if test="temuProductTitle != null">temu_product_title = #{temuProductTitle},</if>
            <if test="productDescription != null">product_description = #{productDescription},</if>
            <if test="metaDescription != null">meta_description = #{metaDescription},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="hasUploaded != null">has_uploaded = #{hasUploaded},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTitleExtractionTaskDetailById" parameterType="Long">
        delete from title_extraction_task_detail where id = #{id}
    </delete>

    <delete id="deleteTitleExtractionTaskDetailByIds" parameterType="String">
        delete from title_extraction_task_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper> 