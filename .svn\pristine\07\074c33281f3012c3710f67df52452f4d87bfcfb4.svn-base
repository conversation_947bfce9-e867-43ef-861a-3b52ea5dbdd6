package com.dataxai.web.controller.tariff_package;

import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.core.domain.R;
import com.dataxai.web.domain.CanUpdatePackageResponse;
import com.dataxai.web.domain.TariffPackageSub;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.TariffPackage;
import com.dataxai.web.service.ITariffPackageService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 套餐Controller
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
@Api(tags = "套餐接口")
@RestController
@RequestMapping("/tariff-package/package")
public class TariffPackageController extends BaseController<TariffPackage>
{
    @Autowired
    private ITariffPackageService tariffPackageService;

    /**
     * 查询套餐列表
     */
    @GetMapping("/list")
    @ApiOperation("查询套餐列表(后端页面使用)")
    public TableDataInfo<TariffPackage> list()
    {
        startPage();
        List<TariffPackage> list = tariffPackageService.selectTariffPackageListClient();
        TableDataInfo<TariffPackage> dataTable = getDataTable(list);
        return dataTable;
    }

    /**
     * 查询套餐列表
     */
    @GetMapping("/front/list")
    @ApiOperation("查询套餐列表(前端页面使用)")
    public R<HashMap<String,Object>> frontList()
    {
        startPage();
        List<TariffPackage> list = tariffPackageService.selectTariffPackageListClient();
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        dataMap.put("total",new PageInfo<>(list).getTotal());
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    @GetMapping("/subList")
    @ApiOperation("查询子套餐列表(后端页面使用)")
    @ApiImplicitParams(value ={
            @ApiImplicitParam(name = "parentId", value = "套餐ID", required = true, dataType = "string")
    })
    public R<List<TariffPackageSub>> subList(@RequestParam("parentId") String parentId)
    {
        List<TariffPackageSub> list = tariffPackageService.selectSubListByParentIdClient(parentId);
        return R.ok(list);
    }

    @GetMapping("/front/subList")
    @ApiOperation("查询子套餐列表(前端页面使用)")
    @ApiImplicitParams(value ={
            @ApiImplicitParam(name = "parentId", value = "套餐ID", required = true, dataType = "string")
    })
    public R<HashMap<String,Object>> frontSubList(@RequestParam(name = "parentId" ) String parentId)
    {
        List<TariffPackageSub> list = tariffPackageService.selectSubListByParentIdClient(parentId);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        dataMap.put("total",new PageInfo<>(list).getTotal());
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    @GetMapping("/canUpdatePackage")
    @ApiOperation("查询子套餐是否适合升级(前端页面使用)")
    @ApiImplicitParams(value ={
            @ApiImplicitParam(name = "packageId", value = "升级到套餐ID", required = true, dataType = "string"),
            @ApiImplicitParam(name = "subId", value = "升级到子套餐ID", required = true, dataType = "string"),
            @ApiImplicitParam(name = "type", value = "购买类型(0-加油包，1-套餐)", required = true, dataType = "long")
    })
    public R<CanUpdatePackageResponse> canUpdatePackage(@RequestParam(value = "packageId") String packageId,
                                                        @RequestParam(value = "subId") String subId,
                                                        @RequestParam(value = "type") long type,
                                                        HttpServletRequest request
                                                        ) throws Exception {
        String remoteAddr = request.getRemoteAddr();
        CanUpdatePackageResponse response = tariffPackageService.nativePay(packageId,subId,type,remoteAddr);
        return R.ok(response);
    }

    @GetMapping("/updatePackage")
    @ApiOperation("套餐升级(前端页面使用)")
    @ApiImplicitParams(value ={
            @ApiImplicitParam(name = "packageId", value = "升级到套餐ID", required = true, dataType = "string"),
            @ApiImplicitParam(name = "subId", value = "升级到子套餐ID", required = true, dataType = "string")
    })
    public AjaxResult updatePackage(@RequestParam("packageId" ) String packageId,
                                    @RequestParam("subId" ) String subId)
    {
        return success(tariffPackageService.updatePackage(packageId,subId));
    }

    /**
     * 查询套餐列表
     */
    @PostMapping("/listBy")
    @ApiOperation("条件查询套餐列表")
    public R<List<TariffPackage>> listBy(@RequestBody TariffPackage tariffPackage)
    {
        startPage();
        List<TariffPackage> list = tariffPackageService.selectTariffPackageList(tariffPackage);
        return R.ok(list);
    }

    /**
     * 导出套餐列表
     */
    @PreAuthorize("@ss.hasPermi('tariff-package:package:export')")
    @Log(title = "套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TariffPackage tariffPackage)
    {
        List<TariffPackage> list = tariffPackageService.selectTariffPackageList(tariffPackage);
        ExcelUtil<TariffPackage> util = new ExcelUtil<TariffPackage>(TariffPackage.class);
        util.exportExcel(response, list, "套餐数据");
    }

    /**
     * 获取套餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('tariff-package:package:query')")
    @GetMapping(value = "/{tariffPackageId}")
    public AjaxResult getInfo(@PathVariable("tariffPackageId") String tariffPackageId)
    {
        return success(tariffPackageService.selectTariffPackageByTariffPackageId(tariffPackageId));
    }

    /**
     * 新增套餐
     */
//    @PreAuthorize("@ss.hasPermi('tariff-package:package:add')")
    @Log(title = "套餐", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation(value = "新增套餐",hidden = true)
    public AjaxResult add(@RequestBody TariffPackage tariffPackage)
    {
        return toAjax(tariffPackageService.insertTariffPackage(tariffPackage));
    }

    /**
     * 修改套餐
     */
    @PreAuthorize("@ss.hasPermi('tariff-package:package:edit')")
    @Log(title = "套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TariffPackage tariffPackage)
    {
        return toAjax(tariffPackageService.updateTariffPackage(tariffPackage));
    }

    /**
     * 删除套餐
     */
    @PreAuthorize("@ss.hasPermi('tariff-package:package:remove')")
    @Log(title = "套餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tariffPackageIds}")
    public AjaxResult remove(@PathVariable String[] tariffPackageIds)
    {
        return toAjax(tariffPackageService.deleteTariffPackageByTariffPackageIds(tariffPackageIds));
    }
}
