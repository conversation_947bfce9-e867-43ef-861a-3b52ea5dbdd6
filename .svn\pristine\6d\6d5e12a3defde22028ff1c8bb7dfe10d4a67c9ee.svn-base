package com.dataxai.web.controller.platform;

import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.domain.Team;
import com.dataxai.domain.TUser;
import com.dataxai.domain.dto.CreateTeamDTO;
import com.dataxai.domain.dto.BatchAddTeamMemberDTO;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.service.ITeamService;
import com.dataxai.web.service.IFrontUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.uuid.UUID;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * 平台API 用户/团队管理控制器
 */
@Slf4j
@Api(tags = "平台API-用户/团队管理接口")
@RestController
@RequestMapping("/platform/user")
public class PlatformUserController extends BaseController {

    @Autowired
    private TUserMapper tUserMapper;

    @Autowired
    private ITeamService teamService;

    @Autowired
    private IFrontUserService frontUserService;

    /**
     * 平台API-创建团队（使用 ClientCode 识别用户）
     *
     * 流程：
     * 1) 通过网关/拦截器基于请求头 ClientCode 解析并注入 platformUser。
     * 2) 若该用户尚未加入团队（t_user.team_id <= 0），则按入参创建团队。
     * 3) 返回该用户的团队信息。
     */
    @ApiOperation("创建团队")
    @PostMapping("/createTeam")
    @Transactional
    public R<Map<String, Object>> createTeam(
            HttpServletRequest request,
            @ApiParam("创建团队参数") @RequestBody PlatformCreateTeamRequest req
    ) {
        try {
            // 平台用户校验（由拦截器通过 ClientCode 注入）
            TUser targetUser = (TUser) request.getAttribute("platformUser");
            if (targetUser == null) {
                return R.fail("用户未授权");
            }

            // 若无团队则创建
            Team team;
            if (targetUser.getTeamId() == null || targetUser.getTeamId() <= 0) {
                CreateTeamDTO dto = new CreateTeamDTO();
                dto.setTeamName(req.getTeamName());
                dto.setIndustry(req.getIndustry());
                dto.setNickname(req.getNickname());
                dto.setProfession(req.getProfession());
                team = teamService.createTeam(dto, targetUser.getUserId());
            } else {
                team = teamService.selectTeamById(targetUser.getTeamId());
            }

            Map<String, Object> data = new HashMap<>();
            data.put("team", team);
            return R.ok(data, "操作成功");
        } catch (Exception e) {
            log.error("平台API创建团队失败", e);
            return R.fail("创建团队失败: " + e.getMessage());
        }
    }

    /**
     * 平台API-为当前平台用户所属团队批量添加成员
     */
    @ApiOperation("批量添加团队成员")
    @PostMapping("/addMembers")
    @Transactional
    public R<Map<String, Object>> addMembers(
            HttpServletRequest request,
            @ApiParam("批量添加成员参数，仅需提供手机号数组") @RequestBody BatchAddTeamMemberDTO batchAddDTO
    ) {
        try {
            // 平台用户校验
            TUser platformUser = (TUser) request.getAttribute("platformUser");
            if (platformUser == null) {
                return R.fail("用户未授权");
            }

            if (platformUser.getTeamId() == null || platformUser.getTeamId() <= 0) {
                return R.fail("当前用户未加入任何团队");
            }

            if (batchAddDTO == null || batchAddDTO.getPhoneNumbers() == null || batchAddDTO.getPhoneNumbers().isEmpty()) {
                return R.fail("phoneNumbers不能为空");
            }

            Map<String, Object> serviceResult = teamService.batchAddTeamMembersNew(batchAddDTO, platformUser.getUserId());

            // 收集错误手机号集合
            List<Map<String, String>> errorList = (List<Map<String, String>>) serviceResult.get("errorList");
            if (errorList == null) {
                errorList = new ArrayList<>();
            }
            Set<String> errorMobiles = new HashSet<>();
            for (Map<String, String> err : errorList) {
                if (err != null && err.get("mobile") != null) {
                    errorMobiles.add(err.get("mobile"));
                }
            }

            // 成功手机号为入参减去错误手机号
            List<Map<String, Object>> successList = new ArrayList<>();
            for (String mobile : batchAddDTO.getPhoneNumbers()) {
                if (mobile == null) {
                    continue;
                }
                if (errorMobiles.contains(mobile)) {
                    continue;
                }
                // 查询并补齐 client_code / client_secret
                TUser user = null;
                try {
                    com.dataxai.common.core.domain.model.User baseUser = tUserMapper.selectUserByPhone(mobile);
                    if (baseUser != null) {
                        user = tUserMapper.selectTUserById(baseUser.getUserId());
                    }
                } catch (Exception ignore) { }

                if (user != null) {
                    boolean needUpdate = false;
                    if (StringUtils.isEmpty(user.getClientCode())) {
                        user.setClientCode(generateClientCode());
                        needUpdate = true;
                    }
                    if (StringUtils.isEmpty(user.getClientSecret())) {
                        user.setClientSecret(generateClientSecret());
                        needUpdate = true;
                    }
                    if (needUpdate) {
                        user.setUpdateTime(DateUtils.getNowDate());
                        try {
                            tUserMapper.updateTUser(user);
                        } catch (Exception e) {
                            // 即便更新失败，仍返回已有信息
                            log.warn("更新用户授权信息失败, mobile={}", mobile, e);
                        }
                    }

                    Map<String, Object> item = new HashMap<>();
                    item.put("mobile", mobile);
                    item.put("client_code", user.getClientCode());
                    item.put("client_secret", user.getClientSecret());
                    successList.add(item);
                }
            }

            int successCount = serviceResult.get("successCount") instanceof Integer ? (Integer) serviceResult.get("successCount") : successList.size();
            int errorCount = serviceResult.get("errorCount") instanceof Integer ? (Integer) serviceResult.get("errorCount") : errorList.size();

            Map<String, Object> data = new HashMap<>();
            data.put("successList", successList);
            data.put("errorList", errorList);
            data.put("successCount", successCount);
            data.put("errorCount", errorCount);

            return R.ok(data, "操作成功");
        } catch (Exception e) {
            log.error("平台API批量添加成员失败", e);
            return R.fail("批量添加失败: " + e.getMessage());
        }
    }

    private String generateClientCode() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
    }

    private String generateClientSecret() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    @Data
    public static class PlatformCreateTeamRequest {
        private String teamName;
        private String nickname;
        private String industry;
        private String profession;
    }
}