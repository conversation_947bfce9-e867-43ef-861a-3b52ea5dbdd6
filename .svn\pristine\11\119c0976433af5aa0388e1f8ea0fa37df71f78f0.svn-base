"use strict";(self.webpackChunkai_console=self.webpackChunkai_console||[]).push([[122],{11749:function(){},54062:function(e,t,n){n.d(t,{Ay:function(){return f}});var i=n(27569),r=n(91161),o=n(68305),a=n(6821),h=n(94862),d=n(33801),c=n(68887),s=n(9939),l={x:0,y:0,width:0,height:0,unit:"px"},w=function(e,t,n){return Math.min(Math.max(e,t),n)},p=function(e,t){return e===t||e.width===t.width&&e.height===t.height&&e.x===t.x&&e.y===t.y&&e.unit===t.unit};function g(e,t,n){return"%"===e.unit?(0,c.A)((0,c.A)((0,c.A)({},l),e),{},{unit:"%"}):{unit:"%",x:e.x?e.x/t*100:0,y:e.y?e.y/n*100:0,width:e.width?e.width/t*100:0,height:e.height?e.height/n*100:0}}function u(e,t,n){return e.unit?"px"===e.unit?(0,c.A)((0,c.A)((0,c.A)({},l),e),{},{unit:"px"}):{unit:"px",x:e.x?e.x*t/100:0,y:e.y?e.y*n/100:0,width:e.width?e.width*t/100:0,height:e.height?e.height*n/100:0}:(0,c.A)((0,c.A)((0,c.A)({},l),e),{},{unit:"px"})}function v(e,t,n,i,r){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0,h=arguments.length>7&&void 0!==arguments[7]?arguments[7]:i,d=arguments.length>8&&void 0!==arguments[8]?arguments[8]:r,s=(0,c.A)({},e),l=Math.min(o,i),w=Math.min(a,r),p=Math.min(h,i),g=Math.min(d,r);t&&(t>1?(w=(l=a?a*t:l)/t,p=h*t):(l=(w=o?o/t:w)*t,g=d/t)),s.y<0&&(s.height=Math.max(s.height+s.y,w),s.y=0),s.x<0&&(s.width=Math.max(s.width+s.x,l),s.x=0);var u=i-(s.x+s.width);u<0&&(s.x=Math.min(s.x,i-l),s.width+=u);var v=r-(s.y+s.height);if(v<0&&(s.y=Math.min(s.y,r-w),s.height+=v),s.width<l&&(("sw"===n||"nw"==n)&&(s.x-=l-s.width),s.width=l),s.height<w&&(("nw"===n||"ne"==n)&&(s.y-=w-s.height),s.height=w),s.width>p&&(("sw"===n||"nw"==n)&&(s.x-=p-s.width),s.width=p),s.height>g&&(("nw"===n||"ne"==n)&&(s.y-=g-s.height),s.height=g),t){var m=s.width/s.height;if(m<t){var y=Math.max(s.width/t,w);("nw"===n||"ne"==n)&&(s.y-=y-s.height),s.height=y}else if(m>t){var x=Math.max(s.height*t,l);("sw"===n||"nw"==n)&&(s.x-=x-s.width),s.width=x}}return s}var m={capture:!0,passive:!1},y=0,x=function(e){function t(){var e;return(0,r.A)(this,t),(e=(0,h.A)(this,t,arguments)).docMoveBound=!1,e.mouseDownOnCrop=!1,e.dragStarted=!1,e.evData={startClientX:0,startClientY:0,startCropX:0,startCropY:0,clientX:0,clientY:0,isResize:!0},e.componentRef=(0,s.createRef)(),e.mediaRef=(0,s.createRef)(),e.initChangeCalled=!1,e.instanceId="rc-".concat(y++),e.state={cropIsActive:!1,newCropIsBeingDrawn:!1},e.onCropPointerDown=function(t){var n=e.props,i=n.crop,r=n.disabled,o=e.getBox();if(i){var a=u(i,o.width,o.height);if(!r){t.cancelable&&t.preventDefault(),e.bindDocMove(),e.componentRef.current.focus({preventScroll:!0});var h=t.target.dataset.ord,d=!!h,c=t.clientX,s=t.clientY,l=a.x,w=a.y;if(h){var p=t.clientX-o.x,g=t.clientY-o.y,v=0,m=0;"ne"===h||"e"==h?(v=p-(a.x+a.width),m=g-a.y,l=a.x,w=a.y+a.height):"se"===h||"s"===h?(v=p-(a.x+a.width),m=g-(a.y+a.height),l=a.x,w=a.y):"sw"===h||"w"==h?(v=p-a.x,m=g-(a.y+a.height),l=a.x+a.width,w=a.y):("nw"===h||"n"==h)&&(v=p-a.x,m=g-a.y,l=a.x+a.width,w=a.y+a.height),c=l+o.x+v,s=w+o.y+m}e.evData={startClientX:c,startClientY:s,startCropX:l,startCropY:w,clientX:t.clientX,clientY:t.clientY,isResize:d,ord:h},e.mouseDownOnCrop=!0,e.setState({cropIsActive:!0})}}},e.onComponentPointerDown=function(t){var n=e.props,i=n.crop,r=n.disabled,o=n.locked,a=n.keepSelection,h=n.onChange,d=e.getBox();if(!(r||o||a&&i)){t.cancelable&&t.preventDefault(),e.bindDocMove(),e.componentRef.current.focus({preventScroll:!0});var c=t.clientX-d.x,s=t.clientY-d.y,l={unit:"px",x:c,y:s,width:0,height:0};e.evData={startClientX:t.clientX,startClientY:t.clientY,startCropX:c,startCropY:s,clientX:t.clientX,clientY:t.clientY,isResize:!0},e.mouseDownOnCrop=!0,h(u(l,d.width,d.height),g(l,d.width,d.height)),e.setState({cropIsActive:!0,newCropIsBeingDrawn:!0})}},e.onDocPointerMove=function(t){var n=e.props,i=n.crop,r=n.disabled,o=n.onChange,h=n.onDragStart,d=e.getBox();if(!r&&i&&e.mouseDownOnCrop){t.cancelable&&t.preventDefault(),e.dragStarted||(e.dragStarted=!0,h&&h(t));var c,s=(0,a.A)(e).evData;s.clientX=t.clientX,s.clientY=t.clientY,c=s.isResize?e.resizeCrop():e.dragCrop(),p(i,c)||o(u(c,d.width,d.height),g(c,d.width,d.height))}},e.onComponentKeyDown=function(n){var i=e.props,r=i.crop,o=i.disabled,a=i.onChange,h=i.onComplete;if(!o){var d=n.key,c=!1;if(r){var s=e.getBox(),l=e.makePixelCrop(s),p=(navigator.platform.match("Mac")?n.metaKey:n.ctrlKey)?t.nudgeStepLarge:n.shiftKey?t.nudgeStepMedium:t.nudgeStep;if("ArrowLeft"===d?(l.x-=p,c=!0):"ArrowRight"===d?(l.x+=p,c=!0):"ArrowUp"===d?(l.y-=p,c=!0):"ArrowDown"===d&&(l.y+=p,c=!0),c){n.cancelable&&n.preventDefault(),l.x=w(l.x,0,s.width-l.width),l.y=w(l.y,0,s.height-l.height);var v=u(l,s.width,s.height),m=g(l,s.width,s.height);a(v,m),h&&h(v,m)}}}},e.onHandlerKeyDown=function(n,i){var r=e.props,o=r.aspect,a=void 0===o?0:o,h=r.crop,d=r.disabled,s=r.minWidth,l=void 0===s?0:s,w=r.minHeight,m=void 0===w?0:w,y=r.maxWidth,x=r.maxHeight,f=r.onChange,C=r.onComplete,D=e.getBox();if(!d&&h&&("ArrowUp"===n.key||"ArrowDown"===n.key||"ArrowLeft"===n.key||"ArrowRight"===n.key)){n.stopPropagation(),n.preventDefault();var b=(navigator.platform.match("Mac")?n.metaKey:n.ctrlKey)?t.nudgeStepLarge:n.shiftKey?t.nudgeStepMedium:t.nudgeStep,R=function(e,t,n,i){var r=(0,c.A)({},e);return"ArrowLeft"===t?"nw"===i?(r.x-=n,r.y-=n,r.width+=n,r.height+=n):"w"===i?(r.x-=n,r.width+=n):"sw"===i?(r.x-=n,r.width+=n,r.height+=n):"ne"===i?(r.y+=n,r.width-=n,r.height-=n):"e"===i?r.width-=n:"se"===i&&(r.width-=n,r.height-=n):"ArrowRight"===t&&("nw"===i?(r.x+=n,r.y+=n,r.width-=n,r.height-=n):"w"===i?(r.x+=n,r.width-=n):"sw"===i?(r.x+=n,r.width-=n,r.height-=n):"ne"===i?(r.y-=n,r.width+=n,r.height+=n):"e"===i?r.width+=n:"se"===i&&(r.width+=n,r.height+=n)),"ArrowUp"===t?"nw"===i?(r.x-=n,r.y-=n,r.width+=n,r.height+=n):"n"===i?(r.y-=n,r.height+=n):"ne"===i?(r.y-=n,r.width+=n,r.height+=n):"sw"===i?(r.x+=n,r.width-=n,r.height-=n):"s"===i?r.height-=n:"se"===i&&(r.width-=n,r.height-=n):"ArrowDown"===t&&("nw"===i?(r.x+=n,r.y+=n,r.width-=n,r.height-=n):"n"===i?(r.y+=n,r.height-=n):"ne"===i?(r.y+=n,r.width-=n,r.height-=n):"sw"===i?(r.x-=n,r.width+=n,r.height+=n):"s"===i?r.height+=n:"se"===i&&(r.width+=n,r.height+=n)),r}(u(h,D.width,D.height),n.key,b,i),k=v(R,a,i,D.width,D.height,l,m,y,x);if(!p(h,k)){var A=g(k,D.width,D.height);f(k,A),C&&C(k,A)}}},e.onDocPointerDone=function(t){var n=e.props,i=n.crop,r=n.disabled,o=n.onComplete,a=n.onDragEnd,h=e.getBox();e.unbindDocMove(),!r&&i&&e.mouseDownOnCrop&&(e.mouseDownOnCrop=!1,e.dragStarted=!1,a&&a(t),o&&o(u(i,h.width,h.height),g(i,h.width,h.height)),e.setState({cropIsActive:!1,newCropIsBeingDrawn:!1}))},e.onDragFocus=function(){var t;null==(t=e.componentRef.current)||t.scrollTo(0,0)},e}return(0,d.A)(t,e),(0,o.A)(t,[{key:"document",get:function(){return document}},{key:"getBox",value:function(){var e=this.mediaRef.current;if(!e)return{x:0,y:0,width:0,height:0};var t=e.getBoundingClientRect();return{x:t.x,y:t.y,width:t.width,height:t.height}}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.crop,i=t.onComplete;if(i&&!e.crop&&n){var r=this.getBox(),o=r.width,a=r.height;o&&a&&i(u(n,o,a),g(n,o,a))}}},{key:"componentWillUnmount",value:function(){this.resizeObserver&&this.resizeObserver.disconnect(),this.unbindDocMove()}},{key:"bindDocMove",value:function(){this.docMoveBound||(this.document.addEventListener("pointermove",this.onDocPointerMove,m),this.document.addEventListener("pointerup",this.onDocPointerDone,m),this.document.addEventListener("pointercancel",this.onDocPointerDone,m),this.docMoveBound=!0)}},{key:"unbindDocMove",value:function(){this.docMoveBound&&(this.document.removeEventListener("pointermove",this.onDocPointerMove,m),this.document.removeEventListener("pointerup",this.onDocPointerDone,m),this.document.removeEventListener("pointercancel",this.onDocPointerDone,m),this.docMoveBound=!1)}},{key:"getCropStyle",value:function(){var e=this.props.crop;if(e)return{top:"".concat(e.y).concat(e.unit),left:"".concat(e.x).concat(e.unit),width:"".concat(e.width).concat(e.unit),height:"".concat(e.height).concat(e.unit)}}},{key:"dragCrop",value:function(){var e=this.evData,t=this.getBox(),n=this.makePixelCrop(t),i=e.clientX-e.startClientX,r=e.clientY-e.startClientY;return n.x=w(e.startCropX+i,0,t.width-n.width),n.y=w(e.startCropY+r,0,t.height-n.height),n}},{key:"getPointRegion",value:function(e,t,n,i){var r,o=this.evData,a=o.clientX-e.x,h=o.clientY-e.y;return r=i&&t?"nw"===t||"n"===t||"ne"===t:h<o.startCropY,(n&&t?"nw"===t||"w"===t||"sw"===t:a<o.startCropX)?r?"nw":"sw":r?"ne":"se"}},{key:"resolveMinDimensions",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=Math.min(n,e.width),o=Math.min(i,e.height);return t&&(r||o)?t>1?r?[r,r/t]:[o*t,o]:o?[o*t,o]:[r,r/t]:[r,o]}},{key:"resizeCrop",value:function(){var e=this.evData,n=this.props,r=n.aspect,o=void 0===r?0:r,a=n.maxWidth,h=n.maxHeight,d=this.getBox(),c=this.resolveMinDimensions(d,o,this.props.minWidth,this.props.minHeight),s=(0,i.A)(c,2),l=s[0],p=s[1],g=this.makePixelCrop(d),u=this.getPointRegion(d,e.ord,l,p),m=e.ord||u,y=e.clientX-e.startClientX,x=e.clientY-e.startClientY;(l&&"nw"===m||"w"===m||"sw"===m)&&(y=Math.min(y,-l)),(p&&"nw"===m||"n"===m||"ne"===m)&&(x=Math.min(x,-p));var f={unit:"px",x:0,y:0,width:0,height:0};"ne"===u?(f.x=e.startCropX,f.width=y,o?(f.height=f.width/o,f.y=e.startCropY-f.height):(f.height=Math.abs(x),f.y=e.startCropY-f.height)):"se"===u?(f.x=e.startCropX,f.y=e.startCropY,f.width=y,f.height=o?f.width/o:x):"sw"===u?(f.x=e.startCropX+y,f.y=e.startCropY,f.width=Math.abs(y),f.height=o?f.width/o:x):"nw"===u&&(f.x=e.startCropX+y,f.width=Math.abs(y),o?(f.height=f.width/o,f.y=e.startCropY-f.height):(f.height=Math.abs(x),f.y=e.startCropY+x));var C=v(f,o,u,d.width,d.height,l,p,a,h);return o||t.xyOrds.indexOf(m)>-1?g=C:t.xOrds.indexOf(m)>-1?(g.x=C.x,g.width=C.width):t.yOrds.indexOf(m)>-1&&(g.y=C.y,g.height=C.height),g.x=w(g.x,0,d.width-g.width),g.y=w(g.y,0,d.height-g.height),g}},{key:"renderCropSelection",value:function(){var e=this,n=this.props,i=n.ariaLabels,r=void 0===i?t.defaultProps.ariaLabels:i,o=n.disabled,a=n.locked,h=n.renderSelectionAddon,d=n.ruleOfThirds,c=n.crop,l=this.getCropStyle();if(c)return s.createElement("div",{style:l,className:"ReactCrop__crop-selection",onPointerDown:this.onCropPointerDown,"aria-label":r.cropArea,tabIndex:0,onKeyDown:this.onComponentKeyDown,role:"group"},!o&&!a&&s.createElement("div",{className:"ReactCrop__drag-elements",onFocus:this.onDragFocus},s.createElement("div",{className:"ReactCrop__drag-bar ord-n","data-ord":"n"}),s.createElement("div",{className:"ReactCrop__drag-bar ord-e","data-ord":"e"}),s.createElement("div",{className:"ReactCrop__drag-bar ord-s","data-ord":"s"}),s.createElement("div",{className:"ReactCrop__drag-bar ord-w","data-ord":"w"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-nw","data-ord":"nw",tabIndex:0,"aria-label":r.nwDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"nw")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-n","data-ord":"n",tabIndex:0,"aria-label":r.nDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"n")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-ne","data-ord":"ne",tabIndex:0,"aria-label":r.neDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"ne")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-e","data-ord":"e",tabIndex:0,"aria-label":r.eDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"e")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-se","data-ord":"se",tabIndex:0,"aria-label":r.seDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"se")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-s","data-ord":"s",tabIndex:0,"aria-label":r.sDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"s")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-sw","data-ord":"sw",tabIndex:0,"aria-label":r.swDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"sw")},role:"button"}),s.createElement("div",{className:"ReactCrop__drag-handle ord-w","data-ord":"w",tabIndex:0,"aria-label":r.wDragHandle,onKeyDown:function(t){return e.onHandlerKeyDown(t,"w")},role:"button"})),h&&s.createElement("div",{className:"ReactCrop__selection-addon",onPointerDown:function(e){return e.stopPropagation()}},h(this.state)),d&&s.createElement(s.Fragment,null,s.createElement("div",{className:"ReactCrop__rule-of-thirds-hz"}),s.createElement("div",{className:"ReactCrop__rule-of-thirds-vt"})))}},{key:"makePixelCrop",value:function(e){return u((0,c.A)((0,c.A)({},l),this.props.crop||{}),e.width,e.height)}},{key:"render",value:function(){var e=this.props,t=e.aspect,n=e.children,i=e.circularCrop,r=e.className,o=e.crop,a=e.disabled,h=e.locked,d=e.style,c=e.ruleOfThirds,l=this.state,w=l.cropIsActive,p=l.newCropIsBeingDrawn,g=o?this.renderCropSelection():null,u=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((function(e){return e&&"string"==typeof e})).join(" ")}("ReactCrop",r,w&&"ReactCrop--active",a&&"ReactCrop--disabled",h&&"ReactCrop--locked",p&&"ReactCrop--new-crop",o&&t&&"ReactCrop--fixed-aspect",o&&i&&"ReactCrop--circular-crop",o&&c&&"ReactCrop--rule-of-thirds",!this.dragStarted&&o&&!o.width&&!o.height&&"ReactCrop--invisible-crop",i&&"ReactCrop--no-animate");return s.createElement("div",{ref:this.componentRef,className:u,style:d},s.createElement("div",{ref:this.mediaRef,className:"ReactCrop__child-wrapper",onPointerDown:this.onComponentPointerDown},n),o?s.createElement("svg",{className:"ReactCrop__crop-mask",width:"100%",height:"100%"},s.createElement("defs",null,s.createElement("mask",{id:"hole-".concat(this.instanceId)},s.createElement("rect",{width:"100%",height:"100%",fill:"white"}),i?s.createElement("ellipse",{cx:"".concat(o.x+o.width/2).concat(o.unit),cy:"".concat(o.y+o.height/2).concat(o.unit),rx:"".concat(o.width/2).concat(o.unit),ry:"".concat(o.height/2).concat(o.unit),fill:"black"}):s.createElement("rect",{x:"".concat(o.x).concat(o.unit),y:"".concat(o.y).concat(o.unit),width:"".concat(o.width).concat(o.unit),height:"".concat(o.height).concat(o.unit),fill:"black"}))),s.createElement("rect",{fill:"black",fillOpacity:.5,width:"100%",height:"100%",mask:"url(#hole-".concat(this.instanceId,")")})):void 0,g)}}])}(s.PureComponent);x.xOrds=["e","w"],x.yOrds=["n","s"],x.xyOrds=["nw","ne","se","sw"],x.nudgeStep=1,x.nudgeStepMedium=10,x.nudgeStepLarge=100,x.defaultProps={ariaLabels:{cropArea:"Use the arrow keys to move the crop selection area",nwDragHandle:"Use the arrow keys to move the north west drag handle to change the crop selection area",nDragHandle:"Use the up and down arrow keys to move the north drag handle to change the crop selection area",neDragHandle:"Use the arrow keys to move the north east drag handle to change the crop selection area",eDragHandle:"Use the up and down arrow keys to move the east drag handle to change the crop selection area",seDragHandle:"Use the arrow keys to move the south east drag handle to change the crop selection area",sDragHandle:"Use the up and down arrow keys to move the south drag handle to change the crop selection area",swDragHandle:"Use the arrow keys to move the south west drag handle to change the crop selection area",wDragHandle:"Use the up and down arrow keys to move the west drag handle to change the crop selection area"}};var f=x}}]);
//# sourceMappingURL=122.aea6f7c1.chunk.js.map