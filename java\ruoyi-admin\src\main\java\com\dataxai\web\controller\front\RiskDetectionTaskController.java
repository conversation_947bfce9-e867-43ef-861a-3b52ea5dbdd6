package com.dataxai.web.controller.front;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.page.TableDataInfo;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.RiskDetectionTask;
import com.dataxai.domain.RiskDetectionTaskDetail;
import com.dataxai.domain.ProductInfo;
import com.dataxai.domain.TeamUser;
import com.dataxai.domain.dto.RiskDetectionTaskDetailDTO;
import com.dataxai.mapper.RiskDetectionTaskDetailMapper;
import com.dataxai.service.IRiskDetectionTaskService;
import com.dataxai.service.IRiskDetectionTaskDetailService;
import com.dataxai.service.IProductInfoService;
import com.dataxai.service.ITeamUserService;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.service.IOrdinalImgResultService;
import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.web.task.core.TaskScoreService;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 风险检测任务表Controller
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Api(tags = "风险检测任务")
@RestController
@RequestMapping("/risk/detection/task")
public class RiskDetectionTaskController extends BaseController<RiskDetectionTask> {
    @Autowired
    private IRiskDetectionTaskService riskDetectionTaskService;

    @Autowired
    private IRiskDetectionTaskDetailService riskDetectionTaskDetailService;

    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private IOrdinalImgResultService ordinalImgResultService;

    @Autowired
    private RiskDetectionImageUploadService riskDetectionImageUploadService;

    @Autowired
    private TaskScoreService taskScoreService;

    @Autowired
    private UserTeamInfoService userTeamInfoService;

    @Autowired
    private IProductInfoService iProductInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;

    @Autowired
    private RiskDetectionTaskDetailMapper riskDetectionTaskDetailMapper;

    /**
     * 查询风险检测任务表列表
     */
    @ApiOperation("查询风险检测任务列表")
    @GetMapping("/list")
    public AjaxResult list(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "batchNumber", required = false) String batchNumber,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "remark", required = false) String remark
    ) {

        // 构造查询对象
        RiskDetectionTask query = new RiskDetectionTask();

        // 根据用户模式设置数据过滤条件
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {

            // 团队模式：直接通过team_id过滤
            Long LoginUserId = SecurityUtils.getUserId();

            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(LoginUserId);
            if (teamUser.getIsAdmin() == true) {
                Long selectUserId = userId;
                if (selectUserId != null) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(selectUserId);
                } else {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                }
            } else {
                query.setTeamId(userTeamInfo.getTeamId());
                query.setOwnerId(LoginUserId);
            }
        } else {
            // 个人模式：通过owner_id过滤，且team_id为0
            query.setOwnerId(getUserId());
            query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
        }

        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setStatus(status);
        query.setTaskBatch(batchNumber);
        query.setRemark(remark);
        startPage();
        List<RiskDetectionTask> list = riskDetectionTaskService.selectRiskDetectionTaskList(query);
        // 组装自定义分页结构
        PageInfo<RiskDetectionTask> pageInfo = new PageInfo<>(list);
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", pageInfo.getTotal());
        pageData.put("data", pageInfo.getList());

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 获取风险检测任务基本信息（包含备注）
     */
    @ApiOperation("获取风险检测任务基本信息")
    @GetMapping(value = "/basic/{id}")
    public AjaxResult getBasicInfo(@PathVariable("id") Long id) {
        RiskDetectionTask riskDetectionTask = riskDetectionTaskService.selectRiskDetectionTaskById(id);
        if (riskDetectionTask == null) {
            return AjaxResult.error("风险检测任务不存在");
        }

        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (!userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() == null) {
            if (!riskDetectionTask.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限查看此风险检测任务");
            }
        }

        return AjaxResult.success(riskDetectionTask);
    }

    /**
     * 获取风险检测任务详细信息
     */
    @ApiOperation("获取风险检测任务详细信息，支持按风险等级筛选，多个等级用逗号分隔，如：高,中,低，支持分页查询")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id,
                              @RequestParam(value = "riskLevel", required = false) String riskLevel) {
        RiskDetectionTask riskDetectionTask = riskDetectionTaskService.selectRiskDetectionTaskById(id);
        if (riskDetectionTask == null) {
            return AjaxResult.error("风险检测任务不存在");
        }
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (!userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() == null) {
            if (!riskDetectionTask.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限查看此风险检测任务");
            }
        }

        // 查询任务详情列表，支持风险等级筛选和分页
        List<RiskDetectionTaskDetail> detailList;
        if (riskLevel != null && !riskLevel.trim().isEmpty()) {
            // 支持多个风险等级（用逗号分隔），同时支持分页
            String[] riskLevels = riskLevel.trim().split(",");
            List<String> levelList = new ArrayList<>();

            for (String level : riskLevels) {
                String trimmedLevel = level.trim();
                if (!trimmedLevel.isEmpty()) {
                    levelList.add(trimmedLevel);
                }
            }

            // 使用分页查询
            startPage();
//            PageHelper.startPage(1, 200);
            detailList = riskDetectionTaskDetailMapper.selectRiskDetectionTaskDetailByTaskIdAndRiskLevels(id, levelList);
        } else {
            // 查询所有详情，支持分页
            startPage();
//            PageHelper.startPage(1, 200);
            detailList = riskDetectionTaskDetailService.selectRiskDetectionTaskDetailByTaskId(id);
        }
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(detailList).getTotal());
// 1. 放入任务基本信息
        pageData.put("id", riskDetectionTask.getId());
        pageData.put("batchNumber", riskDetectionTask.getTaskBatch());
        pageData.put("totalAmount", riskDetectionTask.getTotalAmount());
        pageData.put("successAmount", riskDetectionTask.getSuccessAmount());
        pageData.put("failAmount", riskDetectionTask.getFailAmount());
        pageData.put("status", riskDetectionTask.getStatus());
        pageData.put("ownerId", riskDetectionTask.getOwnerId());
        pageData.put("createTime", riskDetectionTask.getCreateTime());
        pageData.put("updateTime", riskDetectionTask.getUpdateTime());
// 2. 放入详情信息
        pageData.put("detailList", new PageInfo<>(detailList).getList());
        pageData.put("detailCount", detailList.size());

// 3. 放入分页信息
//        pageData.put("pageNum", 1);
//        pageData.put("pageSize", 200);
        PageInfo<RiskDetectionTaskDetail> pageInfo = new PageInfo<>(detailList);
        RiskDetectionTaskDetailDTO detailDTO = new RiskDetectionTaskDetailDTO();
        detailDTO.setId(riskDetectionTask.getId());
        detailDTO.setTaskBatch(riskDetectionTask.getTaskBatch());
        detailDTO.setTotalAmount(riskDetectionTask.getTotalAmount());
        detailDTO.setSuccessAmount(riskDetectionTask.getSuccessAmount());
        detailDTO.setFailAmount(riskDetectionTask.getFailAmount());
        detailDTO.setStatus(riskDetectionTask.getStatus());
        detailDTO.setOwnerId(riskDetectionTask.getOwnerId());
        detailDTO.setCreateTime(riskDetectionTask.getCreateTime());
        detailDTO.setUpdateTime(riskDetectionTask.getUpdateTime());
        detailDTO.setRemark(riskDetectionTask.getRemark());
        detailDTO.setTotal(Long.valueOf(pageInfo.getTotal()).intValue());
        detailDTO.setPageNum(pageInfo.getPageNum());
        detailDTO.setPageSize(pageInfo.getPageSize());
        detailDTO.setPages(Long.valueOf(pageInfo.getPages()).intValue());
//        detailDTO.setDetailList();

        return AjaxResult.success(pageData);
    }

    /**
     * 通过图片文件创建风险检测任务
     */
    @ApiOperation("通过图片文件创建风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByImages")
    public AjaxResult createByImages(@RequestParam(value = "files", required = false) List<MultipartFile> files,
                                     @RequestParam(value = "files[]", required = false) List<MultipartFile> filesArray) {
        // 优先使用files，如果为空则使用files[]
        List<MultipartFile> fileList = files;
        if (fileList == null || fileList.isEmpty()) {
            fileList = filesArray;
        }

        if (fileList == null || fileList.isEmpty()) {
            return AjaxResult.error("图片文件不能为空");
        }

        try {
            logger.info("开始通过图片文件创建风险检测任务，文件数量: {}", fileList.size());
            TaskLogUtils.RiskFilter.info("开始通过图片文件创建风险检测任务，文件数量: {}", fileList.size());

            // 1. 扣除积分（每个详情扣1分）
            Long totalPoints = (long) fileList.size();
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, TaskLogUtils.TASK_TYPE_RISK_FILTER);
            if (!deductioned) {
                TaskLogUtils.RiskFilter.warn("积分不足，无法创建风险检测任务，用户ID: {}", getUserId());
                return AjaxResult.error("积分不足，无法创建风险检测任务");
            }

            // 2. 上传图片到阿里云
            logger.info("开始上传图片到阿里云...");
            TaskLogUtils.RiskFilter.info("开始上传图片到阿里云...");
            List<String> imageUrls = riskDetectionImageUploadService.uploadRiskImages(fileList);
            logger.info("图片上传完成，成功上传 {} 个文件", imageUrls.size());
            TaskLogUtils.RiskFilter.info("图片上传完成，成功上传 {} 个文件", imageUrls.size());

            if (imageUrls.isEmpty()) {
                logger.error("没有图片上传成功，无法创建风险检测任务");
                TaskLogUtils.RiskFilter.error("没有图片上传成功，无法创建风险检测任务");
                return AjaxResult.error("图片上传失败，无法创建风险检测任务");
            }

            // 2. 创建风险检测任务
            RiskDetectionTask task = new RiskDetectionTask();
            task.setOwnerId(getUserId());
            task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());

            }
            int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
            if (taskResult <= 0) {
                return AjaxResult.error("创建风险检测任务失败");
            }

            // 3. 创建风险检测任务详情
            List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrl);
                detail.setType(3); // 3-来源为直接上传的图片
                detail.setTypeId(null); // 直接上传的图片，type_id为空
                detail.setProcessStatus(1); // 待处理
                detail.setOwnerId(getUserId());
                detail.setImageStatus(0);
                detailList.add(detail);

            }

            int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("successDetails", detailResult);

            return AjaxResult.success("创建风险检测任务成功", result);

        } catch (Exception e) {
            logger.error("通过图片文件创建风险检测任务失败", e);
            return AjaxResult.error("创建风险检测任务失败：" + e.getMessage());
        }
    }

    /**
     * 通过图片链接创建风险检测任务
     */
    @ApiOperation("通过图片链接创建风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByImageUrls")
    public AjaxResult createByImageUrls(@RequestBody Object requestData) {
        List<String> imageUrls = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        List<Long> productId = new ArrayList<>();
        Map<?, ?> map = (Map<?, ?>) requestData;
        Object urlsObj = map.get("imageUrls");
        Object idList1 = map.get("idsList");
        if (map.get("imageUrls") != null) {
            // 处理不同的请求数据格式
            if (requestData instanceof List) {
                // 如果是List，直接转换
                List<?> list = (List<?>) requestData;
                for (Object item : list) {
                    if (item instanceof String) {
                        imageUrls.add((String) item);
                    } else if (item != null) {
                        imageUrls.add(item.toString());
                    }
                }
            } else if (requestData instanceof Map) {
                if (urlsObj instanceof List) {
                    List<?> list = (List<?>) urlsObj;
                    for (Object item : list) {
                        if (item instanceof String) {
                            imageUrls.add((String) item);
                        } else if (item != null) {
                            imageUrls.add(item.toString());
                        }
                    }
                }
            }
            if (imageUrls.isEmpty()) {
                return AjaxResult.error("图片链接列表不能为空");
            }
            try {
                logger.info("开始通过图片链接创建风险检测任务，链接数量: {}", imageUrls.size());

                // 过滤有效的图片链接
                List<String> validImageUrls = new ArrayList<>();
                for (String imageUrl : imageUrls) {
                    if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                        validImageUrls.add(imageUrl.trim());
                    }
                }

                if (validImageUrls.isEmpty()) {
                    logger.error("没有有效的图片链接，无法创建风险检测任务");
                    return AjaxResult.error("没有有效的图片链接，无法创建风险检测任务");
                }

                // 扣除积分（每个详情扣1分）
                Long totalPoints = (long) validImageUrls.size();
                boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints);
                if (!deductioned) {
                    return AjaxResult.error("积分不足，无法创建风险检测任务");
                }
                // 创建风险检测任务
                RiskDetectionTask task = new RiskDetectionTask();
                task.setOwnerId(getUserId());
                task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
                task.setTotalAmount(validImageUrls.size());
                task.setSuccessAmount(0);
                task.setFailAmount(0);
                task.setStatus(1); // 待执行
                UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
                if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                    // 团队模式：直接通过team_id过滤
                    task.setTeamId(userTeamInfo.getTeamId());

                }
                int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
                if (taskResult <= 0) {
                    return AjaxResult.error("创建风险检测任务失败");
                }

                // 创建风险检测任务详情
                List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
                for (String imageUrl : validImageUrls) {
                    RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                    detail.setTaskId(task.getId());
                    detail.setImageUrl(imageUrl);
                    detail.setType(3); // 3-来源为直接上传的图片
                    detail.setTypeId(null); // 直接上传的图片，type_id为空
                    detail.setProcessStatus(1); // 待处理
                    detail.setOwnerId(getUserId());
                    detail.setImageStatus(0);
                    detailList.add(detail);
                }

                int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);

                Map<String, Object> result = new HashMap<>();
                result.put("taskId", task.getId());
                result.put("taskBatch", task.getTaskBatch());
                result.put("totalImages", validImageUrls.size());
                result.put("successDetails", detailResult);

                return AjaxResult.success("创建风险检测任务成功", result);

            } catch (Exception e) {
                logger.error("通过图片链接创建风险检测任务失败", e);
                return AjaxResult.error("创建风险检测任务失败：" + e.getMessage());
            }
        } else {
            if (requestData instanceof List) {
                // 如果是List，直接转换
                List<?> list = (List<?>) idList;
                for (Object item : list) {
                    if (item instanceof String) {
                        productId.add(Long.valueOf((String) item));
                    } else if (item != null) {
                        productId.add(Long.valueOf(item.toString()));
                    }
                }
            } else if (requestData instanceof Map) {
                if (idList1 instanceof List) {
                    List<?> list = (List<?>) idList1;
                    for (Object item : list) {
                        if (item instanceof String) {
                            productId.add(Long.valueOf((String) item));
                        } else if (item != null) {
                            productId.add(Long.valueOf(item.toString()));
                        }
                    }
                }

            }
            if (productId.isEmpty()) {
                return AjaxResult.error("数据id列表不能为空");
            }
            try {
                logger.info("开始通过图片链接创建风险检测任务，链接数量: {}", productId.size());
                // 过滤有效的图片链接
                List<String> validImageUrls = new ArrayList<>();
                for (Long id : productId) {
                    ProductInfo productInfo = iProductInfoService.selectProductInfoById(id);
                    if (productInfo.getProductImageUrl() != null && !productInfo.getProductImageUrl().isEmpty()) {
                        validImageUrls.add(productInfo.getProductImageUrl().trim());
                    }
                }
                if (validImageUrls.isEmpty()) {
                    logger.error("没有有效的图片链接，无法创建风险检测任务");
                    return AjaxResult.error("没有有效的图片链接，无法创建风险检测任务");
                }

                // 扣除积分（每个详情扣1分）
                Long totalPoints = (long) validImageUrls.size();
                boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints);
                if (!deductioned) {
                    return AjaxResult.error("积分不足，无法创建风险检测任务");
                }

                // 创建风险检测任务
                RiskDetectionTask task = new RiskDetectionTask();
                task.setOwnerId(getUserId());
                task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
                task.setTotalAmount(validImageUrls.size());
                task.setSuccessAmount(0);
                task.setFailAmount(0);
                task.setStatus(1); // 待执行

                int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
                if (taskResult <= 0) {
                    return AjaxResult.error("创建风险检测任务失败");
                }


                // 创建风险检测任务详情
                List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
                for (Long id : productId) {
                    RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                    ProductInfo productInfo = iProductInfoService.selectProductInfoById(id);
                    if (productInfo.getProductImageUrl() != null && !productInfo.getProductImageUrl().isEmpty()) {
                        detail.setTaskId(task.getId());
                        detail.setProductTitle(productInfo.getProductTitle());
                        detail.setImageUrl(productInfo.getProductImageUrl());
                        detail.setType(3); // 3-来源为直接上传的图片
                        detail.setTypeId(null); // 直接上传的图片，type_id为空
                        detail.setProcessStatus(1); // 待处理
                        detail.setOwnerId(getUserId());
                        detail.setImageStatus(0);
                        detailList.add(detail);
                    }
                }
                int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);

                Map<String, Object> result = new HashMap<>();
                result.put("taskId", task.getId());
                result.put("taskBatch", task.getTaskBatch());
                result.put("totalImages", validImageUrls.size());
                result.put("successDetails", detailResult);

                return AjaxResult.success("创建风险检测任务成功，成功数量" + detailList.size() + "个", result);

            } catch (Exception e) {
                logger.error("通过图片链接创建风险检测任务失败", e);
                return AjaxResult.error("创建风险检测任务失败：" + e.getMessage());
            }
        }
    }

    /**
     * 通过产品信息ID创建风险检测任务
     */
    @ApiOperation("通过产品信息ID创建风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.INSERT)
    @PostMapping("/createByProductIds")
    public AjaxResult createByProductIds(@RequestBody Map<String, Object> requestData) {
        // 获取productList参数
        Object productListObj = requestData.get("productList");
        if (!(productListObj instanceof List)) {
            return AjaxResult.error("产品信息参数列表不能为空");
        }

        List<Map<String, Object>> productList = (List<Map<String, Object>>) productListObj;
        if (productList == null || productList.isEmpty()) {
            return AjaxResult.error("产品信息参数列表不能为空");
        }

        try {
            List<String> imageUrls = new ArrayList<>();
            List<Long> validIds = new ArrayList<>();
            List<Integer> typeList = new ArrayList<>();
            List<Long> typeIdList = new ArrayList<>();
            Integer typeid = 0;

            // 1. 根据type不同，查询不同的表获取图片地址
            for (Map<String, Object> item : productList) {
                Object idObj = item.get("id");
                Object typeObj = item.get("type");

                if (idObj == null || typeObj == null) {
                    continue;
                }

                Long id = Long.valueOf(idObj.toString());
                Integer type = Integer.valueOf(typeObj.toString());
                typeid = type;

                String imageUrl = null;

                if (type == 1) {
                    // type=1时，查询t_ordinal_img_result表的res_img_url字段
                    OrdinalImgResult ordinalImgResult = ordinalImgResultService.selectOrdinalImgResultByImageId(id.toString());
                    if (ordinalImgResult != null && ordinalImgResult.getUserId().equals(getUserId())) {
                        String resImgUrl = ordinalImgResult.getResImgUrl();
                        if (resImgUrl != null && !resImgUrl.isEmpty()) {
                            // 使用CommonUtils.addCosPrefix添加域名前缀
                            imageUrl = CommonUtils.addCosPrefix(resImgUrl);
                        }
                    }
                } else if (type == 2) {
                    // type=2时，查询product_info表的product_image_url字段
                    ProductInfo productInfo = productInfoService.selectProductInfoById(id);
                    if (productInfo != null ) {
                        imageUrl = productInfo.getProductImageUrl();
                    }
                }

                if (imageUrl != null && !imageUrl.isEmpty()) {
                    imageUrls.add(imageUrl);
                    validIds.add(id);
                    typeList.add(type);
                    typeIdList.add(id); // typeId和id是同一个值
                }
            }

            if (imageUrls.isEmpty()) {
                return AjaxResult.error("没有找到有效的图片");
            }

            // 扣除积分（每个详情扣1分）
            Long totalPoints = (long) imageUrls.size();
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, Constants.TASK_TYPE_RISK_FILTER);
            if (!deductioned) {
                return AjaxResult.error("积分不足，无法创建风险检测任务");
            }

            // 2. 创建风险检测任务
            RiskDetectionTask task = new RiskDetectionTask();
            task.setOwnerId(getUserId());
            task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行

            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());

            }

            int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
            if (taskResult <= 0) {
                return AjaxResult.error("创建风险检测任务失败");
            }

            // 3. 创建风险检测任务详情
            List<RiskDetectionTaskDetail> detailList = new ArrayList<>();
            List<ProductInfo> poroductInfoList = new ArrayList<>();
            for (int i = 0; i < imageUrls.size(); i++) {
                RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                ProductInfo productInfos = new ProductInfo();

                if (typeid == 2) {
                    ProductInfo productInfo = iProductInfoService.selectProductInfoById(validIds.get(i));
                    detail.setProductTitle(productInfo.getProductTitle());
                    detail.setTypeId(productInfo.getId());
                    productInfos.setId(validIds.get(i));
                    productInfos.setInfringementMark(1);
                } else {
                    detail.setTypeId(typeIdList.get(i));
                }
                detail.setTaskId(task.getId());

                detail.setImageUrl(imageUrls.get(i));
                detail.setType(typeList.get(i));

                detail.setProcessStatus(1); // 待处理
                detail.setOwnerId(getUserId());
                detail.setImageStatus(0);
                detailList.add(detail);
                poroductInfoList.add(productInfos);
            }

            int detailResult = riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(detailList);
            if (typeid == 2) {
                productInfoService.updateProductInfoList(poroductInfoList);
            }


            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("validIds", validIds.size());
            result.put("successDetails", detailResult);

            return AjaxResult.success("创建风险检测任务成功", result);

        } catch (Exception e) {
            logger.error("通过产品信息ID创建风险检测任务失败", e);
            return AjaxResult.error("创建风险检测任务失败：" + e.getMessage());
        }
    }

    /**
     * 新增风险检测任务表
     */
    @ApiOperation("新增风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiskDetectionTask riskDetectionTask) {
        // 设置用户ID
        riskDetectionTask.setOwnerId(getUserId());
        // 生成任务批次号
        riskDetectionTask.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
        // 设置初始状态
        riskDetectionTask.setStatus(1);
        // 设置初始数量
        if (riskDetectionTask.getTotalAmount() == null) {
            riskDetectionTask.setTotalAmount(0);
        }
        if (riskDetectionTask.getSuccessAmount() == null) {
            riskDetectionTask.setSuccessAmount(0);
        }
        if (riskDetectionTask.getFailAmount() == null) {
            riskDetectionTask.setFailAmount(0);
        }

        return toAjax(riskDetectionTaskService.insertRiskDetectionTask(riskDetectionTask));
    }

    /**
     * 修改风险检测任务表
     */
    @ApiOperation("修改风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiskDetectionTask riskDetectionTask) {
        // 验证任务是否属于当前用户
        RiskDetectionTask existingTask = riskDetectionTaskService.selectRiskDetectionTaskById(riskDetectionTask.getId());
        if (existingTask == null) {
            return AjaxResult.error("风险检测任务不存在");
        }
        if (!existingTask.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限修改此风险检测任务");
        }

        // 设置用户ID（确保不被篡改）
        riskDetectionTask.setOwnerId(getUserId());

        return toAjax(riskDetectionTaskService.updateRiskDetectionTask(riskDetectionTask));
    }

    /**
     * 删除风险检测任务表
     */
    @ApiOperation("删除风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        // 验证任务是否属于当前用户
        RiskDetectionTask riskDetectionTask = riskDetectionTaskService.selectRiskDetectionTaskById(id);
        if (riskDetectionTask == null) {
            return AjaxResult.error("风险检测任务不存在");
        }
        if (!riskDetectionTask.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限删除此风险检测任务");
        }

        return toAjax(riskDetectionTaskService.deleteRiskDetectionTaskById(id));
    }

    /**
     * 批量删除风险检测任务表
     */
    @ApiOperation("批量删除风险检测任务")
    @Log(title = "风险检测任务表", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult removeBatch(@PathVariable Long[] ids) {
        // 验证所有任务是否属于当前用户
        for (Long id : ids) {
            RiskDetectionTask riskDetectionTask = riskDetectionTaskService.selectRiskDetectionTaskById(id);
            if (riskDetectionTask == null) {
                return AjaxResult.error("风险检测任务不存在，ID: " + id);
            }
            if (!riskDetectionTask.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限删除风险检测任务，ID: " + id);
            }
        }

        return toAjax(riskDetectionTaskService.deleteRiskDetectionTaskByIds(ids));
    }

    /**
     * 批量删除风险检测任务详情
     */
    @ApiOperation("批量删除风险检测任务详情")
    @Log(title = "风险检测任务详情表", businessType = BusinessType.DELETE)
    @DeleteMapping("/detail/batch/{ids}")
    public AjaxResult removeDetailBatch(@PathVariable Long[] ids) {
        // 验证所有详情是否属于当前用户
        for (Long id : ids) {
            RiskDetectionTaskDetail detail = riskDetectionTaskDetailService.selectRiskDetectionTaskDetailById(id);
            if (detail == null) {
                return AjaxResult.error("风险检测任务详情不存在，ID: " + id);
            }
            if (!detail.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限删除风险检测任务详情，ID: " + id);
            }
        }

        return toAjax(riskDetectionTaskDetailService.deleteRiskDetectionTaskDetailByIds(ids));
    }

    /**
     * 废弃图片接口
     * @param detail
     * @return
     */
    @PutMapping("/abandoned")
    public AjaxResult abandoned(@RequestBody RiskDetectionTaskDetail detail) {

        return toAjax(riskDetectionTaskDetailService.updateRiskDetectionTaskDetailAbandoned(detail)) ;
    }

}