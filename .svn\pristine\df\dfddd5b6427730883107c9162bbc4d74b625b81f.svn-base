<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.RiskDetectionTaskDetailMapper">
    
    <resultMap type="com.dataxai.domain.RiskDetectionTaskDetail" id="RiskDetectionTaskDetailResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="productTitle"    column="product_title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="type"    column="type"    />
        <result property="typeId"    column="type_id"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="elements"    column="elements"    />
        <result property="suggestion"    column="suggestion"    />
        <result property="processStatus"    column="process_status"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="hasUploaded"    column="has_uploaded"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="scaleImageUrl"    column="scale_image_url"    />
        <result property="imageStatus"    column="image_status"    />
    </resultMap>

    <sql id="selectRiskDetectionTaskDetailVo">
        select id, task_id,product_title, image_url,scale_image_url, type, type_id, risk_level, elements, suggestion, process_status, owner_id, has_uploaded, create_time, update_time,image_status from risk_detection_task_detail
    </sql>

    <select id="selectRiskDetectionTaskDetailList" parameterType="RiskDetectionTaskDetail" resultMap="RiskDetectionTaskDetailResult">
        <include refid="selectRiskDetectionTaskDetailVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="riskLevel != null and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="processStatus != null "> and process_status = #{processStatus}</if>
            <if test="imageStatus != null "> and image_status = #{imageStatus}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
        </where>
        order by id asc
    </select>

    <select id="selectRiskDetectionTaskDetailByTaskId" parameterType="Long" resultMap="RiskDetectionTaskDetailResult">
        <include refid="selectRiskDetectionTaskDetailVo"/>
        where task_id = #{taskId}
--         order by create_time asc
    </select>
    
    <select id="selectRiskDetectionTaskDetailByTaskIdAndRiskLevels" resultMap="RiskDetectionTaskDetailResult">
        <include refid="selectRiskDetectionTaskDetailVo"/>
        where task_id = #{taskId}
        <if test="riskLevels != null and riskLevels.size() > 0">
            and risk_level in
            <foreach item="riskLevel" collection="riskLevels" open="(" separator="," close=")">
                #{riskLevel}
            </foreach>
        </if>
--         order by create_time asc
    </select>
    
    <select id="selectRiskDetectionTaskDetailById" parameterType="Long" resultMap="RiskDetectionTaskDetailResult">
        <include refid="selectRiskDetectionTaskDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRiskDetectionTaskDetail" parameterType="RiskDetectionTaskDetail" useGeneratedKeys="true" keyProperty="id">
        insert into risk_detection_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="productTitle != null">product_title,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url,</if>
            <if test="type != null">type,</if>
            <if test="typeId != null">type_id,</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level,</if>
            <if test="elements != null">elements,</if>
            <if test="suggestion != null">suggestion,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="hasUploaded != null">has_uploaded,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="imageStatus != null">image_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="productTitle != null">#{productTitle},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">#{scaleImageUrl},</if>
            <if test="type != null">#{type},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="riskLevel != null and riskLevel != ''">#{riskLevel},</if>
            <if test="elements != null">#{elements},</if>
            <if test="suggestion != null">#{suggestion},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="hasUploaded != null">#{hasUploaded},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="imageStatus != null">#{imageStatus},</if>
         </trim>
    </insert>

    <update id="updateRiskDetectionTaskDetail" parameterType="com.dataxai.domain.RiskDetectionTaskDetail">
        update risk_detection_task_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="productTitle != null">product_title = #{productTitle},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="scaleImageUrl != null and scaleImageUrl != ''">scale_image_url = #{scaleImageUrl},</if>
            <if test="type != null">type = #{type},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level = #{riskLevel},</if>
            <if test="elements != null">elements = #{elements},</if>
            <if test="suggestion != null">suggestion = #{suggestion},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="hasUploaded != null">has_uploaded = #{hasUploaded},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="imageStatus != null">image_status = #{imageStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRiskDetectionTaskDetailById" parameterType="Long">
        delete from risk_detection_task_detail where id = #{id}
    </delete>

    <delete id="deleteRiskDetectionTaskDetailByIds" parameterType="String">
        delete from risk_detection_task_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRiskDetectionTaskDetailByTaskId" parameterType="Long">
        delete from risk_detection_task_detail where task_id = #{taskId}
    </delete>

    <update id="batchUpdateHasUploaded">
        update risk_detection_task_detail 
        set has_uploaded = #{hasUploaded}
        where image_url in
        <foreach item="imageUrl" collection="imageUrls" open="(" separator="," close=")">
            #{imageUrl}
        </foreach>
    </update>
    <update id="updateRiskDetectionTaskDetailAbandoned" parameterType="com.dataxai.domain.RiskDetectionTaskDetail">
        UPDATE risk_detection_task_detail SET image_status = #{imageStatus} WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper> 