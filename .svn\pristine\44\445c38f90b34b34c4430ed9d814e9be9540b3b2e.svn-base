package com.dataxai.web.controller.task;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.alibaba.fastjson2.JSONArray;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.uuid.IdUtils;
import com.dataxai.web.domain.*;
import com.dataxai.web.dto.FollowByTypeDTO;
import com.dataxai.web.service.ITaskOrdinalService;
import com.dataxai.web.service.MaterialResolverService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.dataxai.web.dto.OrdinalImageDTO;
import com.dataxai.web.task.core.*;
import com.dataxai.web.task.TaskProcessor;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.mapper.TaskMapper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务Controller
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@RestController
@Slf4j
@RequestMapping("/task/photo")
@Api(tags = { "任务管理接口" })
public class TaskController extends BaseController<Task>
{
    @Autowired
    private TaskCoreService taskCoreService;

    @Autowired
    private TaskImageService taskImageService;

    @Autowired
    private TaskSegmentationService taskSegmentationService;

    @Autowired
    private TaskHistoryService taskHistoryService;

    @Autowired
    private TaskProcessor taskProcessor;

    @Autowired
    private TaskOrdinalFactoryManager taskOrdinalFactoryManager;

    @Autowired
    private UserTeamInfoService userTeamInfoService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private ITaskOrdinalService taskOrdinalService;

    @Autowired
    private MaterialResolverService materialResolverService;

    /**
     * 查询任务列表
     */
    @ApiOperation(value = "根据用户和任务类型查询任务列表")
    @GetMapping("/listByType/{type}")
    @ApiImplicitParam(name = "type", value = "任务类型(0-真人图，1-人台图，5-白板图)", required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult listByUserIdAndType(@PathVariable("type") Long type)
    {
        Task task = new Task();
        task.setType(type);

        // 根据用户模式设置数据过滤条件
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            task.setTeamId(userTeamInfo.getTeamId());
            task.setUserId(null); // 清空userId，避免冲突
        } else {
            // 个人模式：通过user_id过滤，且team_id为0
            task.setUserId(SecurityUtils.getUserId());
            task.setTeamId(null); // 确保不设置teamId
        }

        List<Task> list = taskCoreService.selectTaskList(task);
        list.stream().peek(x->{
            x.setOriginalUrl(CommonUtils.addCosPrefix(x.getOriginalUrl()));
        }).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 查询任务列表
     */
    @ApiOperation(value = "根据用户id查询任务列表",hidden = true)
    @GetMapping("/listByUserId/{userId}")
    public TableDataInfo<Task> list(@PathVariable("userId") Long userId)
    {
        startPage();
        List<Task> list = taskCoreService.selectTaskListByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 查询任务列表
     */
    @GetMapping("/list/all")
    public TableDataInfo<Task> list(Task task)
    {
        startPage();
        List<Task> list = taskCoreService.selectTaskList(task);
        return getDataTable(list);
    }

    /**
     * 导出任务列表
     */
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task)
    {
        List<Task> list = taskCoreService.selectTaskList(task);
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        util.exportExcel(response, list, "任务数据");
    }

    /**
     * 获取任务切割信息
     */
    @Log(title = "获取任务切割信息", businessType = BusinessType.OTHER)
    @GetMapping(value = "/seg/{taskId}")
    @ApiOperation("获取任务切割信息")
    @ApiImplicitParam(name = "taskId",value = "任务id",required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult getSegDatInfo(@PathVariable("taskId") String taskId) throws IOException {
        Task task = new Task();
        task.setTaskId(taskId);
        Task taskSegDataResult = taskImageService.selectTaskSegData(task);

        TaskSegData taskSegData = new TaskSegData();
        taskSegData.setTaskId(taskSegDataResult.getTaskId());
        taskSegData.setSegData(taskSegDataResult.getSegData());
        return success(taskSegData);
    }

    /**
     * 前端页面 获取任务详细信息
     */
    @Log(title = "任务", businessType = BusinessType.OTHER)
    @GetMapping(value = "/getInfo/{taskId}")
    @ApiOperation("获取任务详细")
    @ApiImplicitParam(name = "taskId",value = "任务id",required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult getInfo(@PathVariable("taskId") String taskId)
    {
        Long   userId = SecurityUtils.getUserId();
        return success(taskCoreService.selectTaskByUserIdAndTaskId(userId,taskId));
    }

    /**
     * 前端页面 复制创意
     */
    @Log(title = "复制创意", businessType = BusinessType.OTHER)
    @PostMapping("/copyTask")
    @ApiOperation("复制创意")
    public AjaxResult copyTask(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException {
        String taskId = taskCoreService.copyTask(taskDTO);
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("taskId",taskId);
        return success(resultMap);
    }

    /**
     * 前端页面 再次执行
     */
    @Log(title = "再次执行", businessType = BusinessType.OTHER)
    @PostMapping("/execAgain")
    @ApiOperation("再次执行")
    @Transactional
    public AjaxResult executeTaskAgain(@RequestBody TaskDTO taskDTO) throws Exception {
        // 只传递 referedTaskOrdinalId，调用工厂的重新执行方法
        taskOrdinalFactoryManager.reExecuteTask(taskDTO.getReferedTaskOrdinalId());
        return toAjax(1);
    }

    /**
     * 前端页面 再次编辑
     */
    @Log(title = "再次编辑", businessType = BusinessType.OTHER)
    @PostMapping("/editAgain")
    @ApiOperation("再次编辑")
    public AjaxResult editTaskAgain(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException, IOException {
        String taskId = taskCoreService.editTaskAgain(taskDTO);

        // 获取原任务的详细信息，包括素材参数
        TaskOrdinal originalTaskOrdinal = taskOrdinalService.selectTaskOrdinalByTaskOrdinalId(taskDTO.getReferedTaskOrdinalId());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("taskId", taskId);

        // 如果原任务存在，解析并返回素材ID
        if (originalTaskOrdinal != null && StringUtils.isNotEmpty(originalTaskOrdinal.getTaskParam())) {
            try {
                // 解析原任务参数，提取素材ID
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode paramNode = objectMapper.readTree(originalTaskOrdinal.getTaskParam());

                // 提取风格素材ID
                if (paramNode.has("materialStyleId") && !paramNode.get("materialStyleId").isNull()) {
                    resultMap.put("materialStyleId", paramNode.get("materialStyleId").asInt());
                }

                // 提取IP素材ID
                if (paramNode.has("materialIpId") && !paramNode.get("materialIpId").isNull()) {
                    resultMap.put("materialIpId", paramNode.get("materialIpId").asInt());
                }

                // 兼容旧格式
                if (paramNode.has("styleId") && !paramNode.get("styleId").isNull()) {
                    resultMap.put("styleId", paramNode.get("styleId").asText());
                }
                if (paramNode.has("style") && !paramNode.get("style").isNull()) {
                    resultMap.put("style", paramNode.get("style").asText());
                }
                if (paramNode.has("stylePrompt") && !paramNode.get("stylePrompt").isNull()) {
                    resultMap.put("stylePrompt", paramNode.get("stylePrompt").asText());
                }

                log.info("再次编辑任务，返回原任务素材信息: materialStyleId={}, materialIpId={}",
                        resultMap.get("materialStyleId"), resultMap.get("materialIpId"));

            } catch (Exception e) {
                log.error("解析原任务参数失败: {}", originalTaskOrdinal.getTaskParam(), e);
            }
        }

        return success(resultMap);
    }

    /**
     * 新增任务
     */
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation("新建任务")
    public AjaxResult add(@RequestBody Task task) throws ExecutionException, InterruptedException {
        // 根据任务类型记录日志
        int taskType = task.getType().intValue();
        TaskLogUtils.info(taskType, "开始创建{}任务，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), SecurityUtils.getUserId());

        try {
            String taskId = taskCoreService.insertTask(task);
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("taskId",taskId);
            resultMap.put("type",task.getType());

            TaskLogUtils.info(taskType, "{}任务创建成功，任务ID: {}, 用户ID: {}",
                TaskLogUtils.getTaskTypeName(taskType), taskId, SecurityUtils.getUserId());

            return success(resultMap);
        } catch (Exception e) {
            TaskLogUtils.error(taskType, "{}任务创建失败，用户ID: {}", TaskLogUtils.getTaskTypeName(taskType), SecurityUtils.getUserId(), e);
            throw e;
        }
    }

    /**
     * 执行任务
     *
     * @param taskOrdinalDTO 任务参数DTO，包含任务的所有配置信息
     * @param request HTTP请求对象
     * @return AjaxResult 执行结果
     * @throws ExecutionException 执行异常
     * @throws InterruptedException 中断异常
     * @throws IOException IO异常
     */
    @Log(title = "任务", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    @ApiOperation("执行任务")
    @Transactional
    public AjaxResult executeTask(@RequestBody TaskOrdinalDTO taskOrdinalDTO,HttpServletRequest request) throws ExecutionException, InterruptedException, IOException, Exception {
        // 根据任务类型记录日志
        int taskType = taskOrdinalDTO.getType().intValue();
        TaskLogUtils.info(taskType, "开始执行{}任务，任务ID: {}, 用户ID: {}",
            TaskLogUtils.getTaskTypeName(taskType), taskOrdinalDTO.getTaskId(), SecurityUtils.getUserId());

        // 使用工厂管理器进行参数校验
        taskOrdinalFactoryManager.validateParameters(taskOrdinalDTO);

        // 创建任务实体对象并复制属性
        TaskOrdinal taskOrdinal = new TaskOrdinal();
        BeanUtils.copyProperties(taskOrdinalDTO, taskOrdinal);

        // 处理可选角色配置，转换为JSON字符串存储
        List<WebModelCharacterControl> optionalCharacterList = taskOrdinalDTO.getOptionalCharacter();
        if (null != optionalCharacterList) {
            JSONArray optionCharacterJsonArray = JSONArray.from(optionalCharacterList);
            taskOrdinal.setOptionalCharacter(optionCharacterJsonArray.toString());
        }

        // 设置任务基本信息
        taskOrdinal.setShortCutDesc(taskOrdinalDTO.getPrompt());
        taskOrdinal.setUserId(SecurityUtils.getUserId());
        taskOrdinal.setRealPerson(taskOrdinalDTO.getRealPerson());

        // 设置关键字段
        taskOrdinal.setTaskOrdinalId(IdUtils.fastUUID()); // 生成主键ID
        taskOrdinal.setType(Long.valueOf(taskType)); // 设置任务类型（int转Long）
        taskOrdinal.setTaskId(taskOrdinalDTO.getTaskId()); // 设置关联的任务ID

        log.info("TaskOrdinal关键字段设置: taskOrdinalId={}, type={}, taskId={}",
                taskOrdinal.getTaskOrdinalId(), taskOrdinal.getType(), taskOrdinal.getTaskId());

        // 在processLogic之前处理素材参数，确保素材信息被包含在初始保存中
        // 检查是否有素材ID需要处理（扩展到所有素材类任务，包括印花图提取等）
        if (taskOrdinalDTO.getMaterialStyleId() != null || taskOrdinalDTO.getMaterialIpId() != null) {
            try {
                log.info("=== 执行前处理素材参数 ===");
                log.info("任务类型: {}, 任务ID: {}", taskType, taskOrdinalDTO.getTaskId());
                log.info("风格素材ID: {}, IP素材ID: {}", taskOrdinalDTO.getMaterialStyleId(), taskOrdinalDTO.getMaterialIpId());

                // 构建包含素材ID的taskParam，并保持原有字段（如 cropCategoryId）
                String enhancedTaskParam = buildTaskParamWithMaterials(taskOrdinalDTO);
                if (StringUtils.isNotEmpty(enhancedTaskParam)) {
                    taskOrdinal.setTaskParam(enhancedTaskParam);
                    log.info("任务参数已增强，包含素材信息");
                } else {
                    log.warn("增强后的任务参数为空，使用原始参数");
                }

            } catch (Exception e) {
                log.error("处理素材参数时发生异常: {}", e.getMessage(), e);
            }
        } else if (taskType == Constants.TASK_TYPE_CROP_EXTRACT && StringUtils.isNotEmpty(taskOrdinalDTO.getTaskParam())) {
            // 印花图提取：如果仅传入了品类ID（cropCategoryId），也需要回填品类名称
            try {
                if (materialResolverService != null) {
                    String fullTaskParam = materialResolverService.buildFullTaskParam(taskOrdinalDTO.getTaskParam());
                    if (StringUtils.isNotEmpty(fullTaskParam)) {
                        taskOrdinal.setTaskParam(fullTaskParam);
                        log.info("任务参数已回填印花图提取品类名称");
                    }
                } else {
                    log.warn("MaterialResolverService未注入，跳过品类名称回填");
                }
            } catch (Exception e) {
                log.error("回填印花图提取品类名称失败: {}", e.getMessage(), e);
            }
        }

        // 使用工厂管理器进行逻辑处理（此时taskParam已包含素材信息）
        try {
            log.info("=== 开始调用 processLogic ===");
            log.info("TaskOrdinal信息: taskId={}, taskParam={}", taskOrdinal.getTaskId(), taskOrdinal.getTaskParam());

            taskOrdinalFactoryManager.processLogic(taskOrdinalDTO, taskOrdinal);

            log.info("=== processLogic 执行成功 ===");

        } catch (Exception e) {
            log.error("processLogic 执行失败: {}", e.getMessage(), e);
            TaskLogUtils.error(taskType, "{}任务逻辑处理失败，任务ID: {}, 用户ID: {}",
                TaskLogUtils.getTaskTypeName(taskType), taskOrdinal.getTaskId(), SecurityUtils.getUserId(), e);
            return toAjax(0);
        }



        // 执行任务并返回结果
        try {
            log.info("=== 开始调用 processExecution ===");

            taskOrdinalFactoryManager.processExecution(taskOrdinal);

            log.info("=== processExecution 执行成功 ===");
            TaskLogUtils.info(taskType, "{}任务执行成功，任务ID: {}, 用户ID: {}",
                TaskLogUtils.getTaskTypeName(taskType), taskOrdinal.getTaskId(), SecurityUtils.getUserId());
            return toAjax(1);
        } catch (Exception e) {
            log.error("processExecution 执行失败: {}", e.getMessage(), e);
            TaskLogUtils.error(taskType, "{}任务执行失败，任务ID: {}, 用户ID: {}",
                TaskLogUtils.getTaskTypeName(taskType), taskOrdinal.getTaskId(), SecurityUtils.getUserId(), e);
            return toAjax(0);
        }
    }

    /**
     * 修改任务名称
     */
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PostMapping("updateTaskName")
    @ApiOperation("修改任务名称")
    public AjaxResult updateTaskName(@RequestBody UpdateTaskNameDTO updateTaskNameDTO)
    {
        Task task = new Task();
        task.setTaskName(updateTaskNameDTO.getTaskName());
        task.setTaskId(updateTaskNameDTO.getTaskId());
        task.setUserId(SecurityUtils.getUserId());
        return toAjax(taskCoreService.updateTaskName(task));
    }

    /**
     * 修改任务
     */
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PostMapping
    @ApiOperation(value = "更新任务")
    public AjaxResult edit(@RequestBody Task task)
    {
        task.setOriginalUrl(CommonUtils.subCosPrefix(task.getOriginalUrl()));
        return toAjax(taskCoreService.resetTask(task));
    }

    /**
     * 删除任务
     */
    @Log(title = "任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    @ApiOperation("删除任务")
    @ApiImplicitParam(name = "taskIds", required = true, dataType = "long", paramType = "path", dataTypeClass = String.class)
    public AjaxResult remove(@PathVariable String[] taskIds)
    {
        Long userId = SecurityUtils.getUserId();
        return toAjax(taskCoreService.logicDeleteTaskByTaskIds(userId,taskIds));
    }


    /**
     * 调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推
     */
    @PostMapping("/imgChangeText")
    @ApiOperation(value = "调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推")
    public AjaxResult imgChangeText(@RequestBody ImgChangeTextDto dto) throws ExecutionException, InterruptedException {
       taskSegmentationService.imgChangeText(dto);
        return success(null);
    }

    /**
     * 调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推
     */
    @PostMapping("/test")
    @ApiOperation(value = "调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推")
    public AjaxResult test(@RequestBody ImgChangeTextDto dto) throws ExecutionException, InterruptedException, IOException {
        taskSegmentationService.test(dto);
        return null;
    }

    @GetMapping("/getTaskResult")
    @ApiOperation(value = "查询用户生成结果")
    public R<Object> getTaskResult(FollowByTypeDTO dto){
        // 先获取用户团队信息，避免消耗分页参数
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();

        // 构建查询状态列表
        List<Integer> statuses = new ArrayList<>();
        statuses.add(Constants.TASK_STATUS_EXECUTING);
        statuses.add(Constants.TASK_STATUS_PARKING);

        // 根据用户模式执行查询
        List<Task> list;
        List<Long> typesList = dto.getTypesAsList();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            list = taskMapper.selectByUserId(null, typesList, userTeamInfo.getTeamId().intValue(), statuses);
        } else {
            // 个人模式：通过user_id过滤，且team_id为0
            Long userId = SecurityUtils.getUserId();
            list = taskMapper.selectByUserId(userId, typesList, null, statuses);
        }

        // 填充任务详细信息
        list = taskHistoryService.fillTaskDetails(list);

        return R.ok(list);
    }

    @GetMapping("/getHistory")
    @ApiOperation(value = "查询用户的历史记录")
    public R<Object> getHistory(@RequestParam("types") String typesStr){
        // 解析逗号分隔的types字符串为List<Long>
        List<Long> types = new ArrayList<>();
        if (typesStr != null && !typesStr.trim().isEmpty()) {
            String[] typeArray = typesStr.split(",");
            for (String typeStr : typeArray) {
                try {
                    types.add(Long.parseLong(typeStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的数字格式
                }
            }
        }

        // 先获取用户团队信息，避免消耗分页参数
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();

        // 设置分页参数
        startPage();

        // 构建查询状态列表
        List<Integer> statuses = new ArrayList<>();
        statuses.add(Constants.TASK_STATUS_SUCCESS);

        // 根据用户模式执行查询
        List<Task> list;
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            list = taskMapper.selectByUserId(null, types, userTeamInfo.getTeamId().intValue(), statuses);
        } else {
            // 个人模式：通过user_id过滤，且team_id为0
            Long userId = SecurityUtils.getUserId();
            list = taskMapper.selectByUserId(userId, types, null, statuses);
        }

        // 填充任务详细信息（这里会有额外的SQL查询，但不影响已获取的分页信息）
        list = taskHistoryService.fillTaskDetails(list);

        // 先获取分页信息，避免后续查询影响分页结果
        PageInfo<Task> pageInfo = new PageInfo<>(list);
        long total = pageInfo.getTotal(); // 正确的总记录数

        // 返回结果
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", total);
        dataMap.put("data", list);
        return R.ok(dataMap);
    }

    @GetMapping("/getHistoryImage")
    @ApiOperation(value = "查询用户传过的图")
    public R<Object> getHistoryImage(@RequestParam("types") String typesStr){
        // 解析逗号分隔的types字符串为List<Long>
        List<Long> types = new ArrayList<>();
        if (typesStr != null && !typesStr.trim().isEmpty()) {
            String[] typeArray = typesStr.split(",");
            for (String typeStr : typeArray) {
                try {
                    types.add(Long.parseLong(typeStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的数字格式
                }
            }
        }

        startPage();
        List<OrdinalImageVO> list =  taskHistoryService.getHistoryImage(types);
        // 组装自定义分页结构
        HashMap<String, Object> pageData = new HashMap<>();
        pageData.put("total", new com.github.pagehelper.PageInfo<>(list).getTotal());
        pageData.put("data", list);
        return R.ok(pageData);
    }

    @PostMapping("/deleteHistoryImage")
    @ApiOperation(value = "删除用户传过的图")
    public R<Object> deleteHistoryImage(@RequestBody OrdinalImageDTO dto ){
        Integer count =  taskHistoryService.deleteHistoryImage(dto);
        return R.ok(count);
    }

    @PostMapping("/deleteImage")
    @ApiOperation(value = "删除生成结果/删除历史记录/删除收藏的图")
    public R<Object> deleteImage(@RequestParam("imageId") String imageId){
        Integer result =  taskHistoryService.deleteImage(imageId);
        return R.ok(result);
    }

    /**
     * 查询用户收藏的图片列表
     */
    @GetMapping("/listByType")
    @ApiOperation(value = "查询用户收藏的图片列表")
    public R<HashMap<String,Object>>  listByUserIdTypes(@RequestParam("types") String typesStr)
    {
        // 解析逗号分隔的types字符串为List<Long>
        List<Long> types = new ArrayList<>();
        if (typesStr != null && !typesStr.trim().isEmpty()) {
            String[] typeArray = typesStr.split(",");
            for (String typeStr : typeArray) {
                try {
                    types.add(Long.parseLong(typeStr.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的数字格式
                }
            }
        }

        Long userId = SecurityUtils.getUserId();
        OrdinalImgResult ordinalImgResult = new OrdinalImgResult();
        ordinalImgResult.setUserId(userId);
        ordinalImgResult.setFollow(1L);
        startPage();
        List<OrdinalImgResult> list = taskHistoryService.listByUserIdTypes(ordinalImgResult,types);
        list.stream().peek(x->{
            x.setResImgUrl(CommonUtils.addCosPrefix(x.getResImgUrl()));
            x.setOriginalImgUrl(CommonUtils.addCosPrefix(x.getOriginalImgUrl()));
            x.setMarkImgUrl(CommonUtils.addCosPrefix(x.getMarkImgUrl()));
            x.setResSmallImgUrl(CommonUtils.addCosPrefix(x.getResSmallImgUrl()));
            x.setDisassembleImgUrl(CommonUtils.addCosPrefix(x.getDisassembleImgUrl()));
        }).collect(Collectors.toList());
        // 组装自定义分页结构
        HashMap<String, Object> pageData = new HashMap<>();
        pageData.put("total", new com.github.pagehelper.PageInfo<>(list).getTotal());
        pageData.put("data", list);
        return R.ok(pageData);
    }

    @PostMapping("/reinitializeThreadPools")
    @ApiOperation(value = "手动重新初始化线程池（当数据库配置更新时调用）")
    public R<Object> reinitializeThreadPools() {
        taskProcessor.reinitializeThreadPools();
        return R.ok();
    }

    /**
     * 测试素材查询功能
     */
    @GetMapping("/test/material/{styleId}/{ipId}")
    @ApiOperation("测试素材查询")
    public AjaxResult testMaterialQuery(@PathVariable Integer styleId, @PathVariable Integer ipId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 测试MaterialResolverService是否注入
            if (materialResolverService == null) {
                result.put("error", "MaterialResolverService注入失败");
                return AjaxResult.error("MaterialResolverService注入失败");
            }
            result.put("serviceInjected", true);

            // 测试风格素材查询
            try {
                MaterialStyle styleResult = materialResolverService.getStyleById(styleId);
                result.put("style", styleResult != null ? styleResult.getName() : "null");
            } catch (Exception e) {
                result.put("styleError", e.getMessage());
            }

            // 测试IP素材查询
            try {
                MaterialIp ipResult = materialResolverService.getIpById(ipId);
                result.put("ip", ipResult != null ? ipResult.getName() : "null");
            } catch (Exception e) {
                result.put("ipError", e.getMessage());
            }

            // 测试buildFullTaskParam
            try {
                String testParam = "{\"materialStyleId\":" + styleId + ",\"materialIpId\":" + ipId + "}";
                String fullParam = materialResolverService.buildFullTaskParam(testParam);
                result.put("fullParam", fullParam);
            } catch (Exception e) {
                result.put("buildError", e.getMessage());
            }

            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("测试素材查询失败", e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 构建包含素材信息的任务参数
     * 在执行阶段动态添加素材详情到taskParam中
     */
    private String buildTaskParamWithMaterials(TaskOrdinalDTO taskOrdinalDTO) {
        try {
            log.info("=== 构建包含素材信息的任务参数 ===");

            // 构建基础参数
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode paramNode;

            // 解析原始taskParam
            if (StringUtils.isNotEmpty(taskOrdinalDTO.getTaskParam())) {
                paramNode = (ObjectNode) objectMapper.readTree(taskOrdinalDTO.getTaskParam());
            } else {
                paramNode = objectMapper.createObjectNode();
            }

            // 添加素材ID到参数中
            if (taskOrdinalDTO.getMaterialStyleId() != null) {
                paramNode.put("materialStyleId", taskOrdinalDTO.getMaterialStyleId());
            }

            if (taskOrdinalDTO.getMaterialIpId() != null) {
                paramNode.put("materialIpId", taskOrdinalDTO.getMaterialIpId());
            }

            // 转换为JSON字符串
            String baseTaskParam = objectMapper.writeValueAsString(paramNode);

            // 检查MaterialResolverService是否可用
            if (materialResolverService == null) {
                log.warn("MaterialResolverService未注入，返回基础参数");
                return baseTaskParam;
            }

            // 使用MaterialResolverService构建完整的素材信息
            String fullTaskParam = materialResolverService.buildFullTaskParam(baseTaskParam);
            return StringUtils.isNotEmpty(fullTaskParam) ? fullTaskParam : baseTaskParam;

        } catch (Exception e) {
            log.error("构建包含素材信息的任务参数失败: {}", e.getMessage(), e);
            // 如果失败，返回原始参数
            return taskOrdinalDTO.getTaskParam();
        }
    }

    /**
     * 基于当前taskParam构建包含素材信息的任务参数
     */
    private String buildTaskParamWithMaterialsFromCurrent(String currentTaskParam, TaskOrdinalDTO taskOrdinalDTO) {
        try {
            log.info("=== 基于当前taskParam构建素材信息 ===");

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode paramNode;

            // 解析当前taskParam
            if (StringUtils.isNotEmpty(currentTaskParam)) {
                paramNode = (ObjectNode) objectMapper.readTree(currentTaskParam);
            } else {
                paramNode = objectMapper.createObjectNode();
            }

            // 添加素材ID到参数中
            if (taskOrdinalDTO.getMaterialStyleId() != null) {
                paramNode.put("materialStyleId", taskOrdinalDTO.getMaterialStyleId());
                log.info("添加风格素材ID: {}", taskOrdinalDTO.getMaterialStyleId());
            }

            if (taskOrdinalDTO.getMaterialIpId() != null) {
                paramNode.put("materialIpId", taskOrdinalDTO.getMaterialIpId());
                log.info("添加IP素材ID: {}", taskOrdinalDTO.getMaterialIpId());
            }

            // 转换为JSON字符串
            String baseTaskParam = objectMapper.writeValueAsString(paramNode);
            log.info("包含素材ID的基础参数: {}", baseTaskParam);

            // 检查MaterialResolverService是否可用
            if (materialResolverService == null) {
                log.warn("MaterialResolverService未注入，返回基础参数");
                return baseTaskParam;
            }

            // 使用MaterialResolverService构建完整的素材信息
            String fullTaskParam = materialResolverService.buildFullTaskParam(baseTaskParam);
            log.info("构建完成的完整参数: {}", fullTaskParam);
            return StringUtils.isNotEmpty(fullTaskParam) ? fullTaskParam : baseTaskParam;

        } catch (Exception e) {
            log.error("基于当前taskParam构建素材信息失败: {}", e.getMessage(), e);
            // 如果失败，返回当前参数
            return currentTaskParam;
        }
    }

}
