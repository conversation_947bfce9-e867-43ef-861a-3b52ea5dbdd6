
import { useMemo, useEffect, useState, useContext, useRef } from 'react';
import { Table, Tag, Progress, Image, Upload, Modal, Button, Radio, Input, message, DatePicker, Tooltip, Switch, Select } from 'antd';

import { CloudUploadOutlined, ExclamationCircleFilled, PlusOutlined, UploadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getCommonStyleList, } from '@/api/task'
import { useNavigate } from 'react-router-dom';
import { getBatchList, addBatch, deleteBatch, stopBatch,updateTaskRemark,getBatchRemark } from '@/api/task'
import dayjs from 'dayjs'
import { batchDownloadImages } from '@/utils/downFile'
import { batchUploadOss, uploadFileCsvApi, batchZipUrl } from '@/api/common'
import FilterBar, { FilterParams } from '@/component/filter';

export const BatchToolsContinuous = () => {
	const navigate = useNavigate()

	const [modal, contextHolder] = Modal.useModal()
	const [messageApi, messageContextHolder] = message.useMessage()

	useEffect(() => { }, [])

	const [batchNo, setBatchNo] = useState('') // 批次号
	const [dateRange, setDateRange] = useState<any>(null) // 日期范围
	const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

	const [isModalVisible, setIsModalVisible] = useState(false) // 控制弹窗的显示与隐藏
	const [fileList, setFileList] = useState<UploadFile[]>([]) // 上传的图片列表
	const [fileListCsv, setFileListCsv] = useState<File | null>(null);// 上传的csv文件列表
    const [openModal, setOpenModal] = useState<any>(false)//remark modal type
    const [remarkText, setRemarkText] = useState('')
    const [remarkId, setRemarkId] = useState(null)//修改备注需要的id
    //提交备注信息
    const editRemark = ()=>{
        updateTaskRemark({remark:remarkText,id:remarkId,taskType: 'batch'}).then(res=>{ 
            setOpenModal(false);
        })
    }

	// 表格数据
	const [dataSource, setDataSource] = useState([
		{
			key: '',
			batchNo: '',
			taskCount: null,
			progress: null,
			status: '',
			createTime: ''
		},
	])

	// 表格列配置
	const columns = [
		{
			title: '任务批次',
			dataIndex: 'batchNumber',
			key: 'batchNumber',
			ellipsis: true,
			align: 'center' as const,
			render: (taskBatch: string, record: any) => (
				<>
					<p>{taskBatch}</p>
					<p>{record.remark}</p>
				</>
			)
		},
		{
			title: '任务数量',
			dataIndex: 'totalAmount',
			key: 'totalAmount',
			width: 140,
			align: 'center' as const,
			render: (totalAmount: number, record: any) => (
				<>
					<p>总数：{totalAmount}</p>
					<p>成功：{record.successAmount}</p>
					{(record.status == 1 || record.status == 3 || record.status == 6) && record.failAmount > 0 && (
						<p style={{ color: '#CF1322' }}>失败：{record.failAmount}</p>
					)}
				</>
			)
		},
		{
			title: '任务进度',
			dataIndex: 'successAmount',
			key: 'successAmount',
			align: 'center' as const,
			render: (successAmount: number, record: any) => (
				<Progress percent={
					(record.status == 1 || record.status == 3 || record.status == 6)
						? Math.round(((successAmount + record.failAmount) / record.totalAmount) * 100)
						: 0
				}
					strokeColor={record.status == '6' ? '#cf1322' : undefined} />
			)
		},
		{
			title: '任务状态',
			dataIndex: 'status',
			key: 'status',
			width: 200,
			align: 'center' as const,
			render: (status: string, record: any) => {
				let color = ''
				if (status == '1') {
					color = 'green'
				} else if (status == '3') {
					color = 'blue'
				} else if (status == '4') {
					color = 'orange'
				} else {
					color = 'red'
				}
				return (
					<Tag color={color}>
						{status == '1'
							? '已完成'
							: status == '3'
								? '执行中'
								: status == '4' ? (record.waitTime == 0 ? '排队中 预估1分钟以内' : record.waitTime > 0 ? `排队中 预估(${record.waitTime}分钟)` : '排队中')
									: status == '5'
										? '准备中'
										: '手动终止'}
					</Tag>
				)
			}
		},

		{
			title: '创建时间',
			dataIndex: 'createTime',
			key: 'createTime',
			align: 'center' as const,
			render: (createTime: string) => (
				<p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
			)
		},
		{
			title: '操作',
			key: 'action',
			align: 'center' as const,
			render: (_: any, record: any) => (
				<div className='flex justify-center  gap-2'>
					{<Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
						onClick={() => goDetail(record)} size="small">查看详情</Button>}
					{<Button type="link" disabled={record.successAmount == 0} style={{ color: record.successAmount == 0 ? '#bfbfbf' : '#32649f' }}
						onClick={() => handleDownloadImgages(record.imgResults, record.batchId)}
						size="small"
						loading={downloadLoading === record.batchId}>下载</Button>}
					{record.status == 4 && <Button type="link" size="small" style={{ color: '#cf1322' }} onClick={() => handleDelBatch(record.batchId)} >删除</Button>}
					{record.status == 3 && <Button type="link" size="small" style={{ color: '#cf1322' }} onClick={() => handleStopBatch(record.batchId)} >手动终止</Button>}
                    {<Button type="link" style={{  color: '#32649f' }} onClick={() => {
                        //record.id 任务id
                        getBatchRemark(record.batchId ).then((res: any) => {
                            console.log(res);
                                setRemarkText(res.remark)
                            })
                            //console.log(record);
                            setRemarkId(record.batchId);
                            setOpenModal(!openModal);
                                
                        }}
                        size="small"
                        >
                            备注
                    </Button>}
				</div>
			)
		}
	]
	//分页状态
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0
	})
	// 表格loading
	const [tableLoading, setTableLoading] = useState(false);

	// 查询条件存储key
	const QUERY_PARAMS_KEY = 'upload_image_query_params';

	//是否首次加载
	const [isFirstLoad, setIsFirstLoad] = useState(true);

	//获取平铺图生成批次列表
	const fetchBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number,
		batchNumber: string,
		remark: string,
		userId: string,
		startTime: string,
		endTime: string
	) => {
		setDataSource([])
		setTableLoading(true)
		try {
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber,
				remark,
				userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response, '获取图片任务批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
			}
		} catch (error) {
			console.error('获取数据时出错：', error)
		} finally {
			setTableLoading(false)
			setIsFirstLoad(false)
		}
	}
	// Refs 声明（必须放在组件顶层）
	const timerRef = useRef<NodeJS.Timeout>();
	const fetchRef = useRef<typeof refreshBatchListData>();
	const paginationRef = useRef(pagination);
	const isFirstLoadRef = useRef(isFirstLoad);
	// 同步最新状态到 ref
	useEffect(() => {
		paginationRef.current = pagination;
		isFirstLoadRef.current = isFirstLoad;
	});
	useEffect(() => {
		// 更新函数引用
		fetchRef.current = refreshBatchListData;

		const tick = () => {
			if (!isFirstLoadRef.current) {
				console.log('定时刷新');
				fetchRef.current?.(6, paginationRef.current.current, 10);
			}
		};

		// 清除旧定时器
		if (timerRef.current) clearInterval(timerRef.current);
		// 启动新定时器
		timerRef.current = setInterval(tick, 20000);
		// 清理函数
		return () => {
			if (timerRef.current) {
				clearInterval(timerRef.current);
			}
		};
	}, [isFirstLoad]); // 依赖项

	//刷新平铺图生成批次列表
	const refreshBatchListData = async (
		type: number,
		pageNum: number,
		pageSize: number
	) => {
		try {
			const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
			let queryParams = {
				batchNumber: '',
				remark: '',
				userId: '',
				stringDateRange: [] as string[]
			};

			if (savedParams) {
				queryParams = JSON.parse(savedParams);
			}

			let startTime = '';
			let endTime = '';
			if (queryParams.stringDateRange.length > 0) {
				[startTime, endTime] = queryParams.stringDateRange;
				startTime = startTime ? `${startTime} 00:00:00` : '';
				endTime = endTime ? `${endTime} 23:59:59` : '';
			}
			const response: any = await getBatchList({
				type,
				pageNum,
				pageSize,
				batchNumber: queryParams.batchNumber,
				remark: queryParams.remark,
				userId: queryParams.userId,
				startTime,
				endTime
			})
			if (response.data) {
				console.log(response, '刷新图片任务批次列表')
				setDataSource(response.data)
				setPagination((prev) => ({
					...prev,
					total: response.total
				}))
			}
		} catch (error) {
			console.error('刷新数据时出错：', error)
		} finally {
			setTableLoading(false)
		}
	}
	// 创建批次任务
	const handleOk = async () => {
		try {
			if (fileList.length === 0 && caozuo == 2) {
				messageApi.error('请先上传图片')
				return
			}
			setCreatLoading(true)
			let params = {
				styleId: radioValue == '1' ? '' : currentStyle?.styleId,
				style: radioValue == '1' ? '无风格' : currentStyle?.style,
				stylePrompt: radioValue == '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
				imageScale: imageScale,
				imageNumber: Number(generateQuantity),
			}
			const files = fileList.map((file) => file.originFileObj as File)
			if (caozuo == 1) {
				addBatch({
					table: fileListCsv,
					type: 6,
					taskParam: params ? JSON.stringify(params) : ''
				}).then((res: any) => {
					if (res.url) {
						modal.confirm({
							centered: true,
							title: '导入表格文件有错误信息，是否下载错误信息?',
							icon: null,
							okText: '确认下载',
							cancelText: '取消',
							onOk() {
								window.open(res.url, '_blank');
							},
							onCancel() {
								console.log('Cancel')
							}
						})
					} else {
						messageApi.success('平铺图生成任务新建成功')
						setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
						handleSearch(1, 10) // 刷新批次列表数据
					}
					userinfoService.refresh() // 刷新用户积分
					setIsModalVisible(false)
				}).catch((err) => {
					messageApi.error(`创建失败: ${err?.data?.msg}`)
				}).finally(() => {
					setCreatLoading(false)
				})


			} else if (caozuo == 2) {
				addBatch({ files, type: 6, taskParam: params ? JSON.stringify(params) : '' }).then((data) => {
					messageApi.success('平铺图生成任务新建成功')
					setIsModalVisible(false)
					setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
					handleSearch(1, 10) // 刷新批次列表数据
					userinfoService.refresh() // 刷新用户积分
				})
					.catch((err) => {
						messageApi.error(`创建失败: ${err?.data?.msg}`)
					})
					.finally(() => {
						setCreatLoading(false)
					})
			}

		} catch (err) {
			messageApi.error('创建失败')
			setCreatLoading(false)
		}
	}
	// 删除批次
	const handleDelBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: (
				<div className={'text-[18px] text-normal'}> 确认删除该批次？</div>
			),
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				deleteBatch(id).then(res => {
					message.success('批次删除成功')
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				}).catch(err => {
					message.error(`批次删除失败：${err?.data?.msg}`)
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				})
			},
			onCancel() {
				console.log('Cancel')
			}
		})

	};
	// 手动终止批次
	const handleStopBatch = (id: string) => {
		modal.confirm({
			centered: true,
			title: (
				<div className={'text-[18px] text-normal'}> 确认手动终止该批次？</div>
			),
			content: null,
			icon: <ExclamationCircleFilled />,
			okText: '确认',
			cancelText: '取消',
			onOk() {
				stopBatch(id).then(res => {
					message.success('该批次手动终止成功')
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				}).catch(err => {
					message.error(`该批次手动终止失败：${err?.data?.msg}`)
					setPagination(prev => ({ ...prev, current: 1 }));
					handleSearch(1, 10)
				})
			},
			onCancel() {
				console.log('Cancel')
			}
		})
	};

	const [downloadLoading, setDownloadLoading] = useState(false);
	// 下载图片
	const handleDownloadImgages = (imgResults: any[], batchId: any) => {
		const imgUrls = imgResults.map((img) => img?.resImgUrl)
		setDownloadLoading(batchId);
		batchZipUrl({ imageUrls: imgUrls, type: 6 }).then((res: any) => {
			if (res) {
				window.open(res, '_blank'); // 在新标签页打开下载链接
			} else {
				messageApi.error('获取下载链接失败');
			}
		}).catch(err => {
			messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
		}).finally(() => {
			setDownloadLoading(false);
		})
		// batchDownloadImages(imgUrls);
	}
	// 查询处理函数
	const handleSearch = (pageNum: number, pageSize: number) => {
		// 存储查询条件到sessionStorage
		const queryParams = {
			batchNumber: batchNo,
			remark: '',
			userId: '',
			stringDateRange
		};
		sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
		if (stringDateRange.length > 0) {
			let [startTime, endTime] = stringDateRange
			startTime = startTime ? `${startTime} 00:00:00` : ''
			endTime = endTime ? `${endTime} 23:59:59` : ''
			fetchBatchListData(6, pageNum, pageSize, batchNo, queryParams.remark, queryParams.userId, startTime, endTime)
		} else {
			fetchBatchListData(6, pageNum, pageSize, batchNo, queryParams.remark, queryParams.userId, '', '')
		}
	}
	//初始化调用获取数据
	useEffect(() => {
		handleSearch(1, 10)
	}, [])

	// 在组件中添加分页变化处理函数
	const handleTableChange = (pagination: any) => {
		setPagination({
			...pagination,
			current: pagination.current,
			pageSize: pagination.pageSize
		});
		handleSearch(pagination.current, pagination.pageSize);
	};
	const [creatLoading, setCreatLoading] = useState(false)

	const goDetail = (record: any) => {
		navigate('/workspace/batchTools/continuous/detail', {
			state: { batchId: record.batchId }
		})
	}
	const showModal = () => {
		setIsModalVisible(true)
		setImageScale('1:1')// 生成图片比例(1:1)
		setFileList([]) // 清空图片列表
		setFileListCsv(null)// 清空表格列表
		//初始化风格选项为不选风格并清空当前风格
		setRadioValue('1')
		setCurrentStyle({
			styleId: '',
			id: '',
			thumbnailImgUrl: '',
			originImgUrl: '',
			style: '',
			stylePrompt: ''
		})
		setGenerateQuantity('1')
	}

	const handleCancel = () => {
		setIsModalVisible(false)
	}

	let prevFileList: any[] = [];
	// 图片上传
	const handleUploadChange = ({ fileList: newFileList }: { fileList: any[] }) => {
		// 比较新旧文件列表，如果相同则不处理
		if (newFileList.length === prevFileList.length &&
			newFileList.every((file, index) => file.uid === prevFileList[index].uid)) {
			return;
		}

		prevFileList = newFileList;
		const validFiles: UploadFile[] = [];

		const promises = newFileList.map((file) => {
			const isImage = /^image\//.test(file.type || '');

			if (!isImage) {
				messageApi.error('请检查文件类型');
				return Promise.resolve(false);
			}

			return new Promise<boolean>((resolve) => {
				const reader = new FileReader();
				const originFileObj = file.originFileObj;
				if (!originFileObj) {
					messageApi.error('无法获取文件对象,请检查文件');
					resolve(false);
					return;
				}
				reader.readAsArrayBuffer(originFileObj);
				reader.onload = async () => {
					try {
						const blob = new Blob([reader.result as ArrayBuffer]);
						const img = await createImageBitmap(blob);
						const { width, height } = img;
						const isValidSize = width <= 4096 && height <= 4096;
						if (!isValidSize) {
							messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过');
							resolve(false);
						} else {
							resolve(true);
						}
					} catch (error) {
						messageApi.error('图片加载失败，请检查图片格式');
						resolve(false);
					}
				};
				reader.onerror = () => {
					messageApi.error('读取文件失败，请检查文件格式');
					resolve(false);
				};
			});
		});

		Promise.all(promises).then((results) => {
			newFileList.forEach((file, index) => {
				if (results[index]) {
					validFiles.push(file);
				}
			});

			const totalFiles = fileList.length + validFiles.length;
			if (totalFiles > 50) {
				messageApi.error('最多只能上传50张图片');
				return;
			}

			if (validFiles.length > 0) {
				messageApi.success(`成功上传 ${validFiles.length} 张图片`);
				setFileList(prev => [...prev, ...validFiles]);
			}
		});
	};


	// 是否选择风格
	const [radioValue, setRadioValue] = useState('1')

	const handleRadioChange = (e: any) => {
		setRadioValue(e.target.value)
		if (e.target.value == '1') {
			setCurrentStyle({
				styleId: '',
				id: '',
				thumbnailImgUrl: '',
				originImgUrl: '',
				style: '',
				stylePrompt: ''
			})
		} else {
			setCurrentStyle(commonStyleList[0])
		}
	}

	interface CustomStyle {
		styleId: string
		id: string
		originImgUrl: string
		thumbnailImgUrl: string
		stylePrompt: string
		style: string
		// 其他属性...
	}

	interface CustomStyleListResponse {
		data: CustomStyle[]
		total: number
		default: any
		// 其他属性...
	}

	// 参考风格列表
	const [commonStyleList, setCommonStyleList] = useState<CustomStyle[]>([])
	// 无风格数据详情
	const [noStyleData, setNoStyleData] = useState<null | {
		stylePrompt: string
	}>(null)
	// 参考风格总页数
	const [commonStyleTotal, setCommonStyleTotal] = useState(0)
	// 参考风格当前页
	const [commonStylePage, setCommonStylePage] = useState(1)
	// 当前选中的风格
	const [currentStyle, setCurrentStyle] = useState<null | {
		styleId: string
		id: string
		originImgUrl: string
		thumbnailImgUrl: string
		style: string
		stylePrompt: string
	}>(null)

	//获取参考风格列表
	const fetchCommonStyleData = async (
		type: number,
		pagenum: number,
		pageSize: number
	) => {
		try {
			const response = (await getCommonStyleList(
				type,
				pagenum,
				pageSize
			)) as CustomStyleListResponse
			setCommonStyleList(response.data)
			setNoStyleData(response.default || { stylePrompt: '' })

			if (response.data.length) {
				setCurrentStyle(response.data[0])
			} else {
				setCurrentStyle({
					styleId: '',
					id: '',
					thumbnailImgUrl: '',
					originImgUrl: '',
					style: '',
					stylePrompt: ''
				})
			}

			setCommonStyleTotal(Math.ceil(response.total / pageSize)) // 总页数
			setCommonStylePage(pagenum)
		} catch (error) {
			console.error('获取数据时出错：', error)
		}
	}
	// 参考风格分页
	const commonStylePageChange = (pagenum: any) => {
		fetchCommonStyleData(1, pagenum, 12)
	}

	useEffect(() => {
		fetchCommonStyleData(1, 1, 12)
	}, [])
	// 参考风格选择弹窗显影
	const [styleModalOpen, setStyleModalOpen] = useState(false)
	// 点击参考风格反显
	const selectStyle = (item: any) => {
		setCurrentStyle(item)
		setStyleModalOpen(false)
	}
	//上传弹窗状态管理
	const [uploadModalOpen, setUploadModalOpen] = useState(false)
	const [caozuo, setCaozuo] = useState(1) //1-导入表格 2-上传图片

	const [columnsUp, setColumnsUp] = useState([])
	const [loading, setLoading] = useState(false)

	// 处理文件前验证
	const beforeUpload = (file: any) => {
		const isExcelOrCsv = [
			'application/vnd.ms-excel',
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'text/csv'
		].includes(file.type)

		const isLt2M = file.size / 1024 / 1024 < 2

		if (!isExcelOrCsv) {
			message.error('仅支持 CSV/XLS/XLSX 格式文件!')
			return Upload.LIST_IGNORE
		}
		if (!isLt2M) {
			message.error('文件大小不能超过 2MB!')
			return Upload.LIST_IGNORE
		}
		return true
	}
	//上传逻辑
	const customRequest = ({ file }: any) => {
		console.log(file);

		setFileListCsv(file);
		if (file.type === 'text/csv') {
			const formData = new FormData();
			formData.append('file', file);
		}
	}

	let commonStyleListEL =
		commonStyleList?.length > 0
			? commonStyleList.map((item: any) => {
				return (
					<div className="w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white">
						<img
							onClick={() => selectStyle(item)}
							src={item.originImgUrl || item.originImgUrl || ''}
							className={`${currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px] `}
							style={{ objectFit: 'cover' }}
							alt=""
						/>
						<p style={{ textAlign: 'center' }}>{item.style}</p>
					</div>
				)
			})
			: null


	//生成图片比例
	const [imageScale, setImageScale] = useState('1:1')

	const handleChange = (value: string) => {
		console.log(`selected ${value}`);
		setImageScale(value)
	};
	//图片生成数量
	const [generateQuantity, setGenerateQuantity] = useState('1')

	const onSearch = (params: FilterParams) => {
		const pageNum = 1;
		const pageSize = 10;
		fetchBatchListData(
			6,
			pageNum,
			pageSize,
			params.batchNumber ?? '',
			params.remark ?? '',
			params.userId ?? '',
			params.startTime,
			params.endTime
		);
	};

	return (
		<div className="h-full w-full p-[20px]">
			{contextHolder} {/* 这里确保 Modal 挂载 */}
			{messageContextHolder} {/* 这里确保 Message 挂载 */}
			<div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
				<Button type="primary" onClick={showModal}>
					新建平铺图生成任务
				</Button>
				{/* 新建平铺图生成任务弹窗 start */}
				<Modal
					title="新建平铺图生成任务"
					open={isModalVisible}
					onOk={handleOk}
					onCancel={handleCancel}
					width={660}
					centered
					okText="创建"
					confirmLoading={creatLoading}
				>
					<div className="flex justify-between items-center mt-8">
						风格选择
						<Radio.Group
							style={{ width: '260px' }}
							block
							options={[
								{ label: '不选风格', value: '1' },
								{ label: '风格选择', value: '2' }
							]}
							value={radioValue}
							onChange={(e) => handleRadioChange(e)}
						/>
					</div>
					{radioValue === '2' && (
						<div
							className="border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]"
							onClick={() => setStyleModalOpen(true)}
						>
							<img
								className="w-[80px] h-[80px] mr-[10px]"
								style={{ objectFit: 'cover' }}
								src={
									currentStyle?.originImgUrl ||
									currentStyle?.thumbnailImgUrl ||
									''
								}
							/>
							<div className="w-[200px]">
								<p className="text-[18px] mb-[12px]">选中风格</p>
								<p>{currentStyle ? currentStyle.style : ''}</p>
							</div>
							<img
								className="w-[18px]"
								src={require('@/asset/icon/zhankai.png')}
							/>
						</div>
					)}
					<div className="flex justify-between items-center  mt-4">
						<p className="w-[110px]">生成图片比例</p>
						<Select
							defaultValue='1:1'
							style={{ width: 260 }}
							onChange={handleChange}
							value={imageScale}
							options={[
								{ value: '1:1', label: '1 : 1' },
								{ value: '2:3', label: '2 : 3' },
								{ value: '3:2', label: '3 : 2' },
								{ value: '3:4', label: '3 : 4' },
								{ value: '4:3', label: '4 : 3' },
								{ value: '9:16', label: '9 : 16' },
								{ value: '16:9', label: '16 : 9' }
							]}
						/>
					</div>
					<div className="flex justify-between items-center mt-4">
						<p className="w-[110px]">图片生成数量</p>
						<Radio.Group
							size="large"
							block
							options={[
								{ label: '1', value: '1' },
								{ label: '2', value: '2' },
								{ label: '4', value: '4' },
							]}
							onChange={(e) => setGenerateQuantity(e.target.value)}
							value={generateQuantity}
							optionType="button"
							buttonStyle="solid"
							style={{ width: 260 }}
						/>
					</div>

					{/* 参考风格弹窗 start */}
					<Modal
						width={800}
						title=""
						footer={null}
						open={styleModalOpen}
						onCancel={() => setStyleModalOpen(false)}
					>
						<div>
							<div className="flex flex-wrap mt-[20px] h-[340px]">
								{commonStyleListEL}
							</div>
							<label>
								<ul className="flex justify-center  mt-[20px]">
									{Array.from({ length: commonStyleTotal }, (_, index) => (
										<li
											className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `}
											style={{ lineHeight: '18px' }}
											onClick={() => {
												commonStylePageChange(index + 1)
											}}
										>
											{index + 1}
										</li>
									))}
									<li>{'共' + commonStyleTotal + '页'}</li>
								</ul>
							</label>
						</div>
					</Modal>
					{/* 参考风格弹窗 end */}
					<div className="flex justify-between items-center mt-8">
						提示词列表
						<div className="w-[40%] flex justify-between">
							<Button
								style={{
									border:
										caozuo === 1 ? '1px dashed #33649f' : '1px dashed #d9d9d9',
									color: caozuo === 1 ? '#33649f' : '#000'
								}}
								type="dashed"
								onClick={() => {
									setUploadModalOpen(true)
									setCaozuo(1)
								}}
							>
								<PlusOutlined />
								导入Excel
							</Button>
							<Button
								style={{
									border:
										caozuo === 2 ? '1px dashed #33649f' : '1px dashed #d9d9d9',
									color: caozuo === 2 ? '#33649f' : '#000'
								}}
								type="dashed"
								onClick={() => {
									setUploadModalOpen(true)
									setCaozuo(2)
								}}
							>
								<PlusOutlined />
								图片转文字
							</Button>
						</div>
					</div>
					<div>
						<Upload.Dragger
							multiple
							accept="image/*"
							fileList={[]} // 设置为空数组隐藏自带列表
							onChange={handleUploadChange}
							beforeUpload={() => false} // 阻止自动上传
							className="upload-area"
							listType="picture"
							showUploadList={false} // 完全隐藏上传列表
							disabled={fileList.length >= 50} // 添加禁用状态
							style={{
								marginTop: '30px',
								display: caozuo === 2 ? 'block' : 'none'
							}}
							directory={true} // 允许选择或拖拽文件夹
						>
							<p className="ant-upload-drag-icon">
								<CloudUploadOutlined />
							</p>
							<p className="ant-upload-text">点击或拖拽文件到此处上传</p>
							<p className="ant-upload-hint">
								最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
							</p>
						</Upload.Dragger>
						<div
							className="mt-2 text-right"
							style={{ display: caozuo === 2 ? 'block' : 'none' }}
						>
							已选择图片张数:{' '}
							<span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
								{fileList.length}
							</span>{' '}
							/ 50
						</div>
						<div style={{ display: caozuo === 1 ? 'block' : 'none' }}>
							<div style={{ padding: 20 }}>
								<Upload.Dragger
									accept=".csv,.xls,.xlsx"
									beforeUpload={(file) => {
										setFileListCsv(file)
										return false
									}} // 阻止自动上传
									customRequest={() => { }}
									showUploadList={false}
									maxCount={1}
								>
									<Button icon={<UploadOutlined />} loading={loading}>
										上传表格文件 (CSV/XLS/XLSX)
										{"   "}{fileListCsv?.name}
									</Button>
								</Upload.Dragger>
								<div style={{ textAlign: 'center', marginTop: '20px' }}>
									<a
										href="https://image-task.xiaoaishop.com/prompt_demo.xlsx"
										download="template.xlsx"
									>
										<Button type="primary">下载Excel模板</Button>
									</a>
								</div>
							</div>
						</div>
					</div>
					{/* 上传导入弹窗 */}
					{/* <Modal
						width={800}
						title=""
						footer={null}
						open={uploadModalOpen}
						onCancel={() => setUploadModalOpen(false)}
					>
						<Upload.Dragger
							multiple
							accept="image/*"
							fileList={[]} // 设置为空数组隐藏自带列表
							onChange={handleUploadChange}
							beforeUpload={() => false} // 阻止自动上传
							className="upload-area"
							listType="picture"
							showUploadList={false} // 完全隐藏上传列表
							disabled={fileList.length >= 50} // 添加禁用状态
							style={{
								marginTop: '30px',
								display: caozuo === 2 ? 'block' : 'none'
							}}
						>
							<p className="ant-upload-drag-icon">
								<CloudUploadOutlined />
							</p>
							<p className="ant-upload-text">点击或拖拽文件到此处上传</p>
							<p className="ant-upload-hint">
								最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
							</p>
						</Upload.Dragger>
						<div
							className="mt-2 text-right"
							style={{ display: caozuo === 2 ? 'block' : 'none' }}
						>
							已选择图片张数:{' '}
							<span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
								{fileList.length}
							</span>{' '}
							/ 50
						</div>
						<div style={{ display: caozuo === 1 ? 'block' : 'none' }}>
							<div style={{ padding: 20 }}>
								<Upload.Dragger
									accept=".csv,.xls,.xlsx"
									beforeUpload={(file) => {
										setFileListCsv(file)
										return false
									}} // 阻止自动上传
									customRequest={customRequest}
									showUploadList={false}
									maxCount={1}
								>
									<Button icon={<UploadOutlined />} loading={loading}>
										上传表格文件 (CSV/XLS/XLSX)
									</Button>
								</Upload.Dragger>
								<div style={{ textAlign: 'center', marginTop: '20px' }}>
									<a
										href="https://image-task.xiaoaishop.com/prompt_demo.xlsx"
										download="template.xlsx"
									>
										<Button type="primary">下载Excel模板</Button>
									</a>
								</div>
							</div>
						</div>
					</Modal> */}
				</Modal>
				{/* 新建平铺图生成任务弹窗 end */}
				<div className="flex items-center gap-2 ]">
					<FilterBar
						fields={['batchNumber','dateRange','remark','userId']}
						storageKey={QUERY_PARAMS_KEY}
						withTime={false}
						onSearch={onSearch}
					/>
				</div>
			</div>
			<div className='w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide'>
				<Table
					columns={columns}
					dataSource={dataSource}
					className="mt-4"
					scroll={{ x: 966 }}
					loading={tableLoading}
					pagination={{
						...pagination,
						showTotal: (total: number) => `共 ${total} 条`,
						showSizeChanger: false
					}}
					onChange={handleTableChange}
				/>
			</div>
			<Modal
				title="添加备注信息"
				visible={openModal}
				onOk={editRemark}
				onCancel={() => setOpenModal(false)}
				okText="确定"
				cancelText="取消"
			>
				<Input placeholder="请输入备注" onChange={(e)=>setRemarkText(e.target.value)} value={remarkText} />
			</Modal>
		</div>
	)
};

export default BatchToolsContinuous;





