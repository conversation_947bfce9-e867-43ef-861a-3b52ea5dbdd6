package com.dataxai.web.service;

import com.dataxai.web.domain.MaterialIpCategory;
import java.util.List;

public interface MaterialIpCategoryService {
    List<MaterialIpCategory> queryAll(String name);

    MaterialIpCategory getById(Integer id);

    boolean addMaterialIpCategory(MaterialIpCategory category);

    boolean updateMaterialIpCategory(MaterialIpCategory category);

    boolean deleteMaterialIpCategory(Integer id);

    // 分页查询
    List<MaterialIpCategory> queryPage(Integer pageNum, Integer pageSize, String name);

    // 获取总数
    int countByCondition(String name);
    
    // 检查分类是否被使用
    boolean isCategoryInUse(Integer categoryId);
}