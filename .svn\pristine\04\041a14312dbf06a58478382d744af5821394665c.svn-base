package com.dataxai.web.controller.ImageController;


import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.PageDomain;
import com.dataxai.common.core.page.TableSupport;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.sql.SqlUtil;
import com.dataxai.web.domain.CustomImage;
import com.dataxai.web.dto.CustomImageDeleteDTO;
import com.dataxai.web.service.CustomImageService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/image/custom")
@Api(tags = { "自定义图片相关接口" })
public class CustomImageController extends BaseController<CustomImage> {

    @Autowired
    private CustomImageService customImageService;

    /**
     * 新增自定义图片
     */
    @Log(title = "新增自定义图片", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation("新增自定义图片")
    public R add(@ApiParam("需要增加的图片") @RequestPart("file") MultipartFile file, Integer type) throws Exception {
        return  customImageService.add(file,type);
    }

    /**
     * 删除自定义图片
     */
    @Log(title = "删除自定义图片", businessType = BusinessType.INSERT)
    @PostMapping("/delete")
    @ApiOperation("删除自定义图片")
    public R delete(@ApiParam("删除自定义图片") @RequestBody CustomImageDeleteDTO customImage) {
        return  customImageService.delete(customImage);
    }

    /**
     * 根据用户自定义图片分页
     */
    @GetMapping("/byPage")
    @ApiOperation("根据用户自定义图片分页")
    public R<HashMap<String,Object>> byPage(@RequestParam(value = "type", required = false) Integer type,
                                           @RequestParam(value = "userId", required = false) Long userId) {
        startPage();
        CustomImage customImage = new CustomImage();
        customImage.setType(type);
        customImage.setUserId(userId);
        List<CustomImage> list =  customImageService.selectByUserId(customImage);
        // 组装自定义分页结构
        HashMap<String, Object> pageData = new HashMap<>();
        pageData.put("total", new com.github.pagehelper.PageInfo<>(list).getTotal());
        pageData.put("data", list);
        return R.ok(pageData);
    }



}
