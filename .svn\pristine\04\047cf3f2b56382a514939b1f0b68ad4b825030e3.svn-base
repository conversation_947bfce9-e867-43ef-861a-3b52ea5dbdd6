/**
 * @Author: w<PERSON><PERSON>
 * @Date: 2024/1/20
 * @Description: ""
 * 常用的公共接口
 */

import { requestInstance } from '@/api/instance'
import { IUserInfo } from '@/types/user'
import { UPLOAD_SERVER_MAX_SIZE } from '@/constant'

/**
 * 上传接口
 * @param file
 */
export const uploadFileApi = (file: File, maxSize = UPLOAD_SERVER_MAX_SIZE) => {
	return requestInstance
		.upload<
			any,
			{
				url: string
			}
		>('/oss/file/fileUploadWithMaxSize', { file, maxSize })
		.fire()
}
// 上传图片
export const uploadFileOssApi = (file: File) => {
	return requestInstance
		.upload<
			any,
			{
				url: string
			}
		>('/oss/file/fileUpload', { file })
		.fire()
}
// 批量上传图片
export const batchUploadOss = (files: any) => {
	return requestInstance
		.upload<
			any,
			{
				url: string
			}
		>('/oss/file/batchUpload', { files })
		.fire()
}
// 批量压缩图片
export const batchZipUrl = (data: any) => {
	return requestInstance
		.post('/oss/file/batchZipUrl', data)
		.fire()
}
// 通过任务id批量下载压缩图片
export const batchZipUrlById = (data: any) => {
	return requestInstance
		.get(`/oss/file/batchzipids/${data.id}`, {})
		.fire()
}
//上传表格或是csv
export const uploadFileCsvApi = (file: any) => {
	return requestInstance
		.upload<
			any,
			{
				url: string
			}
		>('/oss/file/fileUploadTable', { file })
		.fire()
}

export const downFileApi = (url: string) => {
	return requestInstance.download(url, {}).fire()
}

export const getUserInfo = () => {
	return requestInstance.get<any, IUserInfo>('/front/user/getInfo', {}).fire()
}

