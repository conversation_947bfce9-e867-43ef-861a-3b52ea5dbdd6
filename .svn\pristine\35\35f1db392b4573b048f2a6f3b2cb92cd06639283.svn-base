package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 任务批次表
 * @TableName t_batch
 */
@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class Batch {
    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String batchId;

    /**
     * 任务批次
     */
    @ApiModelProperty(  value = "任务批次")
    private String batchNumber;

    /**
     * 任务类型
     */
    @ApiModelProperty(  value = "任务类型")
    private Long type;

    /**
     * 任务所属用户id
     */
    @ApiModelProperty(  value = "任务所属用户id")
    private Long userId;

    /**
     * 所属团队ID
     */
    @ApiModelProperty(  value = "所属团队ID，默认为0表示个人批次")
    private Long teamId;

    @ApiModelProperty(  value = "工作流节点ID")
    private Long WorkflowNodeExecutionId;

    /**
     * 任务执行状态(1-成功，2-失败，3-执行中，4-排队中)
     */
    @ApiModelProperty(  value = "任务执行状态(1-成功，2-失败，3-执行中，4-排队中)")
    private Integer status;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(  value = "任务创建时间")
    private Date createTime;

    /**
     * 任务更新时间
     */
    @ApiModelProperty(  value = "任务更新时间")
    private Date updateTime;

    /**
     * 是否删除(0-未删除，1-已删除)
     */
    @ApiModelProperty(  value = "是否删除(0-未删除，1-已删除)")
    private Integer delFlag;

    /**
     * 总数
     */
    @ApiModelProperty(  value = "总数")
    private Integer totalAmount;

    /**
     * 成功数量
     */
    @ApiModelProperty(  value = "成功数量")
    private Integer successAmount;

    /**
     * 失败数量
     */
    @ApiModelProperty(  value = "失败数量")
    private Integer failAmount;

    /**
     * 备注信息
     */
    @ApiModelProperty(  value = "备注信息")
    private String remark;

    @ApiModelProperty(  value = "任务集合")
    private List<Task> tasks;

    @ApiModelProperty(  value = "结果集合")
    private List<OrdinalImgResult> imgResults;

    @ApiModelProperty( value = "等待时间")
    private Integer waitTime;
}
