package com.dataxai.web.service.impl;

import java.util.List;

import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.utils.SnowFlakeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.dataxai.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.mapper.TaskOrdinalMapper;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.service.ITaskOrdinalService;

/**
 * 任务次数Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-14
 */
@Service
public class TaskOrdinalServiceImpl implements ITaskOrdinalService
{
    @Autowired
    private TaskOrdinalMapper taskOrdinalMapper;

    /**
     * 查询任务次数
     *
     * @param taskOrdinalId 任务次数主键
     * @return 任务次数
     */
    @Override
    public TaskOrdinal selectTaskOrdinalByTaskOrdinalId(String taskOrdinalId)
    {
        return taskOrdinalMapper.selectTaskOrdinalByTaskOrdinalId(taskOrdinalId);
    }

    /**
     * 查询任务次数列表
     *
     * @param taskOrdinal 任务次数
     * @return 任务次数
     */
    @Override
    public List<TaskOrdinal> selectTaskOrdinalList(TaskOrdinal taskOrdinal)
    {
        return taskOrdinalMapper.selectTaskOrdinalList(taskOrdinal);
    }

    /**
     * 新增任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTaskOrdinal(TaskOrdinal taskOrdinal)
    {
        taskOrdinal.setCreateTime(DateUtils.getNowDate());
        String nextId  = SnowFlakeUtils.nextIdStr();
        taskOrdinal.setTaskOrdinalId(nextId);
        int rows = taskOrdinalMapper.insertTaskOrdinal(taskOrdinal);
        insertOrdinalImgResult(taskOrdinal);
        return rows;
    }

    /**
     * 修改任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTaskOrdinal(TaskOrdinal taskOrdinal)
    {
        taskOrdinal.setUpdateTime(DateUtils.getNowDate());
        taskOrdinalMapper.deleteOrdinalImgResultByTaskOrdinalId(taskOrdinal.getTaskOrdinalId());
        insertOrdinalImgResult(taskOrdinal);
        return taskOrdinalMapper.updateTaskOrdinal(taskOrdinal);
    }

    /**
     * 批量删除任务次数
     *
     * @param taskOrdinalIds 需要删除的任务次数主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTaskOrdinalByTaskOrdinalIds(String[] taskOrdinalIds)
    {
        taskOrdinalMapper.deleteOrdinalImgResultByTaskOrdinalIds(taskOrdinalIds);
        return taskOrdinalMapper.deleteTaskOrdinalByTaskOrdinalIds(taskOrdinalIds);
    }

    /**
     * 删除任务次数信息
     *
     * @param taskOrdinalId 任务次数主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTaskOrdinalByTaskOrdinalId(String taskOrdinalId)
    {
        taskOrdinalMapper.deleteOrdinalImgResultByTaskOrdinalId(taskOrdinalId);
        return taskOrdinalMapper.deleteTaskOrdinalByTaskOrdinalId(taskOrdinalId);
    }

    /**
     * 新增每次任务执行后生成的图片信息
     *
     * @param taskOrdinal 任务次数对象
     */
    public void insertOrdinalImgResult(TaskOrdinal taskOrdinal)
    {
        List<OrdinalImgResult> ordinalImgResultList = taskOrdinal.getOrdinalImgResultList();
        String taskOrdinalId = taskOrdinal.getTaskOrdinalId();
        if (StringUtils.isNotNull(ordinalImgResultList))
        {
            List<OrdinalImgResult> list = new ArrayList<OrdinalImgResult>();
            for (OrdinalImgResult ordinalImgResult : ordinalImgResultList)
            {
                ordinalImgResult.setTaskOrdinalId(taskOrdinalId);
                list.add(ordinalImgResult);
            }
            if (list.size() > 0)
            {
                taskOrdinalMapper.batchOrdinalImgResult(list);
            }
        }
    }
}
