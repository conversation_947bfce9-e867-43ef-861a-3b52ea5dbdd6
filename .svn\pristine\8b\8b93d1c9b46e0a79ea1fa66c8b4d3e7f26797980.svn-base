package com.dataxai.web.gtask.processor;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dataxai.domain.ProductInfo;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.controller.front.ProductInfoController;
import com.dataxai.web.gtask.processor.GTaskProcessor;
import com.dataxai.web.gtask.utils.ImageProcessUtils;
import com.dataxai.web.utils.JpgUrlToWebpConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品颜色识别 GTask 处理器
 *
 * 作用：
 * - 定期查询“尚未识别颜色”的产品（imageColor 为空）
 * - 读取小图地址（scaleImageUrl），调用模型接口识别主颜色
 * - 将识别结果回写至产品表字段 imageColor
 *
 * 运行流程（配合调度器 ProductColorGTaskScheduler）：
 * 1) 调度器按固定频率触发 executeSchedule()
 * 2) 本处理器的 queryPendingTasks(batchSize) 查询待处理产品
 * 3) 框架并发分发至多个 worker，逐条调用 processSingleTask(item)
 * 4) processSingleTask 内部使用小图 URL 调用模型接口，解析颜色，回写数据库
 *
 * 解析策略：
 * - 优先从 resp.answer 中解析；支持 answer 为 JSON 对象或 JSON 字符串
 * - 支持字段别名：product_color、primary_color、颜色、主色等
 * - 兼容 data/outputs 等常见包装字段；兜底会对整个 JSON 深度扫描
 *
 * 注意事项：
 * - 请确保 ProductInfo 中 scaleImageUrl / imageColor 字段与数据库一致
 * - 模型 token、URL 可按环境配置或切换
 * - 若返回结构调整，请在 extractColorFromResult/parseFromObject 中扩展键名
 */
@Slf4j
@Component
public class ProductColorGTaskProcessor implements GTaskProcessor<ProductInfo> {

    // 模型接口
    private static final String MODEL_API_URL = "http://**************:8601/v1/chat-messages";
    private static final String MODEL_API_TOKEN = "app-sCNcrs8qzmtkw3NRBieTlmxQ";

    @Autowired
    private com.dataxai.service.IProductInfoService productInfoService;
    @Autowired
    private ImageProcessUtils imageProcessUtils;
    @Autowired
    private ProductInfoController productInfoController;

    @Override
    public String getTaskType() {
        return "product_color";
    }

    @Override
    public String getTaskDescription() {
        return "产品主颜色识别任务";
    }

    /**
     * 查询一批待处理的产品
     *
     * 选择策略：
     * - 以 imageColor 为空作为“待识别”的候选条件
     * - 小图是否为空在 processSingleTask 中二次校验并回填（必要时会现场生成小图）
     * - 结合 gtask.batch-size.product_color 限制单轮处理条数
     */
    @Override
    public List<ProductInfo> queryPendingTasks(int batchSize) {
        try {
            ProductInfo query = new ProductInfo();
//            query.setImageColor(null);
            TaskLogUtils.ProductColorCollection.info("==================================================");
            TaskLogUtils.ProductColorCollection.info("开始查询颜色提取任务","product_info_imageColor");
            TaskLogUtils.ProductColorCollection.info("查询条件：imageColor = {}", query.getImageColor());
            List<ProductInfo> all = productInfoService.selectProductInfoImageColorIsNullList(query);
            TaskLogUtils.ProductColorCollection.info("任务数量：imageColorSize = {}", all.size());

            if (all == null) return Collections.emptyList();
            TaskLogUtils.ProductColorCollection.info("开始执行颜色提取任务", "product_info_imageColor");
            // 限制批量（避免一次取太多导致超时/资源占用过高）
            return all.stream().limit(batchSize).collect(Collectors.toList());
        } catch (Exception e) {
            TaskLogUtils.ProductColorCollection.error("查询待处理颜色识别产品失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理单个产品的颜色识别
     *
     * 步骤：
     * 1) 获取小图 URL（scaleImageUrl）；若为空，则尝试从原图生成小图并上传，回填 imageUrl
     * 2) 调用颜色识别模型接口，得到返回 JSON
     * 3) 从 JSON 中解析主颜色（必要时也可解析次颜色）
     * 4) 将识别结果写回产品（imageColor），并同步回填 scaleImageUrl（如本次生成）
     */
    @Override
    public TaskProcessResult processSingleTask(ProductInfo item) throws IOException {
        Long productId = item.getId();
        String imageUrl = item.getScaleImageUrl();

        // 1) 若小图缺失，现场生成并上传（兜底逻辑）
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            ProductInfo productInfo = productInfoService.selectProductInfoById(productId);
            String productImageUrl = productInfo.getProductImageUrl();
            if (productImageUrl == null || productImageUrl.trim().isEmpty()) {
                return TaskProcessResult.failure("无图片URL，跳过");
            }
            byte[] processedImageData = JpgUrlToWebpConverter.urlToWebpBytes(item.getOriginalImageUrl(), "product_info_imageColor");
//            byte[] processedImageData = imageProcessUtils.downloadAndProcessImage(productInfo.getOriginalImageUrl(), "product_info_imageColor");
            if (processedImageData == null) {

//                TaskLogUtils.ProductColorCollection.error("图片处理失败，任务详情ID: {}", Long.valueOf(item.getBatch()));
                TaskLogUtils.ProductColorCollection.error("图片处理失败，任务详情ID: {}", item.getId());
                return TaskProcessResult.failure("图片处理失败");
            }
            String processedImageUrl = productInfoController.uploadProcessedImage(processedImageData, item.getId());
//            String processedImageUrl = productInfoController.uploadProcessedImage(processedImageData, Long.valueOf(item.getBatch()));
            if (processedImageUrl == null || processedImageUrl.trim().isEmpty()) {
                TaskLogUtils.ProductColorCollection.error("上传处理后图片失败，任务详情ID: {}", item.getId());
//                TaskLogUtils.ProductColorCollection.error("上传处理后图片失败，任务详情ID: {}", Long.valueOf(item.getBatch()));
                return TaskProcessResult.failure("上传处理后图片失败");
            }
            imageUrl = processedImageUrl;
        }
        try {
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                return TaskProcessResult.failure("无小图URL，跳过");
            }
            // 2) 调模型接口
            JSONObject result = callColorModel(imageUrl);
            if (result == null) {
                return TaskProcessResult.failure("模型接口调用失败");
            }

            // 3) 解析颜色
            ColorResult color = extractColorFromResult(result);
            if (color == null || color.primary == null) {
                return TaskProcessResult.failure("未解析到颜色");
            }

            // 4) 回写产品
            ProductInfo update = new ProductInfo();
            update.setId(productId);
            update.setScaleImageUrl(imageUrl);
            update.setImageColor(color.primary);
            productInfoService.updateProductInfo(update);

            return TaskProcessResult.success("颜色识别成功: " + color.primary);
        } catch (Exception e) {
            TaskLogUtils.ProductColorCollection.error("产品颜色识别异常, productId={}", productId, e);
            return TaskProcessResult.failure("异常: " + e.getMessage());
        }
    }

    private JSONObject callColorModel(String imageUrl) {
        try {
            JSONObject body = new JSONObject();
            body.set("query", "请分析图片的主颜色与辅颜色，仅返回JSON: {\"primary_color\":\"...\",\"secondary_color\":\"...\"}");
            body.set("inputs", new JSONObject());
            body.set("response_mode", "blocking");
            body.set("user", "system");

            // files 数组传图片
            cn.hutool.json.JSONArray files = new cn.hutool.json.JSONArray();
            JSONObject file = new JSONObject();
            file.set("type", "image");
            file.set("transfer_method", "remote_url");
            file.set("url", imageUrl);
            files.add(file);
            body.set("files", files);

            HttpResponse resp = HttpRequest.post(MODEL_API_URL)
                    .header("Authorization", "Bearer " + MODEL_API_TOKEN)
                    .header("Content-Type", "application/json")
                    .body(body.toString())
                    .timeout(30000)
                    .execute();

            if (resp.isOk()) {
                return JSONUtil.parseObj(resp.body());
            }
            TaskLogUtils.ProductColorCollection.warn("颜色模型接口响应非200, code={}, body={}", resp.getStatus(), resp.body());
            return null;
        } catch (Exception e) {
            TaskLogUtils.ProductColorCollection.error("调用颜色模型接口异常", e);
            return null;
        }
    }

    private ColorResult extractColorFromResult(JSONObject resp) {
        try {
            if (resp == null) return null;

            // 1) 优先从 answer 节点解析
            JSONObject answer = resp.getJSONObject("answer");
            if (answer != null) {
                ColorResult cr = parseFromObject(answer);
                if (cr != null) return cr;

                // answer.content 可能是字符串/JSON对象/数组
                String contentStr = answer.getStr("content");
                ColorResult fromContent = parseFromString(contentStr);
                if (fromContent != null) return fromContent;

                Object contentObj = answer.get("content");
                if (contentObj instanceof cn.hutool.json.JSONArray) {
                    cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) contentObj;
                    for (Object o : arr) {
                        if (o instanceof JSONObject) {
                            ColorResult cr2 = parseFromObject((JSONObject) o);
                            if (cr2 != null) return cr2;
                            String text = ((JSONObject) o).getStr("text");
                            ColorResult cr3 = parseFromString(text);
                            if (cr3 != null) return cr3;
                        } else if (o instanceof CharSequence) {
                            ColorResult cr4 = parseFromString(o.toString());
                            if (cr4 != null) return cr4;
                        }
                    }
                } else if (contentObj instanceof JSONObject) {
                    ColorResult cr2 = parseFromObject((JSONObject) contentObj);
                    if (cr2 != null) return cr2;
                }
            }

            // 2) 顶层直接给字段
            ColorResult top = parseFromObject(resp);
            if (top != null) return top;

            // 3) 常见 data/outputs 包装
            JSONObject data = resp.getJSONObject("data");
            if (data != null) {
                ColorResult cr = parseFromObject(data);
                if (cr != null) return cr;
                String outText = data.getStr("output_text");
                ColorResult cr2 = parseFromString(outText);
                if (cr2 != null) return cr2;
                cn.hutool.json.JSONArray outputs = data.getJSONArray("outputs");
                if (outputs != null) {
                    for (Object o : outputs) {
                        if (o instanceof JSONObject) {
                            ColorResult cr3 = parseFromObject((JSONObject) o);
                            if (cr3 != null) return cr3;
                            String text = ((JSONObject) o).getStr("text");
                            ColorResult cr4 = parseFromString(text);
                            if (cr4 != null) return cr4;
                            Object content = ((JSONObject) o).get("content");
                            if (content instanceof cn.hutool.json.JSONArray) {
                                for (Object c : (cn.hutool.json.JSONArray) content) {
                                    if (c instanceof JSONObject) {
                                        String t = ((JSONObject) c).getStr("text");
                                        ColorResult cr5 = parseFromString(t);
                                        if (cr5 != null) return cr5;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 4) 深度遍历兜底
            return deepScanForColor(resp);
        } catch (Exception e) {
            TaskLogUtils.ProductColorCollection.error("颜色结果解析失败", e);
            return null;
        }
    }

    private ColorResult parseFromObject(JSONObject obj) {
        if (obj == null) return null;
        // 常见字段名集合
        String[] primKeys = {"product_color", "primary_color", "primary", "main_color", "dominant_color", "color", "colour", "颜色", "主色", "主颜色"};
        String[] secKeys  = {"secondary_color", "secondary", "sub_color", "accent_color", "辅色", "次要颜色"};
        String p = null, s = null;
        for (String k : primKeys) {
            if (obj.containsKey(k)) { p = obj.getStr(k); break; }
        }
        for (String k : secKeys) {
            if (obj.containsKey(k)) { s = obj.getStr(k); break; }
        }
        if (p != null || s != null) return new ColorResult(p, s);
        // 有时嵌入在 message 或 choices 等结构的 text 里
        String text = obj.getStr("text");
        ColorResult fromText = parseFromString(text);
        return fromText;
    }

    private ColorResult parseFromString(String s) {
        if (s == null || s.trim().isEmpty()) return null;
        String t = s.trim();
        // 若为 JSON 对象字符串
        try {
            if (JSONUtil.isTypeJSONObject(t)) {
                JSONObject jo = JSONUtil.parseObj(t);
                return parseFromObject(jo);
            }
        } catch (Exception ignore) {}
        // 尝试从文本中提取 JSON 片段
        int l = t.indexOf('{');
        int r = t.lastIndexOf('}');
        if (l >= 0 && r > l) {
            try {
                JSONObject jo2 = JSONUtil.parseObj(t.substring(l, r + 1));
                ColorResult cr = parseFromObject(jo2);
                if (cr != null) return cr;
            } catch (Exception ignore) {}
        }
        // 简单正则从自然语言里抓取
        try {
            java.util.regex.Matcher m1 = java.util.regex.Pattern.compile("primary[_ ]?color[：:]?\\s*([A-Za-z\\u4e00-\\u9fa5]+)", java.util.regex.Pattern.CASE_INSENSITIVE).matcher(t);
            java.util.regex.Matcher m2 = java.util.regex.Pattern.compile("secondary[_ ]?color[：:]?\\s*([A-Za-z\\u4e00-\\u9fa5]+)", java.util.regex.Pattern.CASE_INSENSITIVE).matcher(t);
            String p = null, s2 = null;
            if (m1.find()) p = m1.group(1);
            if (m2.find()) s2 = m2.group(1);
            if (p != null || s2 != null) return new ColorResult(p, s2);
        } catch (Exception ignore) {}
        return null;
    }

    private ColorResult deepScanForColor(Object node) {
        if (node == null) return null;
        if (node instanceof JSONObject) {
            JSONObject obj = (JSONObject) node;
            ColorResult cr = parseFromObject(obj);
            if (cr != null) return cr;
            for (String key : obj.keySet()) {
                ColorResult sub = deepScanForColor(obj.get(key));
                if (sub != null) return sub;
            }
        } else if (node instanceof cn.hutool.json.JSONArray) {
            for (Object item : (cn.hutool.json.JSONArray) node) {
                ColorResult sub = deepScanForColor(item);
                if (sub != null) return sub;
            }
        } else if (node instanceof CharSequence) {
            return parseFromString(node.toString());
        }
        return null;
    }

    private static class ColorResult {
        final String primary;
        final String secondary;
        ColorResult(String p, String s) { this.primary = p; this.secondary = s; }
    }
}