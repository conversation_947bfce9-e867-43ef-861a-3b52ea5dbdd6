package com.dataxai.web.service;

import com.dataxai.web.domain.MaterialStyle;
import java.util.List;

public interface MaterialStyleService {
    List<MaterialStyle> queryAll(String name, Integer status, Integer categoryId, Integer taskType);

    MaterialStyle getById(Integer id);

    boolean addMaterialStyle(MaterialStyle materialStyle);

    boolean updateMaterialStyle(MaterialStyle materialStyle);

    boolean deleteMaterialStyle(Integer id);

    // 分页查询
    List<MaterialStyle> queryPage(Integer pageNum, Integer pageSize,
                                  String name, Integer status, Integer categoryId, Integer taskType);

    // 获取总数
    int countByCondition(String name, Integer status, Integer categoryId, Integer taskType);
}