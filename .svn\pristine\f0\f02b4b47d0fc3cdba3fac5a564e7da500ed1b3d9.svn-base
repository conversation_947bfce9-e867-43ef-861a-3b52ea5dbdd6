package com.dataxai.web.controller.admincontroller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.web.domain.*;
import com.dataxai.web.service.IAdminTaskOrdinalService;
import com.dataxai.web.service.IAdminUserService;
import com.dataxai.web.utils.CommonUtils;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.service.IAdminTaskService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 任务Controller
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/admintask/admintask")
public class AdminTaskController extends BaseController
{
    @Autowired
    private IAdminTaskService adminTaskService;

    @Autowired
    private IAdminUserService adminUserService;

    @Autowired
    private IAdminTaskOrdinalService adminTaskOrdinalService;

    /**
     * 查询任务列表
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminTask adminTask)
    {

        Map<String, Object> params = adminTask.getParams();
        if(null != params){
            if(params.containsKey("beginCreateTime")){
                String beginCreateTime = (String) params.get("beginCreateTime");
                params.put("beginCreateTime",beginCreateTime+" 00:00:00");
            }
            if(params.containsKey("endCreateTime")){
                String endCreateTime = (String) params.get("endCreateTime");
                params.put("endCreateTime",endCreateTime+" 23:59:59");
            }
        }

        String userPhone = adminTask.getUserPhone();
        if(StringUtils.isNotEmpty(userPhone)){
            AdminUser adminUserParam = new AdminUser();
            adminUserParam.setPhone(userPhone);
            List<AdminUser> adminUserList = adminUserService.selectAdminUserList(adminUserParam);
            if(CollectionUtil.isNotEmpty(adminUserList)){
                AdminUser adminUser = adminUserList.get(0);
                adminTask.setUserId(adminUser.getUserId());
            }
        }
        startPage();
        List<AdminTask> list = adminTaskService.selectAdminTaskList(adminTask);
        List<AdminTaskManager> collect = list.stream().map(item -> {
            AdminTaskManager adminTaskManager = new AdminTaskManager();
            BeanUtils.copyProperties(item, adminTaskManager);
            return adminTaskManager;
        }).collect(Collectors.toList()).stream().peek(item -> {
            AdminUser adminUser = adminUserService.selectAdminUserByUserId(item.getUserId());
            String taskId = item.getTaskId();
            AdminTaskOrdinal adminTaskOrdinalParam = new AdminTaskOrdinal();
            adminTaskOrdinalParam.setTaskId(taskId);
            adminTaskOrdinalParam.setDelFlag(0L);
            List<AdminTaskOrdinal> adminTaskOrdinals = adminTaskOrdinalService.selectTaskOrdinalList(adminTaskOrdinalParam);
            if(CollectionUtil.isNotEmpty(adminTaskOrdinals)){
                AdminTaskOrdinal adminTaskOrdinal = adminTaskOrdinals.get(0);
                if(null != adminTaskOrdinal){
                    String referedTaskOrdinalId = adminTaskOrdinal.getReferedTaskOrdinalId();
//                    logger.info("referedTaskOrdinalId =    "+referedTaskOrdinalId);
                    if (StringUtils.isNotEmpty(referedTaskOrdinalId)) {
                        AdminTaskOrdinal taskOrdinal = adminTaskOrdinalService.selectTaskOrdinalByTaskOrdinalId(referedTaskOrdinalId);
                        if (null != taskOrdinal) {
                            AdminTask adminReferedTask = adminTaskService.selectAdminTaskByTaskId(taskOrdinal.getTaskId());
                            if(null != adminReferedTask){
                                item.setReferedTaskName(adminReferedTask.getTaskName());
                            }
                        }
                    }
                }
            }
//            item.setOriginalUrl(Constants.OOS_URL_PREFIX + item.getOriginalUrl());
            item.setOriginalUrl(CommonUtils.addCosPrefix(item.getOriginalUrl()));
            if(null != adminUser){
                item.setUserPhone(adminUser.getPhone());
            }
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(collect);
        dataTable.setTotal(new PageInfo(list).getTotal());
        return dataTable;
    }

    /**
     * 导出任务列表
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:export')")
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminTask adminTask)
    {
        List<AdminTask> list = adminTaskService.selectAdminTaskList(adminTask);
        ExcelUtil<AdminTask> util = new ExcelUtil<AdminTask>(AdminTask.class);
        util.exportExcel(response, list, "任务数据");
    }

    /**
     * 获取任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId)
    {
        return success(adminTaskService.selectAdminTaskByTaskId(taskId));
    }

    /**
     * 新增任务
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:add')")
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdminTask adminTask)
    {
        return toAjax(adminTaskService.insertAdminTask(adminTask));
    }

    /**
     * 修改任务
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:edit')")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdminTask adminTask)
    {
        return toAjax(adminTaskService.updateAdminTask(adminTask));
    }

    /**
     * 删除任务
     */
    @PreAuthorize("@ss.hasPermi('admintask:admintask:remove')")
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable String[] taskIds)
    {
        return toAjax(adminTaskService.deleteAdminTaskByTaskIds(taskIds));
    }
}
