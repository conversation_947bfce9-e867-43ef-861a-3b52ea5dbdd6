package com.dataxai.web.service.export.strategy;


import com.dataxai.domain.TExcelCustomTemplateDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * 定义了导出操作的策略接口。
 * <p>
 * 每个实现此接口的类代表一种特定的 Excel 导出方式（例如，针对不同类型的模板）。
 * {@link com.dataxai.web.service.export.TExcelExportService} 会根据 {@code templateType} 动态选择合适的策略来执行导出。
 *
 * @see com.dataxai.web.service.export.TExcelExportService
 */
public interface ExportStrategy {

    /**
     * 判断此策略是否支持给定的模板类型。
     *
     * @param templateType 模板类型ID，来自于 {@link TExcelCustomTemplateDTO#getTemplateType()}。
     * @return 如果支持则返回 {@code true}，否则返回 {@code false}。
     */
    boolean supports(Long templateType);

    /**
     * 执行导出操作。
     * <p>
     * 此方法的具体实现通常由 {@link BaseExportStrategy} 提供一个模板化的流程，
     * 而具体的填充逻辑则由最终的策略子类完成。
     *
     * @param dto      包含导出所需全部信息的请求体。
     * @param response HTTP 响应对象，用于将文件流写回客户端。
     * @throws Exception 如果在导出过程中发生任何错误。
     */
    void export(TExcelCustomTemplateDTO dto, HttpServletResponse response) throws Exception;
}

