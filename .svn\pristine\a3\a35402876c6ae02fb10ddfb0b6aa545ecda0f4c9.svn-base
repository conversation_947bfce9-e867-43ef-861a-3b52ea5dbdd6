package com.dataxai.web.batch.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.AbstractBatchTaskFactory;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.utils.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 印花图提取 批次任务工厂
 *
 * 任务类型: 52 (Constants.TASK_TYPE_CROP_EXTRACT)
 */
@Component
@Slf4j
public class CropExtractTaskFactory extends AbstractBatchTaskFactory {

	@Override
	public Long[] getSupportedTaskTypes() {
		return new Long[]{(long) Constants.TASK_TYPE_CROP_EXTRACT};
	}

	@Override
	public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
	                            Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

		// 将临时路径转换成可用的图片URL（会上传到OSS）
		if (!CollectionUtil.isEmpty(pathList)) {
			processPathList(pathList, dto);
		}
		System.out.println("任务开始");
		System.out.println(dto.getImageNumber());
		System.out.println("----------------------------------------------");
//		dto.setImageNumber(imageNumber);
		String taskParam1 = dto.getTaskParam();
		JSONObject jsonObj = new JSONObject(taskParam1);
		int imageNumber = jsonObj.getInt("imageNumber");
		System.out.println(imageNumber);
		dto.setImageNumber((long) imageNumber);
		// 构建任务参数

		String taskParam = buildTaskParam(dto, dto.getType());
		List<Task> taskList = new ArrayList<>();
		List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

		try {
			ObjectMapper objectMapper = new ObjectMapper();
			OrdinalParamDTO param = new OrdinalParamDTO();
			if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getTaskParam())) {
				param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
			}

			// 仅处理有图片URL场景（印花图提取需要原始图片）
			if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getImgUrl())) {
				List<String> imgUrlList = parseImgUrlList(dto.getImgUrl());
				for (String url : imgUrlList) {
					Task task = createBaseTask(dto, userId, param, batch);
					task.setOriginalUrl(CommonUtils.subCosPrefix(url));
					taskList.add(task);

					TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
							task.getTaskId(), url, batch);
					taskOrdinalList.add(taskOrdinal);
				}
			}

		} catch (Exception e) {
			log.error("创建印花图提取任务失败", e);
			throw new RuntimeException("创建印花图提取任务失败", e);
		}

		// 批量插入任务
		insertTasks(taskList);

		// 执行子任务
		processTaskOrdinals(taskOrdinalList);
	}

	@Override
	public List<Task> createTasks(BatchDTO dto, Batch batch, Long userId, String taskParam) {
		// 在 processBatchTask 中已处理
		return new ArrayList<>();
	}

	@Override
	public List<TaskOrdinal> createTaskOrdinals(BatchDTO dto, Batch batch, List<Task> taskList,
	                                           Long userId, String taskParam) {
		// 在 processBatchTask 中已处理
		return new ArrayList<>();
	}
} 