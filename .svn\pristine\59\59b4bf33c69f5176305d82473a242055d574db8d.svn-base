package com.dataxai.web.controller.platform;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.domain.*;
import com.dataxai.domain.dto.TitleExtractionTaskDetailDTO;
import com.dataxai.mapper.TitleExtractionTaskDetailMapper;
import com.dataxai.service.IProductInfoService;
import com.dataxai.service.ITeamUserService;
import com.dataxai.service.ITitleExtractionTaskDetailService;
import com.dataxai.service.ITitleExtractionTaskService;
import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.service.IOrdinalImgResultService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 平台API-标题提取任务接口
 */
@Api(tags = "平台API标题提取任务接口")
@RestController
@RequestMapping("/platform/title/extraction/task")
public class PlatformTitleExtractionTaskController extends BaseController {

    @Autowired
    private ITitleExtractionTaskService titleExtractionTaskService;
    @Autowired
    private ITitleExtractionTaskDetailService titleExtractionTaskDetailService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IOrdinalImgResultService ordinalImgResultService;
    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;
    @Autowired
    private TitleExtractionTaskDetailMapper titleExtractionTaskDetailMapper;

    /** 列表 */
    @ApiOperation("查询标题提取任务列表")
    @GetMapping("/list")
    public R<Map<String, Object>> list(HttpServletRequest request,
                                       @RequestParam(value = "startTime", required = false) String startTime,
                                       @RequestParam(value = "endTime", required = false) String endTime,
                                       @RequestParam(value = "status", required = false) Integer status,
                                       @RequestParam(value = "batchNumber", required = false) String batchNumber,
                                       @RequestParam(value = "userId", required = false)Long userId,
                                       @RequestParam(value = "remark", required = false) String remark) {
        try {
            TUser user = (TUser) request.getAttribute("platformUser");
            if (user == null) return R.fail("用户未授权");

            // 构造查询对象
            TitleExtractionTask query = new TitleExtractionTask();

            // 根据用户模式设置数据过滤条件
            UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(user.getUserId());
                if (teamUser != null && teamUser.getIsAdmin() == true) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                } else {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(user.getUserId());
                }
            } else {
                // 个人模式：通过owner_id过滤，且team_id为0
                query.setOwnerId(user.getUserId());
                query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
            }

            query.setStartTime(startTime);
            query.setEndTime(endTime);
            query.setStatus(status);
            query.setTaskBatch(batchNumber);

            startPage();
            List<TitleExtractionTask> list = titleExtractionTaskService.selectTitleExtractionTaskList(query);
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("total", new PageInfo<>(list).getTotal());
            pageData.put("data", list);
            return R.ok(pageData);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /** 详情 */
    @ApiOperation("获取标题提取任务详情，支持分页查询")
    @GetMapping("/{id}")
    public R<TitleExtractionTaskDetailDTO> getInfo(HttpServletRequest request,
                                    @PathVariable Long id,
                                    @RequestParam(value = "processStatus", required = false) String processStatus) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        TitleExtractionTask titleExtractionTask = titleExtractionTaskService.selectTitleExtractionTaskById(id);
        if (titleExtractionTask == null) return R.fail("标题提取任务不存在");

        // 根据用户模式设置权限验证
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(user.getUserId());
        if (!userTeamInfo.isTeamMode() || userTeamInfo.getTeamId() == null) {
            // 个人模式：必须是任务拥有者
            if (!Objects.equals(titleExtractionTask.getOwnerId(), user.getUserId())) {
                return R.fail("无权限查看此标题提取任务");
            }
        } else {
            // 团队模式：检查是否属于同一团队
            if (!Objects.equals(titleExtractionTask.getTeamId(), userTeamInfo.getTeamId())) {
                return R.fail("无权限查看此标题提取任务");
            }
        }

        // 查询所有详情，支持分页
        startPage();
        List<TitleExtractionTaskDetail> detailList = titleExtractionTaskDetailMapper.selectTitleExtractionTaskDetailByTaskId(id);
        PageInfo<TitleExtractionTaskDetail> pageInfo = new PageInfo<>(detailList);
        // 组装详细信息DTO
        TitleExtractionTaskDetailDTO detailDTO = new TitleExtractionTaskDetailDTO();
        // 复制任务基本信息
        detailDTO.setId(titleExtractionTask.getId());
        detailDTO.setTaskBatch(titleExtractionTask.getTaskBatch());
        detailDTO.setTotalAmount(titleExtractionTask.getTotalAmount());
        detailDTO.setSuccessAmount(titleExtractionTask.getSuccessAmount());
        detailDTO.setFailAmount(titleExtractionTask.getFailAmount());
        detailDTO.setStatus(titleExtractionTask.getStatus());
        detailDTO.setOwnerId(titleExtractionTask.getOwnerId());
        detailDTO.setCreateTime(titleExtractionTask.getCreateTime());
        detailDTO.setUpdateTime(titleExtractionTask.getUpdateTime());
        detailDTO.setRemark(titleExtractionTask.getRemark());
        detailDTO.setTotal(Long.valueOf(pageInfo.getTotal()).intValue());
        detailDTO.setPageNum(pageInfo.getPageNum());
        detailDTO.setPageSize(pageInfo.getPageSize());
        detailDTO.setPages(Long.valueOf(pageInfo.getPages()).intValue());
        detailDTO.setDetailList(detailList);

        return R.ok(detailDTO);
    }

    /** 通过图片链接创建 */
    @ApiOperation("通过图片链接创建标题提取任务")
    @Log(title = "平台API-创建标题提取任务(图片链接)", businessType = BusinessType.INSERT)
    @PostMapping("/createByImageUrls")
    public R<Map<String, Object>> createByImageUrls(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        Integer type = 1;
        Object typeObj = requestData.get("type");
        if (typeObj != null) type = Integer.valueOf(typeObj.toString());

        Object urlsObj = requestData.get("imageUrls");
        List<String> imageUrls = new ArrayList<>();
        if (urlsObj instanceof List) {
            for (Object item : (List<?>) urlsObj) {
                if (item != null && !item.toString().trim().isEmpty()) imageUrls.add(item.toString().trim());
            }
        }
        if (imageUrls.isEmpty()) return R.fail("图片链接列表不能为空");

        try {
            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(user.getUserId());
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1);
            task.setType(type);
            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) return R.fail("创建标题提取任务失败");

            List<TitleExtractionTaskDetail> detailList = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrl);
                detail.setType(3);
                detail.setTypeId(null);
                detail.setProcessStatus(1);
                detail.setOwnerId(user.getUserId());
                detail.setHasUploaded(false);
                detailList.add(detail);
            }
            int detailResult = titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(detailList);
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("successDetails", detailResult);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("创建标题提取任务失败：" + e.getMessage());
        }
    }

    /** 通过产品ID创建 */
    @ApiOperation("通过产品信息ID创建标题提取任务")
    @Log(title = "平台API-创建标题提取任务(产品ID)", businessType = BusinessType.INSERT)
    @PostMapping("/createByProductIds")
    public R<Map<String, Object>> createByProductIds(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");

        Integer taskType = 1;
        Object typeObj = requestData.get("type");
        if (typeObj != null) taskType = Integer.valueOf(typeObj.toString());

        Object productListObj = requestData.get("productList");
        if (!(productListObj instanceof List)) return R.fail("产品信息参数列表不能为空");
        List<Map<String, Object>> productList = (List<Map<String, Object>>) productListObj;
        if (productList.isEmpty()) return R.fail("产品信息参数列表不能为空");
        try {
            List<String> imageUrls = new ArrayList<>();
            List<Long> validIds = new ArrayList<>();
            List<Integer> typeList = new ArrayList<>();
            List<Long> typeIdList = new ArrayList<>();

            for (Map<String, Object> item : productList) {
                Object idObj = item.get("id");
                Object tObj = item.get("type");
                if (idObj == null || tObj == null) continue;
                Long id = Long.valueOf(idObj.toString());
                Integer t = Integer.valueOf(tObj.toString());
                String imageUrl = null;
                if (t == 1) {
                    OrdinalImgResult ordinalImgResult = ordinalImgResultService.selectOrdinalImgResultByImageId(id.toString());
                    if (ordinalImgResult != null && ordinalImgResult.getUserId().equals(user.getUserId())) {
                        String resImgUrl = ordinalImgResult.getResImgUrl();
                        if (resImgUrl != null && !resImgUrl.isEmpty()) imageUrl = com.dataxai.web.utils.CommonUtils.addCosPrefix(resImgUrl);
                    }
                } else if (t == 2) {
                    ProductInfo productInfo = productInfoService.selectProductInfoById(id);
                    if (productInfo != null && Objects.equals(productInfo.getOwnerId(), user.getUserId())) {
                        imageUrl = productInfo.getProductImageUrl();
                    }
                }
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    imageUrls.add(imageUrl);
                    validIds.add(id);
                    typeList.add(t);
                    typeIdList.add(id);
                }
            }
            if (imageUrls.isEmpty()) return R.fail("没有找到有效的图片地址");

            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(user.getUserId());
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(imageUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1);
            task.setType(taskType);
            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) return R.fail("创建标题提取任务失败");

            List<TitleExtractionTaskDetail> detailList = new ArrayList<>();
            for (int i = 0; i < imageUrls.size(); i++) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                detail.setImageUrl(imageUrls.get(i));
                detail.setType(typeList.get(i));
                detail.setTypeId(typeIdList.get(i));
                detail.setProcessStatus(1);
                detail.setOwnerId(user.getUserId());
                detail.setHasUploaded(false);
                detailList.add(detail);
            }
            int detailResult = titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(detailList);
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getId());
            result.put("taskBatch", task.getTaskBatch());
            result.put("totalImages", imageUrls.size());
            result.put("validIds", validIds.size());
            result.put("successDetails", detailResult);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("创建标题提取任务失败：" + e.getMessage());
        }
    }

    /** 删除任务 */
    @ApiOperation("删除标题提取任务")
    @Log(title = "平台API-删除标题提取任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<String> remove(HttpServletRequest request, @PathVariable Long id) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");
        TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(id);
        if (task == null) return R.fail("标题提取任务不存在");
        if (!Objects.equals(task.getOwnerId(), user.getUserId())) return R.fail("无权限删除此标题提取任务");
        int rows = titleExtractionTaskService.deleteTitleExtractionTaskById(id);
        return rows > 0 ? R.ok("删除成功") : R.fail("删除失败");
    }

    /** 批量删除任务 */
    @ApiOperation("批量删除标题提取任务")
    @Log(title = "平台API-批量删除标题提取任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public R<String> removeBatch(HttpServletRequest request, @PathVariable String ids) {
        TUser user = (TUser) request.getAttribute("platformUser");
        if (user == null) return R.fail("用户未授权");
        if (ids == null || ids.trim().isEmpty()) return R.fail("参数不能为空");
        String[] parts = ids.split(",");
        List<Long> idList = new ArrayList<>();
        for (String p : parts) { try { idList.add(Long.valueOf(p.trim())); } catch (Exception ignored) {} }
        if (idList.isEmpty()) return R.fail("没有有效的ID");
        for (Long id : idList) {
            TitleExtractionTask task = titleExtractionTaskService.selectTitleExtractionTaskById(id);
            if (task == null) return R.fail("标题提取任务不存在，ID: " + id);
            if (!Objects.equals(task.getOwnerId(), user.getUserId())) return R.fail("无权限删除标题提取任务，ID: " + id);
        }
        int rows = titleExtractionTaskService.deleteTitleExtractionTaskByIds(idList.toArray(new Long[0]));
        return rows > 0 ? R.ok("批量删除成功") : R.fail("批量删除失败");
    }
}