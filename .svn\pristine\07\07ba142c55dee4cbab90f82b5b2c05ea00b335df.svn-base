package com.dataxai.web.utils.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Redis连接监控器
 * 监控Redis连接状态，自动重连
 */
@Slf4j
@Component
public class RedisConnectionMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isConnected = new AtomicBoolean(true);
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0);

    // 配置参数
    private static final int MAX_CONSECUTIVE_FAILURES = 3;
    private static final int MAX_RECONNECT_ATTEMPTS = 5;
    private static final long RECONNECT_DELAY_MS = 5000; // 5秒
    private static final long MONITOR_INTERVAL_MS = 30000; // 30秒

    @PostConstruct
    public void startMonitoring() {
        if (isRunning.compareAndSet(false, true)) {
            log.info("启动Redis连接监控...");
            scheduler.scheduleWithFixedDelay(this::checkConnection, 10, MONITOR_INTERVAL_MS, TimeUnit.MILLISECONDS);
        }
    }

    @PreDestroy
    public void stopMonitoring() {
        if (isRunning.compareAndSet(true, false)) {
            log.info("停止Redis连接监控...");
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 检查Redis连接状态
     */
    private void checkConnection() {
        try {
            RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
            if (connectionFactory == null) {
                log.error("Redis连接工厂为空");
                handleConnectionFailure();
                return;
            }

            try (RedisConnection connection = connectionFactory.getConnection()) {
                String result = new String(connection.ping());
                if ("PONG".equals(result)) {
                    handleConnectionSuccess();
                } else {
                    log.warn("Redis ping响应异常: {}", result);
                    handleConnectionFailure();
                }
            }
        } catch (Exception e) {
            log.error("Redis连接检查失败: {}", e.getMessage());
            handleConnectionFailure();
        }
    }

    /**
     * 处理连接成功
     */
    private void handleConnectionSuccess() {
        if (!isConnected.get()) {
            log.info("Redis连接已恢复");
            isConnected.set(true);
        }
        consecutiveFailures.set(0);
        reconnectAttempts.set(0);
    }

    /**
     * 处理连接失败
     */
    private void handleConnectionFailure() {
        isConnected.set(false);
        int failures = consecutiveFailures.incrementAndGet();

        log.warn("Redis连接失败，连续失败次数: {}", failures);

        if (failures >= MAX_CONSECUTIVE_FAILURES) {
            attemptReconnect();
        }
    }

    /**
     * 尝试重连Redis
     */
    private void attemptReconnect() {
        int attempts = reconnectAttempts.incrementAndGet();

        if (attempts > MAX_RECONNECT_ATTEMPTS) {
            log.error("Redis重连失败，已达到最大重试次数: {}", MAX_RECONNECT_ATTEMPTS);
            return;
        }

        try {
            log.info("尝试重新连接Redis... (第{}次尝试)", attempts);

            // 等待一段时间再重连
            Thread.sleep(RECONNECT_DELAY_MS * attempts);

            RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
            if (connectionFactory != null) {
                // 强制刷新连接池
                try (RedisConnection connection = connectionFactory.getConnection()) {
                    String result = new String(connection.ping());
                    if ("PONG".equals(result)) {
                        log.info("Redis重连成功");
                        isConnected.set(true);
                        consecutiveFailures.set(0);
                        reconnectAttempts.set(0);
                        return;
                    }
                }
            }

            log.warn("Redis重连失败，将在下次监控周期重试");

        } catch (Exception e) {
            log.error("Redis重连过程中发生异常: {}", e.getMessage());
        }
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get();
    }

    /**
     * 获取连接统计信息
     */
    public String getConnectionStats() {
        return String.format("连接状态: %s, 连续失败次数: %d, 重连尝试次数: %d",
                isConnected.get() ? "正常" : "异常",
                consecutiveFailures.get(),
                reconnectAttempts.get());
    }

    /**
     * 手动触发连接检查
     */
    public void manualCheck() {
        checkConnection();
    }

    /**
     * 强制重连
     */
    public void forceReconnect() {
        log.info("手动触发Redis强制重连");
        consecutiveFailures.set(MAX_CONSECUTIVE_FAILURES);
        attemptReconnect();
    }
}