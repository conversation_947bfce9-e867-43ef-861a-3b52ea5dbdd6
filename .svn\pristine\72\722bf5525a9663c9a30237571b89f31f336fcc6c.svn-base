import { useEffect, useRef, useState, } from 'react';
import { Table, Tag, Progress, Upload, Modal, Button, Radio, Input, message, DatePicker, Pagination, Select } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import { CloudUploadOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getTitleExtractionTaskList, createTitleExtractionTaskByImages, deleteBatch,getTaskRemark, stopBatch,updateTaskRemark} from '@/api/task'
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs'
import { batchZipUrl } from '@/api/common'
import FilterBar, { FilterParams } from '@/component/filter';

export const BatchToolsTitleExtraction = () => {
    const navigate = useNavigate()

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()

    const [batchNumber, setBatchNumber] = useState('') // 批次号
    const [remark, setRemark] = useState('') // 备注
    const [userId, setUsertId] = useState('') // 查询的账号
    const [dateRange, setDateRange] = useState<any>(null) // 日期范围
    const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

    const [isModalVisible, setIsModalVisible] = useState(false)
    const [fileList, setFileList] = useState<UploadFile[]>([])
    const [taskType, setTaskType] = useState<number>(1) // 任务配置类型，默认为1（贵阳）
    
    const [openModal, setOpenModal] = useState<any>(false)//remark modal type
    const [remarkText, setRemarkText] = useState('')
    const [remarkId, setRemarkId] = useState(null)//修改备注需要的id

    // 表格数据
    const [dataSource, setDataSource] = useState([])
    // 表格loading
    const [tableLoading, setTableLoading] = useState(false)
    // 分页配置
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    })
    //提交备注信息
    const editRemark = ()=>{
        updateTaskRemark({remark:remarkText,id:remarkId,taskType: 'title'}).then(res=>{ 
            setOpenModal(false);
        })
    }
    //获取标题提取批次列表
    const fetchBatchListData = async (
        pageNum: number,
        pageSize: number,
        batchNumber: string,
        remark: string,
        userId: string,
        startTime: string,
        endTime: string
    ) => {
        setDataSource([])
        setTableLoading(true)
        try {
            const response: any = await getTitleExtractionTaskList({
                pageNum,
                pageSize,
                batchNumber,
                remark,
                userId,
                startTime,
                endTime
            })
            if (response.data) {
                console.log(response.data, '获取标题提取批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
                setIsFirstLoad(false)
            }
        } catch (error) {
            console.error('获取数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }
    useEffect(() => {
        handleSearch(1, 10)
    }, [])

    // 查询条件存储key
    const QUERY_PARAMS_KEY = 'upload_image_query_params';

    //是否首次加载
    const [isFirstLoad, setIsFirstLoad] = useState(true)
    // Refs 声明（必须放在组件顶层）
    const timerRef = useRef<NodeJS.Timeout>()
    const fetchRef = useRef<typeof refreshBatchListData>()
    const paginationRef = useRef(pagination)
    const isFirstLoadRef = useRef(isFirstLoad)
    // 同步最新状态到 ref
    useEffect(() => {
        paginationRef.current = pagination
        isFirstLoadRef.current = isFirstLoad
    })
    useEffect(() => {
        // 更新函数引用
        fetchRef.current = refreshBatchListData

        const tick = () => {
            if (!isFirstLoadRef.current) {
                console.log('定时刷新')
                fetchRef.current?.(paginationRef.current.current, 10)
            }
        }

        // 清除旧定时器
        if (timerRef.current) clearInterval(timerRef.current)
        // 启动新定时器
        timerRef.current = setInterval(tick, 5000)
        // 清理函数
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current)
            }
        }
    }, [isFirstLoad]) // 依赖项
    //刷新标题提取批次列表
    const refreshBatchListData = async (
        pageNum: number,
        pageSize: number
    ) => {
        try {
            const savedParams = sessionStorage.getItem(QUERY_PARAMS_KEY);
            let queryParams = {
                batchNumber: '',
                remark: '',
                userId: '',
                stringDateRange: [] as string[]
            };

            if (savedParams) {
                queryParams = JSON.parse(savedParams);
            }

            let startTime = '';
            let endTime = '';
            if (queryParams.stringDateRange.length > 0) {
                [startTime, endTime] = queryParams.stringDateRange;
                startTime = startTime ? `${startTime} 00:00:00` : '';
                endTime = endTime ? `${endTime} 23:59:59` : '';
            }
            const response: any = await getTitleExtractionTaskList({
                pageNum,
                pageSize,
                batchNumber: queryParams.batchNumber,
                userId: queryParams.userId || '',
                remark: queryParams.remark || '',
                startTime,
                endTime
            })
            if (response.data) {
                console.log(response, '刷新图片任务批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
            }
        } catch (error) {
            console.error('刷新数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }
    // 查询处理函数
    const handleSearch = (pageNum: number, pageSize: number) => {
        // 存储查询条件到sessionStorage
        const queryParams = {
            batchNumber,
            remark,
            stringDateRange
        };
        sessionStorage.setItem(QUERY_PARAMS_KEY, JSON.stringify(queryParams));
        if (stringDateRange.length > 0) {
            let [startTime, endTime] = stringDateRange
            startTime = startTime ? `${startTime} 00:00:00` : ''
            endTime = endTime ? `${endTime} 23:59:59` : ''
            fetchBatchListData(pageNum, pageSize, batchNumber, remark,userId, startTime, endTime)
        } else {
            fetchBatchListData(pageNum, pageSize, batchNumber, remark,userId,  '', '')
        }
    }

    // 表格列配置
    const columns = [
        {
            title: '任务批次',
            dataIndex: 'taskBatch',
            key: 'taskBatch',
            ellipsis: true,
            align: 'center' as const,
			render: (taskBatch: string, record: any) => (
				<>
					<p>{taskBatch}</p>
					<p>{record.remark}</p>
				</>
			)
        },
        {
            title: '任务数量',
            dataIndex: 'totalAmount',
            key: 'totalAmount',
            width: 140,
            align: 'center' as const,
            render: (totalAmount: number, record: any) => (
                <>
                    <p>总数：{totalAmount}</p>
                    <p>成功：{record.successAmount}</p>
                    {(record.status == 1 || record.status == 3 || record.status == 6) && record.failAmount > 0 && (
                        <p style={{ color: '#cf1322' }}>失败：{record.failAmount}</p>
                    )}
                </>
            )
        },
        {
            title: '任务进度',
            dataIndex: 'successAmount',
            key: 'successAmount',
            align: 'center' as const,
            render: (successAmount: number, record: any) => (
                <Progress
                    percent={
                        (record.status == 2 || record.status == 3)
                            ? Math.round(((successAmount + record.failAmount) / record.totalAmount) * 100)
                            : 0
                    }
                    strokeColor={record.status == '6' ? '#cf1322' : undefined}
                />
            )
        },
        {
            title: '任务状态',
            dataIndex: 'status',
            key: 'status',
            width: 200,
            align: 'center' as const,
            render: (status: string, record: any) => {
                let color = ''
                if (status == '3') {
                    color = 'green'
                } else if (status == '2') {
                    color = 'blue'
                } else if (status == '1') {
                    color = 'orange'
                } else {
                    color = 'red'
                }
                return (
                    <Tag color={color}>
                        {status == '3'
                            ? '已完成'
                            : status == '2'
                                ? '执行中'
                                : status == '1' ? (record.waitTime == 0 ? '排队中 预估1分钟以内' : record.waitTime > 0 ? `排队中 预估(${record.waitTime}分钟)` : '排队中')
                                    : status == '5'
                                        ? '准备中'
                                        : '手动终止'}
                    </Tag>
                )
            }
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            align: 'center' as const,
            render: (createTime: string) => (
                <p>{dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
            )
        },
        {
            title: '操作',
            key: 'action',
            align: 'center' as const,
            render: (_: any, record: any) => (
                <div className="flex justify-center  gap-2">
                    {(record.status == 2 || record.status == 3) &&(
                        <Button
                            type="link"
                            disabled={record.successAmount == 0}
                            style={{
                                color: record.successAmount == 0 ? '#bfbfbf' : '#32649f'
                            }}
                            onClick={() => goDetail(record)}
                            size="small"
                        >
                            查看详情
                        </Button>
                    )}
                    {(record.status == 2 || record.status == 3 )&& record.detailList && (
                        <Button
                            type="link"
                            style={{
                                color: '#32649f'
                            }}
                            onClick={() => handleDownloadImgages(record.detailList, record.id)}
                            size="small"
                            loading={downloadLoading === record.id}
                        >
                            下载
                        </Button>
                    )}
                    
                    {<Button type="link"
                            style={{
                                color: '#32649f'
                            }}
                            onClick={() => {
                                //record.id 任务id
                                getTaskRemark(record.id).then((res: any) => {
                                    console.log(res);
                                    
                                    setRemarkText(res.remark)
                                })
                                //console.log(record);
                                
                                setRemarkId(record.id);
                                setOpenModal(!openModal);
                                
                            }}
                            size="small"
                        >
                            备注
                    </Button>}
                    {/* {record.status == 1 && (
						<Button
							type="link"
							size="small"
							style={{ color: '#cf1322' }}
							onClick={() => handleDelBatch(record.id)}
						>
							删除
						</Button>
					)} */}
                    {/* {record.status == 2 && (
						<Button
							type="link"
							size="small"
							style={{ color: '#cf1322' }}
							onClick={() => handleStopBatch(record.id)}
						>
							手动终止
						</Button>
					)} */}
                </div>
            )
        }
    ]
    // 手动终止批次
    const handleStopBatch = (id: string) => {
        modal.confirm({
            centered: true,
            title: (
                <div className={'text-[18px] text-normal'}> 确认手动终止该批次？</div>
            ),
            content: null,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            onOk() {
                stopBatch(id)
                    .then((res) => {
                        message.success('该批次手动终止成功')
                        setPagination(prev => ({ ...prev, current: 1 }));
                        handleSearch(1, 10)
                    })
                    .catch((err) => {
                        message.error(`该批次手动终止失败：${err?.data?.msg}`)
                        setPagination(prev => ({ ...prev, current: 1 }));
                        handleSearch(1, 10)
                    })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    }
    // 删除批次
    const handleDelBatch = (id: string) => {
        modal.confirm({
            centered: true,
            title: <div className={'text-[18px] text-normal'}> 确认删除该批次？</div>,
            content: null,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            onOk() {
                deleteBatch(id)
                    .then((res) => {
                        message.success('批次删除成功')
                        setPagination(prev => ({ ...prev, current: 1 }));
                        handleSearch(1, 10)
                    })
                    .catch((err) => {
                        message.error(`批次删除失败：${err?.data?.msg}`)
                        setPagination(prev => ({ ...prev, current: 1 }));
                        handleSearch(1, 10)
                    })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    }
    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = (list: any[], batchId: any) => {
        const imgUrls = list.map((img) => img?.imageUrl)
        setDownloadLoading(batchId);
        batchZipUrl({ imageUrls: imgUrls, type: 18 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false);
        })
    }

    // 查看详情
    const goDetail = (record: any) => {
        navigate('/workspace/batchTools/titleExtraction/detail', {
            state: { batchId: record.id }
        })
    }
    const showModal = () => {
        setIsModalVisible(true)
        setFileList([]) // 清空图片列表
    }
    const [creatLoading, setCreatLoading] = useState(false)
    // 创建任务
    const handleOk = async () => {
        try {
            if (fileList.length === 0) {
                messageApi.error('请先上传图片')
                return
            }
            setCreatLoading(true)
            // const files = fileList.map((file) => file.originFileObj as File)
            const files = fileList.map((file) => file.originFileObj)

            createTitleExtractionTaskByImages({ files, type: taskType }).then((data) => {
                messageApi.success('标题提取任务新建成功')
                setIsModalVisible(false)
                setPagination(prev => ({ ...prev, current: 1 }))// 强制刷新分页到第一页
                handleSearch(1, 10) // 刷新批次列表数据
                userinfoService.refresh() // 刷新用户积分
            }).catch((err) => {
                messageApi.error(`创建失败: ${err?.data?.msg}`)
            }).finally(() => {
                setCreatLoading(false)
            })
        } catch (err) {
            messageApi.error('创建失败')
            setCreatLoading(false)
        }
    }

    const handleCancel = () => {
        setIsModalVisible(false)
    }

    let prevFileList: any[] = []
    // 图片上传
    const handleUploadChange = ({
        fileList: newFileList
    }: {
        fileList: any[]
    }) => {
        // 比较新旧文件列表，如果相同则不处理
        if (
            newFileList.length === prevFileList.length &&
            newFileList.every((file, index) => file.uid === prevFileList[index].uid)
        ) {
            return
        }

        prevFileList = newFileList
        const validFiles: UploadFile[] = []

        const promises = newFileList.map((file) => {
            const isImage = /^image\//.test(file.type || '')

            if (!isImage) {
                messageApi.error('请检查文件类型')
                return Promise.resolve(false)
            }

            return new Promise<boolean>((resolve) => {
                const reader = new FileReader()
                const originFileObj = file.originFileObj
                if (!originFileObj) {
                    messageApi.error('无法获取文件对象,请检查文件')
                    resolve(false)
                    return
                }
                reader.readAsArrayBuffer(originFileObj)
                reader.onload = async () => {
                    try {
                        const blob = new Blob([reader.result as ArrayBuffer])
                        const img = await createImageBitmap(blob)
                        const { width, height } = img
                        const isValidSize = width <= 4096 && height <= 4096
                        if (!isValidSize) {
                            messageApi.error('部分图片尺寸超过限制(4096x4096)，已跳过')
                            resolve(false)
                        } else {
                            resolve(true)
                        }
                    } catch (error) {
                        messageApi.error('图片加载失败，请检查图片格式')
                        resolve(false)
                    }
                }
                reader.onerror = () => {
                    messageApi.error('读取文件失败，请检查文件格式')
                    resolve(false)
                }
            })
        })

        Promise.all(promises).then((results) => {
            newFileList.forEach((file, index) => {
                if (results[index]) {
                    validFiles.push(file)
                }
            })

            const totalFiles = fileList.length + validFiles.length
            if (totalFiles > 50) {
                messageApi.error('最多只能上传50张图片')
                return
            }

            if (validFiles.length > 0) {
                messageApi.success(`成功上传 ${validFiles.length} 张图片`);
                setFileList((prev) => [...prev, ...validFiles])
            }
        })
    }

    // 在组件中添加分页变化处理函数
    const handleTableChange = (pagination: any) => {
        setPagination({
            ...pagination,
            current: pagination.current,
            pageSize: pagination.pageSize
        })
        handleSearch(pagination.current, pagination.pageSize)
    }
    const onSearch = (params: FilterParams) => {
        // 你的分页状态
        const pageNum = 1;
        const pageSize = 10;
        fetchBatchListData(
            pageNum,
            pageSize,
            params.batchNumber ?? '',
            params.remark ?? '',
            params.userId ?? '11',
            params.startTime,
            params.endTime
        );
    }; 
    return (
        <>
        <div className="h-full w-full p-[20px]">
            {contextHolder} {/* 这里确保 Modal 挂载 */}
            {messageContextHolder} {/* 这里确保 Message 挂载 */}
            <div className="w-full flex items-center justify-between h-[60px] border-b-[1px] border-normal">
                <Button type="primary" onClick={showModal}>
                    新建标题提取任务
                </Button>
                {/* 新建标题提取任务弹窗 start */}
                <Modal
                    title="新建标题提取任务"
                    open={isModalVisible}
                    onOk={handleOk}
                    onCancel={handleCancel}
                    width={660}
                    centered
                    okText="创建"
                    confirmLoading={creatLoading}
                >
                    <div className="flex justify-between items-center" style={{ marginBottom: '20px' }}>
                        <p className="w-[110px]">标题模板</p>
                        <Select
                            defaultValue={1}
                            style={{ width: 260 }}
                            onChange={(value) => setTaskType(value)}
                            value={taskType}
                            options={[
                                { value: 1, label: '合肥' },
                                { value: 2, label: '贵阳' }
                            ]}
                        />
                    </div>
                    <Upload.Dragger
                        multiple
                        accept="image/*"
                        fileList={[]} // 设置为空数组隐藏自带列表
                        onChange={handleUploadChange}
                        beforeUpload={() => false} // 阻止自动上传
                        className="upload-area"
                        listType="picture"
                        showUploadList={false} // 完全隐藏上传列表
                        disabled={fileList.length >= 50} // 添加禁用状态
                        style={{ marginTop: '30px' }}
                        directory={true} // 允许选择或拖拽文件夹
                    >
                        <p className="ant-upload-drag-icon">
                            <CloudUploadOutlined />
                        </p>
                        <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                        <p className="ant-upload-hint">
                            最多上传50张图片,单张图片最大4096*4096,支持jpg/png等格式
                        </p>
                    </Upload.Dragger>
                    <div className="mt-2 text-right">
                        已选择图片张数:{' '}
                        <span className={fileList.length >= 51 ? 'text-red-500' : 'text-primary'}>
                            {fileList.length}
                        </span>{' '}
                        / 50
                    </div>
                </Modal>
                {/* 新建标题提取任务弹窗 end */}
                
                <FilterBar
                    fields={['batchNumber', 'dateRange', 'remark','userId']}
                    storageKey={QUERY_PARAMS_KEY}
                    withTime={false}
                    onSearch={onSearch}
                />
                {/* <div className="flex items-center gap-2 ]">
                    <Input
                        style={{ width: '150px' }}
                        value={batchNumber}
                        onChange={(e) => setBatchNumber(e.target.value)}
                        placeholder="输入批次号"
                    />
                    <DatePicker.RangePicker
                        style={{ width: '250px' }}
                        value={dateRange}
                        onChange={(dates, dateStrings) => {
                            // dates 是 Moment 对象数组，包含完整时间信息
                            // dateStrings 是格式化后的字符串数组
                            console.log('日期范围:', dates, dateStrings)
                            setDateRange(dates)
                            setStringDateRange(dateStrings)
                        }}
                        format="YYYY-MM-DD"
                        disabledDate={(current) => {
                            return current && current > dayjs().endOf('day')
                        }}
                    />
                    <Button
                        type="primary"
                        onClick={() => {
                            setPagination(prev => ({ ...prev, current: 1 }));
                            handleSearch(1, 10)
                        }}
                        loading={false}
                    >
                        查询
                    </Button>
                </div> */}
            </div>
            <div className="w-[calc(100vw-144px)]  h-[calc(100vh-192px)] overflow-y-scroll scrollbar-container scrollbar-hide">
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    className="mt-4"
                    scroll={{ x: 966 }}
                    loading={tableLoading}
                    pagination={{
                        ...pagination,
                        showTotal: (total: number) => `共 ${total} 条`,
                        showSizeChanger: false
                    }}
                    onChange={handleTableChange}
                />
            </div>
        </div>
        <Modal
            title="添加备注信息"
            visible={openModal}
            onOk={editRemark}
            onCancel={() => setOpenModal(false)}
            okText="确定"
            cancelText="取消"
        >
            <Input placeholder="备注" onChange={(e)=>setRemarkText(e.target.value)} value={remarkText} />
        </Modal>
        </>
        
    )
};

export default BatchToolsTitleExtraction;










