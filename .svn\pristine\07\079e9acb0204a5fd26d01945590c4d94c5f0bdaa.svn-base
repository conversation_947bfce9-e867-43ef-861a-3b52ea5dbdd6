package com.dataxai.web.gtask.processor;

/**
 * 标题提取结果封装类
 * 用于封装从API返回结果中解析出的所有字段
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
public class TitleExtractionResult {

    /** 商品标题 */
    private String productTitle;

    /** TEMU商品标题 */
    private String temuProductTitle;

    /** 商品描述 */
    private String productDescription;

    /** Meta描述 */
    private String metaDescription;

    public TitleExtractionResult() {
    }

    public TitleExtractionResult(String productTitle, String temuProductTitle,
                               String productDescription, String metaDescription) {
        this.productTitle = productTitle;
        this.temuProductTitle = temuProductTitle;
        this.productDescription = productDescription;
        this.metaDescription = metaDescription;
    }

    public String getProductTitle() {
        return productTitle;
    }

    public void setProductTitle(String productTitle) {
        this.productTitle = productTitle;
    }

    public String getTemuProductTitle() {
        return temuProductTitle;
    }

    public void setTemuProductTitle(String temuProductTitle) {
        this.temuProductTitle = temuProductTitle;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public String getMetaDescription() {
        return metaDescription;
    }

    public void setMetaDescription(String metaDescription) {
        this.metaDescription = metaDescription;
    }

    /**
     * 获取主要标题（优先返回商品标题）
     */
    public String getPrimaryTitle() {
        if (productTitle != null && !productTitle.isEmpty()) {
            return productTitle;
        }
        if (temuProductTitle != null && !temuProductTitle.isEmpty()) {
            return temuProductTitle;
        }
        return "";
    }

    /**
     * 检查是否有有效的标题数据
     */
    public boolean hasValidTitle() {
        return (productTitle != null && !productTitle.isEmpty()) ||
               (temuProductTitle != null && !temuProductTitle.isEmpty());
    }

    /**
     * 检查是否所有字段都为空
     */
    public boolean isEmpty() {
        return (productTitle == null || productTitle.isEmpty()) &&
               (temuProductTitle == null || temuProductTitle.isEmpty()) &&
               (productDescription == null || productDescription.isEmpty()) &&
               (metaDescription == null || metaDescription.isEmpty());
    }

    @Override
    public String toString() {
        return "TitleExtractionResult{" +
                "productTitle='" + productTitle + '\'' +
                ", temuProductTitle='" + temuProductTitle + '\'' +
                ", productDescription='" + (productDescription != null ? productDescription.substring(0, Math.min(50, productDescription.length())) + "..." : null) + '\'' +
                ", metaDescription='" + (metaDescription != null ? metaDescription.substring(0, Math.min(50, metaDescription.length())) + "..." : null) + '\'' +
                '}';
    }
}
