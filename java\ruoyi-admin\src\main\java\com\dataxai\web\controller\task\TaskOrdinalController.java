package com.dataxai.web.controller.task;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.service.ITaskOrdinalService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.SecurityUtils;

/**
 * 任务次数Controller
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@RestController
@RequestMapping("/task-ordinal/ordinal")
//@Api(tags={"【任务次数】Controller"})
public class TaskOrdinalController extends BaseController<TaskOrdinal>
{
    @Autowired
    private ITaskOrdinalService taskOrdinalService;

    /**
     * 查询任务次数列表
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:list')")
    @GetMapping("/list")
    public TableDataInfo<TaskOrdinal> list(TaskOrdinal taskOrdinal)
    {
        startPage();
        List<TaskOrdinal> list = taskOrdinalService.selectTaskOrdinalList(taskOrdinal);
        return getDataTable(list);
    }

    /**
     * 导出任务次数列表
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:export')")
    @Log(title = "任务次数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskOrdinal taskOrdinal)
    {
        List<TaskOrdinal> list = taskOrdinalService.selectTaskOrdinalList(taskOrdinal);
        ExcelUtil<TaskOrdinal> util = new ExcelUtil<TaskOrdinal>(TaskOrdinal.class);
        util.exportExcel(response, list, "任务次数数据");
    }

    /**
     * 获取任务次数详细信息
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:query')")
    @GetMapping(value = "/{taskOrdinalId}")
    public AjaxResult getInfo(@PathVariable("taskOrdinalId") String taskOrdinalId)
    {
        return success(taskOrdinalService.selectTaskOrdinalByTaskOrdinalId(taskOrdinalId));
    }

    /**
     * 新增任务次数
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:add')")
    @Log(title = "任务次数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskOrdinal taskOrdinal)
    {
        return toAjax(taskOrdinalService.insertTaskOrdinal(taskOrdinal));
    }

    /**
     * 修改任务次数
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:edit')")
    @Log(title = "任务次数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskOrdinal taskOrdinal)
    {
        return toAjax(taskOrdinalService.updateTaskOrdinal(taskOrdinal));
    }

    /**
     * 删除任务次数
     */
    @PreAuthorize("@ss.hasPermi('task-ordinal:ordinal:remove')")
    @Log(title = "任务次数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskOrdinalIds}")
    public AjaxResult remove(@PathVariable String[] taskOrdinalIds)
    {
        return toAjax(taskOrdinalService.deleteTaskOrdinalByTaskOrdinalIds(taskOrdinalIds));
    }
}
