package com.dataxai.web.controller.front;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.ProductInfo;
import com.dataxai.domain.TeamUser;
import com.dataxai.service.IProductInfoService;
import com.dataxai.service.ITeamUserService;
import com.dataxai.web.task.core.TaskScoreService;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.BatchProductRequest;
import com.dataxai.common.utils.TaskLogUtils;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.gtask.utils.ImageProcessUtils;
import com.dataxai.web.utils.JpgUrlToWebpConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 产品信息表Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Api(tags = "产品信息")
@RestController
@RequestMapping("/product/info")
public class ProductInfoController extends BaseController
{
    @Autowired
    private IProductInfoService productInfoService;

    @Autowired
    private TaskScoreService taskScoreService;

    @Autowired
    private UserTeamInfoService userTeamInfoService;
    @Autowired
    private ITeamUserService iTeamUserService;

    @Autowired
    private AliYunFileService aliYunFileService;
    @Autowired
    private ImageProcessUtils imageProcessUtils;
    @Autowired
    private JpgUrlToWebpConverter jpgUrlToWebpConverter;

    /**
     * 新增产品信息表
     */
    @ApiOperation("新增产品信息")
    @Log(title = "产品信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "commentNum", required = false) String commentNum,
            @RequestParam(value = "originalImageUrl", required = false) String originalImageUrl,
            @RequestParam(value = "productLink", required = false) String productLink,
            @RequestParam(value = "productPrice", required = false) String productPrice,
            @RequestParam(value = "productTitle", required = false) String productTitle,
            @RequestParam(value = "sourcePlatform", required = false) String sourcePlatform,
            @RequestParam(value = "starLevel", required = false) String starLevel
    )
    {
        // 扣除积分（每个产品扣1分）
        Long totalPoints = 1L;
        boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, TaskLogUtils.TASK_TYPE_PRODUCT_COLLECTION);
        if (!deductioned) {
            return AjaxResult.error("积分不足，无法新增产品信息");
        }

        ProductInfo productInfo = new ProductInfo();
        try {
            productInfo.setCommentNum(commentNum != null && !commentNum.isEmpty() ? Integer.parseInt(commentNum) : null);
        } catch (NumberFormatException e) {
            return AjaxResult.error("commentNum 参数格式错误");
        }
        productInfo.setProductLink(productLink);
        productInfo.setProductPrice(productPrice);
        productInfo.setProductTitle(productTitle);
        productInfo.setSourcePlatform(sourcePlatform);
        try {
            productInfo.setStarLevel(starLevel != null && !starLevel.isEmpty() ? Float.parseFloat(starLevel) : null);
        } catch (NumberFormatException e) {
            return AjaxResult.error("starLevel 参数格式错误");
        }

        // 设置用户ID和用户名
        productInfo.setOwnerId(getUserId());
        productInfo.setOwnerName(getUsername());

        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            productInfo.setTeamId(userTeamInfo.getTeamId());
        } else {
            productInfo.setTeamId(Long.valueOf(0));
        }

        // 默认状态：1（待同步图片）
        productInfo.setStatus(1);

        // 处理文件上传到阿里云
        String aliUrl = null;
        try {
            if (file != null && !file.isEmpty()) {
                aliUrl = aliYunFileService.uploadALiYun(file);
            }
        } catch (Exception e) {
            logger.error("上传文件到阿里云失败", e);
        }

        if (aliUrl != null && !aliUrl.isEmpty()) {
            // 上传成功：保存到product_image_url，状态设为2
            productInfo.setProductImageUrl(aliUrl);
            productInfo.setOriginalImageUrl(originalImageUrl);
            productInfo.setStatus(2);
        } else {
            // 上传失败或无文件：把原始图片地址存入original_image_url，状态为1
            productInfo.setOriginalImageUrl(originalImageUrl);
            productInfo.setStatus(1);
        }

        // 保存产品信息
        int result = productInfoService.insertProductInfo(productInfo);
        return toAjax(result);
    }

    /**
     * 批量新增产品信息表（支持batch检查）
     */
    @ApiOperation("批量新增产品信息")
    @Log(title = "产品信息表", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult addBatch(@RequestBody BatchProductRequest batchRequest)
    {
        try {
            if (batchRequest == null || batchRequest.getProducts() == null || batchRequest.getProducts().isEmpty()) {
                return AjaxResult.error("产品信息列表不能为空");
            }

            if (batchRequest.getBatch() == null || batchRequest.getBatch().trim().isEmpty()) {
                return AjaxResult.error("批次号不能为空");
            }

            // 转换DTO为ProductInfo对象
            List<ProductInfo> productInfoList = new ArrayList<>();
            for (BatchProductRequest.ProductItem item : batchRequest.getProducts()) {
                ProductInfo productInfo = new ProductInfo();
                productInfo.setCommentNum(item.getCommentNum());
                productInfo.setOriginalImageUrl(item.getOriginalImageUrl());
                // 1. 下载并处理图片（转换为小图）
                byte[] processedImageData = JpgUrlToWebpConverter.urlToWebpBytes(item.getOriginalImageUrl(), "product_info_imageColor");
//                 imageProcessUtils.downloadAndProcessImage(item.getOriginalImageUrl(), "product_info_imageColor");
                if (processedImageData == null) {
                    TaskLogUtils.ProductColorCollection.error("图片处理失败，任务详情ID: {}", Long.valueOf(batchRequest.getBatch()));
                    return error("图片处理失败");
                }
                // 2. 上传处理后的图片到阿里云
                String processedImageUrl = uploadProcessedImage(processedImageData, Long.valueOf(batchRequest.getBatch()));
                if (processedImageUrl == null) {
                    TaskLogUtils.ProductColorCollection.error("上传处理后图片失败，任务详情ID: {}", Long.valueOf(batchRequest.getBatch()));
                    return error("上传处理后图片失败");
                }
                productInfo.setScaleImageUrl(processedImageUrl);
                productInfo.setProductLink(item.getProductLink());
                // 将Object类型的productPrice转换为String
                productInfo.setProductPrice(item.getProductPrice() != null ? item.getProductPrice().toString() : null);
                productInfo.setProductTitle(item.getProductTitle());
                productInfo.setSourcePlatform(item.getSourcePlatform());
                productInfo.setStarLevel(item.getStarLevel());
                UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
                if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                    // 团队模式：直接通过team_id过滤
                    productInfo.setTeamId(userTeamInfo.getTeamId());

                }
                // 设置用户ID和用户名
                Long userId = getUserId();
                String username = getUsername();
                System.out.println("用户ID: " + userId + ", 用户名: " + username);

                if (userId == null) {
                    return AjaxResult.error("用户未登录或登录已过期");
                }

                productInfo.setOwnerId(userId);
                productInfo.setOwnerName(username != null ? username : "未知用户");

                // 设置状态为1（待同步图片）
                productInfo.setStatus(1);

                productInfoList.add(productInfo);
            }

            // 调用Service进行批量插入（带批次号检查）
            Map<String, Object> batchResult = productInfoService.batchInsertProductInfoWithBatch(productInfoList, batchRequest.getBatch());

            // 添加调试信息
            System.out.println("Service返回结果: " + batchResult);

            // 安全获取Map中的值，避免空指针异常
            boolean success = batchResult.get("success") != null ? (Boolean) batchResult.get("success") : false;
            String message = batchResult.get("message") != null ? (String) batchResult.get("message") : "未知错误";
            int existingCount = batchResult.get("existingCount") != null ? (Integer) batchResult.get("existingCount") : 0;
            int newCount = batchResult.get("newCount") != null ? (Integer) batchResult.get("newCount") : 0;
            int failCount = batchResult.get("failCount") != null ? (Integer) batchResult.get("failCount") : 0;
            int totalCount = batchResult.get("totalCount") != null ? (Integer) batchResult.get("totalCount") : 0;

            if (!success) {
                // 批次已使用过，不需要扣除积分
                return AjaxResult.error(message);
            }

            // 扣除积分（每个产品扣1分，只扣除新增的数量）
            Long totalPoints = (long) newCount;
            boolean deductioned = taskScoreService.deductionPoints(getUserId(), totalPoints, Constants.SCORE_TYPE_4, TaskLogUtils.TASK_TYPE_PRODUCT_COLLECTION);
            if (!deductioned) {
                return AjaxResult.error("积分不足，无法批量新增产品信息");
            }

            // 处理图片上传任务
            List<ProductInfo> uploadTasks = new ArrayList<>();
            for (ProductInfo productInfo : productInfoList) {
                if (productInfo.getId() != null && productInfo.getOriginalImageUrl() != null && !productInfo.getOriginalImageUrl().isEmpty()) {
                    uploadTasks.add(productInfo);
                }
            }

            // 注释：图片上传现在由定时任务处理，不再使用队列
            // 定时任务会自动处理status=1的记录，无需手动添加任务

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", message);
            result.put("existingCount", existingCount);
            result.put("newCount", newCount);
            result.put("failCount", failCount);
            result.put("totalCount", totalCount);
            result.put("batch", batchRequest.getBatch());

            return AjaxResult.success("批量新增完成", result);
        } catch (Exception e) {
            logger.error("批量新增产品信息失败", e);
            return AjaxResult.error("批量新增产品信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询产品信息表列表（只保留分页、时间范围、来源平台）
     */
    @ApiOperation("查询产品信息列表")
    @GetMapping("/list")
    public AjaxResult list(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "sourcePlatform", required = false) String sourcePlatform,
            @RequestParam(value = "infringementMark", required = false)Integer infringementMark,
            @RequestParam(value = "imageColor", required = false)String imageColor,
            @RequestParam(value = "userId", required = false)Long userId,
            @RequestParam(value = "remark", required = false) String remark
    ) {
        // 构造查询对象
        ProductInfo query = new ProductInfo();

        // 根据用户模式设置数据过滤条件
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：直接通过team_id过滤
            Long LoginUserId = SecurityUtils.getUserId();

            TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(LoginUserId);
            if (teamUser.getIsAdmin() == true){
                Long selectUserId = userId;
                if (selectUserId != null ) {
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(selectUserId);
                }else{
                    query.setTeamId(userTeamInfo.getTeamId());
                    query.setOwnerId(null); // 清空userId，避免冲突
                }

            }else{
                query.setTeamId(userTeamInfo.getTeamId());
                query.setOwnerId(LoginUserId);
            }
        } else {
            // 个人模式：通过owner_id过滤，且team_id为0
            query.setOwnerId(getUserId());
            query.setTeamId(Long.valueOf(0)); // 确保不设置teamId
        }

        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setSourcePlatform(sourcePlatform);
        query.setInfringementMark(infringementMark);
        query.setImageColor(imageColor);
        query.setRemark(remark);
        startPage();
        List<ProductInfo> list = productInfoService.selectProductInfoList(query);

        // 组装自定义分页结构
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("data", list);

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 获取产品信息基本信息（包含备注）
     */
    @ApiOperation("获取产品信息基本信息")
    @GetMapping(value = "/basic/{id}")
    public AjaxResult getBasicInfo(@PathVariable Long id) {
        ProductInfo productInfo = productInfoService.selectProductInfoById(id);
        if (productInfo == null) {
            return AjaxResult.error("产品信息不存在");
        }
        
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getCurrentUserTeamInfo();
        if (!userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() == null){
            if (!productInfo.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限查看此产品信息");
            }
        }
        
        return AjaxResult.success(productInfo);
    }

    /**
     * 获取产品信息详细信息
     */
    @ApiOperation("获取产品信息详细信息")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id)
    {
        ProductInfo productInfo = productInfoService.selectProductInfoById(id);
        if (productInfo == null) {
            return AjaxResult.error("产品信息不存在");
        }
        if (!productInfo.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限查看此产品信息");
        }
        return AjaxResult.success(productInfo);
    }

    /**
     * 修改产品信息表
     */
    @ApiOperation("修改产品信息")
    @Log(title = "产品信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductInfo productInfo)
    {
        // 验证产品是否属于当前用户
        ProductInfo existingProduct = productInfoService.selectProductInfoById(productInfo.getId());
        if (existingProduct == null) {
            return AjaxResult.error("产品信息不存在");
        }
        if (!existingProduct.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限修改此产品信息");
        }

        // 设置用户ID和用户名（确保不被篡改）
        productInfo.setOwnerId(getUserId());
        productInfo.setOwnerName(getUsername());

        // 处理图片更新逻辑
        if (productInfo.getProductImageUrl() != null && !productInfo.getProductImageUrl().isEmpty()) {
            productInfo.setOriginalImageUrl(productInfo.getProductImageUrl());
            productInfo.setProductImageUrl(null);
            productInfo.setStatus(1); // 重新设置为待同步状态
        }

        int result = productInfoService.updateProductInfo(productInfo);

        // 注释：图片上传现在由定时任务处理，不再使用队列
        // 定时任务会自动处理status=1的记录

        return toAjax(result);
    }

    /**
     * 删除产品信息表
     */
    @ApiOperation("删除产品信息")
    @Log(title = "产品信息表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        // 验证产品是否属于当前用户
        ProductInfo productInfo = productInfoService.selectProductInfoById(id);
        if (productInfo == null) {
            return AjaxResult.error("产品信息不存在");
        }
        if (!productInfo.getOwnerId().equals(getUserId())) {
            return AjaxResult.error("无权限删除此产品信息");
        }

        return toAjax(productInfoService.deleteProductInfoById(id));
    }

    /**
     * 批量删除产品信息表
     */
    @ApiOperation("批量删除产品信息")
    @Log(title = "产品信息表", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult removeBatch(@PathVariable Long[] ids)
    {
        // 验证所有产品是否属于当前用户
        for (Long id : ids) {
            ProductInfo productInfo = productInfoService.selectProductInfoById(id);
            if (productInfo == null) {
                return AjaxResult.error("产品信息不存在，ID: " + id);
            }
            if (!productInfo.getOwnerId().equals(getUserId())) {
                return AjaxResult.error("无权限删除产品信息，ID: " + id);
            }
        }

        return toAjax(productInfoService.deleteProductInfoByIds(ids));
    }
    /**
     * 将图片略缩图上传至阿里云
     */
    public String uploadProcessedImage(byte[] imageData, Long taskDetailId) {
        try {
            TaskLogUtils.ProductColorCollection.info("开始上传处理后的图片，任务详情ID: {}", taskDetailId);

            // 使用ByteArrayResource包装图片数据
            org.springframework.core.io.ByteArrayResource resource =
                    imageProcessUtils.createImageResource(imageData);

            String uploadedUrl = aliYunFileService.uploadImageWebp(resource);
            TaskLogUtils.ProductColorCollection.info("处理后图片上传成功，任务详情ID: {}, URL: {}",
                    taskDetailId, uploadedUrl);
            return uploadedUrl;
        } catch (Exception e) {
            TaskLogUtils.ProductColorCollection.error("上传处理后图片异常，任务详情ID: {}", taskDetailId, e);
            return null;
        }
    }



//    @GetMapping("/status")
//    public ProductColorGTaskScheduler.SchedulerStatus status() { return scheduler.getSchedulerStatus(); }
}
