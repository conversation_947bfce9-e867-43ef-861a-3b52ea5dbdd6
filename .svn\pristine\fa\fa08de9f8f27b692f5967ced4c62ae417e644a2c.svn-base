
import { useEffect, useState, useMemo } from 'react';
import { Table, Modal, Button, message, DatePicker, Checkbox, Select, Rate } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { ExclamationCircleFilled } from '@ant-design/icons';
import { userinfoService } from '@/common/services/userinfo/userinfoService'
import { getGatherList, deleteGather, deleteGatherBatch } from '@/api/task'
import { LikeImage } from '@/component/generate-image/LikeImage'
import { useAtomMethod } from '@/helper/hooks/atom-method-hook/useAtomMethod'
import dayjs from 'dayjs'
import { batchZipUrl } from '@/api/common'
import BottomActionBar from '@/component/batch-tools/BottomActionBar'
import { checkImageDisabled } from '@/utils/tools'

export const BatchToolsGather = () => {

    const [modal, contextHolder] = Modal.useModal()
    const [messageApi, messageContextHolder] = message.useMessage()

    const [userInfo] = useAtomMethod(userinfoService.userInfo)

    const [dateRange, setDateRange] = useState<any>(null) // 日期范围
    const [stringDateRange, setStringDateRange] = useState<any[]>([]) // 日期范围字符串类型

    const [sourcePlatform, setSourcePlatform] = useState('') // 来源平台
    const [colorFilter, setColorFilter] = useState<any>(null)  // 颜色筛选
    const handleSourcePlatformChange = (value: string) => {
        console.log(`selected ${value}`);
        setSourcePlatform(value)
    };
    const [infringementMark, setInfringementMark] = useState<any>(null) // 是否上传过侵权风险过滤
    const handleInfringementMarkChange = (value: number) => {
        console.log(`selected ${value}`);
        setInfringementMark(value)
    };

    // 表格数据
    const [dataSource, setDataSource] = useState([])
    // 表格loading
    const [tableLoading, setTableLoading] = useState(false)
    // 分页配置
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 50,
        total: 0
    })
    //获取数据采集批次列表
    const fetchBatchListData = async (
        pageNum: number,
        pageSize: number,
        sourcePlatform: string,
        infringementMark: any,
        imageColor: string,
        startTime: string,
        endTime: string
    ) => {
        setDataSource([])
        setTableLoading(true)
        try {
            const response: any = await getGatherList({
                pageNum,
                pageSize,
                sourcePlatform,
                infringementMark,
                imageColor,
                startTime,
                endTime
            })
            if (response.data) {
                console.log(response, '获取数据采集批次列表')
                setDataSource(response.data)
                setPagination((prev) => ({
                    ...prev,
                    total: response.total
                }))
            }
        } catch (error) {
            console.error('获取数据时出错：', error)
        } finally {
            setTableLoading(false)
        }
    }
    useEffect(() => {
        handleSearch(1, 50)
    }, [])

    // 查询处理函数
    const handleSearch = (pageNum: number, pageSize: number) => {
        if (stringDateRange.length > 0) {
            let [startTime, endTime] = stringDateRange
            fetchBatchListData(pageNum, pageSize, sourcePlatform, infringementMark, colorFilter, startTime, endTime)
        } else {
            fetchBatchListData(pageNum, pageSize, sourcePlatform, infringementMark, colorFilter, '', '')
        }
    }

    // 图片列表渲染
    const getTaskImageComponent = useMemoizedFn(
        (image: any) => {
            return (
                <div
                    className={
                        'aspect-square w-full h-full'
                    }
                    key={image.id}
                >
                    <LikeImage
                        type={image.id}
                        imageId={''}
                        taskId={''}
                        taskOrdinalId={''}
                        imgUrl={image.productImageUrl || ''}
                        oriImgUrl={image.productImageUrl || ''}
                        smallImgUrl={image.scaleImageUrl || image.productImageUrl || ''}
                        markImgUrl={image.productImageUrl || ''}
                        progress={!image.productImageUrl ? 0 : 1}
                        previewImages={[]}
                        index={0}
                        seed={-1}
                        delVisible={false}
                        likeVisible={false}
                        downloadVisible={true}
                        comparison={false}
                    />
                </div>
            )
        }
    )

    const [selectedImages, setSelectedImages] = useState<any[]>([]);

    const handleSelect = (task: any) => {
        setSelectedImages(prev => {
            const newSelected = prev.some(item => item.id === task.id)
                ? prev.filter(item => item.id !== task.id)
                : [...prev, task];
            return newSelected;
        });
    };

    // 表格列配置
    const columns = [
        {
            title: (
                <Checkbox
                    checked={dataSource.length > 0 && dataSource.filter((item: any) => !checkImageDisabled(item, 'gather')).every((item: any) =>
                        selectedImages.some(selected => selected.id === item.id)
                    )}
                    onChange={(e) => {
                        const enabledItems = dataSource.filter((item: any) => !checkImageDisabled(item, 'gather'));
                        const currentPageIds = enabledItems.map((item: any) => item.id);
                        if (e.target.checked) {
                            const currentPageItems = enabledItems.filter(
                                (item: any) => !selectedImages.some(selected => selected.id === item.id)
                            );
                            setSelectedImages([...selectedImages, ...currentPageItems]);
                        } else {
                            // 过滤掉当前页数据
                            setSelectedImages(selectedImages.filter(
                                item => !currentPageIds.includes(item.id)
                            ));
                        }
                    }}
                />
            ),
            key: 'selection',
            width: 50,
            render: (record: any) => (
                <Checkbox
                    style={{ marginTop: 0 }}
                    checked={selectedImages.some(item => item.id === record.id)}
                    onChange={() => handleSelect(record)}
                    disabled={checkImageDisabled(record, 'gather')}
                />
            )
        },
        {
            title: '图片',
            dataIndex: 'productImageUrl',
            key: 'productImageUrl',
            render: (_: string, record: any) => (
                <>
                    <div className="w-[240px] h-[240px] flex rounded-lg items-center justify-center relative group bg-[#eef2ff] overflow-hidden">
                        {getTaskImageComponent(record)}
                    </div>
                </>
            )
        },
        {
            title: '标题',
            dataIndex: 'productTitle',
            key: 'productTitle',
            render: (productTitle: string, record: any) => (
                <a style={{ display: 'inline-block', maxWidth: '280px', color: '#32649f' }}
                    href={record?.productLink || ''} target="_blank">
                    {productTitle || ''}
                </a>
            )
        },
        {
            title: '信息',
            dataIndex: 'sourcePlatform',
            key: 'sourcePlatform',
            render: (sourcePlatform: string, record: any) => (
                <div className='flex flex-col gap-2'>
                    <p>来源平台：{sourcePlatform || ''}</p>
                    <p>星级：<Rate disabled defaultValue={Number(record?.starLevel) || 0} /></p>
                    <p>评价：{record?.commentNum || '0'}条</p>
                    <p>价格：{record?.productPrice || '0'}</p>
                    <p>创建时间：{dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    <p>操作人：{record?.ownerName || ''}</p>
                </div>
            )
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: string, record: any) => (
                <div >
                    {record.status == 1 ? '待同步图片' : record.status == 2 ? '已同步图片' : '同步图片失败'}
                </div>
            )
        },
        {
            title: '操作',
            key: 'action',
            dataIndex: 'id',
            width: 140,
            render: (id: any) => (
                <Button
                    type="link"
                    size="small"
                    style={{ color: '#cf1322' }}
                    onClick={() => handleDelBatch(id)}
                >
                    删除
                </Button>
            )
        }
    ]

    // 删除批次
    const handleDelBatch = (id: any) => {
        const isArray = Array.isArray(id);
        modal.confirm({
            centered: true,
            title: <div className={'text-[18px] text-normal'}>
                确认删除{isArray ? `选中的${id.length}条` : '该条'}数据采集？
            </div>,
            content: null,
            icon: <ExclamationCircleFilled />,
            okText: '确认',
            cancelText: '取消',
            onOk() {
                const deleteFunc = Array.isArray(id) ? deleteGatherBatch : deleteGather;
                const params = Array.isArray(id) ? id.join(',') : id; // 如果是数组转为逗号拼接字符串
                deleteFunc(params)
                    .then(() => {
                        message.success(`数据采集信息${Array.isArray(id) ? '批量' : ''}删除成功`)
                        setSelectedImages([]); // 清空已选图片
                        setPagination(prev => ({ ...prev, current: 1 }));
                        handleSearch(1, 50)
                    })
                    .catch((err) => {
                        message.error(`数据采集信息${Array.isArray(id) ? '批量' : ''}删除失败：${err?.data?.msg}`)
                    })
            },
            onCancel() {
                console.log('Cancel')
            }
        })
    }
    const [downloadLoading, setDownloadLoading] = useState(false);
    // 下载图片
    const handleDownloadImgages = () => {
        const imgUrls = selectedImages.map((img) => img?.productImageUrl);
        setDownloadLoading(true);
        batchZipUrl({ imageUrls: imgUrls, type: 16 }).then((res: any) => {
            if (res) {
                window.open(res, '_blank'); // 在新标签页打开下载链接
            } else {
                messageApi.error('获取下载链接失败');
            }
        }).catch(err => {
            messageApi.error(`图片下载失败: ${err?.data?.msg}, 请重试`);
        }).finally(() => {
            setDownloadLoading(false);
        })
    }

    // 在组件中添加分页变化处理函数
    const handleTableChange = (pagination: any) => {
        setPagination({
            ...pagination,
            current: pagination.current,
            pageSize: pagination.pageSize
        })
        handleSearch(pagination.current, pagination.pageSize)
    }

    const [showBatchActions, setShowBatchActions] = useState(false);

    const toggleBatchActions = () => {
        setShowBatchActions(!showBatchActions)
        setSelectedImages([]); // 清空已选图片
    };

    const cancelSelection = () => {
        setSelectedImages([]);
        setShowBatchActions(false); // 取消选择时隐藏操作栏
    };


    return (
        <div className="h-full w-full p-[20px]">
            {contextHolder}
            {messageContextHolder}
            <div className="w-full flex items-center  h-[60px] border-b-[1px] border-normal  gap-2 ">
                <Button
                    type="primary"
                >
                    <a href="https://design.the2016.com/storage/TigerRuh.zip">下载浏览器插件</a>
                </Button>
                <div className="w-full flex items-center   border-normal  gap-2 ">
                    <Button
                        type="primary"
                        style={{ display: 'block', marginLeft: 'auto' }}
                        onClick={toggleBatchActions}
                    >
                        {showBatchActions || selectedImages.length > 0 ? '取消批量操作' : '批量操作'}
                    </Button>
                    <Select
                        defaultValue=""
                        style={{ width: 120 }}
                        onChange={handleSourcePlatformChange}
                        value={sourcePlatform}
                        options={[
                            { value: '', label: '全部' },
                            { value: 'Temu', label: 'Temu' },
                        ]}
                    />
                    <Select
                        placeholder="颜色筛选"
                        style={{ width: 120 }}
                        onChange={(val) => setColorFilter(val)}
                        value={colorFilter}
                        allowClear
                        options={[
                            { value: '黑色', label: '黑色' },
                            { value: '白色', label: '白色' },
                        ]}
                    />
                    <Select
                        style={{ width: 200 }}
                        placeholder="是否上传过侵权风险过滤"
                        onChange={handleInfringementMarkChange}
                        value={infringementMark}
                        allowClear
                        options={[
                            { value: 0, label: '未上传' },
                            { value: 1, label: '已上传' },
                        ]}
                    />
                    <DatePicker.RangePicker
                        style={{ width: '320px' }}
                        value={dateRange}
                        onChange={(dates, dateStrings) => {
                            console.log('日期范围:', dates, dateStrings)
                            setDateRange(dates)
                            setStringDateRange(dateStrings)
                        }}
                        format="YYYY-MM-DD HH:mm:ss"
                        showTime={{ format: 'HH:mm:ss' }}
                        disabledDate={(current) => {
                            return current && current > dayjs().endOf('day')
                        }}
                    />
                    <Button
                        type="primary"
                        onClick={() => {
                            setPagination(prev => ({ ...prev, current: 1 }));
                            handleSearch(1, 50)
                        }}
                        loading={false}
                    >
                        查询
                    </Button>
                </div>
            </div>
            <div className="w-[calc(100vw-144px)]  h-[calc(100vh-192px)] scrollbar-hide">
                <Table
                    columns={columns}
                    dataSource={dataSource}
                    className="mt-4 gather-table"
                    loading={tableLoading}
                    pagination={{
                        ...pagination,
                        showTotal: (total: number) => `共 ${total} 条`,
                        showSizeChanger: true,
                        pageSizeOptions: ['50', '100', '200', '500'],
                        showQuickJumper: true
                    }}
                    onChange={handleTableChange}
                    scroll={{ y: 'calc(100vh - 302px)', x: 966 }}
                />
            </div>
            <BottomActionBar
                visible={selectedImages.length > 0 || showBatchActions}
                selectedCount={selectedImages.length}
                isAllSelected={dataSource.length > 0 && dataSource.filter((item: any) => !checkImageDisabled(item, 'gather')).every((item: any) => selectedImages.some(selected => selected.id === item.id))}
                onToggleSelectAll={() => {
                    const enabledItems = dataSource.filter((item: any) => !checkImageDisabled(item, 'gather'));
                    const currentPageIds = enabledItems.map((item: any) => item.id);
                    const isAll = enabledItems.length > 0 && enabledItems.every((item: any) => selectedImages.some(selected => selected.id === item.id));
                    if (isAll) {
                        // 取消当前页面的全选，但保留其他页面的选择
                        setSelectedImages(selectedImages.filter(item => !currentPageIds.includes(item.id)));
                    } else {
                        // 全选当前页面，保留已有选择
                        const currentPageItems = enabledItems.filter((item: any) => !selectedImages.some(selected => selected.id === item.id));
                        setSelectedImages([...selectedImages, ...currentPageItems]);
                    }
                }}
                onCancelSelection={cancelSelection}
                onDownload={handleDownloadImgages}
                downloadLoading={downloadLoading}
                downloadDisabled={selectedImages.length === 0}
                actionDisabled={selectedImages.length === 0}
                className="w-[66%] min-w-[840px]"
                enableWorkflow={userInfo?.currentMode == 2}
                selectedItems={selectedImages}
                extractImageUrl={(it: any) => it?.productImageUrl}
                onActionFinished={() => {
                    setSelectedImages([]);
                    setPagination(prev => ({ ...prev, current: 1 }));
                    handleSearch(1, 50)
                }}
                enableGatherExport
                enableGatherExportTemplate={userInfo?.currentMode == 2}
                enableGatherDelete
                extractGatherId={(it: any) => it?.id}
                extractGatherImageUrl={(it: any) => it?.productImageUrl}
                extractGatherTitle={(it: any) => it?.productTitle}
            />
        </div >
    )
};

export default BatchToolsGather;



