package com.dataxai.web.service;

/**
 * 素材使用历史服务接口
 */
public interface MaterialHistoryService {
    
    /**
     * 记录IP素材使用历史
     * @param materialIpId IP素材ID
     * @param userId 用户ID
     * @param taskId 任务ID
     */
    void recordIpUsage(Integer materialIpId, Integer userId, String taskId);

    /**
     * 记录风格素材使用历史
     * @param materialStyleId 风格素材ID
     * @param userId 用户ID
     * @param taskId 任务ID
     */
    void recordStyleUsage(Integer materialStyleId, Integer userId, String taskId);
    
    /**
     * 从任务参数中解析并记录素材使用历史
     * @param taskParam 任务参数JSON字符串
     * @param userId 用户ID
     * @param taskId 任务ID
     */
    void recordMaterialUsageFromTaskParam(String taskParam, Integer userId, String taskId);
    
    /**
     * 从任务参数中解析并记录素材使用历史（带任务类型）
     * @param taskParam 任务参数JSON字符串
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param taskType 任务类型
     */
    void recordMaterialUsageFromTaskParam(String taskParam, Integer userId, String taskId, Integer taskType);
    
    /**
     * 清理7天前的历史记录
     */
    void cleanOldRecords();
}
