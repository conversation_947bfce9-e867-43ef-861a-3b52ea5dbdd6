package com.dataxai.web.service.impl;

import com.dataxai.web.domain.MaterialIp;
import com.dataxai.web.mapper.MaterialIpMapper;
import com.dataxai.web.mapper.MaterialIpUserMapper;
import com.dataxai.web.mapper.MaterialIpHistoryMapper;
import com.dataxai.web.service.MaterialIpService;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.utils.ThumbnailUtils;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.Constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MaterialIpServiceImpl implements MaterialIpService {

    @Autowired
    private MaterialIpMapper materialIpMapper;
    
    @Autowired
    private AliYunFileService aliYunFileService;

    @Autowired
    private MaterialIpUserMapper materialIpUserMapper;

    @Autowired
    private MaterialIpHistoryMapper materialIpHistoryMapper;

    @Override
    public List<MaterialIp> queryAll(String name, Integer status, Integer categoryId) {
        return materialIpMapper.selectByCondition(name, status, categoryId);
    }

    @Override
    public MaterialIp getById(Integer id) {
        return materialIpMapper.selectById(id);
    }

    @Override
    public boolean addMaterialIp(MaterialIp materialIp) {
        materialIp.setCreateTime(new Date());
        materialIp.setUpdateTime(new Date());
        
        // 生成缩略图
        generateAndSaveThumbnail(materialIp);
        
        return materialIpMapper.insert(materialIp) > 0;
    }

    @Override
    public boolean updateMaterialIp(MaterialIp materialIp) {
        materialIp.setUpdateTime(new Date());
        
        // 生成缩略图
        generateAndSaveThumbnail(materialIp);
        
        return materialIpMapper.update(materialIp) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMaterialIp(Integer id) {
        // 1) 先删除IP收藏
        materialIpUserMapper.deleteByIpId(id);
        // 2) 再删除最近使用历史
        try { materialIpHistoryMapper.deleteOldRecords(); } catch (Exception ignore) {}
        materialIpHistoryMapper.deleteByIpId(id);
        // 3) 删除IP记录
        return materialIpMapper.deleteById(id) > 0;
    }

    @Override
    public List<MaterialIp> queryPage(Integer pageNum, Integer pageSize,
                                       String name, Integer status, Integer categoryId) {
        int offset = (pageNum - 1) * pageSize;
        return materialIpMapper.selectPageByCondition(offset, pageSize, name, status, categoryId);
    }

    @Override
    public int countByCondition(String name, Integer status, Integer categoryId) {
        return materialIpMapper.countByCondition(name, status, categoryId);
    }
    
    /**
     * 生成并保存缩略图
     * @param materialIp IP素材对象
     */
    private void generateAndSaveThumbnail(MaterialIp materialIp) {
        try {
            // 检查是否有IP图片URL
            if (StringUtils.hasText(materialIp.getIpUrl())) {
                log.info("开始为IP素材生成缩略图: ipUrl={}", materialIp.getIpUrl());
                
                // 从URL下载图片并生成缩略图
                String thumbnailUrl = generateThumbnailFromUrl(materialIp.getIpUrl());
                if (StringUtils.hasText(thumbnailUrl)) {
                    // 直接存储完整的缩略图URL，不需要去掉前缀
                    materialIp.setThumbnailImgUrl(thumbnailUrl);
                    log.info("缩略图生成成功: thumbnailUrl={}", thumbnailUrl);
                } else {
                    log.warn("缩略图生成失败，使用原图URL");
                    materialIp.setThumbnailImgUrl(materialIp.getIpUrl());
                }
            } else {
                log.info("IP素材没有图片URL，跳过缩略图生成");
            }
        } catch (Exception e) {
            log.error("生成缩略图时发生异常: {}", e.getMessage(), e);
            // 缩略图生成失败时，使用原图URL
            materialIp.setThumbnailImgUrl(materialIp.getIpUrl());
        }
    }
    
    /**
     * 从URL生成缩略图
     * @param imageUrl 原图URL
     * @return 缩略图URL
     */
    private String generateThumbnailFromUrl(String imageUrl) {
        try {
            // 检查图片URL是否为空
            if (!StringUtils.hasText(imageUrl)) {
                return null;
            }
            
            // 使用项目中已有的阿里云图片处理服务
            // 直接返回带缩略图处理参数的URL
            String thumbnailUrl = imageUrl + Constants.OOS_URL_THUMBNAIL;
            log.info("生成缩略图URL: 原图={}, 缩略图={}", imageUrl, thumbnailUrl);
            return thumbnailUrl;
            
        } catch (Exception e) {
            log.error("从URL生成缩略图失败: {}", e.getMessage(), e);
            return null;
        }
    }
}