package com.dataxai.web.batch;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.mapper.BatchMapper;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.task.core.TaskOrdinalFactoryManager;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.utils.CustomMultipartFile;
import com.dataxai.web.utils.FileUtil;
import com.dataxai.web.utils.SnowFlakeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * 抽象批次任务工厂基类
 *
 * <p>提供批次任务处理的通用方法</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractBatchTaskFactory implements BatchTaskFactory {

    @Autowired
    protected AliYunFileService aliYunFileService;

    @Autowired
    protected TaskMapper taskMapper;

    @Autowired
    protected BatchMapper batchMapper;

    @Autowired
    protected TaskOrdinalFactoryManager taskOrdinalFactoryManager;

    @Autowired
    protected UserTeamInfoService userTeamInfoService;

    /**
     * 处理路径列表，转换为图片URL
     *
     * @param pathList 路径列表
     * @param dto 批次DTO
     * @return 图片URL列表
     * @throws IOException IO异常
     */
    protected List<String> processPathList(List<Path> pathList, BatchDTO dto) throws IOException {
        if (CollectionUtil.isEmpty(pathList)) {
            return new ArrayList<>();
        }

        // 收集需要清理的文件夹
        Set<String> foldersToClean = new HashSet<>();

        List<MultipartFile> multipartFiles = new ArrayList<>();
        for (Path path : pathList) {
            try {
                MultipartFile multipartFile = convertPathToMultipartFile(path);
                multipartFiles.add(multipartFile);

                // 收集文件夹路径用于后续清理
                Path parentFolder = path.getParent();
                if (parentFolder != null && !parentFolder.toString().equals(FileUtil.TEMP_DIR_PATH)) {
                    foldersToClean.add(parentFolder.toString());
                }

                deleteTempFile(path);
            } catch (Exception e) {
                logProcessingError(path, e);
            }
        }

        List<String> urlList = aliYunFileService.batchUploadALiYun(multipartFiles);

        // 清理临时文件夹
        cleanupTempFolders(foldersToClean);

        String url = urlList.stream().collect(Collectors.joining(","));
        dto.setImgUrl(url);

        return urlList;
    }

    /**
     * 解析图片URL字符串为列表
     *
     * @param imgUrl 图片URL字符串
     * @return 图片URL列表
     */
    protected List<String> parseImgUrlList(String imgUrl) {
        List<String> imgUrlList = new ArrayList<>();
        if (com.dataxai.common.utils.StringUtils.isNotEmpty(imgUrl)) {
            String[] split = imgUrl.split(",");
            imgUrlList = java.util.Arrays.asList(split);
        }
        return imgUrlList;
    }

    /**
     * 构建任务参数
     *
     * @param dto 批次DTO
     * @param taskType 任务类型
     * @return 任务参数字符串
     * @throws IOException IO异常
     */
    protected String buildTaskParam(BatchDTO dto, Long taskType) throws IOException {
        System.out.println("开始执行");
        System.out.println("执行任务参数" + dto.getImageNumber());
        System.out.println("----------------------------------");
        // 不再直接 Math.toIntExact 以避免 NPE

        // 如果有原始任务参数，直接使用并只更新必要的字段
        if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getTaskParam())) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                ObjectNode paramNode = (ObjectNode) objectMapper.readTree(dto.getTaskParam());
                
                // 计算安全的图片数量
                int safeImageNumber;
                if (dto.getImageNumber() != null) {
                    safeImageNumber = dto.getImageNumber().intValue();
                } else {
                    safeImageNumber = 4;
                }
                
                // 只更新积分和图片数量，保留所有素材信息
                if (taskType != 8 && taskType != 9 && taskType != 6 && taskType != 7 && taskType != 52) {
                    paramNode.put("usedIntegral", 1);
                    paramNode.put("imageNumber", 1);
                } else if (taskType == 12 && paramNode.has("magnification") && paramNode.get("magnification").asInt() == 4) {
                    paramNode.put("usedIntegral", 2);
                    paramNode.put("imageNumber", 1);
                } else {
                    paramNode.put("imageNumber", safeImageNumber);
                }
                
                log.info("保留原有素材信息，更新积分和图片数量后的任务参数: {}", paramNode.toString());
                return objectMapper.writeValueAsString(paramNode);
                
            } catch (Exception e) {
                log.error("解析原有任务参数失败，使用默认参数: {}", e.getMessage(), e);
                // 如果解析失败，回退到原来的逻辑
            }
        }

        // 如果没有原始任务参数，使用原来的逻辑构建默认参数
        ObjectMapper objectMapper = new ObjectMapper();
        OrdinalParamDTO param = new OrdinalParamDTO();

        // 计算安全的图片数量
        int safeImageNumber;
        if (dto.getImageNumber() != null) {
            safeImageNumber = dto.getImageNumber().intValue();
        } else {
            safeImageNumber = 4;
        }

        // 设置积分和图片数量
        if (taskType != 8 && taskType != 9 && taskType != 6 && taskType != 7 && taskType != 52) {
            param.setUsedIntegral(1L);
            param.setImageNumber(1);
        } else if (taskType == 12 && param.getMagnification() != null && param.getMagnification() == 4) {
            param.setUsedIntegral(2L);
            param.setImageNumber(1);
        } else {
            param.setImageNumber(safeImageNumber);
        }

        return objectMapper.writeValueAsString(param);
    }

    /**
     * 创建基础任务对象
     *
     * @param dto 批次DTO
     * @param userId 用户ID
     * @param param 参数DTO
     * @param batch 批次对象
     * @return 任务对象
     */
    protected Task createBaseTask(BatchDTO dto, Long userId, OrdinalParamDTO param, Batch batch) {
        Task task = new Task();
        task.setType(dto.getType());
        task.setCreateTime(batch.getCreateTime());
        task.setUpdateTime(batch.getCreateTime());
        task.setBatchId(batch.getBatchId());
        task.setUserId(userId);

        // 获取当前用户的团队信息并设置team_id
        UserTeamInfoDTO userTeamInfo = userTeamInfoService.getUserTeamInfo(userId);
        if (userTeamInfo != null && userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
            // 团队模式：设置团队ID
            task.setTeamId(userTeamInfo.getTeamId());
        } else {
            // 个人模式：设置为0表示个人任务
            task.setTeamId(0L);
        }

        task.setDelFlag(0);
        task.setTaskId(SnowFlakeUtils.nextIdStr());
        task.setStatus(Constants.TASK_STATUS_PARKING);
        task.setDescStatus(0);

        if (com.dataxai.common.utils.StringUtils.isNotEmpty(param.getOriginImgUrl())) {
            task.setOriginalUrl(CommonUtils.subCosPrefix(param.getOriginImgUrl()));
            task.setThumbnailUrl(task.getOriginalUrl() + Constants.OOS_URL_THUMBNAIL);
        }

        return task;
    }

    /**
     * 创建基础子任务对象
     *
     * @param userId 用户ID
     * @param taskParam 任务参数
     * @param type 任务类型
     * @param taskId 任务ID
     * @param url 图片URL
     * @param batch 批次对象
     * @return 子任务对象
     */
    protected TaskOrdinal createBaseTaskOrdinal(Long userId, String taskParam, Long type,
                                               String taskId, String url, Batch batch) {
        TaskOrdinal taskOrdinal = new TaskOrdinal();
        taskOrdinal.setUserId(userId);
        taskOrdinal.setTaskParam(taskParam);
        taskOrdinal.setType(type);
        taskOrdinal.setTaskId(taskId);
        taskOrdinal.setOriginImgUrl(CommonUtils.subCosPrefix(url));
        taskOrdinal.setIsDeductPoints(false);
        taskOrdinal.setCreateTime(batch.getCreateTime());
        taskOrdinal.setUpdateTime(batch.getUpdateTime());
        return taskOrdinal;
    }

    /**
     * 批量插入任务
     *
     * @param taskList 任务列表
     */
    protected void insertTasks(List<Task> taskList) {
        if (!taskList.isEmpty()) {
            taskMapper.insertBatchTask(taskList);
        }
    }

    /**
     * 处理子任务执行
     *
     * @param taskOrdinalList 子任务列表
     */
    protected void processTaskOrdinals(List<TaskOrdinal> taskOrdinalList) {
        processTaskOrdinals(taskOrdinalList, true);
    }

    /**
     * 清理临时文件夹
     *
     * @param foldersToClean 需要清理的文件夹集合
     */
    private void cleanupTempFolders(Set<String> foldersToClean) {
        if (CollectionUtil.isEmpty(foldersToClean)) {
            return;
        }

        log.info("开始清理 {} 个临时文件夹", foldersToClean.size());
        int cleanedCount = 0;

        for (String folderPath : foldersToClean) {
            try {
                if (FileUtil.deleteTempFolder(folderPath)) {
                    cleanedCount++;
                }
            } catch (Exception e) {
                log.error("清理临时文件夹失败: {}, 原因: {}", folderPath, e.getMessage());
            }
        }

        log.info("临时文件夹清理完成，已清理 {} 个文件夹", cleanedCount);
    }

    /**
     * 批次任务完成后的清理工作
     *
     * @param batchId 批次ID
     */
    protected void cleanupBatchTempResources(String batchId) {
        try {
            log.info("开始清理批次 {} 的临时资源", batchId);

            // 清理与批次相关的临时文件夹
            int cleanedCount = FileUtil.cleanupBatchTempFolders(batchId);

            // 清理空的临时文件夹
            int emptyFolderCount = FileUtil.cleanupEmptyTempFolders();

            log.info("批次 {} 临时资源清理完成，清理批次相关文件夹 {} 个，清理空文件夹 {} 个",
                     batchId, cleanedCount, emptyFolderCount);

            // 输出临时目录统计信息
            String statistics = FileUtil.getTempDirStatistics();
            log.debug("临时目录统计信息: {}", statistics);

        } catch (Exception e) {
            log.error("清理批次 {} 临时资源时发生异常: {}", batchId, e.getMessage(), e);
        }
    }

    /**
     * 处理子任务执行
     *
     * @param taskOrdinalList 子任务列表
     * @param fromBatch 是否来自批量任务
     */
    protected void processTaskOrdinals(List<TaskOrdinal> taskOrdinalList, boolean fromBatch) {
        if (!taskOrdinalList.isEmpty()) {
            for (TaskOrdinal taskOrdinal : taskOrdinalList) {
                try {
                    // 如果是批量任务，在extra字段中设置标识
                    if (fromBatch) {
                        taskOrdinal.setExtra("batch_task=true");
                        log.debug("设置任务为批量任务标识 - 任务ID: {}", taskOrdinal.getTaskId());
                    }

                    taskOrdinalFactoryManager.processExecution(taskOrdinal);
                } catch (Exception e) {
                    log.error("任务执行失败，任务ID：{}，错误：{}", taskOrdinal.getTaskId(), e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 转换Path为MultipartFile
     */
    private MultipartFile convertPathToMultipartFile(Path path) throws IOException {
        byte[] content = Files.readAllBytes(path);
        return new CustomMultipartFile(
            "file",
            path.getFileName().toString(),
            Files.probeContentType(path),
            content
        );
    }

    /**
     * 删除临时文件
     */
    private void deleteTempFile(Path path) {
        try {
            Files.deleteIfExists(path);
            log.debug("已删除临时文件: {}", path.getFileName());
        } catch (IOException e) {
            log.error("删除临时文件失败: {}, 原因: {}", path, e.getMessage());
        }
    }

    /**
     * 记录处理错误
     */
    private void logProcessingError(Path path, Exception e) {
        log.error("处理文件失败: {}, 错误: {}", path.getFileName(), e.getMessage(), e);
    }
}