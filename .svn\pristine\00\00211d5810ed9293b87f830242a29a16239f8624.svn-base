# 积分接口使用文档

## 概述
本文档描述了两个新增的积分记录查询接口的使用方法，包括个人积分记录查询和团队积分记录查询。

## 1. 个人积分记录列表接口

### 接口信息
- **接口地址**: `GET /front/user/scoreHistory`
- **接口描述**: 获取当前用户的个人积分记录列表
- **权限要求**: 需要用户登录

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认10 | 10 |
| start_time | String | 否 | 开始时间（格式：yyyy-MM-dd HH:mm:ss） | 2025-01-01 00:00:00 |
| end_time | String | 否 | 结束时间（格式：yyyy-MM-dd HH:mm:ss） | 2025-01-31 23:59:59 |

### 响应数据
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "total": 100,
        "data": [
            {
                "id": 1,
                "userId": 1001,
                "userNickName": "用户昵称",
                "userPhone": "13800138000",
                "score": -4,
                "type": 4,
                "remainScore": 96,
                "taskType": 8,
                "updateTime": "2025-01-25 10:30:00"
            }
        ]
    }
}
```

### 字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 记录ID |
| userId | Long | 用户ID |
| userNickName | String | 用户昵称 |
| userPhone | String | 用户手机号 |
| score | Long | 积分变更值（负数为扣减，正数为增加） |
| type | Long | 积分类型（参考Constants常量） |
| remainScore | Long | 剩余积分 |
| taskType | Integer | 任务类型（参考Constants常量） |
| updateTime | String | 更新时间 |

### 积分类型说明（type字段）
- 1: 购买套餐
- 2: 购买加油包
- 3: 积分过期
- 4: 任务消耗
- 5: 再次生成
- 6: 下载4k图
- 7: 后台增加

### 任务类型说明（taskType字段）
- 0: 真人图
- 1: 人台图
- 5: 白板图
- 6: 平铺图-文生图
- 7: 平铺图-图生图
- 8: 文生图
- 9: 相似图裂变
- 10: 图片裁剪
- 11: 图片去背景
- 12: 图片变清晰
- 13: 创意图提取
- 14: 图片自动裁剪
- 15: 批量上传图片
- 17: 侵权风险过滤
- 18: 标题提取

### 请求示例
```javascript
// 查询所有记录
GET /front/user/scoreHistory?pageNum=1&pageSize=10

// 按时间范围查询
GET /front/user/scoreHistory?start_time=2025-01-01 00:00:00&end_time=2025-01-31 23:59:59
```

## 2. 团队积分记录列表接口

### 接口信息
- **接口地址**: `GET /front/team/scoreHistory`
- **接口描述**: 获取当前用户所在团队的积分记录列表
- **权限要求**: 需要用户登录并加入团队

### 权限说明
- **团队管理员**: 可查看团队所有成员的积分记录
- **普通成员**: 只能查看自己的积分记录

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认10 | 10 |
| start_time | String | 否 | 开始时间（格式：yyyy-MM-dd HH:mm:ss） | 2025-01-01 00:00:00 |
| end_time | String | 否 | 结束时间（格式：yyyy-MM-dd HH:mm:ss） | 2025-01-31 23:59:59 |

### 响应数据
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "total": 50,
        "data": [
            {
                "id": 1,
                "teamId": 100,
                "userId": 1001,
                "scoreChange": -4,
                "changeType": "任务消耗",
                "type": 4,
                "taskType": 8,
                "remark": null,
                "createTime": "2025-01-25 10:30:00",
                "teamName": "设计团队",
                "userName": "张三",
                "phonenumber": "13800138000",
                "nickname": "小张"
            }
        ]
    }
}
```

### 字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 记录ID |
| teamId | Long | 团队ID |
| userId | Long | 使用人ID |
| scoreChange | Long | 积分变更值（负数为扣减，正数为增加） |
| changeType | String | 变更类型描述 |
| type | Integer | 积分类型（参考Constants常量） |
| taskType | Integer | 任务类型（参考Constants常量） |
| remark | String | 备注 |
| createTime | String | 创建时间 |
| teamName | String | 团队名称 |
| userName | String | 用户姓名 |
| phonenumber | String | 用户手机号 |
| nickname | String | 用户在团队中的昵称 |

### 请求示例
```javascript
// 查询所有记录
GET /front/team/scoreHistory?pageNum=1&pageSize=10

// 按时间范围查询
GET /front/team/scoreHistory?start_time=2025-01-01 00:00:00&end_time=2025-01-31 23:59:59
```

### 错误响应
如果用户未加入任何团队：
```json
{
    "code": 500,
    "msg": "您未加入任何团队"
}
```

## 3. 注意事项

1. **时间格式**: 所有时间参数必须使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **分页参数**: 可以通过URL参数传递分页信息，也可以省略使用默认值
3. **权限控制**: 团队积分记录接口会根据用户在团队中的角色自动过滤数据
4. **数据一致性**: 积分类型和任务类型的数值对应关系请参考Constants常量类
5. **时间查询**: 个人积分记录基于`update_time`字段，团队积分记录基于`create_time`字段

## 4. 开发者信息

- **开发时间**: 2025年1月
- **相关文件**:
  - `FrontUserController.java` - 个人积分记录接口
  - `TeamController.java` - 团队积分记录接口
  - `AdminScoreHistory.java` - 个人积分记录实体
  - `TeamScoreHistory.java` - 团队积分记录实体 