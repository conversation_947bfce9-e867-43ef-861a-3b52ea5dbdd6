export interface ITags {
	id: number
	tagName: string
}

export interface IArticle {
	id: number
	img: string
	name: string
	link: string
	summary: string
	tagIds: string
	tagList: ITags[]
	selected: boolean
	createTime: string
}

export interface IArticleParams {
	selectTagIds?: string
	current: number
	pageSize: number
}

export interface IArticleDetail extends IArticle {
	content: string
	delFlag: number
	topTime: string
}
