package com.dataxai.web.batch.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.batch.AbstractBatchTaskFactory;
import com.dataxai.web.domain.Batch;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.utils.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用任务工厂实现
 *
 * <p>处理其他类型的批次任务</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class CommonTaskFactory extends AbstractBatchTaskFactory {

    @Override
    public Long[] getSupportedTaskTypes() {
        return new Long[]{
            (long) Constants.TASK_TYPE_REAL_PERSON,     // 0 - 真人图
            (long) Constants.TASK_TYPE_MANNEQUIN,       // 1 - 人台图
            (long) Constants.TASK_TYPE_WHITEBOARD,      // 5 - 白板图
            (long) Constants.TASK_TYPE_SIMILAR_SPLIT,   // 9 - 相似图裂变
            (long) Constants.TASK_TYPE_CROP_TEXTURE,    // 10 - 图片裁剪
            (long) Constants.TASK_TYPE_REMOVE_BG,       // 11 - 图片去背景
            (long) Constants.TASK_TYPE_ENHANCE,         // 12 - 图片变清晰
            (long) Constants.TASK_TYPE_CREATIVE_EXTRACT, // 13 - 创意图提取
            (long) Constants.TASK_TYPE_AUTO_CROP_TEXTURE, // 14 - 图片自动裁剪纹理
            (long) Constants.TASK_TYPE_RISK_FILTER      // 17 - 侵权风险过滤
        };
    }

    @Override
    public void processBatchTask(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                Long userId, List<MultipartFile> files, List<Path> pathList) throws IOException {

        // 处理路径列表转换为图片URL
        if (!CollectionUtil.isEmpty(pathList)) {
            processPathList(pathList, dto);
        }
        System.out.println("任务开始");
        System.out.println(dto.getImageNumber());
        System.out.println("----------------------------------------------");
        String taskParam1 = dto.getTaskParam();
        JSONObject jsonObj = new JSONObject(taskParam1);
        int imageNumber = jsonObj.getInt("imageNumber");
        System.out.println(imageNumber);
        dto.setImageNumber((long) imageNumber);

//        dto.setImageNumber(imageNumber);

        // 构建任务参数
        String taskParam = buildTaskParam(dto, dto.getType());

        List<Task> taskList = new ArrayList<>();
        List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getTaskParam())) {
                param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
            }

            // 通用任务主要处理有图片URL的情况
            if (com.dataxai.common.utils.StringUtils.isNotEmpty(dto.getImgUrl())) {
                List<String> imgUrlList = parseImgUrlList(dto.getImgUrl());
                for (String url : imgUrlList) {
                    Task task = createBaseTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
                    taskList.add(task);

                    TaskOrdinal taskOrdinal = createBaseTaskOrdinal(userId, taskParam, dto.getType(),
                                                                  task.getTaskId(), url, batch);
                    taskOrdinalList.add(taskOrdinal);
                }
            }

        } catch (Exception e) {
            log.error("创建通用任务失败", e);
            throw new RuntimeException("创建通用任务失败", e);
        }

        // 批量插入任务
        insertTasks(taskList);

        // 处理子任务执行
        processTaskOrdinals(taskOrdinalList);

    }

    @Override
    public List<Task> createTasks(BatchDTO dto, Batch batch, Long userId, String taskParam) {
        // 通用任务在 processBatchTask 中直接处理，不需要单独的创建逻辑
        return new ArrayList<>();
    }

    @Override
    public List<TaskOrdinal> createTaskOrdinals(BatchDTO dto, Batch batch, List<Task> taskList,
                                               Long userId, String taskParam) {
        // 通用任务在 processBatchTask 中直接处理，不需要单独的创建逻辑
        return new ArrayList<>();
    }
}