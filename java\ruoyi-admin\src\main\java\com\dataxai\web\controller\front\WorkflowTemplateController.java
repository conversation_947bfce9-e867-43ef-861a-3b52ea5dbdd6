package com.dataxai.web.controller.front;

import java.util.List;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.*;
import com.dataxai.service.ITeamUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.domain.dto.WorkflowTemplateDetailDTO;
import com.dataxai.mapper.WorkflowTemplateNodeMapper;
import com.dataxai.service.IWorkflowTemplateService;
import com.dataxai.service.IWorkflowTemplateGroupService;
import com.dataxai.domain.dto.CopyTemplateRequest;
import com.dataxai.domain.dto.MoveTemplateRequest;
import com.dataxai.domain.dto.UpdateNodesRequest;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.common.exception.ServiceException;
import com.github.pagehelper.PageInfo;
import java.util.HashMap;
import java.util.Map;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.web.service.MaterialResolverService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.web.bind.annotation.RequestParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作流模板Controller
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Api(tags = "工作流模板管理")
@RestController
@RequestMapping("/workflow/template")
public class WorkflowTemplateController extends BaseController
{
    @Autowired
    private IWorkflowTemplateService workflowTemplateService;

    @Autowired
    private IWorkflowTemplateGroupService workflowTemplateGroupService;

    @Autowired
    private TUserMapper tUserMapper;

    @Autowired
    private MaterialResolverService materialResolverService;

    @Autowired
    private WorkflowTemplateNodeMapper workflowTemplateNodeMapper;
    @Autowired
    private ITeamUserService iTeamUserService;

    /**
     * 获取当前用户信息（包含团队ID和模式）
     */
    private TUser getCurrentUserInfo() {
        Long userId = WorkflowSecurityUtils.getCurrentUserId();
        return tUserMapper.selectTUserById(userId);
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId() {
        TUser currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());

        Long teamId = currentUser.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 查询工作流模板列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询工作流模板列表")
    public AjaxResult list(WorkflowTemplate workflowTemplate)
    {
        // 验证团队模式并使用当前登录用户所属的团队ID
        Long currentUserTeamId = validateAndGetTeamId();
        workflowTemplate.setTeamId(currentUserTeamId);
        Long userId = SecurityUtils.getUserId();
        TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
//        if(teamUser.getIsAdmin() == true){
            Long selectUserId = workflowTemplate.getUserId();
            if (selectUserId != null){
                workflowTemplate.setUserId(selectUserId);
            }
//        }else{
//            workflowTemplate.setUserId(userId);
//        }
        startPage();
        List<WorkflowTemplate> list = workflowTemplateService.selectWorkflowTemplateList(workflowTemplate);

        // 为每个工作流模板查询节点信息
        List<WorkflowTemplateDetailDTO> resultList = new ArrayList<>();
        for (WorkflowTemplate template : list) {
            WorkflowTemplateDetailDTO detailDTO = new WorkflowTemplateDetailDTO();
            // 复制模板基本信息
            detailDTO.setId(template.getId());
            detailDTO.setTemplateName(template.getTemplateName());
            detailDTO.setGroupId(template.getGroupId());
            detailDTO.setTeamId(template.getTeamId());
            detailDTO.setUserId(template.getUserId());
            detailDTO.setCreateTime(template.getCreateTime());
            detailDTO.setUpdateTime(template.getUpdateTime());

            // 查询关联的分组信息
            if (template.getGroupId() != null) {
                WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(template.getGroupId());
                if (group != null) {
                    detailDTO.setGroupName(group.getGroupName());
                }
            }

            // 查询模板节点列表
            List<WorkflowTemplateNode> nodeList = workflowTemplateNodeMapper.selectByTemplateId(template.getId());

            detailDTO.setNodeList(nodeList);

            resultList.add(detailDTO);
        }

        // 组装自定义分页结构
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("data", resultList);

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 导出工作流模板列表
     */
    @Log(title = "工作流模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出工作流模板列表")
    public void export(HttpServletResponse response, WorkflowTemplate workflowTemplate)
    {
        // 验证团队模式并使用当前登录用户所属的团队ID
        Long currentUserTeamId = validateAndGetTeamId();
        workflowTemplate.setTeamId(currentUserTeamId);
        List<WorkflowTemplate> list = workflowTemplateService.selectWorkflowTemplateList(workflowTemplate);
        ExcelUtil<WorkflowTemplate> util = new ExcelUtil<WorkflowTemplate>(WorkflowTemplate.class);
        util.exportExcel(response, list, "工作流模板数据");
    }

    /**
     * 获取工作流模板详细信息
     */
    @GetMapping(value = "/detail/{id}")
    @ApiOperation(value = "获取工作流模板详细信息")
    public AjaxResult getInfo(@ApiParam("模板ID") @PathVariable("id") Long id)
    {
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
        if (template == null) {
            return error("工作流模板不存在");
        }

        // 验证团队模式和权限
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                template.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        return success(template);
    }

    /**
     * 获取工作流模板详细信息（包含节点）
     */
    @GetMapping(value = "/details/{id}")
    @ApiOperation(value = "获取工作流模板详细信息（包含节点）")
    public AjaxResult getDetails(@ApiParam("模板ID") @PathVariable("id") Long id)
    {
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
        if (template == null) {
            return error("工作流模板不存在");
        }

        // 验证团队模式和权限
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                template.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        WorkflowTemplate templateWithNodes = workflowTemplateService.selectWorkflowTemplateWithNodes(id);

        // 解析节点中的素材信息，为前端编辑提供素材详情
        if (templateWithNodes != null && templateWithNodes.getNodeList() != null) {
            enhanceNodesWithMaterialDetails(templateWithNodes.getNodeList());
        }

        return success(templateWithNodes);
    }

    /**
     * 新增工作流模板
     */
    @Log(title = "工作流模板", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增工作流模板")
    public AjaxResult add(@RequestBody WorkflowTemplate workflowTemplate)
    {
        // 验证团队模式，使用当前登录用户的ID和对应的团队ID
        TUser currentUser = getCurrentUserInfo();
        Long currentUserId = currentUser.getUserId();
        Long currentUserTeamId;

        try {
            WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());
            currentUserTeamId = currentUser.getTeamId();
            if (currentUserTeamId == null) {
                return error("用户未加入任何团队");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }

        workflowTemplate.setUserId(currentUserId);
        workflowTemplate.setTeamId(currentUserTeamId);

        // 检查分组是否存在且属于当前用户团队
        if (workflowTemplate.getGroupId() != null) {
            WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
            if (group == null) {
                return error("指定的工作流模板分组不存在");
            }
            try {
                WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, group.getTeamId(), "模板分组");
            } catch (Exception e) {
                return error(e.getMessage());
            }
        }

        // 检查模板名称在团队中的唯一性
        if (!workflowTemplateService.checkTemplateNameUnique(
                currentUserTeamId,
                workflowTemplate.getTemplateName(),
                null)) {
            return error("模板名称在当前团队中已存在");
        }

        return toAjax(workflowTemplateService.insertWorkflowTemplate(workflowTemplate));
    }

    /**
     * 新增工作流模板（包含节点）
     */
    @Log(title = "新增工作流模板", businessType = BusinessType.INSERT)
    @PostMapping("/withNodes")
    @ApiOperation(value = "新增工作流模板（包含节点）")
    public AjaxResult addWithNodes(@RequestBody WorkflowTemplate workflowTemplate)
    {
        // 前端已将素材ID直接放在节点对象中，无需额外提取

        // 验证团队模式，使用当前登录用户的ID和对应的团队ID
        TUser currentUser = getCurrentUserInfo();
        Long currentUserId = currentUser.getUserId();
        Long currentUserTeamId;

        try {
            WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());
            currentUserTeamId = currentUser.getTeamId();
            if (currentUserTeamId == null) {
                return error("用户未加入任何团队");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }

        workflowTemplate.setUserId(currentUserId);
        workflowTemplate.setTeamId(currentUserTeamId);

        // 检查分组是否存在且属于当前用户团队
        if (workflowTemplate.getGroupId() != null) {
            WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
            if (group == null) {
                return error("指定的工作流模板分组不存在");
            }
            try {
                WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, group.getTeamId(), "模板分组");
            } catch (Exception e) {
                return error(e.getMessage());
            }
        }

        // 检查模板名称在团队中的唯一性
        if (!workflowTemplateService.checkTemplateNameUnique(
                currentUserTeamId,
                workflowTemplate.getTemplateName(),
                null)) {
            return error("模板名称在当前团队中已存在");
        }

        // 验证节点配置
        if (workflowTemplate.getNodeList() != null && !workflowTemplate.getNodeList().isEmpty()) {
            String validationResult = workflowTemplateService.validateNodeConfiguration(workflowTemplate.getNodeList());
            if (StringUtils.isNotEmpty(validationResult)) {
                return error(validationResult);
            }
        }

        // 处理文生图节点的素材参数增强（从节点中提取素材ID）
        if (workflowTemplate.getNodeList() != null && !workflowTemplate.getNodeList().isEmpty()) {
            enhanceWorkflowNodesWithMaterialsFromNodes(workflowTemplate.getNodeList());
        }

        int result = workflowTemplateService.insertWorkflowTemplateWithNodes(workflowTemplate, workflowTemplate.getNodeList());
        return toAjax(result);
    }

    /**
     * 修改工作流模板
     */
    @Log(title = "工作流模板", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改工作流模板")
    public AjaxResult edit(@RequestBody WorkflowTemplate workflowTemplate)
    {
        // 检查模板是否存在
        WorkflowTemplate existingTemplate = workflowTemplateService.selectWorkflowTemplateById(workflowTemplate.getId());
        if (existingTemplate == null) {
            return error("指定的工作流模板不存在");
        }

        // 验证团队模式和当前用户所属的团队和模板所属的团队是否匹配
        TUser currentUser = getCurrentUserInfo();
        Long currentUserId = currentUser.getUserId();

        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                existingTemplate.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        Long currentUserTeamId = currentUser.getTeamId();

        // 使用当前登录用户的ID
        workflowTemplate.setUserId(currentUserId);
        workflowTemplate.setTeamId(currentUserTeamId);

        // 检查分组是否存在且属于当前用户团队
        if (workflowTemplate.getGroupId() != null) {
            WorkflowTemplateGroup group = workflowTemplateGroupService.selectWorkflowTemplateGroupById(workflowTemplate.getGroupId());
            if (group == null) {
                return error("指定的工作流模板分组不存在");
            }
            try {
                WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, group.getTeamId(), "模板分组");
            } catch (Exception e) {
                return error(e.getMessage());
            }
        }

        // 检查模板名称在团队中的唯一性
        if (!workflowTemplateService.checkTemplateNameUnique(
                currentUserTeamId,
                workflowTemplate.getTemplateName(),
                workflowTemplate.getId())) {
            return error("模板名称在当前团队中已存在");
        }

        return toAjax(workflowTemplateService.updateWorkflowTemplate(workflowTemplate));
    }

    /**
     * 更新模板节点
     */
    @Log(title = "更新模板节点", businessType = BusinessType.UPDATE)
    @PutMapping("/nodes/{templateId}")
    @ApiOperation(value = "更新模板节点")
    public AjaxResult updateNodes(
            @ApiParam("模板ID") @PathVariable("templateId") Long templateId,
            @RequestBody UpdateNodesRequest request)
    {
        // 检查模板是否存在
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(templateId);
        if (template == null) {
            return error("指定的工作流模板不存在");
        }

        // 验证团队模式和当前用户所属的团队和模板所属的团队是否匹配
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                template.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        // 获取节点列表（素材ID已包含在节点对象中）
        List<WorkflowTemplateNode> nodeList = request.getNodes();

        // 验证节点配置
        if (nodeList != null && !nodeList.isEmpty()) {
            String validationResult = workflowTemplateService.validateNodeConfiguration(nodeList);
            if (StringUtils.isNotEmpty(validationResult)) {
                return error(validationResult);
            }
        }

        // 处理文生图节点的素材参数增强（从节点中提取素材ID）
        if (nodeList != null && !nodeList.isEmpty()) {
            enhanceWorkflowNodesWithMaterialsFromNodes(nodeList);
        }

        int result = workflowTemplateService.updateWorkflowTemplateNodes(templateId, nodeList);
        return toAjax(result);
    }

    /**
     * 删除工作流模板
     */
    @Log(title = "工作流模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    @ApiOperation(value = "删除工作流模板")
    public AjaxResult remove(@ApiParam("模板ID数组") @PathVariable Long[] ids)
    {
        // 验证团队模式并获取当前用户信息
        TUser currentUser = getCurrentUserInfo();
        Long currentUserTeamId;

        try {
            WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());
            currentUserTeamId = currentUser.getTeamId();
            if (currentUserTeamId == null) {
                return error("用户未加入任何团队");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }

        // 验证当前用户所属的团队和模板所属的团队是否匹配
        for (Long id : ids) {
            WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(id);
            if (template == null) {
                return error("模板不存在，ID: " + id);
            }

            try {
                WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, template.getTeamId(), "工作流模板");
            } catch (Exception e) {
                return error(e.getMessage());
            }
        }

        return toAjax(workflowTemplateService.deleteWorkflowTemplateByIds(ids));
    }

    /**
     * 复制工作流模板
     */
    @Log(title = "复制工作流模板", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{templateId}")
    @ApiOperation(value = "复制工作流模板")
    public AjaxResult copy(
            @ApiParam("源模板ID") @PathVariable("templateId") Long templateId,
            @RequestBody CopyTemplateRequest request)
    {
        // 检查源模板是否存在
        WorkflowTemplate sourceTemplate = workflowTemplateService.selectWorkflowTemplateById(templateId);
        if (sourceTemplate == null) {
            return error("指定的源工作流模板不存在");
        }

        // 验证团队模式和当前用户所属的团队和模板所属的团队是否匹配
        TUser currentUser = getCurrentUserInfo();
        Long currentUserId = currentUser.getUserId();

        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                sourceTemplate.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        Long currentUserTeamId = currentUser.getTeamId();

        // 检查新分组是否存在且属于当前用户团队
        if (request.getNewGroupId() != null) {
            WorkflowTemplateGroup targetGroup = workflowTemplateGroupService.selectWorkflowTemplateGroupById(request.getNewGroupId());
            if (targetGroup == null) {
                return error("指定的目标分组不存在");
            }
            try {
                WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, targetGroup.getTeamId(), "目标分组");
            } catch (Exception e) {
                return error(e.getMessage());
            }
        }

        // 自动生成新模板名称：原模板名称 + "_copy"
        String newTemplateName = sourceTemplate.getTemplateName() + "_copy";

        int result = workflowTemplateService.copyWorkflowTemplate(templateId, newTemplateName, request.getNewGroupId());
        return toAjax(result);
    }

    /**
     * 移动模板到指定分组
     */
    @Log(title = "移动模板分组", businessType = BusinessType.UPDATE)
    @PutMapping("/move/{templateId}")
    @ApiOperation(value = "移动模板到指定分组")
    public AjaxResult moveToGroup(
            @ApiParam("模板ID") @PathVariable("templateId") Long templateId,
            @RequestBody MoveTemplateRequest request)
    {
        // 检查模板是否存在
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(templateId);
        if (template == null) {
            return error("指定的工作流模板不存在");
        }

        // 验证团队模式和当前用户所属的团队和模板所属的团队是否匹配
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                template.getTeamId(),
                "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        Long currentUserTeamId = currentUser.getTeamId();

        // 验证必需参数
        if (request.getNewGroupId() == null) {
            return error("新分组ID不能为空");
        }

        // 检查目标分组是否存在且属于当前用户团队
        WorkflowTemplateGroup targetGroup = workflowTemplateGroupService.selectWorkflowTemplateGroupById(request.getNewGroupId());
        if (targetGroup == null) {
            return error("指定的目标分组不存在");
        }

        try {
            WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, targetGroup.getTeamId(), "目标分组");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        int result = workflowTemplateService.moveTemplateToGroup(templateId, request.getNewGroupId());
        return toAjax(result);
    }

    /**
     * 检查模板名称唯一性
     */
    @PostMapping("/checkTemplateNameUnique")
    @ApiOperation(value = "检查模板名称唯一性")
    public AjaxResult checkTemplateNameUnique(@RequestBody WorkflowTemplate workflowTemplate)
    {
        // 验证团队模式并使用当前登录用户所属的团队ID
        Long currentUserTeamId = validateAndGetTeamId();

        boolean unique = workflowTemplateService.checkTemplateNameUnique(
                currentUserTeamId,
                workflowTemplate.getTemplateName(),
                workflowTemplate.getId());
        return success(unique);
    }

    /**
     * 验证节点配置
     */
    @PostMapping("/validateNodes")
    @ApiOperation(value = "验证节点配置")
    public AjaxResult validateNodes(@RequestBody List<WorkflowTemplateNode> nodeList)
    {
        // 验证团队模式（节点配置验证不涉及具体资源，只验证模式即可）
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());
        } catch (Exception e) {
            return error(e.getMessage());
        }

        String validationResult = workflowTemplateService.validateNodeConfiguration(nodeList);
        if (StringUtils.isNotEmpty(validationResult)) {
            return error(validationResult);
        }
        return success("节点配置验证通过");
    }

    /**
     * 增强工作流节点参数，为文生图节点添加完整的素材信息
     * 从节点对象中提取素材ID
     */
    private void enhanceWorkflowNodesWithMaterialsFromNodes(List<WorkflowTemplateNode> nodeList) {
        if (nodeList == null || nodeList.isEmpty()) {
            return;
        }

        for (WorkflowTemplateNode node : nodeList) {
            try {
                // 只处理文生图类型的节点
                if (!isTextToImageType(node.getTaskType() != null ? node.getTaskType().longValue() : null)) {
                    continue;
                }

                // 从节点对象中获取素材ID和相关参数
                Integer materialStyleId = node.getMaterialStyleId();
                Integer materialIpId = node.getMaterialIpId();
                Integer onlyFissionPattern = node.getOnlyFissionPattern();

                // 如果节点中没有素材ID，跳过处理
                if (materialStyleId == null && materialIpId == null) {
                    continue;
                }

                String nodeParams = node.getNodeParams();
                if (StringUtils.isEmpty(nodeParams)) {
                    // 如果节点参数为空，创建新的参数对象
                    nodeParams = "{}";
                }

                // 构建包含素材ID的参数
                String enhancedParams = buildNodeParamsWithMaterials(nodeParams, materialStyleId, materialIpId, onlyFissionPattern);
                if (StringUtils.isNotEmpty(enhancedParams)) {
                    node.setNodeParams(enhancedParams);
                    log.info("工作流节点素材参数已增强: taskType={}, materialStyleId={}, materialIpId={}",
                            node.getTaskType(), materialStyleId, materialIpId);
                }

            } catch (Exception e) {
                log.error("增强工作流节点素材参数失败: taskType={}, error={}",
                        node.getTaskType(), e.getMessage(), e);
                // 处理失败不影响主流程，继续执行
            }
        }
    }

    /**
     * 构建包含素材信息的节点参数
     */
    private String buildNodeParamsWithMaterials(String originalNodeParams,
                                               Integer materialStyleId, Integer materialIpId, Integer onlyFissionPattern) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode paramNode;

            // 解析原始nodeParams
            if (StringUtils.isNotEmpty(originalNodeParams)) {
                paramNode = (ObjectNode) objectMapper.readTree(originalNodeParams);
            } else {
                paramNode = objectMapper.createObjectNode();
            }

            // 添加素材ID到参数中
            if (materialStyleId != null) {
                paramNode.put("materialStyleId", materialStyleId);
                log.info("工作流节点添加风格素材ID: {}", materialStyleId);
            }

            if (materialIpId != null) {
                paramNode.put("materialIpId", materialIpId);
                log.info("工作流节点添加IP素材ID: {}", materialIpId);
            }

            // 从节点对象的扩展信息中读取 cropCategoryId 并放入参数中（如存在）
            try {
                // 当前方法上下文中无法直接获取节点对象，这里兼容从已有参数中读取并保留
                if (!paramNode.has("cropCategoryId") || paramNode.get("cropCategoryId").isNull()) {
                    // 保持为空不处理，等待外层在组装节点参数时传入
                } else {
                    log.info("工作流节点保留已有的印花图提取品类ID: {}", paramNode.get("cropCategoryId").asInt());
                }
            } catch (Exception ignore) {}

            // 添加仅裂变图案参数
            if (onlyFissionPattern != null) {
                paramNode.put("onlyFissionPattern", onlyFissionPattern);
                log.info("工作流节点添加仅裂变图案参数: {}", onlyFissionPattern);
            }

            // 转换为JSON字符串
            String baseNodeParams = objectMapper.writeValueAsString(paramNode);
            log.info("工作流节点包含素材ID的基础参数: {}", baseNodeParams);

            // 检查MaterialResolverService是否可用
            if (materialResolverService == null) {
                log.warn("MaterialResolverService未注入，返回基础参数");
                return baseNodeParams;
            }

            // 使用MaterialResolverService构建完整的素材信息
            String fullNodeParams = materialResolverService.buildFullTaskParam(baseNodeParams);
            log.info("工作流节点构建完成的完整参数: {}", fullNodeParams);
            return StringUtils.isNotEmpty(fullNodeParams) ? fullNodeParams : baseNodeParams;

        } catch (Exception e) {
            log.error("工作流节点构建包含素材信息的参数失败: {}", e.getMessage(), e);
            // 如果失败，返回原始参数
            return originalNodeParams;
        }
    }

    /**
     * 判断是否为文生图类型任务
     */
    private boolean isTextToImageType(Long taskType) {
        if (taskType == null) {
            return false;
        }
        return taskType.equals(8L) || taskType.equals(6L) || taskType.equals(7L);
    }

    /**
     * 增强节点信息，为前端编辑提供素材详情
     * 解析nodeParams中的素材信息，提取到节点对象的属性中
     */
    private void enhanceNodesWithMaterialDetails(List<WorkflowTemplateNode> nodeList) {
        if (nodeList == null || nodeList.isEmpty()) {
            return;
        }

        for (WorkflowTemplateNode node : nodeList) {
            try {
                // 只处理文生图类型的节点
                if (!isTextToImageType(node.getTaskType() != null ? node.getTaskType().longValue() : null)) {
                    continue;
                }

                String nodeParams = node.getNodeParams();
                if (StringUtils.isEmpty(nodeParams)) {
                    continue;
                }

                // 解析nodeParams中的素材信息
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode paramNode = objectMapper.readTree(nodeParams);

                // 提取素材ID
                JsonNode materialStyleIdNode = paramNode.get("materialStyleId");
                JsonNode materialIpIdNode = paramNode.get("materialIpId");

                if (materialStyleIdNode != null && !materialStyleIdNode.isNull()) {
                    // 这里可以将素材ID设置到节点的扩展属性中
                    // 由于WorkflowTemplateNode实体类可能没有这些字段，我们可以通过修改nodeParams来实现
                    log.info("节点包含风格素材ID: {}", materialStyleIdNode.asInt());
                }

                if (materialIpIdNode != null && !materialIpIdNode.isNull()) {
                    log.info("节点包含IP素材ID: {}", materialIpIdNode.asInt());
                }

                // 提取素材详情信息（如果存在）
                extractMaterialDetailsFromNode(node, paramNode);

            } catch (Exception e) {
                log.error("解析节点素材信息失败: nodeId={}, error={}",
                        node.getId(), e.getMessage(), e);
                // 解析失败不影响主流程，继续执行
            }
        }
    }

    /**
     * 从节点参数中提取素材详情信息
     */
    private void extractMaterialDetailsFromNode(WorkflowTemplateNode node, JsonNode paramNode) {
        try {
            // 创建一个新的JSON对象，包含原有参数和素材详情
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode enhancedParams = objectMapper.createObjectNode();

            // 复制原有参数
            paramNode.fields().forEachRemaining(entry -> {
                enhancedParams.set(entry.getKey(), entry.getValue());
            });

            // 添加素材详情到响应中（用于前端显示）
            JsonNode styleNode = paramNode.get("style");
            JsonNode styleUrlNode = paramNode.get("styleUrl");
            JsonNode fashIpNode = paramNode.get("fashIp");
            JsonNode ipUrlNode = paramNode.get("ipUrl");

            if (styleNode != null && !styleNode.isNull()) {
                enhancedParams.put("styleName", styleNode.asText());
            }

            if (styleUrlNode != null && !styleUrlNode.isNull()) {
                enhancedParams.put("styleImageUrl", styleUrlNode.asText());
            }

            if (fashIpNode != null && !fashIpNode.isNull()) {
                enhancedParams.put("ipName", fashIpNode.asText());
            }

            if (ipUrlNode != null && !ipUrlNode.isNull()) {
                enhancedParams.put("ipImageUrl", ipUrlNode.asText());
            }

            // 更新节点参数
            node.setNodeParams(objectMapper.writeValueAsString(enhancedParams));

        } catch (Exception e) {
            log.error("提取节点素材详情失败: nodeId={}, error={}",
                    node.getId(), e.getMessage(), e);
        }
    }
}