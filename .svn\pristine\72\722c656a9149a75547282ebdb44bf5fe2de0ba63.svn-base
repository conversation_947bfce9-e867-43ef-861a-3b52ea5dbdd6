package com.dataxai.web.mapper;

import com.dataxai.web.domain.MaterialStyleHistory;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface MaterialStyleHistoryMapper {
    
    /**
     * 插入使用历史记录
     */
    int insert(MaterialStyleHistory record);
    
    /**
     * 根据用户ID获取最近7天使用的风格素材（分页）
     */
    List<MaterialStyleHistory> selectRecentByUser(@Param("userId") Integer userId,
                                                @Param("offset") int offset,
                                                @Param("pageSize") int pageSize,
                                                @Param("taskType") Integer taskType);
    
    /**
     * 统计用户最近7天使用的风格素材数量
     */
    int countRecentByUser(@Param("userId") Integer userId,
                          @Param("taskType") Integer taskType);
    
    /**
     * 获取用户最近使用的风格映射 (styleId -> historyId)
     * 用于在素材列表中标记最近使用状态
     */
    List<Map<String, Object>> selectUserRecentMap(@Param("userId") Integer userId, 
                                                @Param("styleIds") List<Integer> styleIds,
                                                @Param("taskType") Integer taskType);
    
    /**
     * 检查用户是否在最近7天使用过某个风格素材
     */
    int checkRecentUsed(@Param("userId") Integer userId, 
                        @Param("styleId") Integer styleId,
                        @Param("taskType") Integer taskType);
    
    /**
     * 删除7天前的历史记录（定时清理）
     */
    int deleteOldRecords();
}
