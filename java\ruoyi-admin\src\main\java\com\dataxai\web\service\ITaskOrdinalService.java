package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.TaskOrdinal;

/**
 * 任务次数Service接口
 *
 * <AUTHOR>
 * @date 2024-01-14
 */
public interface ITaskOrdinalService
{
    /**
     * 查询任务次数
     *
     * @param taskOrdinalId 任务次数主键
     * @return 任务次数
     */
    public TaskOrdinal selectTaskOrdinalByTaskOrdinalId(String taskOrdinalId);

    /**
     * 查询任务次数列表
     *
     * @param taskOrdinal 任务次数
     * @return 任务次数集合
     */
    public List<TaskOrdinal> selectTaskOrdinalList(TaskOrdinal taskOrdinal);

    /**
     * 新增任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    public int insertTaskOrdinal(TaskOrdinal taskOrdinal);

    /**
     * 修改任务次数
     *
     * @param taskOrdinal 任务次数
     * @return 结果
     */
    public int updateTaskOrdinal(TaskOrdinal taskOrdinal);

    /**
     * 批量删除任务次数
     *
     * @param taskOrdinalIds 需要删除的任务次数主键集合
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskOrdinalIds(String[] taskOrdinalIds);

    /**
     * 删除任务次数信息
     *
     * @param taskOrdinalId 任务次数主键
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskOrdinalId(String taskOrdinalId);
}
