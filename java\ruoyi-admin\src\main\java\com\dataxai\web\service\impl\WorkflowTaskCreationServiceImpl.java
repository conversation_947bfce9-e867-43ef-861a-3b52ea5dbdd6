package com.dataxai.web.service.impl;

import com.dataxai.service.IWorkflowTaskCreationService;
import com.dataxai.web.service.BatchService;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.common.service.UserTeamInfoService;
import com.dataxai.service.IRiskDetectionTaskService;
import com.dataxai.service.IRiskDetectionTaskDetailService;
import com.dataxai.service.ITitleExtractionTaskService;
import com.dataxai.service.ITitleExtractionTaskDetailService;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.Batch;
import com.dataxai.domain.RiskDetectionTask;
import com.dataxai.domain.TitleExtractionTask;
import com.dataxai.domain.RiskDetectionTaskDetail;
import com.dataxai.domain.TitleExtractionTaskDetail;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.dto.UserTeamInfoDTO;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.dataxai.mapper.WorkflowMapper;
import com.dataxai.domain.WorkflowNodeExecution;
import com.dataxai.domain.Workflow;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.OrdinalParamDTO;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.task.core.TaskOrdinalFactoryManager;
import com.dataxai.web.utils.SnowFlakeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;

/**
 * 工作流任务创建服务实现
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Service
public class WorkflowTaskCreationServiceImpl implements IWorkflowTaskCreationService
{
    private static final Logger logger = LoggerFactory.getLogger(WorkflowTaskCreationServiceImpl.class);

    @Autowired
    private BatchService batchService;

    @Autowired
    private UserTeamInfoService userTeamInfoService;

    @Autowired
    private IRiskDetectionTaskService riskDetectionTaskService;

    @Autowired
    private ITitleExtractionTaskService titleExtractionTaskService;

    @Autowired
    private IRiskDetectionTaskDetailService riskDetectionTaskDetailService;

    @Autowired
    private ITitleExtractionTaskDetailService titleExtractionTaskDetailService;

    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;

    @Autowired
    private WorkflowMapper workflowMapper;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private TaskOrdinalFactoryManager taskOrdinalFactoryManager;

    /**
     * 安全地获取用户ID，如果无法获取则从工作流节点中获取或使用默认值
     *
     * @param workflowNodeExecutionId 工作流节点执行ID
     * @return 用户ID
     */
    private Long getSafeUserId(Long workflowNodeExecutionId) {
        try {
            // 首先尝试从当前用户上下文获取
            return SecurityUtils.getUserId();
        } catch (Exception e) {
            logger.warn("无法从当前上下文获取用户ID，尝试从工作流节点获取，节点ID: {}", workflowNodeExecutionId);

            try {
                // 从工作流节点获取用户信息
                if (workflowNodeExecutionId != null) {
                    WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(workflowNodeExecutionId);
                    if (nodeExecution != null && nodeExecution.getWorkflowId() != null) {
                        Workflow workflow = workflowMapper.selectWorkflowById(nodeExecution.getWorkflowId());
                        if (workflow != null && workflow.getUserId() != null) {
                            logger.info("从工作流获取到用户ID: {}, 工作流ID: {}", workflow.getUserId(), workflow.getId());
                            return workflow.getUserId();
                        }
                    }
                }
            } catch (Exception ex) {
                logger.error("从工作流节点获取用户ID失败，节点ID: {}", workflowNodeExecutionId, ex);
            }

            // 如果都无法获取，使用默认值 1（系统用户）
            logger.warn("无法获取用户ID，使用默认值 1，节点ID: {}", workflowNodeExecutionId);
            return 1L;
        }
    }

    /**
     * 安全地获取用户团队信息
     *
     * @param userId 用户ID
     * @return 用户团队信息
     */
    private UserTeamInfoDTO getSafeUserTeamInfo(Long userId) {
        try {
            return userTeamInfoService.getCurrentUserTeamInfo();
        } catch (Exception e) {
            logger.warn("无法获取用户团队信息，使用默认值: {}", e.getMessage());
            // 返回默认的团队信息（个人模式）
            return new UserTeamInfoDTO(userId, null, 1);
        }
    }

    /**
     * 创建素材创作类型批次
     */
    @Override
    @Transactional
    public String createMaterialCreationBatch(Integer taskType, List<String> materialUrls, Long workflowNodeExecutionId) {
        try {
            logger.info("开始创建素材创作类型批次，任务类型: {}, 素材数量: {}, 工作流节点ID: {}",
                    taskType, materialUrls.size(), workflowNodeExecutionId);

            // 安全地获取用户ID和团队信息
            Long userId = getSafeUserId(workflowNodeExecutionId);
            UserTeamInfoDTO userTeamInfo = getSafeUserTeamInfo(userId);
            Long teamId = userTeamInfo.isTeamMode() ? userTeamInfo.getTeamId() : null;

            // 构建BatchDTO
            BatchDTO batchDTO = new BatchDTO();
            batchDTO.setType(taskType.longValue());
            batchDTO.setImgUrl(String.join(",", materialUrls));
            batchDTO.setRemark("工作流自动创建批次");

            // 使用工作流专用的批次创建方法（不扣除积分）
            Batch batch = batchService.createWorkflowBatch(batchDTO, userId, teamId);

            // 更新批次的工作流节点关联
            if (batch != null && workflowNodeExecutionId != null) {
                batch.setWorkflowNodeExecutionId(workflowNodeExecutionId);
                // 调用BatchService的更新方法来设置workflowNodeExecutionId
                Integer updateResult = batchService.updateBat(batch);
                if (updateResult > 0) {
                    logger.info("成功更新批次的工作流节点关联，批次ID: {}, 节点ID: {}",
                            batch.getBatchId(), workflowNodeExecutionId);
                } else {
                    logger.warn("更新批次的工作流节点关联失败，批次ID: {}, 节点ID: {}",
                            batch.getBatchId(), workflowNodeExecutionId);
                }
            }

            if (batch != null) {
                logger.info("素材创作类型批次创建成功，批次ID: {}, 任务类型: {}, 素材数量: {}",
                        batch.getBatchId(), taskType, materialUrls.size());

                // 创建任务和任务详情
                createTasksAndTaskOrdinals(batchDTO, batch, userId, materialUrls, workflowNodeExecutionId);

                return batch.getBatchId();
            } else {
                throw new RuntimeException("批次创建失败，返回的批次对象为空");
            }

        } catch (Exception e) {
            logger.error("创建素材创作类型批次失败，任务类型: {}, 工作流节点ID: {}", taskType, workflowNodeExecutionId, e);
            throw new RuntimeException("创建素材创作批次失败: " + e.getMessage());
        }
    }

    /**
     * 创建风险检测类型任务
     */
    @Override
    @Transactional
    public String createRiskDetectionTask(List<String> materialUrls, Long workflowNodeExecutionId) {
        try {
            logger.info("开始创建风险检测任务，素材数量: {}, 工作流节点ID: {}",
                    materialUrls.size(), workflowNodeExecutionId);

            // 安全地获取用户ID
            Long userId = getSafeUserId(workflowNodeExecutionId);

            // 创建风险检测任务
            RiskDetectionTask task = new RiskDetectionTask();
            task.setOwnerId(userId);
            task.setTaskBatch(riskDetectionTaskService.generateTaskBatch());
            task.setTotalAmount(materialUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            task.setWorkflowNodeExecutionId(workflowNodeExecutionId); // 设置工作流节点执行记录ID
            task.setCreateTime(DateUtils.getNowDate());
            task.setUpdateTime(DateUtils.getNowDate());

            UserTeamInfoDTO userTeamInfo = getSafeUserTeamInfo(userId);
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());
            }

            int taskResult = riskDetectionTaskService.insertRiskDetectionTask(task);
            if (taskResult <= 0) {
                throw new RuntimeException("创建风险检测任务失败");
            }

            // 创建任务详情
            List<RiskDetectionTaskDetail> details = new ArrayList<>();
            for (String imageUrl : materialUrls) {
                RiskDetectionTaskDetail detail = new RiskDetectionTaskDetail();
                detail.setTaskId(task.getId());
                // 先去掉可能存在的前缀，再添加前缀，避免重复
                String cleanUrl = CommonUtils.subCosPrefix(imageUrl);
                detail.setImageUrl(CommonUtils.addCosPrefix(cleanUrl));
                detail.setProcessStatus(1); // 待检测
                detail.setCreateTime(DateUtils.getNowDate());
                detail.setUpdateTime(DateUtils.getNowDate());
                detail.setOwnerId(userId); // 使用安全获取的用户ID
                detail.setImageStatus(0);
                details.add(detail);
            }

            // 批量插入详情
            riskDetectionTaskDetailService.insertRiskDetectionTaskDetailBatch(details);

            logger.info("风险检测任务创建成功，任务ID: {}", task.getId());
            return task.getId().toString();

        } catch (Exception e) {
            logger.error("创建风险检测任务失败，工作流节点ID: {}", workflowNodeExecutionId, e);
            throw new RuntimeException("创建风险检测任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建标题提取类型任务
     */
    @Override
    @Transactional
    public String createTitleExtractionTask(List<String> materialUrls, Long workflowNodeExecutionId) {
        try {
            logger.info("开始创建标题提取任务，素材数量: {}, 工作流节点ID: {}",
                    materialUrls.size(), workflowNodeExecutionId);

            // 安全地获取用户ID
            Long userId = getSafeUserId(workflowNodeExecutionId);

            // 创建标题提取任务
            TitleExtractionTask task = new TitleExtractionTask();
            task.setOwnerId(userId);
            task.setTaskBatch(titleExtractionTaskService.generateTaskBatch());
            task.setTotalAmount(materialUrls.size());
            task.setSuccessAmount(0);
            task.setFailAmount(0);
            task.setStatus(1); // 待执行
            task.setWorkflowNodeExecutionId(workflowNodeExecutionId); // 设置工作流节点执行记录ID
            task.setCreateTime(DateUtils.getNowDate());
            task.setUpdateTime(DateUtils.getNowDate());

            UserTeamInfoDTO userTeamInfo = getSafeUserTeamInfo(userId);
            if (userTeamInfo.isTeamMode() && userTeamInfo.getTeamId() != null) {
                // 团队模式：直接通过team_id过滤
                task.setTeamId(userTeamInfo.getTeamId());
            }

            int taskResult = titleExtractionTaskService.insertTitleExtractionTask(task);
            if (taskResult <= 0) {
                throw new RuntimeException("创建标题提取任务失败");
            }

            // 创建任务详情
            List<TitleExtractionTaskDetail> details = new ArrayList<>();
            for (String imageUrl : materialUrls) {
                TitleExtractionTaskDetail detail = new TitleExtractionTaskDetail();
                detail.setTaskId(task.getId());
                // 先去掉可能存在的前缀，再添加前缀，避免重复
                String cleanUrl = CommonUtils.subCosPrefix(imageUrl);
                detail.setImageUrl(CommonUtils.addCosPrefix(cleanUrl));
                detail.setProcessStatus(1); // 待提取
                detail.setCreateTime(DateUtils.getNowDate());
                detail.setUpdateTime(DateUtils.getNowDate());
                detail.setOwnerId(userId); // 使用安全获取的用户ID
                details.add(detail);
            }

            // 批量插入详情
            titleExtractionTaskDetailService.insertTitleExtractionTaskDetailBatch(details);

            logger.info("标题提取任务创建成功，任务ID: {}", task.getId());
            return task.getId().toString();

        } catch (Exception e) {
            logger.error("创建标题提取任务失败，工作流节点ID: {}", workflowNodeExecutionId, e);
            throw new RuntimeException("创建标题提取任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建任务和任务详情
     *
     * @param batchDTO 批次DTO
     * @param batch 批次对象
     * @param userId 用户ID
     * @param materialUrls 素材URL列表
     * @param workflowNodeExecutionId 工作流节点执行ID
     */
    private void createTasksAndTaskOrdinals(BatchDTO batchDTO, Batch batch, Long userId,
                                           List<String> materialUrls, Long workflowNodeExecutionId) {
        try {
            logger.info("开始为批次创建任务和任务详情，批次ID: {}, 任务类型: {}, 素材数量: {}",
                    batch.getBatchId(), batchDTO.getType(), materialUrls.size());

            // 直接创建任务和任务详情，避免调用 BatchTaskFactoryManager 导致的用户上下文问题
            createWorkflowTasksAndOrdinals(batchDTO, batch, userId, materialUrls);

            logger.info("批次任务和任务详情创建完成，批次ID: {}, 任务类型: {}, 素材数量: {}",
                    batch.getBatchId(), batchDTO.getType(), materialUrls.size());

        } catch (Exception e) {
            logger.error("创建任务和任务详情失败，批次ID: {}, 任务类型: {}, 工作流节点ID: {}",
                    batch.getBatchId(), batchDTO.getType(), workflowNodeExecutionId, e);
            throw new RuntimeException("创建任务和任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 为工作流创建任务和任务详情（避免用户上下文问题）
     *
     * @param batchDTO 批次DTO
     * @param batch 批次对象
     * @param userId 用户ID
     * @param materialUrls 素材URL列表
     */
    private void createWorkflowTasksAndOrdinals(BatchDTO batchDTO, Batch batch, Long userId, List<String> materialUrls) {
        try {
            // 获取团队信息
            UserTeamInfoDTO userTeamInfo = getSafeUserTeamInfo(userId);
            Long teamId = userTeamInfo.isTeamMode() ? userTeamInfo.getTeamId() : 0L;

            // 构建任务参数
            String taskParam = buildWorkflowTaskParam(batchDTO);

            List<Task> taskList = new ArrayList<>();
            List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

            // 为每个素材URL创建任务和任务详情
            for (String url : materialUrls) {
                // 创建任务
                Task task = createWorkflowTask(batchDTO, batch, userId, teamId, url);
                taskList.add(task);

                // 创建任务详情
                TaskOrdinal taskOrdinal = createWorkflowTaskOrdinal(userId, taskParam, batchDTO.getType(),
                        task.getTaskId(), url, batch);
                taskOrdinalList.add(taskOrdinal);
            }

            // 批量插入任务
            if (!taskList.isEmpty()) {
                taskMapper.insertBatchTask(taskList);
                logger.info("批量插入任务成功，数量: {}", taskList.size());
            }

            // 处理任务详情执行
            if (!taskOrdinalList.isEmpty()) {
                for (TaskOrdinal taskOrdinal : taskOrdinalList) {
                    try {
                        // 设置批量任务标识
                        taskOrdinal.setExtra("batch_task=true");
                        taskOrdinalFactoryManager.processExecution(taskOrdinal);
                    } catch (Exception e) {
                        logger.error("任务详情执行失败，任务ID：{}，错误：{}", taskOrdinal.getTaskId(), e.getMessage(), e);
                    }
                }
                logger.info("处理任务详情执行完成，数量: {}", taskOrdinalList.size());
            }

        } catch (Exception e) {
            logger.error("创建工作流任务和任务详情失败", e);
            throw new RuntimeException("创建工作流任务和任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建工作流任务参数
     */
    private String buildWorkflowTaskParam(BatchDTO batchDTO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if (com.dataxai.common.utils.StringUtils.isNotEmpty(batchDTO.getTaskParam())) {
                param = objectMapper.readValue(batchDTO.getTaskParam(), OrdinalParamDTO.class);
            }
            return objectMapper.writeValueAsString(param);
        } catch (Exception e) {
            logger.warn("构建任务参数失败，使用默认参数: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * 创建工作流任务
     */
    private Task createWorkflowTask(BatchDTO batchDTO, Batch batch, Long userId, Long teamId, String url) {
        Task task = new Task();
        task.setBatchId(batch.getBatchId());
        task.setTaskId(SnowFlakeUtils.nextIdStr());
        task.setStatus(Constants.TASK_STATUS_PARKING);
        task.setType(batchDTO.getType());
        task.setOriginalUrl(CommonUtils.subCosPrefix(url));
        task.setThumbnailUrl(CommonUtils.subCosPrefix(url));
        task.setUserId(userId);
        task.setCreateTime(batch.getCreateTime());
        task.setUpdateTime(batch.getCreateTime());
        task.setDelFlag(0);
        task.setDescStatus(0);
        task.setTeamId(teamId);
        // 不设置 workflowNodeExecutionId，因为这是素材创作任务，不是工作流节点任务
        // task.setWorkflowNodeExecutionId(null);
        return task;
    }

    /**
     * 创建工作流任务详情
     */
    private TaskOrdinal createWorkflowTaskOrdinal(Long userId, String taskParam, Long type,
                                                 String taskId, String url, Batch batch) {
        TaskOrdinal taskOrdinal = new TaskOrdinal();
        taskOrdinal.setUserId(userId);
        taskOrdinal.setTaskParam(taskParam);
        taskOrdinal.setType(type);
        taskOrdinal.setTaskId(taskId);
        taskOrdinal.setOriginImgUrl(CommonUtils.subCosPrefix(url));
        taskOrdinal.setIsDeductPoints(false); // 工作流任务不扣积分
        taskOrdinal.setCreateTime(batch.getCreateTime());
        taskOrdinal.setUpdateTime(batch.getUpdateTime());
        return taskOrdinal;
    }
}
