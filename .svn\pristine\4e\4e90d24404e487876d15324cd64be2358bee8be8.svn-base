package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户风格收藏表
 * @TableName t_material_style_user
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialStyleUser {
    
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "收藏记录ID")
    private Integer id;
    
    /**
     * 风格素材ID
     */
    @ApiModelProperty(value = "风格素材ID")
    private Integer materialStyleId;
    
    /**
     * 任务类型：6-平铺图文生图，8-文生图
     */
    @ApiModelProperty(value = "任务类型：6-平铺图文生图，8-文生图")
    private Integer taskType;
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;
}
