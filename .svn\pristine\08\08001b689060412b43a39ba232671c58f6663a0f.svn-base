/**
 * @Author: wuy<PERSON>
 * @Date: 2023/12/20
 * @Description: ""
 */
export interface IMaskList {
	index: number
	mask: string
}
export interface IRleResult {
	height: number
	width: number
	mask_list: IMaskList[]
}

export interface ICorpResult {
	description:string
	frameCoordinates:string
	markImgUrl: string
	originalUrl: string
}

export interface ILayerRecognitionProps {
	imgUrl: string
	maskImageUrl: string
	rle: IRleResult
	scale?: number //缩放比例
}

export interface IMatrixImage {
	imageData: string
}

export interface ILayerRef {
	getSelectLayer: () => { layer: IMatrixImage; dom: HTMLDivElement }[]
	setSelectLayer: (layer: IMatrixImage[]) => void
	undoLayer: () => void
	redoLayer: () => void
	resetLayer: () => void
	invertSelect: (isInverse: boolean) => void
  handleCopy: () => void
}
