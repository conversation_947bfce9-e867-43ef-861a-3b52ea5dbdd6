/**
 * @Author: wuyang
 * @Date: 2024/1/11
 * @Description: ""
 */

import { IModalFeatureList, IModelSpellList } from '@/types/task'
import { requestInstance } from './instance'
import { EBizPhone } from '@/types/bizPhone'

/**
 * 查询用户任务列表
 * @param type
 */
export const getTaskList = ({ type }: { type: EBizPhone }) => {
	return requestInstance.get<any, any>(`/task/photo/listByType/${type}`, {})
}

/**
 * 创建任务
 * @param originalUrl
 * @param referedTaskId
 * @param type
 */
export const createTask = ({
	originalUrl,
	referedTaskId,
	type
}: {
	originalUrl?: string
	referedTaskId?: string
	type: EBizPhone
}) => {
	return requestInstance
		.post<any, { taskId: string; type: number }>('/task/photo/add', {
			originalUrl,
			referedTaskId,
			type
		})
		.fire()
}

/**
 * 复制创意
 * @param originalUrl
 * @param referedTaskId
 * @param type
 */
export const copyTask = ({
	originalUrl,
	taskId,
	referedTaskOrdinalId,
	type,
	seed
}: {
	originalUrl: string
	taskId: string
	referedTaskOrdinalId: string
	type: EBizPhone
	seed: number
}) => {
	return requestInstance
		.post<any, { taskId: string }>('/task/photo/copyTask', {
			originalUrl,
			taskId: taskId,
			referedTaskOrdinalId: referedTaskOrdinalId,
			type,
			seed
		})
		.fire()
}

/**
 * 再次编辑
 * @param originalUrl
 * @param taskId
 * @param referedTaskOrdinalId
 * @param type
 */
export const editAgainTask = ({
	originalUrl,
	taskId,
	referedTaskOrdinalId,
	type
}: {
	repair: any
	originalUrl: string
	taskId: string
	referedTaskOrdinalId: string
	type: EBizPhone
}) => {
	return requestInstance
		.post<any, { taskId: string }>('/task/photo/editAgain', {
			originalUrl,
			taskId: taskId,
			referedTaskOrdinalId: referedTaskOrdinalId,
			type
		})
		.fire()
}

/**
 * 再次执行
 * @param originalUrl
 * @param taskId
 * @param referedTaskOrdinalId
 * @param type
 */
export const execAgainTask = ({
	originalUrl,
	taskId,
	referedTaskOrdinalId,
	type
}: {
	repair: any
	originalUrl: string
	taskId: string
	referedTaskOrdinalId: string
	type: EBizPhone
}) => {
	return requestInstance
		.post<any, any>('/task/photo/execAgain', {
			originalUrl,
			taskId: taskId,
			referedTaskOrdinalId: referedTaskOrdinalId,
			type
		})
		.fire()
}

export const executeTask = (data: any) => {
	return requestInstance.post<any, any>('/task/photo/execute', data).fire()
}

/**
 * 给任务更新图片
 * @param originalUrl
 * @param taskId
 */
export const updateOriginalUrlTask = ({
	originalUrl,
	taskId
}: {
	originalUrl: string
	taskId: string
}) => {
	return requestInstance
		.post<any, any>('/task/photo', {
			originalUrl,
			taskId
		})
		.fire()
}

/**
 * 获取任务切割信息
 * @param taskId
 */
export const getTaskSeg = (taskId: string) => {
	return requestInstance.get<any, { segData: string; taskId: number }>(
		`/task/photo/seg/${taskId}`,
		{}
	)
}

/**
 * 获取任务详情
 * @param taskId
 */
export const getTask = (taskId: string) => {
	return requestInstance.get<any, any>(`/task/photo/getInfo/${taskId}`, {})
}

/**
 * 修改任务名称
 * @param type
 * @param taskId
 * @param taskName
 */
export const updateTaskName = ({
	type,
	taskId,
	taskName
}: {
	taskId: string
	taskName: string
	type: EBizPhone
}) => {
	return requestInstance
		.post<any, any>('/task/photo/updateTaskName', {
			taskId: taskId,
			taskName,
			type
		})
		.fire()
}

/**
 * 删除任务
 * @param taskIds
 */
export const deleteTask = (taskIds: string[]) => {
	return requestInstance
		.del<any, any>(`/task/photo/${taskIds.join(',')}`, {})
		.fire()
}

/**
 * 获取快捷模板中筛选的信息列表
 * @param pageNum
 * @param pageSize
 */
export const getShortCutRecommendList = ({
	pageNum,
	pageSize,
	type
}: {
	pageNum: number
	pageSize: number
	type: any
}) => {
	return requestInstance
		.get<
			any,
			{
				total: number
				data: { prompt: string; id: number }[]
			}
		>(`/short-cut/recom/listByFresh`, {
			pageNum,
			pageSize,
			type
		})
		.fire()
}

/**
 * 获取预设模板中的模特选择器相关信息
 */
export const getPresetModelCategory = () => {
	return requestInstance.get<any, { key: string; value: string[] }[]>(
		`/character/model/list/category`,
		{}
	)
}

/**
 * 根据预设模版中的模特信息查询很多模特图片 模特头像
 * @param age
 * @param gender
 * @param skin
 */
export const getPresetModelList = ({
	pageNum,
	pageSize,
	params
}: {
	pageNum: number
	pageSize: number
	params: Array<{ key: string; value: string }>
}) => {
	const filters = Object.fromEntries((params || []).map((p) => [p.key, p.value]))
	return requestInstance
		.get<any, any>(`/character/model/list`, {
			pageNum,
			pageSize,
			...filters
		})
		.fire()
}

/**
 * 获取预设模板中的场景选择器相关信息
 */
export const getPresetSceneCategory = () => {
	return requestInstance.get<any, string[]>(`/scen/realHuman/list/category`, {})
}

/**
 * 获取预设模板中根据场景类型获取很多场景图片
 */
export const getPresetSceneList = ({
	largeCategory,
	pageNum,
	pageSize
}: {
	largeCategory: string
	pageNum: number
	pageSize: number
}) => {
	return requestInstance
		.get<any, any>(`/scen/realHuman/listByFresh`, {
			largeCategory,
			pageNum,
			pageSize
		})
		.fire()
}

/**
 * 获取商品类型的预设信息
 */
export const getPresetGoodsCategory = () => {
	return requestInstance.get<any, { image: string; category: string }[]>(
		`/scen/goods/list/category`,
		{}
	)
}

/**
 * 根据场景信息查询当前信息所在的page 页数
 * @param pageSize
 * @param prompt
 */
export const getPresetScenePageInfo = ({
	pageSize,
	prompt,
	categoryLarge,
	id
}: {
	categoryLarge: string
	pageSize: number
	prompt: string
	id: any
}) => {
	return requestInstance
		.get<any, any>(`/scen/realHuman/pageNum`, {
			categoryLarge,
			id,
			pageSize,
			prompt
		})
		.fire()
}

/**
 * 根据模特信息查询当前信息所在的page 页数
 * @param pageSize
 * @param prompt
 */
export const getPresetModelPageInfo = (data: {
	pageSize: number
	prompt: string
	id: string
	params: Array<{ key: string; value: string }>
}) => {
	return requestInstance.get<any, any>(`/character/model/pageNum`, data).fire()
}

/**
 * 根据商品信息查询当前信息所在的page 页数
 * @param pageSize
 * @param prompt
 */
export const getPresetGoodsPageInfo = ({
	pageSize,
	category,
	prompt,
	id
}: {
	category: string
	pageSize: number
	prompt: string
	id: string
}) => {
	return requestInstance
		.get<any, any>(`/scen/goods/pageNum`, {
			pageSize,
			category,
			prompt,
			id
		})
		.fire()
}

/**
 * 获取商品类型的根据预设信息获取很多商品图片
 */
export const getPresetGoodsList = ({
	catetory,
	pageNum,
	pageSize
}: {
	catetory: string
	pageNum: number
	pageSize: number
}) => {
	return requestInstance
		.get<any, any>(`/scen/goods/listByCategory`, {
			catetory,
			pageNum,
			pageSize
		})
		.fire()
}

/**
 * 保存自定义面部特征图片
 * source: 增加的图片来源 0 为本地 1 为历史
 * @param imgUrl
 * @param taskId
 * @param source
 */
export const addCharacter = ({
	imgUrl,
	taskId,
	source
}: {
	imgUrl: string
	taskId: string
	source: number
}) => {
	return requestInstance
		.post<any, { characterId: string; imgUrl?: any; source: number }>(
			'/defined/character/add',
			{
				source,
				imgUrl,
				taskId // 在此处添加页面类别
			}
		)
		.fire()
}

/**
 * 获取自定义面部特征图片
 * @param taskId
 */
export const getCharacterByTaskId = (taskId: string) => {
	return requestInstance
		.get<any, any>(`/defined/character/info/${taskId}`, {})
		.fire()
}

/**
 * 删除自定义面部特征图片
 * @param taskId
 */
export const delCharacterByTaskId = (taskId: string) => {
	return requestInstance
		.post<any, any>(`/defined/character/remove/${taskId}`, {})
		.fire()
}

/**
 * 模特咒语词典
 */

export const getModelSpellList = () => {
	return requestInstance
		.get<
			any,
			{
				data: IModelSpellList[]
				total: number
			}
		>(`/model/character/dic/listAll`, {})
		.fire()
}

/** 商品咒语词典 */
export const getGoodsSpellList = () => {
	return requestInstance
		.get<
			any,
			{
				data: IModelSpellList[]
				total: number
			}
		>(`/goods/dic/listAll`, {})
		.fire()
}

/** 模特特征可选项 */
export const getModalFeatureList = () => {
	return requestInstance
		.get<
			any,
			{
				data: IModalFeatureList[]
				total: number
			}
		>(`/character/control/listAll`, {})
		.fire()
}
/* 图片反馈 */
export const imageFeedback = (data: any) => {
	return requestInstance
		.post<any, any>(
			'/system/comment',
			data
		)
		.fire()
}
/* 查询历史上传面部自定义特征 */
export const getCharacterList = (userId: string, pageNum: any) => {
	return requestInstance
		.get(`/system/history/list`, { userId: userId, pageNum: pageNum })
		.fire()
}
/* 删除历史上传面部自定义特征 */
export const delCharacterByHistoryId = (historyId: string) => {
	return requestInstance.del(`/system/history/${historyId}`, {}).fire()
}
/**
 *  查询管理后台配置的文生图风格列表
 */
export const getTextToImgStyleList = (data: any) => {
	return requestInstance
		.get(`/front/material/style/page`, data)
		.fire()
}
/**
 *  查询管理后台配置的文生图风格分类列表
 */
export const getTextToImgStyleCategoryList = (data: any) => {
	return requestInstance
		.get('/material/style/category/page', data)
		.fire()
}
/**
 *  查询文生图风格收藏列表
 */
export const getFavoriteStyleList = (data: any) => {
	return requestInstance
		.get(`/front/material/style/favorites`, data)
		.fire()
}
/**
 *  查询用户最近7天使用文生图风格列表
 */
export const getRecentStyleList = (data: any) => {
	return requestInstance
		.get('/front/material/style/recent', data)
		.fire()
}
/**
 *  文生图风格添加收藏
 */
export const addStyleFavorite = (data: any) => {
	return requestInstance
		.post(`/material/style/favorite`, data)
		.fire()
}
/**
 *  文生图风格取消收藏
 */
export const delStyleFavorite = (id: any) => {
	return requestInstance
		.del(`/material/style/favorite/${id}`, {})
		.fire()
}

/**
 *  查询管理后台配置的动漫IP列表
 */
export const getFashIpList = (data: any) => {
	return requestInstance
		.get(`/front/material/ip/page`, data)
		.fire()
}
/**
 *  查询管理后台配置的动漫IP分类列表
 */
export const getFashIpCategoryList = (data: any) => {
	return requestInstance
		.get('/material/ip/category/page', data)
		.fire()
}
/**
 *  查询用户最近7天使用动漫IP列表
 */
export const getRecentIpList = (data: any) => {
	return requestInstance
		.get('/front/material/ip/recent', data)
		.fire()
}
/**
 *  查询动漫IP收藏列表
 */
export const getFavoriteIpList = (data: any) => {
	return requestInstance
		.get(`/front/material/ip/favorites`, data)
		.fire()
}
/**
 *  动漫IP添加收藏
 */
export const addIpFavorite = (data: any) => {
	return requestInstance
		.post(`/material/ip/favorite`, data)
		.fire()
}
/**
 *  动漫IP取消收藏
 */
export const delIpFavorite = (id: any) => {
	return requestInstance
		.del(`/material/ip/favorite/${id}`, {})
		.fire()
}

/**
 *  查询参考风格列表
 */
export const getCommonStyleList = (type: number, pageNum: number, pageSize: number) => {
	return requestInstance
		.get(`/image/style/byPage`, { type: type, pageSize: pageSize, pageNum: pageNum })
		.fire()
}
/**
 * 查询自定义风格
 */
export const getCustomStyleList = (type: number, pageNum: number, pageSize: number) => {
	return requestInstance
		.get(`/image/custom/byPage`, { type: type, pageSize: pageSize, pageNum: pageNum })
		.fire()
}

/**
 *  新增自定义风格
 * @param file
 */
export const addCustomStyleList = (file: File, type: string) => {
	return requestInstance
		.upload<
			any,
			{
				url: string
			}
		>(`/image/custom/add`, { file, type })
		.fire()
}

/**
 *  删除自定义风格
 */
export const delCustomStyleList = (id: string, imgUrl: string, thumbnailImgUrl: string) => {
	return requestInstance
		.post(`/image/custom/delete`, { id, imgUrl, thumbnailImgUrl })
		.fire()
}

/**
 *  查询历史记录
 */
export const getHistoryList = (pageNum: number, pageSize: number, types: any) => {
	const typesStr = Array.isArray(types) ? types.join(',') : types;
	return requestInstance
		.get(`/task/photo/getHistory`, { pageSize, pageNum, types: typesStr })
		.fire()
}
/**
 *  查询我的收藏列表
 */

export const getMyCollection = (pageNum: number, pageSize: number, types: any) => {
	const typesStr = Array.isArray(types) ? types.join(',') : types;
	return requestInstance
		.get(`/task/photo/listByType`, { pageSize, pageNum, types: typesStr })
		.fire()
}
/**
 *  查询上传过的图片列表
 */

export const getHistoryImage = (pageNum: number, pageSize: number, types: any) => {
	const typesStr = Array.isArray(types) ? types.join(',') : types;
	return requestInstance
		.get(`/task/photo/getHistoryImage`, { pageSize, pageNum, types: typesStr })
		.fire()
}
/**
 *  任务重新生成
 */

export const taskExecAgain = (originalUrl: string, taskId: string, referedTaskOrdinalId: string, type: string) => {
	return requestInstance
		.post('/task/photo/execAgain', { originalUrl, taskId, referedTaskOrdinalId, type })
		.fire()
}
/**
 *  任务再次编辑
 */

export const taskEditAgain = (originalUrl: string, referedTaskOrdinalId: string, taskId: string, type: string) => {
	return requestInstance
		.post('/task/photo/editAgain', { originalUrl, referedTaskOrdinalId, taskId, type })
		.fire()
}

/**
 *  删除参考图
 */
export const deleteReferenceFile = (objectKeys: any) => {
	return requestInstance
		.post(`/oss/file/deleteFile`, { objectKeys })
		.fire()
}
/**
 *  删除传过的图
 */
export const deleteHistoryImage = (originImgUrl: string, taskId: string, taskOrdinalId: string) => {
	return requestInstance
		.post('/task/photo/deleteHistoryImage', { originImgUrl, taskId, taskOrdinalId })
		.fire()
}
/**
 *  智能联想
 *  @param description
 */
export const getAssociation = (description: string, type: string) => {
	return requestInstance
		.get('/image/style/association', { description, type })
		.fire()
}
/**
 *   识图转文
 *  @param url
 */
export const imageIntoText = (url: string, type: string) => {
	return requestInstance
		.get('/image/style/readPicture', { url, type })
		.fire()
}
/**
 *  根据types 查询最新的任务数据
 */
export const getNewestTaskResult = (types: any) => {
	const typesStr = Array.isArray(types) ? types.join(',') : types;
	return requestInstance
		.get(`/task/photo/getTaskResult`, { types: typesStr })
		.fire()
}
/**
 *  查询批次列表
 */
export const getBatchList = (data: any) => {
	return requestInstance
		.get(`/batch/list`, data)
		.fire()
}
/**
 *  新建批次信息
 */
export const addBatch = (data: any) => {
	return requestInstance
		.upload(`/batch/add`, data)
		.fire()
}

/**
 * 删除批次信息
 * @param batchId
 */
export const deleteBatch = (batchId: string) => {
	return requestInstance
		.del<any, any>(`/batch/delete/${batchId}`, {})
		.fire()
}
/**
 * 手动终止批次
 * @param batchId
 */
export const stopBatch = (batchId: string) => {
	return requestInstance
		.post<any, any>(`/batch/stop/${batchId}`, {})
		.fire()
}
/**
 * 查询批次详情
 * @param batchId
 */
export const getBatchDetail = (batchId: string) => {
	return requestInstance
		.get(`/batch/getOne/${batchId}`, {})
		.fire()
}
/**
 * 分页查询批次详情(适用于文生图和相似图裂变详情)
 */
export const getOnePage = (data: any) => {
	return requestInstance
		.get(`/batch/getOnePage/list`, data)
		.fire()
}
// export const getOnePage = (batchId: string, pageNum?: number, pageSize?: number) => {
// 	return requestInstance
// 		.get(`/batch/getOnePage/${batchId}`, { pageNum, pageSize })
// 		.fire()
// }
/**
 * 修改自动裁剪任务执行后生成的图片
 */
export const updateBatchImage = (data: any) => {
	return requestInstance
		.upload(`/batch/updateImage`, data)
		.fire()
}
/**
 * 将图片同步到设计器
 */
export const uploadSynchronization = (data: any) => {
	return requestInstance
		.post(`/batch/synchronization`, data)
		.fire()
}
/**
 * 查询数据采集列表
 */
export const getGatherList = (data: any) => {
	return requestInstance
		.get('/product/info/list', data)
		.fire()
}
/**
 * 删除数据采集信息
 * @param id
 */
export const deleteGather = (id: string) => {
	return requestInstance
		.del<any, any>(`/product/info/${id}`, {})
		.fire()
}
/**
 * 批量删除数据采集信息
 * @param ids
 */
export const deleteGatherBatch = (ids: any) => {
	return requestInstance
		.del<any, any>(`/product/info/batch/${ids}`, {})
		.fire()
}
/**
 * 查询风险过滤列表
 */
export const getRiskFilterList = (data: any) => {
	return requestInstance
		.get('/risk/detection/task/list', data)
		.fire()
}
/**
 * 上传图片文件新建风险过滤任务
 */
export const createFilterByFiles = (data: any) => {
	return requestInstance
		.upload(`/risk/detection/task/createByImages`, data)
		.fire()
}
/**
 * 选择图片url新建风险过滤任务
 */
export const createByImageUrls = (data: any) => {
	return requestInstance
		.post('/risk/detection/task/createByImageUrls', data)
		.fire()
}
/**
 * 选择商品id新建风险过滤任务
 */
export const createByProductIds = (data: any) => {
	return requestInstance
		.post('/risk/detection/task/createByProductIds', data)
		.fire()
}
/**
 * 获取侵权风险过滤详情
 * @param id
 */
export const getRiskFilterDetail = (data: any) => {
	return requestInstance
		.get(`/risk/detection/task/${data.id}`, data)
		.fire()
}
/**
 * 批量删除风险检测任务详情
 */
export const deleteRiskFilterDetail = (data: any) => {
	return requestInstance
		.del(`/risk/detection/task/detail/batch/${data.ids}`, {})
		.fire()
}
/**
 * 根据平台代码获取当前用户的账号信息
 * @param platformCode
 */
export const getPlatformInfo = (data: any) => {
	return requestInstance
		.get(`/front/platform-account/by-platform/${data.platformCode}`, data)
		.fire()
}
/**
 * 保存或更新用户第三方平台账号信息
 */
export const savePlatformAccount = (data: any) => {
	return requestInstance
		.post('/front/platform-account/save', data)
		.fire()
}
/**
 * 数据采集导出
 */
export const excelExport = (data: any) => {
	return requestInstance
		.downloadPost('/image/excelExport/export/products', data)
		.fire()
}
/**
 * 查询标题提取任务列表
 */
export const getTitleExtractionTaskList = (data: any) => {
	return requestInstance
		.get('/title/extraction/task/list', data)
		.fire()
}
/**
 * 通过图片文件创建标题提取任务
 */
export const createTitleExtractionTaskByImages = (data: any) => {
	return requestInstance
		.upload('/title/extraction/task/createByImages', data)
		.fire()
}
/**
 * 通过图片url创建标题提取任务
 */
export const createTitleExtractionTaskByImageUrls = (data: any) => {
	return requestInstance
		.post('/title/extraction/task/createByImageUrls', data)
		.fire()
}
/**
 * 通过产品ID创建标题提取任务
 */
export const createTitleExtractionTaskByProductIds = (data: any) => {
	return requestInstance
		.post('/title/extraction/task/createByProductIds', data)
		.fire()
}
/**
 * 获取标题提取详情
 * @param id
 */
export const getTitleExtractionTaskDetail = (data: any) => {
	return requestInstance
		.get(`/title/extraction/task/${data.id}`, { pageNum: data.pageNum, pageSize: data.pageSize })
		.fire()
}
/**
 * 获取工作流模板列表
 */
export const getWorkflowTemplateList = (data: any) => {
	return requestInstance
		.get('/workflow/template/list', data)
		.fire()
}
/**
 * 获取工作流列表
 */
export const getWorkflowList = (data: any) => {
	return requestInstance
		.get('/workflow/workflow/list', data)
		.fire()
}
/**
 * 查询工作流模板分组列表
 */
export const getTemplateGroupList = (data: any) => {
	return requestInstance
		.get('/workflow/templateGroup/list', data)
		.fire()
}
/**
 * 新增工作流模板分组
 */
export const addTemplateGroup = (data: any) => {
	return requestInstance
		.post('/workflow/templateGroup', data)
		.fire()
}
/**
 * 修改工作流模板分组
 */
export const editTemplateGroup = (data: any) => {
	return requestInstance
		.put('/workflow/templateGroup', data)
		.fire()
}
/**
 * 删除工作流模板分组
 */
export const delTemplateGroup = (data: any) => {
	return requestInstance
		.del(`/workflow/templateGroup/${data.id}`, {})
		.fire()
}
/**
 * 新增工作流模板（包含节点）
 */
export const addWorkflowTemplate = (data: any) => {
	return requestInstance
		.post('/workflow/template/withNodes', data)
		.fire()
}
/**
 * 复制工作流模板
 */
export const copyWorkflowTemplate = (data: any) => {
	return requestInstance
		.post(`/workflow/template/copy/${data.id}`, data)
		.fire()
}
/**
 * 获取工作流模板详细信息（包含节点）
 */
export const getWorkflowTemplateDetails = (data: any) => {
	return requestInstance
		.get(`/workflow/template/details/${data.templateId}`, {})
		.fire()
}
/**
 * 修改工作流模板名称/分组
 */
export const editTemplateInfo = (data: any) => {
	return requestInstance
		.put('/workflow/template', data)
		.fire()
}
/**
 * 更新模板节点
 */
export const editWorkflowTemplate = (data: any) => {
	return requestInstance
		.put(`/workflow/template/nodes/${data.id}`, data)
		.fire()
}
/**
 * 移动模板到指定分组
 */
export const moveWorkflowTemplate = (data: any) => {
	return requestInstance
		.put(`/workflow/template/move/${data.id}`, data)
		.fire()
}
/**
 * 删除工作流模板（包含节点）
 */
export const delWorkflowTemplate = (data: any) => {
	return requestInstance
		.del(`/workflow/template/delete/${data.id}`, {})
		.fire()
}
/**
 * 创建工作流（通过文件上传）
 */
export const createWorkflowByFiles = (data: any) => {
	return requestInstance
		.upload('/workflow/workflow/createByFiles', data)
		.fire()
}
/**
 * 创建工作流（通过URL地址列表）
 */
export const createWorkflowByUrls = (data: any) => {
	return requestInstance
		.post('/workflow/workflow/createByUrls', data)
		.fire()
}
/**
 * 查询商品模板分组列表
 */
export const getGoodsTemplateGroupList = (data: any) => {
	return requestInstance
		.get('/system/group/list', data)
		.fire()
}
/**
 * 导入商品模版列表查询
 */
export const getImportTemplateList = (data: any) => {
	return requestInstance
		.get('system/template/list', data)
		.fire()
}
/**
 * 解析excel文件
 */
export const parseExcelFile = (data: any) => {
	return requestInstance
		.upload('system/template/uploadexcel', data)
		.fire()
}

/**
 * 新增商品模板分组
 */
export const addGoodsTemplateGroup = (data: any) => {
	return requestInstance
		.post('/system/group/add', data)
		.fire()
}
/**
 * 修改商品模板分组
 */
export const editGoodsTemplateGroup = (data: any) => {
	return requestInstance
		.put('/system/group/edit', data)
		.fire()
}
/**
 * 删除商品模板分组
 */
export const delGoodsTemplateGroup = (data: any) => {
	return requestInstance
		.del(`/system/group/${data.id}`, {})
		.fire()
}

/**
 * 获取商品模板列表
 */
export const getGoodsTemplateList = (data: any) => {
	return requestInstance
		.get('/system/goodsTemplate/list', data)
		.fire()
}

/**
 * 新增商品模板
 */
export const addGoodsTemplate = (data: any) => {
	return requestInstance
		.post('/system/template/added', data)
		.fire()
}

/**
 * 删除商品模板
 */
export const delGoodsTemplate = (data: any) => {
	return requestInstance
		.del(`/system/template/${data.id}`, {})
		.fire()
}

/**
 * 复制商品模板
 */
export const copyGoodsTemplate = (data: any) => {
	return requestInstance
		.post(`/system/template/copeexcelcustomtemplate/${data.id}`, {})
		.fire()
}

/**
 * 移动商品模板到指定分组
 */
export const moveGoodsTemplate = (data: any) => {
	return requestInstance
		.put(`/system/template/edit`, data)
		.fire()
}

/**
 * 获取商品模板详情
 */
export const getGoodsTemplateDetail = (data: any) => {
	return requestInstance
		.get(`/system/template/selectdetails/${data.id}`, {})
		.fire()
}
/**
 * 编辑商品模板
 */
export const editGoodsTemplate = (data: any) => {
	return requestInstance
		.put('/system/template/updatatExceltemplate', data)
		.fire()
}
/**
 * 商品模板导出
 */
export const goodsTemplateExportexcel = (data: any) => {
	return requestInstance
		.downloadPost('/system/template/exportexcel', data)
		.fire()
}
/* 备注查询 （文生图...印花图） */
export const getBatchRemark = (id: any) => {
	return requestInstance
		.get(`/batch/getOne/${id}`, {})
		.fire()
}


/* 备注查询 (采集 标题 侵权) */
export const getTaskRemark = (id: any) => {
	return requestInstance
		.get(`/title/extraction/task/${id}`, {})
		.fire()
}

/* 任务备注修改 */
export const updateTaskRemark = (data: any) => {
	return requestInstance
		.put('/batch/task/remark', data)
		.fire()
}
/**
 * 团队成员列表
 */
export const getTeamMembers = (data: any) => {
	return requestInstance
		.get('/front/team/members', data)
		.fire()
}
/**
 * 查询印花图品类
 */
export const getPrintingCategoryList = (data: any) => {
	return requestInstance.get('/cropExtractCategory/front/list', data).fire()
}