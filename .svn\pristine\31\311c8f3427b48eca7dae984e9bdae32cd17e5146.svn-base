import React, { useMemo, useState } from 'react';
import { Button, Checkbox, Dropdown, Modal, Input, Select, Radio, Slider, Switch, Tabs, Empty, message, Tooltip, Table } from 'antd';
import type { CheckboxGroupProps } from 'antd/es/checkbox';
import {
    QuestionCircleOutlined, FileTextOutlined, CopyOutlined, ScissorOutlined, BgColorsOutlined, ZoomInOutlined,
    AppstoreOutlined, SafetyOutlined, FontSizeOutlined, NodeIndexOutlined, ReconciliationOutlined, RightOutlined
} from '@ant-design/icons';
import {
    addBatch,
    uploadSynchronization,
    createByImageUrls,
    createTitleExtractionTaskByImageUrls,
    getWorkflowTemplateList,
    getTemplateGroupList,
    createWorkflowByUrls,
    getCommonStyleList,
    getTextToImgStyleList,
    getTextToImgStyleCategoryList,
    getFavoriteStyleList,
    getRecentStyleList,
    addStyleFavorite,
    delStyleFavorite,
    getFashIpList,
    getFashIpCategoryList,
    getFavoriteIpList,
    getRecentIpList,
    addIpFavorite,
    delIpFavorite,
    excelExport,
    getImportTemplateList,
    goodsTemplateExportexcel,
    deleteGather,
    deleteGatherBatch,
    getPrintingCategoryList
} from '@/api/task'
import { ReactComponent as FollowSvg } from '@/asset/svg/follow.svg'
import { ReactComponent as FollowActiveSvg } from '@/asset/svg/follow-active.svg'

export interface BottomActionBarProps<TItem> {
    // 是否显示操作栏
    visible: boolean;
    // 已选数量与总数（展示用）
    selectedCount: number;
    // 是否全选（复选框状态）
    isAllSelected: boolean;
    // 全选/反选处理
    onToggleSelectAll: () => void;
    // 取消选择
    onCancelSelection: () => void;

    // 同步（上传）按钮相关
    syncEnabled?: boolean;
    syncMenuItems?: { key: number | string; label: string }[];
    onSyncClick?: (key: number | string) => void;

    // 下载按钮相关
    onDownload?: () => void;
    downloadLoading?: boolean;
    downloadDisabled?: boolean;

    onActionClick?: (key: string) => void;
    actionDisabled?: boolean;

    // 额外右侧自定义按钮（如：导出、导出模板、删除等）
    extraActionsRender?: () => React.ReactNode;

    // 容器样式覆盖
    className?: string;
    style?: React.CSSProperties;

    // 将弹窗与事件托管到组件：
    // 图片操作（创建任务）下拉菜单（6 平铺、8 文生、9 相似图、12 变清晰、14 裁剪、17 过滤、18 标题、99 工作流）
    taskMenuItems?: { key: string; label: string; icon?: React.ReactNode }[];
    // 选择的列表与提取图片 URL 的方法
    selectedItems?: TItem[];
    extractImageUrl?: (item: TItem) => string;
    // 上传额外参数（如 batchId、type），可为函数基于平台返回
    syncExtraParams?: Record<string, any> | ((platform: number) => Record<string, any>);
    // 是否启用工作流弹窗
    enableWorkflow?: boolean;
    // 动作完成后调用（用于页面刷新、清空选择等）
    onActionFinished?: () => void;

    // 采集页专用能力（可选）
    enableGatherExport?: boolean;
    enableGatherExportTemplate?: boolean;
    enableGatherDelete?: boolean;
    extractGatherId?: (item: TItem) => string | number;
    extractGatherImageUrl?: (item: TItem) => string;
    extractGatherTitle?: (item: TItem) => string;
}

function BottomActionBar<TItem>(props: BottomActionBarProps<TItem>) {
    const {
        visible,
        selectedCount,
        isAllSelected,
        onToggleSelectAll,
        onCancelSelection,
        syncEnabled,
        syncMenuItems,
        onSyncClick,
        onDownload,
        downloadLoading,
        downloadDisabled,
        onActionClick,
        actionDisabled,
        extraActionsRender,
        className,
        style,
        taskMenuItems,
        selectedItems = [],
        extractImageUrl,
        syncExtraParams,
        enableWorkflow,
        onActionFinished,
        enableGatherExport,
        enableGatherExportTemplate,
        enableGatherDelete,
        extractGatherId,
        extractGatherImageUrl,
        extractGatherTitle,
    } = props;

    // 内部状态：上传标签
    const [tagModalVisible, setTagModalVisible] = useState(false);
    const [tagValue, setTagValue] = useState('');
    const [uploadLoading, setUploadLoading] = useState(false);
    const [synchronizeType, setSynchronizeType] = useState<number>(1);

    // 任务弹窗与参数
    const [taskInfo, setTaskInfo] = useState<{ type: string; typeName: string; isModalOpen: boolean }>({ type: '', typeName: '', isModalOpen: false });
    const [creatLoading, setCreatLoading] = useState(false);
    const [isGuideModalVisible, setIsGuideModalVisible] = useState(false);
    const [isChecked, setIsChecked] = useState(false);

    const magnificationOptions: CheckboxGroupProps<string>['options'] = useMemo(() => ([
        { label: '1X', value: '1' },
        { label: '2X', value: '2' },
        { label: '4X', value: '4' },
    ]), []);
    const [magnification, setMagnification] = useState('1');

    const generateQuantityOptions: CheckboxGroupProps<string>['options'] = useMemo(() => ([
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '4', value: '4' },
    ]), []);
    const [generateQuantity, setGenerateQuantity] = useState('1');

    const [onlyFissionPattern, setOnlyFissionPattern] = useState(false);
    const [similarity, setSimilarity] = useState(0.5);
    const [imageScale, setImageScale] = useState('1:1');
    const [isMattingFree, setIsMattingFree] = useState(false);

    // 风格/IP选择
    const [styleRadioValue, setStyleRadioValue] = useState('1');
    const [fashIpRadioValue, setFashIpRadioValue] = useState('1');
    const [styleOrIp, setStyleOrIp] = useState<'style' | 'ip'>('style');
    const [isStyleIpModalOpen, setIsStyleIpModalOpen] = useState(false);
    const [styleTabs, setStyleTabs] = useState<any[]>([]);
    const [activeStyleTab, setActiveStyleTab] = useState('');
    const [filterTab, setFilterTab] = useState('1');
    const [commonStyleList, setCommonStyleList] = useState<any[]>([]);
    const [commonStyleTotal, setCommonStyleTotal] = useState(0);
    const [commonStylePage, setCommonStylePage] = useState(1);
    const [currentStyle, setCurrentStyle] = useState<any>({});
    const [currentFashIP, setCurrentFashIP] = useState<any>({});
    const [noStyleData, setNoStyleData] = useState<{ stylePrompt: string } | null>(null);
    const [styleModalOpen, setStyleModalOpen] = useState(false);

    // 印花品类选择相关状态
    // const [categoryOptions, setCategoryOptions] = useState<any[]>([]);
    // const [categoryLoading, setCategoryLoading] = useState(false);
    // const [cropCategoryId, setCropCategoryId] = useState<number | undefined>(undefined);

    // 工作流
    const [isWorkflowModalVisible, setIsWorkflowModalVisible] = useState(false);
    const [workflowTemplateList, setWorkflowTemplateList] = useState<any[]>([]);
    const [groups, setGroups] = useState<any[]>([]);
    const [activeGroup, setActiveGroup] = useState<any>({ id: 0, groupName: '全部' });
    const [currentTemplate, setCurrentTemplate] = useState<any>({});
    const [creatWorkflowLoading, setCreatWorkflowLoading] = useState(false);

    const [messageApi, messageContextHolder] = message.useMessage();

    const selectedUrls: string[] = useMemo(() => {
        if (!extractImageUrl || !selectedItems?.length) return [];
        return selectedItems.map(extractImageUrl).filter(Boolean);
    }, [selectedItems, extractImageUrl]);

    // 公共节点/任务菜单（默认）
    const allNodeList = useMemo(() => ([
        { taskType: 8, label: '文生图' },
        { taskType: 9, label: '相似图裂变' },
        { taskType: 6, label: '平铺图-文生图' },
        { taskType: 7, label: '平铺图-图生图' },
        { taskType: 14, label: '图案裁剪' },
        { taskType: 11, label: '图片去背景' },
        { taskType: 12, label: '图片变清晰' },
        { taskType: 52, label: '印花图提取' },
        { taskType: 17, label: '侵权风险过滤' },
        { taskType: 18, label: '标题提取' },
    ]), []);

    // 默认同步菜单项
    const defaultSyncMenuItems = useMemo(() => ([
        { key: 1, label: '上传设计器' },
        { key: 2, label: '上传设计平台' },
    ]), []);

    const defaultTaskMenuItems = useMemo(() => {
        const base = [
            { key: '8', label: '文生图', icon: <FileTextOutlined /> },
            { key: '9', label: '相似图裂变', icon: <CopyOutlined /> },
            { key: '6', label: '平铺图生成', icon: <AppstoreOutlined /> },
            { key: '14', label: '图案裁剪', icon: <ScissorOutlined /> },
            { key: '11', label: '图片去背景', icon: <BgColorsOutlined /> },
            { key: '12', label: '图片变清晰', icon: <ZoomInOutlined /> },
            { key: '52', label: '印花图提取', icon: <ReconciliationOutlined /> },
            { key: '17', label: '侵权风险过滤', icon: <SafetyOutlined /> },
            { key: '18', label: '标题提取', icon: <FontSizeOutlined /> },
        ];
        if (enableWorkflow) base.push({ key: '99', label: '工作流', icon: <NodeIndexOutlined /> });
        return base;
    }, [enableWorkflow]);

    // ---------- 导出/导出模板/删除 ----------
    const [exportModalVisible, setExportModalVisible] = useState(false);
    const [exportLoading, setExportLoading] = useState(false);
    const [sexValue, setSexValue] = useState<'男装' | '女装'>('男装');
    const [colorValue, setColorValue] = useState<'白色' | '黑色'>('白色');
    const [tabularValue, setTabularValue] = useState<'1' | '2' | '3'>('1');

    const [exportTemplateModal, setExportTemplateModal] = useState(false);
    const [exportTemplateLoading, setExportTemplateLoading] = useState(false);
    const [exportDataSource, setExportDataSource] = useState<any[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
    const [selectedTemplate, setSelectedTemplate] = useState<any>({});
    const [isAutoMatch, setIsAutoMatch] = useState(false);

    const doOpenExport = () => {
        setExportModalVisible(true);
        setSexValue('男装');
        setColorValue('白色');
        setTabularValue('1');
    };
    const doExport = async () => {
        try {
            setExportLoading(true);
            // gather 导出需要 imageIds 数组
            const imageIds = (selectedItems || []).map((it: any) => (extractGatherId ? extractGatherId(it) : it?.id));
            const param = { imageIds, type: sexValue, color: colorValue, tableType: tabularValue, pageType: 'product' };
            const blob: any = await excelExport(param);
            if (blob) {
                const a = document.createElement('a');
                const url = window.URL.createObjectURL(blob);
                a.href = url; a.download = url.substring(url.lastIndexOf('/') + 1); document.body.appendChild(a); a.click(); a.remove(); window.URL.revokeObjectURL(url);
                messageApi.success('导出成功');
                setExportModalVisible(false);
                onActionFinished?.();
            } else {
                messageApi.error('获取下载链接失败');
            }
        } catch (err: any) {
            messageApi.error(`导出失败: ${err?.msg || err?.data?.msg || ''}`);
        } finally {
            setExportLoading(false);
        }
    };

    const doOpenExportTemplate = async () => {
        try {
            setExportTemplateModal(true);
            setSelectedRowKeys([]);
            setIsAutoMatch(false);
            setExportTemplateLoading(true);
            const res: any = await getImportTemplateList({ pageNum: 1, pageSize: 999 });
            setExportDataSource(res?.data || []);
        } catch (err: any) {
            messageApi.error(`获取导出模板失败:${err?.msg || err?.data?.msg || ''}`);
        } finally {
            setExportTemplateLoading(false);
        }
    };
    const doExportTemplate = async () => {
        if (!selectedRowKeys?.length) { messageApi.warning('请选择导出模板'); return; }
        const list = (selectedItems || []).map((img: any) => ({
            id: selectedRowKeys.join(','),
            enname: extractGatherTitle ? extractGatherTitle(img) : (img?.productTitle || ''),
            dataId: extractGatherId ? extractGatherId(img) : img?.id,
            imageUrl: extractGatherImageUrl ? extractGatherImageUrl(img) : img?.productImageUrl,
            templateType: selectedTemplate?.selectedTemplateType ?? selectedTemplate?.templateType,
        }));
        try {
            setExportTemplateLoading(true);
            const blob: any = await goodsTemplateExportexcel({ tExcelCustomTemplateList: list, doesmatch: isAutoMatch ? 1 : 2, exportType: 1, templateType: selectedTemplate?.selectedTemplateType ?? selectedTemplate?.templateType });
            if (blob) {
                const a = document.createElement('a');
                const url = window.URL.createObjectURL(blob);
                a.href = url; a.download = url.substring(url.lastIndexOf('/') + 1); document.body.appendChild(a); a.click(); a.remove(); window.URL.revokeObjectURL(url);
                messageApi.success('导出成功');
                setExportTemplateModal(false);
                onActionFinished?.();
            } else { messageApi.error('获取下载链接失败'); }
        } catch (err: any) {
            messageApi.error(`导出失败: ${err?.msg || err?.data?.msg || ''}`);
        } finally { setExportTemplateLoading(false); }
    };
    const doDeleteGather = async () => {
        try {
            const ids = (selectedItems || []).map((it: any) => (extractGatherId ? extractGatherId(it) : it?.id));
            if (!ids?.length) return;
            if (ids.length === 1) await deleteGather(ids[0]); else await deleteGatherBatch(ids.join(','));
            messageApi.success(`数据采集信息${ids.length > 1 ? '批量' : ''}删除成功`);
            onActionFinished?.();
        } catch (err: any) {
            messageApi.error(`数据采集信息${(selectedItems || []).length > 1 ? '批量' : ''}删除失败：${err?.data?.msg || ''}`);
        }
    };

    // ------- 数据获取辅助函数（风格/IP/收藏/最近/印花图品类等） -------
    const fetchCommonStyleData = async (pagenum: number, pageSize: number) => {
        try {
            const res: any = await getCommonStyleList(1, pagenum, pageSize);
            setCommonStyleList(res.data || []);
            setNoStyleData(res.default || { stylePrompt: '' });
            if (res.data?.length) setCurrentStyle(res.data[0]);
            setCommonStyleTotal(Math.ceil((res.total || 0) / pageSize));
            setCommonStylePage(pagenum);
        } catch { }
    };
    const fetchStyleCateData = () => {
        getTextToImgStyleCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
            const tabs = res?.list?.map((item: any) => ({ key: item.id, label: item.name })) || [];
            setStyleTabs(tabs);
            setActiveStyleTab(tabs[0]?.key);
            fetchStyleData(1, 12, tabs[0]?.key);
        });
    };
    const fetchStyleData = async (pageNum: number, pageSize: number, categoryId: any) => {
        const response: any = await getTextToImgStyleList({ pageNum, pageSize, categoryId });
        setCommonStyleList(response.list || []);
        setCommonStyleTotal(Math.ceil((response.total || 0) / pageSize));
        setCommonStylePage(pageNum);
        if (!currentStyle?.id && response.list?.length > 0) setCurrentStyle(response.list[0]);
    };
    const fetchFavoriteStyleData = (pageNum: number, pageSize: number) => {
        getFavoriteStyleList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list || []);
            setCommonStyleTotal(Math.ceil((res.total || 0) / pageSize));
            setCommonStylePage(pageNum);
        });
    };
    const fetchRecentStyleData = (pageNum: number, pageSize: number) => {
        getRecentStyleList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list || []);
            setCommonStyleTotal(Math.ceil((res.total || 0) / pageSize));
            setCommonStylePage(pageNum);
        });
    };
    const fetchIpCateData = () => {
        getFashIpCategoryList({ pageNum: 1, pageSize: 999 }).then((res: any) => {
            const tabs = res?.list?.map((item: any) => ({ key: item.id, label: item.name })) || [];
            setStyleTabs(tabs);
            setActiveStyleTab(tabs[0]?.key);
            fetchFashIpData(1, 12, tabs[0]?.key);
        });
    };
    const fetchFashIpData = async (pageNum: number, pageSize: number, categoryId: any) => {
        const response: any = await getFashIpList({ pageNum, pageSize, categoryId });
        setCommonStyleList(response.list || []);
        setCommonStyleTotal(Math.ceil((response.total || 0) / pageSize));
        setCommonStylePage(pageNum);
        if (!currentFashIP?.id && response.list?.length > 0) setCurrentFashIP(response.list[0]);
    };
    const fetchFavoriteIpData = (pageNum: number, pageSize: number) => {
        getFavoriteIpList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list || []);
            setCommonStyleTotal(Math.ceil((res.total || 0) / pageSize));
            setCommonStylePage(pageNum);
        });
    };
    const fetchRecentIpData = (pageNum: number, pageSize: number) => {
        getRecentIpList({ pageNum, pageSize }).then((res: any) => {
            setCommonStyleList(res.list || []);
            setCommonStyleTotal(Math.ceil((res.total || 0) / pageSize));
            setCommonStylePage(pageNum);
        });
    };
    // // 获取印花图品类
    // const fetchPrintingPatternCategory = async () => {
    //     getPrintingCategoryList({}).then((res: any) => {
    //         setCategoryOptions(res || []);
    //     });
    // }
    const handleFollow = (item: any) => {
        const isFavorited = item.isFavorited;
        const isStyle = styleOrIp === 'style';
        const apiCall = isStyle ? (isFavorited ? () => delStyleFavorite(item.favoriteId) : () => addStyleFavorite({ materialStyleId: item.id })) : (isFavorited ? () => delIpFavorite(item.favoriteId) : () => addIpFavorite({ materialIpId: item.id }));
        const refresh = () => {
            if (styleOrIp === 'style') {
                if (filterTab === '2') fetchFavoriteStyleData(1, 12); else if (filterTab === '3') fetchRecentStyleData(1, 12); else fetchStyleData(1, 12, activeStyleTab);
            } else {
                if (filterTab === '2') fetchFavoriteIpData(1, 12); else if (filterTab === '3') fetchRecentIpData(1, 12); else fetchFashIpData(1, 12, activeStyleTab);
            }
        };
        apiCall().then(() => { messageApi.success(isStyle ? (isFavorited ? '取消收藏风格成功' : '风格收藏成功') : (isFavorited ? '取消收藏IP成功' : 'IP收藏成功')); refresh(); })
            .catch((err) => { messageApi.error(`${isStyle ? (isFavorited ? '取消收藏风格失败' : '风格收藏失败') : (isFavorited ? '取消收藏IP失败' : 'IP收藏失败')}: ${err?.data?.msg || err?.msg || ''}`); });
    };

    if (!visible) return null;

    return (
        <div
            className={`fixed w-[60%] bottom-16 left-[20%] right-0 bg-white border-t border-gray-200 p-4 shadow-md ${className || ''}`}
            style={{ zIndex: 100, ...(style || {}) }}
        >
            {messageContextHolder}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Checkbox
                        checked={isAllSelected}
                        onChange={onToggleSelectAll}
                    >
                        全选
                    </Checkbox>
                    <span className="text-gray-600">已选择：{selectedCount}项</span>
                </div>
                <div className="flex space-x-2">
                    {syncEnabled && (syncMenuItems || defaultSyncMenuItems) && (
                        <Dropdown
                            menu={{
                                items: (syncMenuItems || defaultSyncMenuItems)?.map((i) => ({ key: String(i.key), label: i.label })),
                                onClick: ({ key }) => {
                                    setSynchronizeType(Number(key));
                                    setTagValue('');
                                    setTagModalVisible(true);
                                },
                            }}
                            placement="top"
                        >
                            <Button type="primary" disabled={selectedCount === 0}>同步图片</Button>
                        </Dropdown>
                    )}

                    <Button
                        type="primary"
                        disabled={downloadDisabled || selectedCount === 0}
                        onClick={onDownload}
                        loading={!!downloadLoading}
                    >
                        下载
                    </Button>

                    {true && (
                        <Dropdown
                            menu={{
                                items: (taskMenuItems || defaultTaskMenuItems)?.map((i: any) => ({
                                    key: i.key, label: (
                                        <div className="flex items-center gap-2">{i.icon}{i.label}</div>
                                    )
                                })),
                                onClick: ({ key }) => {
                                    if (String(key) === '99' && enableWorkflow) {
                                        // 打开工作流：先获取分组，再拉取模板
                                        setIsWorkflowModalVisible(true);
                                        setCurrentTemplate({});
                                        getTemplateGroupList({ pageNum: 1, pageSize: 99, groupName: '' }).then((res: any) => {
                                            const gs = [{ id: 0, groupName: '全部' }, ...(res?.data || [])];
                                            setGroups(gs);
                                            const first = gs[0];
                                            setActiveGroup(first);
                                            const groupId = first?.id?.toString() === '0' ? '' : first?.id;
                                            return getWorkflowTemplateList({ pageNum: 1, pageSize: 99, groupId }).then((r: any) => setWorkflowTemplateList(r?.data || []));
                                        });
                                        return;
                                    }
                                    const clicked = (taskMenuItems || defaultTaskMenuItems)?.find((i: any) => i.key === key);
                                    setTaskInfo({ type: String(key), typeName: clicked?.label as string, isModalOpen: true });
                                    // 初始化参数
                                    setStyleRadioValue('1');
                                    setFashIpRadioValue('1');
                                    setIsMattingFree(false);
                                    setImageScale(String(key) === '9' ? '原图比例' : '1:1');
                                    setSimilarity(0.5);
                                    setGenerateQuantity('1');
                                    setMagnification('1');
                                    setOnlyFissionPattern(false);
                                    setCurrentStyle({});
                                    setCurrentFashIP({});
                                    // setCropCategoryId(undefined);
                                },
                            }}
                            placement="top"
                        >
                            <Button type="primary" disabled={actionDisabled || selectedCount === 0}>图片操作</Button>
                        </Dropdown>
                    )}

                    {extraActionsRender?.()}

                    {/* 采集页：导出模板/导出/删除 */}
                    {enableGatherExportTemplate && (
                        <Button type="primary" disabled={selectedCount === 0} onClick={doOpenExportTemplate}>导出模板</Button>
                    )}
                    {enableGatherExport && (
                        <Button type="primary" disabled={selectedCount === 0} onClick={doOpenExport}>导出</Button>
                    )}
                    {enableGatherDelete && (
                        <Button type="primary" disabled={selectedCount === 0} onClick={doDeleteGather}>删除</Button>
                    )}

                    <Button type="primary" onClick={onCancelSelection}>取消选择</Button>
                </div>
            </div>
            {/* 上传标签弹窗 */}
            <Modal
                title={synchronizeType === 1 ? '上传设计器标签' : '上传设计平台标签'}
                open={tagModalVisible}
                onOk={async () => {
                    try {
                        setUploadLoading(true);
                        const extra = typeof syncExtraParams === 'function' ? syncExtraParams(synchronizeType) : (syncExtraParams || {});
                        await uploadSynchronization({ imageUrls: selectedUrls, tags: tagValue, platform: synchronizeType, ...extra });
                        messageApi.success(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}成功`);
                        setTagModalVisible(false);
                        setTagValue('');
                        onActionFinished?.();
                    } catch (err: any) {
                        messageApi.error(`图片上传${synchronizeType === 1 ? '设计器' : '设计平台'}失败: ${err?.data?.msg || err?.msg || ''}`);
                    } finally {
                        setUploadLoading(false);
                    }
                }}
                onCancel={() => { setTagModalVisible(false); setTagValue(''); }}
                okText="确认上传"
                cancelText="取消"
                confirmLoading={uploadLoading}
                centered
            >
                <Input placeholder="请输入标签" value={tagValue} onChange={(e) => setTagValue(e.target.value)} />
            </Modal>

            {/* 创建任务弹窗（含风格/IP配置）*/}
            <Modal title={`新建${taskInfo.typeName}任务`} okText="创建" confirmLoading={creatLoading} open={taskInfo.isModalOpen} centered onOk={async () => {
                const type = taskInfo.type;
                const typeName = taskInfo.typeName;
                const urls = selectedUrls;
                const success = () => {
                    messageApi.success(`创建${typeName}成功`);
                    setTaskInfo({ type: '', typeName: '', isModalOpen: false });
                    onActionFinished?.();
                };
                try {
                    let params: any = {};
                    if (type === '6') {
                        params = {
                            styleId: styleRadioValue === '1' ? '' : currentStyle?.styleId,
                            style: styleRadioValue === '1' ? '无风格' : currentStyle?.style,
                            stylePrompt: styleRadioValue === '1' ? noStyleData?.stylePrompt : currentStyle?.stylePrompt,
                            imageScale,
                            imageNumber: Number(generateQuantity),
                        };
                    } else if (type === '8') {
                        params = { isMattingFree: isMattingFree ? 1 : 0, imageScale, imageNumber: Number(generateQuantity) };
                    } else if (type === '9') {
                        params = { similarity, imageScale, onlyFissionPattern: onlyFissionPattern ? 1 : 0, imageNumber: Number(generateQuantity) };
                    } else if (type === '12') {
                        params = { magnification: magnification };
                    } else if (type === '52') {
                        params = { imageNumber: Number(generateQuantity) };
                    }
                    setCreatLoading(true);
                    if (type === '17') {
                        if (!isChecked) { messageApi.error('请先勾选确认侵权风险过滤功能使用须知'); setCreatLoading(false); return; }
                        const resp = await createByImageUrls({ imageUrls: urls });
                        if (resp) success();
                    } else if (type === '18') {
                        const resp = await createTitleExtractionTaskByImageUrls({ imageUrls: urls, type: 1 });
                        if (resp) success();
                    } else if (type === '8') {
                        const resp = await addBatch({ type, imageUrls: urls, materialStyleId: styleRadioValue === '1' ? '' : currentStyle?.id, materialIpId: fashIpRadioValue === '1' ? '' : currentFashIP?.id, taskParam: params ? JSON.stringify(params) : '' });
                        if (resp) success();
                    }
                    // else if (type === '52') {
                    //     const resp = await addBatch({ type, imageUrls: urls, cropCategoryId: cropCategoryId ? cropCategoryId : null, taskParam: params ? JSON.stringify(params) : '' });
                    //     if (resp) success();
                    // } 
                    else {
                        const resp = await addBatch({ type, imageUrls: urls, taskParam: params ? JSON.stringify(params) : '' });
                        if (resp) success();
                    }
                } catch (err: any) {
                    messageApi.error(`创建失败: ${err?.data?.msg || err?.msg || ''}`);
                } finally {
                    setCreatLoading(false);
                }
            }} onCancel={() => setTaskInfo(prev => ({ ...prev, isModalOpen: false }))}>
                <div className=' min-h-[140px] flex  flex-col items-center justify-center'>
                    <p>将对选中的 <span style={{ color: '#32649f' }}>{selectedCount}</span> 张图片执行{taskInfo.typeName}操作</p>
                    {(taskInfo.type === '8' || taskInfo.type === '6') && (
                        <div>
                            {taskInfo.type === '6' && <>
                                <div className='flex justify-between items-center mt-8'>风格选择
                                    <Radio.Group style={{ width: '260px' }} options={[{ label: '不选风格', value: '1' }, { label: '风格选择', value: '2' }]} value={styleRadioValue} onChange={(e) => { setStyleRadioValue(e.target.value); if (e.target.value === '1') setCurrentStyle({}); }} />
                                </div>
                                {styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[140px]' onClick={() => setStyleModalOpen(true)}>
                                    <img className='w-[80px] h-[80px] mr-[10px]' style={{ objectFit: 'cover' }} src={currentStyle?.originImgUrl || currentStyle?.thumbnailImgUrl || ''} />
                                    <div className='w-[200px]'><p className='text-[18px] mb-[12px]'>选中风格</p><p>{currentStyle?.style || ''}</p></div>
                                    <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                                </div>}
                            </>}
                            {taskInfo.type === '8' && <>
                                <div className='flex justify-between items-center mt-8'>IP角色选择
                                    <Radio.Group style={{ width: '260px' }} options={[{ label: '不选IP', value: '1' }, { label: 'IP选择', value: '2' }]} value={fashIpRadioValue} onChange={(e) => { setFashIpRadioValue(e.target.value); if (e.target.value === '1') setCurrentFashIP({}); else { setFilterTab('1'); fetchIpCateData(); setStyleOrIp('ip'); } }} />
                                </div>
                                {fashIpRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]' onClick={() => { setFilterTab('1'); fetchIpCateData(); setIsStyleIpModalOpen(true); setStyleOrIp('ip'); }}>
                                    <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentFashIP?.thumbnailImgUrl || currentFashIP?.ipUrl || ''} />
                                    <div className='w-[230px]'><p className='text-[18px] mb-[12px]'>选中IP</p><p>{currentFashIP?.name || '动漫IP'}</p></div>
                                    <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                                </div>}
                                <div className='flex justify-between items-center mt-8'>风格选择
                                    <Radio.Group style={{ width: '260px' }} options={[{ label: '不选风格', value: '1' }, { label: '风格选择', value: '2' }]} value={styleRadioValue} onChange={(e) => { setStyleRadioValue(e.target.value); if (e.target.value === '1') setCurrentStyle({}); else { setFilterTab('1'); fetchStyleCateData(); setStyleOrIp('style'); } }} />
                                </div>
                                {styleRadioValue === '2' && <div className='border border-normal hover:border-primary rounded-lg p-[12px] flex justify-between items-center mt-[16px] h-[120px]' onClick={() => { setFilterTab('1'); fetchStyleCateData(); setIsStyleIpModalOpen(true); setStyleOrIp('style'); }}>
                                    <img className='w-[80px] h-[80px]' style={{ objectFit: 'cover' }} src={currentStyle?.thumbnailImgUrl || currentStyle?.styleUrl || ''} />
                                    <div className='w-[230px]'><p className='text-[18px] mb-[12px]'>选中风格</p><p>{currentStyle?.name || '自定义风格'}</p></div>
                                    <img className='w-[18px]' src={require('@/asset/icon/zhankai.png')} />
                                </div>}
                                <div className='flex  justify-between items-center  text-normal mt-[20px] mb-[10px]'>
                                    <p>免抠图生成<Tooltip title="利用AI自动从图片中精准分离出前景对象并去除背景,可以省去用户手动抠图的操作。"><QuestionCircleOutlined className=' ml-[6px]' /></Tooltip></p>
                                    <Switch checked={isMattingFree} onChange={(checked) => setIsMattingFree(checked)} />
                                </div>
                            </>}
                            <div className='flex justify-between items-center  mt-4 mb-4'>
                                <p className='w-[100px]'>生成图片比例</p>
                                <Select style={{ width: 280 }} value={imageScale} onChange={(v) => setImageScale(v)} options={[
                                    { value: '1:1', label: '1 : 1' },
                                    { value: '2:3', label: '2 : 3' },
                                    { value: '3:2', label: '3 : 2' },
                                    { value: '3:4', label: '3 : 4' },
                                    { value: '4:3', label: '4 : 3' },
                                    { value: '9:16', label: '9 : 16' },
                                    { value: '16:9', label: '16 : 9' },
                                ]} />
                            </div>
                        </div>
                    )}
                    {taskInfo.type === '9' && (
                        <div>
                            <div className='flex justify-between items-center mt-4'>
                                <p className='w-[110px]'>原图相似度</p>
                                <span className='mr-[10px] '>低</span>
                                <Slider className='w-[220px]' min={0.3} max={0.8} step={0.05} value={similarity} onChange={(v) => setSimilarity(v)} tooltip={{ open: false }} />
                                <span className='ml-[10px] '>高</span>
                            </div>
                            <div className='flex justify-between items-center  mt-4'>
                                <p className='w-[100px]'>生成图片比例</p>
                                <Select style={{ width: 280 }} value={imageScale} onChange={(v) => setImageScale(v)} options={[
                                    { value: '原图比例', label: '原图比例' },
                                    { value: '1:1', label: '1 : 1' },
                                    { value: '2:3', label: '2 : 3' },
                                    { value: '3:2', label: '3 : 2' },
                                    { value: '3:4', label: '3 : 4' },
                                    { value: '4:3', label: '4 : 3' },
                                    { value: '9:16', label: '9 : 16' },
                                    { value: '16:9', label: '16 : 9' },
                                ]} />
                            </div>
                            <div className='flex justify-between items-center text-normal mt-[20px] mb-[10px]'>
                                <p>仅裂变图案</p>
                                <Switch checked={onlyFissionPattern} onChange={(checked) => setOnlyFissionPattern(checked)} />
                            </div>
                        </div>
                    )}
                    {taskInfo.type === '12' && (
                        <div>
                            <p className='w-[360px] flex items-center justify-between text-normal mt-[26px] '>放大倍数
                                <Radio.Group
                                    size="large"
                                    block
                                    options={magnificationOptions}
                                    onChange={(e) => setMagnification(e.target.value)}
                                    value={magnification}
                                    optionType="button"
                                    buttonStyle="solid"
                                    className='w-[280px]'
                                />
                            </p>
                        </div>
                    )}
                    {(taskInfo.type === '6' || taskInfo.type === '8' || taskInfo.type === '9' || taskInfo.type === '52') && (
                        <div>
                            <p className={`w-[380px] flex items-center justify-between text-normal  ${taskInfo.type === '52' ? 'mt-8 mb-0' : 'mb-4 mt-2'}`}>图片生成数量
                                <Radio.Group
                                    size="large"
                                    block
                                    options={generateQuantityOptions}
                                    onChange={(e) => setGenerateQuantity(e.target.value)}
                                    value={generateQuantity}
                                    optionType="button"
                                    buttonStyle="solid"
                                    className='w-[280px]'
                                />
                            </p>
                        </div>
                    )}
                    {/* 印花图品类选择，直接用 categoryOptions 作为 options */}
                    {/* {taskInfo.type === '52' && (
                        <div>
                            <p className={`w-[380px] flex items-center justify-between text-normal  ${taskInfo.type === '52' ? 'mt-8 mb-0' : 'mb-4 mt-2'}`}>
                                选择品类
                                <Select
                                    style={{ width: 280 }}
                                    showSearch
                                    allowClear
                                    placeholder="请选择品类"
                                    value={cropCategoryId}
                                    onChange={(v) => setCropCategoryId(v)}
                                    optionLabelProp="label"
                                    filterOption={(input, option) =>
                                        (typeof option?.label === 'string' ? option.label.toLowerCase() : '').includes(input.toLowerCase())
                                    }
                                    options={categoryOptions.map(item => ({
                                        label: item.name,
                                        value: item.id
                                    }))}
                                    loading={categoryLoading}
                                    onFocus={() => {
                                        if (categoryOptions.length === 0) {
                                            fetchPrintingPatternCategory();
                                        }
                                    }}
                                />
                            </p>
                        </div>
                    )} */}

                </div>
                {taskInfo.type === '17' && (
                    <div className="w-full flex items-center ">
                        <Checkbox checked={isChecked} onChange={() => { if (isChecked) setIsChecked(false); else setIsGuideModalVisible(true); }} />
                        <span className='ml-[10px] cursor-pointer' onClick={() => { if (isChecked) setIsChecked(false); else setIsGuideModalVisible(true); }}>侵权风险过滤功能使用须知</span>
                    </div>
                )}
            </Modal>

            {/* 风格选择弹窗 */}
            <Modal width={800} title="" footer={null} open={styleModalOpen} onCancel={() => setStyleModalOpen(false)}>
                <div>
                    <div className='flex flex-wrap mt-[20px] h-[340px]'>
                        {commonStyleList?.length > 0 ? commonStyleList.map((item: any) => (
                            <div key={item.id} className='w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white'>
                                <img onClick={() => { setCurrentStyle(item); setStyleModalOpen(false); }} src={item.originImgUrl || item.thumbnailImgUrl || ''} className={`${currentStyle?.styleId == item.styleId ? '!border-primary' : ''} w-full h-[110px] rounded-lg  hover:border-primary border-[2px]`} style={{ objectFit: 'cover' }} />
                                <p style={{ textAlign: 'center' }} >{item.style}</p>
                            </div>
                        )) : null}
                    </div>
                    <label>
                        <ul className='flex justify-center  mt-[20px]'>
                            {Array.from({ length: commonStyleTotal }, (_, index) => (
                                <li key={index} className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]`} style={{ lineHeight: '18px' }} onClick={() => { fetchCommonStyleData(index + 1, 12); }}>{index + 1}</li>
                            ))}
                            <li >{'共' + commonStyleTotal + '页'}</li>
                        </ul>
                    </label>
                </div>
            </Modal>

            {/* 风格/IP设置弹窗 */}

            <Modal width={800} footer={null} title={styleOrIp == 'style' ? '设置作品风格' : '设置动漫IP'} open={isStyleIpModalOpen} centered onCancel={() => setIsStyleIpModalOpen(false)}>
                <div className='h-[452px]  relative'>
                    <div className="flex items-center gap-x-4">
                        {['1', '2', '3'].map((k) => (
                            <p key={k} className={`${filterTab == k ? 'bg-black text-white' : ''} w-[70px] h-[30px] rounded-full cursor-pointer bg-[#eee] `} style={{ lineHeight: '30px', textAlign: 'center' }} onClick={() => {
                                setFilterTab(k);
                                if (styleOrIp === 'style') {
                                    if (k === '2') fetchFavoriteStyleData(1, 12); else if (k === '3') fetchRecentStyleData(1, 12); else fetchStyleData(1, 12, activeStyleTab);
                                } else {
                                    if (k === '2') fetchFavoriteIpData(1, 12); else if (k === '3') fetchRecentIpData(1, 12); else fetchFashIpData(1, 12, activeStyleTab);
                                }
                            }}>{k === '1' ? '所有' : k === '2' ? '收藏' : '最近'}</p>
                        ))}
                    </div>
                    {filterTab == '1' && (<Tabs defaultActiveKey="1" items={styleTabs} activeKey={activeStyleTab} onChange={(key: any) => {
                        setActiveStyleTab(key);
                        if (styleOrIp == 'style') { fetchStyleData(1, 12, key); } else { fetchFashIpData(1, 12, key); }
                    }} />)}
                    <div className=' h-[340px]'>
                        <div className='flex flex-wrap mt-[20px]'>
                            {commonStyleList?.length > 0 ? commonStyleList.map((item: any) => (
                                <div key={item.id} className='w-[110px] h-[144px] mr-[7px]  ml-[7px] mt-[1px] rounded-lg overflow-hidden p-[1px] cursor-pointer relative border-white' onClick={() => {
                                    if (styleOrIp == 'style') { setCurrentStyle(item); } else { setCurrentFashIP(item); }
                                    setIsStyleIpModalOpen(false);
                                }}>
                                    <div className="relative w-full h-[110px] rounded-lg overflow-hidden group">
                                        <img src={styleOrIp === 'style' ? `${item?.thumbnailImgUrl || item?.styleUrl || ''}` : `${item?.thumbnailImgUrl || item?.ipUrl || ''}`} className={`${(styleOrIp === 'style' ? currentStyle?.id : currentFashIP?.id) == item.id ? '!border-primary' : ''} w-full h-full rounded-lg hover:border-primary border-[2px]`} style={{ objectFit: 'cover' }} />
                                        <div className="absolute inset-0 bg-black bg-opacity-30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                                            <div className='cursor-pointer absolute w-[20px] h-[20px] top-2 right-2' onClick={(e) => { e.stopPropagation(); handleFollow(item); }}>
                                                {item.isFavorited ? <FollowActiveSvg className={'w-[16px]'} fill={'var(--primary-color)'} /> : <FollowSvg className={'w-[16px] svg-hover-white'} />}
                                            </div>
                                        </div>
                                    </div>
                                    <p style={{ textAlign: 'center' }} >{item.name}</p>
                                </div>
                            )) : <div className='w-full flex justify-center items-center pt-[100px]'><Empty /></div>}
                        </div>
                        <label>
                            <ul className='flex justify-center mt-[20px] w-full  absolute bottom-[8px] left-0'>
                                {Array.from({ length: commonStyleTotal }, (_, index) => (
                                    <li key={index} className={`${commonStylePage == index + 1 ? '!border-black' : ''} w-[20px] h-[20px] border  cursor-pointer ml-[5px] mr-[5px] text-center bg-[#eee]  `} style={{ lineHeight: '18px' }} onClick={() => { if (styleOrIp == 'style') { fetchStyleData(index + 1, 12, activeStyleTab); } else { fetchFashIpData(index + 1, 12, activeStyleTab); } }}>{index + 1}</li>
                                ))}
                                <li>{'共 ' + commonStyleTotal + ' 页'}</li>
                            </ul>
                        </label>
                    </div>
                </div>
            </Modal>


            {/* 侵权风险过滤须知 */}
            <Modal title="侵权风险检测功能使用须知" open={isGuideModalVisible} onOk={() => { setIsChecked(true); setIsGuideModalVisible(false); }} onCancel={() => setIsGuideModalVisible(false)} width={660} centered okText="我已知晓，同意">
                <div className='border-[1px] border-normal p-[16px] border-[#999] bg-[#fafafa] mt-4 mb-4' style={{ borderRadius: '8px' }}>
                    <p className='mb-4'>在您使用本网站提供的侵权风险过滤服务前，请您仔细阅读本声明的所有条款，您一旦开始使用该服务，即表明您无条件地接受本免责声明，您应遵守本声明和相关法律的规定。</p>
                    <p className='mb-4'>1. 我们提供的侵权风险过滤服务（包括但不限于文字、图片等内容的侵权风险分析）均基于自动化算法及公开数据，结果仅供参考，不构成任何形式的法律意见或专业建议。</p>
                    <p className='mb-4'>2. 本服务无法保证结果的绝对准确性、完整性或时效性，可能存在漏判、误判或因法律法规变化导致的偏差。</p>
                    <p className='mb-4'>3. 您需自行判断过滤检测结果的适用性，并承担因依赖该结果而产生的全部风险。对于您依据本服务做出的任何行为（如内容发布、下架、商业决策等），我们不承担法律责任。</p>
                    <p className='mb-4'>4. 如您不同意本声明内容，应立即停止使用侵权风险过滤服务。继续使用视为接受全部条款。</p>
                    <p className='mb-4'>生成页面建议提示内容：检测结果仅供参考，不构成任何形式的法律意见或专业建议。您应做出独立的判断，本网站对您依据本服务做出的决策不承担责任。</p>
                </div>
            </Modal>

            {/* 工作流弹窗 */}
            {enableWorkflow && (
                <Modal title="新建工作流" open={isWorkflowModalVisible} onOk={async () => {
                    if (!currentTemplate?.id) { messageApi.error('请先选择模板'); return; }
                    try {
                        setCreatWorkflowLoading(true);
                        await createWorkflowByUrls({ materialUrls: selectedUrls, templateId: currentTemplate?.id });
                        messageApi.success('工作流创建成功');
                        setIsWorkflowModalVisible(false);
                        onActionFinished?.();
                    } catch (err: any) {
                        messageApi.error(`创建失败: ${err?.msg || err?.data?.msg || ''}`);
                    } finally {
                        setCreatWorkflowLoading(false);
                    }
                }} onCancel={() => setIsWorkflowModalVisible(false)} width={600} centered okText="添加" confirmLoading={creatWorkflowLoading}>
                    <p className='text-gray-500 font-bold mt-5'>添加素材<span style={{ color: '#32649f', fontWeight: 'normal', marginLeft: '10px' }}>已选择 {selectedCount} 张图片</span></p>
                    <div className='flex justify-between items-center  mt-5 mb-6'>
                        <p className='text-gray-500 font-bold'>选择流程</p>
                    </div>
                    <Tabs
                        activeKey={activeGroup?.id?.toString()}
                        onChange={(key: any) => {
                            const selectedGroup = groups.find(g => g.id.toString() === key);
                            setActiveGroup(selectedGroup);
                            getWorkflowTemplateList({ pageNum: 1, pageSize: 99, groupId: key === '0' ? '' : key }).then((r: any) => setWorkflowTemplateList(r.data || []));
                        }}
                        items={groups.map((group) => ({
                            label: group?.groupName || '',
                            key: group?.id?.toString(),
                            children: (
                                <>
                                    {workflowTemplateList.length > 0 ? (
                                        <div className='bg-[#f5f5f5] p-2 flex flex-wrap gap-2 h-[320px] overflow-y-scroll scrollbar-container scrollbar-hide '>
                                            {workflowTemplateList.map((item, index) => (
                                                <div key={index} className={`bg-white w-full  p-4 rounded-lg border  ${currentTemplate?.id && currentTemplate?.id == item?.id ? 'border-[#F06A34]' : 'border-white'}`}
                                                    style={{ height: 'fit-content' }}
                                                    onClick={() => setCurrentTemplate(item)}>
                                                    <p className='text-gray-500 '>{item?.templateName}</p>
                                                    <div className="flex flex-wrap gap-1 min-h-[64px] pt-1">
                                                        {item?.nodeList.map((node: any, inx: number) => (
                                                            <div key={inx} className="flex items-center">
                                                                <span className='border border-primary text-primary pl-2 pr-2 rounded-[4px]'>
                                                                    {node?.taskType ? allNodeList.find(n => n.taskType === node.taskType)?.label : '未知'}
                                                                </span>
                                                                {inx < item?.nodeList.length - 1 && (<RightOutlined className='text-[9px] text-gray-500 ml-1' />)}
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>))}
                                        </div>) : <div className='h-[320px] flex justify-center items-center'>
                                        <Empty description="暂无模板，快去新增吧～" />
                                    </div>}
                                </>
                            ),
                        }))}
                    />
                </Modal>
            )}

            {/* 导出模板选择弹窗 */}
            {enableGatherExportTemplate && (
                <Modal
                    title='导出模板'
                    open={exportTemplateModal}
                    onOk={doExportTemplate}
                    onCancel={() => setExportTemplateModal(false)}
                    okText='确认导出'
                    cancelText='取消'
                    confirmLoading={exportTemplateLoading}
                    centered
                    width={660}
                >
                    <div className='flex items-center mt-4'>
                        <p className='w-[200px]'>是否自动匹配颜色</p>
                        <Switch checked={isAutoMatch} onChange={(v) => setIsAutoMatch(v)} />
                    </div>
                    <Table
                        className='mt-4'
                        loading={exportTemplateLoading}
                        rowKey='id'
                        pagination={false}
                        dataSource={exportDataSource}
                        columns={[
                            { title: '模板名称', dataIndex: 'templateName', key: 'templateName' },
                            { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime' },
                            { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
                        ]}
                        rowSelection={{
                            selectedRowKeys,
                            type: 'radio',
                            onChange: (keys: any[], rows: any[]) => {
                                setSelectedRowKeys(keys);
                                const first = rows?.[0];
                                setSelectedTemplate(first ? { ...first, selectedTemplateType: first?.templateType } : {});
                            }
                        }}
                        scroll={{ y: 55 * 6 }}
                    />
                </Modal>
            )}

            {/* 导出选择弹窗 */}
            {enableGatherExport && (
                <Modal
                    title='导出'
                    open={exportModalVisible}
                    onOk={doExport}
                    onCancel={() => setExportModalVisible(false)}
                    okText='确认导出'
                    cancelText='取消'
                    confirmLoading={exportLoading}
                    centered
                >
                    <p className='text-center mb-8'>将对选中的 <span style={{ color: '#32649f' }}>{selectedCount}</span> 张图片执行导出操作</p>
                    <div className='flex justify-center items-center mt-4'>
                        <p className='w-[90px]'>表格格式</p>
                        <Select style={{ width: 200 }} value={tabularValue} onChange={(v: any) => setTabularValue(v)} options={[
                            { value: '1', label: 'Temu美本' },
                            { value: '2', label: '香港跨境' },
                            { value: '3', label: '中国资料跨境店铺' },
                        ]} />
                    </div>
                    <div className='flex justify-center items-center mt-4'>
                        <p className='w-[90px]'>性别</p>
                        <Select style={{ width: 200 }} value={sexValue} onChange={(v: any) => setSexValue(v)} options={[
                            { value: '男装', label: '男装' },
                            { value: '女装', label: '女装' },
                        ]} />
                    </div>
                    <div className='flex justify-center items-center mt-4 mb-8'>
                        <p className='w-[90px]'>颜色</p>
                        <Select style={{ width: 200 }} value={colorValue} onChange={(v: any) => setColorValue(v)} options={[
                            { value: '白色', label: '白色' },
                            { value: '黑色', label: '黑色' },
                        ]} />
                    </div>
                </Modal>
            )}
        </div>
    );
}

export default BottomActionBar;


