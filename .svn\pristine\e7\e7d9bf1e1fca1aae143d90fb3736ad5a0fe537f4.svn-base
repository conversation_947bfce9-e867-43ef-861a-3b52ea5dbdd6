<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.MaterialStyleUserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.dataxai.web.domain.MaterialStyleUser">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="material_style_id" property="materialStyleId" jdbcType="INTEGER"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, material_style_id, task_type, user_id
    </sql>

    <!-- 插入收藏记录 -->
    <insert id="insert" parameterType="com.dataxai.web.domain.MaterialStyleUser">
        INSERT INTO t_material_style_user (material_style_id, task_type, user_id)
        VALUES (#{materialStyleId}, #{taskType}, #{userId})
    </insert>

    <!-- 根据ID删除收藏记录 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM t_material_style_user WHERE id = #{id}
    </delete>

    <!-- 根据用户ID和风格ID删除收藏记录 -->
    <delete id="deleteByUserAndStyle">
        DELETE FROM t_material_style_user 
        WHERE user_id = #{userId} AND material_style_id = #{styleId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
    </delete>

    <!-- 根据用户ID查询收藏列表（分页） -->
    <select id="selectByUser" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM t_material_style_user
        WHERE user_id = #{userId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        ORDER BY id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据用户ID统计收藏数量 -->
    <select id="countByUser" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_material_style_user 
        WHERE user_id = #{userId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
    </select>

    <!-- 检查收藏是否存在 -->
    <select id="checkFavoriteExists" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_material_style_user
        WHERE user_id = #{userId} AND material_style_id = #{styleId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
    </select>

    <!-- 获取用户收藏的风格映射 -->
    <select id="selectUserFavoriteMap" resultType="java.util.Map">
        SELECT
            material_style_id as styleId,
            id as favoriteId
        FROM t_material_style_user
        WHERE user_id = #{userId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        <if test="styleIds != null and styleIds.size() > 0">
            AND material_style_id IN
            <foreach collection="styleIds" item="styleId" open="(" separator="," close=")">
                #{styleId}
            </foreach>
        </if>
    </select>

    <!-- 根据用户ID和风格ID查询收藏记录 -->
    <select id="selectByUserAndStyle" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_material_style_user
        WHERE user_id = #{userId} AND material_style_id = #{styleId}
        <if test="taskType != null">
            AND task_type = #{taskType}
        </if>
        LIMIT 1
    </select>

</mapper>
