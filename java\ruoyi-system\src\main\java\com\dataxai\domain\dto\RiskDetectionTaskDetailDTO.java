package com.dataxai.domain.dto;

import com.dataxai.domain.RiskDetectionTask;
import com.dataxai.domain.RiskDetectionTaskDetail;

import java.util.List;

/**
 * 风险检测任务详细信息DTO
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RiskDetectionTaskDetailDTO extends RiskDetectionTask {

    /** 任务详情列表 */
    private List<RiskDetectionTaskDetail> detailList;

    /** 详情总数 */
    private Integer detailCount;

    /** 已处理详情数 */
    private Integer processedCount;

    /** 未处理详情数 */
    private Integer unprocessedCount;

    /** 当前页码 */
    private Integer pageNum;

    /** 每页数量 */
    private Integer pageSize;

    /** 总记录数 */
    private Integer total;

    /** 总页数 */
    private Integer pages;

    /** 是否有下一页 */
    private Boolean hasNextPage;

    /** 是否有上一页 */
    private Boolean hasPreviousPage;

    public List<RiskDetectionTaskDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<RiskDetectionTaskDetail> detailList) {
        this.detailList = detailList;
    }

    public Integer getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(Integer detailCount) {
        this.detailCount = detailCount;
    }

    public Integer getProcessedCount() {
        return processedCount;
    }

    public void setProcessedCount(Integer processedCount) {
        this.processedCount = processedCount;
    }

    public Integer getUnprocessedCount() {
        return unprocessedCount;
    }

    public void setUnprocessedCount(Integer unprocessedCount) {
        this.unprocessedCount = unprocessedCount;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Boolean getHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(Boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public Boolean getHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(Boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }
}