package com.dataxai.web.service.impl;

import com.alibaba.fastjson2.JSON;
import com.aliyun.green20220302.models.ImageModerationRequest;
import com.aliyun.green20220302.models.ImageModerationResponse;
import com.aliyun.green20220302.models.ImageModerationResponseBody;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.framework.aliyun.AliYunUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.DeleteImage;
import com.dataxai.web.domain.Task;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.mapper.DeleteImageMapper;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.service.AliYunFileService;
import com.aliyun.green20220302.Client;
import com.dataxai.web.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.opencsv.CSVReader;
import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.text.SimpleDateFormat;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.ArrayList;

@Service
@Slf4j
public class AliYunFileServiceImpl implements AliYunFileService {



    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private DeleteImageMapper deleteImageMapper;

    @Value("${aliyun.endpoint}")
    private String endpoint;

    @Override
    public String uploadALiYun(MultipartFile image) {
        // 创建OSSClient实例。
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;
        String endpoint = AliYunUtils.AliYun_endpoint;
        try {
            // 文件名可以根据业务需求自定义
            String url = uploadImageFile(image,ossClient,bucketName);
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    private String uploadImageFile(MultipartFile image, OSS ossClient,String bucketName) throws IOException {
        String originalFilename = image.getOriginalFilename();
        //String originalFilename = image.getName();
        String suffix = "";
        if(StringUtils.isNotEmpty(originalFilename)){
            int lastIndexOf = originalFilename.lastIndexOf(".");
            suffix = originalFilename.substring(lastIndexOf);
        }
        String filename = UUID.randomUUID().toString().replaceAll("-","")+suffix;
        //目录的生成 当前日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = dateFormat.format(new Date());
        String key = date+"/"+filename;

        ossClient.putObject(bucketName, key,image.getInputStream());
        System.out.println("Object " + key + " uploaded successfully.");
        // 构建图片的完整URL
        StringBuilder imgUrl = new StringBuilder();
        imgUrl.append(Constants.OOS_URL_PREFIX).append(key);
        return imgUrl.toString();
    }

    @Override
    public String uploadImageWebp(ByteArrayResource image) {
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;
        try {
            // 文件名可以根据业务需求自定义
            String filename = UUID.randomUUID().toString().replaceAll("-", "") + ".webp";
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String date = dateFormat.format(new Date());
            String key = date + "/" + filename;

            // 上传文件到指定文件夹下
            ossClient.putObject(bucketName, key, image.getInputStream());
            System.out.println("Object " + key + " uploaded successfully.");

            // 构建图片的完整URL
            StringBuilder imgUrl = new StringBuilder();
            imgUrl.append(Constants.OOS_URL_PREFIX).append(key);
            return imgUrl.toString();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }
    //将字节信息上传到阿里云

    @Override
    public String upload(ByteArrayResource image) {
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;
        try {
            // 文件名可以根据业务需求自定义
            String filename = UUID.randomUUID().toString().replaceAll("-", "") + ".jpg";
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String date = dateFormat.format(new Date());
            String key = date + "/" + filename;

            // 上传文件到指定文件夹下
            ossClient.putObject(bucketName, key, image.getInputStream());
            System.out.println("Object " + key + " uploaded successfully.");

            // 构建图片的完整URL
            StringBuilder imgUrl = new StringBuilder();
            imgUrl.append(Constants.OOS_URL_PREFIX).append(key);
            return imgUrl.toString();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }


    public static List<PromptDTO> readExcel(InputStream inputStream) throws Exception {
        List<PromptDTO> promptDTOList = new ArrayList<>();

        Workbook workbook = new XSSFWorkbook(inputStream);
        Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表

        for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 从第二行开始（索引从0开始）
            Row row = sheet.getRow(i);
            if (row == null) continue; // 跳过空行

            Cell cell = row.getCell(0); // 获取第一列
            if (cell == null || cell.getCellType() == CellType.BLANK) continue; // 跳过空单元格

            String value = getCellValueAsString(cell);
            if (value != null && !value.trim().isEmpty()) {
                PromptDTO dto = new PromptDTO();
                dto.setPrompt(value.trim());
                promptDTOList.add(dto);
            }
        }
        workbook.close();
        return promptDTOList;
    }

    // 辅助方法：获取单元格的值为字符串
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }

    public List<PromptDTO> readCSV(InputStream inputStream) throws Exception {
        List<PromptDTO> promptDTOList = new ArrayList<>();

        Reader reader = new InputStreamReader(inputStream);
        CSVReader csvReader = new CSVReader(reader);

        String[] nextRecord;
        boolean isFirstRow = true; // 标记是否是第一行（表头）

        while ((nextRecord = csvReader.readNext()) != null) {
            if (isFirstRow) {
                isFirstRow = false; // 跳过表头
                continue;
            }

            if (nextRecord.length > 0) {
                String value = nextRecord[0].trim(); // 获取第一列
                if (!value.isEmpty()) {
                    PromptDTO dto = new PromptDTO();
                    dto.setPrompt(value);
                    promptDTOList.add(dto);
                }
            }
        }

        csvReader.close();
        return promptDTOList;
    }


    @Override
    public List<PromptDTO> readTable(String table) {
        OSS ossClient = createOssClient();
        try {
            String bucketName = AliYunUtils.AliYun_bucketName;
            // 下载文件
            InputStream inputStream = null;
            // 判断文件类型并解析
            List<PromptDTO> result;
            if (table.endsWith(".xlsx")) {
                result =readExcel(inputStream);
            } else if (table.endsWith(".csv")) {
                result = readCSV(inputStream);
            } else {
                System.out.println("不支持的文件格式");
                return null;
            }

            // 打印结果
            result.forEach(dto -> System.out.println(dto.getPrompt()));

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
        return null;
    }

    @Override
    public List<String> batchUploadALiYun(List<MultipartFile> files) {
        // 创建OSSClient实例。
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;
        String endpoint = AliYunUtils.AliYun_endpoint;
        // 用于存储所有成功上传的图片URL
        List<String> uploadedUrls = Collections.synchronizedList(new ArrayList<>());
        // 创建线程池
        int corePoolSize = 10;
        int maxPoolSize = 20;
        long keepAliveTime = 60L; // 空闲线程存活时间
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(50); // 任务队列容量

        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：由调用线程执行任务
        );
//        int threadPoolSize = 10; // 根据实际需求调整线程池大小
//        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

        try {

            // 提交任务到线程池
            List<Future<String>> futures = files.stream().map(file -> executorService.submit(() -> {
                try {
                    StringBuilder fileInfo = new StringBuilder("上传文件列表：\n");
                    fileInfo.append("- 文件名: ").append(file.getOriginalFilename())
                        .append(", 大小: ").append(file.getSize()).append(" 字节")
                        .append(", 类型: ").append(file.getContentType())
                        .append("\n");
                    String url = uploadImageFile(file,ossClient,bucketName);
                    return url;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null; // 返回null表示该文件上传失败
                }
            })).collect(Collectors.toList());
            // 收集结果
            for (Future<String> future : futures) {
                try {
                    String url = future.get(); // 获取上传结果
                    if (url != null) {
                        uploadedUrls.add(url);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } finally {
            // 关闭线程池和OSS客户端
            executorService.shutdown();
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        // 返回所有成功上传的图片URL列表
        return uploadedUrls;
    }

    @Override
    public String batchZipUrl(String imeUrls) throws Exception {
        // 分割图片地址
//        imeUrls = CommonUtils.subCosPrefix(imeUrls);
        String[] split = imeUrls.split(",");
        List<String> list = Arrays.asList(split);

        long startTime = System.currentTimeMillis();

        // 异步并行下载图片
        ExecutorService executorService = Executors.newFixedThreadPool(100);
        List<CompletableFuture<byte[]>> futures = new ArrayList<>();

        for (String key : list) {
            CompletableFuture<byte[]> future = CompletableFuture.supplyAsync(() -> downloadImageFromOSS(key), executorService);
            futures.add(future);
        }
        Set<Integer> pendingIndices = new HashSet<>(); // 记录未完成任务的索引
        int maxRetries = 5; // 最大重试次数

        // 收集下载结果
        List<byte[]> imageDataList = new ArrayList<>();
        for (int i = 0; i < futures.size(); i++) {
            pendingIndices.add(i);
        }

        // 多轮尝试获取结果
        for (int attempt = 1; attempt <= maxRetries && !pendingIndices.isEmpty(); attempt++) {
            System.out.println("Attempt " + attempt + ": Processing " + pendingIndices.size() + " pending tasks...");

            Set<Integer> newPendingIndices = new HashSet<>(); // 新一轮未完成任务的索引

            for (int index : pendingIndices) {
                CompletableFuture<byte[]> future = futures.get(index);
                try {
                    byte[] imageData = future.get(1, TimeUnit.SECONDS); // 每个任务最多等待 5 秒
                    if (imageData != null) {
                        imageDataList.add(imageData);
                    } else {
                        newPendingIndices.add(index); // 如果结果为空，继续标记为未完成
                    }
                } catch (TimeoutException e) {
                    newPendingIndices.add(index); // 超时任务标记为未完成
                    System.err.println("Task timed out: Retrying... (" + (maxRetries - attempt) + " attempts left)");
                } catch (Exception e) {
                    System.err.println("Error occurred while fetching image data for task " + index);
                    e.printStackTrace();
                }
            }

            pendingIndices = newPendingIndices; // 更新未完成任务索引
        }
        executorService.shutdown();
        executorService.awaitTermination(1, TimeUnit.MINUTES);


        // 创建 ZIP 文件
        byte[] zipData = createZipFile(imageDataList);

        // 将 ZIP 文件上传到 OSS
        String zipObjectKey = uploadToOSS(zipData);

        // 生成临时下载链接
        String downloadUrl = generatePresignedUrl(zipObjectKey);

        long endTime = System.currentTimeMillis();
        System.out.println("Total time: " + (endTime - startTime) + " ms");
        return downloadUrl;

    }

    @Override
    public Map<String, Object> excelUpload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空！");
        }
        // 获取文件扩展名
        String originalFileName = file.getOriginalFilename();
        // 转换为临时文件
        File tempFile = convertToFile(file);
        try {
            String s = generateUniqueFileName(originalFileName);
            String  key = "excel/"+s;
            // 符合要求，直接上传原文件
            String url = uploadToOSS(tempFile, key);
            Map<String,Object> map = new HashMap<>();
            map.put("url",url);
            return map;
        } catch (Exception e) {
            throw new RuntimeException("文件处理失败", e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }

    }

    /**
     * 创建 ZIP 文件
     */
    private byte[] createZipFile(List<byte[]> imageDataList) throws IOException {
        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(zipOutputStream)) {
            for (int i = 0; i < imageDataList.size(); i++) {
                byte[] imageData = imageDataList.get(i);
                ZipEntry zipEntry = new ZipEntry("image_" + (i + 1) + ".png");
                zipOut.putNextEntry(zipEntry);
                zipOut.write(imageData);
                zipOut.closeEntry();
            }
        }
        return zipOutputStream.toByteArray();
    }
    /**
     * 上传 ZIP 文件到 OSS
     */
    private String uploadToOSS(byte[] zipData) {
        String bucketName = AliYunUtils.AliYun_bucketName;
        String datePath = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
        String objectKey = datePath + System.currentTimeMillis() + "_images.zip";

        AliYunUtils.executeOSSOperation(ossClient -> {
            ossClient.putObject(bucketName, objectKey, new ByteArrayInputStream(zipData));
            return null;
        });

        return objectKey;
    }

    /**
     * 下载图片内容
     */
    private byte[] downloadImageFromOSS(String relativePath) {

        try (InputStream inputStream = new URL(relativePath).openStream()) {
            return IOUtils.toByteArray(inputStream); // 使用 Apache Commons IO 工具类读取流
        } catch (IOException e) {
            System.err.println("Failed to download image from URL: " + relativePath);
            return null;
        }
    }

    private static void uploadToOSS(String objectKey, byte[] data) throws IOException {
        String bucketName = AliYunUtils.AliYun_bucketName;
        AliYunUtils.executeOSSOperation(ossClient -> {
            ossClient.putObject(bucketName, objectKey, new ByteArrayInputStream(data));
            return null; // 返回值可以忽略
        });
    }


    private static String generatePresignedUrl(String objectKey) {
        String bucketName = AliYunUtils.AliYun_bucketName;
        return AliYunUtils.executeOSSOperation(ossClient -> {
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000L); // 链接有效期 60 分钟
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectKey);
            request.setExpiration(expiration);
            String replace = ossClient.generatePresignedUrl(request).toString();
            // 要查找的关键字
            String keyword = "aliyuncs.com/";
            // 找到关键字的位置
            int index = replace.indexOf(keyword);
            String result = replace.substring(index + keyword.length());
            return Constants.OOS_URL_PREFIX+result;
        });
    }



    public static Client createClient(String accessKeyId, String accessKeySecret, String endpoint) throws Exception {
        Config config = new Config();
        config.setAccessKeyId(accessKeyId);
        config.setAccessKeySecret(accessKeySecret);
        config.setEndpoint(endpoint);
        return new Client(config);
    }

    //鉴定url
    @Override
    public Boolean authenticateUrl(String imgUrl) throws Exception {
        String accessKeyId = AliYunUtils.AliYun_accessKeyId;
        String accessKeySecret = AliYunUtils.AliYun_accessKeySecret;
        String bucketName = AliYunUtils.AliYun_bucketName;
        String greenEndpoint = AliYunUtils.AliYun_green_region;
        // 创建 Client 实例
        Client client = createClient(accessKeyId, accessKeySecret, greenEndpoint);

        // 构造批量检测参数
        List<Map<String, Object>> tasks = new ArrayList<>();

        Map<String, Object> task = new HashMap<>();
        task.put("dataId", UUID.randomUUID().toString());
        task.put("url", imgUrl); // 图片URL
        tasks.add(task);

        List<String> scenes = Arrays.asList("porn", "terrorism", "politics"); // 设置需要检测的场景
        Map<String, Object> serviceParameters = new HashMap<>();
        serviceParameters.put("scenes", scenes);
        serviceParameters.put("tasks", tasks);
        serviceParameters.put("ossBucketName", bucketName);
        serviceParameters.put("imageUrl", imgUrl);

        ImageModerationRequest request = new ImageModerationRequest();
        request.setService("baselineCheck");
        request.setServiceParameters(JSON.toJSONString(serviceParameters));

        // 发送请求
        RuntimeOptions runtime = new RuntimeOptions();
        ImageModerationResponse response = client.imageModerationWithOptions(request, runtime);

        // 处理响应
        if (response != null && response.getStatusCode() == 200) {
            ImageModerationResponseBody body = response.getBody();
            if (body.getCode() == 200) {
                ImageModerationResponseBody.ImageModerationResponseBodyData data = body.getData();
                List<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> results = data.getResult();
                for (ImageModerationResponseBody.ImageModerationResponseBodyDataResult result : results) {
                    if (result.getLabel().equals("violent_horrificContent") ||
                            (result.getConfidence() != null && result.getConfidence() > 80)) {
                        List<String> list = new ArrayList<>();
                        list.add(CommonUtils.subCosPrefix(imgUrl));
                        batchDelete(list, false,0); // 删除有问题的图片
                        return false;
                    }
                }
                return true; // 所有图片鉴权通过
            }
        }
        return true;
    }

    @Override
    public Boolean batchDelete(List<String> fileUrls,Boolean checkStatus,Integer integer) {
        log.info("开始删除图片操作.....操作人"+ SecurityUtils.getUserId()+".....操作类型："+integer+"....删除图片:"+fileUrls);
        DeleteImage deleteImage = new DeleteImage();
        deleteImage.setType(integer);
        deleteImage.setUserId(SecurityUtils.getUserId());
        deleteImage.setImageUrl(fileUrls.toString());
        deleteImageMapper.insertDeleteImage(deleteImage);
        if(checkStatus){
            String urls = fileUrls.stream().collect(Collectors.joining(","));
            Task taskParam = new Task();
            taskParam.setOriginalUrl(urls);
            List<Task> tasks = taskMapper.selectTaskList(taskParam);
            if(CollectionUtil.isNotEmpty(tasks)){
                Task task = tasks.get(0);
                if(task.getStatus() !=Constants.TASK_STATUS_EXECUTING && task.getStatus() != Constants.TASK_STATUS_PARKING
                        && task.getStatus() !=Constants.TASK_STATUS_SUCCESS){
                    Boolean flag = batchDeleteFile(fileUrls);
                    return flag;
                }else{
                    return true;
                }
            }else{
                Boolean flag = batchDeleteFile(fileUrls);
                return flag;
            }
        }else{
            Boolean flag = batchDeleteFile(fileUrls);
            return flag;
        }

    }

    /**
     * 判断是否是支持的文件类型
     *
     * @param extension 文件扩展名
     * @return 是否支持
     */
    private boolean isSupportedFileType(String extension) {
        return ".xls".equalsIgnoreCase(extension) || ".xlsx".equalsIgnoreCase(extension) || ".csv".equalsIgnoreCase(extension);
    }

    @Override
    public List<PromptDTO> parseTable (MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空！");
        }

        // 获取文件扩展名
        String originalFileName = file.getOriginalFilename();
        String extension = getExtension(originalFileName);

        // 转换为临时文件
        File tempFile = convertToFile(file);
        try {
            List<PromptDTO> data = parseTable(tempFile, extension);
            return data;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 上传表格
     * @param file
     * @return
     */
    @Override
    public Map<String,Object> fileUploadTable(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空！");
        }

        // 获取文件扩展名
        String originalFileName = file.getOriginalFilename();
        String extension = getExtension(originalFileName);
        // 转换为临时文件
        File tempFile = convertToFile(file);

        try {
            // 解析表格内容
            List<PromptDTO> data = parseTable(tempFile, extension);

            // 校验逻辑
            boolean isValid = validateData(data);
            String url = new String();
            if (isValid) {
                // 符合要求，直接上传原文件
                url = uploadToOSS(tempFile, generateUniqueFileName(originalFileName));
            } else {
                // 不符合要求，生成新文件并在第二列添加校验结果
                File newFile = generateNewFile(tempFile, data, extension);
                url =  uploadToOSS(newFile, generateUniqueFileName("modified_" + originalFileName));
            }
            Map<String,Object> map = new HashMap<>();
            map.put("flag",isValid);
            map.put("url",url);
            return map;
        } catch (Exception e) {
            throw new RuntimeException("文件处理失败", e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }

    }

//    @Override
    public Map<String,Object> validateUploadTable(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空！");
        }
        // 获取文件扩展名
        String originalFileName = file.getOriginalFilename();
        String extension = getExtension(originalFileName);
        // 转换为临时文件
        File tempFile = convertToFile(file);

        try {
            // 解析表格内容
            List<PromptDTO> data = parseTable(tempFile, extension);

            // 校验逻辑
            /**
             *取消行数限制，为避免大范围逻辑代码修改flag默认传true不再走validateData数据校验
             */
//            boolean isValid = validateData(data);
            String url = new String();
            url = uploadToOSS(tempFile, generateUniqueFileName(originalFileName));
//            String url = new String();
//            if (isValid) {
//                // 符合要求，直接上传原文件
//                url = uploadToOSS(tempFile, generateUniqueFileName(originalFileName));
//            } else {
//                // 不符合要求，生成新文件并在第二列添加校验结果
//                File newFile = generateNewFile(tempFile, data, extension);
//                url =  uploadToOSS(newFile, generateUniqueFileName("modified_" + originalFileName));
//            }
            Map<String,Object> map = new HashMap<>();
            map.put("flag",true);
            map.put("url",url);
            return map;
        } catch (Exception e) {
            throw new RuntimeException("文件处理失败", e);
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }

    }


    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 设置字体
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);

        // 设置居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 应用字体
        style.setFont(font);
        return style;
    }

    /**
     * 创建内容样式
     */
    private CellStyle createContentStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 设置字体
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);

        // 设置左对齐
        style.setAlignment(HorizontalAlignment.LEFT);

        // 自动换行
        style.setWrapText(false);

        // 隐藏超出部分（不缩小字体以适应单元格）
        style.setShrinkToFit(false);

        // 应用字体
        style.setFont(font);
        return style;
    }



    /**
     * 生成新文件并在第二列添加校验结果
     *
     * @param originalFile 原始文件
     * @param data         表格数据
     * @param extension    文件扩展名
     * @return 新文件
     */
    private File generateNewFile(File originalFile, List<PromptDTO> data, String extension) throws IOException {
        File newFile = File.createTempFile("modified", extension);

        if (".csv".equalsIgnoreCase(extension)) {
            // 处理 CSV 文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(newFile))) {
                for (PromptDTO dto : data) {
                    String content = dto.getPrompt();
                    String validationResult = dto.getResult();
                    writer.write(content + "," + validationResult);
                    writer.newLine();
                }
            }
        } else if (".xls".equalsIgnoreCase(extension) || ".xlsx".equalsIgnoreCase(extension)) {
            // 处理 Excel 文件
            Workbook workbook = null;
            try {
                if (".xls".equalsIgnoreCase(extension)) {
                    workbook = new HSSFWorkbook();
                } else {
                    workbook = new XSSFWorkbook();
                }

                Sheet sheet = workbook.createSheet("Modified");

                // 创建表头样式
                CellStyle headerStyle = createHeaderStyle(workbook);
                // 创建内容样式
                CellStyle contentStyle = createContentStyle(workbook);

                // 创建表头
                Row headerRow = sheet.createRow(0);
                String[] headers = {"提示词", "校验结果"};
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                    cell.setCellStyle(headerStyle);
                }

                // 设置列宽
                sheet.setColumnWidth(0, 150 * 256); // 第一列宽度为 150px
                sheet.setColumnWidth(1, 50 * 256);  // 第二列宽度为 50px

                // 填充数据
                int rowNum = 1;
                for (PromptDTO dto : data) {
                    Row excelRow = sheet.createRow(rowNum++);
                    String content = dto.getPrompt();
                    String validationResult = dto.getResult();

                    Cell promptCell = excelRow.createCell(0);
                    Cell resultCell = excelRow.createCell(1);

                    promptCell.setCellValue(content);
                    resultCell.setCellValue(validationResult);

                    promptCell.setCellStyle(contentStyle);
                    resultCell.setCellStyle(contentStyle);
                }

                try (FileOutputStream out = new FileOutputStream(newFile)) {
                    workbook.write(out);
                }
            } finally {
                if (workbook != null) {
                    workbook.close();
                }
            }
        }

        return newFile;
    }
    /**
     * 解析表格为 List<PromptDTO>
     */
    private List<PromptDTO> parseTable(File file, String extension) throws IOException {
        List<PromptDTO> data = new ArrayList<>();

        if (".csv".equalsIgnoreCase(extension)) {
            // 处理 CSV 文件
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String line;
                boolean isFirstLine = true; // 跳过表头
                while ((line = reader.readLine()) != null) {
                    if (isFirstLine) {
                        isFirstLine = false;
                        continue;
                    }
                    String[] row = line.split(","); // 假设使用逗号分隔
                    if (row.length > 0 && !row[0].trim().isEmpty()) { // 跳过空行
                        PromptDTO dto = new PromptDTO();
                        dto.setPrompt(row[0].trim());
                        dto.setResult(row.length > 1 ? row[1].trim() : ""); // 第二列可能为空
                        data.add(dto);
                    }
                }
            }
        } else if (".xls".equalsIgnoreCase(extension) || ".xlsx".equalsIgnoreCase(extension)) {
            // 处理 Excel 文件
            Workbook workbook = null;
            try {
                if (".xls".equalsIgnoreCase(extension)) {
                    workbook = new HSSFWorkbook(new FileInputStream(file));
                } else {
                    workbook = new XSSFWorkbook(new FileInputStream(file));
                }

                Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
                boolean isFirstRow = true; // 跳过表头
                for (Row row : sheet) {
                    if (isFirstRow) {
                        isFirstRow = false;
                        continue;
                    }
                    Cell firstCell = row.getCell(0);
                    if (firstCell != null && firstCell.getCellType() == CellType.STRING) {
                        String value = firstCell.getStringCellValue().trim();
                        if (!value.isEmpty()) { // 跳过空行
                            PromptDTO dto = new PromptDTO();
                            dto.setPrompt(value);
                            dto.setResult(""); // 初始化 result 为空
                            data.add(dto);
                        }
                    }
                }
            } finally {
                if (workbook != null) {
                    workbook.close();
                }
            }
        } else {
            throw new IllegalArgumentException("不支持的文件类型！只支持 Excel 和 CSV 文件。");
        }

        return data;
    }

    /**
     * 校验数据
     */
    private boolean validateData(List<PromptDTO> data) {
        boolean isValid = true;
        int rowIndex = 1; // 从第二行开始计数
        if(data.size() == 0){
            StringBuilder validationResult = new StringBuilder();
            validationResult.append("无效表格；");
            isValid = false;
            return isValid;
        }

        for (PromptDTO dto : data) {
            StringBuilder validationResult = new StringBuilder();

            // 校验文案数量
            if (rowIndex > 500) {
                validationResult.append("超过50行；");
                isValid = false;
            }

            // 校验文案字符数
            if (dto.getPrompt().length() > 1000000) {
                validationResult.append("超过1000字符；");
                isValid = false;
            }

            // 设置校验结果
            if (validationResult.length() > 0) {
                dto.setResult(validationResult.toString());
            }

            rowIndex++;
        }

        return isValid;
    }

    /**
     * 上传文件到阿里云 OSS
     *
     * @param file     文件对象
     * @param fileName 文件名
     * @return 上传后的文件名
     */
    private String uploadToOSS(File file, String fileName) {
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;

        try {
            // 上传文件到指定的 Bucket
            PutObjectResult result = ossClient.putObject(bucketName, fileName, file);

            // 检查上传结果
            if (result != null) {
                System.out.println("文件上传成功！");
            }
            // 构建完整URL
            StringBuilder imgUrl = new StringBuilder();
            imgUrl.append(Constants.OOS_URL_PREFIX).append(fileName);
            return imgUrl.toString();
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        } finally {
            // 关闭 OSS 客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 将 MultipartFile 转换为临时文件
     *
     * @param multipartFile MultipartFile 对象
     * @return 临时文件
     */
    private File convertToFile(MultipartFile multipartFile) {
        try {
            File tempFile = File.createTempFile("temp", getExtension(multipartFile.getOriginalFilename()));
            try (InputStream in = multipartFile.getInputStream();
                 OutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
            return tempFile;
        } catch (IOException e) {
            throw new RuntimeException("文件转换失败", e);
        }
    }

    /**
     * 生成唯一的文件名，格式为：年/月/日/文件名+时间戳.扩展名
     *
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    private String generateUniqueFileName(String originalFileName) {
        // 获取文件扩展名
        String extension = getExtension(originalFileName);

        // 获取当前日期，用于生成目录结构
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        String datePath = dateFormat.format(new Date());

        // 获取时间戳（毫秒级）
        String timestamp = String.valueOf(System.currentTimeMillis());

        // 提取原始文件名（不带扩展名）
        String baseName = getBaseName(originalFileName);

        // 拼接完整的文件路径和文件名
        return datePath + "/" + baseName + "_" + timestamp + extension;

    }
    /**
     * 获取文件的基本名称（不带扩展名）
     *
     * @param fileName 文件名
     * @return 基本名称
     */
    private String getBaseName(String fileName) {
        if (fileName == null) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        return (lastDotIndex > 0) ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private String getExtension(String fileName) {
        if (fileName == null) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        return (lastDotIndex > 0) ? fileName.substring(lastDotIndex).toLowerCase() : "";
    }

    private Boolean batchDeleteFile(List<String> fileUrls) {
        // 创建OSSClient实例。
        OSS ossClient = createOssClient();
        String bucketName = AliYunUtils.AliYun_bucketName;
        try {
            fileUrls = fileUrls.stream().filter(f->! f.equals("Web_Violating_regulations_picture/Violating_regulations.png")).collect(Collectors.toList());
            // 创建批量删除请求
            DeleteObjectsRequest request = new DeleteObjectsRequest(bucketName)
                    .withKeys(fileUrls); // 将List转换为数组
            // 执行批量删除操作
            DeleteObjectsResult result = ossClient.deleteObjects(request);

            // 获取已删除的对象列表
            List<String> deletedObjects = result.getDeletedObjects();

            // 判断是否所有请求的文件都被成功删除
            if (deletedObjects.size() == fileUrls.size()) {
                return true;
            } else {
                // 有部分或全部文件未被成功删除的情况
                return  false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }

    //创建oss实例
    private static OSS createOssClient() {
        String accessKeyId = AliYunUtils.AliYun_accessKeyId;
        String accessKeySecret = AliYunUtils.AliYun_accessKeySecret;
        String endpoint = AliYunUtils.AliYun_endpoint;
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }

}
