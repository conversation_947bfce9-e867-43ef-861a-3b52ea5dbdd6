package com.dataxai.web.controller.front;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.domain.*;
import com.dataxai.service.ITeamUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.service.IWorkflowService;
import com.dataxai.service.IWorkflowTemplateService;
import com.dataxai.domain.dto.CreateWorkflowRequest;
import com.dataxai.domain.dto.WorkflowDetailDTO;
import com.dataxai.domain.dto.WorkflowNodeTaskDTO;
import com.dataxai.domain.dto.TaskSummaryDTO;
import com.dataxai.mapper.TUserMapper;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.domain.Task;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.dataxai.mapper.WorkflowNodeExecutionMapper;
import com.dataxai.web.service.RiskDetectionImageUploadService;
import com.dataxai.common.utils.WorkflowSecurityUtils;
import com.dataxai.common.exception.ServiceException;
import com.github.pagehelper.PageInfo;
import java.net.HttpURLConnection;
import java.net.URL;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 工作流Controller
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Api(tags = "工作流管理")
@RestController
@RequestMapping("/workflow/workflow")
public class WorkflowController extends BaseController
{
    @Autowired
    private IWorkflowService workflowService;

    @Autowired
    private IWorkflowTemplateService workflowTemplateService;

    @Autowired
    private TUserMapper tUserMapper;

    @Autowired
    private WorkflowNodeExecutionMapper workflowNodeExecutionMapper;

    @Autowired
    private RiskDetectionImageUploadService riskDetectionImageUploadService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private ITeamUserService iTeamUserService;

    /**
     * 获取当前用户信息（包含团队ID和模式）
     */
    private TUser getCurrentUserInfo() {
        Long userId = WorkflowSecurityUtils.getCurrentUserId();
        return tUserMapper.selectTUserById(userId);
    }

    /**
     * 验证团队模式并获取团队ID
     */
    private Long validateAndGetTeamId() {
        TUser currentUser = getCurrentUserInfo();
        if (currentUser == null) {
            throw new ServiceException("用户信息不存在", 400);
        }

        // 验证团队模式
        WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());

        Long teamId = currentUser.getTeamId();
        if (teamId == null) {
            throw new ServiceException("用户未加入任何团队", 400);
        }

        return teamId;
    }

    /**
     * 检查URL是否可用
     */
    private boolean isUrlAccessible(String urlString) {
        if (urlString == null || urlString.trim().isEmpty()) {
            return false;
        }

        try {
            URL url = new URL(urlString.trim());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000); // 5秒超时
            connection.setReadTimeout(5000);
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            connection.disconnect();

            // 认为2xx和3xx都是可用的
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            logger.warn("URL不可用: {}, 错误: {}", urlString, e.getMessage());
            return false;
        }
    }

    /**
     * 查询工作流列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询工作流列表")
    public AjaxResult list(Workflow workflow)
    {
        // 验证团队模式并使用当前登录用户所属的团队ID
        Long currentUserTeamId = validateAndGetTeamId();
        Long userId = SecurityUtils.getUserId();
        TeamUser teamUser = iTeamUserService.selectTeamUserByUserId(userId);
        workflow.setTeamId(currentUserTeamId);
        if (teamUser.getIsAdmin() == true){
            Long selectUserId = workflow.getUserId();
            if (selectUserId != null){
                workflow.setUserId(selectUserId);
            }
        }else{
            workflow.setUserId(userId);
        }
        startPage();
        List<Workflow> list = workflowService.selectWorkflowList(workflow);

        // 为每个工作流查询节点执行信息
        List<WorkflowDetailDTO> resultList = new ArrayList<>();
        for (Workflow wf : list) {
            WorkflowDetailDTO detailDTO = new WorkflowDetailDTO();
            // 复制工作流基本信息
            detailDTO.setId(wf.getId());
            detailDTO.setWorkflowName(wf.getWorkflowName());
            detailDTO.setTemplateId(wf.getTemplateId());
            detailDTO.setTeamId(wf.getTeamId());
            detailDTO.setUserId(wf.getUserId());
            detailDTO.setStatus(wf.getStatus());
            detailDTO.setCreateTime(wf.getCreateTime());
            detailDTO.setUpdateTime(wf.getUpdateTime());

            // 查询关联的模板信息
            if (wf.getTemplateId() != null) {
                WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(wf.getTemplateId());
                if (template != null) {
                    detailDTO.setTemplateName(template.getTemplateName());
                }
            }

            // 查询节点执行记录列表
            List<WorkflowNodeExecution> nodeExecutionList = workflowNodeExecutionMapper.selectByWorkflowId(wf.getId());

            // 统计节点执行状态
            int totalNodes = nodeExecutionList.size();
            int pendingNodes = 0;
            int runningNodes = 0;
            int completedNodes = 0;
            int failedNodes = 0;

            for (WorkflowNodeExecution nodeExecution : nodeExecutionList) {
                Integer status = nodeExecution.getStatus();
                if (status == null || status == 0) {
                    pendingNodes++;
                } else if (status == 1) {
                    runningNodes++;
                } else if (status == 2) {
                    completedNodes++;
                } else if (status == 3) {
                    failedNodes++;
                }
            }

            detailDTO.setNodeExecutionList(nodeExecutionList);
            detailDTO.setTotalNodes(totalNodes);
            detailDTO.setPendingNodes(pendingNodes);
            detailDTO.setRunningNodes(runningNodes);
            detailDTO.setCompletedNodes(completedNodes);
            detailDTO.setFailedNodes(failedNodes);

            resultList.add(detailDTO);
        }

        // 组装自定义分页结构
        Map<String, Object> pageData = new HashMap<>();
        pageData.put("total", new PageInfo<>(list).getTotal());
        pageData.put("data", resultList);

        return AjaxResult.success("操作成功", pageData);
    }

    /**
     * 导出工作流列表
     */
    @Log(title = "工作流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出工作流列表")
    public void export(HttpServletResponse response, Workflow workflow)
    {
        // 验证团队模式并使用当前登录用户所属的团队ID
        Long currentUserTeamId = validateAndGetTeamId();
        workflow.setTeamId(currentUserTeamId);
        List<Workflow> list = workflowService.selectWorkflowList(workflow);
        ExcelUtil<Workflow> util = new ExcelUtil<Workflow>(Workflow.class);
        util.exportExcel(response, list, "工作流数据");
    }

    /**
     * 获取工作流详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取工作流详细信息")
    public AjaxResult getInfo(@ApiParam("工作流ID") @PathVariable("id") Long id)
    {
        Workflow workflow = workflowService.selectWorkflowById(id);
        if (workflow == null) {
            return error("工作流不存在");
        }

        // 验证团队模式和权限
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                workflow.getTeamId(),
                "工作流");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        return success(workflow);
    }

    /**
     * 获取工作流详细信息（包含素材和节点任务信息）
     */
    @GetMapping(value = "/details/{id}")
    @ApiOperation(value = "获取工作流详细信息（包含素材和节点任务信息）")
    public AjaxResult getDetails(@ApiParam("工作流ID") @PathVariable("id") Long id)
    {
        Workflow workflow = workflowService.selectWorkflowById(id);
        if (workflow == null) {
            return error("工作流不存在");
        }

        // 验证团队模式和权限
        TUser currentUser = getCurrentUserInfo();
        try {
            WorkflowSecurityUtils.validateFullTeamAccess(
                currentUser.getCurrentMode(),
                currentUser.getTeamId(),
                workflow.getTeamId(),
                "工作流");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        // 获取工作流详情（包含节点任务信息）
        Object result = workflowService.selectWorkflowWithDetails(id);

        // 补充t_task表的任务信息
        if (result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> resultMap = (Map<String, Object>) result;

            @SuppressWarnings("unchecked")
            List<WorkflowNodeTaskDTO> nodeTaskList = (List<WorkflowNodeTaskDTO>) resultMap.get("nodeTaskList");

            if (nodeTaskList != null) {
                for (WorkflowNodeTaskDTO nodeTask : nodeTaskList) {
                    // 为素材创作类任务（6,7,8,9,11,12,14）查询t_task表
                    Integer taskType = nodeTask.getTaskType();
                    if (taskType != null && (taskType == 6 || taskType == 7 || taskType == 8 ||
                                           taskType == 9 || taskType == 11 || taskType == 12 || taskType == 14)) {
                        try {
                            // 通过批次ID查询任务，因为 t_task 表不再有 workflow_node_execution_id 字段
                            List<Task> tasks = new ArrayList<>();
                            // 通过节点执行ID查询节点执行记录，获取批次ID
                            WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(nodeTask.getNodeExecutionId());
                            if (nodeExecution != null && nodeExecution.getBatchId() != null && nodeExecution.getBatchId() > 0) {
                                Task taskQuery = new Task();
                                taskQuery.setBatchId(nodeExecution.getBatchId().toString());
                                tasks = taskMapper.selectTaskList(taskQuery);
                            }
                            List<TaskSummaryDTO> taskSummaryList = nodeTask.getTaskList();
                            if (taskSummaryList == null) {
                                taskSummaryList = new ArrayList<>();
                                nodeTask.setTaskList(taskSummaryList);
                            }

                            // 转换Task为TaskSummaryDTO
                            for (Task task : tasks) {
                                TaskSummaryDTO dto = new TaskSummaryDTO();
                                dto.setTaskId(task.getTaskId());
                                dto.setTaskName(task.getTaskName());
                                dto.setTaskType(taskType);
                                dto.setStatus(task.getStatus().intValue());
                                dto.setStatusDesc(getTaskStatusDescription(task.getStatus().intValue()));
                                dto.setCreateTime(task.getCreateTime());
                                dto.setUpdateTime(task.getUpdateTime());
                                dto.setOriginalUrl(task.getOriginalUrl());
                                dto.setThumbnailUrl(task.getThumbnailUrl());
                                dto.setBatchId(task.getBatchId());
                                taskSummaryList.add(dto);
                            }

                            // 重新计算统计信息
                            nodeTask.setTaskStatistics(calculateTaskStatistics(taskSummaryList));
                        } catch (Exception e) {
                            logger.warn("查询节点{}的t_task任务失败: {}", nodeTask.getNodeExecutionId(), e.getMessage());
                        }
                    }
                }
            }
        }

        return success(result);
    }

    /**
     * 新增工作流
     */
    @Log(title = "工作流", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增工作流")
    public AjaxResult add(@RequestBody Workflow workflow)
    {
        // 检查模板是否存在
        if (workflow.getTemplateId() != null) {
            if (workflowTemplateService.selectWorkflowTemplateById(workflow.getTemplateId()) == null) {
                return error("指定的工作流模板不存在");
            }
        }

        // 自动生成工作流名称
        workflow.setWorkflowName(generateWorkflowName());

        return toAjax(workflowService.insertWorkflow(workflow));
    }

    /**
     * 创建工作流（通过文件上传）
     */
    @Log(title = "创建工作流", businessType = BusinessType.INSERT)
    @PostMapping("/createByFiles")
    @ApiOperation(value = "创建工作流（通过文件上传）")
    public AjaxResult createByFiles(
            @ApiParam("模板ID") @RequestParam("templateId") Long templateId,
            @ApiParam("素材文件") @RequestParam(value = "files", required = false) List<MultipartFile> files,
            @ApiParam("素材文件数组") @RequestParam(value = "files[]", required = false) List<MultipartFile> filesArray)
    {
        // 检查模板是否存在
        if (workflowTemplateService.selectWorkflowTemplateById(templateId) == null) {
            return error("指定的工作流模板不存在");
        }

        // 优先使用files，如果为空则使用files[]
        List<MultipartFile> fileList = files;
        if (fileList == null || fileList.isEmpty()) {
            fileList = filesArray;
        }

        if (fileList == null || fileList.isEmpty()) {
            return error("素材文件不能为空");
        }

        // 验证团队模式，使用当前登录用户的ID和对应的团队ID
        TUser currentUser = getCurrentUserInfo();
        Long currentUserId = currentUser.getUserId();
        Long currentUserTeamId;

        try {
            WorkflowSecurityUtils.validateTeamMode(currentUser.getCurrentMode());
            currentUserTeamId = currentUser.getTeamId();
            if (currentUserTeamId == null) {
                return error("用户未加入任何团队");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }

        try {
            logger.info("开始通过文件创建工作流，文件数量: {}", fileList.size());

            // 1. 上传图片到阿里云
            logger.info("开始上传图片到阿里云...");
            List<String> imageUrls = riskDetectionImageUploadService.uploadImages(fileList);
            logger.info("图片上传完成，成功上传 {} 个文件", imageUrls.size());

            if (imageUrls.isEmpty()) {
                logger.error("没有图片上传成功，无法创建工作流");
                return error("图片上传失败，无法创建工作流");
            }

            // 2. 创建工作流
            Workflow workflow = new Workflow();
            workflow.setWorkflowName(generateWorkflowName());
            workflow.setTemplateId(templateId);
            workflow.setTeamId(currentUserTeamId);
            workflow.setUserId(currentUserId);

            int result = workflowService.createWorkflowByUrls(workflow, imageUrls);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowId", workflow.getId());
            resultData.put("workflowName", workflow.getWorkflowName());
            resultData.put("totalFiles", fileList.size());
            resultData.put("uploadedImages", imageUrls.size());

            return AjaxResult.success("创建工作流成功", resultData);

        } catch (Exception e) {
            logger.error("通过文件创建工作流失败", e);
            return error("创建工作流失败：" + e.getMessage());
        }
    }

    /**
     * 创建工作流（通过URL列表）
     */
    @Log(title = "创建工作流", businessType = BusinessType.INSERT)
    @PostMapping("/createByUrls")
    @ApiOperation(value = "创建工作流（通过URL列表）")
    public AjaxResult createByUrls(@RequestBody CreateWorkflowRequest request)
    {
        // 验证团队模式，使用当前登录用户的ID和对应的团队ID
        Long currentUserId = WorkflowSecurityUtils.getCurrentUserId();
        Long currentUserTeamId = validateAndGetTeamId();

        // 检查模板是否存在且属于当前用户团队
        WorkflowTemplate template = workflowTemplateService.selectWorkflowTemplateById(request.getTemplateId());
        if (template == null) {
            return error("指定的工作流模板不存在");
        }

        try {
            WorkflowSecurityUtils.validateTeamAccess(currentUserTeamId, template.getTeamId(), "工作流模板");
        } catch (Exception e) {
            return error(e.getMessage());
        }

        // 验证必需参数
        if (request.getTemplateId() == null) {
            return error("模板ID不能为空");
        }

        if (request.getMaterialUrls() == null || request.getMaterialUrls().isEmpty()) {
            return error("素材URL列表不能为空");
        }

        // 检查URL可用性，不可用的舍弃掉
        List<String> validUrls = new ArrayList<>();
        List<String> invalidUrls = new ArrayList<>();

        for (String url : request.getMaterialUrls()) {
            if (url != null && !url.trim().isEmpty()) {
                String trimmedUrl = url.trim();
                if (isUrlAccessible(trimmedUrl)) {
                    validUrls.add(trimmedUrl);
                } else {
                    invalidUrls.add(trimmedUrl);
                }
            }
        }

        if (validUrls.isEmpty()) {
            return error("没有可用的素材URL");
        }

        try {
            logger.info("开始通过URL创建工作流，总URL数量: {}, 有效URL数量: {}, 无效URL数量: {}",
                       request.getMaterialUrls().size(), validUrls.size(), invalidUrls.size());

            // 预处理图片URL（下载并重新上传到阿里云）
            logger.info("开始预处理图片URL...");
            List<String> processedUrls = riskDetectionImageUploadService.processImageUrls(validUrls);

            if (processedUrls.isEmpty()) {
                return error("图片预处理失败，没有可用的图片");
            }

            logger.info("图片预处理完成，成功处理 {} 个图片，失败 {} 个图片",
                       processedUrls.size(), validUrls.size() - processedUrls.size());

            Workflow workflow = new Workflow();
            workflow.setWorkflowName(generateWorkflowName());
            workflow.setTemplateId(request.getTemplateId());
            workflow.setTeamId(currentUserTeamId);
            workflow.setUserId(currentUserId);

            int result = workflowService.createWorkflowByUrls(workflow, processedUrls);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("workflowId", workflow.getId());
            resultData.put("workflowName", workflow.getWorkflowName());
            resultData.put("totalUrls", request.getMaterialUrls().size());
            resultData.put("validUrls", validUrls.size());
            resultData.put("processedUrls", processedUrls.size());
            resultData.put("invalidUrls", invalidUrls.size());
            resultData.put("failedProcessUrls", validUrls.size() - processedUrls.size());
            if (!invalidUrls.isEmpty()) {
                resultData.put("invalidUrlList", invalidUrls);
            }

            String message = String.format("创建工作流成功，使用了%d个处理后的图片", processedUrls.size());
            if (!invalidUrls.isEmpty()) {
                message += String.format("，%d个URL不可用已被舍弃", invalidUrls.size());
            }
            if (validUrls.size() - processedUrls.size() > 0) {
                message += String.format("，%d个图片处理失败已被舍弃", validUrls.size() - processedUrls.size());
            }

            return AjaxResult.success(message, resultData);

        } catch (Exception e) {
            logger.error("通过URL创建工作流失败", e);
            return error("创建工作流失败：" + e.getMessage());
        }
    }

    /**
     * 修改工作流
     */
    @Log(title = "工作流", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改工作流")
    public AjaxResult edit(@RequestBody Workflow workflow)
    {
        // 检查模板是否存在
        if (workflow.getTemplateId() != null) {
            if (workflowTemplateService.selectWorkflowTemplateById(workflow.getTemplateId()) == null) {
                return error("指定的工作流模板不存在");
            }
        }

        return toAjax(workflowService.updateWorkflow(workflow));
    }

    /**
     * 删除工作流
     */
    @Log(title = "工作流", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除工作流")
    public AjaxResult remove(@ApiParam("工作流ID数组") @PathVariable Long[] ids)
    {
        return toAjax(workflowService.deleteWorkflowByIds(ids));
    }



    /**
     * 取消工作流执行
     */
    @Log(title = "取消工作流", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    @ApiOperation(value = "取消工作流执行")
    public AjaxResult cancel(@ApiParam("工作流ID") @PathVariable("id") Long id)
    {
        boolean result = workflowService.cancelWorkflow(id);
        return result ? success("工作流取消成功") : error("工作流取消失败");
    }

    /**
     * 获取工作流执行进度
     */
    @GetMapping("/progress/{id}")
    @ApiOperation(value = "获取工作流执行进度")
    public AjaxResult getProgress(@ApiParam("工作流ID") @PathVariable("id") Long id)
    {
        Object progress = workflowService.getWorkflowProgress(id);

        // 补充t_task表的任务信息
        if (progress instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> progressMap = (Map<String, Object>) progress;

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> nodeProgressList = (List<Map<String, Object>>) progressMap.get("nodeProgressList");

            if (nodeProgressList != null) {
                for (Map<String, Object> nodeProgress : nodeProgressList) {
                    Integer taskType = (Integer) nodeProgress.get("taskType");
                    Long nodeExecutionId = (Long) nodeProgress.get("nodeExecutionId");

                    // 为素材创作类任务（6,7,8,9,11,12,14）查询t_task表
                    if (taskType != null && nodeExecutionId != null &&
                        (taskType == 6 || taskType == 7 || taskType == 8 ||
                         taskType == 9 || taskType == 11 || taskType == 12 || taskType == 14)) {
                        try {
                            // 通过批次ID查询任务，因为 t_task 表不再有 workflow_node_execution_id 字段
                            List<Task> tasks = new ArrayList<>();
                            // 通过节点执行ID查询节点执行记录，获取批次ID
                            WorkflowNodeExecution nodeExecution = workflowNodeExecutionMapper.selectWorkflowNodeExecutionById(nodeExecutionId);
                            if (nodeExecution != null && nodeExecution.getBatchId() != null && nodeExecution.getBatchId() > 0) {
                                Task taskQuery = new Task();
                                taskQuery.setBatchId(nodeExecution.getBatchId().toString());
                                tasks = taskMapper.selectTaskList(taskQuery);
                            }

                            @SuppressWarnings("unchecked")
                            List<TaskSummaryDTO> taskList = (List<TaskSummaryDTO>) nodeProgress.get("taskList");
                            if (taskList == null) {
                                taskList = new ArrayList<>();
                                nodeProgress.put("taskList", taskList);
                            }

                            // 转换Task为TaskSummaryDTO并添加到任务列表
                            for (Task task : tasks) {
                                TaskSummaryDTO dto = new TaskSummaryDTO();
                                dto.setTaskId(task.getTaskId());
                                dto.setTaskName(task.getTaskName());
                                dto.setTaskType(taskType);
                                dto.setStatus(task.getStatus().intValue());
                                dto.setStatusDesc(getTaskStatusDescription(task.getStatus().intValue()));
                                dto.setCreateTime(task.getCreateTime());
                                dto.setUpdateTime(task.getUpdateTime());
                                dto.setOriginalUrl(task.getOriginalUrl());
                                dto.setThumbnailUrl(task.getThumbnailUrl());
                                dto.setBatchId(task.getBatchId());
                                taskList.add(dto);
                            }

                            // 重新计算统计信息
                            com.dataxai.domain.dto.TaskStatisticsDTO taskStats = calculateTaskStatistics(taskList);
                            nodeProgress.put("taskCount", taskStats.getTotalTasks());
                            nodeProgress.put("taskStatistics", taskStats);

                        } catch (Exception e) {
                            logger.warn("查询节点{}的t_task任务进度失败: {}", nodeExecutionId, e.getMessage());
                        }
                    }
                }
            }
        }

        return success(progress);
    }



    /**
     * 生成唯一的工作流名称
     * 格式：WF + 时间戳(yyyyMMddHHmmss) + 随机数(3位)
     *
     * @return 唯一的工作流名称
     */
    private String generateWorkflowName() {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%03d", (int)(Math.random() * 1000));
        return "WF" + timestamp + random;
    }

    /**
     * 获取任务状态描述
     */
    private String getTaskStatusDescription(int status) {
        switch (status) {
            case 1: return "成功";
            case 2: return "失败";
            case 3: return "执行中";
            case 4: return "排队中";
            default: return "未知状态(" + status + ")";
        }
    }

    /**
     * 计算任务统计信息
     */
    private com.dataxai.domain.dto.TaskStatisticsDTO calculateTaskStatistics(List<TaskSummaryDTO> taskList) {
        com.dataxai.domain.dto.TaskStatisticsDTO statistics = new com.dataxai.domain.dto.TaskStatisticsDTO();

        if (taskList == null || taskList.isEmpty()) {
            statistics.setTotalTasks(0);
            statistics.setPendingTasks(0);
            statistics.setRunningTasks(0);
            statistics.setCompletedTasks(0);
            statistics.setFailedTasks(0);
            statistics.setProgressPercentage(0.0);
            return statistics;
        }

        int total = taskList.size();
        int pending = 0;
        int running = 0;
        int completed = 0;
        int failed = 0;

        for (TaskSummaryDTO task : taskList) {
            Integer status = task.getStatus();
            if (status == null || status == 4) {
                pending++; // 排队中
            } else if (status == 3) {
                running++; // 执行中
            } else if (status == 1) {
                completed++; // 成功
            } else if (status == 2) {
                failed++; // 失败
            }
        }

        statistics.setTotalTasks(total);
        statistics.setPendingTasks(pending);
        statistics.setRunningTasks(running);
        statistics.setCompletedTasks(completed);
        statistics.setFailedTasks(failed);

        // 计算进度百分比
        double progress = total > 0 ? (double) completed / total * 100 : 0.0;
        statistics.setProgressPercentage(Math.round(progress * 100.0) / 100.0);

        return statistics;
    }
}